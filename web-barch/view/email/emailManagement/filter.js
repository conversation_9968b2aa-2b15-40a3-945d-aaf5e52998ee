"use strict";
define(function(require, exports, module) {
	exports.init = function(opt, html) {
		layui.use([ 'form', 'layedit', 'laydate', 'trasen', 'upload' ,'treeSelect'], function() {
		    var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload;
		    layer.open({
				type : 1,
				title : opt.title,
				closeBtn : 1,
				shadeClose : false,
				area : [ '400px', '180px' ],
				skin : 'yourclass',
				content : html,
				success : function(layero, index) {
					form.render();
				}
		    });
		    
		    //会议时间
            laydate.render({
                elem: '#emailFilterSendTime',
                type: 'datetime',
                showBottom: true
            });
		}) 
    };
});