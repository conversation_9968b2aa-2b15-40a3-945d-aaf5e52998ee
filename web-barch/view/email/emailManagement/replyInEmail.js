"use strict";
define(function(require, exports, module) {
	exports.init = function(opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function() {
		    var form = layui.form,
		    laydate = layui.laydate,
		    trasen = layui.trasen,
		    upload = layui.upload,
		    layedit = layui.layedit;
		    
		    var fileArray = new Array();
            var oldFileArray = new Array();
            var ed = {};
			var url = common.url + '/ts-document/attachment/';
            var token = $.cookie('token');
            var canIndex = false;
            var chooseInput = "toEmailSelUser";
            
            var editIndex;
		    layer.open({
				type : 1,
				title : opt.title,
				closeBtn : 1,
				shadeClose : false,
				area : [ '100%', '100%' ],
				skin : 'yourclass',
				content : html,
				success : function(layero, index) {
					getEmailContact();
                    if (opt.data) {
                    	$("#replyId").val(opt.data.statusId);
                    	var isEmail = true;
                    	
                    	//收件人员选择
            			$('#selectToEmailSelUser').funs('click', function () {
            				var data = {
            					isCheckDept: 'N',
            					user_str: 'toEmailSelUser',
            					user_id: "toEmailUserId",
            					user_code: "toEmailselUserCode"
            				};
            				$.quoteFun('/common/userSel', {
            					trasen: trasenTable,
            					title: '人员选择',
            					data: data,
            					callback: function(selUserNameVal, userIdArr, userCodeArr, deptNameArr, deptUserNameArr){
            						var textarea = document.getElementById('toEmailSelUser');
            						makeExpandingArea(textarea,userIdArr.length);
            					}
            				});
            			})
            			
            			//抄送人员选择
						$('#selectCcEmailSelUser').funs('click', function () {
							var data = {
								isCheckDept: 'N',
								user_str: 'ccEmailSelUser',
								user_id: "ccEmailUserId",
								user_code: "ccEmailselUserCode"
							};
							$.quoteFun('/common/userSel', {
								trasen: trasenTable,
								title: '人员选择',
								data: data,
								callback: function(selUserNameVal, userIdArr, userCodeArr, deptNameArr, deptUserNameArr){
									var textarea = document.getElementById('ccEmailSelUser');
									makeExpandingArea(textarea,userIdArr.length);
								}
							});
						})
						
						$("#toEmailSelUser").focus(function(){
							chooseInput = "toEmailSelUser";
						});
						
						$("#ccEmailSelUser").focus(function(){
							chooseInput = "ccEmailSelUser";
						});
                    	
                    	if("all" == opt.data.type){
                    		var sendName="";
                    		var sendUserCode="";
                    		var toId = opt.data.toId;
                    		var senderId = opt.data.senderId;
                    		if(toId.indexOf(senderId) < 0){
                    			sendName = opt.data.senderName + ",";
                        		sendUserCode = opt.data.senderId + ",";
                    		}
                    		
                    		if(null != opt.data.toName){
                    			sendName += opt.data.toName + ",";
                    			sendUserCode += opt.data.toId + ",";
                    		}
                    		if(null != opt.data.ccName){
                    			sendName += opt.data.ccName + ",";
                    			sendUserCode += opt.data.ccId + ",";
                    		}
                    		$("#subject").val("回复：" + opt.data.subject);
                    		$("#toEmailSelUser").val(sendName.substring(0,sendName.length-1));
                    		$("#toEmailselUserCode").val(sendUserCode.substring(0,sendUserCode.length-1));
                    	}else if("forward" == opt.data.type){
                    		$("#subject").val("转发：" + opt.data.subject);
                    	}else if("informationForward" == opt.data.type){
                    		isEmail = false;
                    		$("#forwardType").val("information-" + opt.data.forWordInformationId);
                    		$("#informationForward").val(opt.data.forWordInformationId);
                    		$("#subject").val("文章推荐-《" + opt.data.subject+"》");
                			$("#emailHtmlContext").val(opt.data.content);
                    		//layedit.setContent(editIndex, opt.data.content, false);
                    	}else{
                    		$("#subject").val("回复：" + opt.data.subject);
                    		$("#toEmailSelUser").val(opt.data.senderName);
                        	$("#toEmailselUserCode").val(opt.data.senderId);
                    	}
                    	
                    	if(isEmail){
                    		initEmailFiles(opt.data.attachmentList);//初始化附件
                        	var contentStr = "<br><br><br>";
                        	contentStr += "<div style='background-color:#f2f2f2;padding:15px;margin-bottom:10px;border-radius: 5px;'>";
	                        	contentStr += "<p style='color:#999999;'>---------------原始邮件---------------</p>";
	                        	contentStr += "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>发件人:</span>" + opt.data.senderName+"</p>";
	                        	contentStr += "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>发送时间:</span>" + opt.data.postTime+"</p>";
	                        	contentStr += "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>收件人:</span>" + opt.data.toName+"</p>";
	                        	if(null != opt.data.ccName){
	                        		contentStr += "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>抄送人:</span>" + opt.data.ccName+"</p>";
	                        	}
	                        	contentStr += "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>主题:</span>" + opt.data.subject +"</p>"
	                        	if(null != opt.data.content && "" != opt.data.content){
	                        		var ct = opt.data.content.replace("padding:15px;","");
		                        	contentStr += "<p style='color:#333333;'>" + ct + "</p>";
	                        	}
                        	contentStr += "</div>";
                        	//layedit.setContent(editIndex, contentStr, false);
                        	$("#emailHtmlContext").val(contentStr);
                        	$("#saveToOutbox").prop("checked",true);
                        	$("#sendToWx").prop("checked",true);
                    	}
                    	
                    }
                    initEditor();
					form.render();
				}
		    });
		    
		    $("body").off("click", ".emailContactLi").on("click", ".emailContactLi", function () {
		    	var employeeNo = $(this).attr("employeeNo");
				var employeeName = $(this).attr("employeeName");
				if("toEmailSelUser" == chooseInput){
					if("" != $("#toEmailSelUser").val()){
						var toEmailselUserCodeArr = $("#toEmailselUserCode").val().split(",");
						if($.inArray(employeeNo,toEmailselUserCodeArr) == -1){
							$("#toEmailSelUser").val($("#toEmailSelUser").val() + "," + employeeName);
							$("#toEmailUserId").val($("#toEmailUserId").val() + "," + employeeNo);
							$("#toEmailselUserCode").val($("#toEmailselUserCode").val() + "," + employeeNo);
						}else{
							layer.msg("已添加至收件人");
						}
					}else{
						$("#toEmailSelUser").val(employeeName);
						$("#toEmailUserId").val(employeeNo);
						$("#toEmailselUserCode").val(employeeNo);
					}
				}
				if("ccEmailSelUser" == chooseInput){
					if("" != $("#ccEmailSelUser").val()){
						var ccEmailselUserCodeArr = $("#ccEmailselUserCode").val().split(",");
						if($.inArray(employeeNo,ccEmailselUserCodeArr) == -1){
							$("#ccEmailSelUser").val($("#ccEmailSelUser").val() + "," + employeeName);
							$("#ccEmailUserId").val($("#ccEmailUserId").val() + "," + employeeNo);
							$("#ccEmailselUserCode").val($("#ccEmailselUserCode").val() + "," + employeeNo);
						}else{
							layer.msg("已添加至抄送人");
						}
					}else{
						$("#ccEmailSelUser").val(employeeName);
						$("#ccEmailUserId").val(employeeNo);
						$("#ccEmailselUserCode").val(employeeNo);
					}
				}
			})
		    
		    function fileUpload(file, cb) {
	            var formData = new FormData();
	            //假设接口接收参数为file,值为选中的文件
	            formData.append('file', file);
	            $.ajax({
	                url: '/ts-document/attachment/fileUpload?module=email&fillupf=2',
	                method: 'post',
	                contentType: false,
	                processData: false,
	                data: formData,
	                success: function (res) {
	                    cb(res.location);
	                },
	            });
	        }
			
			function initEditor() {
                tinymce.init({
                    selector: '#emailHtmlContext',
                    auto_focus: true,
                    elementpath: false,
                    statusbar: false,
                    language: 'zh_CN',
                    plugins: ['quickbars', 'link', 'table', 'code', 'image', 'advlist', 'lists', 'media', 'paste'],
                    menubar: '',
                    menu: {},
                    toolbar: ['outdent indent | bold italic underline strikethrough backcolor forecolor formatselect | fontsizeselect | lineheight cut copy paste | link image  media | alignleft aligncenter alignright alignjustify '],
                    table_clone_elements: 'p',
                    table_grid: false,
										fontsize_formats: '42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt 6.5pt 5.5pt 5pt',
                    font_formats: "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
                    width: '98%',
                    height: '100%',
                    auto_focus: true,
                    paste_enable_default_filters: false,
                    images_upload_url: common.url + '/ts-document/attachment/fileUpload?module=email&fillupf=2', //接受上传文件的后端地址
                    setup: function (editor) {
											editor.on('init', function(ed) {
												ed.target.editorCommands.execCommand('fontName', false, '宋体')
											})
										},
                    paste_postprocess: function (plugin, args) {
                        let res = args.node.querySelectorAll('img');
                        var flag = false;
                        for (let i = 0; i < res.length; i++) {
                            if (res[i].src.indexOf('file:') != -1) {
                                flag = true;
                            }
                        }
                        if (flag) {
                            $.ajax({
                                type: 'get',
                                url: 'http://localhost:26789/file/readfile',
                                success: function (res) {
                                    updateImgBase64();
                                },
                                error: function (res) {
                                    layer.open({
                                        type: 1,
                                        skin: 'layui-layer-demo', //样式类名
                                        closeBtn: 0, //不显示关闭按钮
                                        anim: 2,
                                        shadeClose: true, //开启遮罩关闭
                                        content: '<div style="padding:10px">粘贴WORD图文模式，您需要先安装一个插件<a style="color:blue" href="/static/wordPasterPlug/trasenWordPaster.zip">点击下载</a>  <br><div>',
                                    });
                                },
                            });
                        }
                    },
                    file_picker_types: 'media',
                    convert_urls: false, //这个参数加上去就可以了
                    media_alt_source: false,
                    media_filter_html: false,
                    powerpaste_word_import: 'merge', // 参数可以是propmt, merge, clear
                    powerpaste_html_import: 'merge', // propmt, merge, clear
                    powerpaste_allow_local_images: true, //允许带图片
                    paste_data_images: true,
                    file_picker_callback: function (cb, value, meta) {
                        if (meta.filetype == 'media') {
                            //创建一个隐藏的type=file的文件选择input
                            let input = document.createElement('input');
                            input.setAttribute('type', 'file');
                            input.onchange = function () {
                                var file = this.files[0];
                                fileUpload(file, cb);
                            };
                            //触发点击
                            input.click();
                        }
                    },
                    init_instance_callback: function (editor) {
                        //页面初始化事件
                        ed = editor;
                    },
                });
            }
			
			//上传Base64图片
            function updateImgBase64() {
                let res = ed.iframeElement.contentWindow.document.querySelectorAll('img');
                let ajax = [];
                for (let i = 0; i < res.length; i++) {
                    if (res[i].src.indexOf('file:') != -1) {
                        ajax.push($.get(`http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`));
                    }
                }
                if (ajax.length != 0) {
                    Promise.all(ajax).then(_res => {
                        _res.forEach(_item => {
                            res[_item.dataIndex]['data-src'] = res[_item.dataIndex]['src'];
                            res[_item.dataIndex]['src'] = _item.base64;
                        });
                        updateImg();
                    });
                }
            }
            /**@desc 提交前替换图片请求服务器资源**/
            function updateImg() {
                let res = ed.iframeElement.contentWindow.document.querySelectorAll('img');
                let ajax = [];
                let dom = [];
                for (let i = 0; i < res.length; i++) {
                    if (res[i].src.indexOf('data:image/png;base64,') != -1) {
                        dom.push(res[i]);
                        ajax.push(
                            $.post(`${url}imageBase64Upload`, {
                                token,
                                module: 'richfile',
                                fillupf: '2',
                                imageBase64: res[i].src.split('data:image/png;base64,')[1],
                            })
                        );
                    }
                }
                if (ajax.length != 0) {
                    Promise.all(ajax).then(_res => {
                        _res.forEach((_item, index) => {
                            if (_item.success) {
                                dom[index].src = `${_item.object.location}`;
                                dom[index].removeAttribute('data-mce-src');
                            }
                        });
                        //submit();
                    });
                }
            }
			
			form.verify({
		    	/*content: function(value) { 
		    		if('' == layedit.getText(editIndex)){
		    			return "内容不能为空";
		    		}else{
		    			return layedit.sync(editIndex);
		    		}
	            }*/
	        });
			 
			$('#replyInEmailForm')
            .off('click', '#toNameReset')
            .on('click', '#toNameReset', function (e) {
            	$("#toEmailSelUser").val("");
				$("#toEmailUserId").val("");
				$("#toEmailselUserCode").val("");
				var textarea = document.getElementById('toEmailSelUser');
				makeExpandingArea(textarea,0);
            });
			
			$('#replyInEmailForm')
            .off('click', '#ccNameReset')
            .on('click', '#ccNameReset', function (e) {
            	$("#ccEmailSelUser").val("");
				$("#ccEmailUserId").val("");
				$("#ccEmailselUserCode").val("");
				var textarea = document.getElementById('ccEmailSelUser');
				makeExpandingArea(textarea,0);
            });
			
			
			//常用联系人
			function getEmailContact(){
				var empSignimg = "";
				$.ajax({
					type: 'get',
					url: common.url + "/ts-information/api/emailContact/selectEmailContactByUsercode",
					async:false,
					success: function (res) {
						if(res.success){
							$("#emailContactUl").empty();
							var liStr = "";
							$.each(res.object,function(i,item){
								liStr += '<li class="emailContactLi" employeeNo="' + item.employeeNo  + '" employeeName="' + item.employeeName + '">' + item.employeeName + '</li>';
							})
							$("#emailContactUl").append(liStr);
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
				return empSignimg;
			}
			 
			function makeExpandingArea(el,len) {
			    var timer = null;
			    var setStyle = function(el, auto) {
			        if (auto) el.style.height = auto;
			        if(len == 0){
			        	 el.style.height = '30px';
			        }else{
			        	 el.style.height = el.scrollHeight + 'px';
			        }
			    }
			    var delayedResize = function(el) {
			        if (timer) {
			            clearTimeout(timer);
			            timer = null;
			        }
			        timer = setTimeout(function() {
			            setStyle(el)
			        }, 200);
			    }
			    if (el.addEventListener) {
			        el.addEventListener('input', function() {
			            setStyle(el, 1);
			        }, false);
			        setStyle(el);
			    } else if (el.attachEvent) {
			        el.attachEvent('onpropertychange', function() {
			            setStyle(el)
			        })
			        setStyle(el)
			    }
			    if (window.VBArray && window.addEventListener) {
			        el.attachEvent("onkeydown", function() {
			            var key = window.event.keyCode;
			            if (key == 8 || key == 46) delayedResize(el);
			
			        });
			        el.attachEvent("oncut", function() {
			            delayedResize(el);
			        });
			    }
			}
		    
			//附件上传
            var emailManagementFileDataList = $('#emailManagementFileDataList'),
                uploadListIns = upload.render({
                    elem: '#emailManagementFileUploadBtn',
                    url: common.url + '/ts-document/attachment/fileUpload?module=email',
                    accept: 'file',
                    multiple: true,
                    auto: true,
                    choose: function (obj) {
                        var files = (this.files = obj.pushFile()); //将每次选择的文件追加到文件队列
                        $('#emailManagementFileList').show();
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">', '<td>' + file.name + '</td>', '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>', '<td>等待上传</td>', '<td>', '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>', '</td>', '</tr>'].join(''));

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                //删除数组中指定元素
                                fileArray = $.grep(fileArray, function (n, i) {
                                    return n['index'] != index;
                                });
                            });
                            
                            emailManagementFileDataList.append(tr);
                        });
                    },
                    done: function (res, index, upload) {
                        if (res.success == true) {
                            //上传成功
                            var tr = emailManagementFileDataList.find('tr#upload-' + index),
                                tds = tr.children();
                            tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                            res.object[0].index = index;
                            fileArray.push(res.object[0]);
                            return delete this.files[index]; //删除文件队列已经上传成功的文件
                        }
                        this.error(index, upload, res.message);
                    },
                    error: function (index, upload, errorMsg) {
                        var tr = emailManagementFileDataList.find('tr#upload-' + index),
                            tds = tr.children();
                        tds.eq(2).html('<span style="color: #FF5722;">上传失败:' + errorMsg + '</span>');
                    },
            });
			

			//初始化附件信息
			function initEmailFiles(attachmentList) {
				if (null != attachmentList && undefined != attachmentList) {
					$("#emailManagementFileList").show();
					$.each(attachmentList, function (index, item) {
						var trStr = "<tr id='upload-" + item.id + "'><td>" + item.originalName + "</td>";
						trStr += "<td>" + (item.fileSize / 1024).toFixed(1) + "kb</td><td><span style='color: #5FB878;'>上传成功</span></td>"
						trStr += '<td><button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button></td></tr>'
						$('#emailManagementFileList').append(trStr);
						oldFileArray.push(item.id);
					});

					$("body").off("click", ".demo-delete").on("click", ".demo-delete", function () {
						var idStr = $(this).parent().parent('tr').attr("id");
						var idArray = idStr.split("-");
						oldFileArray.splice($.inArray(idArray[1], oldFileArray), 1);
						$(this).parent().parent('tr').remove();
					})
				}
			}
		    
		    //发送
	        form.on('submit(replyInEmailSubmit)', function (data) {
	            var d = data.field;
	            d.isDraft = 0;
	            d.timing = 0;
	            d.replyId = $("#replyId").val();
	            var url = '/ts-information/optimize/emailInternal/sendEmailInternal';
				if (!d.id) delete d.id;
				d.content = tinymce.get('emailHtmlContext').getContent();
	            var uploadedFile="";
	            $.each(fileArray,function(index,obj){
	            	uploadedFile += obj.fileId + ",";
	            });
	            $.each(oldFileArray,function(index,obj){
	            	uploadedFile += obj + ",";
	            });
	            
	            d.uploadedFile = uploadedFile.substring(0,uploadedFile.length-1);
	
	            $.loadings();
	            $.ajax({
	                type: 'post',
	                url: common.url + url,
	                contentType : "application/json;charset=UTF-8",
	                data: JSON.stringify(d),
	                success: function (res) {
	                    $.closeloadings();
	                    if (res.success) {
	                        layer.closeAll();
	                        tinymce.remove('#emailHtmlContext');
	                        layer.msg(res.object);
	                        $("#outBoxTab").click();
	                    } else {
	                        layer.closeAll();
	                        tinymce.remove('#emailHtmlContext');
	                        layer.msg(res.message);
	                    }
	                },
	                error: function (res) {
	                    res = JSON.parse(res.responseText);
	                    layer.msg(res.message);
	                }
	            });
	            return false;
	         });
	        
	        //取消
	        $('#closeInEmail').funs('click', function() {
	        	 tinymce.remove('#emailHtmlContext');
	        	 layer.closeAll();
			})
	        
	       
		}) 
    };
});