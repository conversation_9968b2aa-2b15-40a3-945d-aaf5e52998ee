<style>
  #WeekendHolidaySchedule {
    overflow: hidden;
    background: #eee;
    font-family: "Microsoft YaHei";
    position: relative;
  }

  #WeekendHolidaySchedule #WeekendHolidayScheduleImport {
    position: relative;
    right: 0px;
    top: 0px;
  }

  #WeekendHolidaySchedule .shell-layer-input-box {
    margin-bottom: 0px !important;
  }

  /* #WeekendHolidaySchedule .transen-con-view-box {
    left: 0px !important;
  } */

  #WeekendHolidaySchedule .transen-con-view-box {
    left: 0px !important;
  }

  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv {
    height: 60px !important;
  }

  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv .ui-jqgrid-htable .ui-th-div {
    height: 30px;
    line-height: 30px;
    top: 0px !important;
  }

  #WeekendHolidaySchedule .ui-jqgrid-hbox {
    overflow-y: scroll;
    width: auto;
  }

  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv,
  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv .ui-jqgrid-htable {
    width: auto !important;
  }

  #WeekendHolidaySchedule .frozen-div .ui-th-div {
    margin-top: 13px;
  }

  #WeekendHolidaySchedule .ui-jqgrid .ui-jqgrid-bdiv {
    width: auto !important;
  }

  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv {
    height: 61px !important;
  }

  #WeekendHolidaySchedule .frozen-div.ui-jqgrid-hdiv .ui-jqgrid-htable .ui-th-div {
    height: 30px;
    line-height: 30px;
    top: 0px !important;
  }

  #WeekendHolidaySchedule .ui-jqgrid-bdiv {
    top: 61px !important;
  }

  #WeekendHolidaySchedule .select-mode {
    background: rgba(234, 236, 241, 1);
    display: flex;
    border-radius: 4px;
    margin: 0 8px;
    overflow: hidden;
    cursor: pointer;
    height: 30px;
  }

  #WeekendHolidaySchedule .select-mode span {
    padding: 4px 8px;
  }

  #WeekendHolidaySchedule .select-mode span.active {
    background-color: rgba(82, 96, 255, 1);
    color: #fff;
  }

  #WeekendHolidaySchedule .table-title {
    position: absolute;
    left: 50%;
    top: 50px;
    transform: translateX(-50%);
    width: auto;
    font-size: 16px;
    font-weight: 600;
  }
</style>
<div class="content-box bg-trans" id="WeekendHolidaySchedule">
  <div class="trasen-con-box" style="left: 0;">
    <div class="transen-con-view-box">
      <div class="oa-nav-search">
        <form id="WeekendHolidayScheduleForm" class="layui-form">

          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">组织机构</span>
            <div class="shell-layer-input-box">
              <input type="text" name="empOrgId" id="empOrgId" class="layui-input" placeholder="请输入组织机构"
                search-input="attendanceReportedSearch" />
            </div>
          </div>

          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">选择日期</span>
            <div class="shell-layer-input-box" style="width: 200px">
              <input type="text" placeholder="时间选择" autocomplete="off"
                class="layui-input edi-layui-input searchInput datetime" name='schedulingDate'
                id="WeekendHolidayScheduleYearSearch" readonly="readonly" />
            </div>
          </div>

          <div class="shell-search-box">
            <button type="button" class="layui-btn" id="WeekendHolidayScheduleSearch">搜索</button>
          </div>

          <div class="shell-search-box">
            <button type="button" class="layui-btn oa-btn-reset" id="WeekendHolidayScheduleResetBtn">
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" id="WeekendHolidayScheduleImport" data-permission=="on">导出</button>
        </div>
      </div>
      <span class="table-title"></span>
      <div class="trasen-con-box" style="top: 78px">
        <div class="table-box" id="WeekendHolidayScheduleBox">
        </div>
      </div>
    </div>
  </div>
</div>