"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    let API = {
      organizationGetTree: "/ts-basics-bottom/organization/getTree3",
      getScheduleLogistics: "/ts-hrms/scheduleinfo/getScheduleLogistics",
      Export: "/ts-hrms/scheduleinfo/getScheduleLogistics/Export",
      getholidaysByDate: "/ts-hrms/schedulingholidays/getholidaysByDate",
    };

    var WeekendHolidayScheduleTable = null;
    var dateArr = [];
    var personList = [];

    layui.use(
      ["form", "zTreeSearch", "trasen", "element", "laydate"],
      function () {
        var laydate = layui.laydate,
          trasen = layui.trasen,
          form = layui.form;

        function rednerDate() {
          laydate.render({
            elem: "#WeekendHolidayScheduleYearSearch",
            type: "month",
            trigger: "click",
            range: false,
            value: dayjs().format("YYYY-MM"),
            callback: () => {
              render();
            },
          });
        }
        rednerDate();
        render();

        $("#WeekendHolidayScheduleResetBtn").funs("click", function () {
          $("#WeekendHolidayScheduleForm")[0].reset();
          rednerDate();
          render();
        });

        $("#WeekendHolidayScheduleSearch").funs("click", function () {
          render();
        });

        $("#WeekendHolidaySchedule")
          .off("click", "#WeekendHolidayScheduleImport")
          .on("click", "#WeekendHolidayScheduleImport", function () {
            if (personList && personList.length > 0) {
              let { schedulingDate } = trasen.getNamesVal(
                $("#WeekendHolidayScheduleForm")
              );
              let empOrgId = $("#WeekendHolidaySchedule #empOrgId").val() || "";
              var url =
                common.url +
                "/ts-hrms/scheduleinfo/getScheduleLogisticsExoprt?" +
                "&schedulingDate=" +
                schedulingDate +
                "&empOrgId=" +
                empOrgId;
              location.href = url;
            } else {
              layer.msg("暂无数据");
            }
          });

        function render() {
          $("#WeekendHolidayScheduleBox").html(
            '<table id="WeekendHolidayScheduleTable"></table>'
          );
          WeekendHolidayScheduleTable = null;

          const { schedulingDate } = trasen.getNamesVal(
            $("#WeekendHolidayScheduleForm")
          );
          let searchStartDate = dayjs(schedulingDate)
            .startOf("month")
            .format("YYYY-MM-DD");
          let searchEndDate = dayjs(schedulingDate)
            .endOf("month")
            .format("YYYY-MM-DD");

          let holidaysParamsData = {
            searchType: "1",
            searchStartDate,
            searchEndDate,
          };

          $("#WeekendHolidaySchedule .table-title").text(
            `行政职能部门 ${searchStartDate}至${searchEndDate}排/派班统计情况`
          );

          let holidayDir = {};
          $.ajax({
            url: API.getholidaysByDate,
            type: "get",
            async: false,
            data: holidaysParamsData,
            success: function (res) {
              if (res.success) {
                let row = res.object || [];
                row.forEach((item) => {
                  const { holidaysDate, holidaysName } = item;
                  const dayValue = dayjs(holidaysDate).day();
                  let dir = {
                    0: "周日",
                    1: "周一",
                    2: "周二",
                    3: "周三",
                    4: "周四",
                    5: "周五",
                    6: "周六",
                  };
                  holidayDir[holidaysDate] =
                    dir[dayValue] + `(${holidaysName || "周末"})`;
                });
              }
            },
          });

          $.ajax({
            url: API.getScheduleLogistics,
            type: "get",
            async: false,
            data: {
              schedulingDate,
              empOrgId: $("#WeekendHolidaySchedule #empOrgId").val() || "",
            },
            success: function (res) {
              personList = res.rows || [];
            },
          });

          let colModel = [
            {
              name: "employeeName",
              width: 90,
              sortable: false,
              align: "center",
              frozen: true,
              fixed: true,
            },
            {
              name: "name",
              width: 130,
              sortable: false,
              align: "center",
              fixed: true,
              frozen: true,
            },
            {
              name: "phone",
              width: 120,
              sortable: false,
              align: "center",
              fixed: true,
              frozen: true,
            },
          ];
          let colNames = ["姓名", "科室", "手机号"];

          Object.entries(holidayDir).forEach(([key, value]) => {
            colModel.push({
              name: key,
              width: 120,
              sortable: false,
              align: "center",
            });
            colNames.push(value);
          });

          WeekendHolidayScheduleTable = new $.trasenTable(
            "WeekendHolidayScheduleTable",
            {
              datatype: "local",
              data: personList || [],
              colModel,
              colNames,
              shrinkToFit: false,
              autoScroll: true,
              autowidth: true,
              sortable: false,
            }
          );
          // 设置二级表头
          let groupHeaders = [];
          Object.entries(holidayDir).forEach(([key, value]) => {
            const [year, month, date] = key.split("-");
            groupHeaders.push({
              startColumnName: key,
              numberOfColumns: 1,
              titleText: `${month}.${date}`,
            });
          });

          WeekendHolidayScheduleTable.oTable.jqGrid("setFrozenColumns");
          $("#WeekendHolidayScheduleTable").jqGrid("setGroupHeaders", {
            useColSpanStyle: true,
            groupHeaders,
          });
        }
      }
    );
  };
});
