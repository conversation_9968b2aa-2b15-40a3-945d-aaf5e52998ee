"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    let API = {
      getPageAllList: "/ts-hrms/schedulinggrouping/getPageAllList",
      organizationGetTree: "/ts-basics-bottom/organization/getTree3",
      cheduleinfoSave: "/ts-hrms/scheduleinfo/save",
      cheduleinfoGetSchedule: "/ts-hrms/scheduleinfo/getSchedule",
    };

    var weekArr = [];
    var monthArr = [];
    var monthSelectPersonId = null;
    var monthSelectPersonGroupId = null;
    var selNode = null;
    var now = new Date();
    var today = {
      year: now.getFullYear(),
      month: addZero(now.getMonth() + 1),
      date: now.getDate(),
      day: now.getDay(),
    };

    var setting = {
      data: {
        simpleData: {
          enable: false,
        },
      },
      callback: {
        onClick: function (event, treeId, treeNode) {
          monthSelectPersonId = null;
          monthSelectPersonGroupId = null;

          selNode = treeNode;
          $("#ShiftViewBox #orgId").val(selNode.id);
					  $("#ShiftViewBox #orgName").val(selNode.name);
					
          if (selectModeActive === "1") {
            let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
            handleGetMonthArr(nowYearMonth);
          }
          render();
          return false;
        },
        onNodeCreated: function (e, id, node) {
          var node = node;
          var treeObj = $.fn.zTree.getZTreeObj("ShiftViewBoxSchedulingTree");
          $("#ShiftViewBox #orgId").val(common.userInfo.deptId);

          if (node.id == common.userInfo.deptId) {
            treeObj.selectNode(node);
          }
        },
        onCheck: function (e, id, treeNode) {},
      },
      view: {
        dblClickExpand: true,
        showTitle: true,
        showLine: false,
      },
    };

    let selectModeActive = null;
    let startWeekDate = "";
    let endWeekDate = "";

    let isSetBoolean = false; // 排版开始状态

    let globalPersonArr = []; // 科室人员数据
    let useScheduleArr = []; // 使用的班次 （去重）

    layui.use(
      ["form", "zTreeSearch", "trasen", "element", "laydate"],
      function () {
        var form = layui.form,
          laydate = layui.laydate,
          trasen = layui.trasen,
          zTreeSearch = layui.zTreeSearch,
          element = layui.element;
        var treeObj;

        $("#ShiftViewBox")
          .off("input", "#TreeSearch")
          .on(
            "input",
            "#TreeSearch",
            common.debounce(function () {
              var val = $(this).val();
              $.fn.ztreeQueryHandler(treeObj, val);
            }, 200)
          );

        // 切换周或者月份排班模式
        $("#ShiftViewBox")
          .off("click", ".select-mode span")
          .on("click", ".select-mode span", function () {
            if (isSetBoolean) {
              layer.msg("请先结束排班");
              return;
            }
            const active = $(this).attr("data-tab");
            if (selectModeActive === active) {
              return false;
            }
            weekArr = [];
            monthArr = [];
            monthSelectPersonId = null;
            monthSelectPersonGroupId = null;

            let opposeStr = active === "0" ? "1" : "0";
            $(`#ShiftViewBox .crew-scheduling-mode-${active}`).show();
            $(`#ShiftViewBox .crew-scheduling-mode-${opposeStr}`).hide();

            selectModeActive = active;
            $(this).toggleClass("active");
            $(this).siblings().removeClass();
            if (selectModeActive === "0") {
              $("#ShiftViewBox #MonthSchedulingTboU").html("");
              $("#ShiftViewBox #SetMonthClassesTable tbody").html("");
              handleGetWeekArr();
            }
            if (selectModeActive === "1") {
              $("#ShiftViewBox #WeekSchedulingTboU").html("");
              handleGetMonthArr(dayjs().format("YYYY-MM-DD"));
            }

            handleSetHeardTimeLabel();
            render();
          });

        initTree();
        function initTree() {
          $.ajax({
            url: common.url + API.organizationGetTree,
            type: "post",
            async: false,
            success: function (res) {
              if (res.success) {
                var zNodes = res.object;
                treeObj = $.fn.zTree.init(
                  $("#ShiftViewBoxSchedulingTree"),
                  setting,
                  zNodes
                );
              } else {
                layer.msg("树加载失败.");
              }
            },
          });
        }

        $(`#ShiftViewBox span[data-tab=1]`).trigger("click");
      }
    );

    function addZero(num) {
      var num = num - 0;
      return num > 9 ? num : "0" + num;
    }

    // 获取本周 日期时间段
    function handleGetWeekArr() {
      // let today = dayjs().day() === 0 ? dayjs().add('-1', 'day') : dayjs()

      // // 本周开始时间
      // let weeklyReportDateStart = dayjs(today).startOf('week').add(1, 'day').format('YYYY-MM-DD');
      // // 本周结束时间
      // let weeklyReportDateEnd = dayjs(today).endOf('week').add(1, 'day').format('YYYY-MM-DD');

      const now = new Date();
      const nowTime = now.getTime();
      // getDay()返回0-6，其中0表示周日，需特殊处理
      const day = now.getDay() > 0 ? now.getDay() : 7; // 表示当前是周几
      const oneDayTime = 24 * 60 * 60 * 1000; // 一天的总ms
      // 本周一时间戳
      const MondayTime = nowTime - (day - 1) * oneDayTime;
      // 本周日时间戳
      const SundayTime = nowTime + (7 - day) * oneDayTime;
      // 格式化时间
      const monday = new Date(MondayTime);
      const sunday = new Date(SundayTime);

      // 本周开始时间
      let weeklyReportDateStart = dayjs(monday).format("YYYY-MM-DD");
      // 本周结束时间
      let weeklyReportDateEnd = dayjs(sunday).format("YYYY-MM-DD");
      weekArr = getAllDate(weeklyReportDateStart, weeklyReportDateEnd);
    }

    function handleGetMonthArr(month) {
      let monthStart = dayjs(month).startOf("month").format("YYYY-MM-DD");
      let monthEnd = dayjs(month).endOf("month").format("YYYY-MM-DD");
      monthArr = getAllDate(monthStart, monthEnd);
      let loaclMonthArr = monthArr.slice(0);

      // dayjs 周日为0 转换为7
      const monthStartWeek = dayjs(monthStart).day() || 7;
      const monthEndWeek = dayjs(monthEnd).day() || 7;
      // 月开始 不为周一 则进行补全第一行 td数量
      if (monthStartWeek !== 1) {
        for (let i = 0; i < monthStartWeek - 1; i++) {
          loaclMonthArr.unshift({});
        }
      }

      // 月结束 不为周日 则进行补全最后一行 td数量
      if (monthEndWeek !== 7) {
        for (let i = 0; i < 7 - monthEndWeek; i++) {
          loaclMonthArr.push({});
        }
      }

      const monthTrArr = splitArray(loaclMonthArr, 7);
      $("#ShiftViewBox #SetMonthClassesTable tbody").html("");
      for (let i = 0; i < monthTrArr.length; i++) {
        const item = monthTrArr[i];
        let tr = document.createElement("tr");

        for (let j = 0; j < item.length; j++) {
          const tdItem = item[j];
          const pathDate = `${tdItem.year}-${tdItem.month}-${tdItem.date}`;
          const fakeItem = JSON.stringify(item[j]) === "{}";

          $(tr).append(`<td 
                        class=${!fakeItem ? "areaContent" : ""}
                        date-day=${tdItem.date}
                        date=${pathDate}>
                          <p class="date">${tdItem.date || ""}</p>
                        </td>`);
        }
        $("#ShiftViewBox #SetMonthClassesTable tbody").append(tr);
      }
    }

    // 获取 时间段中的日期
    function getAllDate(start, end) {
      let dateArr = [];
      let startArr = start.split("-");
      let endArr = end.split("-");
      let db = new Date();
      db.setUTCFullYear(startArr[0], startArr[1] - 1, startArr[2]);
      let de = new Date();
      de.setUTCFullYear(endArr[0], endArr[1] - 1, endArr[2]);
      let unixDb = db.getTime();
      let unixDe = de.getTime();
      let stamp;
      const oneDay = 24 * 60 * 60 * 1000;
      for (stamp = unixDb; stamp <= unixDe; ) {
        // 转为 year month date 对象
        let dateYYYYMMDD = dateFormatChange(new Date(parseInt(stamp)));
        let reg = /(\d{4})\-(\d{2})\-(\d{2})/;
        let localDateValue = dateYYYYMMDD.match(reg);

        let dateObj = {
          year: localDateValue[1],
          month: localDateValue[2],
          date: localDateValue[3],
        };

        dateArr.push(dateObj);
        stamp = stamp + oneDay;
      }

      // yyyy-mm-dd
      function dateFormatChange(time) {
        let ymd = "";
        let mouth =
          time.getMonth() + 1 >= 10
            ? time.getMonth() + 1
            : "0" + (time.getMonth() + 1);
        let day = time.getDate() >= 10 ? time.getDate() : "0" + time.getDate();
        ymd += time.getFullYear() + "-"; // 获取年份。
        ymd += mouth + "-"; // 获取月份。
        ymd += day; // 获取日。
        return ymd; // 返回日期。
      }
      return dateArr;
    }

    // 渲染列表表头时间
    function handleSetHeardTimeLabel() {
      if (selectModeActive === "0") {
        $("#ShiftViewBox .week-show").show();
        $("#ShiftViewBox .month-show").hide();

        $("#ShiftViewBox #ThisWeek").show();
        $("#ShiftViewBox #ThisMonth").hide();

        $("#ShiftViewBox #schedulingExportBtn").show();
        $("#ShiftViewBox #JKSchedulingExport").show();
        $("#ShiftViewBox #schedulingPrinitBtn").show();
        $("#ShiftViewBox #JKSchedulingPrinit").show();
        $("#ShiftViewBox #MonthExport").hide();
        setWeekTopTitle();
      } else {
        $("#ShiftViewBox .week-show").hide();
        $("#ShiftViewBox .month-show").show();

        $("#ShiftViewBox #ThisWeek").hide();
        $("#ShiftViewBox #ThisMonth").show();

        $("#ShiftViewBox #schedulingExportBtn").hide();
        $("#ShiftViewBox #JKSchedulingExport").hide();
        $("#ShiftViewBox #schedulingPrinitBtn").hide();
        $("#ShiftViewBox #JKSchedulingPrinit").hide();
        $("#ShiftViewBox #MonthExport").show();
        setMonthTopTitle();
      }

      function setWeekTopTitle() {
        var isWeek = false;
        $("#ShiftViewBox #titleScheduling #begin-year").text(weekArr[0].year);
        $("#ShiftViewBox #titleScheduling #begin-month").text(weekArr[0].month);
        $("#ShiftViewBox #titleScheduling #begin-date").text(weekArr[0].date);
        $("#ShiftViewBox #titleScheduling #end-year").text(weekArr[6].year);
        $("#ShiftViewBox #titleScheduling #end-month").text(weekArr[6].month);
        $("#ShiftViewBox #titleScheduling #end-date").text(weekArr[6].date);

        for (var i = 0; i < weekArr.length; i++) {
          let item = weekArr[i],
            year = item.year,
            month = item.month,
            date = item.date;
          $(`#ShiftViewBox #WeekPaibanBox #areaThead-${i}`).text(
            month + "月" + date + "日"
          );
          $(`#ShiftViewBox #WeekPaibanBox #areaThead-${i}`)
            .parent()
            .attr("date", `${year}-${month}-${date}`);
          if (
            today.year == year &&
            today.month == month &&
            today.date == date
          ) {
            isWeek = true;
          }
        }

        if (isWeek) {
          $("#ShiftViewBox #titleScheduling #ThisWeek").removeClass(
            "this-week-month-color"
          );
        } else {
          $("#ShiftViewBox #titleScheduling #ThisWeek").addClass(
            "this-week-month-color"
          );
        }
      }
      function setMonthTopTitle() {
        var isMonth = false;

        const newDataYear = monthArr[0].year;
        const newDataMonth = monthArr[0].month;

        $("#ShiftViewBox #titleScheduling #local-year").text(newDataYear);
        $("#ShiftViewBox #titleScheduling #local-month").text(newDataMonth);
        if (today.year == newDataYear && today.month == newDataMonth) {
          isMonth = true;
        }

        if (isMonth) {
          $("#ShiftViewBox #titleScheduling #ThisMonth").removeClass(
            "this-week-month-color"
          );
        } else {
          $("#ShiftViewBox #titleScheduling #ThisMonth").addClass(
            "this-week-month-color"
          );
        }
      }
    }

    // 上一周按钮
    $("#ShiftViewBox")
      .off("click", "#prev-week")
      .on("click", "#prev-week", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let prevWeekMonday = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        // 本周开始时间
        let prevStart = dayjs(prevWeekMonday)
          .add(-1, "week")
          .startOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        // 本周结束时间
        let prevEnd = dayjs(prevWeekMonday)
          .add(-1, "week")
          .endOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        weekArr = getAllDate(prevStart, prevEnd);
        handleSetHeardTimeLabel();
        render();
      });
    // 下一周按钮
    $("#ShiftViewBox")
      .off("click", "#next-week")
      .on("click", "#next-week", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }

        let nextWeekMonday = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        // 本周开始时间
        let nextStart = dayjs(nextWeekMonday)
          .add(1, "week")
          .startOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        // 本周结束时间
        let nextEnd = dayjs(nextWeekMonday)
          .add(1, "week")
          .endOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        weekArr = getAllDate(nextStart, nextEnd);
        handleSetHeardTimeLabel();
        render();
      });
    // 本周按钮
    $("#ShiftViewBox")
      .off("click", "#ThisWeek")
      .on("click", "#ThisWeek", function () {
        if (
          $("#ShiftViewBox #ThisWeek")
            .attr("class")
            .indexOf("this-week-month-color") === -1
        ) {
          return;
        }
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        handleGetWeekArr();
        handleSetHeardTimeLabel();
        render();
      });

    // 上个月按钮
    $("#ShiftViewBox")
      .off("click", "#prev-month")
      .on("click", "#prev-month", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
        let changeYearMonth = dayjs(nowYearMonth)
          .subtract(1, "month")
          .format("YYYY-MM");
        handleGetMonthArr(dayjs(changeYearMonth).format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    // 下个月按钮
    $("#ShiftViewBox")
      .off("click", "#next-month")
      .on("click", "#next-month", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
        let changeYearMonth = dayjs(nowYearMonth)
          .add(1, "month")
          .format("YYYY-MM");
        handleGetMonthArr(dayjs(changeYearMonth).format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    // 本月按钮
    $("#ShiftViewBox")
      .off("click", "#ThisMonth")
      .on("click", "#ThisMonth", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }

        handleGetMonthArr(dayjs().format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    function render() {
      let paramsData = {
        orgId: $("#ShiftViewBox #orgId").val() || "",
        movementType: "2",
      };
      if (selectModeActive === "0") {
        paramsData.searchStartDate = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        paramsData.searchEndDate = `${weekArr[6].year}-${weekArr[6].month}-${weekArr[6].date}`;
      }
      if (selectModeActive === "1") {
        let length = monthArr.length - 1;
        paramsData.searchStartDate = `${monthArr[0].year}-${monthArr[0].month}-${monthArr[0].date}`;
        paramsData.searchEndDate = `${monthArr[length].year}-${monthArr[length].month}-${monthArr[length].date}`;
      }
      $.ajax({
        url: API.getPageAllList,
        type: "post",
        async: false,
        data: paramsData,
        success: function (res) {
          if (res.success) {
            if (res.object.length > 0) {
              const groundObject = {}; // 组合分组人员
              globalPersonArr = res.object;

              res.object.forEach(
                (item) => (groundObject[item.frequencyGroupingId] = [])
              );

              for (const inKey in groundObject) {
                let data = res.object.filter(
                  (filter) => inKey === String(filter.frequencyGroupingId)
                );
                groundObject[inKey] = groundObject[inKey].concat(data);
              }

              let strGroundPerson = ""; // 表格数据
              if (selectModeActive === "0") {
                for (const groundKey in groundObject) {
                  strGroundPerson += renderWeekMonthPersonTrFn(
                    groundObject[groundKey],
                    groundKey,
                    "week"
                  );
                }
                $("#ShiftViewBox #WeekSchedulingTboU").html(strGroundPerson);
              } else {
                for (const groundKey in groundObject) {
                  strGroundPerson += renderWeekMonthPersonTrFn(
                    groundObject[groundKey],
                    groundKey,
                    "month"
                  );
                }
                $("#ShiftViewBox #MonthSchedulingTboU").html(strGroundPerson);

                let echoSelectMonthPerson = null;
                if (monthSelectPersonId) {
                  echoSelectMonthPerson = $(
                    "#ShiftViewBox #MonthSchedulingTboU"
                  ).find(`[employeeid=${monthSelectPersonId}]`);
                } else {
                  echoSelectMonthPerson = $(
                    "#ShiftViewBox #MonthSchedulingTboU"
                  )
                    .children()
                    .eq(0);
                }

                monthSelectPersonId = echoSelectMonthPerson.attr("employeeid");
                monthSelectPersonGroupId =
                  echoSelectMonthPerson.attr("groupid");

                if (monthSelectPersonGroupId === "null") {
                  monthSelectPersonGroupId = "";
                }

                echoSelectMonthPerson.toggleClass("active");
              }
              handleResetTrNoFn();
              handleEachSchedulingData();
            } else {
              let colSpan = selectModeActive === "0" ? 10 : 3;
              let NoDataTr = `<tr><td colSpan=${colSpan}>暂无数据</td></tr>`;

              if (selectModeActive === "0") {
                $("#ShiftViewBox #WeekSchedulingTboU").html(NoDataTr);
              }

              if (selectModeActive === "1") {
                monthSelectPersonId = null;
                monthSelectPersonGroupId = null;
                $("#ShiftViewBox #MonthSchedulingTboU").html(NoDataTr);
                $("#ShiftViewBox #SetMonthClassesTable tbody").html(
                  '<tr><td colSpan="7">暂无数据</td></tr>'
                );
              }
            }
          } else {
            layer.msg("操作失败");
          }
        },
      });
    }

    // 回显排班数据 接口
    function handleEachSchedulingData(orgId) {
      if (selectModeActive === "0") {
        $("#ShiftViewBox .areaContent").each((index, item) => $(item).text(""));
      } else {
        $("#ShiftViewBox .areaContent").each((index, item) => {
          const dayTopItem = $(item).children().eq(0);
          $(item).text("");
          $(item).prepend(dayTopItem);
        });
      }

      useScheduleArr = [];

      const titleScheduling = $("#ShiftViewBox #titleScheduling");
      const beginYear = $(titleScheduling).find("#begin-year").text();
      const beginMonth = $(titleScheduling).find("#begin-month").text();
      const beginDate = $(titleScheduling).find("#begin-date").text();
      const endYear = $(titleScheduling).find("#end-year").text();
      const endMonth = $(titleScheduling).find("#end-month").text();
      const endDate = $(titleScheduling).find("#end-date").text();

      startWeekDate = `${beginYear}年${beginMonth}月${beginDate}日`;
      endWeekDate = `${endYear}年${endMonth}月${endDate}日`;

      let dataStartDate = `${beginYear}-${beginMonth}-${beginDate}`;
      let dataEndDate = `${endYear}-${endMonth}-${endDate}`;

      const data = {
        employeeIds: globalPersonArr.map((item) => item.employeeId),
        empOrgId: $("#ShiftViewBox #orgId").val() || "",
        startDate: dataStartDate,
        endDate: dataEndDate,
      };

      if (selectModeActive === "1") {
        let length = monthArr.length - 1;
        data.employeeIds = [monthSelectPersonId];
        data.startDate = `${monthArr[0].year}-${monthArr[0].month}-${monthArr[0].date}`;
        data.endDate = `${monthArr[length].year}-${monthArr[length].month}-${monthArr[length].date}`;
      }

      $.ajax({
        url: API.cheduleinfoGetSchedule,
        contentType: "application/json; charset=utf-8",
        type: "post",
        async: false,
        data: JSON.stringify(data),
        success: function (res) {
          if (res.success && res.object.length > 0) {
            let map = new Map();
            for (let item of res.object) {
              if (!map.has(item.frequencyId)) {
                map.set(item.frequencyId, item);
              }
            }
            useScheduleArr = [...map.values()];

            res.object.forEach((dataItem) => {
              // 获取thead头部 日期的索引
              const dateTr = $("#ShiftViewBox #SchedulingTable #titleId")
                .find(`[date="${dataItem.schedulingDate}"]`)
                .index();
              // 拿到索引 与用户tr进行匹配 找到用户那一天的 排班小格子
              const employeeIdTr = $("#ShiftViewBox #WeekSchedulingTboU").find(
                `[employeeId="${dataItem.employeeId}"]`
              );
              let userTrTd = employeeIdTr.children()[dateTr];

              if (selectModeActive === "1") {
                userTrTd = $("#ShiftViewBox #SetMonthClassesTable").find(
                  `[date="${dataItem.schedulingDate}"]`
                )[0];
              }

              if (userTrTd) {
                const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
                if (colorWhite.indexOf(dataItem.frequencyColour) !== -1) {
                  dataItem.frequencyColour = "#000";
                }
                //回显排班时间
                let timeHtml = "";
                const frequencyTimeArr =
                  dataItem.frequencyTime.split(",") || [];
                frequencyTimeArr.forEach((time) => {
                  timeHtml += `<div
                                  class="shiftTime"
                                >${time}
                                </div>`;
                });
                // 将已选班次 拼接入table内
                let span = `<span
                            data-id='${dataItem.frequencyId}'
                            data-color='${dataItem.frequencyColour}'
                            data-name='${dataItem.frequencyName}'
                            data-time='${dataItem.frequencyTime}'
                            style="color: ${dataItem.frequencyColour}"
                          >
                            ${dataItem.frequencyName}
                          </span>
                          ${timeHtml}
                          `;
                userTrTd.innerHTML += span;
              }
            });
          }
        },
      });
    }

    function renderWeekMonthPersonTrFn(arr, key, type) {
      //value       分组id
      //groundName  分组name
      //data        分组数据
      let html = "";
      let isgroup = Number(key !== "null");
      if (key !== "null") {
        html += `<tr class="groupTit" groupid="${key}">
                  <td 
                    style="background-color: rgb(255, 153, 0);
                    text-align: left !important;" 
                    colSpan="10" 
                    isgroup="${isgroup}" 
                    group="${arr.length}"
                   >
                    ${arr[0].frequencyGroupingName}
                  </td>
                </tr>`;
      }
      let tableSelectArea = "";
      if (type === "week") {
        let yyyymmdd0 = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        let yyyymmdd1 = `${weekArr[1].year}-${weekArr[1].month}-${weekArr[1].date}`;
        let yyyymmdd2 = `${weekArr[2].year}-${weekArr[2].month}-${weekArr[2].date}`;
        let yyyymmdd3 = `${weekArr[3].year}-${weekArr[3].month}-${weekArr[3].date}`;
        let yyyymmdd4 = `${weekArr[4].year}-${weekArr[4].month}-${weekArr[4].date}`;
        let yyyymmdd5 = `${weekArr[5].year}-${weekArr[5].month}-${weekArr[5].date}`;
        let yyyymmdd6 = `${weekArr[6].year}-${weekArr[6].month}-${weekArr[6].date}`;
        tableSelectArea = `
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd0}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd1}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd2}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd3}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd4}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd5}></td>
          <td draggable="false" width="10%" class="areaContent" date=${yyyymmdd6}></td>
        `;
      }
      arr.forEach((j) => {
        html += `
              <tr 
                employeeId="${j.employeeId}" 
                sort="${j.sort}"
                employeeName="${j.employeeName}"
                employeeNo="${j.employeeNo}"
                groupid="${key}"
                groupname="${arr[0].frequencyGroupingName}"
              >
                <td width="3%" style="font-weight: bold;background-color: #F9FAFB;"></td>
                <td width="10%" style="font-weight: bold;">${j.employeeNo}</td>
                <td width="8%" id="SchedulingDraggable" draggable="false" style="font-weight: bold;">${
                  j.employeeName
                }</td>
                <td width="9%" style="font-weight: bold;">${
                  j.personalIdentity || ""
                }</td>
                ${tableSelectArea}
              </tr>`;
      });
      return html;
    }

    // 查看个人排班详情
    $("#ShiftViewBox")
      .off("click", "#SchedulingDraggable")
      .on("click", "#SchedulingDraggable", function (e) {
        if (selectModeActive === "0") {
          const year = $("#ShiftViewBox #begin-year").text();
          const month = $("#ShiftViewBox #begin-month").text();
          const data = {
            employeeId: $(this).parent().attr("employeeid"),
            empOrgId: $("#ShiftViewBox #orgId").val(),
            schedulingDate: `${year}-${month}`,
          };
          $.quoteFun(
            "workforceManagement/workforce/modules/crewschedulingInfo",
            {
              title: "排班详情",
              data,
              statisticsPage: false,
              isWorkOvertime: false,
              ref: function () {},
            }
          );
        } else {
          const clickTr = $(this).parent();
          if (!$(clickTr).hasClass("active")) {
            clickTr.toggleClass("active");
          }
          clickTr.siblings().removeClass();
          monthSelectPersonId = "";
          monthSelectPersonGroupId = "";
          monthSelectPersonId = clickTr.attr("employeeid");
          monthSelectPersonGroupId = clickTr.attr("groupid");

          if (monthSelectPersonGroupId === "null") {
            monthSelectPersonGroupId = "";
          }

          handleEachSchedulingData();
        }
      });

    //重新对序号排序的父子算法
    function handleResetTrNoFn() {
      let index = 1;
      let tableDom =
        selectModeActive === "0"
          ? $("#ShiftViewBox #WeekSchedulingTboU")
          : $("#ShiftViewBox #MonthSchedulingTboU");

      let trArr = $(tableDom).find("[employeeId]");
      for (let i = 0; i < trArr.length; i++) {
        $(trArr[i]).find("td")[0].innerHTML = index;
        $(trArr[i]).attr("sort", index);
        index++;
      }
    }

    // 导出1
    $("#ShiftViewBox")
      .off("click", "#schedulingExportBtn")
      .on("click", "#schedulingExportBtn", function () {
        printOrExportTableFunction("export", 1);
      });

    // 打印1
    $("#ShiftViewBox")
      .off("click", "#schedulingPrinitBtn")
      .on("click", "#schedulingPrinitBtn", function () {
        printOrExportTableFunction("print", 1);
      });

    // 导出2
    $("#ShiftViewBox")
      .off("click", "#JKSchedulingExport")
      .on("click", "#JKSchedulingExport", function () {
        $("#ShiftViewBox #SchedulingTable .shiftTime").remove();
        printOrExportTableFunction("export", 2);
        setTimeout(() => {
          render();
        }, 1000);
      });

    // 月导出
    $("#ShiftViewBox")
      .off("click", "#MonthExport")
      .on("click", "#MonthExport", function () {
        var schedulingDate = `${monthArr[0].year}-${monthArr[0].month}`;
        var empOrgId = $("#ShiftViewBox #orgId").val() || "";
				var empOrgName = $("#ShiftViewBox #orgName").val() || "";
        var url =
          common.url +
          "/ts-hrms/schedulingmanage/getWorkingtableMonthExport?schedulingDate=" +
          schedulingDate +
          "&empOrgId=" +
          empOrgId+
					"&empOrgName=" +
					empOrgName;
        location.href = url;
      });

    // 打印2
    $("#ShiftViewBox")
      .off("click", "#JKSchedulingPrinit")
      .on("click", "#JKSchedulingPrinit", function () {
        $("#ShiftViewBox #SchedulingTable .shiftTime").hide();
        printOrExportTableFunction("print", 2);
        $("#ShiftViewBox #SchedulingTable .shiftTime").show();
      });

    function printOrExportTableFunction(type, styleType) {
      if (isSetBoolean) {
        layer.msg("请先结束排班");
        return;
      }
      let titleType = type === "export" ? "导出" : "打印";
      let orgName = $("#ShiftViewBox #personnelFormTreeSearch").val();
      if (isEmpty(orgName)) {
        orgName = common.hrUserInfo.empDeptName;
      }

      let printOrExportTitleHtml = `<tr
                            printOrExport="true"
                            style="text-align: center;font-size: 20px;"
                          >
                            <td colSpan="10">
                              ${orgName}排班表
                            </td>
                          </tr>
                          <tr
                            class="groupTit"
                            printOrExport="true"
                          >
                            <td colSpan="10">
                              排班日期: ${startWeekDate} ～ ${endWeekDate}
                            </td>
                          </tr>
                          `;
      let printOrExportFootHtml = `
                          <tr id="ExportOrPrintTableFoot" printOrExport="true">
                            <td colspan="4">
                                科室名称: ${orgName}
                            </td>
                            <td colspan="3">
                               ${titleType}日期: ${today.year}/${today.month}/${today.date}
                            </td>
                            <td colspan="3">
                                排班人: ${common.userInfo.username}
                            </td>
                          </tr>
                          `;

      $("#ShiftViewBox #SchedulingTable thead").prepend(printOrExportTitleHtml);

      if (styleType === 2) {
        let scheduleInfoTr = ``;
        useScheduleArr.forEach((item) => {
          scheduleInfoTr += `
            <tr printOrExport="true">
              <td colspan="4">
                班次名称: ${item.frequencyName}
              </td>
              <td colspan="6">
                班次时间: ${item.frequencyTime}
              </td>
            </tr>
          `;
        });
        $("#ShiftViewBox #SchedulingTable #WeekSchedulingTboU").append(
          scheduleInfoTr
        );
      }

      $("#ShiftViewBox #SchedulingTable #WeekSchedulingTboU").append(
        printOrExportFootHtml
      );

      $("#ShiftViewBox #SchedulingTable").attr("border", "1");
      $("#ShiftViewBox #SchedulingTable tr td").css("text-align", "center");
      $("#ShiftViewBox #SchedulingTable tr th").css("text-align", "center");
      $("#ShiftViewBox #SchedulingTable .groupTit td").css(
        "text-align",
        "left"
      );

      $("#ShiftViewBox #ExportOrPrintTableFoot").css({
        border: "transparent",
      });
      $("#ShiftViewBox #ExportOrPrintTableFoot").children().eq(0).css({
        "text-align": "left",
      });
      $("#ShiftViewBox #ExportOrPrintTableFoot").children().eq(1).css({
        "text-align": "center",
      });
      $("#ShiftViewBox #ExportOrPrintTableFoot").children().eq(2).css({
        "text-align": "right",
      });
      const exprotName = `${today.year}年${today.month}月${orgName}排班`;
      switch (type) {
        case "export":
          tableToExcel("SchedulingTable", "schedulingExportBtn", exprotName);
          break;
        case "print":
          $("#ShiftViewBox #SchedulingTable").jqprint();
          break;
      }
      setTimeout(() => {
        $('#ShiftViewBox #SchedulingTable tr[printOrExport="true"]').remove();
        $("#ShiftViewBox #SchedulingTable").attr("border", "0");
      }, 500);
    }

    function tableToExcel(tableid, btnname, exprotName) {
      var uri = "data:application/vnd.ms-excel;base64,",
        template =
          '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
        base64 = function (s) {
          return window.btoa(unescape(encodeURIComponent(s)));
        },
        dataFormat = function (s, c) {
          return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
          });
        };
      //根据ID获取table表格HTML
      var table = document.getElementById(tableid);
      var ctx = {
        worksheet: "Worksheet",
        table: table.innerHTML,
      };

      var alink = document.createElement("a");
      alink.href = uri + base64(dataFormat(template, ctx));
      alink.download = `${exprotName}.xls`;
      alink.click();
    }

    function splitArray(arr, chunkSize) {
      let result = [];
      for (let i = 0, len = arr.length; i < len; i += chunkSize) {
        result.push(arr.slice(i, i + chunkSize));
      }
      return result;
    }
  };
});
