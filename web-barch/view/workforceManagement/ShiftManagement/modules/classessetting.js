define(function (require, exports, module) {
  exports.init = function (opt, html) {
    $("#shift-box #shiftBoxContent")[0].innerHTML = html;

    layui.use(["form", "layer", "zTreeSearch"], function () {
      var form = layui.form,
        layer = layui.layer,
        zTreeSearch = layui.zTreeSearch;

      var trasenTable = new $.trasenTable("shiftSettingTable", {
        url:
          common.url + "/ts-hrms/schedulingfrequency/getJurisdictionPageList",
        pager: "grid-pager-shiftSettingPager",
        shrinkToFit: true,
        queryFormId: "classessettingForm",
        mtype: "post",
        colModel: [
          {
            label: "ID",
            name: "frequencyId",
            width: "auto",
            hidden: true,
          },
          {
            label: "班次名称",
            sortable: false,
            name: "frequencyName",
            align: "center",
            width: "80px",
          },
          {
            label: "使用科室",
            name: "orgName",
            sortable: false,
            editable: false,
            align: "center",
          },
          {
            label: "考勤类型",
            name: "frequencyTypeName",
            align: "center",
            width: "70px",
            sortable: false,
            formatter: function (cell, opt, row) {
              var text = "";
              if (row.frequencyType == "1") {
                text = "上班";
              } else if (row.frequencyType == "2") {
                text = "休息";
              } else if (row.frequencyType == "3") {
                text = "假勤";
              } else if (row.frequencyType == "4") {
                text = "晚夜班";
              }
              return text;
            },
          },
          {
            name: "frequencyType",
            hidden: true,
          },
          {
            label: "班次类型",
            sortable: false,
            name: "shiftAttribute",
            align: "center",
            width: "80px",
          },
          {
            label: "时间段",
            sortable: false,
            name: "shiftAttributeTime",
            align: "center",
            width: "70px",
          },
          {
            label: "所属科室",
            name: "orgId",
            sortable: false,
            hidden: true,
          },
          {
            name: "shiftAttribute",
            hidden: true,
          },
          {
            name: "shiftAttributeTime",
            hidden: true,
          },
          {
            label: "出勤天数",
            sortable: false,
            name: "dayLong",
            align: "center",
            width: "50px",
          },
          {
            label: "颜色",
            name: "frequencyColour",
            align: "center",
            width: "100px",
            sortable: false,
            formatter: function (cell, opt, row) {
              return `<div 
                    data-rowColor="${row.frequencyColour}"
                    style='background:${row.frequencyColour};
                    width:100%;
                    height: 15px'
                  >
                  </div>`;
            },
          },
          {
            label: "班次时长(H)",
            name: "dayHours",
            align: "center",
            sortable: false,
            width: "70px",
          },
          {
            label: "考勤时间",
            name: "frequencyTime",
            sortable: false,
            align: "center",
          },
          {
            label: "备注",
            name: "remark",
            sortable: false,
            align: "center",
          },
        ],
      });
      initTree();

      function initTree() {
        zTreeSearch.init("#classessettingForm #org", {
          url: common.url + "/ts-basics-bottom/organization/getTree",
          type: "post",
          checkbox: false,
          condition: "name",
          callback: function () {
            $('#classessettingForm [name="orgId"]').val("");
            $('#classessettingForm [name="orgName"]').val("");
            trasenTable.refresh();
          },
          zTreeOnClick: function (nodesId, treeNode) {
            $('#classessettingForm [name="orgId"]').val(treeNode.id);
            refreshTable();
          },
        });
        form.render("select");
      }

      // 搜索
      form.on("submit(classessettingSearch)", function (data) {
        refreshTable();
      });

      // 新增
      $("#Classessetting .operation")
        .off("click", "#shiftSettingTableAdd")
        .on("click", "#shiftSettingTableAdd", function () {
          $.quoteFun(
            "workforceManagement/ShiftManagement/modules/classessettingAdd",
            {
              title: "新增",
              ref: refreshTable,
            }
          );
        });

      // 表格刷新
      function refreshTable() {
        trasenTable.refresh();
      }

      // 按钮刷新
      $("#Classessetting")
        .off("click", "#classessettingTableRefresh")
        .on("click", "#classessettingTableRefresh", function () {
          $(`#Classessetting [name="frequencyName"]`).val("");
          $('#classessettingForm [name="orgId"]').val("");
          $('#classessettingForm [name="orgName"]').val("");
          trasenTable.refresh();
        });

      //编辑
      $("#Classessetting .operation")
        .off("click", "#shiftSettingTableEditor")
        .on("click", "#shiftSettingTableEditor", function () {
          var rowData = trasenTable.getSelectRowData();
          if (rowData.length || rowData.length == 0) {
            layer.msg("请选择一条记录进行操作！");
            return false;
          }
          rowData.frequencyColour = $(rowData.frequencyColour).attr(
            "data-rowcolor"
          );
          // rowData.frequencyType = rowData.frequencyType === "上班" ? "1" : "2";

          $.quoteFun(
            "workforceManagement/ShiftManagement/modules/classessettingAdd",
            {
              title: "编辑",
              ref: refreshTable,
              data: rowData,
            }
          );
        });

      // 删除
      $("#Classessetting .operation")
        .off("click", "#shiftSettingTableDel")
        .on("click", "#shiftSettingTableDel", function () {
          var rowData = trasenTable.getSelectRowData();
          if (rowData.length || rowData.length == 0) {
            layer.msg("请选择一条记录进行操作！");
            return false;
          }
          layer.confirm(
            "确定要删除该条记录吗？",
            {
              btn: ["确定", "取消"],
              title: "提示",
              closeBtn: 0,
            },
            function () {
              $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url:
                  common.url +
                  "/ts-hrms/schedulingfrequency/" +
                  rowData.frequencyId,
                success: function (res) {
                  if (res.success) {
                    refreshTable();
                    layer.closeAll();
                    layer.msg("操作成功");
                  } else {
                    layer.msg(res.message || "操作失败");
                  }
                },
              });
            },
            function () {}
          );
        });
    });
  };
});
