"use strict";
define(function (require, exports, module) {
  var init = function () {
    return perform();
  }
  module.exports = {
    init: init
  }
  var perform = function () {
    layui.use(['form', 'laydate', 'upload', 'trasen', 'zTreeSearch'], function () {
      var form = layui.form,
        layer = layui.layer,
        upload = layui.upload,
        laydate = layui.laydate,
        trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch,
				HolidayOvertimeTable = null;


      // 渲染月份
      var now = new Date(); //当前日期
      var nowDayOfWeek = now.getDay(); //今天本周的第几天
      var nowDay = now.getDate(); //当前日
      var nowMonth = now.getMonth(); //当前月
      var nowYear = now.getYear(); //当前年
      nowYear += (nowYear < 2000) ? 1900 : 0; //
      var lastMonthDate = new Date(); //上月日期
      lastMonthDate.setDate(1);
      lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
      var lastYear = lastMonthDate.getYear();
      var lastMonth = lastMonthDate.getMonth();
	  var scrollHeight = 0;
	  var scrollId = '';
      //格式化日期：yyyy-MM-dd
      function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();
        if (mymonth < 10) {
          mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
          myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
      }
      //获得某月的天数
      function getMonthDays(myMonth) {
        var monthStartDate = new Date(nowYear, myMonth, 1);
        var monthEndDate = new Date(nowYear, myMonth + 1, 1);
        var days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);
        return days;
      }

      //获得本月的开始日期
      function getMonthStartDate() {
        var monthStartDate = new Date(nowYear, nowMonth, 1);
        return formatDate(monthStartDate);
      }
      //获得本月的结束日期
      function getMonthEndDate() {
        var monthEndDate = new Date(nowYear, nowMonth, getMonthDays(nowMonth));
        return formatDate(monthEndDate);
      }

      $("#HolidayOvertimeDate").val(getMonthStartDate() + " ~ " + getMonthEndDate());
      $("#HolidayOvertimeForm input[name='startDate']").val(getMonthStartDate());
      $("#HolidayOvertimeForm input[name='endDate']").val(getMonthEndDate());
      laydate.render({
        elem: '#HolidayOvertimeDate',
        type: 'date',
        range: '~',
        done: function (value) {
          var dateArr = value.split(' ~ ');
          $("#HolidayOverStartDate").val(dateArr[0]);
          $("#HolidayOverEndDate").val(dateArr[1]);
          refreshTable();
        }
      });

      /*      laydate.render({
              elem: '#HolidayOvertimeDate',
              format: 'yyyy-MM-dd',
              type: 'month', // 年月选择器
              btns: ['clear', 'confirm'],
              value: year + "-" + month, // 默认选择当前日期的上一个月
              max: dateStr // 最大值只可选择当前日期的上一个月（max不支持只填写年月的格式）
            });
       */
      form.render("select");

      let tableData = [];
      let tableCols = [];
	  
	  var selectedRowIndex = 0;  //记住记录号
	  var scrollPosition = 0;     //记住jqgrid列表中页面的滚动位置

      initTable()
      // var trasenTable = new $.trasenTable("HolidayOvertimeTable", {
      //   url: common.url + '/ts-hrms/schedulingmanage/getOvertimeWorkingtablePageList ',
      //   pager: 'grid-pager-HolidayOvertimePager',
      //   shrinkToFit: true,
      //   queryFormId: 'HolidayOvertimeForm',
      //   mtype: 'post',
      //   sortname: 't4.name',
      //   postData: {
      //     startDate: getMonthStartDate(),
      // endDate:getMonthEndDate()
      //   },
      //   colModel: [
      //     {label: 'ID', name: 'employeeId', hidden: true, align: 'center', sortable: false},
      //     {label: '工号',index:'t3.employee_no', name: 'employeeNo', width: 70, align: 'center'},
      //     {label: '姓名',index:'t3.employee_name', name: 'employeeName', width: 80, align: 'center'},
      //     {label: '科室',name:'orgName', index: 't4.name', width: 120, align: 'center',},
      //     {label: '时长（天）', name: 'dayLong', width: 120, align: 'center', sortable: false},
      //     {label: '班次', name: 'frequencyName', width: 120, align: 'center', sortable: false},
      //     {label: '日期', name: 'schedulingDate', width: 120, align: 'center', sortable: false},
      //    /* {label: '加班天数', name: 'dayLong', width: 90, align: 'center', sortable: false},
      //     {label: '加班小时数', name: 'dayHours', width: 90, align: 'center', sortable: false}, */
      //     {
      //       label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
      //       name: '',
      //       sortable: false,
      //       width: 40,
      //       editable: false,
      //       title: false,
      //       align: 'center',
      //       classes: 'visible jqgrid-rownum ui-state-default',
      //       formatter: function (cell, options, row) {
      //         var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
      //         var btns = '';
      //         btns += '<button class="layui-btn " id="HolidayOvertimeTableDetailsBtn" row-id="' + row.employeeId + '"  row-deptName="' + row.orgName + '"  row-empName="' + row.employeeName + '" row-empNo="' + row.employeeNo + '">  <i class="fa fa-eye deal_icon"  aria-hidden="true"></i> 查看明细 </button>';
      //         html += btns + '</div></div>';
      //         return html;
      //       },
      //     },
      //   ],
      // });

      function initTable() {
        tableCols = getTableColHanle();
        tableData = getTableDataHandle();

        function getTableDataHandle() {
          var result = [];
          $.ajax({
            type: "post",
            url: common.url + '/ts-hrms/schedulingmanage/getOvertimeWorkingtablePageList',
            async: false,
            contentType: "application/x-www-form-urlencoded",
            data: {
              startDate: $("#HolidayOverStartDate").val(),
              endDate: $("#HolidayOverEndDate").val(),
              sortname: 't4.name',
              empOrgId: $('#HolidayOvertimeForm [name="empOrgId"]').val(),
              pageSize: 9999,
            },
            success: function (res) {
              if (res.rows.length) {
                result = res.rows;
              }
            },
            error: function (res) {
              res = JSON.parse(res.responseText);
              layer.msg(res.message);
            }
          });
          return result;
        }

        function getTableColHanle() {
          let result = [];
          $.ajax({
            type: "post",
            url: common.url + '/ts-hrms/scheduleinfo/getOvertimeWorkingtableTitleCols',
            async: false,
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
              startDate: $("#HolidayOverStartDate").val(),
              endDate: $("#HolidayOverEndDate").val(),
            }),
            success: function (res) {
              if (res.success && res.statusCode === 200) {
                res.object.forEach(item => {
                  item.align = 'center'
                })
                result = res.object;
              }
            },
            error: function (res) {
              res = JSON.parse(res.responseText);
              layer.msg(res.message);
            }
          });
          return result
        }

        $("#HolidayOvertimeTable").remove();
        $("#HolidayOvertimeTableBox").append("<table id=\"HolidayOvertimeTable\"></table>"); //再新增一个grid的渲染容器
        var cols = [
          {
            label: 'ID',
            name: 'employeeId',
            hidden: true,
            align: 'center',
            sortable: false
          }, 
          {
            label: '工号',
            index: 't3.employee_no',
            name: 'employeeNo',
            width: 70,
            align: 'center'
          },
          {
            label: '姓名',
            index: 't3.employee_name',
            name: 'employeeName',
            width: 80,
            align: 'left'
          },
          {
            label: '身份证',
            index: 't3.identity_number',
            name: 'identityNumber',
            width: 100,
            align: 'center'
          },
          {
            label: '科室',
            name: 'orgName',
            index: 't4.name',
            width: 120,
            align: 'left',
          },
          {
            label: '时长（天）',
            name: 'dayLong',
            width: 120,
            align: 'center',
            sortable: false
          }
        ].concat(
          tableCols,
          {
            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
            name: '',
            sortable: false,
            width: 40,
            editable: false,
            title: false,
            align: 'center',
            classes: 'visible jqgrid-rownum ui-state-default',
            formatter: function (cell, options, row) {
              var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
              var btns = '';
              btns += '<button class="layui-btn " id="HolidayOvertimeTableDetailsBtn" row-id="' + row.employeeId + '"  row-deptName="' + row.orgName + '"  row-empName="' + row.employeeName + '" row-empNo="' + row.employeeNo + '">  <i class="fa fa-eye deal_icon"  aria-hidden="true"></i> 查看明细 </button>';
              html += btns + '</div></div>';
              return html;
            },
          }
        );
        // 招聘计划岗位明细列表
        $.jgrid.gridUnload("HolidayOvertimeTable");
        HolidayOvertimeTable = new $.trasenTable("HolidayOvertimeTable", {
          data: tableData,
          datatype: "local",
          colModel: cols,
          rowNum: 999999,
          queryFormId: 'HolidayOvertimeForm',
					gridComplete:function(){
						setTimeout(function() {
							$("#HolidayOvertimeTable").closest(".ui-jqgrid-bdiv").scrollTop(scrollPosition);
						}, 0);
					}
        });
        jQuery("#HolidayOvertimeTable").jqGrid("setGroupHeaders", {
          useColSpanStyle: true,
          groupHeaders: [{
            startColumnName: (tableCols[0] || '').name,
            numberOfColumns: tableCols.length,
            titleText: "日期"
          }]
        });
      }
	  
	  //修改参数
	  $('body')
	    .off('click', '.bcclick')
	    .on('click', '.bcclick', function () {
	      var bcid  = $(this).attr("bcid");
		  var bcname  = $(this).attr("bcname");
		  
		  layer.confirm(` <div id="bcupdate" >
　　						<label><font color="red">`+bcname+`</font> 节假日是否算休息：</label>
							</br>
						　　<input type="radio" name ="rdSpeed" value="休息" checked >是
						　　<input type="radio" name ="rdSpeed" value="">否
						</div>`,
		  {
        btn: ['确定', '取消'],
        title: '提示',
        closeBtn: 0
		  }, 
      function(index){
		    var bcupdate = $("#bcupdate input[type='radio']:checked").val();
        var content = {
				"frequencyId": bcid,
				"jjrType":bcupdate
			   }
				selectedRowIndex =   $("#HolidayOvertimeTable").jqGrid("getGridParam","selrow");
				scrollPosition = $("#HolidayOvertimeTable").closest(".ui-jqgrid-bdiv").scrollTop();
		    	$.ajax({
		    		type: 'POST',
		    		url: "/ts-hrms/schedulingfrequency/updateRest",
		    		contentType: 'application/json; charset=utf-8',
		    		data: JSON.stringify(content),
		    		dataType: "json",
					success: function (data) {
						if(data.success){
							initTable();
							layer.close(index);
							layer.msg('操作成功');
						}
		    		}
		    	});
		   layer.close(index);
		  }, function(){
			  //取消
		  });
			return false;
	    });

      //查看明细按钮
      $('body')
        .off('click', '#HolidayOvertimeTableDetailsBtn')
        .on('click', '#HolidayOvertimeTableDetailsBtn', function () {
          var employeeId = $(this).attr("row-id");
          var deptName = $(this).attr("row-deptName");
          var empName = $(this).attr("row-empName");
          var empNo = $(this).attr("row-empNo");

          var detailsData = {
            empOrgId: $('#HolidayOvertimeForm [name="empOrgId"]').val(),
            employeeId: employeeId,
            schedulingDate: $("#HolidayOvertimeDate").val()
          };
          $.quoteFun('/workforceManagement/workforce/modules/crewschedulingInfo', {
            title: deptName + ' ' + empName + ' [' + empNo + '] 排班明细',
            data: detailsData,
            statisticsPage: true,
            isWorkOvertime: true
          });
        });

      //导出
      $("#HolidayOvertime").off("click", "#holidayOvertimeExport").on("click", "#holidayOvertimeExport", function () {
        var schedulingDate = $("#HolidayOvertimeForm input[name='schedulingDate']").val();
        var empOrgId = $("#HolidayOvertimeForm input[name='empOrgId']").val();
        var startDate = $("#HolidayOvertimeForm input[name='startDate']").val();
        var endDate = $("#HolidayOvertimeForm input[name='endDate']").val();
        layer.confirm('确定要导出吗？', {
          btn: ['确定', '取消'],
          title: '提示',
          closeBtn: 0
        }, function (index) {
          var date = $("#transactionDateSearch").val();
          var url = common.url + "/ts-hrms/schedulingmanage/getOvertimeWorkingtableExport?schedulingDate=" +
            schedulingDate + "&empOrgId=" + empOrgId + "&startDate=" + startDate + "&endDate=" + endDate;;
          location.href = url;
          layer.close(index);
        }, function () {});
      }); 
	  
 	/*  $("#HolidayOvertime").off("click", "#holidayOvertimeExport").on("click", "#holidayOvertimeExport", function () {
			jqgridToExcel1("HolidayOvertimeTable","节假日加班统计报表");
	  }); */
	  function jqgridToExcel1(id, fileName) {
		  
			var Table = $("#gview_" + id);
			var Tal = $("#" + id);
			var tableStr = '<table border="1"><thead>{thead}</thead>{tbody}</table>';
			var thtr = Table.children("div:eq(1)").children("div").children("table").children("thead").children('tr').eq(1);//获取jqgrid的表头tr
			var tbtr = Tal.children("tbody").children('tr');//获取jqgrid内容tr
			var thrlength = thtr.length;//表头tr数
		
			var tbrlength = tbtr.length;//内容tr数
			var theadStr =   $(".ui-jqgrid-htable.ui-common-table thead tr:not(:first-child)").html();
			var tbodyStr = '';
		  /*  for (var i = 0; i < thrlength; i++) {//循环获取表头每条tr内容
				if (thtr[i].style.height != 'auto') {//获取height不为auto的tr内容
					theadStr += '<tr>';
					var th2 = Table.children("div:eq(1)").children("div").children("table").children("thead").children('tr:eq(' + i + ')').children('th');
					var th2length = th2.length;
					var th2th = '';
					for (var y = 0; y < th2length; y++) {//循环获取tr内display不为none的td，并递加
						if (th2[y].style.display != 'none') {
							th2th += "<th>" + th2[y].innerText + "</th>";
						}
					}
					theadStr += th2th;
					theadStr += '</tr>'
				}
			} */
			
			for (var i = 1; i < tbrlength; i++) {//循环获取内容每条tr内容
				tbodyStr += '<tr>';
				var td2 = Tal.children("tbody").children('tr:eq(' + i + ')').children('td');
				var td2length = td2.length;
				var td2td = '';
				for (var y = 0; y < td2length; y++) {
					if (td2[y].style.display != 'none') {
						td2td += "<td>" + td2[y].innerText + "</td>";
					}
				}
				tbodyStr += td2td;
				tbodyStr += '</tr>'
			}
		 var hejiTR = $(".footrow.footrow-ltr.ui-widget-content").html();
		 tbodyStr += hejiTR;
			tableStr = format(tableStr, {
				thead: theadStr,
				tbody: tbodyStr
			});
			tableToExcel(tableStr, fileName);
		};
	  											//替换thead,tbody
			function format(s, c) { return s.replace(/{(\w+)}/g, function (m, p) { return c[p]; }) };
			//导出Excel
			function tableToExcel (table, fileName) {
				
				var blob = new Blob([table], { type: "application/vnd.ms-excel" });
				if (($('body').hasClass('IE') || $('body').hasClass('InternetExplorer')) && window.navigator.msSaveOrOpenBlob) {
					window.navigator.msSaveBlob(blob, fileName + '.xls');
				}
				else {
					var blob = new Blob([table], { type: "application/vnd.ms-excel" });
					let href = window.URL.createObjectURL(blob);
					var a = document.createElement('a');
					document.body.appendChild(a);
					a.hreflang = 'zh';
					a.charset = 'utf8';
					a.href = href;
					a.download = fileName + '.xls';
					a.click();
					document.body.removeChild(a);
				}
			 
			};
	  
	  

      // 渲染科室树
      zTreeSearch.init('#HolidayOvertimeForm #org', {
        url: common.url + '/ts-basics-bottom/organization/getTree',
        type: 'post',
        checkbox: false,
        condition: 'name',
        callback: function () {
          $('#HolidayOvertimeForm [name="empOrgId"]').val('');
        },
        zTreeOnClick: function (nodesId, treeNode) {
          $('#HolidayOvertimeForm [name="empOrgId"]').val(treeNode.id);
          refreshTable()
        },
      });
      form.render('select');

      // 刷新
      function refreshTable() {
        initTable();
      }

      // 查询
      form.on('submit(HolidayOvertimeSearch)', function (data) {
        refreshTable();
      });

      // 重置
      form.on('submit(HolidayOvertimeRefresh)', function (data) {
        $('#HolidayOvertimeForm [name="empOrgId"]').val('');

        $("#HolidayOvertimeDate").val(getMonthStartDate() + " ~ " + getMonthEndDate());
        $("#HolidayOvertimeForm input[name='startDate']").val(getMonthStartDate());
        $("#HolidayOvertimeForm input[name='endDate']").val(getMonthEndDate());
        refreshTable();
      });
    });
  }
});
