'use strict';
define(function(require, exports, module) {
    exports.init = function (opt, html){
        var ed = {},
            url = common.url + '/ts-document/attachment/',
            token = $.cookie('token');

        var API = {
            saveKnowledge: '/ts-worksheet/knowledgeBase/save',//保存知识库
        };

        layui.use(['form', 'laydate', 'trasen', 'zTreeSearch', 'upload'], function(){
            var { form, laydate, trasen, zTreeSearch, upload } = layui;

            layer.open({
                type: 1,
                title: '新增知识点',
                closeBtn: 1,
                shadeClose: false,
                skin: 'addLibraryCSS',
                area: [ '900px', '80%' ],
                content: html,
                success: function(layero, index){
                    if(opt.data){
                        //初始化信息
                        trasen.setNamesVal($('#addLibraryForm'), opt.data );
                        
                    }
                    initEditor();
                },
                cancel: function (){
                  tinymce.remove('#knowledgeTextarea');
                }
            });

            renderKnowledgeType();//渲染知识类型选择树
            bindBtnClickEvent();//绑定点击事件

            //--------------------------页面元素渲染---------------------------------------
            //渲染知识类型选择树
            function renderKnowledgeType(){
                zTreeSearch.init('#knowledgeTypeName', {
                    url: common.url + '/ts-worksheet/knowledgeBase/selectKnowledgeTreeAllList',
                    type: 'get',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function(treeId, treeNode){
                        $("#knowledgeTypeId").val(treeNode.id);
                        $("#knowledgeTypeName").val(treeNode.fullPath);
                    }
                })
            }

            // -------------------------富文本编辑器---------------------------------------
            //初始化 富文本编辑器
            function initEditor(){
                tinymce.init({
                    selector: '#knowledgeTextarea',
                    auto_focus: true,
                    elementpath: false,
                    statusbar: false,
                    language: 'zh_CN',
                    plugins: ['quickbars', 'link', 'table', 'code', 'image', 'advlist', 'lists', 'media', 'paste', ],
                    menubar: '',
                    menu: {},
                    toolbar: ['undo redo newdocument | bold italic underline strikethrough backcolor forecolor formatselect | fontselect | fontsizeselect | lineheight ', 'cut copy paste | link image  media  | table  | alignleft aligncenter alignright alignjustify | outdent indent | bullist numlist | code'],
                    table_clone_elements: 'p',
                    table_grid: false,
                    fontsize_formats: '42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt 6.5pt 5.5pt 5pt',
                    font_formats: "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
                    width: '100%',
                    height: '100%',
                    auto_focus: true,
                    paste_enable_default_filters: false,
                    // images_upload_url: common.url + '/ts-document/attachment/fileUpload?module=workDesk&fillupf=2', //接受上传文件的后端地址
                    setup: function (editor) {
                        editor.on('init', function(ed) {
                            ed.target.editorCommands.execCommand('fontName', false, '宋体')
                        })
                    },
                    images_upload_handler: function(blobInfo, success, failure){
                      let formData = new FormData();
                      formData.append('file', blobInfo.blob());
                      $.ajax({
                        url: '/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk',
                        type: "post",
                        data: formData,
                        processData: false,
                        contentType: false,
                        async: false,
                        success: function (res) {
                          if(res.success == false){
                            failure('图片上传失败' +( res.message ? ': ' + res.message : '！'))
                            return;
                          }
                          success(res.object[0].filePath);
                        }
                      })
                    },
                    //paste_data_images: true,
                    paste_postprocess: function (plugin, args) {
                        let res = args.node.querySelectorAll('img');
                        var flag = false;
                        for (let i = 0; i < res.length; i++) {
                            if (res[i].src.indexOf('file:') != -1) {
                                flag = true;
                            }
                        }
                        if (flag) {
                            $.ajax({
                                type: 'get',
                                url: 'http://localhost:26789/file/readfile',
                                success: function (res) {
                                    updateImgBase64();
                                },
                                error: function (res) {
                                    layer.open({
                                        type: 1,
                                        skin: 'layui-layer-demo', //样式类名
                                        closeBtn: 0, //不显示关闭按钮
                                        anim: 2,
                                        shadeClose: true, //开启遮罩关闭
                                        content: '<div style="padding:10px">粘贴WORD图文模式，您需要先安装一个插件<a style="color:blue" href="/static/wordPasterPlug/trasenWordPaster.zip">点击下载</a>  <br><div>',
                                    });
                                },
                            });
                        }
                    },
                    file_picker_types: 'file media',
                    convert_urls: false, //这个参数加上去就可以了
                    media_alt_source: false,
                    media_filter_html: false,
                    powerpaste_word_import: 'merge', // 参数可以是propmt, merge, clear
                    powerpaste_html_import: 'merge', // propmt, merge, clear
                    powerpaste_allow_local_images: true, //允许带图片
                    paste_data_images: true,
                    file_picker_callback: function (cb, value, meta) {
                        if (meta.filetype == 'media') {
                            //创建一个隐藏的type=file的文件选择input
                            let input = document.createElement('input');
                            input.setAttribute('type', 'file');
                            input.onchange = function () {
                                var file = this.files[0];
                                fileUpload(file, cb);
                            };
                            //触发点击
                            input.click();
                        }
                    },
                    init_instance_callback: function (editor) {
                        tinyMCE.editors["knowledgeTextarea"].insertContent(opt.data.takeRemark);
                        //页面初始化事件
                        ed = editor;
                    },
                });
            }
            //文件上传
            function fileUpload(file, cb) {
                var formData = new FormData();
                //假设接口接收参数为file,值为选中的文件
                formData.append('file', file);
                $.ajax({
                    url: '/ts-document/attachment/fileUpload?module=workDesk&fillupf=2',
                    method: 'post',
                    contentType: false,
                    processData: false,
                    data: formData,
                    success: function (res) {
                        cb(res.location);
                    },
                });
            }
            //上传Base64图片
            function updateImgBase64() {
                let res = ed.iframeElement.contentWindow.document.querySelectorAll('img');
                let ajax = [];
                for (let i = 0; i < res.length; i++) {
                    if (res[i].src.indexOf('file:') != -1) {
                        ajax.push($.get(`http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`));
                    }
                }
                if (ajax.length != 0) {
                    Promise.all(ajax).then(_res => {
                        _res.forEach(_item => {
                            res[_item.dataIndex]['data-src'] = res[_item.dataIndex]['src'];
                            res[_item.dataIndex]['src'] = _item.base64;
                        });
                        updateImg();
                    });
                }
            }

            /**@desc 提交前替换图片请求服务器资源**/
            function updateImg() {
                let res = ed.iframeElement.contentWindow.document.querySelectorAll('img');
                let ajax = [];
                let dom = [];
                for (let i = 0; i < res.length; i++) {
                    if (res[i].src.indexOf('data:image/png;base64,') != -1) {
                        dom.push(res[i]);
                        ajax.push(
                            $.post(`${url}imageBase64Upload`, {
                                token,
                                module: 'richfile',
                                fillupf: '2',
                                imageBase64: res[i].src.split('data:image/png;base64,')[1],
                            })
                        );
                    }
                }
                if (ajax.length != 0) {
                    Promise.all(ajax).then(_res => {
                        _res.forEach((_item, index) => {
                            if (_item.success) {
                                dom[index].src = `${_item.object.location}`;
                                dom[index].removeAttribute('data-mce-src');
                            }
                        });
                        //submit();
                    });
                }
            }

            //------------------------------点击事件绑定--------------------------------------
            function bindBtnClickEvent(){
                //存草稿 点击事件
                form.on('submit(librarySaveDraft)', function (data) {
                    let saveData = data.field;
                    if(saveData.recommendedWorkHours == ''){
                        delete saveData.recommendedWorkHours;
                    }                    
                    
                    saveData.knowledgeContent = tinymce.get('knowledgeTextarea').getContent() || undefined;
                    saveData.knowledgeStatus = 0;//草稿

                    $.ajax({
                        url: API.saveKnowledge,
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(saveData),
                        async: false,
                        success: function (res) {
                            if(res.success){
                                tinymce.remove('#knowledgeTextarea');
                                layer.closeAll();
                                layer.msg('添加成功');
                            }
                            else{
                                layer.msg(res.message||'添加失败');
                            }
                        }
                    })
                });

                //提交按钮 点击事件
                form.on('submit(librarySave)', function(data){
                    let saveData = data.field;
                    if(saveData.recommendedWorkHours == ''){
                        delete saveData.recommendedWorkHours;
                    }

                    saveData.knowledgeContent = tinymce.get('knowledgeTextarea').getContent() || undefined;
                    saveData.knowledgeStatus = 1;//提交
                    
                    $.ajax({
                        url: API.saveKnowledge,
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(saveData),
                        async: false,
                        success: function (res) {
                            if(res.success){
                                tinymce.remove('#knowledgeTextarea');
                                layer.closeAll();
                                layer.msg('添加成功');
                            }
                            else{
                                layer.msg(res.message||'添加失败');
                            }
                        }
                    })
                })

                //取消按钮点击事件
                $('#libraryCancel').bind('click', function(){
                    tinymce.remove('#knowledgeTextarea');
                    layer.closeAll();
                })
            }

            //-------------------------------事件处理-------------------------------------------
            
        });

        
    }
});