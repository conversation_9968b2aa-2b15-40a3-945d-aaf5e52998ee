'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };

    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'trasen', 'zTreeSearch'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                zTreeSearch = layui.zTreeSearch,
                trasen = layui.trasen;

            var parames = $.getparames(); // 地址参数
            var keywords = '', // 检索关键字
                page = 1; // 当前页

            var table;

            // 初始化
            var init = function (data) {
                table = new $.trasenTable('portalMotifPageSetTable', {
                    url: common.url + '/ts-portal/api/portal/Aftintemplate/getAftintemplateListByname',
                    mtype: 'get',
                    pager: '#portalMotifPageSetTable-pager',
                    postData: data,
                    shrinkToFit: true,
                    colModel: [
                        { label: 'id', name: 'templateId', index: 'template_id', width: 'auto', align: 'center', editable: false, hidden: true },
                        { label: '名称', name: 'templateName', index: 'template_name', width: 120, editable: false },
                        { label: '应用终端', name: 'terminalname', index: 'terminalname', width: 140, editable: false },
                        { label: '角色ID', name: 'roleCode', index: 'role_code', width: 140, editable: false, hidden: true },
                        { label: '角色', name: 'roleName', index: 'role_name', width: 140, editable: false },
                        {
                            label: '启用',
                            name: 'isUse',
                            index: 'is_use',
                            width: 60,
                            align: 'center',
                            editable: false,
                            formatter: function (cellvalue, options, data) {
                                // 0:启用;1:禁用
                                switch (cellvalue) {
                                    case 0:
                                        cellvalue =
                                            '<input type="checkbox" name="" lay-filter="portalMotifSwitchpageset" rid="' +
                                            data.templateId +
                                            '" state="use" value="0" checked="checked" lay-skin="switch" style="display: none !important;">';
                                        break;
                                    case 1:
                                        cellvalue =
                                            '<input type="checkbox" name="" lay-filter="portalMotifSwitchpageset" rid="' +
                                            data.templateId +
                                            '" state="use" value="1" lay-skin="switch" style="display: none !important;">';
                                        break;
                                }
                                return cellvalue;
                            },
                        },
                        {
                            label: '锁定',
                            name: 'isLock',
                            index: 'is_lock',
                            width: 60,
                            align: 'center',
                            editable: false,
                            formatter: function (cellvalue, options, data) {
                                // 0:锁定;1:解锁
                                switch (cellvalue) {
                                    case '0':
                                        cellvalue =
                                            '<input type="checkbox" name="" lay-filter="portalMotifSwitchpageset" rid="' +
                                            data.templateId +
                                            '" state="lock" checked="checked" value="0" lay-skin="switch" style="display: none !important;">';
                                        break;
                                    case '1':
                                        cellvalue =
                                            '<input type="checkbox" name="" lay-filter="portalMotifSwitchpageset" rid="' +
                                            data.templateId +
                                            '" state="lock" value="1" lay-skin="switch" style="display: none !important;">';
                                        break;
                                }
                                return cellvalue;
                            },
                        },
                        { label: '创建人', name: 'creatUser', index: 'creat_user', width: 200, editable: false },
                        { label: '最后修改时间', name: 'updated', index: 'updated', width: 140, editable: false },
                    ],
                    // 选中行
                    loadComplete: function (rowid, iCol, cellcontent, e) {
                        form.render();
                    },
                    queryFormId: 'portalMotifPageSetScreeningform',
                });

                //查询
                form.on('submit(portalMotifPageSetearch)', function (data) {
                    table.refresh();
                });
            };
            init();

            // 元素类型选择
            form.on('switch(portalMotifSwitchpageset)', function (data) {
                var val = data.value;
                var dom = data.elem;
                var type = $(dom).attr('state');
                var url = common.url + '/ts-portal/api/portal/Aftintemplate/lockAftintemplateById'; // 锁定接口
                var data = {
                    templateId: $(dom).attr('rid'),
                };
                if (val == 1) {
                    val = 0;
                } else {
                    val = 1;
                }
                $(dom).val(val);
                if (type == 'use') {
                    data.isUse = val;
                    url = common.url + '/ts-portal/api/portal/Aftintemplate/updateAftintemplateById';
                } else {
                    data.isLock = val;
                }
                $.ajax({
                    type: 'get',
                    url: url,
                    data: data,
                    // contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if (res.success) {
                            // layer.msg('操作成功！');
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {},
                });
            });

            // 类型
            function systemSelect() {
                new $.selectPlug('#portalMotifPageSetType', {
                    url: common.url + '/ts-portal/api/portal/elment/getElementTypeByName',
                    datatype: 'get', // 请求方式
                    searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: {}, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    localData: {}, // 静态数据
                    textName: 'typeName', // 选项文字的key（接口返回数据里面的参数）
                    valName: 'typeId', // 选项id的key（接口返回数据里面的参数）
                    inpTextName: 'typeName', // 需要提交的已选文本的输入框的name属性值
                    inpValName: '', // 需要提交的已选值的输入框的name属性值
                    defaultText: '', // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: '', // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res) {
                            table.oTable.setGridParam({
                                page: 1,
                                postData: {
                                    typeId: res.typeId || '',
                                },
                            });
                            table.oTable.trigger('reloadGrid');
                        }
                    },
                });
            }
            // systemSelect();

            //查询
            form.on('submit(portalMotifPageSetsearch)', function (data) {
                table.refresh();
            });

            // 新增
            $('#portalMotifPageSetAdd').funs('click', function () {
                $.quoteFun('/portalMotif/pageSet/add', {
                    mTable: table,
                    title: '新增',
                });
            });

            // 编辑
            $('#portalMotifPageSetEditor').funs('click', function () {
                var id = table.getSelectRowId();
                var data = table.getRowData(id); // 选中主题
                if (!id) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/portalMotif/pageSet/add', {
                    mTable: table,
                    title: '编辑',
                    type: 1, // 1为编辑状态
                    id: data.templateId,
                });
            });

            // 预览
            $('#portalMotifPageSetpreview').funs('click', function () {
                var id = table.getSelectRowId();
                var data = table.getRowData(id); // 选中主题
                if (!id) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/portalMotif/pageSet/preview', {
                    mTable: table,
                    title: '预览',
                    type: 1, // 1为编辑状态
                    id: data.templateId,
                });
            });

            // 删除
            $('#portalMotifPageSetDelete').funs('click', function () {
                var rowid = table.getSelectRowId();
                var data = table.getSelectRowData();
                if (!rowid) {
                    layer.msg('请选择一条需要删除的数据!');
                    return false;
                }
                layer.confirm(
                    '确定要删除当前选中的数据吗？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function () {
                        $.ajax({
                            type: 'get',
                            url: common.url + '/ts-portal/api/portal/Aftintemplate/deleteAftintemplateById',
                            data: {
                                templateId: data.templateId,
                            },
                            // contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('删除成功！');
                                    table.refresh();
                                } else {
                                    layer.msg('操作失败！');
                                }
                            },
                            error: function (res) {},
                        });
                    },
                    function () {}
                );
            });

            // 复制
            $('#portalMotifPageSetCopy').funs('click', function () {
                var rowid = table.getSelectRowId();
                var data = table.getSelectRowData();
                if (!rowid) {
                    layer.msg('请选择一行进行操作！');
                    return false;
                }
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/elment/copyElmentById',
                    data: {
                        elementid: data.elementid,
                    },
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('复制成功！');
                            table.refresh();
                        } else {
                            layer.msg('操作失败！');
                        }
                    },
                    error: function (res) {},
                });
            });

            // 页面分配
            $('#portalMotifPageSetallot').funs('click', function () {
                var id = table.getSelectRowId();
                var data = table.getRowData(id); // 选中主题
                if (!id) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var html = $('#portalMotifPageSetFormBox').html();
                layer.open({
                    type: 1,
                    title: '页面分配',
                    closeBtn: 1,
                    maxmin: false,
                    shadeClose: false,
                    shade: 0.2,
                    area: ['400px', '300px'],
                    content: html,
                    success: function (layero, index) {
                        $('#portalMotifPageSetrolID').val(data.templateId);
                        $('#portalMotifPageSetPreMenu').val(data.templateName);
                        new $.selectPlug('#portalMotifPageSetroleBox', {
                            url: common.url + '/ts-system/right/rolelist',
                            datatype: 'get', // 请求方式
                            data: {
                                sidx: 'role_code',
                                sord: 'asc',
                                rows: 15,
                            },
                            pageParamter: 'page',
                            searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                            textName: 'roleName', 
                            valName: 'roleCode', 
                            inpTextId: 'portalMotifPageSetrolename',
                            inpValId: 'portalMotifPageSetroleval', 
                            inpTextName: 'roleName', 
                            inpValName: 'roleCode', 
                            choice: true, // 是否多选
                            defaultText: data.roleName || '', // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                            defaultVal: data.roleCode || '', // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                            callback: function (res) {
                                //res 当前选中项的数据  初始化时默认位false
                                if (res) {
                                }
                            },
                        });
                    },
                });
            });

            // 保存
            form.on('submit(portalMotifPageSetSubmitjsCofirm)', function (data) {
                var d = data.field;
                $.ajax({
                    type: 'put',
                    url: common.url + '/ts-portal/api/portal/Aftintemplate/AftintemplateAddRole',
                    data: d,
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            table.refresh();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        layer.msg('操作失败！');
                    },
                });
            });

            // 菜单分配
            $(document)
                .off('click', '#portalMotifPageMenuAdd')
                .on('click', '#portalMotifPageMenuAdd', function () {
                    var id = table.getSelectRowId();
                    var data = table.getRowData(id); // 选中主题
                    if (!data) {
                        layer.msg('请先选择一行数据进行操作！');
                        return false;
                    }
                    var html = $('#portalMotifPageMenuAddBox').html();
                    layer.open({
                        type: 1,
                        title: '分配菜单',
                        closeBtn: 1,
                        maxmin: false,
                        shadeClose: false,
                        area: ['400px', '300px'],
                        skin: 'yourclass',
                        content: html,
                        success: function (layero, index) {
                            $('#portalMotifPageMenuID').val($.uuid());
                            zTreeSearch.init('#portalMotifPageMenuPreMenu', {
                                url: common.url + '/ts-system/menu/getMenu?menutype=2', // 检索地址
                                condition: 'name', // 检索参数
                                type: 'get',
                                zTreeOnClick: function (treeId, treeNode) {
                                    // 回调   treeId：树id   treeNode：点击子节点的数据
                                    $('#portalMotifPageMenuPID').val(treeNode.id);
                                    $('#portalMotifPageMenuMenulevel').val(treeNode.menulevel);
                                    $('#portalMotifPageMenuSyscode').val(treeNode.userData.syscode);
                                },
                            });
                            form.render();
                        },
                    });
                });

            // 菜单分配提交
            form.on('submit(portalMotifPageMenuSubmitCofirm)', function (data) {
                var data = data.field;
                var id = $('#portalMotifPageMenuID').val();
                var rid = table.getSelectRowId();
                var tda = table.getRowData(rid); // 选中主题
                data.alink = '/portalMotif/mod?id=' + tda.templateId + '&mid=' + $.uuid();
                data.menutype = 0;
                data.isExpand = 'Y';
                $.loadings();
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-system/sysmenu/publish',
                    contentType: 'application/json; charset=utf-8',
                    data: JSON.stringify(data),
                    success: function (res) {
                        $.closeloadings();
                        layer.closeAll();
                        if (res.success) {
                            layer.msg('操作成功');
                        } else {
                            layer.msg(res.message);
                        }
                    },
                });
            });

            //  end  ----
        });
    };
});
