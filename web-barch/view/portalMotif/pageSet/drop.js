define(function(require,exports,module) {
    exports.init = function(opt, html) {
        layui.use(['form', 'trasen'], function () {
            var form = layui.form,
                trasen = layui.trasen;

            var color = '#d81e06',
                type = 'top';
            var dragTarget, dropdom;
            var dropData = []; // 配置的首页的数据

            drag('custom__box');

            function drag(n){
                var dp = document.getElementById('portalMotifPageSetContent');

                var dom = document.getElementsByClassName(n);
                for(var i = 0;i < dom.length; i ++){
                    dom[i].addEventListener('dragstart', dragstart);
                    dom[i].addEventListener('dragleave', dragleave);
                }
                dp.addEventListener('dragover', mover);
                dp.addEventListener('drop', drop);
                var dtop = document.getElementById('portalMotifPageSetScreen');
                dtop.addEventListener('dragover', mover);
                dtop.addEventListener('drop', drop);
            }

            // 开始
            function dragstart(ev){
                dragTarget = ev.target;
            }

            // 离开元素
            function dragleave(ev){
                
            }

            // 移动
            function mover(ev){
                ev.preventDefault();
                var pid = $(dragTarget).attr('pid');
                var dom = document.getElementById('portalMotifPageSetContent');
                var screenH = $('#portalMotifPageSetScreen').height();
                var pos = getPageLocation(ev);
                var index = -1;
                for(var i = 0; i < dom.children.length; i++){
                    var p = dom.children[i].getBoundingClientRect();
                    var w = dom.children[i].offsetWidth;
                    var h = dom.children[i].offsetHeight;
                    if(pos.x > p.x && pos.x < p.x + w && pos.y > p.y && pos.y < p.y + h){ 
                        index = i;
                    }
                }
                dropdom = dom.children[index]; // 当前鼠标停留的元素
                if(pid == -2){
                    // 删格
                    gridMove();
                }else{
                    if(pid != 8 && pid != 9 && pid != -1){
                        $(dropdom).addClass('active').siblings().removeClass('active');
                        if(index == -1){
                            $('#portalMotifPageSetContent .item').removeClass('active');
                        }
                    }else{
                        var ish = $('#portalMotifPageSetScreen').attr('ish'); // 0  1
                        if(screenH == 0){
                            $('#portalMotifPageSetContent').css('top', '50px');
                            $('#portalMotifPageSetScreen').css('height', '50px');
                        }
                        var isxts = isScreenXT(ev); // 是否悬停在筛选
                    }
                }
            }

            // 放置
            var wdDragReady = [];
            function drop(ev){
                ev.preventDefault();
                var pid = $(dragTarget).attr('pid'); // 当前拖动元素的父id
                var id = $(dragTarget).attr('id'); // 当前拖动元素id
                var index = $(dropdom).index(); // 悬停元素的位置
                var nObj = null; // 当前拖动元素配置

                // 获取当前拖动元素配置
                for(var i in opt.data){
                    if(opt.data[i].pid == pid){
                        var _a = opt.data[i].children;
                        for(var j in _a){
                            if(_a[j].id == id){
                                nObj = _a[j];
                            }
                        }
                    }
                }
                if(nObj == null){
                    return false;
                }
                nObj.type = pid;

                if(pid == -2){
                    // 删格
                    gridDrop(nObj, index);
                    return false;
                }

                var arr = null;
                var elmid = nObj.id;
                if(pid == 6 || pid == 1){
                    arr = JSON.parse(nObj.content || '[]');
                }else{
                    arr = nObj.content;
                    elmid = nObj.id;
                }

                if(pid == 8 || pid == -1){
                    var isxts = isScreenXT(ev); // 是否悬停在筛选
                    var ish = $('#portalMotifPageSetScreen').attr('ish'); // 0  1
                    if(isxts == false){
                        if(ish == 0){
                            $('#portalMotifPageSetContent').css('top', '5px');
                            $('#portalMotifPageSetScreen').css('height', '0px');
                        }
                        return false;
                    }else{
                        dropdom = $('#portalMotifPageSetScreen');
                    }
                }

                var d = {
                    elmid: elmid,
                    content: arr,
                    type: pid,
                    set: {
                        color: '333333',
                        titName: '标题',
                        size: 14,
                        bgcolor: 'ffffff',
                        morecolor: '666666',
                        borderColor: '',
                        fontB: true,
                        fontI: false,
                        fontU: false
                    }
                }
                if(typeof opt.callback == 'function'){
                    opt.callback(dropdom, d, index);
                }
                $('#portalMotifPageSetScreen').removeClass('active');
                // $(dropdom).removeClass('active').addClass('ha');
            }

            // 删格放置
            function gridDrop(o, index){
                var elm = $('#portalMotifPageSetGridS');
                elm.css({
                    'left': 0,
                    'top': 0,
                    'width': 0,
                    'height': 0
                });
            }

            // 删格停留
            function gridMove(o, index){
                var l = $(dropdom).offset().left + 5;
                var t = $(dropdom).offset().top + 5;
                var w = $(dropdom).width() - 5;
                var h = $(dropdom).height() - 5;
                var elm = $('#portalMotifPageSetGridS');
                elm.css({
                    'left': l,
                    'top': t,
                    'width': w,
                    'height': h
                });
            }

            //  = 获取鼠标所在的坐标位置 = 
            var getPageLocation = function(event) {
                var e = event || window.event;
                var scrollX = document.documentElement.scrollLeft || document.body.scrollLeft;
                var scrollY = document.documentElement.scrollTop || document.body.scrollTop;
                var x = e.pageX || e.clientX + scrollX;
                var y = e.pageY || e.clientY + scrollY;
                return {
                    'x': x,
                    'y': y
                };
            }

            // 是否悬停在筛选栏
            function isScreenXT(ev){
                // portalMotifPageSetScreen
                var dom = document.getElementById('portalMotifPageSetScreen');
                var pos = getPageLocation(ev);
                var p = dom.getBoundingClientRect();
                var w = dom.offsetWidth;
                var h = 60;
                var is = false;
                if(pos.x > p.x && pos.x < p.x + w && pos.y > p.y && pos.y < p.y + h){ 
                    $('#portalMotifPageSetScreen').addClass('active');
                    is = true;
                }else{
                    $('#portalMotifPageSetScreen').removeClass('active');
                    is = false;
                }
                return is;
            }

            // ------ end
        })
    };
});