<script type="text/javascript" src="/public/plug/color/colpick.js"></script>
<script type="text/javascript" src="/script/wangEditor.min.js"></script>
<style type="text/css">
    /*portalMotifInfoBox*/
</style>
<div class="trasen-con-view-box" style="background-color: #fff; height: 100%;">
    <div class="topBox">
        <div class="row" style="padding-left: 5px;">
            <form id="portalMotifPageSetsScreeningform" class="layui-form fl" style="margin-top: 3px;">
                <div class="layui-inline fl">
                    <input type="text" name="condition" placeholder="请输入关键字" autocomplete="off" class="layui-input edi-layui-input searchInput" search-input="search" id="search-val" search-input="search">
                </div>
                <div class="pubBtnBox fl mgl10">
                    <button type="button" class="" lay-submit="" lay-filter="portalMotifPageSetsearch" search-input="button">搜索</button>
                </div>
            </form>

            <div class="pubBtnBox fr" style="margin-top: 3px;margin-right: 5px;">
                <button type="button" data-permission="on" id="portalMotifPageSetallot">页面分配</button>
                <button type="button" data-permission="on" id="portalMotifPageMenuAdd">菜单分配</button>
                <button type="button" data-permission="on" id="portalMotifPageSetAdd">新增</button>
                <button type="button" data-permission="on" id="portalMotifPageSetEditor">编辑</button>
                <!-- <button type="button" data-permission="on" id="portalMotifPageSetCopy">复制</button> -->
                <button type="button" data-permission="on" id="portalMotifPageSetDelete">删除</button>
                <button type="button" data-permission="on" id="portalMotifPageSetpreview">预览</button>
            </div>
        </div>
    </div>
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="portalMotifPageSetTable"></table>

            <!-- 分页 -->
            <div id="portalMotifPageSetTable-pager"></div>
        </div>
    </div>
</div>
<!-- 首页分配 -->
<script type="text/html" id="portalMotifPageSetFormBox">
    <form id="portalMotifPageSetForm" class="layui-form">
        <input type="hidden" name="templateId" value="" id="portalMotifPageSetrolID">
        <div class="layui-content-box">

            <div class="layui-col-xs12">
                <label class="shell-layui-form-label"><span class="required">*</span>名称</label>
                <div class="shell-layer-input-box pubSelectPesonBox" id="">
                    <input type="text" autocomplete="off" placeholder="请选择" value="" name="templateName" lay-verify="required" class="layui-input" id="portalMotifPageSetPreMenu" readonly="readonly">
                </div>
            </div>

            <div class="layui-col-xs12">
                <label class="shell-layui-form-label"><span class="required">*</span>角色</label>
                <div class="shell-layer-input-box" id="portalMotifPageSetroleBox">
                </div>
            </div>

        </div>

        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="portalMotifPageSetSubmitjsCofirm">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
        </div>
    </form>
</script>

<!-- 菜单分配 -->
<script type="text/html" id="portalMotifPageMenuAddBox">
    <form id="portalMotifPageMenuForm" class="layui-form">
        <input type="hidden" name="id" value="" id="portalMotifPageMenuID">
        <input type="hidden" name="pid" value="" id="portalMotifPageMenuPID">
        <input type="hidden" name="syscode" value="" id="portalMotifPageMenuSyscode">
        <input type="hidden" name="menulevel" value="" id="portalMotifPageMenuMenulevel">
        <input type="hidden" name="sort" value="99" autocomplete="off" class="layui-input">
        <input type="hidden" name="status" autocomplete="off" class="layui-input" value="1">
        <div class="layui-content-box">

            <div class="layui-col-xs12">
                <label class="shell-layui-form-label"><span class="required">*</span>上级菜单</label>
                <div class="shell-layer-input-box pubSelectPesonBox" id="">
                    <input type="text" autocomplete="off" data-key="#oneSelect" placeholder="请选择" value="" name="" lay-verify="required" class="layui-input" zTreeLick='click' id="portalMotifPageMenuPreMenu">
                    <i class="icon"></i>
                </div>
            </div>

            <div class="layui-col-xs12">
                <label class="shell-layui-form-label"><span class="required">*</span>菜单名称</label>
                <div class="shell-layer-input-box">
                    <input type="text" name="menuname" autocomplete="off" class="layui-input" id="" lay-verify="required">
                </div>
            </div>

            <!-- <div class="layui-col-xs12">
                <label class="shell-layui-form-label">菜单状态</label>
                <div class="shell-layer-input-box">
                    <input type="radio" name="status" autocomplete="off" class="layui-input" value="1" id="" title="启用" checked="checked">
                    <input type="radio" name="status" autocomplete="off" class="layui-input" value="0" id="" title="禁用">
                </div>
            </div> -->

        </div>

        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="portalMotifPageMenuSubmitCofirm">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
        </div>
    </form>
</script>