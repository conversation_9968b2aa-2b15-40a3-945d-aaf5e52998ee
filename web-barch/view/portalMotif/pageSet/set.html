<style type="text/css">
    .portalMotifElementDataSetTableBox{
        position: absolute;
        left: 5px;
        right: 40%;
        top: 38px;
        bottom: 5px;
    }
    .portalMotifElementDataSetTableBox .trasen-con-box{
        top: 0;
    }
    .portalMotifElementDataSetFormBox{
        position: absolute;
        left: calc(60% + 20px);
        right: 5px;
        top:108px;
        bottom: 5px;
    }
    .portalMotifElementDataSetFormBox > div{
        margin-bottom: 10px;
    }
    .portalMotifElementDataSetFormBox .shell-layui-form-label{
        width: auto;
        text-align: left;
    }
    .portalMotifElementDataSetFormBox .lab{
        position: relative;
    }
    .portalMotifElementDataSetFormBox .labin{
        padding-left: 0;
    }
</style>

<div class="row" style="margin-top: 5px;">
    <form id="portalMotifElementDataSetform" class="layui-form fl" style="margin-top: 3px; margin-left: 10px;">
        <div class="layui-inline fl">
            <input type="text" name="dsName" placeholder="请输入关键字" autocomplete="off" class="layui-input edi-layui-input searchInput" search-input="search" id="search-val" search-input="search">
        </div>
        <div class="pubBtnBox fl mgl10">
            <button type="button" class="" lay-submit="" lay-filter="portalMotifElementDataSetsearch" search-input="button">搜索</button>
        </div>
    </form>

    <div class="pubBtnBox fr" style="margin-top: 3px;margin-right: 5px;">
        <button type="button" id="portalMotifElementDataSetAdd">新增</button>
        <button type="button" id="portalMotifElementDataSetDelete">删除</button>
        <button type="button" id="portalMotifElementDataSetSave">保存</button>
    </div>
</div>
    
<div class="portalMotifElementDataSetTableBox">
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="portalMotifElementDataSetTable"></table>

            <!-- 分页 -->
            <div id="portalMotifElementDataSetTable-pager"></div>
        </div>
    </div>
</div>

<div class="portalMotifElementDataSetFormBox layui-form">
    <form id="portalMotifElementDataSetFormBox">
    
    </form>
</div>
<script type="text/html" id="portalMotifElementDataSetFormHtml">
    <input type="hidden" name="dsId" autocomplete="off" class="layui-input" id="">
    <input type="hidden" name="dsType" autocomplete="off" class="layui-input" id="">
    <div class="layui-col-xs11">
        <label class="shell-layui-form-label lab"><span class="required">*</span> 数据源名称</label>
        <div class="shell-layer-input-box labin">
            <input type="text" name="dsName" autocomplete="off" class="layui-input" id="">
        </div>
    </div>
    <!-- <div class="layui-col-xs11">
        <label class="shell-layui-form-label lab"><span class="required">*</span> 数据源类型</label>
        <div class="shell-layer-input-box labin" id="portalMotifElementDataSetFormHtmlType">
        </div>
    </div> -->
    <div class="layui-col-xs11">
        <label class="shell-layui-form-label lab"><span class="required">*</span> 链接到url或文件地址</label>
        <div class="shell-layer-input-box labin">
            <input type="text" name="colUrl" autocomplete="off" class="layui-input" id="">
        </div>
    </div>

    <div class="layui-col-xs11">
        <label class="shell-layui-form-label">是否启用</label>
        <div class="shell-layer-input-box" style="padding-left: 90px;">
            <input type="radio" name="isUse" value="1" title="是" checked="checked">
            <input type="radio" name="isUse" value="0" title="否">
        </div>
    </div>
</script>