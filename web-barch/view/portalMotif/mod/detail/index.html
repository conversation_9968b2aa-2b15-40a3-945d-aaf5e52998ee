<style type="text/css">
    .portalMotifmodDetail__box{
        width: 1000px;
        margin: 0 auto;
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        padding: 0 20px;
    }
    .portalMotifmodDetail__title{
        font-size: 20px;
        text-align: center;
        padding: 10px 0;
    }
    .portalMotifmodDetail__fj{
        font-size: 12px;
        color: #999;
        text-align: center;
        border-bottom: 1px #f1f1f1 solid;
        padding-bottom: 10px;
        margin-bottom: 10px;
    }
    .portalMotifmodDetail__con{
        font-size: 13px;
        text-align: justify;
        color: #666;
        line-height: 1.8;
    }
</style>

<div id="portalMotifmodDetail__box"></div>
<script type="text/html" id="portalMotifmodDetail__boxHtml">
    <div class="flexYbox portalMotifmodDetail__box">
        {{# if(d.error){ }}
            {{ d.text }}
        {{# }else{ }}
        <div class="portalMotifmodDetail__title">{{ d.title }}</div>
        <div class="portalMotifmodDetail__fj">发布人：{{ d.user }} {{ d.dep }}  发布时间：{{ d.date }} </div>
        <div class="flex portalMotifmodDetail__con">
            {{ d.con }}
        </div>
        {{# } }}
    </div>
</script>