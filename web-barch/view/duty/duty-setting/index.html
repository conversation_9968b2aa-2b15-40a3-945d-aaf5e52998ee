<style>
  #DutySetting {
    top: 8px;
    padding: 8px;
    background-color: #fff;
    font-family: <PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
  }

  #DutySetting #AutoDutyBtn {
    position: absolute;
    right: 212px;
    top: 8px;
  }

  #DutySetting #Submit {
    position: absolute;
    right: 144px;
    top: 8px;
  }

  #DutySetting #Review {
    position: absolute;
    right: 76px;
    top: 8px;
  }

  #DutySetting #Export {
    position: absolute;
    right: 8px;
    top: 8px;
  }

  #DutySetting .person-draggable {
    cursor: pointer;
  }

  #DutySetting .oa-search-tree {
    background: #FAFAFA;
    border-radius: 4px;
    border: 1px solid #E7EBF0;
    box-sizing: border-box;
    height: 100%;
  }

  #DutySetting .oa-search-tree .tree-top-opera {
    display: flex;
    padding: 8px;
  }

  #DutySetting #DutySettingGroupList {
    height: calc(100% - 8px);
    margin-top: 8px;
    overflow: auto;

    display: flex;
    flex-direction: column;
  }

  #DutySetting #DutySettingGroupList li {
    align-items: center;
    padding-right: 2px;
    cursor: pointer;

    font-size: 14px;
    color: #333333;
    min-height: 40px;
    display: inline-flex;
    align-items: center;
    overflow-wrap: anywhere;
    font-family: MicrosoftYaHei;
    cursor: pointer;
  }

  #DutySetting #DutySettingGroupList li.active {
    font-weight: bold;
    color: #5260FF;
  }

  #DutySetting #MonthDutyBox {
    width: 100%;
    display: flex;
    height: calc(100% - 38px);
    padding: 0 8px;
    overflow: hidden;
  }

  #DutySetting .date-tips {
    display: flex;
    align-items: center;
    margin: 0 10px;
    color: #333;
    font-size: 17px;
    font-weight: 700;
    margin-bottom: 8px;
  }

  #DutySetting .date-tips i {
    cursor: pointer;
  }

  #DutySetting .this-week-month-color {
    background-color: transparent;
    border-color: #000;
    color: #000;
  }

  #DutySetting #MonthDutyBox #PersonTable {
    width: 240px;
    overflow: hidden;
  }

  #DutySetting #MonthDutyBox #PersonTable tbody tr.active {
    background: #E2E3FD !important;
    z-index: 9999;
  }

  #DutySetting #MonthDutyBox #PersonTable tbody tr.active td:first-child {
    background: #E2E3FD !important;
  }


  #DutySetting #MonthDutyBox table {
    width: 100%;
    height: 100%;
    color: #333;
  }

  #DutySetting #MonthDutyBox table tr td,
  #DutySetting #MonthDutyBox table tr th {
    border-top: 1px solid #e6e6e6 !important;
    border-left: 1px solid #e6e6e6 !important;
    border-right: 1px solid #e6e6e6 !important;
    border-bottom: 0 !important;
    padding: 8px 0;
    text-align: center;
    overflow: hidden;
  }

  #DutySetting #MonthDutyBox table tbody {
    display: block;
    height: calc(100% - 55px);
    overflow-y: scroll;
    overflow-x: hidden;
  }

  #DutySetting #MonthDutyBox table tbody::-webkit-scrollbar {
    width: 0px;
    /*两侧滚动条宽度*/
  }

  #DutySetting #MonthDutyBox table thead {
    display: table;
    table-layout: fixed;
    width: 100%;
    height: 55px;
  }

  #DutySetting #MonthDutyBox table tbody tr {
    display: table;
    table-layout: fixed;
    width: 100%;
  }

  #DutySetting #MonthDutyBox table tbody tr:last-child {
    border-bottom: 1px solid #e6e6e6 !important;
  }

  #DutySetting #MonthTable {
    flex: 1;
    border-bottom: 1px solid #eee !important;
  }

  #DutySetting #MonthTable .areaContent {
    box-sizing: border-box;
    position: relative;
  }

  #DutySetting #MonthTable .areaContent.active {
    background: #E2E3FD !important;
    color: #333 !important;
  }

  #DutySetting #MonthTable .areaContent .container {
    width: 100%;
    height: 100%;
  }

  #DutySetting #MonthTable .areaContent .container .date {
    text-align: left;
    font-size: 18px;
  }

  #DutySetting #MonthTable .areaContent .container .person-info {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-weight: 700;
    font-size: 20px;
  }

  #DutySetting #MonthTable td,
  #DutySetting #MonthTable th {
    border-left: 1px solid transparent !important;
  }

  #DutySetting #MonthTable thead {
    height: 55px !important;
  }

  #DutySetting #MonthTable tbody tr td {
    /* height: 75px; */
  }
</style>

<!--个人群组-->
<div id="DutySetting" class="trasen-con-box table">
  <!-- 左侧树 -->
  <div class="oa-search-tree">
    <div class="shell-search-tree">
      <div class="tree-top-opera">
        <input type="text" id="DutySettingGroupSearch" class="layui-input" placeholder="请搜索" />
      </div>
    </div>
    <div class="ztree-box scrollbar-box" style="top: 35px">
      <ul id="DutySettingGroupList">
      </ul>
      <input type="hidden" id="rosterGroupId">
      <input type="hidden" id="rosterGroup">
    </div>
  </div>

  <div class="transen-con-view-box">
    <div class="date-tips month-show">
      <i class="layui-icon layui-icon-left changeDay" id="prev-month" title="切换上个月"></i>
      <span id="local-year">2020</span>年
      <span id="local-month">11</span>月
      <i class="layui-icon layui-icon-right changeDay" id="next-month" title="切换下个月"></i>

      <button class="layui-btn" id="ThisMonth" type="button">本月</button>


      <button class="layui-btn" id="AutoDutyBtn" type="button">自动值班</button>
      <button class="layui-btn" id="Submit" type="button">保存</button>
      <button class="layui-btn" id="Review" type="button">预览</button>
      <button class="layui-btn" id="Export" type="button">导出</button>
    </div>

    <div id="MonthDutyBox">
      <table id="PersonTable">
        <thead>
          <tr>
            <th width="4%">序号</th>
            <th width="12%">工号</th>
            <th width="7%">姓名</th>
          </tr>
        </thead>
        <tbody id="PersonTableTboU">
        </tbody>
      </table>
      <table id="MonthTable">
        <thead>
          <tr>
            <th>周一</th>
            <th>周二</th>
            <th>周三</th>
            <th>周四</th>
            <th>周五</th>
            <th>周六</th>
            <th>周日</th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
  </div>
</div>