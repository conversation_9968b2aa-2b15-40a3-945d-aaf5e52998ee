"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'layedit'], function () {

            var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload,
                layer = layui.layer, element = layui.element, zTreeSearch = layui.zTreeSearch,
                layedit = layui.layedit;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['580px', '500px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $('#id').val(opt.id);
                    loadwWorkflowDefinition();
                    if (opt.data) {
                        trasen.setNamesVal($('#receiveFileSeqAddForm'), opt.data);
                        $("#seqProceId").attr("disabled","disabled");
                        if (opt.data.keyValue && opt.data.keyValue == 1) {
                            $('#keyValue').prop("checked", true);
                        } else {
                            $('#keyValue').prop("checked", false);
                        }
                        if (opt.data.seqIsName && opt.data.seqIsName == 1) {
                            $('#seqIsName').prop("checked", true);
                        } else {
                            $('#seqIsName').prop("checked", false);
                        }
                        if (opt.data.seqIsYearr && opt.data.seqIsYearr == 1) {
                            $('#seqIsYearr').prop("checked", true);
                        } else {
                            $('#seqIsYearr').prop("checked", false);
                        }
                        if (opt.data.seqIsNum && opt.data.seqIsNum == 1) {
                            $('#seqIsNum').prop("checked", true);
                        } else {
                            $('#seqIsNum').prop("checked", false);
                        }
                    }
                    form.render();
                }
            });

            //跨年重拍
            form.on('switch(keyValue)', function (data) {
                var name = data.elem.name;
                var value = data.elem.value;
                var dom = data.elem;
                if (data.elem.checked) {
                    value = 1;
                    $(dom).val(value);
                } else {
                    value = 0;
                    $(dom).val(value);
                }
            })

            //流水号格式选择
            form.on('checkbox(primaryFilter)', function (data) {
                var name = data.elem.name;
                var value = data.elem.value;
                var dom = data.elem;
                if (data.elem.checked) {
                    value = 1;
                    $(dom).val(value);
                    $('#' + name).prop("checked", true);
                } else {
                    value = 0;
                    $(dom).val(value);
                    $('#' + name).prop("checked", false);
                }
                if ($('[name="seqIsName"]').is(':checked') && $('[name="seqIsYearr"]').is(':checked')) {
                    $("#seqFormatr").val("流水号名称 年度 顺序号号");
                    $("#seqModer").val("[流水号名称]〔[年度]〕[顺序号]号");
                }
                if ($('[name="seqIsName"]').is(':checked') && !$('[name="seqIsYearr"]').is(':checked')) {
                    $("#seqFormatr").val("流水号名称 顺序号号");
                    $("#seqModer").val("[流水号名称]〕[顺序号]号");
                }
                if (!$('[name="seqIsName"]').is(':checked') && $('[name="seqIsYearr"]').is(':checked')) {
                    $("#seqFormatr").val("年度 顺序号号");
                    $("#seqModer").val("〔[年度]〕[顺序号]号");
                }
                if (!$('[name="seqIsName"]').is(':checked') && !$('[name="seqIsYearr"]').is(':checked')) {
                    $("#seqFormatr").val("顺序号号");
                    $("#seqModer").val("[顺序号]号");
                }
                form.render();
            });

            //流水号类别下拉赋值
            $.ajax({
                type: "get",
                url: common.url + '/ts-document/govReceivefileseq/getSeqTypeList',
                data: {pageSize: 200, pageNo: 1},
                success: function (res) {
                    if (res) {
                        var postType = ' <option value="">请选择</option>';
                        if (opt.data) {//修改
                            $.each(res.rows, function (i, v) {
                                if (opt.data.seqTypeId == v.seqTypeId) {
                                    postType += '<option  value="' + v.seqTypeId + '" selected="selected">' + v.seqTypeName + '</option>';
                                } else {
                                    postType += '<option value="' + v.seqTypeId + '">' + v.seqTypeName + '</option>';
                                }
                            });
                        } else {//增加
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.seqTypeId + '">' + v.seqTypeName + '</option>';
                            });
                        }
                        $('#seqTypeId').html(postType);
                        form.render();
                    }
                }
            });

            //对应流程 下拉赋值
            function loadwWorkflowDefinition(){
            	$.ajax({
                    type: "get",
                    url: common.url + '/ts-workflow/workflow/definition/list?pageSize=1000',
                    async:false,
                    success: function (res) {
                        if (res.rows) {
                            var dataList = res.rows;
                            var postType = ' <option value="">请选择</option>';
                            for (var i = 0; i < dataList.length; i++) {
                                postType += '<option value="' + dataList[i].wfDefinitionId + '">' + dataList[i].workflowName + '</option>';

                            }
                            $('#seqProceId').html(postType);
                            form.render();
                        }
                    }
                });
            }
            

            //监听流水号类别 下拉选择
            form.on('select(seqTypeId)', function (data) {
                var seqTypeName = data.elem[data.elem.selectedIndex].text;
                $("#seqTypeName").val(seqTypeName);
                form.render();
            });

            //监听对应流程 下拉选择
            form.on('select(seqProceId)', function (data) {
                var seqProceName = data.elem[data.elem.selectedIndex].text;
                $("#seqProceName").val(seqProceName);
                form.render();
            });

            //使用范围
            $('#useRangeName').funs('click', function () {
                var data = {
                    isCheckDept: 'N',
                    user_str: 'useRangeName',
                    user_id: "useRangeId",
                    user_code: "useRangeId",
                    dept_code: "useOrgId"
                };
                $.quoteFun('/common/userSel', {
                    trasen: trasenTable,
                    title: '选择使用范围',
                    data: data
                });
            })

            //可维护人
            $('#canmodifyEmpName').funs('click', function () {
                var data = {
                    isCheckDept: 'N',
                    user_str: 'canmodifyEmpName',
                    user_id: "canmodifyEmpId",
                    user_code: "canmodifyEmpId",
                    dept_code: "columnManagementSelDeptId"
                };
                $.quoteFun('/common/userSel', {
                    trasen: trasenTable,
                    title: '选择可维护人',
                    data: data
                });
            })

            // 保存
            form.on('submit(docWordSubmitCofirm)', function (data) {
                var d = data.field;
                if ($('[name="seqIsName"]').is(':checked')) {
                    d.seqIsName = 1;
                } else {
                    d.seqIsName = 0;
                }
                if ($('[name="seqIsYearr"]').is(':checked')) {
                    d.seqIsYearr = 1;
                } else {
                    d.seqIsYearr = 0;
                }
                if ($('[name="seqIsNum"]').is(':checked')) {
                    d.seqIsNum = 1;
                } else {
                    d.seqIsNum = 0;
                }
                if ($('[name="keyValue"]').is(':checked')) {
                    d.keyValue = 1;
                } else {
                    d.keyValue = 0;
                }
                var url;
                if (d.id) {
                    url = '/ts-document/govReceivefileseq/update';
                } else {
                    url = '/ts-document/govReceivefileseq/save';
                }
                if (!d.id) {
                    delete d.id;
                }
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    dateType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            trasen.info(res.object);
                            opt.ref();
                        }else{
                        	layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });
            });
        })
    };
});