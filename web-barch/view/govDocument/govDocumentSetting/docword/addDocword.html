<!--新增 机关代字-->
<form id="docWordAddForm" class="layui-form">
    <input type="hidden" name="id" value="" id="id" />

    <div class="layui-content-box overflow-v">
        <div class="layui-col-xs12">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                机关代字
            </label>
            <div class="shell-layer-input-box">
                <input type="text" name="wordName" autocomplete="off" lay-verify="required" class="layui-input" id="wordName" maxlength="50" placeholder="长度不超过50字" />
            </div>
        </div>

        <div class="layui-col-xs12">
            <label class="shell-layui-form-label">文号</label>
            <div class="shell-layer-input-box">
                <select name="docnumId" id="docnumId" lay-filter="docnumId" lay-search></select>
                <input type="hidden" id="docnumName" name="docnumName" value="" />
            </div>
        </div>

        <div class="layui-col-xs12">
            <label class="shell-layui-form-label">对应模板</label>
            <div class="shell-layer-input-box">
                <select name="templateId" id="templateId" lay-filter="templateId" lay-search></select>
                <input type="hidden" id="templateName" name="templateName" value="" />
            </div>
        </div>

        <div class="layui-col-xs12">
            <label class="shell-layui-form-label">对应流程</label>
            <div class="shell-layer-input-box">
                <select name="processId" id="processId" lay-filter="processId" lay-search></select>
                <input type="hidden" id="processName" name="processName" value="" />
            </div>
        </div>

        <!--      <div class="layui-col-xs12">
            <label class="shell-layui-form-label">使用范围</label>
            <div class="shell-layer-input-box layui-form-select">
                <input type="text" autocomplete="off" name="useRangeName" class="layui-input"
                       placeholder="请选择" readonly="" id="useRangeName">
                <span style="color: red">'使用范围'为空时默认所有的人</span>
                <input type="hidden" id="useRangeId" name="useRangeId" value="">
            </div>
        </div> -->
        <!--
        <div class="layui-col-xs12">
            <label class="shell-layui-form-label">可维护人</label>
            <div class="shell-layer-input-box layui-form-select">
                <input type="text" autocomplete="off" name="canmodifyEmpName" class="layui-input"
                       placeholder="请选择" readonly="" id="canmodifyEmpName">
                <span style="color: red">'可维护人'为空时默认所有的人</span>
                <input type="hidden" id="canmodifyEmpId" name="canmodifyEmpId" value="">
            </div>
        </div> -->
    </div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="docWordSubmitCofirm">确定</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">取消</button>
    </div>
</form>
