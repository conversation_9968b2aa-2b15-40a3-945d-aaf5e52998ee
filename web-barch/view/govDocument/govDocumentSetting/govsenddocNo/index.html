<style>
  #govNo .oa-nav-search .layui-inline {
    vertical-align: top;
  }
  #govNo .oa-nav-search .layui-form-label {
    padding: 5px 10px;
  }
  #govNo .tableBox {
    width: 100%;
    flex: 1;
    position: relative;
  }
  #govNo .ui-jqgrid tr.jqgrow td {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
  .ui-state-highlight a,
  .ui-widget-content .ui-state-highlight a,
  .ui-widget-header .ui-state-highlight a
  #govNo .ui-state-hover a,
  #govNo .ui-state-hover a:link,
  #govNo .child-form-table .preview {
    color: #5260ff;
  }
  #govNo .mouseenter-item {
    width: 100%;
    height: 100%;
    display: inline-flex;
    align-items: center;
    cursor: default;
  }
</style>
<div id="govNo">
  <div class="layui-content-box" style="overflow: auto; padding: 8px">
    <div
      class="layui-tab-content"
      style="
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        padding: 0;
        display: flex;
        flex-direction: column;
      "
    >
      <div class="oa-nav-search">
        <div class="fr">
          <button type="button" class="layui-btn layui-btn-normal" id="govAddNo">新增</button>
      </div>
      </div>
      <div class="trasen-con-box">
        <div class="table-box">
          <table id="govNoTbale"></table>
          <div id="govNoPager"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="layer-btn archivesTabBtn">
    <a href="javascript:;" class="layui-btn layui-btn-primary" id="close"
      >关闭</a
    >
  </div>
</div>
