'use strict';
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(
      ['form', 'table', 'element','trasen', 'laydate'],
      function () {
        var form = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;
        var trasen = layui.trasen;
        var govNoTbale = null;
        var wins = layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          shadeClose: false,
          area: ['500px', '90%'],
          skin: 'yourclass',
          content: html,
          cancel: opt.ref,
          success: function (layero, index) {
            refreshTable();
          },
        });
        var windowsOpen = [];
        var loop = setInterval(function () {
          //监听子页面关闭事件,轮询时间1000毫秒
          for (var i = windowsOpen.length - 1; i >= 0; i--) {
            if (windowsOpen[i].closed) {
              if (windowsOpen[i].openWiner != undefined) {
                refreshTable();
              }
              var openedWindowIndex = common.openedWindow.findIndex((item) => {
                windowsOpen[i].name = item.name;
              });
              if (openedWindowIndex != -1)
                common.openedWindow.splice(openedWindowIndex, 1);
              windowsOpen.splice(i, 1);
              break;
            }
          }
        }, 1000);
        function refreshTable() {
          govNoTbale
            ? govNoTbale.refresh()
            : initTable();
        }
        //查询条件
        function getQuery() {
          var search = [];
          var opts = [];
          search = $('#govNoForm').serializeArray();
          opts = $('#govNoSearchScreenForm').serializeArray();
          var data = {};
          for (var i in search) {
            opts.push(search[i]);
          }
          for (var i in opts) {
            data[opts[i].name] = opts[i].value;
          }
          data['docnumId'] = opt.data.docnumId;
          return data;
        }
         //搜索
        $('#govNo #govNoSearch,#govNo #govNoScreenSearchScreen')
        .off('click')
        .on('click', function () {
          initTable();
        });
        // 重置
        $('#govNo #govNoTbaleResetBtn,#govNo #govNoStartReset')
          .off('click')
          .on('click', function () {
            $('#govNoForm')[0].reset();
            $('#govNoSearchScreenForm')[0].reset();
            let dateList =$('#govNo .layDate');
            for(var i=0;i<dateList.length;i++){
              $(dateList[i]).val('');
            }
            initTable();
          });
        // 表格
        function govNoFun() {
          govNoTbale = new $.trasenTable(
            'govNoTbale',
            {
              url: '/ts-oa/govSendfile/getFileNumberList',
              datatype: 'json',
              mtype: 'get',
              pager: 'govNoPager',
              postData: getQuery(),
              colModel: [
                {
                  label: '文号名称',
                  sortable: false,
                  name: 'numName',
                  width: 150,
                  align: 'center',
                },
                {
                  label: '文号值',
                  sortable: false,
                  name: 'value',
                  width: 150,
                  align: 'center',
                },
                {
                  label: '操作',
                  sortable: false,
                  name: '',
                  width: 150,
                  align: 'center',
                  formatter: function (cellvalue, options, row) {
                    var html = `<span style="color: #5260ff;cursor:pointer;" row-id="${row.id}" id="govNoEdit">编辑</span>`;
                    html += `<span style="color: #5260ff;cursor:pointer;margin-left:5px" row-id="${row.id}" id="govNoDelete">删除</span>`
                    return html;
                },
                },
              ],
			        rowList: [100, 200, 500, 1000,2000,10000],
              buidQueryParams: function () {
                return getQuery();
              },
            }
          );
        }
        //初始化表格
        function initTable() {
          if (govNoTbale) {
            govNoTbale.refresh();
          } else {
            govNoFun();
          }
        }
        // 机关代字文号情况
        $('#govAddNo').funs('click', function () {
          $.quoteFun('/govDocument/govDocumentSetting/govsenddocNo/addOrEdit', {
              trasen: govNoTbale,
              title: '新增文号',
              docnumId: opt.data.docnumId,
              ref: refreshTable,
          });
        })
        // 机关代字文号情况
        $('#govNoEdit').funs('click', function () {
          var data = govNoTbale.getSourceRowData(); // 选中行数据
          $.quoteFun('/govDocument/govDocumentSetting/govsenddocNo/addOrEdit', {
              trasen: govNoTbale,
              title: '编辑文号',
              data: data,
              docnumId: opt.data.docnumId,
              ref: refreshTable,
          });
        })
        //删除机关代字文号
        $('#govNoDelete').funs('click', function () {
            var data = govNoTbale.getSourceRowData(); // 选中行数据
            if (!data) {
                layer.msg('请先选择一行数据进行操作！');
                return false;
            }
            var d = {
              numberId: data.id,
            };
            var comfirm = layer.confirm(
                '确定要删除该文号？',
                {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                },
                function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/govSendfile/delFileNumber',
                        dateType: 'json',
                        data: d,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.close(comfirm);
                                trasen.info('操作成功');
                                refreshTable();
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                }
            );
        });
        //关闭
        $('#govNo #close')
          .off('click')
          .on('click', function () {
            opt.ref && opt.ref();
            layer.close(wins);
        });
      }
    );
  };
});
