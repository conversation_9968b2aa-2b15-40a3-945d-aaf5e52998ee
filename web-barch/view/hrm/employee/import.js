"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layer = layui.layer,
                layedit = layui.layedit;
            var index  = layer.load(1);
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['600px', '350px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                        $('#id').val(opt.data.id)
                    }
                    form.render();
                },
                cancel: function () {
                    // 右上角关闭事件的逻辑
                    opt.ref();
                    layer.closeAll();
                }
            });

            //下载导入模板
            $('#downloadImportEmployee').attr('href', common.url + '/ts-basics-bottom/employee/import/importEmployee');

            //导入
            $('#importEmployeeFileUploadBtn').click(function () {
                $('#importMessage').html('');
            })

            upload.render({
                elem: '#importEmployeeFileUploadBtn',
                url: common.url + '/ts-basics-bottom/employee/import/employeeImport'
                , accept: 'file'
                , size: 500000  //500Mb
                , multiple: true
                , auto: true
                , exts: 'xlsx|xls'
                , done: function (res) {
                    $.closeloadings();
                    var html = '';
                    var data = res.object;
                    if (res.success) {//成功
                        layer.close(index);
                        html += '<span>提示信息: </span><span>&nbsp;&nbsp;' + res.message + '</span>'
                    } else {
                        html += '<span>提示信息: </span><span style="color: red;">&nbsp;&nbsp;' + res.message + '</span>'
                    }
                    if (data) {//失败信息显示
                        layer.close(index);
                        for (var i = 0; i < data.length; i++) {
                            html += '<div>'
                            html += '<span style="color: red; font-size: 13px;">错误信息: </span><span style="color: red;font-size: 12px;">&nbsp;&nbsp;' + data[i].data + '</span>'
                            html += '</div>'
                        }
                    }
                    $('#importMessage').append(html);
                },
                error: function () {
                    layer.msg("数据表不符合要求,请下载模板填写！")
                }
            });

        })
    };
});