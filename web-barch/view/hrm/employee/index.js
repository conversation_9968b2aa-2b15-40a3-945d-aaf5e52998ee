'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;

            $('#employeeBasicListBox .oa-nav .oa-nav_item').click(function () {
                $(this).addClass('active').siblings().removeClass('active');
                $('#employeeBasicListBox .employeeBasicListBox').hide();
                $('#employeeBasicListBox .employeeBasicListBox').eq($(this).index()).show();
            });
            //调用记录
            var employeeTransferTable;
            //tab切换
            $('#employeeBasicListBox #employee').funs('click', function () {
                employeeDataTable.refresh();
                $('#employeeBasicListBox #employee_transfer').attr('style', 'display:block;'); //显示div
            });

            $('#employeeBasicListBox #transfer').funs('click', function () {
                if (!employeeTransferTable) {
                    initEmployeeTransferTable();
                } else {
                    employeeTransferTable.refresh();
                }
                $('#employeeBasicListBox #employee_transfer').attr('style', 'display:none;'); //隐藏div
            });

            //员工信息
            var employeeDataTable = new $.trasenTable('indexEmployeeTable', {
                url: common.url + '/ts-oa/employee/list',
                pager: '#indexEmployeePage',
                mtype: 'post',
                shrinkToFit: true,
                rowNum: 15,
                rownumbers: true,
                sortname: 'a.CREATE_DATE',
                sortorder: 'DESC',
                rowList: [15, 30, 50, 100],
                colModel: [
                    {
                        label: '发薪号',
                        sortable: true,
                        name: 'empPayroll',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: '登录账号',
                        sortable: true,
                        name: 'userAccounts',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: 'isHrmAdmin',
                        name: 'isHrmAdmin',
                        hidden: true,
                        align: 'center',
                    },
                    {
                        label: '姓名',
                        sortable: false,
                        name: 'empName',
                        width: 110,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            var html = "<div class='selectEmployee' style='color: #5260ff' row-id='" + rowObject.id + "'>" + cellvalue + '</div>';
                            return html;
                        },
                    },
                    {
                        label: '性别',
                        sortable: false,
                        name: 'empSex',
                        width: 60,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            //性别  0：男  1：女
                            return cellvalue == 0 ? '男' : '女';
                        },
                    },
                    {
                        label: '所属部门',
                        sortable: false,
                        name: 'empDeptName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '兼职科室',
                        sortable: false,
                        name: 'deptNameList',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '出生日期',
                        sortable: false,
                        name: 'empBirth',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '入职日期',
                        sortable: true,
                        name: 'empHiredate',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '职务',
                        sortable: false,
                        name: 'empDutyName',
                        width: 120,
                        align: 'center',
                    },
                    {
                        label: '状态',
                        sortable: false,
                        name: 'empStatus',
                        width: 80,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            //员工状态  1：正常  2：停用  3：病假  4：事假  5：离职  6：年假  7：退休  8：调休   9：婚丧假  10：产假护理假
                            var text = '';
                            if (cellvalue == '1') {
                                text = '正常';
                            } else if (cellvalue == '2') {
                                text = '<div style="color: red">停用</div>';
                            } else if (cellvalue == '3') {
                                text = '病假';
                            } else if (cellvalue == '4') {
                                text = '事假';
                            } else if (cellvalue == '5') {
                                text = '离职';
                            } else if (cellvalue == '6') {
                                text = '年假';
                            } else if (cellvalue == '7') {
                                text = '退休';
                            } else if (cellvalue == '8') {
                                text = '调休';
                            } else if (cellvalue == '9') {
                                text = '婚丧假';
                            } else if (cellvalue == '10') {
                                text = '产假护理假';
                            }
                            return text;
                        },
                    },
                    /*{label: '科室主任', sortable: false, name: 'deptDirector', width: 130, align: 'center'},*/
                    {
                        label: 'empDeptId',
                        sortable: false,
                        name: 'empDeptId',
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn " id="addPartTimeJobDept" title="添加兼职科室" row-id="' + options.rowId + '"> <i class="fa fa-user-plus deal_icon" aria-hidden="true"></i>添加兼职科室 </button>';
                            if (row.empDeptId != '90000000') {
                                btns += '<button class="layui-btn " id="delDept"  title="移出科室" row-id="' + options.rowId + '"> <i class="fa fa-user-times deal_icon" aria-hidden="true"></i>移出科室 </button>';
                            }
                            if ('true' == row.isHrmAdmin) {
                                btns += '<button class="layui-btn " id="editEvaBaseEmployee"  title="编辑" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                                if (row.empStatus == '1') {
                                    btns += '<button class="layui-btn " id="prohibitEmployee"  title="停用" row-id="' + options.rowId + '"> <i class="fa fa-minus-circle deal_icon" aria-hidden="true"></i>停用 </button>';
                                } else {
                                    btns += '<button class="layui-btn " id="enableEmployee"  title="启用" row-id="' + options.rowId + '"> <i class="fa fa-bell deal_icon" aria-hidden="true"></i>启用 </button>';
                                }
                                btns += '<button class="layui-btn " id="delEvaBaseEmployee"  title="删除" row-id="' + options.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                            }
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaBaseEmployeeForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            function initEmployeeTransferTable() {
                employeeTransferTable = new $.trasenTable('indexEmployeeTransferTable', {
                    url: common.url + '/ts-oa/employee/employeeTransfer/list',
                    pager: '#indexEmployeeTransferPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    rowNum: 13,
                    rownumbers: true,
                    sortname: 'CREATE_DATE',
                    sortorder: 'DESC',
                    rowList: [15, 30, 50, 100],
                    colModel: [
                        {
                            label: '发薪号',
                            sortable: false,
                            name: 'empPayroll',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '姓名',
                            sortable: false,
                            name: 'empName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '原科室',
                            sortable: false,
                            name: 'originPosition',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '调往科室',
                            sortable: false,
                            name: 'newPosition',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '到科时间',
                            sortable: false,
                            name: 'arrivalTime',
                            width: 170,
                            align: 'center',
                        },
                        {
                            label: '创建日期',
                            sortable: true,
                            name: 'createDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '操作人',
                            sortable: false,
                            name: 'createUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#evaBaseTrandferForm').serializeArray();
                        var opt = [];
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        return data;
                    },
                });
            }

            form.render();

            // 刷新员工信息
            function refresh() {
                var deptCode = $('#employeeBasicListBox #deptCode').val();
                var empDept = $('#employeeBasicListBox #empDept').val();
                var empName = $('#employeeBasicListBox #emp_Name').val();
                var empCode = $('#employeeBasicListBox #emp_Code').val();
                if (empDept != '' && empDept != undefined) {
                    //deptLeaderData(deptCode, empName, empCode);
                } else {
                    // deptLeaderData('', empName, empCode);
                }
                findEmployeeCount();
                employeeDataTable.refresh();
            }

            // 刷新调用记录
            function transferRefresh() {
                employeeTransferTable.refresh();
            }

            //调用记录搜索
            $('#EvaBaseSearchTransfer').funs('click', function () {
                transferRefresh();
            });

            //调用记录重置
            $('#employeeBasicListBox #TrandferReset').funs('click', function () {
                $('#employeeBasicListBox #Trandfer_NewPosition').val('');
                $('#employeeBasicListBox #Trandfer_OriginPosition').val('');
                $('#employeeBasicListBox #Trandfer_EmpName').val('');
                form.render();
                employeeTransferTable.refresh();
            });

            //员工信息重置
            $('#employeeBasicListBox #empLoyeeReset').funs('click', function () {
                $('#employeeBasicListBox #emp_Name').val('');
                $('#employeeBasicListBox #empDeptId').val('');
                $('#employeeBasicListBox #empDept').val('');
                $('#employeeBasicListBox #deptCode').val('');
                $('#employeeBasicListBox #method').val('');
                $('#employeeBasicListBox #emp_Code').val('');
                $('#employeeBasicListBox #emp_DutyId').val('');
                $('#employeeBasicListBox #emp_Sex').val('');
                $('#employeeBasicListBox #empStatus').val('');
                $('#employeeBasicListBox #userAccounts').val('');
                $('#employeeBasicListBox #empDeptCode').val('');
                $('#employeeBasicListBox #empDeptName').val('');
                $('#employeeBasicListBox #empPayroll').val('');
                $('#employeeBasicListBox #userSel').val('');

                // deptLeaderData('', '');
                findEmployeeCount();
                form.render();
                employeeDataTable.refresh();
            });

            //员工信息搜索
            $('#employeeBasicListBox #EvaBaseSearch').funs('click', function () {
                var deptCode = $('#employeeBasicListBox #deptCode').val();
                var empDept = $('#employeeBasicListBox #empDept').val();
                var empName = $('#employeeBasicListBox #emp_Name').val();
                if (empDept != '' && empDept != undefined) {
                    //deptLeaderData(deptCode, empName);
                } else {
                    // deptLeaderData('', empName);
                }
                findEmployeeCount();
                employeeDataTable.refresh();
            });

            //科室领导数据绑定
            //deptLeaderData();

            function deptLeaderData(empDeptId, empName, empCode) {
                /*  var d = {
                    empDeptCode: empDeptId,
                    empName: empName,
                    empCode: empCode,
                };
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/employee/getDepartmentLeaderShip',
                    dateType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            var deptData = res.object;
                            var html = '';
                            var html1 = '';
                            var i = 0;
                            if (deptData != null) {
                                if (res.statusCode == 0) {
                                    //显示部分
                                    html += '<div class="employeeHideUserTitle"><span>科室<br/>领导</span></div>';
                                    if (deptData.deptDirector && deptData.deptDirector.length > 5) {
                                        i++;
                                        html += '<div class="employeeHideUserDf"><span>' + deptData.deptDirector.substring(0, 5) + '</span><font>科室主任</font></div>';
                                    } else {
                                        var deptDirector = deptData.deptDirector;
                                        if (deptDirector) {
                                            i++;
                                            html += '<div class="employeeHideUserDf"><span>' + deptData.deptDirector + '</span><font>科室主任</font></div>';
                                        }
                                    }

                                    if (deptData.clerkName && deptData.clerkName.length > 5) {
                                        i++;
                                        html += '<div class="employeeHideUserDf"><span>' + deptData.clerkName.substring(0, 5) + '</span><font>科室秘书</font></div>';
                                    } else {
                                        if (deptData.clerkName != null) {
                                            i++;
                                            html += '<div class="employeeHideUserDf"><span>' + deptData.clerkName + '</span><font>科室秘书</font></div>';
                                        }
                                    }

                                    if (deptData.directLeadershipName && deptData.directLeadershipName.length > 5) {
                                        if (i < 2) {
                                            i++;
                                            html += '<div class="employeeHideUserDf"><span>' + deptData.directLeadershipName.substring(0, 5) + '</span><font>直接领导</font></div>';
                                        }
                                    } else {
                                        if (i < 2) {
                                            if (deptData.directLeadershipName != null) {
                                                i++;
                                                html += '<div class="employeeHideUserDf"><span>' + deptData.directLeadershipName + '</span><font>直接领导</font></div>';
                                            }
                                        }
                                    }
                                    if (deptData.headnurseName && deptData.headnurseName.length > 5) {
                                        if (i < 2) {
                                            i++;
                                            html += '<div class="employeeHideUserDf"><span>' + deptData.headnurseName.substring(0, 5) + '</span><font>护士长</font></div>';
                                        }
                                    } else {
                                        if (i < 2) {
                                            if (deptData.headnurseName != null) {
                                                i++;
                                                html += '<div class="employeeHideUserDf"><span>' + deptData.headnurseName + '</span><font>护士长</font></div>';
                                            }
                                        }
                                    }

                                    //隐藏部分
                                    html1 += '<h4>科室领导</h4>';
                                    if (deptData.deptDirector) {
                                        html1 += '<p>' + '科室主任( ' + deptData.deptDirector + ' )' + '</p>';
                                    }
                                    if (deptData.clerkName) {
                                        html1 += '<p>' + '科室秘书( ' + deptData.clerkName + ' )' + '</p>';
                                    }
                                    if (deptData.departmentHeadName) {
                                        html1 += '<p>' + '部门领导( ' + deptData.departmentHeadName + ' )' + '</p>';
                                    }
                                    if (deptData.directLeadershipName) {
                                        html1 += '<p>' + '直接领导( ' + deptData.directLeadershipName + ' )' + '</p>';
                                    }
                                    if (deptData.managementLeadName) {
                                        html1 += '<p>' + '分管领导( ' + deptData.managementLeadName + ' )' + '</p>';
                                    }
                                    if (deptData.departmentPhorName) {
                                        html1 += '<p>' + '部门长( ' + deptData.departmentPhorName + ' )' + '</p>';
                                    }
                                    if (deptData.headnurseName) {
                                        html1 += '<p>' + '护士长( ' + deptData.headnurseName + ' )' + '</p>';
                                    }
                                    if (deptData.medicalDirectorName) {
                                        html1 += '<p>' + '医疗主任( ' + deptData.medicalDirectorName + ' )' + '</p>';
                                    }
                                }
                            }
                            $('#employeeDeptListTitle').html(html);
                            $('#employeeDeptListBox').html(html1);
                        }
                        else {
                                                   layer.msg("没有科室领导数据");
                                               }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });*/
            }

            //查询员工总数
            findEmployeeCount();

            function findEmployeeCount() {
                var d = {
                    empDeptCode: $('#employeeBasicListBox #empDeptCode').val(),
                    empName: $('#employeeBasicListBox #emp_Name').val(),
                    empCode: $('#employeeBasicListBox #emp_Code').val(),
                    empDutyId: $('#employeeBasicListBox #emp_DutyId').val(),
                    empSex: $('#employeeBasicListBox #emp_Sex').val(),
                    empStatus: $('#employeeBasicListBox #empStatus').val(),
                    method: $('#employeeBasicListBox #method').val(),
                };
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/employee/employeeStatistics',
                    dateType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            $('#employeeCount').text(res.object + '人');
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            }

            //查看详情
            $('body')
                .off('click', '#employeeBasicListBox .selectEmployee')
                .on('click', '#employeeBasicListBox .selectEmployee', function () {
                    var data = employeeDataTable.getSourceRowData();
                    $.quoteFun('/hrm/employee/selectEmployee', {
                        trasen: employeeDataTable,
                        title: '查看员工信息',
                        data: data,
                    });
                });

            // 新增员工信息
            $('#employeeBasicListBox #addEvaBase').funs('click', function () {
                $.quoteFun('/hrm/employee/add', {
                    trasen: employeeDataTable,
                    title: '新增员工信息',
                    ref: refresh,
                });
            });

            //人员筛选
            $('#employeeBasicListBox #empLoyeeScreen').funs('click', function () {
                $('#employeeBasicListBox #emp_Name').val('');
                $('#employeeBasicListBox #empDeptId').val('');
                $('#employeeBasicListBox #empDept').val('');
                $('#employeeBasicListBox #empDeptCode').val('');
                $('#employeeBasicListBox #method').val('');
                $('#employeeBasicListBox #emp_Code').val('');
                $('#employeeBasicListBox #emp_DutyId').val('');
                $('#employeeBasicListBox #emp_Sex').val('');
                $('#employeeBasicListBox #empStatus').val('');
                $.quoteFun('/hrm/employee/screen', {
                    trasen: employeeDataTable,
                    title: '人员筛选',
                    ref: refresh,
                });
            });

            // 修改员工信息
            $('#editEvaBaseEmployee').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/hrm/employee/add', {
                    trasen: employeeDataTable,
                    title: '编辑员工信息',
                    data: data,
                    none: true,
                    ref: refresh,
                });
            });

            //科室领导鼠标移入移出
            $('#employeeBasicListBox .employeeHideUserList').hover(function () {
                $('.employeeHideBox').toggle();
            });

            //导出
            $('#expotEmployee').funs('click', function () {
                layer.confirm(
                    '您确定导出员工信息？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        var empName = $('#employeeBasicListBox #emp_Name').val();
                        var empCode = $('#employeeBasicListBox #emp_Code').val();
                        var empDutyId = $('#employeeBasicListBox #emp_DutyId').val();
                        var empSex = $('#employeeBasicListBox #emp_Sex').val();
                        var empStatus = $('#employeeBasicListBox #empStatus').val();
                        var deptName = $('#employeeBasicListBox #empDept').val(); //部门名称
                        var deptCode = $('#employeeBasicListBox #empDeptCode').val(); //部门Code
                        var empHiredate = $('#employeeBasicListBox #method').val();
                        var empDeptCode = '';

                        if (deptCode != undefined && deptCode != '') {
                            empDeptCode = deptCode;
                        }
                        var employeeName = '';
                        if (empName != undefined && empName != '') {
                            employeeName = empName;
                        }
                        window.location.href = common.url + '/ts-basics-bottom/employee/excel/expotEmployee?empName=' + employeeName + '&empDeptCode=' + empDeptCode + '&empCode=' + empCode + '&empDutyId=' + empDutyId + '&empSex=' + empSex + '&empHiredate=' + empHiredate + '&empStatus=' + empStatus;
                        layer.closeAll();
                        layer.close(index);
                    }
                );
            });

            //导入
            $('#importEmployee').funs('click', function () {
                //window.open(common.url + '/ts-basics-bottom/employee/import/importEmployee');
                $.quoteFun('/hrm/employee/import', {
                    trasen: employeeDataTable,
                    title: '员工基本信息导入',
                    ref: refresh,
                });
            });

            //添加兼职科室
            $('#employeeBasicListBox #addPartTimeJobDept').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/hrm/employee/addPartTimeJobDept', {
                    trasen: employeeDataTable,
                    title: '设置兼职科室',
                    data: data,
                    ref: refresh,
                });
            });

            //移出科室
            $('#employeeBasicListBox #delDept').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                    empDeptName: data.empDeptName,
                };
                layer.confirm(
                    '您确定将' + data.empName + ',从' + data.empDeptName + '科室移出？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/delDept',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg(res.object);
                                    layer.close(index);
                                    refresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 停用员工信息
            $('#prohibitEmployee').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                    isDeleted: 'Y',
                };
                layer.confirm(
                    '确定停用?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/disableEnableEmployee',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('操作成功');
                                    layer.close(index);
                                    refresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 启用员工信息
            $('#enableEmployee').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                    isDeleted: 'N',
                };
                layer.confirm(
                    '确定启用?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/disableEnableEmployee',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('操作成功');
                                    layer.close(index);
                                    refresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 删除员工信息
            $('#delEvaBaseEmployee').funs('click', function () {
                var data = employeeDataTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定删除?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/deletedById/{empId}',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('操作成功');
                                    refresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            //移入科室
            $('#employeeBasicListBox #addDept').funs('click', function () {
                $.quoteFun('/hrm/employee/moveIn', {
                    trasen: employeeDataTable,
                    title: '移入科室',
                    ref: refresh,
                });
            });

            //部门下拉框赋值
            /*    function treeSelect() {
                    zTreeSearch.init('#empDept', {
                        url: common.url + '/ts-system/dept/getDept',
                        type: 'get',
                        checkbox: false,
                        condition: 'name',
                        zTreeOnClick: function (treeId, treeNode) {
                            $('#deptCode').val(treeNode.id);
                        }
                    });
                }*/

            // treeSelect();

            //同步用户信息
            $('#syncUserInformation').funs('click', function () {
                layer.confirm(
                    '确定同步用户信息?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.loadings();
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/syncUserInformation',
                            timeout: 1000000,
                            /*dateType: "json",
                        contentType: 'application/json',
                        data: JSON.stringify(d),*/
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('同步成功');
                                    refresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message || '同步失败');
                                }
                            },
                            error: function (res) {
                                layer.closeAll();
                                refresh();
                                //res = JSON.parse(res.responseText);
                                //layer.msg(res.message);
                            },
                        });
                    }
                );
            });
        });
    };
});
