<style type="text/css" scoped>
    .employeeBasicListBox {
        display: none;
    }

    .employeeHideUserList {
        float: right;
        width: 210px;
        margin-top: 20px;
        position: relative;
        cursor: pointer;
    }

    .employeeHideUserList .employeeHideUserTitle {
        width: 35px;
        margin-right: 5px;
        float: left;
        color: #5260ff;
        border-right: 1px solid #006eff;
    }

    .employeeHideUserList .employeeHideUserDf {
        width: 80px;
        float: left;
    }

    .employeeHideUserList .employeeHideUserDf span {
        display: block;
        color: #5260ff;
    }

    .employeeHideUserList .employeeHideBox {
        display: none;
        position: absolute;
        z-index: 99999;
        top: 100%;
        background: #fff;
        width: 100%;
        border: 1px solid #ccc;
        padding: 5px;
        right: 0;
        line-height: 24px;
    }

    .employeeHideUserList .employeeHideBox h4 {
        color: #5260ff;
    }

    .employeeHideUserList .employeeHideBox p {
        border-top: 1px dashed #ccc;
        color: #666;
    }

    .empLoyeeBtnBox button {
        border: 0;
        background: none;
        width: 70px;
        margin-left: 15px;
    }

    .empLoyeeBtnBox {
        float: left;
    }

    .empLoyeeBtnBox button i {
        display: block;
        margin: 5px auto;
        background: #f59a23;
        color: #fff;
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 30px;
        border-radius: 35px;
    }

    .empLoyeeBtnBox button .greenBg {
        background: #70b603 !important;
    }

    .empLoyeeBtnBox button .blueBg {
        background: #8080ff !important;
    }

    .empLoyeeBtnBox button .blueBg2 {
        background: #5260ff !important;
    }

    .empLoyeeNumBox {
        float: left;
        margin-left: 15px;
        height: 60px;
        line-height: 30px;
        background: #f5f5f5;
        border: 1px dashed #ccc;
        border-radius: 5px;
        padding: 0 10px;
    }

    .empLoyeeNumBox i {
        font-size: 36px;
        overflow: hidden;
        color: #5260ff;
        display: block;
        position: relative;
        top: 5px;
        text-align: center;
    }

    .empLoyeeNumBox b {
        color: #5260ff;
        font-size: 16px;
    }
</style>
<!-- 主表格内容 -->
<div class="content-box" id="employeeBasicListBox">
    <div class="oa-nav">
        <a href="javascript:;" class="oa-nav_item active" id="employee">员工信息</a>
        <a href="javascript:;" class="oa-nav_item" id="transfer">调动记录</a>
        <div class="fr" id="employee_transfer">
            <button type="button" class="layui-btn layui-btn-normal" data-permission="on" id="syncUserInformation">同步用户</button>
            <button type="button" class="layui-btn layui-btn-normal" id="expotEmployee" style="font-size: 14px">
                <i class="fa fa-sign-out"></i>
                导出
            </button>
            <button type="button" class="layui-btn layui-btn-normal" id="importEmployee" style="font-size: 14px">
                <i class="fa fa-sign-in"></i>
                导入
            </button>
        </div>
    </div>
    <div class="trasen-con-box employeeBasicListBox" style="display: block; top: 45px">
        <div class="topBox">
            <div class="row">
                <form id="evaBaseEmployeeForm" class="layui-form">
                    <input id="emp_Code" name="empCode" type="hidden" />
                    <input id="emp_DutyId" name="empDutyId" type="hidden" />
                    <input id="method" name="method" type="hidden" />
                    <input id="emp_Sex" name="empSex" type="hidden" />
                    <input id="empStatus" name="empStatus" type="hidden" />
                    <input type="hidden" id="empDeptCode" name="empDeptCode" />
                    <input type="hidden" id="empDeptName" name="empDeptName" />
                    <input type="hidden" id="userAccounts" name="userAccounts" />
                    <input type="hidden" id="empPayroll" name="empPayroll" />
                    <input type="hidden" id="userSel" name="userSel" />

                    <div class="shell-search-box" style="display: none">
                        <span class="shell-layer-input-boxTit">姓名</span>
                        <div class="shell-layer-input-box">
                            <input type="text" name="empName" class="layui-input" id="emp_Name" placeholder="" />
                        </div>
                    </div>

                    <!--  <div class="shell-search-box" style="display:none;">
                        <span class="shell-layer-input-boxTit">部门</span>
                        <div class="shell-layer-input-box">
                            <input type="text" autocomplete="off" name="empDeptName" class="layui-input" placeholder="请选择"
                                lay-verify="required" id="empDept" zTreeLick="click">
                            <i class="layui-edge"></i>
                            <input type="hidden" id="deptCode" name="empDeptCode">
                        </div>
                    </div> -->

                    <div class="empLoyeeBtnBox">
                        <div class="empLoyeeNumBox">
                            <i class="icon iconfont icon_yuangongshu"></i>
                            <b id="employeeCount"></b>
                        </div>
                        <button type="button" data-permission="on" id="addEvaBase">
                            <i class="icon iconfont icon_tianjiaxinzeng"></i>
                            人员新增
                        </button>
                        <button type="button" data-permission="on" id="addDept">
                            <i class="icon iconfont icon_zhuanfa greenBg"></i>
                            移入科室
                        </button>
                        <button type="button" class="" id="empLoyeeScreen">
                            <i class="icon iconfont icon_sousuo blueBg"></i>
                            人员筛选
                        </button>

                        <button type="button" class="" id="empLoyeeReset">
                            <i class="icon iconfont icon_canshushezhi greenBg"></i>
                            重置刷新
                        </button>
                    </div>
                    <div class="employeeHideUserList">
                        <div id="employeeDeptListTitle"></div>

                        <div class="employeeHideBox" id="employeeDeptListBox"></div>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-box" style="top: 70px !important">
            <table id="indexEmployeeTable"></table>
            <!-- 分页 -->
            <div id="indexEmployeePage"></div>
        </div>
    </div>

    <!--调用记录-->
    <div class="trasen-con-box employeeBasicListBox">
        <div class="oa-nav-search">
            <form id="evaBaseTrandferForm" class="layui-form">
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">姓名</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="empName" class="layui-input" id="Trandfer_EmpName" placeholder="" search-input="hrm-employee" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">原科室</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="originPosition" class="layui-input" id="Trandfer_OriginPosition" search-input="hrm-employee" placeholder="" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">调往科室</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="newPosition" class="layui-input" id="Trandfer_NewPosition" search-input="hrm-employee" placeholder="" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <button type="button" class="layui-btn" id="EvaBaseSearchTransfer" search-btn="hrm-employee">搜索</button>
                    <button type="button" class="layui-btn oa-btn-reset" id="TrandferReset"><i class="layui-icon layui-icon-refresh"></i></button>
                </div>
            </form>
        </div>

        <div class="table-box" style="top: 41px">
            <table id="indexEmployeeTransferTable"></table>
            <!-- 分页 -->
            <div id="indexEmployeeTransferPage"></div>
        </div>
    </div>
</div>
