<style></style>
<form id="evaBaseAddForm" class="layui-form">
    <input type="hidden" name="id" value="" id="id" />
    <div class="layui-content-box overflow-v">
        <div class="layui-col-xs11">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                职务编号
            </label>
            <div class="shell-layer-input-box">
                <input type="text" name="dutyCode" autocomplete="off" lay-verify="required" class="layui-input" id="dutyCode" />
            </div>
        </div>

        <div class="layui-col-xs11">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                职务名称
            </label>
            <div class="shell-layer-input-box">
                <input type="text" name="dutyName" autocomplete="off" lay-verify="required" class="layui-input" id="dutyName" />
            </div>
        </div>

        <!--  <div class="layui-col-xs11">
            <label class="shell-layui-form-label">上级职务</label>
            <div class="shell-layer-input-box">
                <select name="pid" id="pid" lay-filter="pid">

                </select>
            </div>
        </div> -->

        <div class="layui-col-xs11">
            <label class="shell-layui-form-label">描述</label>
            <div class="shell-layer-input-box">
                <textarea class="layui-textarea" id="remark" name="remark" value=""></textarea>
            </div>
        </div>
    </div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="evaBaseSubmitCofirm">保存</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
    </div>
</form>
