"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {

            var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['400px', '300px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $('#id').val(opt.id);
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                    }
                    form.render();
                }
            });

            // 保存
            form.on('submit(evaBaseSubmitCofirm)', function (data) {
                var d = data.field;
                var url;
                if (d.id) {
                    url = '/ts-oa/employee/employeeType/update';
                } else {
                    url = '/ts-oa/employee/employeeType/save';
                }
                if (!d.id) {
                    delete d.id;
                }
                $.loadings();
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    dateType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        } else {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });
            });

        })

    };
});