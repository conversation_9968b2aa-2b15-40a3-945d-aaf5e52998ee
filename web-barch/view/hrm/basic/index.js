'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            //获取休假类型
            function getDictHOLIDAY_TYPE() {
                $.ajax({
                    url: '/ts-basics-bottom/dictItem/getDictItemByTypeCode',
                    method: 'get',
                    data: {
                        typeCode: 'HOLIDAY_TYPE',
                    },
                    success: function (res) {
                        if (res.success) {
                            var html = '';
                            var list = res.object || [];
                            for (var i = 0; i < list.length; i++) {
                                html += '<option value="' + list[i].itemName + '">' + list[i].itemName + '</option>';
                            }
                            $('#hrmBasicListBox [name="interest"]').html(html);
                            form.render('select');
                        }
                    },
                });
            }
            getDictHOLIDAY_TYPE();
            //tab切换
            $('#hrmBasicListBox .oa-nav .oa-nav_item')
                .off('click')
                .on('click', function () {
                    $(this).addClass('active').siblings().removeClass('active');
                    $('#hrmBasicListBox .hrmBasicListBox').hide();
                    $('#hrmBasicListBox .hrmBasicListBox').eq($(this).index()).show();
                    if ($(this).index() == 0) {
                        employeeTypeTable.refresh();
                    } else if ($(this).index() == 1) {
                        if (!dutyTable) {
                            getdutyTable();
                        } else {
                            dutyTable.refresh();
                        }
                    } else if ($(this).index() == 2) {
                        if (!TitleManagementTable) {
                            getTitleManagementTable();
                        } else {
                            TitleManagementTable.refresh();
                        }
                    } else if ($(this).index() == 3) {
                    }
                });

            //员工类型
            var employeeTypeTable = new $.trasenTable('indexEmployeeTypeTable', {
                url: common.url + '/ts-oa/employee/employeeType/list',
                pager: '#indexEmployeeTypePage',
                mtype: 'post',
                shrinkToFit: true,
                autowidth: true,
                width: '100%',
                sortname: 'SORT',
                sortorder: 'ASC',
                colModel: [
                    {
                        label: '员工类型',
                        name: 'userType',
                        align: 'center',
                        index: 'USER_TYPE',
                        width: 200,
                    },
                    {
                        label: '排序',
                        name: 'sort',
                        align: 'center',
                        index: 'SORT',
                    },
                    {
                        label: '创建人',
                        name: 'createUserName',
                        align: 'center',
                        width: 200,
                    },
                    {
                        label: '创建日期',
                        sortable: true,
                        name: 'createDate',
                        align: 'center',
                        width: 200,
                    },
                    /*  {
                        label: '更新人员',
                        sortable: false,
                        name: 'updateUserName',
                        width: 120,
                        align: "center"
                    },
                    {
                        label: '更新日期',
                        sortable: false,
                        name: 'updateDate',
                        width: 130,
                        align: "center"
                    },*/
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        editable: false,
                        title: false,
                        width: 50,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn " id="editEvaBase"  row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn " id="delEvaBase" row-id="' + options.rowId + '">  <i class="fa fa-trash deal_icon"  aria-hidden="true"></i> 删除 </button>';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#userType_EvaBaseForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
                gridComplete: function () {
                    $('.hrmBasicListBox').hide();
                    $('.hrmBasicListBox').eq(0).show();
                },
                //queryFormId: 'resourcesModForm'
            });

            //职务管理
            var dutyTable = null;

            function getdutyTable() {
                dutyTable = new $.trasenTable('indexDutyTable', {
                    url: common.url + '/ts-oa/employee/duty/list',
                    pager: '#indexDutyPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    sortname: 'DUTY_CODE',
                    sortorder: 'ASC',
                    colModel: [
                        {
                            label: '职务编号',
                            sortable: false,
                            name: 'dutyCode',
                            index: 'DUTY_CODE',
                            width: 100,
                            align: 'center',
                        },
                        {
                            label: '职务名称',
                            sortable: false,
                            name: 'dutyName',
                            width: 100,
                            align: 'center',
                        },
                        /*{
                            label: '上级职务',
                            sortable: false,
                            name: 'pid',
                            width: 130,
                            align: "center"
                        },*/
                        {
                            label: '描述',
                            sortable: false,
                            name: 'remark',
                            width: 170,
                            align: 'center',
                        },
                        {
                            label: '创建人',
                            sortable: false,
                            name: 'createUserName',
                            width: 130,
                            align: 'center',
                        },
                        {
                            label: '创建日期',
                            sortable: true,
                            name: 'createDate',
                            width: 170,
                            align: 'center',
                        },
                        /*{
                            label: '更新人员',
                            sortable: false,
                            name: 'updateUserName',
                            width: 120,
                            align: "center"
                        },
                        {
                            label: '更新日期',
                            sortable: false,
                            name: 'updateDate',
                            width: 170,
                            align: "center"
                        },*/
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cell, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn " id="editEvaBaseDuty"  row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i> 编辑 </button>';
                                btns += '<button class="layui-btn " id="delEvaBaseDuty" row-id="' + options.rowId + '">  <i class="fa fa-trash deal_icon"  aria-hidden="true"></i> 删除 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#evaBaseDutyForm').serializeArray();
                        var opt = [];
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        return data;
                    },
                    //queryFormId: 'resourcesModForm'
                });
            }

            //职称管理
            var TitleManagementTable = null;

            function getTitleManagementTable() {
                TitleManagementTable = new $.trasenTable('indexTitleManagementTable', {
                    url: common.url + '/ts-oa/employee/title/list',
                    pager: '#indexTitleManagementPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    sortname: 'CREATE_DATE',
                    sortorder: 'DESC',
                    colModel: [
                        {
                            label: '职称编号',
                            sortable: false,
                            name: 'titleCode',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '职称名称',
                            sortable: false,
                            name: 'titleName',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '描述',
                            sortable: false,
                            name: 'remark',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '创建人',
                            sortable: false,
                            name: 'createUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '创建日期',
                            sortable: true,
                            name: 'createDate',
                            width: 150,
                            align: 'center',
                        },
                        /* {
                            label: '更新人员',
                            sortable: false,
                            name: 'updateUserName',
                            width: 120,
                            align: "center"
                        },
                        {
                            label: '更新日期',
                            sortable: false,
                            name: 'updateDate',
                            width: 150,
                            align: "center"
                        },*/

                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cell, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn " id="editEvaBaseTitleManagement"  row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i> 编辑 </button>';
                                btns += '<button class="layui-btn " id="delEvaBaseTitleManagement" row-id="' + options.rowId + '">  <i class="fa fa-trash deal_icon"  aria-hidden="true"></i> 删除 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#TitleManagementForm').serializeArray();
                        var opt = [];
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        return data;
                    },
                    //queryFormId: 'resourcesModForm'
                });
            }

            form.render();

            //关闭回车提交
            $('#userType').on('keydown', function (event) {
                if (event.keyCode == 13) {
                    return false;
                }
            });

            //员工类型刷新
            function typeRefresh() {
                employeeTypeTable.refresh();
            }

            //员工职务刷新
            function dutyRefresh() {
                dutyTable.refresh();
            }

            //员工职称刷新
            function titleManagementRefresh() {
                TitleManagementTable.refresh();
            }

            //员工职称搜索
            $('#EvaBaseSearchTitleManagement').funs('click', function () {
                titleManagementRefresh();
            });

            //员工职称 重置
            $('#TitleManagementReset').funs('click', function () {
                $('#titleName').val('');
                $('#titleCode').val('');
                TitleManagementTable.refresh();
            });

            //员工类型搜索
            $('#EvaBaseSearch').funs('click', function () {
                typeRefresh();
            });

            //重置
            $('#typeReset').funs('click', function () {
                $('#userType').val('');
                employeeTypeTable.refresh();
            });

            $('#dutyReset').funs('click', function () {
                $('#dutyName').val('');
                $('#dutyCode').val('');
                dutyTable.refresh();
            });

            //员工职务搜索
            $('#EvaBaseSearchDuty').funs('click', function () {
                dutyRefresh();
            });

            // 员工职称新增
            $('#addEvaBaseTitleManagement').funs('click', function () {
                $.quoteFun('/hrm/basic/addTitle', {
                    trasen: TitleManagementTable,
                    title: '新增员工职称',
                    ref: titleManagementRefresh,
                });
            });

            // 员工职称修改
            $('#editEvaBaseTitleManagement').funs('click', function () {
                var data = TitleManagementTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/hrm/basic/addTitle', {
                    trasen: TitleManagementTable,
                    title: '编辑员工职称',
                    data: data,
                    ref: titleManagementRefresh,
                });
            });

            // 员工职称 删除
            $('#delEvaBaseTitleManagement').funs('click', function () {
                var data = TitleManagementTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定删除[' + data.titleName + ']该数据?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/title/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    titleManagementRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 员工类型新增
            $('#addEmpTypeEvaBase').funs('click', function () {
                $.quoteFun('/hrm/basic/add', {
                    trasen: employeeTypeTable,
                    title: '新增员工类型',
                    ref: typeRefresh,
                });
            });

            // 员工职务新增
            $('#addEvaBaseDuty').funs('click', function () {
                $.quoteFun('/hrm/basic/dutyAdd', {
                    trasen: dutyTable,
                    title: '新增员工职务',
                    ref: dutyRefresh,
                });
            });

            // 员工类型修改
            $('#editEvaBase').funs('click', function () {
                var data = employeeTypeTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/hrm/basic/add', {
                    trasen: employeeTypeTable,
                    title: '编辑员工类型',
                    data: data,
                    ref: typeRefresh,
                });
            });

            // 员工职务修改
            $('#editEvaBaseDuty').funs('click', function () {
                var data = dutyTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/hrm/basic/dutyAdd', {
                    trasen: dutyTable,
                    title: '编辑员工职务',
                    data: data,
                    ref: dutyRefresh,
                });
            });

            // 员工类型删除
            $('#delEvaBase').funs('click', function () {
                var data = employeeTypeTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定删除[' + data.userType + ']该数据?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/employeeType/deletedById/{employeeTypeId}',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('操作成功');
                                    typeRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 员工职务删除
            $('#delEvaBaseDuty').funs('click', function () {
                var data = dutyTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定删除[' + data.dutyName + ']该数据?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-oa/employee/duty/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('操作成功');
                                    dutyRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            //年假设置
            /*
            {
                typeName: '',
                workStart: '',
                workEnd: '',
                holidays: '',
            },
            */
            getSetting();
            var yearHolidaysRule = [];
            var selIndex = null;
            var isupdate = null;
            function getSetting() {
                $.ajax({
                    method: 'get',
                    url: '/ts-oa/leaveRules/list',
                    success: function (res) {
                        if (res.success) {
                            yearHolidaysRule = res.object || [];
                            if (yearHolidaysRule[0]) {
                                isupdate = true;
                                $('#hrmBasicListBox [name="interest"]').val(yearHolidaysRule[0].typeName);
                            }
                            initRule();
                        }
                    },
                });
            }
            function initRule() {
                var html = '';
                for (var i = 0; i < yearHolidaysRule.length; i++) {
                    html +=
                        '<li index="' +
                        i +
                        '">规则' +
                        (i + 1) +
                        '、工龄范围<span class="required">大于等于' +
                        yearHolidaysRule[i].workStart +
                        '</span>且 <span class="required">小于' +
                        yearHolidaysRule[i].workEnd +
                        '</span>年，年假<span class="required">' +
                        yearHolidaysRule[i].holidays +
                        '</span>天；' +
                        '<span class="oper">' +
                        '<i class="fa fa-pencil-square-o edit" aria-hidden="true" ></i>' +
                        '<i class="fa fa-trash-o del" aria-hidden="true" ></i>' +
                        '</span></li>';
                }
                $('#hrmBasicListBox .ruleList').html(html);
            }

            form.on('submit(yearHolidaysRuleSet)', function (data) {
                for (var i = 0; i < yearHolidaysRule.length; i++) {
                    yearHolidaysRule[i].typeName = $('#hrmBasicListBox [name="interest"]').val();
                }
                var url = '/ts-oa/leaveRules/save';
                /*    if (isupdate) {
                    url = '/ts-oa/leaveRules/update';
                }*/
                $.ajax({
                    url: url,
                    method: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify(yearHolidaysRule),
                    success: function (res) {
                        if (res.success) {
                            isupdate = true;
                            layer.msg(res.message || '成功');
                        }
                    },
                });
                return false;
            });
            //
            $('#hrmBasicListBox  #createRule')
                .off('click')
                .on('click', function () {
                    selIndex = null;
                    $.quoteFun('/hrm/basic/rule', {
                        callback: function (data) {
                            yearHolidaysRule.push(data);
                            initRule();
                        },
                    });
                    return false;
                });
            //
            $('#hrmBasicListBox  .ruleList')
                .off('click', '.edit')
                .on('click', '.edit', function () {
                    var index = $(this).closest('li').attr('index');
                    selIndex = index;
                    $.quoteFun('/hrm/basic/rule', {
                        data: yearHolidaysRule[index],
                        callback: function (data) {
                            yearHolidaysRule[selIndex] = data;
                            initRule();
                        },
                    });
                    return false;
                });
            //
            $('#hrmBasicListBox  .ruleList')
                .off('click', '.del')
                .on('click', '.del', function () {
                    var index = $(this).closest('li').attr('index');
                    yearHolidaysRule.splice(index, 1);
                    initRule();
                    return false;
                });
        });
    };
});
