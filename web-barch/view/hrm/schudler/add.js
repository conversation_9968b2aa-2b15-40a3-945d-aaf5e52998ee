'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layedit = layui.layedit,
                zTreeSearch = layui.zTreeSearch;

            function getStartTime() {
                var arr = [0, 15, 30, 45];
                var d = new Date();
                var m = d.Format('m');
                var h = d.Format('hh');
                var mm;
                if (m > 45) {
                    h++;
                    mm = '00';
                } else {
                    for (var i = 0; i < arr.length; i++) {
                        if (arr[i] == m) {
                            mm = arr[i];
                            break;
                        }
                        if (arr[i] > m) {
                            mm = arr[i];
                            break;
                        }
                    }
                }
                return h + ':' + mm;
            }
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['600px', '620px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data.id != null && opt.data.id != '') {
                        $('#scheduleId').val(opt.data.id);
                        loadScheduleDetails(opt.data.id);
                        $('#repeat').addClass('none');
                    } else {
                        $('#scheduleStartTime').val(getDefTime().start);
                        $('#scheduleEndTime').val(getDefTime().end);
                        $('#repeat').removeClass('none');
                        timeSel();
                    }
                    form.render();
                },
            });

            function timeSel() {
                TimePicker({
                    el: '#scheduleStartTime',
                    start: '00:00',
                    done: function (value) {
                        var hours = value.split(':')[0];
                        var time = '' + (hours * 1 + 1) + ':00';
                        if (hours == 23) {
                            time = value;
                        }
                        $('#scheduleEndTime').val(time);
                        TimePicker({
                            el: '#scheduleEndTime',
                            start: value,
                        });
                    },
                });
                TimePicker({
                    el: '#scheduleEndTime',
                    start: $('#scheduleStartTime').val() || '00:00',
                });
            }

            function CompareDate(d1, d2) {
                return new Date(d1.replace(/-/g, '/')) < new Date(d2.replace(/-/g, '/'));
            }
            var today = new Date().Format('yyyy-MM-dd');
            laydate.render({
                elem: '#scheduleDate',
                trigger: 'click',
                value: opt.data.date || new Date(),
                // min: common.getOtherDate(-1),
                min: '2000-01-01',
                done: function (value, date, endDate) {
                    // $('#scheduleTime').replaceWith($('#scheduleTime').removeAttr('lay-key').removeClass('laydates').clone());
                    // if (new Date(value).getTime() == new Date(today).getTime()) {
                    //     scalTime = laydate.render(setTimeOpt(true));
                    // } else {
                    //     scalTime = laydate.render(setTimeOpt(false));
                    // }
                },
            });
            laydate.render({
                elem: '#repeatEndTime',
                trigger: 'click',
                value: '',
                // min: common.getOtherDate(-1),
                min: '2000-01-01',
                done: function (value, date, endDate) {
                    // $('#scheduleTime').replaceWith($('#scheduleTime').removeAttr('lay-key').removeClass('laydates').clone());
                    // if (new Date(value).getTime() == new Date(today).getTime()) {
                    //     scalTime = laydate.render(setTimeOpt(true));
                    // } else {
                    //     scalTime = laydate.render(setTimeOpt(false));
                    // }
                },
            });
            function getDefTime() {
                var time = new Date();
                var h = time.Format('h') * 1 + 1;
                var m = ':00';
                var m2 = ':00';
                var h2 = h + 1;
                if (h >= 23) {
                    h = 23;
                    h2 = 23;
                    m2 = ':45';
                }
                return {
                    start: '' + h + m,
                    end: '' + h2 + m2,
                };
            }
            function loadScheduleDetails(id) {
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/schedule/selectScheduleDetails',
                    dateType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        id: id,
                    }),
                    success: function (res) {
                        trasen.setNamesVal($('#evaBaseAddForm'), res.object);
                        timeSel();
                        form.render();
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            }
            form.on('select(repeatStatus)', function(data) {
                $('#repeatEndTime').val('');
                if (data.value == 0) {
                    $('#repeatEndTimeRequired').addClass('none');
                    $('#repeatEndTime').addClass('none');
                    $('#repeatEndTime').attr('lay-verify','');
                } else {
                    $('#repeatEndTime').removeClass('none');
                    $('#repeatEndTimeRequired').removeClass('none');
                    $('#repeatEndTime').attr('lay-verify','required');
                }
            });
            // 保存
            form.on('submit(sheduleSubmitCofirm)', common.debounce(function (data) {
                var d = data.field;
                var url;
                if (d.id) {
                    url = '/ts-oa/schedule/insertOrUpdate';
                } else {
                    url = '/ts-oa/schedule/insertOrUpdate';
                }
                if (!d.id) {
                    delete d.id;
                    // var nowDate = new Date().Format('yyyy-MM-dd');
                    // if (nowDate == d.scheduleDate) {
                    //     var start = d.scheduleStartTime.replace(':', '') * 1;
                    //     var n = new Date().Format('hhmm') * 1;
                    //     if (start < n) {
                    //         layer.msg('当前时间之前无法添加日程！');
                    //         return false;
                    //     }
                    // }
                }
                $.ajax({
                    url: '/ts-oa/schedule/validateScheduleTime',
                    method: 'get',
                    data: {
                        scheduleDate: d.scheduleDate,
                        scheduleStartTime: d.scheduleStartTime,
                        id: d.id || '',
                    },
                    success: function (res) {
                        if (res.success) {
                            if (res.object) {
                                layer.confirm('此时间段，有日程计划，是否继续添加？', {
                                    btn: ['确定', '取消'],
                                    title: '提示',
                                    closeBtn: 0
                                }, function (index) {
                                    layer.close(index);
                                    save();
                                });
                            } else {
                                save();
                            }
                        } else {
                            layer.msg(res.message || '验证失败');
                        }
                    },
                });
                function save() {
                    $.ajax({
                        type: 'post',
                        url: common.url + url,
                        dateType: 'json',
                        contentType: 'application/json',
                        data: JSON.stringify(d),
                        success: function (res) {
                            if (res.success) {
                                layer.closeAll();
                                layer.msg(res.object);
                                opt.ref && opt.ref();
                            } else {
                                layer.msg(res.object);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                }
                return false;
            },200));
            $('#evaBaseAddForm')
                .off('click', '#closeLyer')
                .on('click', '#closeLyer', function () {
                    layer.closeAll();
                });
        });
    };
});
