'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'laydate', 'upload'], function () {
            var form = layui.form;
            var laydate = layui.laydate;
            var upload = layui.upload;

            var wins = layer.open({
                type: 1,
                title: '批量撤回工资条',
                closeBtn: false,
                shadeClose: false,
                area: ['460px', '240px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $('#salaryBack [name="dateStr"]').val(opt.dateStr);
                    //批次号
                    $.ajax({
                        url: '/ts-oa/salary/salarySet/getSalaryBatchNumber/' + opt.type + '/' + opt.dateStr,
                        success: function (res) {
                            if (res.success) {
                                var html = '';
                                for (var i = 0; i < res.object.length; i++) {
                                    html += '<option value="' + res.object[i] + '">' + res.object[i] + '</option>';
                                }
                                $('#salaryBack [name="batchNumber"]').append(html);
                                form.render('select');
                            }
                        },
                    });
                },
            });
            function checkPwd(pwd, cb) {
                $.ajax({
                    url: '/ts-oa/salary/salarySet/verifyPassword',
                    method: 'post',
                    data: {
                        password: pwd,
                    },
                    success: function (res) {
                        if (!res.object) {
                            layer.msg('密码错误');
                        } else {
                            cb();
                        }
                    },
                });
            }
            form.render('select');
            form.on('submit(salaryBackSave)', function (data) {
                var formData = data.field;
                layer.confirm('撤回后将给对应人员发送通知，对方无法查看该月工资条内容，确定要撤回吗？', 
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    }, 
                    function (index) {
                        checkPwd(formData.password, function () {
                            $.ajax({
                                method: 'post',
                                url: '/ts-oa/salary/salarySet/cancelSalaryData/' + opt.type + '/' + opt.dateStr,
                                data: {
                                    batchNumber: formData.batchNumber,
                                },
                                success: function (res) {
                                    layer.close(index);
                                    if (res.success) {
                                        opt.ref();
                                        layer.msg(res.message || '工资条撤回成功');
                                        layer.close(wins);
                                    } else {
                                        layer.msg(res.message || '工资条撤回失败');
                                    }
                                },
                            });
                        });
                });

                return false;
            });
            $('#salaryBack')
                .off('click', '#save')
                .on('click', '#save', function () {
                    $('#salaryBack [lay-filter="salaryBackSave"]').trigger('click');
                    return false;
                });
            $('#salaryBack')
                .off('click', '#close')
                .on('click', '#close', function () {
                    layer.close(wins);
                    return false;
                });
        });
    };
});
