<style>
    .userDeptTreeBox {
        position: absolute;
        top: 45px;
        left: 10px;
        height: 60%;
    }

    .userDeptTreeBox .ztree {
        height: 95%;
        top: 0;
    }

    .layui-tree-branch {
        display: none;
    }

    .layui-form-checkbox {
        display: none;
    }

    .selectUserNameBox {
        position: absolute;
        height: 70px;
        border: 1px solid #ccc;
        top: 73%;
        width: 98%;
        padding: 10px 1%;
        margin: 0 10px;
        box-sizing: border-box;
        overflow: auto;
    }
</style>
<form id="thpsDeptEvaBaseAddForm" class="layui-form">
    <input id="roleIdValue" name="roleIdValue" type="hidden" />
    <input id="roleNameValue" name="roleNameValue" type="hidden" />
    <div class="button-area" style="padding: 5px 10px">
        <form method="" lay-filter="component-form-element" id="thpsDeptResourcesUserMainForm" class="layui-form areaButtonBoxL">
            <div class="layui-inline fl">
                <input type="text" placeholder="请输入用户编码" name="usercode" autocomplete="off" class="layui-input edi-layui-input" />
            </div>
            <div class="layui-inline fl mgl10">
                <input type="text" name="username" placeholder="请输入用户名称" autocomplete="off" class="layui-input edi-layui-input" />
            </div>
            <div class="layui-inline fl mgl10">
                <input type="text" name="deptcode" placeholder="请输入部门名称" autocomplete="off" class="layui-input edi-layui-input" id="treeSelectDept" />
            </div>
            <!-- 	        <div class="layui-inline fl mgl10">
                            <select name="status" lay-filter="select">
                                <option value='1' selected>启用</option>
                                <option value="0">停用</option>
                            </select>
                        </div> -->
            <div class="pubBtnBox fl mgl10">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="systemmanageOrgDeptScreenSearch" id="" search-input="button">搜索</button>
                <button type="button" class="layui-btn layui-btn-primary" id="ScreenReset">重置</button>
            </div>
        </form>
    </div>
    <div class="flex shell-left-tree userDeptTreeBox">
        <ul id="thpsDeptResourcesUserTree" class="ztree"></ul>
    </div>
    <div class="trasen-con-box" style="left: 220px; top: 45px; height: 60%">
        <div class="table-box" style="border: 1px solid #ddd">
            <!-- 表单 -->
            <table id="thpsDeptResourcesUserTable"></table>

            <!-- 分页 -->
            <div id="thpsDeptResourcesUserPager"></div>
        </div>
    </div>

    <!--选中人员赋值-->
    <div class="selectUserNameBox" id="select_UserNameBox"></div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="thpsDeptEvaBaseSubmitCofirm">保存</button>
        <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="chooseThpsDeptSubmitEmpty">清空</button>
        <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="closeThpsDept">关闭</button>
    </div>
</form>
