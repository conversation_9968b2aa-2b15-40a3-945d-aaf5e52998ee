"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'upload', 'trasen','zTreeSearch'], function() {
            var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            upload = layui.upload,
            zTreeSearch = layui.zTreeSearch;
            form.render("select");
            var trasenTable;

/*            // 发放月份的日期控件
            var date = new Date();
            var year = date.getFullYear(); // 获取完整的年份(4位)
            var month = date.getMonth(); // 获取当前月份(0-11,0代表1月)的上一个月
            if (month < 10) {
                month = "0" + month;
            }
            var lastDay = new Date(year, month, 0); //得到上个月月底日期
            var yearmonth = year + '-' + month;
            var date = year + '-' + month + '-' + lastDay.getDate();*/
            
			var myDate = new Date();
	        var time = myDate.toLocaleDateString().split('/').join('-');//将1970/08/08转化成1970-08-08
			function getPreMonth(date) {
				var arr = date.split('-');
				var year = arr[0]; //获取当前日期的年份
				var month = arr[1]; //获取当前日期的月份
				var day = arr[2]; //获取当前日期的日
				var days = new Date(year, month, 0);
				days = days.getDate(); //获取当前日期中月的天数
				var year2 = year;
				var month2 = parseInt(month) - 1;
				if (month2 == 0) {
					year2 = parseInt(year2) - 1;
					month2 = 12;
				}
				var day2 = day;
				var days2 = new Date(year2, month2, 0);
				days2 = days2.getDate();
				if (day2 > days2) {
					day2 = days2;
				}
				if (month2 < 10) {
					month2 = '0' + month2;
				}
				var t2 = year2 + '-' + month2 + '-' + day2;
				return t2;
			}
			let preMonthDay = getPreMonth(time);
			$("#personnelAllDayAuditSearchDate").val(preMonthDay.substring(0,7));
            laydate.render({
                elem: '#personnelAllDayAuditSearchDate',
                format: 'yyyy-MM',
                type: 'month', // 年月选择器
                btns: ['confirm'],
                value: preMonthDay.substring(0,7), // 默认选择当前日期的上一个月
                max: preMonthDay,// 最大值只可选择当前日期的上一个月（max不支持只填写年月的格式）
                done: function(value, date, endDate){
					$("#personnelAllDayAuditSearchDate").val(value)
                	refreshTable();
                  }
            });

			// 监听列表下拉选项查询变更事件
            form.on('select(personnelAllDayAuditApprovalStatusSelectChangeFilter)', function(data) {
            	refreshTable();
            });

            // 查询
            form.on('submit(personnelAllDayAuditSearch)', function(data) {
            	refreshTable()
            });
			
			// 重置
            form.on('submit(personnelAllDayAuditReset)', function(data) {
				$("#personnelAllDayAuditForm")[0].reset();
				$('#personnelAllDayAuditForm #personnelAllDayAuditSearchDate').val(preMonthDay.substring(0,7));
                form.render();
            	refreshTable()
            });
            
            //部门统计详情
            $(".areaButtonBoxR").off("click", "#personnelAllDayAuditDetailsButton").on("click", "#personnelAllDayAuditDetailsButton", function () {
            	var rowId=$("#personnelAllDayAuditTable").jqGrid('getGridParam','selrow');
            	var rowData = $("#personnelAllDayAuditTable").jqGrid('getRowData',rowId);
           	   	var date = $("input[name='attendanceDate']").val();
           	    if(date == null || date == ""){
           	    	layer.msg('请选择要查看的月份')
                    return false;
           	    }
           	    var deptName =  date + " 月全勤审核详情";
           	    //查看科室详情
           	     var deptIds = '';
           	     var ids = $("#personnelAllDayAuditTable").jqGrid('getGridParam', 'selarrrow');
           	     if(ids != null && ids.length > 0){
           	    	 for(var i = 0; i < ids.length; i++){
           	    		 var d = $("#personnelAllDayAuditTable").jqGrid('getRowData', ids[i]); 
           	    		deptIds += d.deptId + ",";
           	    	 }
           	     }
           	     // attendance:'0'出勤 ，1晚夜班
	           	 $.quoteFun('/attendanceManage/deptDetail/deptDetails', {
	                  title: deptName ,
	                  date:date,
//	                  deptId:rowData.deptId,
	                  attendance:'0',
	                  searchOrgId: deptIds
	              })
            });
            
            //加载搜索区域部门下拉框
            treeSelect();
            function treeSelect() {
                //新部门
                zTreeSearch.init('#personnelAllDayAuditDeptNameSearch', {
                    url: common.url + '/ts-basics-bottom/organization/getTree',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function(treeId, treeNode) {
                        $("#personnelAllDayAuditDiv input[name='orgId']").val(treeNode.id); 
												refreshTable()
                    }
                });
            }
            
            // 表格刷新
            function refreshTable() {
            	var orgName = $("#personnelAllDayAuditDiv input[name='orgName']").val();
//            	var orgId = $("personnelAllDayAuditDiv input[name='orgId']").val();
            	if(orgName == ""){
            		$("#personnelAllDayAuditDiv input[name='orgId']").val("");
            	}
            	initGrid();
            	$("#personnelAllDayAuditTable").jqGrid('setGridParam', {
                   postData: {
                	   orgId: $("#personnelAllDayAuditDiv input[name='orgId']").val(),
                       attendanceDate: $("#personnelAllDayAuditSearchDate").val(),
                       approvalStatus:$("#personnelAllDayAuditApprovalStatus").val()
                   } 
                }).trigger("reloadGrid");
            }
            
            //未上报明细
            $(".areaButtonBoxR").off("click", "#personnelAllDayAuditTableNoUnseBut").on("click", "#personnelAllDayAuditTableNoUnseBut", function () {
           	   	var date = $("#personnelAllDayAuditSearchDate").val();
           	    if(date == null || date == ""){
           	    	layer.msg('请选择要查看的月份')
                    return false;
           	    }
	           	$.quoteFun('/attendanceManage/unsettled/unsettled', {
	                title:'未提交考勤人员列表',
	                date:date
	            })
            });
            
            //考勤审核通过
            $(".areaButtonBoxR").off("click", "#personnelAllDayAuditPass").on("click", "#personnelAllDayAuditPass", function () {
             	var date = $("input[name='attendanceDate']").val();
           	     if(date == null || date == ""){
           	    		layer.msg('请选择要审核的月份')
                    	return false;
           	     }
				var orgIds = ""
				var ids = $("#personnelAllDayAuditTable").jqGrid('getGridParam', 'selarrrow'); //获取 多行数据
				if(ids != "" && ids.length > 0){
					for(var i=0;i<ids.length ;i++){
					var rowData = $("#personnelAllDayAuditTable").jqGrid('getRowData', ids[i]); 
					orgIds += rowData.deptId + ",";
					}
				}
          	  	var text = '<span style="color:#FF0000;">选中科室'+ date +'月的考勤将审核通过</span>';
              	layer.confirm(text, { 
									btn: ['确定', '取消'],
									title: '提示' ,
									closeBtn: 0
								}, function() {
	           	   $.ajax({
	                   type: "post",
	                   url: "/ts-hrms/attendanceRecord/updatePersonnelAllDayAudit",
	                   data: {
	                	   orgIds:orgIds,
	                	   date:$("#personnelAllDayAuditSearchDate").val()
	                   },
	                   success: function(res) {
	                       if (res.success) {
	                    	   refreshTable();
	                           layer.msg('设置成功！');
	                       } else {
	                           layer.msg(res.message);
	                       }
	                   }
	               });
               }, function() {});
            });
        });
        
        var columnArray;
        
        
        // 动态表格
        initGrid();
        function initGrid(){
            $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url: common.url + '/ts-hrms/attendanceStatistics/getPersonnelAllDayAuditTableHeadCols',
                async: false,
                success:function(res) {
                    if (res.success) {
                    	columnArray = res.object;
                        // 表格渲染   
                   	 // 表格渲染   
                        $('#personnelAllDayAuditDiv .table-box').html('<table id="personnelAllDayAuditTable"></table>')
                    	trasenTable =  $("#personnelAllDayAuditTable").jqGrid({
                        	mtype: 'POST',
                        	datatype: "json", 
                            url: common.url + "/ts-hrms/attendanceStatistics/gePersonnelAllDayAuditList",
//                            pager: 'personnelAllDayAuditPager',
                            sortname: 't2.create_date',
                            rowNum : 5000,
                            postData: {
                                orgId: $("#personnelAllDayAuditDiv input[name='orgId']").val(),
                                attendanceDate: $("#personnelAllDayAuditSearchDate").val(),
                                approvalStatus:$("#personnelAllDayAuditApprovalStatus").val()
                            },
                            multiselect: true,//复选框
                            colModel: res.object,
//                            footerrow: true,
                            //userDataOnFooter: true,
//                            rownumbers:true,//行数
                            iewrecords : true, // 是否显示行数
                            footerrow: true,
                            queryFormId: 'personnelAllDayAuditForm',
                            gridComplete: completeMethod
                        });
                    	$("#personnelAllDayAuditTable").jqGrid('setFrozenColumns');
                    }
                }
            });
        }
     
        function completeMethod(){  
          	//合计
        	var data = {}; 
	      	var colNames=$("#personnelAllDayAuditTable").jqGrid('getGridParam','colNames');
			var colModel=$("#personnelAllDayAuditTable").jqGrid('getGridParam','colModel');
			var table = "";
			var newColumnName = [];
		    var newColumnValue = [];
		   //i =3 开始  基本工资
			for (var i=3;i<colNames.length;i++) {
				var columnName = colModel[i].name;
				var count = $("#personnelAllDayAuditTable").getCol(columnName,false,'sum');
				data[columnName] = count;
			}
			data['attendanceDate'] = "合计";
			$("#personnelAllDayAuditTable").jqGrid("footerData", "set", data);
        	
        	var count = $("#personnelAllDayAuditTable").getGridParam("records");//当前有几行
	          if (count > 0) {
	        	  if(columnArray.length != "" && columnArray.length > 0 ){
	        		  var hideArray = new Array();
	        		  var showArray = new Array();
	        		  for(var i = 0; i < columnArray.length; i++){
		        		  if(columnArray[i].index != ""){
		        			  var count = $("#personnelAllDayAuditTable").getCol(columnArray[i].name, false, 'sum'); //统计要计算的列
		        			  if(count == 0){
		        				  hideArray.push(columnArray[i].name);
		        			  }else{
		        				  showArray.push(columnArray[i].name);
		        			  }
		        		  }
		        	  }
	        		  $("#personnelAllDayAuditTable").setGridParam().hideCol(hideArray).trigger("reloadGrid"); //要隐藏的列
	        		  $("#personnelAllDayAuditTable").setGridParam().showCol(showArray).trigger("reloadGrid"); //要隐藏的列
	        	  }
	          }
	          $("#personnelAllDayAuditDiv .ui-jqgrid-sdiv").addClass('ui-jqgrid-sdiv-box');
              $("#personnelAllDayAuditDiv .ui-jqgrid-bdiv").addClass('ui-jqgrid-bdiv-box-bottom');
              $("#personnelAllDayAuditDiv .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
              $('#personnelAllDayAuditDiv .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
        } 
        
    };
});