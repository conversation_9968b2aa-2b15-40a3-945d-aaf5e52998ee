"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'laydate', 'upload', 'trasen','zTreeSearch'], function() {
            var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            upload = layui.upload,
            zTreeSearch = layui.zTreeSearch;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                	//设置被考勤人员信息
                    form.render();
                    //按钮控制
                   
                }
            });
            
            var trasenTable;

            // 发放月份的日期控件
            var date = new Date();
            var year = date.getFullYear(); // 获取完整的年份(4位)
            var month = date.getMonth(); // 获取当前月份(0-11,0代表1月)的上一个月
            if (month < 10) {
                month = "0" + month;
            }
            var lastDay = new Date(year, month, 0); //得到上个月月底日期
            var yearmonth = year + '-' + month;
            var date = year + '-' + month + '-' + lastDay.getDate();

         /*   laydate.render({
                elem: '#payrollDateDateIpt',
                format: 'yyyy-MM',
                type: 'month', // 年月选择器
                btns: ['clear', 'confirm'],
                value: yearmonth, // 默认选择当前日期的上一个月
                max: date // 最大值只可选择当前日期的上一个月（max不支持只填写年月的格式）
            });*/


            // 查询
            form.on('submit(salaryRecordSearch)', function(data) {
                refreshTable();
            });

            //导出
            $(".areaButtonBoxR").off("click", "#attendanceManageDetailsTableExportCount").on("click", "#attendanceManageDetailsTableExportCount", function () {
            	var attendanceDate = opt.date;
            	var title = attendanceDate +" 月科室审核考勤明细";
            	var orgId = '';
            	if(typeof( opt.deptIds ) != "undefined"){
            		orgId = opt.deptIds;
            	}
                var url = common.url + "/ts-hrms/attendanceStatistics/exportDeptAuditDetailsList?attendanceDate="+
                attendanceDate+"&title="+title+"&searchOrgId="+orgId+"&isQQ=1";
                location.href = (url);
            });
            
            
            var columnArray;
            
            // 动态表格
            initGrid();
            function initGrid(){
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: common.url + '/ts-hrms/attendanceStatistics/getTableHeadColsByDept',
                    async:false,
                    success:function(res) {
                        if (res.success) {
                        	columnArray = res.object;
                            // 表格渲染   
                           $("#attendanceDeptAuditDetailsTable").jqGrid({
                            	mtype: 'POST',
                            	datatype: "json", 
                                url: common.url + "/ts-hrms/attendanceStatistics/getAttendanceStatisticsListByDept",
//                                pager: 'attendanceDeptAuditDetailsPager',
                                sortname: 't2.create_date',
                                postData: {
                                	attendanceDate: opt.date,
                                	searchOrgId: opt.deptIds
                                },
                                rowNum : 5000,
                                shrinkToFit:false,
                                autoScrool:false,
                                rowList : [ 5000, 5000, 5000 ],
                                colModel: res.object,
                                rownumbers:true,//行数
                                iewrecords : true, // 是否显示行数
                                footerrow: true,
                                queryFormId: 'attendanceStatisticsForm',
                                gridComplete: completeMethod,
                                ondblClickRow:function(row){
                            		var rowData = $('#attendanceDeptAuditDetailsTable').jqGrid('getRowData',row);
                            		
                	   	           	 $.quoteFun('/attendanceManage/reportedData/modules/declareTimecard', {
                		                  title: rowData.sheetEmpname +" "+ rowData.sendMonth+' 考勤详情',
                		                  empId:rowData.employeeId,
                		                  settingDate:opt.date,
                		                  isSetting:true,
                		                  empNo:rowData.employeeNo,
                		                  empName:rowData.employeeName,
                		                  orgName:rowData.deptName
                		              })
                            	}
                            });
                           $("#attendanceDeptAuditDetailsTable").jqGrid('setFrozenColumns');
                        }
                    }
                });
            }
         
            function completeMethod(){  
            	
              	//合计
            	var data = {}; 
		      	var colNames=$("#attendanceDeptAuditDetailsTable").jqGrid('getGridParam','colNames');
				var colModel=$("#attendanceDeptAuditDetailsTable").jqGrid('getGridParam','colModel');
				var table = "";
				var newColumnName = [];
			    var newColumnValue = [];
			   //i =3 开始  基本工资
				for (var i=4;i<colNames.length;i++) {
					var columnName = colModel[i].name;
					var count = $("#attendanceDeptAuditDetailsTable").getCol(columnName,false,'sum');
					data[columnName] = count;
				}
				data['attendanceDate'] = "合计";
				$("#attendanceDeptAuditDetailsTable").jqGrid("footerData", "set", data);
            	
            	var count = $("#attendanceDeptAuditDetailsTable").getGridParam("records");//当前有几行
    	          if (count > 0) {
    	        	  if(columnArray.length != "" && columnArray.length > 0 ){
    	        		  var hideArray = new Array();
    	        		  var showArray = new Array();
    	        		  for(var i = 0; i < columnArray.length; i++){
    		        		  if(columnArray[i].index != ""){
    		        			  var count = $("#attendanceDeptAuditDetailsTable").getCol(columnArray[i].name, false, 'sum'); //统计要计算的列
    		        			  if(count == 0){
    		        				  hideArray.push(columnArray[i].name);
    		        			  }else{
    		        				  showArray.push(columnArray[i].name);
    		        			  }
    		        		  }
    		        	  }
    	        		  $("#attendanceDeptAuditDetailsTable").setGridParam().hideCol(hideArray).trigger("reloadGrid"); //要隐藏的列
    	        		  $("#attendanceDeptAuditDetailsTable").setGridParam().showCol(showArray).trigger("reloadGrid"); //要隐藏的列
    	        	  }
    	          } else {
    	          
    	          }
    	          
    	          $("#attendancedeStatisticsDiv .ui-jqgrid-sdiv").addClass('ui-jqgrid-sdiv-box');
                  $("#attendancedeStatisticsDiv .ui-jqgrid-bdiv").addClass('ui-jqgrid-bdiv-box-bottom');
                  $("#attendancedeStatisticsDiv .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                  $('#attendancedeStatisticsDiv .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
            } 

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }
            
        });
        

        
    };
});