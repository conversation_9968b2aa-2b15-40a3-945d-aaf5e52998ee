"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'upload', 'trasen','zTreeSearch'], function() {
        	
            var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            upload = layui.upload,
            zTreeSearch = layui.zTreeSearch;
        	
            
            
            //表格渲染
            var attendanceRelationTable = new $.trasenTable("attendanceRelationTable", {
                url: common.url + "/ts-hrms/timekeeper/getDataList",
                pager: 'attendanceRelationPager',
                sortname: 't4.name',
                sortorder: 'asc',
                multiselect: false,//复选框
                rownumbers: true,
                shrinkToFit: true,
                colModel: [
                	
                	{ label: '考勤员姓名', name: 'timeEmployeeName',align: "left", index:'t2.employee_name',  editable: false },
                    { label: '考勤员工号', name: 'timeEmployeeNo',align: "left", index:'t2.employee_no',  editable: false },
                	{ label: '考勤员科室', name: 'timeOrgName', align: "left", index:'t4.name',  editable: false },
                    { label: '被考勤人员工号', name: 'byEmployeeNo',align: "left", index:'t3.employee_no',  editable: false },
                    { label: '被考勤人员姓名', name: 'byEmployeeName',align: "left", index:'t3.employee_name',  editable: false },
                    { label: '被考勤人员科室', name: 'byOrgName',align: "left", index:'t5.name',  editable: false },
	                { label: 'id', name: 'id', index: 'id',  editable: false, hidden: true ,key:true}
                ],
                queryFormId: 'attendanceRelationForm'
            });
            

            // 查询
            form.on('submit(attendanceRelationSearch)', function(data) {
                refreshTable();
            });
            //重置
            form.on('submit(attendanceRelationRe)', function(data) {
            	$("#attendanceRelationForm input[name='employeeNo']").val("");
            	$("#attendanceRelationForm input[name='employeeName']").val("");
            	$("#attendanceRelationForm input[name='orgName']").val("");
                refreshTable();
            });
            
            // 表格刷新
            function refreshTable() {
            	attendanceRelationTable.refresh();
            }
            
            $(".areaButtonBoxR").off('click', '#setAttendanceItemTimekeeper').on("click", "#setAttendanceItemTimekeeper", function() {
            	$.quoteFun('attendanceManage/attendanceItem/modules/timekeeper', {
            		title: '考勤关系'
            	})
            });

        });
    };
});