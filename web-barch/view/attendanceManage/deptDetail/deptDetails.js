"use strict";
define(function (require, exports, module) {
	var attendanceStatus;
    exports.init = function (opt, html) {
        layui.use(['form', 'laydate', 'upload', 'trasen','zTreeSearch'], function() {
        	attendanceStatus = opt.attendance;
            var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            upload = layui.upload,
            zTreeSearch = layui.zTreeSearch;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                	//设置被考勤人员信息
                    form.render();
                    //按钮控制
                   
                }
            });
            
            var trasenTable;

            // 发放月份的日期控件
            var date = new Date();
            var year = date.getFullYear(); // 获取完整的年份(4位)
            var month = date.getMonth(); // 获取当前月份(0-11,0代表1月)的上一个月
            if (month < 10) {
                month = "0" + month;
            }
            var lastDay = new Date(year, month, 0); //得到上个月月底日期
            var yearmonth = year + '-' + month;
            var date = year + '-' + month + '-' + lastDay.getDate();

            laydate.render({
                elem: '#payrollDateDateIpt',
                format: 'yyyy-MM',
                type: 'month', // 年月选择器
                btns: ['clear', 'confirm'],
                value: yearmonth, // 默认选择当前日期的上一个月
                max: date, // 最大值只可选择当前日期的上一个月（max不支持只填写年月的格式）
                done: function(value, date, endDate){
                    $('#payrollDateDateIpt').val(value)
                    refreshTable();
                }
            });


            // 查询
            form.on('submit(salaryRecordSearch)', function(data) {
                refreshTable();
            });

            
        });
        
        var columnArray;
        
        // 动态表格
        initGrid();
        function initGrid(){
            $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url: common.url + "/ts-hrms/attendanceStatistics/getTableHeadColsByDept",
                async:false,
                success:function(res) {
                    if (res.success) {
                    	columnArray = res.object;
                        // 表格渲染   
                      $('#attendanceDeptDetailStatisticsDiv .table-box').html('<table id="attendanceCheckDetailsTable"></table>')
                       $("#attendanceCheckDetailsTable").jqGrid({
                        	mtype: 'POST',
                        	datatype: "json", 
                            url: common.url + "/ts-hrms/attendanceStatistics/getAttendanceStatisticsListByDeptDetails",
//                            pager: 'attendanceCheckDeptDetailsPager',
                            sortname: 't2.create_date',
                            postData: {
                            	attendanceDate: opt.date,
                            	searchOrgId: opt.searchOrgId,
                                isQQ:attendanceStatus
                            },
                            rowNum : 5000,
                            rowList : [ 5000, 5000, 5000 ],
                            colModel: res.object,
                            rownumbers:true,//行数
                            iewrecords : true, // 是否显示行数
                            footerrow: true,
                            queryFormId: 'attendanceStatisticsForm',
                            gridComplete: completeMethod,
                            ondblClickRow:function(row){
                            	var rowData = $('#attendanceCheckDetailsTable').jqGrid('getRowData',row);
                            	//弹出到工资展示页面
                             	 $.quoteFun('/attendanceManage/reportedData/modules/declareTimecard', {
	                                 title: rowData.sheetEmpname +" "+ rowData.sendMonth+' 考勤详情',
	                                 empId:rowData.employeeId,
	                                 settingDate:opt.date,
	                                 isSetting:true,
	                                 empNo:rowData.employeeNo,
	                                 empName:rowData.employeeName,
	                                 orgName:rowData.deptName
                             })
                            }
                        });
                       $("#attendanceCheckDetailsTable").jqGrid('setFrozenColumns');
                       
                    }
                }
            });
        }
     
        function completeMethod(){
        	
          	//合计
        	var data = {}; 
	      	var colNames=$("#attendanceCheckDetailsTable").jqGrid('getGridParam','colNames');
			var colModel=$("#attendanceCheckDetailsTable").jqGrid('getGridParam','colModel');
			var table = "";
			var newColumnName = [];
		    var newColumnValue = [];
		   //i =3 开始  基本工资
			for (var i=4;i<colNames.length;i++) {
				var columnName = colModel[i].name;
				var count = $("#attendanceCheckDetailsTable").getCol(columnName,false,'sum');
				data[columnName] = count;
			}
			data['attendanceDate'] = "合计";
			$("#attendanceCheckDetailsTable").jqGrid("footerData", "set", data);
        	
        	var count = $("#attendanceCheckDetailsTable").getGridParam("records");//当前有几行
	          if (count > 0) {
	        	  if(columnArray.length != "" && columnArray.length > 0 ){
	        		  var hideArray = new Array();
	        		  for(var i = 0; i < columnArray.length; i++){
		        		  if(columnArray[i].index != ""){   
		        			  var count = $("#attendanceCheckDetailsTable").getCol(columnArray[i].name, false, 'sum'); //统计要计算的列
		        			  if(count == 0){
		        				  hideArray.push(columnArray[i].name);
		        			  }
		        		  }
		        	  }
	        		  $("#attendanceCheckDetailsTable").setGridParam().hideCol(hideArray).trigger("reloadGrid"); //要隐藏的列
	        	  }
	          } else {
	          
	          }
	          
	          $("#attendanceDeptDetailStatisticsDiv .ui-jqgrid-sdiv").addClass('ui-jqgrid-sdiv-box');
              $("#attendanceDeptDetailStatisticsDiv .ui-jqgrid-bdiv").addClass('ui-jqgrid-bdiv-box-bottom');
              $("#attendanceDeptDetailStatisticsDiv .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
              $('#attendanceDeptDetailStatisticsDiv .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
       
        } 

        // 表格刷新
        function refreshTable() {
            trasenTable.refresh();
        }
        
        //导出
        $(".areaButtonBoxR").off("click", "#attendanceDetailsTableExport").on("click", "#attendanceDetailsTableExport", function () {
        	var title = "";
        	var isQQ = opt.attendance;//0全勤，1 晚夜班
        	var attendanceDate = opt.date;
        	if(isQQ == "0"){
        		title = attendanceDate + " 月全勤审核数据明细";
        	}else if(isQQ == "1"){
        		title = attendanceDate + " 月晚夜班审核数据明细";
        	}
        
        	var orgId = '';
        	if(typeof( opt.searchOrgId ) != "undefined"){
        		orgId = opt.searchOrgId;
        	}
        	
            var url = common.url + "/ts-hrms/attendanceStatistics/exportAttendance?attendanceDate="
            	+attendanceDate+"&title="+title+"&searchOrgId="+orgId+"&isQQ="+isQQ;
            location.href = (url);
        });
    };
});