"use strict";

define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }

    var perform = function() {
        let API = {
          changeFaultTypeStatus: '/ts-worksheet/faultType/status',//更改故障类型 启用/禁用 状态
          deleteFaultType: '/ts-worksheet/faultType/deleteFaultType',//删除故障类型
          stopFaultType:'/ts-worksheet/faultType/status',//禁用标签
          useFaultType: '/ts-worksheet/faultType/faultTypeEnable/',//启用标签
          loginPersonInfo: '/ts-worksheet/workSheetPeopple/loginPersonInfo',
        };
        var path = {};
        var searchIds = '';
        var loginUserInfo = {};
        $.ajax({
          url: API.loginPersonInfo,
          type: 'GET',
          async: false,
          success: function (res) {
            if(!res.success){
              res.message ? layer.msg(res.message) : null;
              return;
            }

            if(!res.object){
              return
            }

            var _data = {}
            _data.repairManDeptId = res.object.deptId;
            _data.repairManDeptName = res.object.deptName;
            _data.repairManName = res.object.name;
            _data.repairManId = res.object.userId;
            _data.repairPhone = (res.object.phone||'').trim();

            loginUserInfo = _data
          }
        })

        //获取当前登录人信息 用来做切换

        layui.use(['form', 'trasen', 'zTreeSearch'], function() {
            var form = layui.form,
                layer = layui.layer,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch
                
            //  树列表
            documentTree();

            //  索引树
            $(document).off('input', '#orderConfigFaultType').on('input', '#orderConfigFaultType', function() {
                var treeObj = $.fn.zTree.getZTreeObj("orderConfigFaultTypeTree");
                var val = $(this).val();
                setTimeout(function() {
                    $.fn.ztreeQueryHandler(treeObj, val);
                }, 200);
            });

            //新增按钮 点击事件
            $('#orderConfigFaultTypeBoxBtn').funs('click',function(){
                $.quoteFun('orderConfig/faultType/modules/add', {
                    title: '新增',
                    data: path,
                    loginUserDeptId: loginUserInfo.repairManDeptId,
                    ref:function () {
                        refresh();
                        documentTree();
                    }
                })
            });

            //监听搜索事件，刷新表格
            form.on('submit(searchInfoSubmit)',function(){
                refresh();
            });
            //监听删除事件，实现全部删除，并且刷新
            form.on('submit(deletInfo)',function(){
                let rowIDs = SysGropuTable.getSelectAllRowIDs();
                //获取所有选中行的数据
                let deleteIds = [];
                let rowDatas = SysGropuTable.getSelectAllRowData()||[];
                rowDatas.forEach((item)=>{
                    deleteIds.push(item.pkFaultTypeId);
                })
                handleDelete( { pkFaultEquipmentIds: deleteIds.join(',') } );
            });

            bindActionEvent();//绑定操作点击事件
        });



        //表格数据
        var SysGropuTable = new $.trasenTable('orderConfigFaultTypeTable', {
            url: common.url  + '/ts-worksheet/faultType/getFaultTypePageList',
            pager: '#orderConfigFaultTypeTablePage',
            mtype: 'post',
            shrinkToFit: true,
            multiselect: true, //复选框
            sortname: 'create_time',
            sortorder: 'ASC',
            postData: {
                groupType: 0,
                fkDeptId: loginUserInfo.repairManDeptId
            }, //系统群组
            colModel: [
                {
                    label: '业务类型ID',
                    name: 'pkFaultTypeId',
                    sortable: false,
                    align: 'left',
                    hidden: true,
                },
                {
                    label: '父Id',
                    name: 'parentId',
                    sortable: false,
                    align: 'left',
                    hidden: true,
                },
                {
                    label: '分类名称',
                    name: 'categoryName',
                    width: 80,
                    align: 'left',
                    sortable: false,
                },
                {
                    label: '全路径',
                    name: 'fullPath',
                    width: 130,
                    align: 'left',
                    sortable: false,
                },
                {
                    label: '状态',
                    sortable: false,
                    name: 'faultStatus',
                    index: 'GROUP_ORDER',
                    width: 50,
                    align: 'center',
                    formatter: function(cellvalue, options, rowObject){
                        return cellvalue == '1' ? '<span class="font-green">启用中</span>' 
                                : '<span class="font-danger">停用</span>';
                    }
                },
                {
                    label: '故障处理人',
                    sortable: false,
                    name: 'peopleInfoOutVos',
                    width: 130,
                    align: 'left',
                    formatter: function (cellvalue, options, rowObject) {
                        // return rowObject.location + ' ' + rowObject.name;
                        var title = ''
                        if(rowObject.peopleInfoOutVos) {
                            for (var i = 0; i < rowObject.peopleInfoOutVos.length; i++) {
                                var item = rowObject.peopleInfoOutVos[i]
                                title += item.name + ' '
                            }
                        }
                        return title
                    },
                },
                // {
                //     label: '贡献时间',
                //     sortable: false,
                //     name: 'createDate',
                //     width: 150,
                //     align: 'center',
                // },
                {
                    label: '使用人范围名称',
                    sortable: false,
                    name: 'rangeName',
                    width: 10,
                    align: 'right',
                    hidden: true,
                },
                {
                    label: '使用人ID',
                    sortable: false,
                    name: 'rangeEmp',
                    align: 'right',
                    width: 10,
                    hidden: true,
                },
                {
                    label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                    sortable: false,
                    name: '',
                    index: '',
                    width: 40,
                    fixed: true,
                    resizable: false,
                    editable: false,
                    title: false,
                    align: 'center',
                    classes: 'visible jqgrid-rownum ui-state-default',
                    formatter: function (cellvalue, options, row) {
                        let statu = { 
                          name: row.faultStatus == '1' ? '禁用':'启用',
                          icon: row.faultStatus == '1' 
                            ? 'fa fa-ban' 
                            : 'fa fa-play-circle-o'
                        };
                        var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                        var btns = '';
                        btns += '<button class="layui-btn" id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                        btns += '<button class="layui-btn" id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                        // btns += '<button class="layui-btn change-status-btn" title="启用/禁用" row-id="' + options.rowId + '">'+ (row.faultStatus == '1' ? '禁用':'启用') +'</button>'

                        btns += `
                          <button 
                            class="layui-btn change-status-btn" 
                            title="启用/禁用"
                            row-id="${options.rowId}"
                            >
                            <i class="${statu.icon} deal_icon" aria-hidden="true"></i>
                            ${statu.name}
                          </button>
                        `
                        // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                        // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                        html += btns + '</div></div>';
                        return html;
                    },
                },
            ],
            //刷新表格数据时，调用该方法进行接口刷新
            buidQueryParams: function () {
                // var search = $('#sys_GroupForm').serializeArray();
                // var opt = [];
                // var data = {};
                // for (var i in search) {
                //     opt.push(search[i]);
                // }
                // for (var i in opt) {
                //     data[opt[i].name] = opt[i].value;
                // }
                let searchData = {};
                searchData.categoryName = $('#orderConfigFaultTypeBox #categoryNameInput')[0].value||'';
                searchData.pkFaultTypeIds = searchIds;
                return searchData;
            },
        });


        // 刷新
        function refresh() {
            SysGropuTable.refresh();
        }
        function documentTree(){
            var trasen = layui.trasen

            $('#orderConfigFaultTypeTree').empty();
            trasen.ztree('#orderConfigFaultTypeTree', {
                url: common.url + "/ts-worksheet/faultType/getFaultTypeAllList/0/" + loginUserInfo.repairManDeptId,
                type: 'get',
                checkbox: false,
                open: 'all',
                zTreeOnClick: function(event, treeId, treeNode) {
                    $("#jobtitleBasicPid").val(treeNode.id);
                    // path = {};
                    var arr = treeNode.getPath();
                    let fullPath = '';

                    for(var i = 0; i < arr.length; i++){
                        var item = arr[i]
                        fullPath += item.name
                        if(i != arr.length - 1){
                            fullPath += '-'
                        }
                    }
                    
                    if(path.fullPath && path.fullPath == fullPath){
                        return
                        // 禁止两次点击取消
                        path = {};
                        let  treeObj = $.fn.zTree.getZTreeObj("orderConfigFaultTypeTree");
                        treeObj.refresh();
                        searchIds = null;
                    }
                    else{
                        path.fullPath = fullPath === '全部分类' ? null : fullPath;
                        path.parentId = fullPath === '全部分类' ? null : treeNode.id;
                        path.parentName = treeNode.name === '全部分类' ? null : treeNode.name;
                        searchIds = '';
                        getAllChildIds(treeNode);
                    }

                    refresh();
                },
                zTreeOnSuccess: function() {
                    //默认选中全部分类
                    const zTree = $.fn.zTree.getZTreeObj("orderConfigFaultTypeTree");//界面中加载ztree的div
                    if(!path.parentId){
                      var node = zTree.getNodeByParam("code",'123456789');//查找节点
                      zTree.selectNode(node);//选中
                      return
                    }

                    let checkedNode = zTree.getNodeByParam('id', path.parentId);
                    zTree.selectNode(checkedNode, true);
                    zTree.expandNode(checkedNode, true, false);
                    
                },
                zTreeAsyncSuccess: function(node) {
                  node.addNodes(null,0, {
                    name:'全部分类',
                    id: '',
                    fullPath: '',
                    code: 123456789,
                  })
                },
            });
            // addNodes(null,{
            //   name: '全部分类'
            // })
        }
        // >------------------- 事件绑定 --------------------<
        // 添加操作点击事件
        function bindActionEvent(table){
            $('.table-more-btn [title="编辑"]').funs('click', function(e){
                e.stopPropagation();
                let node = e.target,
                rowId = node.getAttribute('row-id'),
                data = SysGropuTable.getSourceRowData(rowId);
                
                //取消选中
                // table.setSelection(rowId,false);

                let parentNames = (data.fullPath||'').split('-');
                data.parentName = parentNames.length<=2 ?
                        parentNames.length==2 ? parentNames[0] : ''
                        : parentNames[parentNames.length-2];
                
                data.faultStatus == '1' ? data.faultStatus = 'on' : data.faultStatus = undefined;
                $.quoteFun('orderConfig/faultType/modules/add', {
                    title: '编辑',
                    data: data,
                    loginUserDeptId: loginUserInfo.repairManDeptId,
                    ref: function(){
                        refresh();
                        documentTree();
                    }
                })
            });
            $('.table-more-btn [title="删除"]').funs('click', function(e){
                e.stopPropagation();
                let node = e.target,
                rowId = node.getAttribute('row-id'),
                data = SysGropuTable.getSourceRowData(rowId),
                deleteData = {};
                deleteData.pkFaultEquipmentIds = data.pkFaultTypeId;
                handleDelete(deleteData);
            });
            $('.table-more-btn [title="启用/禁用"]').funs('click', function(e){
                e.stopPropagation();
                let node = e.target,
                rowId = node.getAttribute('row-id'),
                data = SysGropuTable.getSourceRowData(rowId);
                
                let changeData = {
                    pkFaultTypeId: data.pkFaultTypeId,
                    faultStatus: data.faultStatus == '1' ? '0' : '1',
                }

                if(data.faultStatus == '1'){
                    layer.open({
                        title: '提示',
                        content: '停用分类将导致该分类无法正常使用，确定要停用吗？',
                        btn: ['确定', '取消'],
                        btn1: function( index, layero){
                            handleChangeFalTypSta(changeData).then(res=>{
                                refresh();
                                documentTree();
                                layer.close(index);
                                layer.msg('禁用成功');
                            }).catch(res=>{
                                layer.msg(res||'禁用失败')
                            })
                        },
                        btn2: function( index, layero){
                            layer.close(index);
                        }
                    })
                }
                else{
                    handleChangeFalTypSta(changeData).then((res)=>{
                        layer.msg('成功启用');
                        refresh();
                        documentTree();
                    }).catch(res=>{
                        layer.msg(res||"启用失败")
                    });
                }
            })
        }


        // >----------------------  处理函数 ----------------------<
        //改变启用状态
        function handleChangeFalTypSta(data){
          return new Promise((resolve, reject)=>{
              $.ajax({
                  url: API.stopFaultType,
                  type: 'POST',
                  data: JSON.stringify( data ),
                  // data: {faultTypeStatusInputVo: data.pkFaultTypeId},
                  contentType: 'application/json;charset=UTF-8',
                  success: function(res){
                      res.success ? resolve():reject(res.message);
                  }
              })
          })
        }

        //获取所有子分类标签ID
        function getAllChildIds(treeNode){
            searchIds = (searchIds ? searchIds + ',' : '') + treeNode.id;

            if( treeNode.children || Object.prototype.toString.call(treeNode.children)=="[object Array]"){
                let list = treeNode.children;
                for(let item of list){
                    getAllChildIds(item);
                }
            }
        }

        //处理删除
        function handleDelete(data){
            layer.open({
                title: '提示',
                content: '删除后将无法恢复，确定要删除该分类吗？',
                btn:['确定','取消'],
                btn1:function(index, layero){
                    $.ajax({
                        url:API.deleteFaultType,
                        type: 'POST',
                        data: data,
                        success: function(res){
                            layer.close(index);
                            if(res.success){
                                layer.msg('删除成功');
                                refresh();
                                documentTree();
                            }
                            else{
                                layer.msg(res.message||'删除失败，请重试');
                            }
                        }
                    })
                },
                btn2: function(index, layero){
                    layer.close(index);
                }
            })
        }

        //事件监听
        Event.create('refreshNowPageDialog').listen('#/orderConfig/faultType', ()=>{
          if (!SysGropuTable) {
            return;
          }
          refresh();
          documentTree();
        });
    };
});
