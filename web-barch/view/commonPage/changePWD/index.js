define(function (require, exports, module) {
    module.exports = {
        init: init,
    };
    $.ajax({
        url: '/ts-basics-bottom/globalSetting/getAllGlobalSetting',
        method: 'get',
        success: function (res) {
            if (res.success && res.object) {
                common.globalSetting = res.object;
				console.log(common.globalSetting, '??')
            }
        },
    });
    function init(opt, html) {
        layui.use(['form'], function () {
            var form = layui.form;
            var wins = layer.open({
                type: 1,
                title: '修改密码',
                closeBtn: opt.close === false ? false : 1,
                shadeClose: false,
                area: ['420px', '250px'],
                skin: 'yourclass',
                content: html,
                success: function () {
                    $('#changePwd #usercode').val($.cookie('usercode'));
                },
            });
            var st = {
                1: '必须包含大写字母',
                2: '必须包含小写字母',
                3: '必须包含数字',
                4: '必须包含特殊字符',
            };
            //密码修改保存
            form.on('submit(main-msg-submit)', function (data) {
                var params = $('#mainEditorMsg').serializeArray();
                var data = {};
                var _url = window.location.href;
                var _urls = _url.split('#');
                var token = $.cookie('THPMSCookie');
                var u = _urls[0] + 'user/logout?u=' + _urls[0];

                for (var i in params) {
                    data[params[i]['name']] = params[i]['value'];
                }

                if ($('#newpassword').val() != $('#renewpassword').val()) {
                    layer.msg('两次密码输入不正确！');
                    $('#renewpassword').focus();
                    return false;
                }
                var level = common.pwdCheckStrong(data.newpassword, common.globalSetting.passwordLength);
                var rules = [];
                if(null != common.globalSetting.passwordRule && '' != common.globalSetting.passwordRule){
                	rules = common.globalSetting.passwordRule.split(',');
                }
                
                var check = true;
                if (level.level < rules.length || level.level == -1) {
                    check = false;
                }
                for (var i = 0; i < rules.length; i++) {
                    if (!level.checkType[rules[i]]) {
                        check = false;
                    }
                }
                if ($('#newpassword').val().length < (common.globalSetting.passwordLength || 6)) {
                    layer.msg('密码不能少于' + (common.globalSetting.passwordLength || 6) + '位数！');
                    $('#renewpassword').focus();
                    return false;
                }
                if (!check) {
                    var arr = rules;
                    var infoStr = '';
                    for (var i = 0; i < arr.length; i++) {
                        infoStr += '，' + st[arr[i]];
                    }
                    layer.msg('修改后的密码' + infoStr);
                    return false;
                }

                // 转换小写
                data.usercode = ($.cookie('sso_domain_user_code') || $.cookie('sso_user_code')).toLowerCase();
                data.oldpassword = Encrypt(data.oldpassword.trim()).toString();
                data.newpassword = Encrypt(data.newpassword.trim()).toString()
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-basics-bottom/user/chgpwd',
                    dateType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (res) {
                        if (typeof res == 'string') {
                            res = JSON.parse(res);
                        }
                        if (res.success) {
                            layer.msg(res.message);
                            layer.close(wins);
                            setTimeout(function () {
                                // $.cookie('lastMenuId','',{expires:-1}); //三级菜单
                                // $.cookie('lastMenuIdqx','',{expires:-1});
                                $.ajax({
                                    // url: '/user/logout?u=' + _urls[0],
                                    url: '/ts-basics-bottom/user/logout?u=' + _urls[0],
                                    success: function (resp) {
                                        if (resp && resp.success) {
                                            $.cookie('THPMSCookie', '', {
                                                expires: -1,
                                                path: '/',
                                            }); //token
                                            $.cookie('token', '', {
                                                expires: -1,
                                                path: '/',
                                            });
                                            $.cookie('lastMenuId', '', {
                                                expires: -1,
                                            }); //三级菜单
                                            $.cookie('lastMenuIdqx', '', {
                                                expires: -1,
                                            });
                                            $.cookie('firstMenuId', '', {
                                                expires: -1,
                                            });
                                            $.cookie('menuId', '12');
                                            sessionStorage.removeItem('lowPwd');
                                            location.href = location.origin + '/login.html';
                                            location.reload();
                                        } else {
                                            alert('退出错误.');
                                        }
                                    },
                                });
                            });
                        } else {
                            layer.msg(res.message);
                            $('#oldpassword').focus();
                        }
                    },
                });
            });
        });
    }
});
