<style>
    .personTransfer {
        display: flex;
        height: 100%;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 14px;
    }

    .personTransfer .transferBox {
        flex: 1;
        border: 1px solid #ccc;
    }

    .personTransfer .transferBtn {
        width: 60px;
    }

    .personTransfer .transferBtn span {
        display: block;
        width: 24px;
        height: 24px;
        margin: 0 auto;
        text-align: center;
        font-size: 16px;
        line-height: 24px;
        border: 1px solid #ccc;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        margin-bottom: 5px;
        cursor: pointer;
    }
    .leftTransfer,
    .rightTransfer {
        height: calc(100% - 61px);
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
        border-top: 1px solid #ccc;
    }
    .leftTransfer li,
    .rightTransfer li {
        background: #fff;
        overflow: hidden;
        color: #333;
        box-sizing: border-box;
        padding: 0 10px;
        margin: 10px 0;
    }
    .leftTransfer li {
        cursor: pointer
    }
    .rightTransfer li {
        cursor: grab;
    }
    .rightTransfer li:active {
        cursor: grabbing;
    }
    .leftTransfer li.sel,
    .rightTransfer li.sel {
        background: #ecf5f9;
    }
    .transferBtn span:hover {
        color: #70b603;
        border-color: #70b603;
    }
    .transferPager {
        line-height: 30px;
        text-align: center;
        position: relative;
        font-style: normal;
    }
    .transferPager button {
        width: 24px;
        height: 24px;
        background: #f2f2f2;
        border: 1px solid #ccc;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        outline: none;
        cursor: pointer;
        color: #217db4;
    }
    .transferPager button:hover {
        color: #fff;
        background: #217db4;
        border-color: #217db4;
    }
    .transferPager button:disabled {
        cursor: no-drop !important;
        background: #f2f2f2;
        color: #217db4;
        border: 1px solid #ccc;
    }
    .transferPager .allData {
        position: absolute;
        left: 2px;
        top: 0;
        font-size: 12px;
    }
    .transferPager i {
        font-style: normal;
    }
</style>
<div id="personTransfer">
    <div class="layui-content-box" style="padding: 0">
        <div class="layui-tab-content personTransfer">
            <div class="transferBox">
                <input type="text" class="layui-input" style="border-radius: 0; border: none" placeholder="输入人员姓名" id="leftSearch" />
                <ul class="leftTransfer">
                </ul>
                <div class="transferPager leftPage">
                    <span class="allData">
                        共
                        <i id="leftTotal">0</i>
                        条
                    </span>
                    <button disabled="disabled" id="leftFirstPage"><i class="fa fa-angle-double-left" aria-hidden="true"></i></button>
                    <button disabled="disabled" id="leftPrevPage"><i class="fa fa-angle-left" aria-hidden="true"></i></button>
                    <span class="page">
                        第
                        <i id="leftPageNo">1</i>
                        /
                        <i id="leftPageCount">0</i>
                    </span>
                    <button id="leftNextPage"><i class="fa fa-angle-right" aria-hidden="true"></i></button>
                    <button id="leftLastPage"><i class="fa fa-angle-double-right" aria-hidden="true"></i></button>
                </div>
            </div>
            <div class="transferBtn">
                <span style="margin-top: 120px" id="leftToRight">
                    <i class="fa fa-caret-right" aria-hidden="true"></i>
                </span>
                <span id="rightToLeft">
                    <i class="fa fa-caret-left" aria-hidden="true"></i>
                </span>
                <span style="font-size: 12px; margin-top: 30px" id="leftToRightAll">
                    <i class="fa fa-forward" aria-hidden="true"></i>
                </span>
                <span style="font-size: 12px" id="rightToLeftAll">
                    <i class="fa fa-backward" aria-hidden="true"></i>
                </span>
            </div>
            <div class="transferBox">
                <input type="text" class="layui-input" style="border-radius: 0; border: none" placeholder="输入人员姓名" id="rightSearch" />
                <ul class="rightTransfer">
                </ul>
                <div class="transferPager rightPage">
                    <span class="allData">
                        共
                        <i id="rightTotal">0</i>
                        条
                    </span>
                    <!-- <button disabled="disabled" id="rightFirstPage"><i class="fa fa-angle-double-left" aria-hidden="true"></i></button>
                    <button disabled="disabled" id="rightPrevPage"><i class="fa fa-angle-left" aria-hidden="true"></i></button>
                    <span class="page">
                        第
                        <i id="rightPageNo">1</i>
                        /
                        <i id="rightPageCount">0</i>
                    </span>
                    <button id="rightNextPage"><i class="fa fa-angle-right" aria-hidden="true"></i></button>
                    <button id="rightLastPage"><i class="fa fa-angle-double-right" aria-hidden="true"></i></button> -->
                </div>
            </div>
        </div>
    </div>
    <div class="layer-btn archivesTabBtn">
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="save">保存</a>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="close">取消</a>
    </div>
</div>
