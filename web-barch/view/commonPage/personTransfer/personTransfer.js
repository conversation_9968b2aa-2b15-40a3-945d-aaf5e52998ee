'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var selData = opt.selData || [];
        var defaultData = opt.defaultData || [];
        var leftData = [];
        var rightData = [];
        var rightFiltData = selData;
        var optPager = {
            pageNo: 'page',
            pageSize: 'rows',
            condition: 'username',
        };
        if (opt.pager) {
            optPager = $.extend({}, optPager, opt.pager);
        }

        var leftPager = {
            pageNo: '1',
            pageSize: '15',
            pageCount: '0',
            total: '0',
        };
        var rightPager = {
            pageNo: '1',
            pageSize: '10000',
            pageCount: '0',
            total: '0',
        };

        function getLeftData() {
            leftData = [];
            var data = {};
            data[optPager.pageNo] = leftPager.pageNo;
            data[optPager.pageSize] = leftPager.pageSize;
            data[optPager.condition] = leftPager.condition;
            $.ajax({
                method: 'get',
                url: opt.url || '/ts-system/user/userlist2',
                data: data,
                success: function (data) {
                    if (typeof data == 'string') {
                        var data = JSON.parse(data);
                    }
                    leftPager.total = data.records || data.total || data.totalCount;
                    leftPager.pageCount = Math.ceil(leftPager.total / leftPager.pageSize);
                    leftData = data.rows;
                    setLeftTransfer();
                },
            });
        }

        function setLeftTransfer() {
            // leftData = leftData.slice(0,15)
            var html = '';
            for (var i = 0; i < leftData.length; i++) {
                if (filtRightData(leftData[i]) == -1) {
                    html += '<li row="' + leftData[i].usercode + '">'
                        + '<p>' + leftData[i].username + '(' + leftData[i].usercode + ')</p>'
                        + '<p style="font-size: 12px; color: #666;">' + leftData[i].deptname + '</p></li>';
                }
            }
            $('#personTransfer .leftTransfer').html(html);
            setLeftPager();
            leftBtnDis();
        }

        function filtRightData(item) {
            var index = -1;
            for (var i = 0; i < rightData.length; i++) {
                if (rightData[i].usercode == item.usercode) {
                    index = i;
                    return index;
                }
            }
            return index;
        }

        function setLeftPager() {
            $('#leftTotal').html(leftPager.total);
            $('#leftPageCount').html(leftPager.pageCount);
            $('#leftPageNo').html(leftPager.pageNo);
        }

        function getRightData(num) {
            rightPager.pageCount = Math.ceil(rightFiltData.length / rightPager.pageSize);
            rightPager.total = rightFiltData.length;
            var num = num - 1 || 0;
            rightData = rightFiltData.slice(num, rightPager.pageSize);
            var html = '';
            for (var i = 0; i < rightData.length; i++) {
                html += 
                    '<li class="rightTransferItem" data-id="' + i +'" row="' + rightData[i].usercode + '">'
                        + '<p>' + rightData[i].username + '(' + rightData[i].usercode + ')</p>'
                        + '<p style="font-size: 12px; color: #666;">' + rightData[i].deptname + '</p>'
                    + '</li>';
            }
            $('#personTransfer .rightTransfer').html(html);
            setRightPager();
            rightBtnDis();
        }

        function setRightPager() {
            $('#rightTotal').html(rightPager.total);
            $('#rightPageCount').html(rightPager.pageCount);
            $('#rightPageNo').html(rightPager.pageNo);
        }

        var personLay;
        layui.use(['form'], function () {
            var form = layui.form;
            personLay = layer.open({
                type: 1,
                title: '选择人员',
                closeBtn: 1,
                shadeClose: false,
                area: ['700px', '500px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    getLeftData();
                    getRightData();
                    var so = Sortable.create($('#personTransfer .rightTransfer')[0], {
                        sort: true,
                        dragClass: 'sortable-drag',
                        onEnd: function (evt) {
                            var newArr = so.toArray();
                            selData = [];
                            if (newArr) {
                                for (var i = 0; i < newArr.length; i++) {
                                    var newItem = Object.assign({}, rightData[newArr[i]]);
                                    selData.push(newItem);
                                }
                            }
                            setRightFiltData($('#rightSearch').val())
                        },
                    });
                },
            });
        });
        //数据穿梭
        $('#personTransfer .leftTransfer')
            .off('click', 'li')
            .on('click', 'li', function () {
                $(this).toggleClass('sel');
            });
        $('#personTransfer .rightTransfer')
            .off('click', 'li')
            .on('click', 'li', function () {
                $(this).toggleClass('sel');
            });
        $('#leftToRight')
            .off('click')
            .on('click', function () {
                var sel = $('#personTransfer .leftTransfer .sel');
                for (var i = 0; i < sel.length; i++) {
                    var code = $(sel[i]).attr('row');
                    var isSel = selData.findIndex(item => {
                        return item.usercode == code;
                    });
                    var index = leftData.findIndex(item => {
                        return item.usercode == code;
                    });
                    if (isSel == -1 || selData.length == 0) {
                        selData.push(leftData[index]);
                    }
                }
                $('#personTransfer .leftTransfer .sel').removeClass('sel');
                if (sel.length > 0) {
                    setRightFiltData($('#rightSearch').val());
                    rightPager.pageNo = 1;
                    getRightData();
                }
            });
        $('#leftToRightAll')
            .off('click')
            .on('click', function () {
                var sel = $('#personTransfer .leftTransfer li');
                for (var i = 0; i < sel.length; i++) {
                    var code = $(sel[i]).attr('row');
                    var isSel = selData.findIndex(item => {
                        return item.usercode == code;
                    });
                    if (isSel == -1 || selData.length == 0) {
                        selData.push(leftData[i]);
                    }
                }
                $('#personTransfer .leftTransfer .sel').removeClass('sel');
                if (sel.length > 0) {
                    setRightFiltData($('#rightSearch').val());
                    rightPager.pageNo = 1;
                    getRightData();
                }
            });
        //移除选中
        $('#rightToLeft')
            .off('click')
            .on('click', function () {
                var sel = $('#personTransfer .rightTransfer .sel');
                for (var i = sel.length - 1; i >= 0; i--) {
                    var code = $(sel[i]).attr('row');
                    for (var j = 0; j < selData.length; j++) {
                        if (selData[j].usercode == code) {
                            selData.splice(j, 1);
                            break;
                        }
                    }
                }
                $('#personTransfer .rightTransfer .sel').removeClass('sel');
                if (sel.length > 0) {
                    setRightFiltData($('#rightSearch').val());
                    rightPager.pageNo = 1;
                    getRightData();
                }
            });
        $('#rightToLeftAll')
            .off('click')
            .on('click', function () {
                var sel = $('#personTransfer .rightTransfer li');
                for (var i = sel.length - 1; i >= 0; i--) {
                    var code = $(sel[i]).attr('row');
                    for (var j = 0; j < selData.length; j++) {
                        if (selData[j].usercode == code) {
                            selData.splice(j, 1);
                            break;
                        }
                    }
                }
                if (sel.length > 0) {
                    rightPager.pageNo = 1;
                    setRightFiltData($('#rightSearch').val());
                    getRightData();
                }
            });

        $('#personTransfer')
            .off('click', '#save')
            .on('click', '#save', function () {
                opt.callback && opt.callback(selData);
                layer.close(personLay);
            });
        $('#personTransfer')
            .off('click', '#close')
            .on('click', '#close', function () {
                layer.close(personLay);
            });
        //数据翻页
        $('#personTransfer')
            .off('click', '#leftFirstPage')
            .on('click', '#leftFirstPage', function () {
                leftPager.pageNo = 1;
                getLeftData();
            });
        $('#personTransfer')
            .off('click', '#leftPrevPage')
            .on('click', '#leftPrevPage', function () {
                leftPager.pageNo--;
                getLeftData();
            });
        $('#personTransfer')
            .off('click', '#leftNextPage')
            .on('click', '#leftNextPage', function () {
                leftPager.pageNo++;
                getLeftData();
            });
        $('#personTransfer')
            .off('click', '#leftLastPage')
            .on('click', '#leftLastPage', function () {
                leftPager.pageNo = leftPager.pageCount;
                getLeftData();
            });

        function leftBtnDis() {
            $('#personTransfer .leftPage button').prop('disabled', false);
            if (leftPager.pageNo == 1) {
                $('#personTransfer #leftFirstPage').prop('disabled', true);
                $('#personTransfer #leftPrevPage').prop('disabled', true);
            }
            if (leftPager.pageNo == leftPager.pageCount) {
                $('#personTransfer #leftNextPage').prop('disabled', true);
                $('#personTransfer #leftLastPage').prop('disabled', true);
            }
            if (leftPager.pageCount == 0) {
                $('#personTransfer #leftFirstPage').prop('disabled', true);
                $('#personTransfer #leftPrevPage').prop('disabled', true);
                $('#personTransfer #leftNextPage').prop('disabled', true);
                $('#personTransfer #leftLastPage').prop('disabled', true);
            }
        }

        $('#personTransfer')
            .off('click', '#rightFirstPage')
            .on('click', '#rightFirstPage', function () {
                rightPager.pageNo = 1;
                getRightData(1);
                rightBtnDis();
            });
        $('#personTransfer')
            .off('click', '#rightPrevPage')
            .on('click', '#rightPrevPage', function () {
                rightPager.pageNo--;
                getRightData(rightPager.pageNo);
                rightBtnDis();
            });
        $('#personTransfer')
            .off('click', '#rightNextPage')
            .on('click', '#rightNextPage', function () {
                rightPager.pageNo++;
                getRightData(rightPager.pageNo);
                rightBtnDis();
            });
        $('#personTransfer')
            .off('click', '#rightLastPage')
            .on('click', '#rightLastPage', function () {
                rightPager.pageNo = rightPager.pageCount;
                getRightData(rightPager.pageNo);
                rightBtnDis();
            });

        function rightBtnDis() {
            $('#personTransfer .rightPage button').prop('disabled', false);
            if (rightPager.pageNo == 1) {
                $('#personTransfer #rightFirstPage').prop('disabled', true);
                $('#personTransfer #rightPrevPage').prop('disabled', true);
            }
            if (rightPager.pageNo == rightPager.pageCount) {
                $('#personTransfer #rightNextPage').prop('disabled', true);
                $('#personTransfer #rightLastPage').prop('disabled', true);
            }
            if (rightPager.pageCount == 0) {
                $('#personTransfer #rightFirstPage').prop('disabled', true);
                $('#personTransfer #rightPrevPage').prop('disabled', true);
                $('#personTransfer #rightNextPage').prop('disabled', true);
                $('#personTransfer #rightLastPage').prop('disabled', true);
            }
        }

        function setRightFiltData(inputValue) {
            var matcher = new RegExp(inputValue || '');
            var arr = $.grep(selData, function (value) {
                return matcher.test(value.username);
            });
            rightFiltData = arr;
        }

        $('#leftSearch')
            .off('input')
            .on(
                'input', 
                common.debounce(function () {
                    leftPager.condition = $(this).val();
                    leftPager.pageNo = 1;
                    getLeftData();
                }, 200)
            );
        $('#rightSearch')
            .off('input')
            .on(
                'input', 
                common.debounce(function () {
                    setRightFiltData($(this).val());
                    rightPager.pageNo = 1;
                    getRightData(rightPager.pageNo);
                }, 200)
            );
    };
});
