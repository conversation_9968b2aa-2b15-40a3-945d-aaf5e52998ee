'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form'], function () {
            var form = layui.form;
            var blockList = [];
            var portalElement = opt.data.portalElement || [];
            var htmls = '';
            for (var i = 0; i < portalElement.length; i++) {
                var item = {};
                if (portalElement[i].elementType == 6) {
                    var col = 3;
                    if (portalElement[i].elementColumn == 2) {
                        col = 6;
                    }
                    item = {
                        url: '/view/system/homePage/img/tempcon_' + portalElement[i].elementType + '_' + portalElement[i].elementColumn + '_' + portalElement[i].elementShow + '.png',
                        col: col,
                    };
                } else {
                    item = {
                        url: '/view/system/homePage/img/tempcon_' + portalElement[i].elementType + '_0.png',
                        col: 3,
                    };
                }
                htmls += ' <div class="layui-col-md' + item.col + '">\
                                <img src="' + item.url + '" alt="" />\
                            </div>';
            }

            var $dom = $(html);
            $dom.find('.layui-row').html(htmls);
            var wins = layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                maxmin: false,
                shadeClose: true,
                area: ['1024px', '565px'], //宽高
                content: $dom.html(),
                success: function (layero, index) {},
            });
        });
    };
});
