"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    //按钮更多  经过
    $('body').off('mouseenter', '#areaButtonMore,.areaButtonMoreBox').on('mouseenter', '#areaButtonMore,.areaButtonMoreBox', function () {
        $('.areaButtonMoreBox').show();
    })
    //按钮更多  移开
    $('body').off('mouseleave', '#areaButtonMore,.areaButtonMoreBox').on('mouseleave', '#areaButtonMore,.areaButtonMoreBox', function () {
        $('.areaButtonMoreBox').hide();
    })
    //模版页面脚本
    var perform = function () {
        layui.use(['form', 'layedit', 'upload', 'laydate', 'trasen'], function () {
            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload;
            laydate.render({
                elem: '#filingDateStarts'
            });
            laydate.render({
                elem: '#filingDateEnd'
            });

            var trasenTable = new $.trasenTable("acceptanceCheck-grid-table", {
                url: common.url + '/ts-hr/upgrade/log/list',
                colModel: [
                    { label:'id',name: 'upgradeLogId', index: 'upgrade_log_id', width: "auto", editable: false, hidden: true, sortable: true },
                    { label:'升级日期',name: 'upgradeDate', index: 'upgrade_date', width: 150, editable: false, sortable: true },
                    { label:'升级内容',name: 'remark', index: 'remark', width: 350, editable: false,  sortable: true },
                    { label:'创建人',name: 'createUserName', index: 'create_user', width: 80, editable: false, sortable: true },
                    { label:'创建时间',name: 'createDate', index: 'create_date', width: 145, editable: false, sortable: true },
                    { label:'修改人',name: 'updateUserName', index: 'update_user', width: 80, editable: false, sortable: true },
                    { label:'修改时间',name: 'updateDate', index: 'update_date', width: 145, editable: false, sortable: true },
                ],
                buidQueryParams: function () {
                    var search = $("#queryForm").serializeArray();
                    var opt = $("#screening").serializeArray();
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    if(data.productId==null || data.productId==undefined){
                        data.productId='';
                    }
                    return data;
                }

            });
            function refreshTable() {
                trasenTable.refresh();
            }
            $("#screeningBox").off("click", "#screenCennel").on("click", "#screenCennel", function () {
                $(".screeningBox").fadeOut(200);
            });
            $("body").off("click", "#screenCRest").on("click", "#screenCRest", function () {
                $("#productId").empty();
                $("#filingDateStarts").val("");
                $("#filingDateEnd").val("");
                refreshTable();
            });

            form.on('submit(screeningSub)', function (data) {
                refreshTable();
            });

            //查询
            form.on('submit(search)', function (data) {
                // $("#querystatus").val("");
                refreshTable();
                // document.getElementById("queryForm").reset();
                return false;
            });
            form.render('select', 'select');
            //事件
            $(function () {
                //新增
                $("body").off("click", '#upgradeLogAdd').on("click", "#upgradeLogAdd", function () {
                    layui.use(['form', 'laytpl'], function () {
                        var getTpl = upgradeLogHtml.innerHTML;
                        layer.open({
                            type: 1,
                            title: '新增',
                            closeBtn: 1,
                            shadeClose: false,
                            area: ['720px', '300px'],
                            skin: 'yourclass',
                            content: getTpl,
                            success: function (layero, index) {
                                laydate.render({
                                    elem: '#upgradeDate',
                                    value:new Date(),
                                });
                                form.render();
                            }
                        });
                    });
                });
                //删除
                $("body").off("click", "#upgradeLogDelete").on("click", "#upgradeLogDelete", function () {
                    var rowData = trasenTable.getSelectRowData();
                    if (rowData.upgradeLogId == null || rowData.upgradeLogId == undefined || rowData.upgradeLogId == '') {
                        layer.msg('请选中需要操作数据');
                        return false;
                    }
                    layer.confirm('确定要删除吗？', {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    }, function (index) {
                        $.ajax({
                            type: "post",
                            contentType: "application/json; charset=utf-8",
                            url: common.url + "/ts-hr/upgrade/log/delete/"+rowData['upgradeLogId'],
                            success: function (res) {
                                if (res.success) {
                                    trasenTable.refresh();
                                    layer.closeAll();
                                    layer.msg(res.message);
                                } else {
                                    layer.msg(res.message || '操作失败');
                                }
                            }
                        });
                    }, function () {
                    });
                });
                //编辑
                $("body").off("click", "#upgradeLogUpdate").on("click", "#upgradeLogUpdate", function () {
                    var rowData = trasenTable.getSelectRowData();
                    if (rowData.upgradeLogId == null || rowData.upgradeLogId == undefined || rowData.upgradeLogId == '') {
                        layer.msg('请选中需要操作数据');
                        return false;
                    }
                    var html = $("#upgradeLogHtml").html();
                    layer.open({
                        type: 1,
                        title: '编辑',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['720px', '300px'],
                        skin: 'yourclass',
                        content: html,
                        success: function (layero, index) {
                            laydate.render({
                                elem: '#upgradeDate',
                            });
                            trasen.setNamesVal(layero, rowData);
                            form.render();
                        }
                    });


                });
                //查看
                $("body").off("click", "#stockCheck").on("click", "#stockCheck", function () {
                    var rowid = $(this).attr('data-rowId');
                    var rowData = trasenTable.getRowData(rowid);
                    if (rowData.materielPublicityId == null || rowData.materielPublicityId == undefined || rowData.materielPublicityId == '') {
                        layer.msg('请选中需要操作数据');
                        return false;
                    }
                    var html = $("#stockHtml").html();
                    layer.open({
                        type: 1,
                        title: '查看详情',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['720px', '300px'],
                        skin: 'yourclass',
                        content: html,
                        success: function (layero, index) {
                            $.ajax({
                                type: "post",
                                contentType: "application/json; charset=utf-8",
                                url: common.url + "/ts-xs/materiel/publicity/findById/"+rowData['materielPublicityId'],
                                success: function (res) {
                                    if(res.success){
                                        var obj=res.object;
                                        $("#updateForm span").hide();
                                        trasen.setNamesVal(layero, obj);
                                        $("#updateForm input").attr("disabled","disabled");
                                        $("#updateForm textarea").attr("disabled","disabled");
                                        $("#stockSave").hide();
                                        form.render();
                                    }
                                }
                            });


                        }
                    });


                });
                $("#acceptanceScreenPeo button").on("click", function () {
                    $(this).addClass("active").siblings().removeClass("active");
                    $("#queryForm input[name='status']").val($(this).attr('data-value'));
                    refreshTable();
                })
                form.on('submit(upgradeLogSave)', function (data) {
                    $('[lay-filter="upgradeLogSaves"]').trigger('click');
                })
                // 防止重复提交
                var plidlock = false;
                // 表单提交
                form.on('submit(upgradeLogSaves)', function (data) {
                    var _url = null;
                    if (data.field.upgradeLogId) {
                        _url = common.url + "/ts-hr/upgrade/log/update";
                    } else {
                        _url = common.url + "/ts-hr/upgrade/log/save";
                    }
                    var _data = JSON.stringify(data.field);
                    if (!plidlock) {
                        plidlock = true;
                        $.ajax({
                            type: "post",
                            contentType: "application/json; charset=utf-8",
                            url: _url,
                            data: _data,
                            success: function (res) {
                                setTimeout(function () {
                                    plidlock = false;
                                },500)
                                if (res.success) {
                                    trasenTable.refresh();
                                    layer.closeAll();
                                    layer.msg(res.message);
                                } else {
                                    layer.msg(res.message || '操作失败');
                                }
                            }
                        });
                    }
                });
                //关闭layer
                $('body').off('click', '#close').on('click', '#close', function(){
                    layer.closeAll()
                });
               
            });
        });
    };

})
