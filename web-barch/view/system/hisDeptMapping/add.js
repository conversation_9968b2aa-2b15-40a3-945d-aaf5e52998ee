'use strict';
define(function(require, exports, module) {
  exports.init = function(opt, html) {
    layui.use(['form', 'laytpl', 'upload', 'laydate', 'trasen'], function() {
      let oaTree = null, // OA 科室树
        oaTreeData = [], // OA 科室树数据
        hisTree = null, // HIS 科室树 Node
        hisTreeData = [], // HIS 科室数据
        buttonLoading = false,
        deptMapList = [],
        isEdit = false, // 是否是编辑
        layero = layer.open({
          type: 1,
          title: opt.title || '员工档案新增',
          closeBtn: 1,
          maxmin: false,
          shadeClose: false,
          area: ['100%', '100%'], //宽高
          content: html,
          success: function(layero, index) {
            renderOADeptTree();
            renderHisDeptTree();
            if (opt.title == '编辑映射' && opt.data) {
              let addData = {
                ...opt.data,
                hisDeptList: opt.data.deptMappingChildList
              };
              delete addData.deptMappingChildList;
              deptMapList = [addData];
              renderMappingList('add', addData);
            }
          }
        });

      /**@desc 搜索框输入筛选树 */
      $('#OADeptAndHisDeptMappingModal [name="oaDeptSearch"]')
        .off('input')
        .on(
          'input',
          (function() {
            let timer = null;
            return function() {
              timer && clearTimeout(timer);
              timer = setTimeout(() => {
                let val = $(this).val();

                oaTree
                  ? $.fn.ztreeQueryHandler(oaTree, val)
                  : renderHisDeptTree();
              }, 500);
            };
          })()
        );
      $('#OADeptAndHisDeptMappingModal [name="hisDeptSearch"]')
        .off('input')
        .on(
          'input',
          (function() {
            let timer = null;
            return function() {
              timer && clearTimeout(timer);
              timer = setTimeout(() => {
                let val = $(this).val();

                hisTree
                  ? $.fn.ztreeQueryHandler(hisTree, val)
                  : renderOADeptTree();
              }, 500);
            };
          })()
        );

      /**@desc 滚动条渲染 */
      $.each(
        $('#OADeptAndHisDeptMappingModal [name="scrollContent"]'),
        function(index, dom) {
          $(dom).overlayScrollbars({});
        }
      );
      /**@desc 渲染 OA科室树 HIS科室树 */
      function renderOADeptTree() {
        $.ajax({
          method: 'get',
          url: '/ts-basics-bottom/organization/getTree',
          async: false,
          success: function(data) {
            oaTreeData = data.object;
            $.fn.zTree.init(
              $('#OADeptAndHisDeptMappingModal #oaMapModalDeptTreeContent'),
              {
                view: {
                  dblClickExpand: false,
                  showTitle: true
                },
                callback: {
                  onCheck: handleOADeptCheck
                },
                //勾选时，关联父，不关联子，取消时，关联父子
                check: {
                  enable: opt.title != '编辑映射',
                  autoCheckTrigger: true,
                  chkStyle: 'checkbox',
                  chkboxType: {
                    Y: '',
                    N: ''
                  }
                },
                data: {
                  simpleData: {
                    enable: true
                  }
                }
              },
              oaTreeData
            );
            //获取ztree对象
            oaTree = $.fn.zTree.getZTreeObj('oaMapModalDeptTreeContent');
            if (opt.title == '编辑映射') {
              let node = oaTree.getNodeByParam('id', opt.data.oaDeptId);
              oaTree.checkNode(node, true, false, false);
            }
          }
        });
      }
      function renderHisDeptTree() {
        $.ajax({
          method: 'get',
          url: '/ts-external/api/basOrgDepartment/getZTree',
          async: false,
          success: function(data) {
            hisTreeData = data.object;
            $.fn.zTree.init(
              $('#OADeptAndHisDeptMappingModal #hisMapModalDeptTreeContent'),
              {
                view: {
                  dblClickExpand: false,
                  showTitle: true
                },
                callback: {
                  onCheck: handleHisDeptCheck,
                  onNodeCreated: function(event, treeId, treeNode) {
                    if (treeNode.sort == 1) {
                      $(
                        `#OADeptAndHisDeptMappingModal #hisMapModalDeptTreeContent #${treeNode.tId}`
                      ).addClass('is-mapped');
                    }
                  }
                },
                //勾选时，关联父，不关联子，取消时，关联父子
                check: {
                  enable: true,
                  autoCheckTrigger: true,
                  chkStyle: 'checkbox',
                  chkboxType: {
                    Y: '',
                    N: ''
                  }
                },
                data: {
                  simpleData: {
                    enable: true
                  }
                }
              },
              hisTreeData
            );
            //获取ztree对象
            hisTree = $.fn.zTree.getZTreeObj('hisMapModalDeptTreeContent');
          }
        });
      }

      function handleOADeptCheck(event, treeId, treeItem) {
        let { checked, tId } = treeItem || {},
          data = oaTree.getNodeByTId(tId),
          { id: oaDeptId, name: oaDeptName } = data,
          activeDom = $(
            '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item.active'
          );

        if (checked) {
          let unsetOaDeptIndex = deptMapList.findIndex(item => !item.oaDeptId),
            addItem = {
              oaDeptId,
              oaDeptName
            };
          if (!activeDom.length) {
            if (unsetOaDeptIndex >= 0) {
              $(
                `#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item:nth-child(${unsetOaDeptIndex})`
              ).click();
              let actionData = deptMapList[unsetOaDeptIndex];
              Object.assign(actionData, addItem);
              renderMappingList('update', actionData, unsetOaDeptIndex + 2);
            } else {
              deptMapList.push(addItem);
              renderMappingList('add', addItem);
            }
          } else {
            let activeIndex =
              $(
                '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item'
              ).index(activeDom) - 1;
            if (deptMapList[activeIndex] && deptMapList[activeIndex].oaDeptId) {
              deptMapList.push(addItem);
              renderMappingList('add', addItem);
            } else {
              let actionData = deptMapList[activeIndex];
              Object.assign(actionData, addItem);
              renderMappingList('update', actionData, activeIndex + 2);
            }
          }
        } else {
          let mapItemIndex = deptMapList.findIndex(
            item => item.oaDeptId == oaDeptId
          );
          if (mapItemIndex >= 0) {
            let actionData = deptMapList[mapItemIndex];
            delete actionData.oaDeptId;
            delete actionData.oaDeptName;
            if (actionData.hisDeptList && actionData.hisDeptList.length) {
              renderMappingList('update', actionData, mapItemIndex + 2);
            } else {
              deptMapList.splice(mapItemIndex, 1);
              renderMappingList('delete', {}, mapItemIndex + 2);
            }
          }
        }
      }
      function handleHisDeptCheck(event, treeId, treeItem) {
        let { checked, tId } = treeItem || {},
          data = hisTree.getNodeByTId(tId),
          { id: hisDeptId = '', name: hisDeptName } = data,
          activeDom = $(
            '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item.active'
          );
        hisDeptId = String(hisDeptId);

        if (checked) {
          let unsetHisDeptIndex = deptMapList.findIndex(
              item =>
                !item.hisDeptList ||
                (item.hisDeptList && !item.hisDeptList.length)
            ),
            addItem = {
              hisDeptId,
              hisDeptName
            };
          if (!activeDom.length) {
            if (unsetHisDeptIndex == -1) {
              if (!deptMapList.length) {
                let actionData = {
                  hisDeptList: [addItem]
                };
                deptMapList.push(actionData);
                renderMappingList('add', actionData);
                return;
              }
              unsetHisDeptIndex = deptMapList.length - 1;
            }
            $(
              `#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item:nth-child(${unsetHisDeptIndex +
                2})`
            ).click();
            let actionData = deptMapList[unsetHisDeptIndex];
            actionData.hisDeptList
              ? actionData.hisDeptList.push(addItem)
              : (actionData.hisDeptList = [addItem]);
            renderMappingList('update', actionData, unsetHisDeptIndex + 2);
          } else {
            let activeIndex =
                $(
                  '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item'
                ).index(activeDom) - 1,
              actionData = deptMapList[activeIndex];
            if (actionData) {
              actionData.hisDeptList
                ? actionData.hisDeptList.push(addItem)
                : (actionData.hisDeptList = [addItem]);
              renderMappingList('update', actionData, activeIndex + 2);
            }
          }
        } else {
          if (activeDom.length) {
            let activeIndex =
                $(
                  '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item'
                ).index(activeDom) - 1,
              actionData = deptMapList[activeIndex];
            if (actionData) {
              let deleteIndex = (actionData.hisDeptList || []).findIndex(
                dept => dept.hisDeptId == hisDeptId
              );
              if (deleteIndex >= 0) {
                actionData.hisDeptList.splice(deleteIndex, 1);
                if (!actionData.oaDeptId && !actionData.hisDeptList.length) {
                  renderMappingList('delete', actionData, activeIndex + 2);
                } else {
                  renderMappingList('update', actionData, activeIndex + 2);
                }
              }
            }
          }
        }
      }

      function renderMappingList(type, data, actionIndex) {
        let { oaDeptName = '', hisDeptList = [] } = data,
          node = '',
          content = $('#OADeptAndHisDeptMappingModal [name="mapping-list"]'),
          hisDeptNodes = hisDeptList
            .map(item => `<div class="his-dept-item">${item.hisDeptName}</div>`)
            .join('');
        switch (type) {
          case 'add':
            var deleteIcon =
              opt.title == '编辑映射'
                ? ''
                : `
            <i
              name="deleteMappingBtn"
              class="oaicon oa-icon-close"
              style="cursor: pointer;"
            ></i>
            `;
            node = `<li class="mapping-list-item">
              <div class="oa-dept-name">${oaDeptName}</div>
              <div class="his-dept-content">
                ${hisDeptNodes}
              </div>
              <div class="delete-column">
                ${deleteIcon}
              </div>
            </li>`;
            node = $(node);
            content.append(node);
            node.off('click').on('click', function() {
              if ($(this).hasClass('active')) {
                return;
              }
              let checkedNodes = hisTree.getCheckedNodes(true) || [];
              checkedNodes.map(treeNode =>
                hisTree.checkNode(treeNode, false, false, false)
              );
              $(
                '#OADeptAndHisDeptMappingModal [name="mapping-list"] li'
              ).removeClass('active');
              $(this).addClass('active');
              let activeIndex =
                  $(
                    '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item'
                  ).index(this) - 1,
                activeData = deptMapList[activeIndex] || {};
              (activeData.hisDeptList || []).map(item => {
                let treeNode = hisTree.getNodeByParam('id', item.hisDeptId);
                treeNode && hisTree.checkNode(treeNode, true, false, false);
              });
            });
            $('[name="deleteMappingBtn"]', node)
              .off('click')
              .on('click', function() {
                let parent = $(this).closest('li');
                if (!parent) {
                  return;
                }
                let deleteIndex =
                    $(
                      '#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item'
                    ).index(parent) - 1,
                  deleteData = deptMapList[deleteIndex],
                  oaTreeNode = null;
                if (deleteData && deleteData.oaDeptId) {
                  oaTreeNode = oaTree.getNodeByParam('id', deleteData.oaDeptId);
                }
                deleteIndex >= 0 && deptMapList.splice(deleteIndex, 1);
                oaTreeNode && oaTree.checkNode(oaTreeNode, false, false, false);
                parent.remove();
              });
            node.click();
            break;
          case 'update':
            node = $(`.mapping-list-item:nth-child(${actionIndex})`, content);
            if (node.length && actionIndex >= 2) {
              let oaDeptNameNode = $('.oa-dept-name', node);
              if (oaDeptName != oaDeptNameNode[0].innerText) {
                oaDeptNameNode[0].innerText = oaDeptName;
              }

              $('.his-dept-content', node).empty();
              $('.his-dept-content', node).append(hisDeptNodes);
            }
            break;
          case 'delete':
            if (actionIndex >= 2) {
              node = $(`.mapping-list-item:nth-child(${actionIndex})`, content);
              node.length && node.remove();
            }
            break;
          default:
            break;
        }
      }

      /**@desc 保存 */
      $('#OADeptAndHisDeptMappingModal #save')
        .off('click')
        .on('click', function() {
          if (!deptMapList.length) {
            layer.msg('请至少添加一条映射关系');
            return;
          }
          let unsetItemIndex = deptMapList.findIndex(
            item =>
              !item.oaDeptId || !item.hisDeptList || !item.hisDeptList.length
          );
          if (unsetItemIndex >= 0) {
            $(
              `#OADeptAndHisDeptMappingModal [name="mapping-list"] .mapping-list-item:nth-child(${unsetItemIndex +
                2})`
            ).click();
            layer.msg('请确保映射关系完整');
            return;
          }
          if (buttonLoading) {
            return;
          }
          let data = deptMapList.map(item => {
            let newItem = {
              ...item,
              deptMappingChildList: item.hisDeptList
            };
            delete newItem.hisDeptList;
            return newItem;
          });
          if (opt.title == '编辑映射') {
            data = data[0];
          }
          buttonLoading = true;
          $.ajax({
            url:
              opt.title == '编辑映射'
                ? '/ts-external/api/deptMapping/update'
                : '/ts-external/api/deptMapping/save',
            method: 'post',
            data: JSON.stringify(data),
            contentType: 'application/json; charset=utf-8',
            async: false,
            success: function(res) {
              buttonLoading = false;
              let defaultToast = opt.title == '编辑映射' ? '编辑' : '新增';
              if (!res.success) {
                layer.msg(res.message || defaultToast + '失败');
                return;
              }
              layer.msg(defaultToast + '成功');
              layer.close(layero);
              opt.ref();
            }
          });
        });

      /**@desc 关闭 */
      $('#OADeptAndHisDeptMappingModal #close')
        .off('click')
        .on('click', function() {
          layer.close(layero);
        });
    });
  };
});
