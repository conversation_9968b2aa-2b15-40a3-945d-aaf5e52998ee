'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'treeSelect', 'zTreeSearch'], function () {
            var form = layui.form,
                trasen = layui.trasen,
                laydate = layui.laydate,
                zTreeSearch = layui.zTreeSearch;

            var upList = [];
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['790px', '450px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        upList = opt.data.jobtitleBasicUpgradeListRespList || [];
                        trasen.setNamesVal(layero, opt.data);
                        setUplist();
                    }

                    var classId = '<option value="">请选择职称类别</option>';
                    var classId_url = common.url + '/ts-basics-bottom/jobtitleBasic/getList';
                    $.ajax({
                        type: 'post',
                        contentType: 'application/json; charset=utf-8',
                        url: classId_url,
                        data: JSON.stringify({ eqJobtitleBasicPid: 0 }),
                        success: function (res) {
                            if (res.object != null) {
                                $.each(res.object, function (i, v) {
                                    classId += '<option value="' + v.jobtitleBasicId + '">' + v.jobtitleBasicName + '</option>';
                                });
                                $('#classId').html(classId);
                                if (opt.data) {
                                    trasen.setNamesVal(layero, opt.data);
                                }

                                if (opt.data) {
                                    var levelId = '<option value="">请选择职称等级</option>';
                                    var levelId_url = common.url + '/ts-basics-bottom/jobtitleBasic/getList';
                                    $.ajax({
                                        type: 'post',
                                        contentType: 'application/json; charset=utf-8',
                                        url: levelId_url,
                                        data: JSON.stringify({ eqJobtitleBasicPid: opt.data.classId }),
                                        success: function (res) {
                                            if (res.object != null) {
                                                $.each(res.object, function (i, v) {
                                                    levelId += '<option value="' + v.jobtitleBasicId + '">' + v.jobtitleBasicName + '</option>';
                                                });
                                                $('#levelId').html(levelId);
                                                if (opt.data) {
                                                    trasen.setNamesVal(layero, opt.data);
                                                }
                                                form.render('select');
                                            }
                                        },
                                    });
                                } else {
                                    form.render('select');
                                }

                                // form.render('select');
                            }
                        },
                    });

                    function setUplist() {
                        $('#upListBox').html('');
                        commonDictCache.getDict('education_type', function (arr) {
                            var education_type = arr;
                            var education_str = '<option value="">请选择学历</option>';
                            $.each(education_type, function (i, v) {
                                education_str += '<option value="' + v.itemCode + '">' + v.itemName + '</option>';
                            });
                            for (var i = 0; i < upList.length; i++) {
                                var item = $('<div class="up-list-item"></div>');
                                var del = $('<i class="oaicon oa-icon-jinzhite-circle-xian required list-del fr"></i>');
                                var edu = $('<div class="layui-inline"> <select lay-verify="required" lay-search="" name="educationTypeCode"> </select> </div>');
                                edu.find('select').html(education_str);
                                var time = $('<div class="layui-inline" style="width: 60px"><input type="text" autocomplete="off" maxlength="50" lay-verify="required" placeholder="" name="upgradeTime" class="layui-input" /> </div>');
                                var pgradeJob = $('<div class="layui-inline"><input type="text" lay-verify="required" name="upgradeJobtitleBasicLable" class="layui-input" /><input type="hidden" lay-verify="required" name="upgradeJobtitleBasicId" class="layui-input" /> </div>');
                                item.append(del);
                                item.append(edu);
                                item.append(' ，评定当前职称满 ');
                                item.append(time);
                                item.append('  年，可晋升 ');
                                item.append(pgradeJob);
                                $('#upListBox').append(item);
                                trasen.setNamesVal(item, upList[i]);
                            }
                            form.render('select');
                        });
                    }
                    function setUpItem() {
                        var l = $('#upListBox .up-list-item');
                        for (var i = 0; i < l.length; i++) {
                            upList[i] = trasen.getNamesVal($(l[i]));
                        }
                    }
                    $('#addUpItem')
                        .off('click')
                        .on('click', function () {
                            setUpItem();
                            upList.push({
                                educationTypeCode: '',
                                educationTypeCodeLable: '',
                                jobtitleBasicId: '',
                                upgradeJobtitleBasicId: '',
                                upgradeJobtitleBasicLable: '',
                                upgradeTime: '',
                            });
                            setUplist();
                        });
                    $('#jobtitleBasicAddForm')
                        .off('mousedown', ' [name="upgradeJobtitleBasicLable"]')
                        .on('mousedown', ' [name="upgradeJobtitleBasicLable"]', function (e) {
                            e.stopPropagation();
                            if ($(this).attr('id') == 'upgradeJobtitleBasicLable') {
                            } else {
                                $('#jobtitleBasicAddForm #upgradeJobtitleBasicLable').removeAttr('id');
                                $(this).attr('id', 'upgradeJobtitleBasicLable');
                            }
                            // $(this).trigger('click');
                        });
                    $('#jobtitleBasicAddForm')
                        .off('click', '.list-del')
                        .on('click', '.list-del', function (e) {
                            e.stopPropagation();
                            var i = $(this).closest('.up-list-item').index();
                            upList.splice(i, 1);
                            $(this).closest('.up-list-item').remove();
                        });
                    // //升级职称
                    zTreeSearch.init('#jobtitleBasicAddForm #upgradeJobtitleBasicLable', {
                        url: common.url + '/ts-basics-bottom/jobtitleBasic/getJobtitleTree',
                        type: 'post',
                        checkbox: false,
                        condition: 'name',
                        zTreeOnClick: function (treeId, treeNode) {
                            if (!treeNode.children || !treeNode.children.length) {
                                $('#jobtitleBasicAddForm #upgradeJobtitleBasicLable').val(treeNode.name);
                                $('#jobtitleBasicAddForm #upgradeJobtitleBasicLable').next().val(treeNode.id);
                                return true;
                            } else {
                                return false;
                            }
                        },
                    });

                    form.on('submit(jobtitleBasicAddSub)', function (data) {
                        setUpItem();
                        var _url = common.url + '/ts-basics-bottom/jobtitleBasic/add';
                        if (opt.data) {
                            _url = common.url + '/ts-basics-bottom/jobtitleBasic/update';
                        }
                        data.field.jobtitleBasicUpgradeList = upList;
                        var _data = JSON.stringify(data.field);
                        $.ajax({
                            type: 'post',
                            contentType: 'application/json; charset=utf-8',
                            url: _url,
                            data: _data,
                            success: function (res) {
                                if (res.success) {
                                    layer.closeAll();
                                    layer.msg('保存成功！');
                                    opt.ref && opt.ref();
                                    opt.refT && opt.refT();
                                } else {
                                    layer.msg(res.message || '操作失败');
                                }
                            },
                        });
                        return false;
                    });

                    form.on('select(classId)', function (data) {
                        var levelId = '<option value="">请选择职称等级</option>';
                        var levelId_url = common.url + '/ts-basics-bottom/jobtitleBasic/getList';
                        // 岗位类别
                        $.ajax({
                            type: 'post',
                            contentType: 'application/json; charset=utf-8',
                            url: levelId_url,
                            data: JSON.stringify({ eqJobtitleBasicPid: data.value }),
                            success: function (res) {
                                if (res.object != null) {
                                    $.each(res.object, function (i, v) {
                                        levelId += '<option value="' + v.jobtitleBasicId + '">' + v.jobtitleBasicName + '</option>';
                                    });
                                    $('#levelId').html(levelId);
                                    form.render('select');
                                }
                            },
                        });
                    });

                    $('#jobtitleBasicAddHtml #jobtitleBasicClose')
                        .off('click')
                        .on('click', function () {
                            layer.closeAll();
                        });
                    $('#jobtitleBasicAddHtml #save')
                        .off('click')
                        .on('click', function () {
                            $('[lay-filter="jobtitleBasicAddSub"]').trigger('click');
                            return false;
                        });
                },
            });
        });
    };
});
