<style type="text/css">
    .mgl5 {
        margin-left: 2px;
    }
</style>
<div  id="systemProjectPlanTemplate">
    <div class="button-area">
        <form id="systemProjectPlanTemplatequeryForm" class="layui-form fl">
            <div class="layui-inline">
                <input type="text" name="search" placeholder="请输入关键字查询" autocomplete="off"
                       class="layui-input edi-layui-input searchInput"
                       id="search-val" search-input="search">
            </div>

            <div class="layui-inline mgl5">
                <label class="layui-inline">计划类型</label>
                <div class="layui-inline">
                    <select name="planType" id="planTypeSelecte" lay-filter="systemProjectPlanTemplateplanTypeSelecte" lay-search>
                        <option value="">--全部--</option>
                        <option value="1">实施类</option>
                        <option value="2">研发类</option>
                        <option value="3">接口类</option>
                        <option value="4">维护类</option>
                    </select>
                </div>
            </div>

            <button type="button" class="layui-btn edi-button mgl5 layui-btn-normal edi-button-blue" lay-submit=""
                    lay-filter="systemProjectPlanTemplatesearch" search-input="button">
                搜索
            </button>
        </form>
        <div class="pubBtnBox areaButtonBoxR">
            <button type="button" class="" id="addPlanTemplateCfg" data-permission="on">新增</button>
            <button type="button" class="editor" id="planTemplateCfgEditor" data-permission="on">编辑</button>
            <button type="button" class="enable" id="updateStatusBut" data-permission="on">修改状态</button>
            <button type="button" class="enable" id="copyTemplateBut" data-permission="on">复制模板</button>
        </div>
    </div>
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table-systemProjectPlanTemplate"></table>
            <!-- 分页 -->
            <div id="grid-pager-systemProjectPlanTemplate"></div>
        </div>
    </div>
</div>

<script type="text/html" id="systemProjectPlanTemplateAddHtml">
    <div id="systemProjectPlanTemplateAddHtmlDiv">
        <div class="archivesTab-con">
            <form class="layui-form" id="addPlanTemplateCfgForm">
                <input type="hidden" name="planTemplateId" id="planTemplateId">
                <input type="hidden" name="productName" id="productName">
                <div class="layui-col-md12">
                    <div class="layui-col-md6">
                        <label class="lay-label-title"><span>*</span>计划类型</label>
                        <div class="lay-input-box">
                            <select class="layui-input" id="planType" name="planType" lay-verify="required" lay-search>
                                <option value="1">实施类</option>
                                <option value="2">研发类</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <label class="lay-label-title"><span class="required">*</span>所属产品</label>
                        <div class="lay-input-box" id="productIdCheckDiv">
                            <select class="layui-input" name="productId" id="productId" lay-verify="required" lay-search>
                                <option id="initproductName">请输入产品名称查询</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <label class="lay-label-title"><span class="required">*</span>是否启用</label>
                        <div class="lay-input-box">
                            <select class="layui-input" id="isValid" name="isValid" lay-verify="required" lay-search>
                                <option value="1">是</option>
                                <option value="2">否</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <label class="lay-label-title"><span class="required">*</span>模板名称</label>
                        <div class="lay-input-box">
                            <input type="tex" name="templateName" id="templateName" lay-verify="required"
                                   autocomplete="off"
                                   class="layui-input layInput" placeholder="请输入模板名称">
                        </div>
                    </div>

                    <div class="layui-col-md3">
                        <label class="lay-label-title">进度占比合计</label>
                        <div class="lay-input-box" id="totalratio" style="line-height:30px;">
                            0
                        </div>
                    </div>

                </div>
                <button type="button" class="none" lay-submit="" lay-filter="systemProjectPlanTemplateAddHtmlDivformSavePlanTemplateCfgSend"></button>
            </form>
            <div class="layui-col-md12 childMother">
                <div class="childMother-left">
                    <button type="button" class="layui-btn layui-btn-xs fr" id="motherTableAdd">新增</button>
                    <div class="clear"></div>
                    <div class="table-box self-motion-table">
                        <table id="table-modMain"></table>
                    </div>
                </div>
                <div class="childMother-right">
                    <button type="button" class="layui-btn layui-btn-xs fr" id="sonTableAdd">新增</button>
                    <div class="clear"></div>
                    <div class="table-box self-motion-table">
                        <table id="table-modSon"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layer_btn" id="addPlanTemplateCfgBtn" lay-submit=""
                    lay-filter="systemProjectPlanTemplateAddHtmlDivformSavePlanTemplateCfg">保存
            </button>
            <button type="button" class="layer_btn layer_back" id="previewPlanTemplateCfgBtn"  >预览
            </button>
            <a href="javascript:;" class="layer_btn layer_back" id="cancel">关闭</a>
        </div>
    </div>

</script>

<!-- 预览 -->
<script type="text/html" id="previewPlanTemplateCfgDiv">
    <div class="archivesTab-con" id="previewPlanTemplateCfgDivDiv">
        <div class="previewPlanTemplateYlnnn">{{d.mb}}计划模板</div>
        <div class="previewPlanTemplateYlttt row">
            <span>计划类型：{{d.planTypename}}</span>
            <span>所属产品：{{d.productname}}</span>
        </div>
        <div class="previewPlanTemplateTableYl th">
            <span class="box">阶段名称</span>
            <span class="box">阶段成果物</span>
            <span class="box">阶段占比</span>
            <span class="box">是否核算产值</span>
            <span class="box">核算比例</span>
        </div>
        {{# layui.each(d.list, function(index, row){ }}
        <div class="previewPlanTemplateTableYl bg">
            <span class="box">{{row.stageName}}</span>
            <span class="box"></span>
            <span class="box">{{row.ratio}}</span>
            <span class="box"></span>
            <span class="box"></span>
        </div>
        {{# layui.each(row.outputList, function(index, item){ }}
        <div class="previewPlanTemplateTableYl tdb">
            <span class="box"></span>
            <span class="box tl">{{item.stageOutputName}}</span>
            <span class="box"></span>
            <span class="box">{{item.isCalcName}}</span>
            <span class="box">
                {{#  if(item.isCalc == 1){ }}
                    {{item.ratio}}
                {{#  } }}  
            </span>
        </div>
        {{# }); }}
        {{# }); }}
    </div>
</script>