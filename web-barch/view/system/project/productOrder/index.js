"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }

    module.exports = {
        init: init
    }

    //模版页面脚本
    var perform = function () {
        layui.use(['form', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                trasen = layui.trasen;

            var lastSel;
            var trasenTable = new $.trasenTable("grid-table-systemProjectProductOrder", {
                pager:'grid-pager-systemProjectProductOrder',
                url: common.url + '/ts-base-data/product/info/list',
                rowNum: 200,
                cellEdit: true,
                cellsubmit: 'remote',
                cellurl: common.url + '/ts-base-data/product/table/edit/update',
                onCellSelect: function (rowId) {
                    if (rowId !== lastSel) {
                        trasenTable.oTable.saveRow(lastSel, true, trasenTable.oTable[0].p.cellurl, {
                            productInfoId: trasenTable.getRowData(rowId)['productInfoId']
                        });
                        lastSel = rowId;
                    }
                    trasenTable.editRowT(rowId);
                    return true;
                },
                beforeSubmitCell: function (rowId, cellName, value, iRow, iCol) {
                    var rowData = trasenTable.getRowData(rowId);
                    return { productInfoId: rowData['productInfoId'], seqNo: rowData['seqNo'] }
                },
                afterSubmitCell: function (resp, rowId, cellName, value, iRow, iCol) {
                    if (resp.success) {
                        return [true, ''];
                    } else {
                        return [false, resp.message || '保存失败'];
                    }
                },
                colModel: [
                    { label: 'ID', name: 'productInfoId', index: 'product_info_id', width: "auto", align: "center", editable: false, hidden: true },
                    { label: '产品名称', name: 'productName', index: 'product_name', width: 200, editable: false },
                    { label: '实施顺序', name: 'seqNo', index: 'seq_no', width: 120, editable: true },
                ],
                queryFormId: 'systemProjectProductOrderqueryForm'
            });

            // 点击表格外面保存可编辑表格数据
            $('body').bind('click', function (e) {
                if (lastSel != "") {
                    if ($(e.target).closest('#grid-table-systemProjectProductOrder').length == 0) {
                        trasenTable.oTable.saveRow(lastSel, true, trasenTable.oTable[0].p.cellurl, {
                            productInfoId: trasenTable.getRowData(lastSel)['productInfoId']
                        });
                        trasenTable.oTable.resetSelection();
                        lastSel = "";
                    }
                }
            });

            function refreshTable() {
                trasenTable.refresh();
            }
            
            form.on('submit(systemProjectProductOrdersearch)', function (data) {
                refreshTable()
            });

            $('#systemProjectProductOrder').off('click', '#saveSX').on('click', '#saveSX', function () {
                var rowid = trasenTable.getSelectRowId();
                $('#systemProjectProductOrder [id="' + rowid + '"] td').eq(0).trigger('click');
            })

        });
    };

})

