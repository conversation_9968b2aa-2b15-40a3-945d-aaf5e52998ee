<style>
    .formField {
        padding-left: 20px;
        user-select: none;
    }
    .formField li {
        line-height: 26px;
        cursor: default;
        display: flex;
        margin: 3px 0;
    }
    .formField li .flex_1 {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .formField li .fieldDetail {
        padding: 0 5px;
        border-radius: 10px;
    }
    #formulaEdit .layui-colla-title {
        line-height: 28px;
        height: 28px;
    }
    #formulaEdit .layui-colla-content {
        padding: 5px 15px;
    }
    #formulaEdit #func {
        user-select: none;
    }
    .describe {
        line-height: 26px;
        color: #666;
    }
    .code {
        color: #781287;
    }
    .funcType {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
</style>
<div id="formulaEdit">
    <div class="layui-content-box" style="padding: 5px">
        <div>
            <p style="line-height: 20px; color: red">所有字符必须使用英文字符(参数之间请用英文逗号 , 隔开，字符串请用英文 "" 包起来 )</p>
        </div>
        <div style="height: 180px">
            <textarea id="formulaEditArea"></textarea>
        </div>
        <div class="trasen-con-box" style="top: 210px; left: 5px; right: 5px">
            <div class="fl" style="width: 220px; height: 100%; overflow-y: auto; margin-right: 10px">
                <p style="line-height: 26px; color: #999">可用变量</p>
                <div style="border: 1px solid #ccc; height: calc(100% - 30px)">
                    <div class="layui-form-item" style="margin: 0; border-bottom: 1px solid #ccc">
                        <label class="layui-form-label" style="width: auto; padding: 5px"><i class="layui-icon layui-icon-search"></i></label>
                        <div class="layui-input-block" style="margin-left: 28px">
                            <input type="text" class="layui-input" name="fieldName" placeholder="搜索变量" style="padding: 0; border: none !important" />
                        </div>
                    </div>
                    <div style="height: calc(100% - 33px); padding: 5px; box-sizing: border-box; overflow: auto">
                        <h4 style="font-weight: 700">表单字段</h4>
                        <ul class="formField" id="formFields"></ul>
                    </div>
                </div>
            </div>
            <div class="fl" style="width: 160px; height: 100%; overflow-y: auto; margin-right: 10px">
                <p style="line-height: 26px; color: #999">函数</p>
                <div style="border: 1px solid #ccc; min-height: calc(100% - 30px)">
                    <div class="layui-form-item" style="margin: 0; border-bottom: 1px solid #ccc">
                        <label class="layui-form-label" style="width: auto; padding: 5px"><i class="layui-icon layui-icon-search"></i></label>
                        <div class="layui-input-block" style="margin-left: 28px">
                            <input type="text" class="layui-input" name="funName" placeholder="搜索函数" style="padding: 0; border: none !important" />
                        </div>
                    </div>
                    <div style="height: calc(100% - 33px)">
                        <ul id="func"></ul>
                    </div>
                </div>
            </div>
            <div style="overflow: hidden; height: 100%; overflow-y: auto">
                <div style="border: 1px solid #ccc; height: calc(100% - 30px); margin-top: 25px">
                    <div class="layui-form-item" style="margin: 0; border-bottom: 1px solid #ccc; height: 30px">
                        <label class="layui-form-label" style="width: auto; padding: 5px" id="funName">函数说明</label>
                    </div>
                    <div style="height: calc(100% - 33px); padding: 5px; box-sizing: border-box" id="funDetails">
                        <p class="describe">单击函数可在此处查看函数具体用法</p>
                        <p class="describe">双击函数可以将函数插入到编辑器中</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" id="save">确定</button>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="close">关闭</a>
    </div>
</div>
