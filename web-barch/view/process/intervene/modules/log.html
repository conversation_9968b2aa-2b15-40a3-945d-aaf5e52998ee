<style>
  #wfDesignLog .oa-nav-search .layui-inline {
    vertical-align: top;
  }
  #wfDesignLog .oa-nav-search .layui-form-label {
    padding: 5px 10px;
  }
  #wfDesignLog .tableBox {
    width: 100%;
    flex: 1;
    position: relative;
  }
  #moveDialog .layui-layer-content .layui-form .layui-form-item .device-uphold-info-box {
    width: 280px;
  }
  #moveDialog .layui-layer-content .label-text{
    width: 120px;
    line-height: 30px;
    margin-right: 4px;
  }
  #wfDesignLog .ui-jqgrid tr.jqgrow td {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
  .ui-state-highlight a,
  .ui-widget-content .ui-state-highlight a,
  .ui-widget-header .ui-state-highlight a
  #wfDesignLog .ui-state-hover a,
  #wfDesignLog .ui-state-hover a:link,
  #wfDesignLog .child-form-table .preview {
    color: #5260ff;
  }
  #wfDesignLog .btns{
    cursor: pointer;
    color: #5260ff;
    margin-left: 10px;
  }
</style>
<div id="wfDesignLog">
  <div class="layui-content-box" style="overflow: auto; padding: 8px">
    <div
      class="layui-tab-content"
      style="
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        padding: 0;
        display: flex;
        flex-direction: column;
      "
    >
      <div class="trasen-con-box">
        <div class="table-box">
          <table id="wfDesignLog_ZB"></table>
          <div id="wfDesignLog_ZB_pager"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="layer-btn archivesTabBtn">
    <a href="javascript:;" class="layui-btn layui-btn-primary" id="close"
      >关闭</a
    >
  </div>
</div>
