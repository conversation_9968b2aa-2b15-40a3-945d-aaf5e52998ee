"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    var API = {
      faultCommonSave :'/ts-workflow/workflow/instance/intervene/skip',
      getNodeList: '/ts-workflow/workflow/instance/intervene/skipStep'
    },
      wfPermissionsList = [];
    layui.use(['form', 'trasen', 'zTreeSearch', 'treeSelect'], function () {
      var form = layui.form,
        trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch;
      layer.open({
        type: 1,
        title: opt.title,
        closeBtn: 1,
        shadeClose: false,
        area: '500px',
        skin: 'yourclass',
        content: html,
        success: async function (layero, index) {
            await getNodeList();
            bindBtnEvent();
              //人员选择
            $("#operation .userTransfer")
            .off("click")
            .on("click", function () {
              var users = filteSelData(
                wfPermissionsList,
              );
              var arr = [];
              for (var i = 0; i < users.length; i++) {
                arr.push({
                  name: users[i].username,
                  code: users[i].usercode,
                });
              }
              wfPermissionsList = [];
              $.quoteFun("/commonPage/userDeptGroup/index", {
                data: {
                  user: true,
                  dept: true,
                  userList: arr,
                },
                callBack: function (list, seluser, dept) {
                  var data = [];
                  for (var i = 0; i < seluser.length; i++) {
                    data.push({
                      username: seluser[i].name,
                      usercode: seluser[i].code,
                    });
                  }
                  for (var i = 0; i < data.length; i++) {
                    wfPermissionsList.push({
                      userName: data[i].username,
                      userId: data[i].usercode,
                    });
                  }
                  setPermissionsText();
                },
              });
            });
        }
      });
      form.render();
    })
    
    //按钮点击事件
    function bindBtnEvent(){
        let form =layui.form;
        //确认添加事件
        form.on('submit(deviceUpholdConfirm)',(data)=>{
            let postData = {
              optRemark:data.field.optRemark,
              currentStepName:data.field.wfStepName,
              currentStepNo:data.field.wfStepId,
              assigneeNo:data.field.usercodes,
              assigneeName:data.field.usernames,
              wfInstanceId: opt.data.wfInstanceId,
            }
            $.ajax({
                type: "POST",
                url: API.faultCommonSave,
                data: JSON.stringify(postData),
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if(res.success){
                        opt.ref();
                        layer.closeAll();
                        layer.msg('流程跳过成功');
                    }
                    else{
                        layer.msg(res.message||"流程跳过失败");
                    }
                }
            });
        });

        //绑定取消按钮点击事件
        $('#deviceUpholdCancel').click((e)=>{
            layer.closeAll();
        });
    }

    //获取节点信息
    function getNodeList(){
      let queryData = {
        wfDefinitionId: opt.data.wfDefinitionId,
        launchDeptCode: opt.data.launchDeptCode,
        createUser: opt.data.createUser,
        wfInstanceId: opt.data.wfInstanceId,
      }
      $.ajax({
        url: API.getNodeList,
        type: "POST",
        async: false,
        data: JSON.stringify(queryData),
        contentType: "application/json; charset=utf-8",
        success: function(res){
          if(!res.success || !res.object){
            return
          }
          let options = {
            searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'wfStepName', 
            valName: 'wfStepId', 
            inpTextName: 'wfStepName', 
            inpValName: 'wfStepId', 
            required: 'required',
            data: res.object||[],
            callback: function(res) {
              wfPermissionsList = [];
              setPermissionsText();
              if(!res)  return;
              let list = res.userList;
              if(list.length > 0){
                for(var i=0;i<list.length;i++){
                  wfPermissionsList.push({
                    userName:list[i].username,
                    userId:list[i].usercode
                  })
                }
                setPermissionsText();
              }
            }
          }
          $('#operation [name="wfStep"]').css('display', 'flex');
          new $.selectPlug('#wfStepName', options)
        }
      })  
    }

    function filteSelData(arr) {
      var filtArr = [];
      for (var i = 0; i < arr.length; i++) {
          filtArr.push({
            username: arr[i].userName,
            usercode: arr[i].userId,
          });
      }
      return filtArr;
    }
    //人员选择
    $("#operation .userTransfer")
    .off("click")
    .on("click", function () {
      var users = filteSelData(
        wfPermissionsList,
      );
      var arr = [];
      for (var i = 0; i < users.length; i++) {
        arr.push({
          name: users[i].username,
          code: users[i].usercode,
        });
      }
      wfPermissionsList = [];
      $.quoteFun("/commonPage/userDeptGroup/index", {
        data: {
          user: true,
          dept: true,
          userList: arr,
        },
        callBack: function (list, seluser, dept) {
          var data = [];
          for (var i = 0; i < seluser.length; i++) {
            data.push({
              username: seluser[i].name,
              usercode: seluser[i].code,
            });
          }
          for (var i = 0; i < data.length; i++) {
            wfPermissionsList.push({
              userName: data[i].username,
              userId: data[i].usercode,
            });
          }
          setPermissionsText();
        },
      });
    });
    function getNames(arr) {
      return arr.map(e => e.username).join(',');
    }
    function getCodes(arr) {
      return arr.map(e => e.usercode).join(',');
    }
    function setPermissionsText() {
      $("#operation #usernames")
        .val(getNames(filteSelData(wfPermissionsList)))
        .attr("title", getNames(filteSelData(wfPermissionsList)));
      $("#operation #usercodes")
      .val(getCodes(filteSelData(wfPermissionsList)))
      .attr("title", getCodes(filteSelData(wfPermissionsList)));
    }
  }
})