'use strict';
/**
 * 人员，部门，群组 选择
 * 参数： {
 * item
 * ref
 * }
 *
 * **/
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(
      ['form', 'table', 'element', 'laydate'],
      function () {
        var form = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;

        var processDealListTable_ZB = null;
        var selTable = null;
        var selTableDom = null;
        var wins = layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          shadeClose: false,
          area: ['1100px', '80%'],
          skin: 'yourclass',
          content: html,
          cancel: opt.ref,
          success: function (layero, index) {
            form.render('select');
            initTable();
          },
        });
        var windowsOpen = [];
        var loop = setInterval(function () {
          //监听子页面关闭事件,轮询时间1000毫秒
          for (var i = windowsOpen.length - 1; i >= 0; i--) {
            if (windowsOpen[i].closed) {
              if (windowsOpen[i].openWiner != undefined) {
                refreshTable();
              }
              var openedWindowIndex = common.openedWindow.findIndex((item) => {
                windowsOpen[i].name = item.name;
              });
              if (openedWindowIndex != -1)
                common.openedWindow.splice(openedWindowIndex, 1);
              windowsOpen.splice(i, 1);
              break;
            }
          }
        }, 1000);

        function refreshTable() {
          processDealListTable_ZB
            ? processDealListTable_ZB.refresh()
            : initTable();
        }
        //查询条件
        function getQuery() {
          var search = [];
          search = $('#dealListForm').serializeArray();
          var data = {};
          for (var i in search) {
            data[search[i].name] = search[i].value;
          }
          return data;
        }
         //搜索
        $('#processDealListTable #processDealListTablesSearch')
        .off('click')
        .on('click', function () {
          initTable();
        });
        $('#processDealListTable #processDealListResetBtn')
          .off('click')
          .on('click', function () {
            $('#dealListForm')[0].reset();
            initTable();
          });
        // 表格
        function processDealListTableFun() {
          processDealListTable_ZB = new $.trasenTable(
            'processDealListTable_ZB',
            {
              url: '/ts-workflow/workflow/instance/intervene/interveneTaskList',
              datatype: 'json',
              mtype: 'get',
              pager: 'processDealListTable_ZB_pager',
              postData: getQuery(),
              rownumbers: false,
              multiselect: true,
              colModel: [
                {
                  label: 'ID',
                  sortable: false,
                  name: 'taskId',
                  hidden: true,
                  key: true,
                },
                {
                  label: '标题',
                  name: 'workflowTitle',
                  sortable: false,
                  align: 'left',
                  width: 300
                },
                {
                  label: '发起人',
                  sortable: true,
                  width: 100,
                  name: 'createUserName',
                  index: 'inst.create_user_name',
                  align: 'center',
                },
                {
                  label: '发起时间',
                  sortable: true,
                  index: 'inst.create_date',
                  width: 180,
                  name: 'createDate',
                  align: 'center',
                },
                {
                  label: '当前节点',
                  sortable: true,
                  width: 120,
                  name: 'currentStepName',
                  index: 'inst.current_step_name',
                  align: 'center',
                  formatter: function (cell, opt, row) {
                    return `<span class="mouseenter-item" wfInstId="${row.wfInstanceId}">${cell}</span>`;
                  },
                },
                {
                  label: '当前审批人工号',
                  sortable: true,
                  width: 110,
                  name: 'assigneeNo',
                  index: 'inst.assignee_no',
                  align: 'center'
                },
                {
                  label: '当前审批人名称',
                  sortable: true,
                  width: 110,
                  name: 'assigneeName',
                  index: 'inst.assignee_name',
                  align: 'center'
                },
                {
                  label:'操作',
                  name: '',
                  sortable: false,
                  width: 100,
                  editable: false,
                  align: 'center',
                  title: false,
                  classes: 'visible jqgrid-rownum ui-state-default',
                  formatter: function (cell, opt, row) {
                    var html =  `<span class="btns moveIn" row-id="${row.taskId}">移交</span>`;
                    html += `<span class="btns moveOut" row-id="${row.taskId}">移出</span>`;
                    return html;
                  },
                },
              ],
			        rowList: [100, 200, 500, 1000,2000,10000],
              onCellSelect: function (rowId, iCol, cellcontent, e) {
                if (iCol > 1) {
                  $('#processDealListTable_ZB').jqGrid(
                    'setSelection',
                    rowId,
                    false
                  );
                }
              },
              buidQueryParams: function () {
                return getQuery();
              },
            }
          );
        }
        //初始化表格
        function initTable() {
          if (processDealListTable_ZB) {
            processDealListTable_ZB.refresh();
          } else {
            processDealListTableFun();
          }
          selTable = processDealListTable_ZB;
          selTableDom = 'processDealListTable_ZB';
        }
        var content = `
        <form class="layui-form" style="margin-top:10px;">
          <div class="layui-form-item device-uphold-add-wrap flex">
            <label style="width: 50px;">备注：</label>
            <div style="width: 270px;">
              <textarea type="text" name="equipmentRemark" placeholder="请输入备注" class="layui-textarea" lay-verify="required" maxlength="300" 
                style="resize: none;min-height: auto; height: 60px;"></textarea>
            </div>
          </div>
        </form>
      `;
        // 单个移出
        $('#processDealListTable')
        .off('click', '.moveOut')
        .on('click', '.moveOut', function () {
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData(rowId);
          layer.open({
            title: '提示',
            content: '<span>确定要移出该条任务数据吗?</span>' + content,
            skin: 'moveDialog',
            btn:['取消', '确定'],
            btn1:(index, layero)=>{
                layer.close(index)
            },
            btn2: (index, layero)=>{
              let remark = layero.find('.layui-textarea').val();
              $.ajax({
                url: '/ts-workflow/workflow/instance/intervene/remove',
                data: JSON.stringify({
                  taskIds: [rowData.taskId],
                  remark
                }),
                contentType:'application/json',
                type: 'post',
                success: function (res) {
                  if (res.success) {
                    initTable();
                    layer.msg(res.message || '移出成功');
                    layer.close(index);
                  } else {
                    layer.msg(res.message || '移出失败');
                  }
                },
              });
            }
          })
        });
        // 单个移交
        $('#processDealListTable')
        .off('click', '.moveIn')
        .on('click', '.moveIn', function () {
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData(rowId);
          $.quoteFun('process/intervene/modules/moveIn', {
            title:  rowData.workflowTitle + '-流程移交',
            data: rowData,
            type: 'alone',
            ref: initTable
          });
        });
        // 批量移出
        $('#processDealListTable')
        .off('click', '#transferBatchMoveOut')
        .on('click', '#transferBatchMoveOut', function () {
          var taskIds = $('#processDealListTable_ZB').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (taskIds.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          layer.open({
            title: '提示',
            content: '<span>确定要移出选中的任务数据吗?</span>' + content,
            skin: 'moveDialog',
            btn:['取消', '确定'],
            btn1:(index, layero)=>{
                layer.close(index)
            },
            btn2: (index, layero)=>{
              let remark = layero.find('.layui-textarea').val();
              $.ajax({
                url: '/ts-workflow/workflow/instance/intervene/remove',
                data: JSON.stringify({
                  taskIds,
                  remark
                }),
                contentType:'application/json',
                type: 'post',
                success: function (res) {
                  if (res.success) {
                    initTable();
                    layer.msg(res.message || '移交成功');
                    layer.close(index);
                  } else {
                    layer.msg(res.message || '移交失败');
                  }
                },
              });
            }
          })
        });
        // 批量移交
        $('#processDealListTable')
        .off('click', '#transferBatchMoveIn')
        .on('click', '#transferBatchMoveIn', function () {
          var taskIds = $('#processDealListTable_ZB').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (taskIds.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          $.quoteFun('process/intervene/modules/moveIn', {
            title:  '批量流程移交',
            data: taskIds,
            type: 'batch',
            ref: initTable
          });
        });
        //关闭
        $('#processDealListTable')
          .off('click', '#close')
          .on('click', '#close', function () {
            opt.ref && opt.ref();
            layer.close(wins);
          });
      }
    );
  };
});
