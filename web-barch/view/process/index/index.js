'use strict';

define(function (require, exports, module) {
  module.exports = {
    init: init,
    cancle: function () {
      Event.create('toDealList').remove('changeStatus');
      Event.create('toDealList').remove('waitDeal');
    },
  };

  function init() {
    layui.use(['form', 'laydate', 'zTreeSearch', 'trasen'], function () {
      var form = layui.form,
        laydate = layui.laydate,
        trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch;

      var handleStatus = 1;
      var sortableFields = {
        // 1: ['createUserName', 'launchDeptName', 'createDate', 'currentStepName'],
        // 2: ['createDate', 'updateDate', 'status', 'currentStepName'],
        3: ['createDate', 'status', 'currentStepName'],
        4: ['createUserName', 'launchDeptName', 'createDate', 'currentStepName', 'status']
      }

      if ('csjkyy' == common.globalSetting.orgCode) {
        //长沙经开医院已办页签流程状态默认全部
        $('#hasDealStatus').val('');
        $('#transferAllBtn').hide();
      }
	  if ('smxzyyy' == common.globalSetting.orgCode) {
	    //石门需求 隐藏转办
	    $('#transferAllBtn').hide();
	  }
      form.render('select');

      $.each($('#processBox .laydate-date'), function (i, v) {
        laydate.render({
          elem: v,
          trigger: 'click',
        });
      });
      //部门树
      zTreeSearch.init('#processBox #processMyDeal [name="launchDeptName"]', {
        url: common.url + '/ts-oa/thpsSysetm/getDeptList',
        type: 'get',
        checkbox: false,
        condition: 'name',
        zTreeOnClick: function (treeId, treeNode) {
          $('#processBox #processMyDeal [name="launchDeptCode"]').val(
            treeNode.id
          );
          /*$("#empDeptName").val(treeNode.name);*/
        },
        callback: function () {
          $('#processBox #processMyDeal [name="launchDeptCode"]').val('');
        },
      });
      zTreeSearch.init('#processBox #processHasDeal [name="launchDeptName"]', {
        url: common.url + '/ts-oa/thpsSysetm/getDeptList',
        type: 'get',
        checkbox: false,
        condition: 'name',
        zTreeOnClick: function (treeId, treeNode) {
          $('#processBox #processHasDeal [name="launchDeptCode"]').val(
            treeNode.id
          );
          /*$("#empDeptName").val(treeNode.name);*/
        },
        callback: function () {
          $('#processBox #processHasDeal [name="launchDeptCode"]').val('');
        },
      });
      zTreeSearch.init('#processBox #processCopy [name="launchDeptName"]', {
        url: common.url + '/ts-oa/thpsSysetm/getDeptList',
        type: 'get',
        checkbox: false,
        condition: 'name',
        zTreeOnClick: function (treeId, treeNode) {
          $('#processBox #processCopy [name="launchDeptCode"]').val(
            treeNode.id
          );
          /*$("#empDeptName").val(treeNode.name);*/
        },
        callback: function () {
          $('#processBox #processCopy [name="launchDeptCode"]').val('');
        },
      });

      var setting = {
        data: {
          simpleData: {
            enable: false,
          },
        },
        callback: {
          onClick: onClick,
          onNodeCreated: function (e, id, node) {},
        },
        view: {
          dblClickExpand: true,
          showTitle: true,
          showLine: false,
        },
      };
      var treeObj;
      var selType;

      function onClick(event, treeId, treeNode) {
        selType = treeNode;
        switch (handleStatus * 1) {
          case 1:
            if (treeNode.userData.classifyType != 9) {
              $('#processMyDeal [name="workflowClassify"]').val(treeNode.id);
              $('#processMyDeal [name="workflowNo"]').val('');
            } else {
              $('#processMyDeal [name="workflowClassify"]').val('');
              $('#processMyDeal [name="workflowNo"]').val(treeNode.code);
              getIsBatchApprove();
            }
            break;
          case 2:
            if (treeNode.userData.classifyType != 9) {
              $('#processHasDeal [name="workflowClassify"]').val(treeNode.id);
              $('#processHasDeal [name="workflowNo"]').val('');
            } else {
              $('#processHasDeal [name="workflowClassify"]').val('');
              $('#processHasDeal [name="workflowNo"]').val(treeNode.code);
            }
            break;
          case 3:
            if (treeNode.userData.classifyType != 9) {
              $('#processMyStart [name="workflowClassify"]').val(treeNode.id);
              $('#processMyStart [name="workflowNo"]').val('');
            } else {
              $('#processMyStart [name="workflowClassify"]').val('');
              $('#processMyStart [name="workflowNo"]').val(treeNode.code);
            }
            break;
          case 4:
            if (treeNode.userData.classifyType != 9) {
              $('#processCopy [name="workflowClassify"]').val(treeNode.id);
              $('#processCopy [name="workflowNo"]').val('');
            } else {
              $('#processCopy [name="workflowClassify"]').val('');
              $('#processCopy [name="workflowNo"]').val(treeNode.code);
            }
            break;
          default:
            break;
        }
        initTable();
      }
      initTree();
      //流程树
      function initTree() {
        var data = {};
        var el = '';
        switch (handleStatus * 1) {
          case 1:
            data.permissionsType = 1;
            data.queryType = 2;
            data.handleStatus = 1;
            el = '#processMyDeal #processDealModTree';
            if ($.fn.zTree.getZTreeObj('processDealModTree')) {
              $.fn.zTree.destroy('processDealModTree');
            }
            $('#processMyDeal')
              .off('keypress', '#processMyDealTreeSearch')
              .on('keypress', '#processMyDealTreeSearch', function (e) {
                e.stopPropagation();
                if (e.keyCode == 13) {
                  $.fn.ztreeQueryHandler(treeObj, $(this).val());
                  treeObj.selectNode(selType);
                }
              });
            break;
          case 2:
            data.permissionsType = 1;
            data.queryType = 2;
            data.handleStatus = 2;
            el = '#processHasDeal #processHasDealModTree';
            if ($.fn.zTree.getZTreeObj('processHasDealModTree')) {
              $.fn.zTree.destroy('processHasDealModTree');
            }
            $('#processHasDeal')
              .off('keypress', '#processHasDealTreeSearch')
              .on('keypress', '#processHasDealTreeSearch', function (e) {
                e.stopPropagation();
                if (e.keyCode == 13) {
                  $.fn.ztreeQueryHandler(treeObj, $(this).val());
                  treeObj.selectNode(selType);
                }
              });
            break;
          case 3:
            data.permissionsType = 1;
            data.queryType = 1;
            el = '#processMyStart #processMyStartModTree';
            if ($.fn.zTree.getZTreeObj('processMyStartModTree')) {
              $.fn.zTree.destroy('processMyStartModTree');
            }
            $('#processMyStart')
              .off('keypress', '#processMyStartTreeSearch')
              .on('keypress', '#processMyStartTreeSearch', function (e) {
                e.stopPropagation();
                if (e.keyCode == 13) {
                  $.fn.ztreeQueryHandler(treeObj, $(this).val());
                  treeObj.selectNode(selType);
                }
              });
            break;
          case 4:
            data.permissionsType = 3;
            data.queryType = 4;
            el = '#processCopy #processCopyModTree';
            if ($.fn.zTree.getZTreeObj('processCopyModTree')) {
              $.fn.zTree.destroy('processMyStartModTree');
            }
            $('#processCopy')
              .off('keypress', '#processCopyTreeSearch')
              .on('keypress', '#processCopyTreeSearch', function (e) {
                e.stopPropagation();
                if (e.keyCode == 13) {
                  $.fn.ztreeQueryHandler(treeObj, $(this).val());
                  treeObj.selectNode(selType);
                }
              });
            break;
          default:
            break;
        }
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          data: JSON.stringify(data),
          url: '/ts-workflow/form/classify/getMyWfTree',
          success: function (res) {
            if (res.success) {
              var zNodes = res.object;
              treeObj = $.fn.zTree.init($(el), setting, zNodes);
            } else {
              layer.msg('树加载失败.');
            }
          },
        });
      }

      var windowsOpen = [];
      var loop = setInterval(function () {
        //监听子页面关闭事件,轮询时间1000毫秒
        for (var i = windowsOpen.length - 1; i >= 0; i--) {
          if (windowsOpen[i].closed) {
            if (windowsOpen[i].openWiner != undefined && handleStatus != 2) {
              processRef();
              initTree();
            }
            var openedWindowIndex = common.openedWindow.findIndex((item) => {
              windowsOpen[i].name = item.name;
            });
            if (openedWindowIndex != -1)
              common.openedWindow.splice(openedWindowIndex, 1);
            windowsOpen.splice(i, 1);
            break;
          }
        }
      }, 1000);

      function getToDayNum() {
        $.ajax({
          method: 'post',
          url: '/ts-workflow/workflow/task/getTotalToDoData',
          success: function (res) {
            if (res.success && res.object) {
              $('#processBox #toDayNum').html(res.object.toDoNum || 0);
              /*   $('#processMyDeal #toDayHasNum').html(res.object.haveDoNum || 0)*/
            }
          },
        });
      }

      $('#processBox [name="followId"]').on('change', function (e) {
        initTable();
      });

      //切换
      $('#processBox')
        .off('click', '.oa-nav .oa-nav_item')
        .on('click', '.oa-nav .oa-nav_item', function () {
          $('#processBox .oa-nav .oa-nav_item').removeClass('active');
          var index = $(this).index();
          $.each($('#processBox .oa-nav'), function () {
            $(this).find(' .oa-nav_item').eq(index).addClass('active');
          });
          // $(this).addClass('active').siblings().removeClass('active');
          handleStatus = $(this).attr('data-value');
          $('#processBox .processTabs')
            .addClass('none')
            .eq(handleStatus - 1)
            .removeClass('none');
          setTimeout(function () {
            initTable();
            initTree();
          }, 100);
        });

      var waitDealTable = null;
      var hasDealTable = null;
      var myStartTable = null;
      var copyTable = null;
      var selTable = null;
      var selTableDom = null;
      initTable();

      function waitDealTableFun() {
        waitDealTable = new $.trasenTable('grid-table-processMyDeal', {
          url: '/ts-workflow/workflow/instance/getMyHandleWorkflowList',
          datatype: 'json',
          mtype: 'get',
          pager: 'grid-pager-processMyDeal',
          shrinkToFit: true,
          sortname: 'inst.create_date',
		  rowNum:20,
		  rowList:[20,50,100],
          postData: $.extend(
            {
              handleStatus: 1,
            },
            getQuery()
          ),
          multiselect: true,
          colModel: [
            {
              label: 'ID',
              sortable: false,
              name: 'taskId',
              hidden: true,
              key: true,
            },
            {
              label: '标题',
              name: 'workflowNumber',
              sortable: false,
              align: 'left',
              width: 300,
              formatter: function (cell, opt, row) {
                let follow = '';
                if (row.followId) {
                  follow =
                    '<img class="deal_icon cancelfollow follow-icon" row-id="' +
                    row.followId +
                    '" src="/static/img/other/follow-active.svg"/>';
                } else {
                  follow =
                    '<img class="deal_icon follow follow-icon" row-id="' +
                    row.wfInstanceId +
                    '" src="/static/img/other/follow-no.svg"/>';
                }
                var data = {
                  1: '一般',
                  2: '加急',
                  3: '急件',
                  4: '特急',
                };
                var str = '';
                var color =
                  row.urgencyLevel >= 2 ? 'style="color:#f59a23"' : '';
                var level =
                  row.urgencyLevel && row.urgencyLevel >= 2
                    ? '[' + data[row.urgencyLevel] + ']'
                    : '';
                var press =
                  row.isPress == 1
                    ? '<span style="color:red">[ 催办 ]</span>  '
                    : '  ';
                var cellStr = '<span ' + color + '>' + level + ' </span>  ';
                // if (row.isPress == 1) {
                //     cellStr = press;
                // }
                cellStr = follow + press + cellStr;
                if (row.wfVariableInfoList && row.wfVariableInfoList.length) {
                  for (var i = 0; i < row.wfVariableInfoList.length; i++) {
                    str +=
                      '[' +
                      (row.wfVariableInfoList[i].variableValue || '') +
                      ']';
                  }
                  cellStr += row.workflowTitle.replace('-', str);
                } else {
                  cellStr += row.workflowTitle;
                }
                if (handleStatus == 1) {
                  cellStr += '等待您的办理!';
                }
                if (handleStatus == 1) {
                  if (row.currentStepName == '重新提交') {
                    return (
                      '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                      opt.rowId +
                      '" row-status="' +
                      row.status +
                      '"  type="restart">' +
                      cellStr +
                      '</p>'
                    );
                  }
                  return (
                    '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '" type=' +
                    (row.isAddSignature == 1 ? 'countersign' : 'confirm') +
                    '  >' +
                    cellStr +
                    '</p>'
                  );
                } else {
                  return (
                    '<p class="seeLink seeWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '">' +
                    cellStr +
                    '</p>'
                  );
                }
              },
            },
            // {
            //     label: '类型',
            //     sortable: false,
            //     name: 'workflowTypeName',
            //     width: 60,
            //     align: 'left',
            // },
            {
              label: '发起人',
              sortable: true,
              width: 75,
              name: 'createUserName',
              index: 'inst.create_user_name',
              align: 'left',
            },
            {
              label: '发起人部门',
              sortable: true,
              width: 100,
              name: 'launchDeptName',
              index: 'inst.launch_dept_name',
              align: 'left',
            },
            {
              label: '发起时间',
              sortable: true,
              index: 'inst.create_date',
              width: 120,
              name: 'createDate',
              align: 'center',
            },
            {
              label: '当前节点',
              sortable: true,
              width: 80,
              name: 'currentStepName',
              index: 'inst.current_step_name',
              align: 'center',
              formatter: function (cell, opt, row) {
                return `<span class="mouseenter-item" wfInstId="${row.wfInstanceId}">${cell}</span>`;
              },
            },
            {
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              align: 'center',
              title: false,
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cell, opt, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                if (row.currentStepName == '重新提交') {
                  btns =
                    '<button class="layui-btn dealWf" type="restart" row-id="' +
                    opt.rowId +
                    '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>重新提交</button> ';
                  btns +=
                    '<button class="layui-btn delWf" row-id="' +
                    opt.rowId +
                    '"><i class="fa fa-trash deal_icon" aria-hidden="true" type="office"></i>删除</button> ';
                } else {
                  btns =
                    '<button class="layui-btn dealWf" type="' +
                    (row.isAddSignature == 1 ? 'countersign' : 'confirm') +
                    '" row-id="' +
                    opt.rowId +
                    '"><i class="fa fa-upload deal_icon" aria-hidden="true"></i>办理</button> ';
                }
                html += btns + '</div></div>';
                return html;
              },
            },
          ],
          onCellSelect: function (rowId, iCol, cellcontent, e) {
            if (iCol > 1) {
              $('#grid-table-processMyDeal').jqGrid(
                'setSelection',
                rowId,
                false
              );
            }
          },
          buidQueryParams: function () {
            return getQuery();
          },
        });
      }

      function hasDealTableFun() {
        hasDealTable = new $.trasenTable('grid-table-processHasDeal', {
          url: '/ts-workflow/workflow/instance/getMyHandleWorkflowList',
          datatype: 'json',
          mtype: 'get',
          pager: 'grid-pager-processHasDeal',
          shrinkToFit: true,
          sortname: 'inst.update_date',
		  rowNum:20,
		  rowList:[20,50,100],
          postData: $.extend(
            {
              handleStatus: 2,
            },
            getQuery()
          ),
          colModel: [
            {
              label: '标题',
              name: 'workflowNumber',
              sortable: false,
              align: 'left',
              width: 300,
              formatter: function (cell, opt, row) {
                let follow = '';
                if (row.followId) {
                  follow =
                    '<img class="deal_icon cancelfollow follow-icon" row-id="' +
                    row.followId +
                    '" src="/static/img/other/follow-active.svg"/>';
                } else {
                  follow =
                    '<img class="deal_icon follow follow-icon" row-id="' +
                    row.wfInstanceId +
                    '" src="/static/img/other/follow-no.svg"/>';
                }
                var data = {
                  1: '一般',
                  2: '加急',
                  3: '急件',
                  4: '特急',
                };
                var str = '';
                var color =
                  row.urgencyLevel >= 2 ? 'style="color:#f59a23"' : '';
                var level =
                  row.urgencyLevel && row.urgencyLevel >= 2
                    ? '[' + data[row.urgencyLevel] + ']'
                    : '';
                var press =
                  row.isPress == 1
                    ? '<span style="color:red">[ 催办 ]</span>  '
                    : '  ';
                var cellStr = '<span ' + color + '>' + level + ' </span>  ';
                // if (row.isPress == 1) {
                //     cellStr = press;
                // }
                cellStr = follow + press + cellStr;
                if (row.wfVariableInfoList && row.wfVariableInfoList.length) {
                  for (var i = 0; i < row.wfVariableInfoList.length; i++) {
                    str +=
                      '[' +
                      (row.wfVariableInfoList[i].variableValue || '') +
                      ']';
                  }
                  cellStr += row.workflowTitle.replace('-', str);
                } else {
                  cellStr += row.workflowTitle;
                }
                if (handleStatus == 1) {
                  cellStr += '等待您的办理!';
                }
                if (handleStatus == 1) {
                  if (row.currentStepName == '重新提交') {
                    return (
                      '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                      opt.rowId +
                      '" row-status="' +
                      row.status +
                      '"  type="restart">' +
                      cellStr +
                      '</p>'
                    );
                  }
                  return (
                    '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"  >' +
                    cellStr +
                    '</p>'
                  );
                } else {
                  return (
                    '<p class="seeLink seeWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '">' +
                    cellStr +
                    '</p>'
                  );
                }
              },
            },
            // {
            //     label: '类型',
            //     sortable: false,
            //     name: 'workflowTypeName',
            //     width: 60,
            //     align: 'left',
            // },
            {
              label: '发起人',
              sortable: false,
              width: 70,
              name: 'createUserName',
              index: 'inst.create_user',
              align: 'left',
            },
            {
              label: '发起时间',
              sortable: true,
              index: 'inst.create_date',
              width: 120,
              name: 'createDate',
              align: 'center',
            },
            {
              label: '办理时间',
              sortable: true,
              width: 120,
              name: 'updateDate',
              index: 'inst.update_date',
              align: 'center',
            },
            {
              label: '流程状态',
              name: 'status',
              sortable: true,
              width: 75,
              index: 'inst.status',
              align: 'center',
              formatter: function (cell, opt, row) {
                var data = {
                  0: '草稿',
                  1: '在办',
                  2: '办结',
                  3: '强制结束',
                  4: '撤销',
                  5: '终止',
                };
                return data[row.status];
              },
            },
            {
              label: '当前节点',
              sortable: true,
              width: 75,
              index: 'inst.current_step_name',
              name: 'currentStepName',
              align: 'left',
              formatter: function (cell, opt, row) {
                return `<span class="mouseenter-item" wfInstId="${row.wfInstanceId}">${cell}</span>`;
              },
            },
            {
              label: '节点未审批人员',
              sortable: false,
              title: false,
              name: 'assigneeNames',
              formatter: function (cell, opt, row) {
                // if (cell && cell.length > 10) {
                //     return '<span title="' + cell + '">' + cell.substring(0, 10) + '..</span>';
                // } else {
                //     return cell || '';
                // }
                cell = cell || '无';
                return (
                  '<p style="white-space: normal;" title="' +
                  cell +
                  '">' +
                  cell +
                  '</p>'
                );
              },
            },
            {
              label: '办结时间',
              sortable: false,
              title: true,
              width: 120,
              name: 'wfFinishedDate',
            },
            {
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              align: 'center',
              title: false,
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cell, opt, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';

                btns =
                  '<button  class="layui-btn  seeWf"><i class="fa fa-eye deal_icon" aria-hidden="true"></i>查看</button>';
                html += btns + '</div></div>';
                return html;
              },
            },
          ],
          buidQueryParams: function () {
            return getQuery();
          },
        });
      }

      function myStartTableFun() {
        myStartTable = new $.trasenTable('grid-table-processMyStart', {
          url: '/ts-workflow/workflow/instance/getMyLaunchWorkflowList',
          datatype: 'json',
          mtype: 'get',
          pager: 'grid-pager-processMyStart',
          shrinkToFit: true,
          sortname: 'inst.create_date',
          rowNum:20,
          rowList:[20,50,100],
          postData: $.extend(
            {
              handleStatus: null,
            },
            getQuery()
          ),
          colModel: [
            {
              label: '标题',
              name: 'workflowNumber',
              sortable: false,
              width: 300,
              align: 'left',
              formatter: function (cell, opt, row) {
                let follow = '';
                if (row.followId) {
                  follow =
                    '<img class="deal_icon cancelfollow follow-icon" row-id="' +
                    row.followId +
                    '" src="/static/img/other/follow-active.svg"/>';
                } else {
                  follow =
                    '<img class="deal_icon follow follow-icon" row-id="' +
                    row.wfInstanceId +
                    '" src="/static/img/other/follow-no.svg"/>';
                }
                var data = {
                  1: '一般',
                  2: '加急',
                  3: '急件',
                  4: '特急',
                };
                var str = '';
                var color =
                  row.urgencyLevel >= 2 ? 'style="color:#f59a23"' : '';
                var level =
                  row.urgencyLevel && row.urgencyLevel >= 2
                    ? '[' + data[row.urgencyLevel] + ']'
                    : '';
                var press =
                  row.isPress == 1
                    ? '<span style="color:red">[ 催办 ]</span>  '
                    : '  ';
                var cellStr = '<span ' + color + '>' + level + ' </span>  ';
                // if (row.isPress == 1) {
                //     cellStr = press;
                // }
                cellStr = follow + press + cellStr;
                if (row.wfVariableInfoList && row.wfVariableInfoList.length) {
                  for (var i = 0; i < row.wfVariableInfoList.length; i++) {
                    str +=
                      '[' +
                      (row.wfVariableInfoList[i].variableValue || '') +
                      ']';
                  }
                  cellStr += row.workflowTitle.replace('-', str);
                } else {
                  cellStr += row.workflowTitle;
                }
                if (handleStatus == 1) {
                  cellStr += '等待您的办理!';
                }
                if (handleStatus == 1) {
                  if (row.currentStepName == '重新提交') {
                    return (
                      '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                      opt.rowId +
                      '" row-status="' +
                      row.status +
                      '"  type="restart">' +
                      cellStr +
                      '</p>'
                    );
                  }
                  return (
                    '<p class="dealLink dealWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"  >' +
                    cellStr +
                    '</p>'
                  );
                } else {
                  return (
                    '<p class="seeLink seeWf" style="cursor:pointer; white-space: normal;" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '">' +
                    cellStr +
                    '</p>'
                  );
                }
              },
            },
            // {
            //     label: '类型',
            //     sortable: false,
            //     name: 'workflowTypeName',
            //     width: 60,
            //     align: 'left',
            // },
            {
              label: '发起时间',
              sortable: true,
              index: 'inst.create_date',
              name: 'createDate',
              align: 'center',
              width: 120,
            },
            {
              label: '流程状态',
              name: 'status',
              sortable: true,
              index: 'inst.status',
              width: 70,
              align: 'center',
              formatter: function (cell, opt, row) {
                var data = {
                  0: '草稿',
                  1: '在办',
                  2: '办结',
                  3: '强制结束',
                  4: '撤销',
                  5: '终止',
                };
                return data[row.status];
              },
            },
            {
              label: '当前节点',
              sortable: true,
              width: 65,
              name: 'currentStepName',
              index: 'inst.current_step_name',
              align: 'left',
              formatter: function (cell, opt, row) {
                return `<span class="mouseenter-item" wfInstId="${
                  row.wfInstanceId
                }">${cell || ''}</span>`;
              },
            },
            {
              label: '节点未审批人员',
              sortable: false,
              title: false,
              name: 'assigneeNames',
              formatter: function (cell, opt, row) {
                // if (cell && cell.length > 10) {
                //     return '<span title="' + cell + '">' + cell.substring(0, 10) + '..</span>';
                // } else {
                //     return cell || '';
                // }
                cell = cell || '无';
                return (
                  '<p style="white-space: normal;" title="' +
                  cell +
                  '">' +
                  cell +
                  '</p>'
                );
              },
            },
            {
              label: '是否打印',
              sortable: false,
              title: true,
              width: 65,
              align: 'center',
              name: 'print',
              formatter: function (cell, opt, row) {
                let label = cell == '0' ? '未打印' : '已打印';
                return '<span>' + label + '</span>';
              },
            },
            {
              label: '办结时间',
              sortable: false,
              title: true,
              width: 120,
              name: 'wfFinishedDate',
            },
            {
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              title: false,
              align: 'center',
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cell, opt, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                if (row.currentStepName == '重新提交') {
                  btns =
                    '<button class="layui-btn restartWf" type="restart" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>重新提交</button> ';
                  btns +=
                    '<button class="layui-btn delWf" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"><i class="fa fa-trash deal_icon" aria-hidden="true" type="office"></i>删除</button> ';
                } else {
                  if (row.status == 0) {
                    btns =
                      '<button  class="layui-btn seeWf" row-id="' +
                      opt.rowId +
                      '"  row-status="' +
                      row.status +
                      '"  type="edit"><i class="fa fa-upload deal_icon" aria-hidden="true"></i>编辑</button> ';
                  } else {
                    btns =
                      '<button class="layui-btn seeWf" row-id="' +
                      opt.rowId +
                      '"  row-status="' +
                      row.status +
                      '" ><i class="fa fa-eye deal_icon" aria-hidden="true" type="office"></i>查看</button>';
                  }
                }
                if (row.status == 2 && row.isNormal == 'N') {
                  btns +=
                    '<button class="layui-btn startWfAgain" type="restart" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>再次发起</button> ';
                }
                if (row.status == 4 && row.isNormal == 'N') {
                  btns +=
                    '<button class="layui-btn startWfAgain" type="restart" row-id="' +
                    opt.rowId +
                    '" row-status="' +
                    row.status +
                    '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>再次发起</button> ';
                  btns +=
                    '<button class="layui-btn delWf" row-id="' +
                    opt.rowId +
                    '"  row-status="' +
                    row.status +
                    '" ><i class="fa fa-trash deal_icon" aria-hidden="true" type="office"></i>删除</button>';
                }
                if (row.status == 0 && row.isNormal == 'N') {
                  btns +=
                    '<button class="layui-btn delWf" row-id="' +
                    opt.rowId +
                    '"  row-status="' +
                    row.status +
                    '" ><i class="fa fa-trash deal_icon" aria-hidden="true" type="office"></i>删除</button>';
                }
                html += btns + '</div></div>';
                return html;
              },
            },
          ],
          buidQueryParams: function () {
            return getQuery();
          },
          onSortCol: function (index, columnIndex, sortOrder) {
            sortableFields[3].forEach(fieldName => {
              let domName = `#processBox #${selTableDom}_${fieldName}`;
              $(domName).find('.ui-jqgrid-sortable span.s-ico').attr('style', 'display: initial !important')
            })
          }
        });
      }

      function copyTableFun() {
        copyTable = new $.trasenTable('grid-table-processCopy', {
          datatype: 'json',
          url: '/ts-workflow/workflow/instance/getCopyToMyWorkflowList',
          pager: 'grid-pager-processCopy',
          mtype: 'get',
          shrinkToFit: true,
          sortname: 'inst.create_date',
		  rowNum:20,
		  rowList:[20,50,100],
          postData: $.extend({}, getQuery()),
          colModel: [
            {
              label: '标题',
              name: 'workflowNumber',
              width: 300,
              sortable: false,
              align: 'left',
              formatter: function (cell, opt, row) {
                let readStatusColor = row.readStatus === '1' ? '#999' : '#000';
                let follow = '';
                if (row.followId) {
                  follow =
                    '<img class="deal_icon cancelfollow follow-icon" row-id="' +
                    row.followId +
                    '" src="/static/img/other/follow-active.svg"/>';
                } else {
                  follow =
                    '<img class="deal_icon follow follow-icon" row-id="' +
                    row.wfInstanceId +
                    '" src="/static/img/other/follow-no.svg"/>';
                }
                var data = {
                  1: '一般',
                  2: '加急',
                  3: '急件',
                  4: '特急',
                };
                var str = '';
                var color =
                  row.urgencyLevel >= 2 ? 'style="color:#f59a23"' : '';
                var level =
                  row.urgencyLevel >= 2
                    ? '[' + data[row.urgencyLevel] + ']'
                    : '';
                var cellStr =
                  '<span ' + color + '>' + follow + level + ' </span><span> ';
                if (row.wfVariableInfoList && row.wfVariableInfoList.length) {
                  for (var i = 0; i < row.wfVariableInfoList.length; i++) {
                    str +=
                      '[' +
                      (row.wfVariableInfoList[i].variableValue || '') +
                      ']';
                  }
                  cellStr += row.workflowTitle.replace('-', str);
                } else {
                  cellStr += row.workflowTitle;
                }
                cellStr += '</span>';
                return (
                  '<p class="dealLink seeWf" style="cursor:pointer; white-space: normal; color:' +
                  readStatusColor +
                  ' " row-id="' +
                  opt.rowId +
                  '" row-status="' +
                  row.status +
                  '">' +
                  cellStr +
                  '</p>'
                );
              },
            },
            {
              label: '发起人',
              sortable: true,
              width: 75,
              name: 'createUserName',
              index: 'inst.create_user_name',
              align: 'left',
            },
            {
              label: '发起人部门',
              sortable: true,
              width: 80,
              name: 'launchDeptName',
              index: 'inst.launch_dept_name',
              align: 'left',
            },
            {
              label: '发起时间',
              name: 'createDate',
              index: 'inst.create_date',
              sortable: true,
              width: 120,
              align: 'center',
            },
            {
              label: '抄送人',
              name: 'copySendUserName',
              sortable: false,
              width: 70,
              align: 'center',
            },
            {
              label: '抄送时间',
              name: 'copySendDate',
              width: 120,
              align: 'center',
            },
            // {
            //     label: '类型',
            //     name: 'workflowTypeName',
            //     sortable: false,
            //     width: 60,
            //     align: 'left',
            // },
            {
              label: '当前节点',
              name: 'currentStepName',
              width: 80,
              sortable: true,
              index: 'inst.current_step_name',
              align: 'left',
              formatter: function (cell, opt, row) {
                return `<span class="mouseenter-item" wfInstId="${row.wfInstanceId}">${cell}</span>`;
              },
            },
            {
              label: '流程状态',
              name: 'status',
              sortable: true,
              width: 85,
              align: 'cnetr',
              index: 'inst.status',
              formatter: function (cell, opt, row) {
                var data = {
                  0: '草稿',
                  1: '在办',
                  2: '办结',
                  3: '强制结束',
                  4: '撤销',
                  5: '终止',
                };
                return data[row.status];
              },
            },
            {
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              title: false,
              align: 'center',
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cell, opt, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                btns =
                  '<button class="layui-btn seeWf" row-id="' +
                  opt.rowId +
                  '" row-status="' +
                  row.status +
                  '"><i class="fa fa-eye deal_icon" aria-hidden="true"></i>查看</button> ';
                html += btns + '</div></div>';
                return html;
              },
            },
          ],
          buidQueryParams: function () {
            return getQuery();
          },
          onSortCol: function (index, columnIndex, sortOrder) {
            sortableFields[4].forEach(fieldName => {
              let domName = `#processBox #${selTableDom}_${fieldName}`;
              $(domName).find('.ui-jqgrid-sortable span.s-ico').attr('style', 'display: initial !important')
            })
          }
        });
      }
      function handleSetMouseenterGetInfo() {
        let seed;
        setTimeout(() => {
          const jqgrow = $(`#${selTableDom} tbody`).find('.jqgrow');
          $(jqgrow)
            .find('.mouseenter-item')
            .on('mouseenter', function (e) {
              seed = setTimeout(() => {
                const wfInstId = $(this).attr('wfinstid');
                let pengdingTop = $(this).offset().top;
                let top = pengdingTop + 'px';
                const left = $(this).offset().left - 320 + 'px';

                if (pengdingTop + 230 > $('#processBox').height()) {
                  top = pengdingTop - 200 + 'px';
                }

                $.ajax({
                  type: 'get',
                  url: '/ts-workflow/workflow/taskHis/findTaskHisCommentList',
                  async: false,
                  data: {
                    wfInstId,
                  },
                  success: function (res) {
                    if (res.success) {
                      let li = '';
                      res.object.forEach((item) => {
                        const {
                          wfStepName,
                          actAssigneeName,
                          finishedDate,
                          remark,
                        } = item;
                        li += `
                          <li>
                              <div class="top">
                                  <p class="stepName">${wfStepName}</p>
                                  <span class="name">${actAssigneeName}</span>
                              </div>

                              <div class="top">
                                  <p class="stepName">${remark || ''}</p>
                                  <span class="date">${finishedDate}</span>
                              </div>
                          </li>`;
                      });

                      if (!li) {
                        li = '<li style="text-align:center">暂无流程信息</li>';
                      }

                      const ul = `
                        <ul class="wf-info-hover" style="position: fixed;left: ${left};top: ${top};">
                          ${li}
                        </ul>`;

                      $('#processBox .wf-info-hover').remove();
                      $('#processBox').append(ul);

                      $('#processBox .wf-info-hover').on(
                        'mouseleave',
                        function () {
                          $(this).remove();
                        }
                      );
                    }
                  },
                });
              }, 800);
            });

          $(jqgrow)
            .find('.mouseenter-item')
            .on('mouseleave', function () {
              clearTimeout(seed);
            });
        }, 2000);
      }
      //批量转办
      $('body')
        .off('click', '#transferAllBtn')
        .on('click', '#transferAllBtn', function () {
          var taskIds = $('#grid-table-processMyDeal').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (taskIds.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          $.quoteFun('/commonPage/stepTransfer/index', {
            title: '批量转办',
            close: function () {},
            callBack: function (data, successCallBack, errorCallBack) {
              var transferFormDatas = data;
              transferFormDatas.taskId = taskIds.join(',');
              $.ajax({
                url: '/ts-workflow/workflow/task/transferTask',
                method: 'post',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify(transferFormDatas),
                success: function (res) {
                  if (res.success) {
                    layer.msg('转办成功');
                    successCallBack && successCallBack();
                    setTimeout(function () {
                      initTable();
                      initTree();
                    }, 1000);
                  } else {
                    layer.msg(res.message || '转办失败');
                    errorCallBack && errorCallBack();
                  }
                },
              });
            },
          });

          // $.quoteFun('/process/index/transferWaitDeal', {
          //     title: '批量转办',
          //     ref: function() {
          //         layer.msg('转办成功');
          //         initTable();
          //         initTree();
          //     },
          //     data: {
          //         taskIds: taskIds.join(','),
          //     },
          // });
        });
       function refresh() {
          setTimeout(function () {
            initTable();
            initTree();
          }, 1000);
       }
       //批量审批
       $('body')
       .off('click', '#transferAllDo')
       .on('click', '#transferAllDo', function () {
         var taskIds = $('#grid-table-processMyDeal').jqGrid(
           'getGridParam',
           'selarrrow'
         );
         let list = [];
         if (taskIds.length <= 0) {
           layer.msg('请选择需要操作的数据！');
           return;
         }
         for (var i=0;i<taskIds.length;i++){
          let rowData = selTable.getSourceRowData(taskIds[i]);
          let obj = {
            taskId: rowData.taskId,
            wfDefinitionId: rowData.wfDefinitionId,
            currentStepId: rowData.currentStepNo,
            wfInstanceId: rowData.wfInstanceId
          };
          list.push(obj);
         }
         $.quoteFun('process/index/approve', {
            title:  '批量审批',
            data: list,
            type: 'skip',
            ref: refresh
          });
          return;
         $.ajax({
            url: 'ts-workflow/workflow/task/batchExamination',
            method: 'post',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify(list),
            success: function (res) {
              if (res.success) {
                layer.msg(res.message, {time:5000});
                setTimeout(function () {
                  initTable();
                  initTree();
                }, 1000);
              } else {
                layer.msg(res.message || '批量审批失败');
              }
            },
         });
       });

      //搜索
      $(
        '#processBox #processMyDealSearch,#processBox #processHasDealSearch,#processBox #processMyStartSearch,#processBox #processCopySearch'
      )
        .off('click')
        .on('click', function () {
          initTable();
        });
      $(
        '#processBox #processMyDealScreenSearchScreen,#processBox #processHasDealScreenSearchScreen,#processBox #processCopyScreenSearchScreen'
      )
        .off('click')
        .on('click', function () {
          initTable();
        });
      $('#processMyDeal')
        .off('click', '#processMyDealReset,#processMyDealScreen')
        .on('click', '#processMyDealReset,#processMyDealScreen', function () {
          $('#processMyDealScreenSearchScreenForm')[0].reset();
          $('#processMyDealQueryForm')[0].reset();
          $('#processMyDealScreenSearchScreenForm [name="launchDeptCode"]').val(
            ''
          );
          form.render('select');
          initTable();
          return;
        });
      $('#processHasDeal')
        .off('click', '#processHasDealReset,#processHasDealScreen')
        .on('click', '#processHasDealReset,#processHasDealScreen', function () {
          $('#processHasDealScreenSearchScreenForm')[0].reset();
          $('#processHasDealQueryForm')[0].reset();
          if ('csjkyy' == common.globalSetting.orgCode) {
            //长沙经开医院已办页签流程状态默认全部
            $('#hasDealStatus').val('');
          }
          $(
            '#processHasDealScreenSearchScreenForm [name="launchDeptCode"]'
          ).val('');
          form.render('select');
          initTable();
          return;
        });
      $('#processMyStart')
        .off('click', '#processMyStartReset')
        .on('click', '#processMyStartReset', function () {
          $('#processInitiatedByMeSearchScreenForm')[0].reset();
          $('#processMyStartQueryForm')[0].reset();
          form.render('select');
          initTable();
          return;
        });
      $('#processCopy')
        .off('click', '#processCopyReset')
        .on('click', '#processCopyReset', function () {
          $('#processCopyScreenSearchScreenForm')[0].reset();
          $('#processCopyQueryForm')[0].reset();
          $('#processCopyQueryForm [name="launchDeptCode"]').val('');
          form.render('select');
          initTable();
          return;
        });
      //查询条件
      function getQuery() {
        var search = [];
        var opt = [];
        switch (handleStatus * 1) {
          case 1:
            search = $('#processMyDealQueryForm').serializeArray();
            opt = $('#processMyDealScreenSearchScreenForm').serializeArray();
            break;
          case 2:
            search = $('#processHasDealQueryForm').serializeArray();
            opt = $('#processHasDealScreenSearchScreenForm').serializeArray();
            break;
          case 3:
            search = $('#processMyStartQueryForm').serializeArray();
            opt = $('#processInitiatedByMeSearchScreenForm').serializeArray();
            break;
          case 4:
            search = $('#processCopyQueryForm').serializeArray();
            opt = $('#processCopyScreenSearchScreenForm').serializeArray();
            break;
          default:
            break;
        }
        var data = {};
        for (var i in search) {
          opt.push(search[i]);
        }
        for (var i in opt) {
          data[opt[i].name] = opt[i].value;
        }
        return data;
      }
      function getIsBatchApprove() {
        let workFlowNo = $('#processMyDealQueryForm [name="workflowNo"]').val();
        if (workFlowNo != '') {
          $.ajax({
            url: `ts-workflow/workflow/definition/getIsBatchApprove?workflowNo=${workFlowNo}`,
            method: 'get',
            success: function (res) {
              if(res.object){
                $('#processBox #transferAllDo').removeClass('none');
              }else{
                $('#processBox #transferAllDo').addClass('none');
              }
            },
          });
        }
      } 
      //表格初始化
      function initTable() {
        let isChecked = $('#processBox [name="followId"]').is(':checked');
        let followId = isChecked ? 'Y' : '';
        let formDomDic = {
          1: '#processMyDealQueryForm',
          2: '#processHasDealQueryForm',
          3: '#processMyStartQueryForm',
          4: '#processCopyQueryForm',
        };

        $(formDomDic[handleStatus]).find('[name=followId]').val(followId);

        switch (handleStatus * 1) {
          case 1:
            if (waitDealTable) {
              waitDealTable.refresh();
            } else {
              waitDealTableFun();
            }
            selTable = waitDealTable;
            selTableDom = 'grid-table-processMyDeal';

            break;
          case 2:
            if (hasDealTable) {
              hasDealTable.refresh();
            } else {
              hasDealTableFun();
            }
            selTable = hasDealTable;
            selTableDom = 'grid-table-processHasDeal';

            break;
          case 3:
            if (myStartTable) {
              myStartTable.refresh();
            } else {
              myStartTableFun();
            }
            selTable = myStartTable;
            selTableDom = 'grid-table-processMyStart';

            break;
          case 4:
            if (copyTable) {
              copyTable.refresh();
            } else {
              copyTableFun();
            }
            selTable = copyTable;
            selTableDom = 'grid-table-processCopy';
            break;
          default:
            break;
        }

        let arr = sortableFields[handleStatus] || [];
        arr.forEach(fieldName => {
          let domName = `#processBox #${selTableDom}_${fieldName}`;
          $(domName).find('.ui-jqgrid-sortable span.s-ico').attr('style', 'display: initial !important')
        })

        handleSetMouseenterGetInfo();
        getToDayNum();
      }

      function processRef() {
        Event.create('indexProcess').trigger('change');
        initTable();
      }

      //TODO 弹窗处理
      // 删除
      $('#processBox')
        .off('click', '.delWf')
        .on('click', '.delWf', function () {
          var rowId = $(this).attr('row-id'),
            rowStatus = $(this).attr('row-status'),
            $thisType = $(this).attr('type');
          var rowData = selTable.getSourceRowData(rowId);
          layer.confirm(
            '确定要删除该流程数据吗?',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              $.ajax({
                url: '/ts-form/form/api/deleteById',
                data: {
                  businessId: rowData.businessId,
                  wfDefinitionId: rowData.wfDefinitionId,
                },
                type: 'post',
                success: function (res) {
                  if (res.success) {
                    initTable();
                    initTree();
                    layer.msg('删除成功');
                    layer.close(index);
                  } else {
                    layer.msg(res.message || '删除失败');
                  }
                },
              });
            }
          );
        });
      //再次发起
      $('#processBox')
        .off('click', '.startWfAgain')
        .on('click', '.startWfAgain', function () {
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData();
          $.ajax({
            method: 'get',
            url:
              '' +
              '/ts-workflow/workflow/definition/code/' +
              rowData.workflowNo,
            success: function (res) {
              if (res.success) {
                if (res.object && res.object.isNormal == 'N') {
                  // var uri = encodeURI('/otherView/processAudit/index.html?workflowNo=' + rowData.workflowNo + '&businessId=' + rowData.businessId + '&wfInstanceId=' + rowData.wfInstanceId + '&taskId=' + rowData.taskId + '&workflowNumber=' + rowData.workflowNumber + '&currentStepName=' + rowData.currentStepName + '&currentStepNo=' + rowData.currentStepNo + '&role=self&type=restart')
                  var son = common.processDeal.startAgain(rowData);
                  windowsOpen.push(son);
                  common.openedWindow.push(son);
                } else if (res.object && res.object.isNormal == 'Y') {
                  var opt = {
                    data: {
                      id: rowData.businessId,
                      workflowNumber: rowData.workflowNumber,
                      wfInstanceId: rowData.wfInstanceId,
                      stepNo: rowData.currentStepNo,
                    },
                    trasen: trasenTable,
                    title: '再次发起',
                    ref: processRef,
                  };
                  $.quoteFun(res.object.examinePageUrl, opt);
                } else {
                  layer.msg(res.message || '获取流程信息失败');
                }
              } else {
                layer.msg(res.message || '查看失败');
              }
            },
          });
        });
      //重新发起
      $('#processBox')
        .off('click', '.restartWf')
        .on('click', '.restartWf', function () {
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData();
          $.ajax({
            method: 'get',
            url:
              '' +
              '/ts-workflow/workflow/definition/code/' +
              rowData.workflowNo,
            success: function (res) {
              if (res.success) {
                if (res.object && res.object.isNormal == 'N') {
                  // var uri = encodeURI('/otherView/processAudit/index.html?workflowNo=' + rowData.workflowNo + '&businessId=' + rowData.businessId + '&wfInstanceId=' + rowData.wfInstanceId + '&taskId=' + rowData.taskId + '&workflowNumber=' + rowData.workflowNumber + '&currentStepName=' + rowData.currentStepName + '&currentStepNo=' + rowData.currentStepNo + '&role=self&type=restart')
                  var son = common.processDeal.restart(rowData);
                  windowsOpen.push(son);
                  common.openedWindow.push(son);
                } else if (res.object && res.object.isNormal == 'Y') {
                  var opt = {
                    data: {
                      id: rowData.businessId,
                      taskId: rowData.taskId,
                      workId: rowData.wfInstanceId,
                      workflowNumber: rowData.workflowNumber,
                      wfInstanceId: rowData.wfInstanceId,
                      stepNo: rowData.currentStepNo,
                    },
                    trasen: trasenTable,
                    title: '重新发起',
                    ref: processRef,
                  };
                  $.quoteFun(res.object.examinePageUrl, opt);
                } else {
                  layer.msg(res.message || '获取流程信息失败');
                }
              } else {
                layer.msg(res.message || '查看失败');
              }
            },
          });
        });
      //关注
      $('#processBox')
        .off('click', '.follow')
        .on('click', '.follow', function (e) {
          e.stopPropagation();
          let wfInstanceId = $(this).attr('row-id');
          let data = {
            wfInstanceId,
          };
          $.ajax({
            method: 'post',
            contentType: 'application/json',
            data: JSON.stringify(data),
            dataType: 'json',
            url: '/ts-workflow/api/taskFollow/save',
            success: function (res) {
              if (res.statusCode === 200 && res.success) {
                initTable();
                layer.msg('操作成功!');
              } else {
                layer.msg(res.message || '操作失败');
              }
            },
          });
        });
      //取消关注
      $('#processBox')
        .off('click', '.cancelfollow')
        .on('click', '.cancelfollow', function (e) {
          e.stopPropagation();
          let id = $(this).attr('row-id');
          $.ajax({
            method: 'post',
            url: '/ts-workflow/api/taskFollow/delete/' + id,
            success: function (res) {
              if (res.statusCode === 200 && res.success) {
                initTable();
                layer.msg('操作成功!');
              } else {
                layer.msg(res.message || '操作失败');
              }
            },
          });
        });
      //处理
      $('#processBox')
        .off('click', '.dealWf')
        .on('click', '.dealWf', function (e) {
          e.stopPropagation();
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData(rowId);
          var type = $(this).attr('type');
          $.ajax({
            method: 'get',
            url:
              '' +
              '/ts-workflow/workflow/definition/code/' +
              rowData.workflowNo,
            success: function (res) {
              if (res.success) {
                if (res.object && res.object.isNormal == 'N') {
                  var uri = '';
                  var son = '';
                  var isHideContent =
                    res.object.isHideContent == 1 &&
                    rowData.createUser == common.userInfo.usercode
                      ? 1
                      : 0;
                  if (type == 'restart') {
                    // var uri = encodeURI('/otherView/processAudit/index.html?workflowNo=' + rowData.workflowNo + '&businessId=' + rowData.businessId + '&wfInstanceId=' + rowData.wfInstanceId + '&taskId=' + rowData.taskId + '&workflowNumber=' + rowData.workflowNumber + '&currentStepName=' + rowData.currentStepName + '&currentStepNo=' + rowData.currentStepNo + '&role=self&type=restart')
                    son = common.processDeal.restart(rowData);
                  } else if (type == 'countersign') {
                    //判断是否为加签审批
                    son = common.processDeal.countersign(
                      rowData,
                      isHideContent
                    );
                  } else {
                    // var uri = encodeURI('/otherView/processAudit/index.html?workflowNo=' + rowData.workflowNo + '&businessId=' + rowData.businessId + '&wfInstanceId=' + rowData.wfInstanceId + '&taskId=' + rowData.taskId + '&workflowNumber=' + rowData.workflowNumber + '&currentStepName=' + rowData.currentStepName + '&currentStepNo=' + rowData.currentStepNo)
                    son = common.processDeal.confirm(rowData, isHideContent);
                  }
                  windowsOpen.push(son);
                  common.openedWindow.push(son);
                } else if (res.object && res.object.isNormal == 'Y') {
                  if (res.object.examinePageUrl.startsWith('/ts-web')) {
                    handleNewFrameJump(1, rowData, res);
                  } else {
                    var opt = {
                      data: {
                        status: rowData.status,
                        id: rowData.businessId,
                        taskId: rowData.taskId,
                        workId: rowData.wfInstanceId,
                        wfInstanceId: rowData.wfInstanceId,
                        workflowNumber: rowData.workflowNumber,
                        currentStepName: rowData.currentStepName,
                        currentStepNo: rowData.currentStepNo,
                        stepNo: rowData.currentStepNo,
                      },
                      rowData: rowData,
                      trasen: trasenTable,
                      title: '流程审批',
                      ref: processRef,
                    };
                    $.quoteFun(res.object.examinePageUrl, opt);
                  }
                } else {
                  layer.msg(res.message || '获取流程信息失败');
                }
              } else {
                layer.msg(res.message || '审批失败');
              }
            },
          });
        });
      //查看
      $('#processBox')
        .off('click', '.seeWf')
        .on('click', '.seeWf', function (e) {
          e.stopPropagation();
          var rowId = $(this).attr('row-id');
          var rowData = selTable.getSourceRowData();
          var rowStatus = rowData.status;
          // 抄送查看后 已读
          if (handleStatus == 4) {
            $.ajax({
              type: 'post',
              async: false,
              url: '/ts-workflow/workflow/task/wfCopyUserRead',
              data: {
                copyId: rowData.copyId || '',
              },
            });
          }

          $.ajax({
            method: 'get',
            url:
              '' +
              '/ts-workflow/workflow/definition/code/' +
              rowData.workflowNo,
            success: function (res) {
              if (res.success) {
                if (res.object && res.object.isNormal == 'N') {
                  var uri = '';
                  var son = '';
                  var currentStepNo = rowData.currentStepNo;
                  var isHideContent =
                    res.object.isHideContent == 1 &&
                    rowData.createUser == common.userInfo.usercode
                      ? 1
                      : 0;
                  if (handleStatus == 1 || handleStatus == 2) {
                    son = common.processDeal.dealSee(
                      rowData,
                      isHideContent,
                      true
                    );
                  } else if (handleStatus == 3) {
                    if (rowStatus == 0) {
                      //草稿
                      uri = encodeURI(
                        '/view-new/processView/start/index.html?' +
                          'workflowNo=' +
                          rowData.workflowNo +
                          '&businessId=' +
                          rowData.businessId +
                          '&currentStepNo=' +
                          'start'
                      );
                      // son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
                      son = window.open(uri);
                      son.openWiner = 1;
                    } else if (rowStatus == 1) {
                      //在办
                      son = common.processDeal.selfSee(rowData, isHideContent);
                    } else {
                      son = common.processDeal.selfSee(rowData, isHideContent);
                    }
                  } else if (handleStatus == 4) {
                    son = common.processDeal.dealSee(
                      rowData,
                      isHideContent,
                      true
                    );
                  }
                  windowsOpen.push(son);
                  common.openedWindow.push(son);
                } else if (res.object && res.object.isNormal == 'Y') {
                  if (res.object.examinePageUrl.startsWith('/ts-web')) {
                    if (res.object.examinePageUrl.indexOf('meet') > -1 && handleStatus == 3) {
                      handleNewFrameJump(null, rowData, res);
                    } else {
                      handleNewFrameJump(2, rowData, res);
                    }
                  } else if (handleStatus == 1 || handleStatus == 2) {
                    var opt = {
                      data: {
                        details: 1,
                        status: rowData.status,
                        id: rowData.businessId,
                        taskId: rowData.taskId,
                        workId: rowData.wfInstanceId,
                        workflowNumber: rowData.workflowNumber,
                        currentStepName: rowData.currentStepName,
                        currentStepNo: rowData.currentStepNo,
                        wfInstanceId: rowData.wfInstanceId,
                        stepNo: rowData.currentStepNo,
                      },
                      rowData: rowData,
                      trasen: trasenTable,
                      title: '查看详情',
                      ref: initTable,
                    };
                    $.quoteFun(res.object.examinePageUrl, opt);
                  } else if (handleStatus == 3) {
                    var opt = {
                      data: {
                        details: 1,
                        id: rowData.businessId,
                        taskId: rowData.taskId,
                        workId: rowData.wfInstanceId,
                        workflowNumber: rowData.workflowNumber,
                        currentStepName: rowData.currentStepName,
                        currentStepNo: rowData.currentStepNo,
                        wfInstanceId: rowData.wfInstanceId,
                        status: rowData.status,
                        stepNo: rowData.currentStepNo,
                      },
                      rowData: rowData,
                      trasen: trasenTable,
                      title: '查看详情',
                      ref: initTable,
                    };
                    $.quoteFun(res.object.examinePageUrl, opt);
                  } else if (handleStatus == 4) {
                    var opt = {
                      data: {
                        details: 1,
                        id: rowData.businessId,
                        taskId: rowData.taskId,
                        status: rowData.status,
                        stepNo: rowData.currentStepNo,
                        wfInstanceId: rowData.wfInstanceId,
                        currentStepName: rowData.currentStepName,
                        currentStepNo: rowData.currentStepNo,
                      },
                      rowData: rowData,
                      trasen: trasenTable,
                      title: '查看详情',
                      ref: initTable,
                    };
                    $.quoteFun(res.object.detailPageUrl, opt);
                  }
                } else {
                  layer.msg(res.message || '获取流程信息失败');
                }
              } else {
                layer.msg(res.message || '失败');
              }
            },
          });
        });
      function handleNewFrameJump(type, data, res) {
        window.dispatchEvent(
          new CustomEvent('sendToNewFrameMessage', {
            detail: {
              type: 'routerChange',
              data: {
                path: res.object.examinePageUrl,
              },
            },
          })
        );
        window.dispatchEvent(
          new CustomEvent('sendToNewFrameMessage', {
            detail: {
              type: 'broadcastInformation',
              data: {
                event: 'messageToastEvent',
                data: {
                  path: res.object.examinePageUrl,
                  businessId: data.businessId,
                  data,
                  type,
                  res,
                },
              },
            },
          })
        );
      }
      //模块监听
      Event.create('toDealList').listen('changeStatus', function (index, data) {
        $('#processBox .processTabs')
          .eq(index)
          .find('[name="status"]')
          .val(data.status);
        form.render('select');
        $('#processBox .oa-nav .oa-nav_item').eq(index).trigger('click');
      });
      Event.create('toDealList').listen('waitDeal', function (index, data) {
        // if (handleStatus == 1) {
        initTable();
        // }
      });
    });
  }
});
