'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form'], function () {
            var form = layui.form;
            //工作流
            opt.div.html(html);
            var options = {
                btn: false,
                edit: false,
                del: false,
                stop: false,
                start: false,
                copy: false,
                collect: false,
                collectN: false,
            };
            options = $.extend({}, options, opt);
            var child_one = opt.div.find('#processType>ul>li').eq(0);
            var child_two = opt.div.find('#processType>ul>li').eq(1);
            var child_three = opt.div.find('#processType>ul>li').eq(2);
            var child_four = opt.div.find('#processType>ul>li').eq(3);
            var childArr = [child_one, child_two, child_three, child_four];
            var width = $('body').outerWidth();
            if (width >= 768 && width < 992) {
                childArr.length = 2;
                //两个
                for (var i = 0; i < opt.data.length; i++) {
                    var itemH = rHtml(opt.data[i]);
                    if (i < 2) {
                        childArr[i].append(itemH);
                    } else {
                        var minIndex = arrMin();
                        childArr[minIndex].append(itemH);
                    }
                }
            } else if (width >= 992 && width < 1200) {
                childArr.length = 3;
                //三个
                for (var i = 0; i < opt.data.length; i++) {
                    var itemH = rHtml(opt.data[i]);
                    if (i < 3) {
                        childArr[i].append(itemH);
                    } else {
                        var minIndex = arrMin();
                        childArr[minIndex].append(itemH);
                    }
                }
            } else if (width >= 1200) {
                childArr.length = 4;
                //四个
                for (var i = 0; i < opt.data.length; i++) {
                    var itemH = rHtml(opt.data[i]);
                    if (i < 4) {
                        childArr[i].append(itemH);
                    } else {
                        var minIndex = arrMin();
                        childArr[minIndex].append(itemH);
                    }
                }
            }

            function arrMin() {
                var arr = [];
                for (var i = 0; i < childArr.length; i++) {
                    arr.push(childArr[i].outerHeight());
                }
                var min = arr[0];
                var index = 0;
                var len = arr.length;
                for (var i = 1; i < len; i++) {
                    if (arr[i] < min) {
                        min = arr[i];
                        index = i;
                    }
                }
                return index;
            }

            function btns(item) {
                var html = '';
                if (options.btn) {
                    html += '<div class="btn">';
                    if (item.userData.isCollection) {
                        if (options.collectN) {
                            html += '<p class="processControl_collectN" code="' + item.code + '" wfId="' + item.id + '"><i class="fa fa-star" aria-hidden="true"></i> </p>';
                        }
                    } else {
                        if (options.collect) {
                            html += '<p class="processControl_collect" code="' + item.code + '" wfId="' + item.id + '"><i class="fa fa-star-o" aria-hidden="true"></i></p>';
                        }
                    }
                    html += '</div>';
                } else {
                    html += '<div class="btn">';
                    if (options.collectN) {
                        html += '<p class="processControl_collectN" code="' + item.code + '" wfId="' + item.id + '"><i class="fa fa-star" aria-hidden="true"></i> </p>';
                    }
                    html += '</div>';
                }
                return html;
            }

            function getTreeItem(arr, code, id) {
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i].id == id) {
                        return arr[i];
                    }
                    if (arr[i].children) {
                        var item = getTreeItem(arr[i].children, code, id);
                        if (item) {
                            return item;
                        }
                    }
                }
            }
            //收藏
            $(opt.div)
                .off('click', '.processControl_collect')
                .on('click', '.processControl_collect', function () {
                    options.collectCallback(getTreeItem(opt.data, $(this).attr('code'), $(this).attr('wfId')));
                });
            //取消收藏
            $(opt.div)
                .off('click', '.processControl_collectN')
                .on('click', '.processControl_collectN', function () {
                    options.collectNCallback(getTreeItem(opt.data, $(this).attr('code'), $(this).attr('wfId')));
                });
            //流程点击
            $(opt.div)
                .off('click', '.template .con')
                .on('click', '.template .con', function () {
                    options.startCallback(getTreeItem(opt.data, $(this).attr('code'), $(this).attr('wfId')));
                });

            function rHtml(item) {
                var html =
                    '<div class="templateItem" color="' +
                    item.userData.color +
                    '"><div class="templateTit" ><span class="icon" style="background:' +
                    item.userData.color +
                    ';"><i class="' +
                    item.userData.icon +
                    '" style="color: #fff;"></i></span>' +
                    item.name +
                    '</div>';
                if (item.children) {
                    html += '<ul class="templateList">';
                    for (var i = 0; i < item.children.length; i++) {
                        html += '<li>';
                        if (item.children[i].children) {
                            html += '<p class="secondTit" style="color:' + item.userData.color + '"><i class="fa fa-circle" aria-hidden="true" ></i>' + item.children[i].name + '</p><ul>';
                            for (var j = 0; j < item.children[i].children.length; j++) {
                                var ss = '';
                                html +=
                                    '<li><div class="template"><span class="con"  code="' +
                                    item.children[i].children[j].code +
                                    '" wfId="' +
                                    item.children[i].children[j].id +
                                    '" title="' +
                                    item.children[i].children[j].name +
                                    '">' +
                                    item.children[i].children[j].name.replace(/\(/, '<b>(').replace(/\)/, ')</b>') +
                                    '</span>' +
                                    btns(item.children[i].children[j]) +
                                    '</div></li>';
                            }
                            html += '</ul>';
                        } else {
                            //分类
                            if (item.children[i].userData.classifyType != 9) {
                                html += '<p class="secondTit" style="color:' + item.userData.color + '"><i class="fa fa-circle" aria-hidden="true" ></i>' + item.children[i].name + '</p>';
                            } else {
                                var ss = '';
                                //流程
                                html +=
                                    '<div class="template"><span class="con"  code="' +
                                    item.children[i].code +
                                    '" wfId="' +
                                    item.children[i].id +
                                    '" title="' +
                                    item.children[i].name +
                                    '">' +
                                    ss +
                                    item.children[i].name.replace(/\(/, '<b>(').replace(/\)/, ')</b>') +
                                    '</span>' +
                                    btns(item.children[i]) +
                                    '</div></p>';
                            }
                        }
                        html += '</li>';
                    }
                    html += '</ul>';
                }
                return html;
            }
        });
    };
});
