'use strict';
/**
 * 人员，部门，群组 选择
 * 参数： {
 * item
 * ref
 * }
 *
 * **/
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(
      ['form', 'table', 'element', 'laydate', 'zTreeSearch'],
      function () {
        var title = opt.data.name.replace(/\(\d+\)/g, '');
        var form = layui.form;
        var element = layui.element;
        var laydate = layui.laydate;
        var zTreeSearch = layui.zTreeSearch;

        var cols = [];
        let childrenTableWidth = {};
        var processDealListTable_DB = null;
        var processDealListTable_ZB = null;
        var processDealListTable_WJ = null;
        var formData = null;
        var time1 = '';
        var hasChildForm = false;
        var wins = layer.open({
          type: 1,
          title: '查阅详情-' + title,
          closeBtn: 1,
          shadeClose: false,
          area: ['100%', '100%'],
          skin: 'yourclass',
          content: html,
          cancel: opt.ref,
          success: function (layero, index) {
            initTree();
            form.render('select');

            $.ajax({
              url: `ts-form/dpTable/getChildFiledList?wfDefinitionId=${opt.data.userData.wfDefinitionId}`,
              method: 'get',
              async: false,
              success: function (res) {
                if (res.success && res.object) {
                  var btnHtmls = '';
                  for (var i = 0; i < res.object.length; i++) {
                    childrenTableWidth[res.object[i].tableId] =
                      res.object[i].tableWidth || 700;

                    btnHtmls += `
                      <button type="button" class="layui-btn childrenFromData" id="childrenFromData" table-id="${res.object[i].tableId}" table-name="${res.object[i].tableName}" fieldName="${res.object[i].fieldName}" title="${res.object[i].tableName}">
                        ${res.object[i].tableName}
                      </button>
                    `;
                  }
                  commonDictCache.getDict('CUSTOM_CODE', function (dict) {
                    let showHandling = false;
                    if (!dict) {
                      showHandling = false;
                    } else {
                      let showHandlingConfig = dict.find(
                        (e) => e.itemCode == 'DISP_WORKFLOW_URGING'
                      );
                      showHandling =
                        showHandlingConfig == undefined ||
                        showHandlingConfig.itemNameValue == 1
                          ? true
                          : false;
                    }
                    if (showHandling) {
                      btnHtmls +=
                        '<button type="button" class="layui-btn" id="childrenFormOneClickReminder">一键催办</button>';
                    }
                  });
                  btnHtmls +=
                    '<button type="button" class="layui-btn" id="childrenFormTableDataExport">导出</button>';
                  $('#processDealListTable .fr').html(btnHtmls);
                } else {
                  layer.msg(res.message || '失败');
                }
              },
            });
            $.ajax({
              url:
                '/ts-form/dpFormTemplate/findById/' +
                opt.data.userData.formId +
                '?tableView=Y',
              method: 'post',
              success: function (res) {
                if (res.success && res.object) {
                  formData = res.object;
                  setCols();
                  setQuery();
                } else {
                  layer.msg(res.message || '失败');
                }
              },
            });
          },
        });
        function setCols() {
          var list = formData.toaFieldSetList;
          cols.push({
            sortable: false,
            label: '操作',
            classes: 'visible',
            width: 60,
            align: 'center',
            formatter: function (cell, opt, row) {
              var data = $(this).jqGrid('getGridParam', 'postData');
              if (data.status == 1) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                btns +=
                  '<button  class="layui-btn  enforceCancel"  row-id="' +
                  opt.rowId +
                  '"><i class="fa fa-power-off deal_icon" aria-hidden="true"></i>强制结束</button> ';
                btns +=
                  '<button  class="layui-btn  delete" row-id="' +
                  opt.rowId +
                  '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除</button> ';
                // btns += '<button  class="layui-btn  cancel" row-id="' + opt.rowId + '"><i class="fa fa-eye deal_icon" aria-hidden="true"></i>查看</button>'
                html += btns + '</div></div>';
                return html;
              } else {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                if (row.current_step_name == '办理完毕') {
                  btns +=
                    '<button  class="layui-btn  placeOnFile"  row-id="' +
                    opt.rowId +
                    '"><i class="fa fa-archive deal_icon" aria-hidden="true"></i>归档</button> ';
                }
                btns +=
                  '<button  class="layui-btn  destruction"  row-id="' +
                  opt.rowId +
                  '"><i class="fa fa-power-off deal_icon" aria-hidden="true"></i>销毁</button> ';
                btns +=
                  '<button  class="layui-btn  delete2" row-id="' +
                  opt.rowId +
                  '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除</button> ';
                html += btns + '</div></div>';
                return html;
                // return '<button class="layui-btn  see" row-id="' + opt.rowId + '">查看</button>'
              }
            },
          });
          cols.push({
            sortable: false,
            label: '是否打印',
            align: 'center',
            name: 'isPrint',
            width: 80,
            formatter: function (cell, opt, row) {
              if (cell > 0) {
                return '<span style="color:#4caf50">已打印</span>';
              } else {
                return '<span style="color:red">未打印</span>';
              }
            },
          });
          cols.push({
            sortable: true,
            label: '当前节点',
            align: 'center',
            width: 120,
            name: 'current_step_name',
            index: 'current_step_name',
            formatter: function (cell, opt, row) {
              var data = $(this).jqGrid('getGridParam', 'postData');
              if (data.status == 2) {
                if (cell == '办理完毕') {
                  return '<span style="color:#4caf50">' + cell + '</span>';
                } else {
                  return '<span style="color:red">' + cell + '</span>';
                }
              } else {
                return '<span style="color:#4caf50">' + cell + '</span>';
              }
            },
          });

          for (var i = 0; i < list.length; i++) {
            if (list[i].isTableField) {
              let width = 150;
              if (list[i].fieldType === 'childForm') {
                width = childrenTableWidth[list[i].tableId] || 700;
              }
              cols.push({
                label: list[i].showName,
                // sortable: false,
                name: list[i].fieldName,
                align: 'center',
                width,
                formatter: (function (json) {
                  if (
                    json.fieldType == 'personChose' ||
                    json.fieldType == 'deptChose'
                  ) {
                    return function (cell, opt, row) {
                      return cell ? cell.split('--')[0] : '';
                    };
                  } else if (json.fieldType == 'interworkCom') {
                    return function (cell, opt, row) {
                      var arr = cell ? JSON.parse(cell) : [];
                      var html = '';
                      for (var i = 0; i < arr.length; i++) {
                        html += arr[i].name;
                      }
                      return html;
                    };
                  } else if (json.fieldType == 'interworkSick') {
                    return function (cell, opt, row) {
                      var item = cell ? JSON.parse(cell) : {};
                      var sexObj = {
                        1: '男',
                        2: '女',
                        9: '未知',
                      };
                      var html = '';
                      item.after.name &&
                        (html +=
                          '<p>【姓名】 修改前：' +
                          (item.before.name || '') +
                          '； 修改后：' +
                          item.after.name +
                          '</p>');
                      item.after.sex &&
                        (html +=
                          '<p>【性别】 修改前：' +
                          (sexObj[item.before.sex] || item.before.sex) +
                          '； 修改后：' +
                          sexObj[item.after.sex] +
                          '</p>');
                      item.after.phone &&
                        (html +=
                          '<p>【联系电话】 修改前：' +
                          (item.before.phone || '') +
                          '； 修改后：' +
                          item.after.phone +
                          '</p>');
                      item.after.idcard &&
                        (html +=
                          '<p>【身份证号码】 修改前：' +
                          (item.before.idcard || '') +
                          '； 修改后：' +
                          item.after.idcard +
                          '</p>');
                      return html;
                    };
                  } else if (json.fieldType == 'interworkSettle') {
                    return function (cell, opt, row) {
                      var item = cell ? JSON.parse(cell) : {};
                      var html = '';
                      var html =
                        item.zy +
                        '；' +
                        new Date(item.finishDate).format('yyyy年MM月dd日') +
                        '结算,应退' +
                        item.recedeFee +
                        '元';
                      return html;
                    };
                  } else if (json.fieldType == 'interworkPay') {
                    return function (cell, opt, row) {
                      var items = cell ? JSON.parse(cell) : [];
                      var html = '';
                      for (var i = 0; i < items.length; i++) {
                        var item = items[i];
                        html +=
                          '<p>' +
                          item.name +
                          '；' +
                          new Date(item.arriveDate).format('yyyy年MM月dd日') +
                          item.payModeName +
                          item.payValues +
                          '元</p>';
                      }
                      return html;
                    };
                  } else if (json.fieldType == 'file') {
                    return function (cell, opt, row) {
                      return cell
                        ? `<p 
                            class="fileLink fileWf" 
                            style="cursor:pointer"
                            row-id="${cell}"
                          >
                            <img 
                              width="16" 
                              heigth="16" 
                              title="附件" 
                              src="/static/img/other/mail_file.svg" 
                              style="margin-right:5px"
                            />
                            附件
                          </p>`
                        : '';
                    };
                  } else if (json.fieldType == 'childForm') {
                    return function (cell, opt, row) {
                      let htmlStr = '';
                      $.ajax({
                        url:
                          'ts-form/dpTable/getChildDataByField?fieldStr=' +
                          cell,
                        method: 'get',
                        async: false,
                        success: function (res) {
                          if (res.success && res.object) {
                            let cFormTableHead = res.object.childFieldList.map(
                              (item = {}, index) => {
                                let { fieldWidth = '' } = item;
                                let minWidth = item.fieldWidth
                                  ? `style="width: ${fieldWidth}px"`
                                  : '';
                                return `<th ${minWidth}>
                                  <span class="cell">${item.remark}</span>
                                </th>`;
                              }
                            );
                            let cFormTableBody = res.object.childDataList.map(
                              (row) => {
                                return computeNewChildFormItem(
                                  res.object.childFieldList,
                                  row
                                );
                              }
                            );
                            htmlStr = `<div class="child-form-table-box" style="padding: 6px 4px">
                              <table
                                data-child-form-key="${json.keyId}"
                                class="child-form-table"
                              >
                                <thead>
                                  <tr>
                                    ${cFormTableHead.join('')}
                                  </tr>
                                </thead>
                                <tbody>${cFormTableBody.join('')}</tbody>
                              </table></div>`;
                          }
                        },
                      });
                      return htmlStr;
                    };
                  }
                })(list[i]),
              });
            }
          }

          cols.push({
            hidden: true,
            name: 'WF_INSTANCE_ID',
          });
          cols.push({
            sortable: false,
            label: '发起人',
            align: 'center',
            width: 80,
            name: 'CREATE_USER_NAME',
          });
          cols.push({
            sortable: true,
            width: 170,
            label: '发起时间',
            align: 'center',
            name: 'instCreateDate',
          });
          cols.push({
            sortable: true,
            width: 170,
            label: '更新时间',
            align: 'center',
            name: 'INST_UPDATE_DATE',
            index: 'UPDATE_DATE',
          });
          cols.push({
            sortable: true,
            label: '办结时间',
            align: 'center',
            width: 170,
            name: 'WF_FINISHED_DATE',
            formatter: function (cell, opt, row) {
              var data = $(this).jqGrid('getGridParam', 'postData');
              if (data.status == 1) {
                return '';
              } else {
                return cell || '';
              }
            },
          });
          cols.push({
            sortable: true,
            label: '流程时长',
            align: 'center',
            width: 170,
            name: 'WF_FINISHED_LONG_TIME',
            formatter: function (cell, opt, row) {
              var data = $(this).jqGrid('getGridParam', 'postData');
              if (data.status == 1) {
                let longTime = getLongTime(
                  row.instCreateDate,
                  row.INST_UPDATE_DATE
                );
                return longTime;
              } else {
                let longTime = getLongTime(
                  row.instCreateDate,
                  row.WF_FINISHED_DATE
                );
                return longTime;
              }
            },
          });
          refreshTable();
        }

        function getLongTime(start, end) {
          var d1 = new Date(start);
          var d2 = new Date(end);
          var dateDiff = d2 - d1; //时间差的毫秒数
          var days = Math.floor(dateDiff / (24 * 3600 * 1000)); //计算出相差天数
          var leave1 = dateDiff % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
          var hours = Math.floor(leave1 / (3600 * 1000)); //计算出小时数
          //计算相差分钟数
          var leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
          var minutes = Math.floor(leave2 / (60 * 1000)); //计算相差分钟数
          //计算相差秒数
          var leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
          var seconds = Math.round(leave3 / 1000);
          let longTimeArr = [];
          if (days) longTimeArr.push(`${days}天`);
          if (hours) longTimeArr.push(`${hours}小时`);
          if (minutes) longTimeArr.push(`${minutes}分钟`);
          if (!longTimeArr.length) longTimeArr.push(`${seconds}秒`);
          return longTimeArr.join('');
        }
        function setQuery() {
          var list = common.deepClone(formData.toaFieldSetList);
          list.sort((a, b) => {
            return a.querySeq - b.querySeq;
          });
          var str = '';
          var hiddenList = [];
          if (list.some((filed) => filed.bindingField)) {
            hiddenList = list
              .filter((filed) => filed.bindingField)
              .map((filed) => filed.bindingField);
          }
          for (var i = 0; i < list.length; i++) {
            if (!list[i].isQuery || hiddenList.includes(list[i].fieldName)) {
              continue;
            }
            if (list[i].fieldType == 'date') {
              // str +=
              //   '<div class="layui-col-md3" style="margin-bottom:5px">' +
              //   '<div class="layui-col-md3">' +
              //   '<div class="title">' +
              //   list[i].showName +
              //   '</div>' +
              //   ' </div>' +
              //   ' <div class="layui-col-md7">' +
              //   ' <input type="text" datetype="' +
              //   list[i].fieldType +
              //   '"  data-name="' +
              //   list[i].fieldName +
              //   (list[i].bindingField
              //     ? '" data-end-name="' + list[i].bindingField
              //     : '') +
              //   '" format="' +
              //   (list[i].dataFormat || '') +
              //   '" class="layui-input oa-date" search-input="dealList-search" />' +
              //   '<input name="start_' +
              //   list[i].fieldName +
              //   '" type="hidden" class="layui-input layDate" />' +
              //   '<input name="end_' +
              //   (list[i].bindingField || list[i].fieldName) +
              //   '" type="hidden" class="layui-input layDate" />' +
              //   ' </div>' +
              //   '  </div>';
              let endName = list[i].fieldName;
              if (list[i].bindingField && list[i].bindingField != 'undefined') {
                endName = list[i].bindingField;
              }
              str += `
                    <div class="layui-col-md3" style="margin-bottom:5px">
                      <div class="layui-col-md3">
                        <div class="title">
                        ${list[i].showName}
                        </div>
                      </div>
                      <div class="">
                          <input
                            type="text"
                            style="display: inline-block; width: 120px"
                            name="start_${list[i].fieldName}"
                            id="start_${list[i].fieldName}"
                            format="${list[i].dataFormat || ''}"
                            search-input="dealList-search"
                            class="layui-input laydate-date"
                            placeholder="开始日期"
                          />
                        <span>-</span>
                        <input
                          type="text"
                          style="display: inline-block; width: 120px"
                          name="end_${endName}"
                          id="end_${endName}"
                          format="${list[i].dataFormat || ''}"
                          search-input="dealList-search"
                          class="layui-input laydate-date"
                          placeholder="结束日期"
                        />
                      </div>
                    </div>
              `;
            } else {
              str +=
                '<div class="layui-col-md3" style="margin-bottom:5px">' +
                '<div class="layui-col-md3">' +
                '<div class="title">' +
                list[i].showName +
                '</div>' +
                ' </div>' +
                ' <div class="layui-col-md7">' +
                ' <input type="text" name="' +
                list[i].fieldName +
                '" class="layui-input" placeholder="" search-input="dealList-search" />' +
                ' </div>' +
                '  </div>';
            }
          }
          if (str) {
            $('#processDealListTable #processArchivesScreen').removeClass(
              'none'
            );
            // $('#processDealListTable .screen-box').removeClass('none')
            $('#processDealListTable #processDealListScreenForm').append(str);
          }
          setTimeout(function () {
            initDateInp();
          });
          $.each($('#processDealListTable .laydate-date'), function (i, v) {
            var format = $(this).attr('format') || 'yyyy-MM-dd';
            var type = {
              'yyyy-MM': 'month',
              'yyyy-MM-dd': 'date',
              'yyyy-MM-dd HH:mm': 'datetime',
              'yyyy-MM-dd HH:mm:ss': 'datetime',
            };
            var classes = {
              'yyyy-MM-dd HH:mm': 'time_Hm',
            };
            laydate.render({
              elem: v,
              format: format,
              type: type[format],
              classes: classes[format],
              trigger: 'click',
            });
          });
          // $.each($('#processDealListTable .oa-date'), function (i, n) {
          //   var name = $(this).attr('data-name');
          //   var endName = $(this).attr('data-end-name');
          //   var format = $(this).attr('format') || 'yyyy-MM-dd';
          //   var type = {
          //     'yyyy-MM': 'month',
          //     'yyyy-MM-dd': 'date',
          //     'yyyy-MM-dd HH:mm': 'datetime',
          //     'yyyy-MM-dd HH:mm:ss': 'datetime',
          //   };
          //   var classes = {
          //     'yyyy-MM-dd HH:mm': 'time_Hm',
          //   };
          //   laydate.render({
          //     elem: this,
          //     trigger: 'click',
          //     range: '~',
          //     format: format,
          //     type: type[format],
          //     classes: classes[format],
          //     done: function (value) {
          //       var dateArr = value.split(' ~ ');
          //       $('#processDealListTable [name="start_' + name + '"]').val(
          //         dateArr[0]
          //       );
          //       $(
          //         '#processDealListTable [name="end_' + (endName || name) + '"]'
          //       ).val(dateArr[1]);
          //     },
          //   });
          // });
        }
        function computeNewChildFormItem(columns = [], rowData = {}) {
          let tdDom = columns.map((item, index) => {
            let {
                remark: label = index,
                fieldName: value = index,
                fieldType = 'VARCHAR',
              } = item,
              inpVal = rowData[value] || '',
              content = '';
            if (!['FILE'].includes(fieldType)) {
              content = `<div style="text-align:center" data-key-name="${value}" title="${inpVal}">${inpVal}</div>`;
            } else {
              if (inpVal) {
                content = renderFileFiled(inpVal);
              }
            }
            return `<td data-child-form-item-name="${value}">${content}</td>`;
          });
          return `<tr>${tdDom.join('')}</tr>`;
        }

        function renderFileFiled(businessId) {
          let fileDom = [];
          $.ajax({
            url:
              '/ts-basics-bottom/fileAttachment/getFileAttachmentByBusinessId?businessId=' +
              businessId,
            type: 'get',
            async: false,
            success: (res) => {
              if (res.success == false) {
                layer.msg(res.message || '附件获取失败');
                return;
              }
              let fileList = res.object.map((file) => {
                let { id, originalName, fileName, realPath, fileSize } = file;
                return {
                  id,
                  originalName,
                  fileName,
                  fileUrl: realPath,
                  fileSize,
                };
              });
              fileDom = renderFileList(fileList);
            },
          });
          return `<ul class="child-form-file-list">${fileDom.join('')}</ul>`;
        }

        function renderFileList(fileList) {
          return fileList.map((file, index) => {
            var imgClass = common.isImg(file.fileName)
              ? 'viewerImg'
              : 'viewerDoc2';
            return `<li class="child-form-file-item">
                <a class="fileDown" href="${file.fileUrl}">
                  ${file.originalName}
                </a>
                <span class="preview ${imgClass}"
                  file-type="${imgClass}"
                  fileUrl="${file.fileUrl}" 
                  fileId="${file.id}" 
                  fileName="${file.fileName}"
                >预览</span>
              </li>`;
          });
        }

        function initTree() {
          zTreeSearch.init('#dealListForm #processDealListCreateDpetName', {
            url: common.url + '/ts-basics-bottom/organization/getTree',
            type: 'post',
            checkbox: true,
            condition: 'name',
            choice: true,
            choiceType: 's',
            allCheckVal: 'Y',
            searchCheckedAll: false,
            ztreeInitFun: function (treeObj) {
              let ids = $('#dealListForm [name="createDpetCode"]').val();
              if (!ids) return;
              ids
                .split(',')
                .filter((id) => id)
                .map((id) => {
                  let node = treeObj.getNodesByParam('id', id)[0];
                  if (!node) return;
                  treeObj.checkNode(node, true, true);
                  node.children && treeObj.expandNode(node, true);
                  let parentNode = node.getParentNode();
                  while (parentNode) {
                    treeObj.expandNode(parentNode, true);
                    parentNode = parentNode.getParentNode();
                  }
                });
            },
            clear: function () {
              $('#dealListForm [name="createDpetCode"]').val('');
            },
            zTreeChoice: function (nodes) {
              // 回调   treeId：树id   treeNode：点击子节点的数据  choice不为true生效
              if (nodes) {
                var ids = '';
                for (var i = 0; i < nodes.length; i++) {
                  ids += nodes[i].id + ',';
                }
                $('#dealListForm [name="createDpetCode"]').val(ids);
              }
            },
          });
        }
        var windowsOpen = [];

        var loop = setInterval(function () {
          //监听子页面关闭事件,轮询时间1000毫秒
          for (var i = windowsOpen.length - 1; i >= 0; i--) {
            if (windowsOpen[i].closed) {
              if (windowsOpen[i].openWiner != undefined) {
                refreshTable();
              }
              var openedWindowIndex = common.openedWindow.findIndex((item) => {
                windowsOpen[i].name = item.name;
              });
              if (openedWindowIndex != -1)
                common.openedWindow.splice(openedWindowIndex, 1);
              windowsOpen.splice(i, 1);
              break;
            }
          }
        }, 1000);
        var queryMap = {
          queryMap: {},
        };
        $.each($('#processDealListTable .laydate-date'), function (i, v) {
          laydate.render({
            elem: v,
            trigger: 'click',
          });
        });
        // laydate.render({
        //   elem: '#processDealListRangeDate',
        //   type: 'date',
        //   range: '~',
        //   done: function (value) {
        //     var dateArr = value.split(' ~ ');
        //     $('#processDealListCreateStartDate').val(dateArr[0]);
        //     $('#processDealListCreateEndDate').val(dateArr[1]);
        //   },
        // });
        // laydate.render({
        //   elem: '#wfFinishedRangeDate',
        //   type: 'date',
        //   range: '~',
        //   done: function (value) {
        //     var dateArr = value.split(' ~ ');
        //     $('#processDealListWfFinishedStartDate').val(dateArr[0]);
        //     $('#processDealListWfFinishedEndDate').val(dateArr[1]);
        //   },
        // });
        // laydate.render({
        //     elem: $('#processDealListTable [name="wfFinishedStartDate"]')[0],
        //     type: 'date',
        //     trigger: 'click',
        // });
        // laydate.render({
        //     elem: $('#processDealListTable [name="wfFinishedEndDate"]')[0],
        //     type: 'date',
        //     trigger: 'click',
        // });
        // laydate.render({
        //     elem: $('#processDealListTable [name="start_CREATE_DATE"]')[0],
        //     type: 'date',
        //     trigger: 'click',
        // });
        // laydate.render({
        //     elem: $('#processDealListTable [name="end_CREATE_DATE"]')[0],
        //     type: 'date',
        //     trigger: 'click',
        // });
        //tab切换
        var status = 1;
        $('#processDealListTable')
          .off('click', '.oa-nav_item')
          .on('click', '.oa-nav_item', function () {
            $(this).addClass('active').siblings().removeClass('active');
            status = $(this).index() + 1;
            if (status == 1) {
              $('#processDealListTable [name="wfStatus"]')
                .closest('.shell-search-box')
                .addClass('none');
              $('#processDealListTable [name="isPrint"]')
                .closest('.shell-search-box')
                .addClass('none');

              $('#childrenFormOneClickReminder').show();
            } else {
              $('#processDealListTable [name="wfStatus"]')
                .closest('.shell-search-box')
                .removeClass('none');
              // if(['yysfybjy', 'smxzyyy'].includes(common.globalSetting.orgCode)){
              // }
              $('#processDealListWfStatus')
                .find('option[value=2]')
                .attr('selected', true);
              $('#processDealListWfStatus + .layui-form-select input').text(
                '办理完毕'
              );
              $('#processDealListWfStatus + .layui-form-select input').val(
                '办理完毕'
              );
              $('#processDealListTable [name="isPrint"]')
                .closest('.shell-search-box')
                .removeClass('none');

              $('#childrenFormOneClickReminder').hide();
            }
            $('#processDealListTable .trasen-con-box')
              .addClass('none')
              .eq($(this).index())
              .removeClass('none');
            refreshTable();
          });

        function refreshTable() {
          if (status == 1) {
            processDealListTable_ZB
              ? processDealListTable_ZB.refresh()
              : initTable();
            processDealListTable_ZB.hideCol('isPrint');
            processDealListTable_ZB.hideCol('WF_FINISHED_DATE');
          } else {
            processDealListTable_WJ
              ? processDealListTable_WJ.refresh()
              : initTableTwo();
            processDealListTable_WJ.showCol('isPrint');
            processDealListTable_WJ.showCol('WF_FINISHED_DATE');
          }
        }

        //搜索
        form.on('submit(processDealListTablesSearch)', function (data) {
          queryMap.wfFinishedStartDate = data.field.wfFinishedStartDate;
          queryMap.wfFinishedEndDate = data.field.wfFinishedEndDate;
          queryMap.wfStatus = data.field.wfStatus;
          queryMap.isPrint = data.field.isPrint;
          queryMap.createDpetCode = data.field.createDpetCode;
          delete data.field.wfFinishedEndDate;
          delete data.field.wfFinishedStartDate;
          delete data.field.wfStatus;
          delete data.field.isPrint;
          delete data.field.createDpetCode;
          var arr = $('#processDealListScreenForm').serializeArray();
          queryMap.queryMap = data.field;
          for (var i in arr) {
            queryMap.queryMap[arr[i].name] = arr[i].value;
          }
          refreshTable();
        });
        $('#processDealListScreenSearch')
          .off('click')
          .on('click', function () {
            $('[lay-filter="processDealListTablesSearch"]').trigger('click');
          });
        $('#processDealListScreenReset,#processDealListResetBtn')
          .off('click')
          .on('click', function () {
            $('#processDealListCreateUserName').val('');
            $('#processDealListCreateDpetName').val('');
            $('#processDealListCreateDpetCode').val('');
            $('#processDealListRangeDate').val('');
            $('#processDealListCreateStartDate').val('');
            $('#processDealListCreateEndDate').val('');
            $('#wfFinishedRangeDate').val('');
            $('#processDealListWfFinishedStartDate').val('');
            $('#processDealListWfFinishedEndDate').val('');
            $('#processDealListWfStatus').val('');
            $('#processDealListIsPrint').val('');
            $('#processDealListScreenForm')[0].reset();
            $('#processDealListScreenForm').find('input').val('');
            $('[lay-filter="processDealListTablesSearch"]').trigger('click');
          });

        function initDateInp() {
          var dateList = $('#processDealListTable [datetype="DATETIME"]');
          for (var i = 0; i < dateList.length; i++) {
            laydate.render({
              elem: dateList[i],
              type: 'date',
            });
          }
        }
        //初始化表格
        function initTable() {
          processDealListTable_ZB = new $.trasenTable(
            'processDealListTable_ZB',
            {
              url: '/ts-form/form/api/getDataList',
              datatype: 'json',
              mtype: 'post',
              pager: 'processDealListTable_ZB_pager',
              rownumbers: false,
              multiselect: true,
              // shrinkToFit: true,
              postData: {
                status: 1,
                workflowNo: opt.data.code,
                wfDefinitionId: opt.data.userData.wfDefinitionId,
              },
              onSelectRow: function (rowid, status, data) {
                var $target = $(data.target);
                // 如果点击的是复选框，阻止行选择
                if (!$target.is(':checkbox')) {
                  setTimeout(function () {
                    var fileIds = $(data.target).attr('row-id');
                    var fileType = $(data.target).attr('file-type');
                    if (fileIds) {
                      fileWf(fileIds);
                    } else if (fileType) {
                      if (fileType == 'viewerImg') {
                        var imgElems = $(data.target)
                          .closest('.child-form-file-list')
                          .find('.preview.viewerImg');
                        let imgList = [];
                        for (var i = 0; i < imgElems.length; i++) {
                          imgList.push({
                            fileUrl: $(imgElems[i]).attr('fileUrl'),
                            fileName: $(imgElems[i]).attr('fileName'),
                          });
                        }
                        common.viewerImgBase(
                          imgList,
                          $(data.target).attr('fileUrl')
                        );
                      } else {
                        var fileUrl = $(data.target).attr('fileUrl');
                        var fileName = $(data.target).attr('fileName');
                        common.viewerDocBase(fileUrl, fileName);
                      }
                    } else {
                      seeWf(rowid);
                    }
                  });
                }
              },
              colModel: cols,
              rowList: [20, 100, 200, 500, 1000, 2000, 10000],
              rowNum: 20,
              buidQueryParams: function () {
                return queryMap;
              },
              gridComplete: function () {
                var childFormTable = $('#processDealListTable_ZB').find(
                  'tbody .child-form-table-box'
                );
                for (var i = 0; i < childFormTable.length; i++) {
                  var item = $(childFormTable[i]).parent();
                  $(item).attr('title', '');
                }
              },
            }
          );
        }
        function initTableTwo() {
          let param = {
            status: 2,
            workflowNo: opt.data.code,
            wfDefinitionId: opt.data.userData.wfDefinitionId,
          };
          param.wfStatus = '2';
          // if(common.globalSetting.orgCode === 'yysfybjy'){
          // }
          processDealListTable_WJ = new $.trasenTable(
            'processDealListTable_WJ',
            {
              url: '/ts-form/form/api/getDataList',
              datatype: 'json',
              mtype: 'post',
              pager: 'processDealListTable_WJ_pager',
              rownumbers: false,
              // shrinkToFit: true,
              postData: param,
              rowList: [100, 200, 500, 1000, 2000, 10000],
              onSelectRow: function (rowid, status, data) {
                setTimeout(function () {
                  var fileIds = $(data.target).attr('row-id');
                  var fileType = $(data.target).attr('file-type');
                  if (fileIds) {
                    fileWf(fileIds);
                  } else if (fileType) {
                    if (fileType == 'viewerImg') {
                      var imgElems = $(data.target)
                        .closest('.child-form-file-list')
                        .find('.preview.viewerImg');
                      let imgList = [];
                      for (var i = 0; i < imgElems.length; i++) {
                        imgList.push({
                          fileUrl: $(imgElems[i]).attr('fileUrl'),
                          fileName: $(imgElems[i]).attr('fileName'),
                        });
                      }
                      common.viewerImgBase(
                        imgList,
                        $(data.target).attr('fileUrl')
                      );
                    } else {
                      var fileUrl = $(data.target).attr('fileUrl');
                      var fileName = $(data.target).attr('fileName');
                      common.viewerDocBase(fileUrl, fileName);
                    }
                  } else {
                    seeWf(rowid);
                  }
                });
              },
              colModel: cols,
              buidQueryParams: function () {
                return queryMap;
              },
              gridComplete: function () {
                var childFormTable = $('#processDealListTable_WJ').find(
                  'tbody .child-form-table-box'
                );
                for (var i = 0; i < childFormTable.length; i++) {
                  var item = $(childFormTable[i]).parent();
                  $(item).attr('title', '');
                }
              },
            }
          );
        }

        //一键催办
        $('#childrenFormOneClickReminder').funs('click', function () {
          var ids = $('#processDealListTable_ZB').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (ids != null && ids.length > 0) {
            var wfInstanceId = new Array();
            for (var i = 0; i < ids.length; i++) {
              var rowData = $('#processDealListTable_ZB').jqGrid(
                'getRowData',
                ids[i]
              );
              wfInstanceId.push(rowData.WF_INSTANCE_ID);
            }

            $.quoteFun('/process/dealList/tableDetail/childrenFormUrging', {
              title: '一键催办',
              wfInstanceId,
            });
          } else {
            layer.msg('请选择一条数据进行操作！');
            return false;
          }
        });

        //强制关闭
        $('#processDealListTable')
          .off('click', '.cancel')
          .on('click', '.cancel', function () {
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_ZB.getSourceRowData(rowId);
            var uri = encodeURI(
              '/view-new/processView/audit/index.html?' +
                'workflowNo=' +
                opt.data.code +
                '&businessId=' +
                rowData.ID +
                '&wfInstanceId=' +
                rowData.WF_INSTANCE_ID +
                '&taskId=' +
                rowData.task_id +
                '&workflowNumber=' +
                rowData.WORKFLOW_NUMBER +
                '&currentStepName=' +
                rowData.current_step_name +
                '&currentStepNo=' +
                rowData.CURRENT_STEP_NO +
                '&role=deal&type=cancle'
            );
            // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
            var son = window.open(uri, createUUID());
            windowsOpen.push(son);
            common.openedWindow.push(son);
          });
        //删除
        $('#processDealListTable')
          .off('click', '.delete')
          .on('click', '.delete', function (e) {
            time1 = new Date().getTime();
            e.stopPropagation();
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_ZB.getSourceRowData(rowId);
            layer.confirm(
              '确定要删除该数据吗?',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                $.ajax({
                  url: '/ts-form/form/api/deleteById',
                  data: {
                    businessId: rowData.ID,
                    wfDefinitionId: opt.data.userData.wfDefinitionId,
                  },
                  type: 'post',
                  success: function (res) {
                    if (res.success) {
                      processDealListTable_ZB.refresh();
                      layer.msg('删除成功');
                      layer.close(index);
                    } else {
                      layer.msg(res.message || '删除失败');
                    }
                  },
                });
              }
            );
            setTimeout(function name(params) {
              time1 = null;
            }, 500);
          });
        //删除
        $('#processDealListTable')
          .off('click', '.delete2')
          .on('click', '.delete2', function (e) {
            time1 = new Date().getTime();
            e.stopPropagation();
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_WJ.getSourceRowData(rowId);
            layer.confirm(
              '确定要删除该数据吗?',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                $.ajax({
                  url: '/ts-form/form/api/deleteById',
                  data: {
                    businessId: rowData.ID,
                    wfDefinitionId: opt.data.userData.wfDefinitionId,
                  },
                  type: 'post',
                  success: function (res) {
                    if (res.success) {
                      processDealListTable_WJ.refresh();
                      layer.msg('删除成功');
                      layer.close(index);
                    } else {
                      layer.msg(res.message || '删除失败');
                    }
                  },
                });
              }
            );
            setTimeout(function name(params) {
              time1 = null;
            }, 500);
          });
        //强制结束
        $('#processDealListTable')
          .off('click', '.enforceCancel')
          .on('click', '.enforceCancel', function (e) {
            e.stopPropagation();
            time1 = new Date().getTime();
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_ZB.getSourceRowData(rowId);
            var html =
              "<div id='remarkBox'>    <div class='layui-content-box ' style='overflow-y:visible'>        <div class='layui-tab-content'>            <form class='layui-form'>                <div class='layui-form-item ' >                    <label class='layui-form-label'>\u539F\u56E0</label>                    <div class='layui-input-block'>                        <textarea name='reason' class='layui-textarea'></textarea>                    </div>                </div>                <button class='none' lay-submit='' lay-filter='remarkSub'></button>            </form>        </div>    </div>    <div class='layer-btn archivesTabBtn'>     <a href='javascript:;' class='layui-btn' id='save'>\u63D0\u4EA4</a>         <a href='javascript:;' class='layui-btn layui-btn-primary' id='close'>\u53D6\u6D88</a>              </div></div>";
            layer.open({
              type: 1,
              title: '结束理由',
              closeBtn: 1,
              shadeClose: false,
              area: ['450px', '230px'],
              skin: 'yourclass',
              content: html,
              success: function (layero, index) {
                form.on('submit(remarkSub)', function (data) {
                  var d = data.field;
                  d.workflowInstId = rowData.WF_INSTANCE_ID;
                  $.ajax({
                    method: 'post',
                    url: '/ts-workflow/workflow/instance/doTerminateProcessInstance',
                    data: d,
                    success: function (res) {
                      if (res.success) {
                        layer.close(index);
                        layer.msg('办理成功');
                        processDealListTable_ZB.refresh();
                        if (processDealListTable_WJ) {
                          processDealListTable_WJ.refresh();
                        }
                      } else {
                        layer.msg(res.message || '办理失败');
                      }
                    },
                  });
                  return false;
                });
                $('#remarkBox #close')
                  .off('click')
                  .on('click', function () {
                    layer.close(index);
                  });
                $('#remarkBox #save')
                  .off('click')
                  .on('click', function () {
                    $('[lay-filter="remarkSub"]').trigger('click');
                    return false;
                  });
              },
            });
            setTimeout(function name(params) {
              time1 = null;
            }, 500);
            return false;
          });
        //归档
        $('#processDealListTable')
          .off('click', '.placeOnFile')
          .on('click', '.placeOnFile', function (e) {
            e.stopPropagation();
            time1 = new Date().getTime();
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_WJ.getSourceRowData(rowId);
            layer.confirm(
              '确定要归档该数据吗?',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                $.ajax({
                  url: '/ts-workflow/workflow/saveWorkflowArchive',
                  data: {
                    wfInstanceId: rowData.WF_INSTANCE_ID,
                  },
                  type: 'post',
                  success: function (res) {
                    if (res.success) {
                      processDealListTable_WJ.refresh();
                      layer.msg('归档成功');
                      layer.close(index);
                    } else {
                      layer.msg(res.message || '归档失败');
                    }
                  },
                });
              }
            );
            setTimeout(function name(params) {
              time1 = null;
            }, 500);
          });
        //销毁
        $('#processDealListTable')
          .off('click', '.destruction')
          .on('click', '.destruction', function (e) {
            e.stopPropagation();
            time1 = new Date().getTime();
            var rowId = $(this).attr('row-id');
            var rowData = processDealListTable_WJ.getSourceRowData(rowId);
            layer.confirm(
              '确定要销毁该数据吗?',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                $.ajax({
                  url: '/ts-workflow/workflow/instance/destructionProcessInstance',
                  data: {
                    workflowInstId: rowData.WF_INSTANCE_ID,
                  },
                  type: 'post',
                  success: function (res) {
                    if (res.success) {
                      processDealListTable_WJ.refresh();
                      layer.msg('销毁成功');
                      layer.close(index);
                    } else {
                      layer.msg(res.message || '销毁失败');
                    }
                  },
                });
              }
            );
            setTimeout(function name(params) {
              time1 = null;
            }, 500);
          });
        //查看
        function seeWf(rowId) {
          if (time1) {
            return;
          }
          var index = $(
            '#processDealListTable .layui-tab li.layui-this'
          ).index();
          var rowId = rowId;
          var rowData;
          var uri;
          if (status == 2) {
            rowData = processDealListTable_WJ.getSourceRowData(rowId);
            uri = encodeURI(
              '/view-new/processView/see/index.html?' +
                'workflowNo=' +
                opt.data.code +
                '&businessId=' +
                rowData.ID +
                '&wfInstanceId=' +
                rowData.WF_INSTANCE_ID +
                '&taskId=' +
                rowData.task_id +
                '&workflowNumber=' +
                rowData.WORKFLOW_NUMBER +
                '&currentStepName=' +
                rowData.current_step_name +
                '&currentStepNo=end' +
                '&role=consult&type=see'
            );
          } else {
            rowData = processDealListTable_ZB.getSourceRowData(rowId);
            uri = encodeURI(
              '/view-new/processView/audit/index.html?' +
                'workflowNo=' +
                opt.data.code +
                '&businessId=' +
                rowData.ID +
                '&wfInstanceId=' +
                rowData.WF_INSTANCE_ID +
                '&taskId=' +
                rowData.task_id +
                '&workflowNumber=' +
                rowData.WORKFLOW_NUMBER +
                '&currentStepName=' +
                rowData.current_step_name +
                '&currentStepNo=' +
                rowData.CURRENT_STEP_NO +
                '&role=consult&type=cancle'
            );
          }
          // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
          var son = window.open(uri, createUUID());
          son.openWiner = 1;
          windowsOpen.push(son);
          common.openedWindow.push(son);
        }
        //附件下载
        function fileWf(fileIds) {
          if (time1) {
            return;
          }
          window.location.href = `${common.url}/ts-document/attachment/batchDownloadByIds?ids=${fileIds}`;
        }
        //关闭
        $('#processDealListTable')
          .off('click', '#close')
          .on('click', '#close', function () {
            opt.ref && opt.ref();
            layer.close(wins);
          });
        var postDownLoadFile = function (options) {
          var config = $.extend(
            true,
            {
              method: 'post',
            },
            options
          );
          // delete config.data.queryMap
          config.data.workflowNo = opt.data.code;
          config.data.wfDefinitionId = opt.data.userData.wfDefinitionId;
          var $iframe = $('<iframe id="down-file-iframe"></iframe>');
          var $form = $(
            '<form target="down-file-iframe" method="' + config.method + '" />'
          );
          $form.attr('action', config.url);
          for (var key in config.data) {
            var $inp = $('<input type="hidden" name="' + key + '" />');
            $inp.val(config.data[key]);
            $form.append($inp);
          }
          $iframe.append($form);
          $(document.body).append($iframe);
          $form[0].submit();
          $iframe.remove();
        };
        //导出
        $('#processDealListTable')
          .off('click', '#childrenFormTableDataExport')
          .on('click', '#childrenFormTableDataExport', function () {
            $('[lay-filter="processDealListTablesSearch"]').trigger('click');
            var data = {
              queryMapStr: '',
            };
            data.queryMapStr = JSON.stringify(queryMap.queryMap);
            if (status == 1) {
              var postData = $('#processDealListTable_ZB').jqGrid(
                'getGridParam',
                'postData'
              );
              data.status = 1;
              data.CREATE_USER_NAME = postData.CREATE_USER_NAME;
              data.wfFinishedStartDate = postData.wfFinishedStartDate;
              data.wfFinishedEndDate = postData.wfFinishedEndDate;
              data.wfDefinitionId = postData.wfDefinitionId;
              data.workflowNo = postData.workflowNo;
              data.sidx = postData.sidx;
              data.sord = postData.sord;
              data.pageSize = postData.pageSize;
              data.wfStatus = postData.wfStatus;
              data.isPrint = postData.isPrint;
              data.pageNo = $(
                $('#processDealListTable_ZB').jqGrid('getGridParam', 'pager')
              )
                .find('.ui-pg-input')
                .val();
            } else {
              var postData = $('#processDealListTable_WJ').jqGrid(
                'getGridParam',
                'postData'
              );
              data.status = 2;
              data.CREATE_USER_NAME = postData.CREATE_USER_NAME;
              data.wfFinishedStartDate = postData.wfFinishedStartDate;
              data.wfFinishedEndDate = postData.wfFinishedEndDate;
              data.wfDefinitionId = postData.wfDefinitionId;
              data.workflowNo = postData.workflowNo;
              data.sidx = postData.sidx;
              data.sord = postData.sord;
              data.pageSize = postData.pageSize;
              data.wfStatus = postData.wfStatus;
              data.isPrint = postData.isPrint;
              data.pageNo = $(
                $('#processDealListTable_WJ').jqGrid('getGridParam', 'pager')
              )
                .find('.ui-pg-input')
                .val();
            }
            $('#processDealListTable_WJ').jqGrid('getGridParam', 'postData');
            postDownLoadFile({
              url: common.url + '/ts-form/form/api/export?tableView=Y',
              data: data,
              method: 'post',
            });
            // layer.close(wins);
          });
        // 子表单查询
        $('#processDealListTable')
          .off('click', '#childrenFromData')
          .on('click', '#childrenFromData', function () {
            let tableId = $(this).attr('table-id');
            let tableName = $(this).attr('table-name');
            let fieldName = $(this).attr('fieldName');
            $.quoteFun('process/dealList/tableDetail/childrenForm', {
              data: tableId,
              title: tableName,
              fieldName,
            });
          });
      }
    );
  };
});
