"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
	layui.use([ 'form', 'layedit', 'laydate', 'trasen', 'upload' ], function() {
	    var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload;
		    layer.open({
				type : 1,
				title : opt.title,
				closeBtn : 1,
				shadeClose : false,
				area : [ '80%', '80%' ],
				skin : 'yourclass',
				content : html,
				success : function(layero, index) {
					var url = "";
					if(opt.optType == '导出考评报表'){
						$("#optTypeDiv").remove();
						url = common.url + '/ts-oa/api/moralityLogs/list?optType=' + opt.optType;
					}else{
						//$("#optTypeDiv option[value='导出考评报表']").remove();
						url = common.url + '/ts-oa/api/moralityLogs/list';
					}
					
			    	var moralityLogsTable = new $.trasenTable("moralityLogsTable", {
	               	 	url: url,
	                    mtype: 'get',
	                    datatype: "json",
	                    sortname: 'opt_time',
	                    sortorder: 'desc',
	                    colModel: [
	                        {
	                            label: '操作类型',
	                            sortable: true,
	                            name: 'optType',
	                            index: "opt_type",
	                            width: 100,
	                            align: 'center'
	                        },
	                        {
	                            label: '操作内容',
	                            sortable: true,
	                            name: 'optContent',
	                            index: "opt_content",
	                            width: 300,
	                            align: 'center'
	                        },
	                        {
	                            label: '操作人',
	                            sortable: true,
	                            name: 'optName',
	                            index: "opt_name",
	                            width: 80,
	                            align: 'center'
	                        },
	                        {
	                        	label: '操作时间', 
	                        	sortable: true, 
	                        	index: 'opt_time',
	                        	name: 'optTime',
	                        	width: 100,
	                        	align: 'center'
	                        },
	                        {label: 'id', sortable: false, name: 'id', width: 10, hidden: true}
	                    ],
	                    height: "auto",
	                    pager: '#moralityLogsPage',
	                    rowNum: 10,
	                    rownumbers: true,
	                    rowList: [15, 30, 50, 100],
	                    shrinkToFit: true,
	                    queryFormId: 'moralityLogsForm'
	               });
				    
			    	form.render();
			    	
				    $("#moralityLogsTable").jqGrid('setLabel','0','序号','labelstyle');
		
				    //时间控件
		            laydate.render({
		                  elem: '#optInputTime',
		                  range: '~',
		                  trigger: 'click',
		                  showBottom: true,
		                  done: function (value, date, endDate) {
		                      var dateArr = value.split(' ~ ');
		                      $('#optHideInputStartTime').val(dateArr[0] + " 00:00:00");
		                      $('#optHideInputEndTime').val(dateArr[1] + " 23:59:59");
		                  },
		            });
		            
		            //查询
		            $('#moralityLogsSearch').funs('click', function () {
		                refreshmoralityLogs();
		            });
		            
		            //重置
		            $('#moralityLogsReset').funs('click', function () {
		                $('#optInputTime').val('');
		                $('#optHideInputStartTime').val('');
		                $('#optHideInputEndTime').val('');
		                $('#optType').val('');
		                $('#optName').val('');
		                form.render();
		                refreshmoralityLogs();
		            });
		            
				    function refreshmoralityLogs(){
				    	moralityLogsTable.refresh();
				    }
				    
				}
		    });
		})
    };
});
