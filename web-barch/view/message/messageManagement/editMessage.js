"use strict";
define(function (require, exports, module) {
	exports.init = function (opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
			var form = layui.form,
				laydate = layui.laydate,
				trasen = layui.trasen,
				upload = layui.upload,
				layedit = layui.layedit;


			var editIndex;
			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['60%', '90%'],
				skin: 'yourclass',
				content: html,
				success: function (layero, index) {
					if(opt.content){
						$("#messageContent").val(opt.content);
						$("#messageContentNums").html(opt.content.length);
						$("#messageContentCount").html(Math.ceil(opt.content.length / 70));
					}
					if (opt.data) {
						$('#messageId').val(opt.data.id);
						$("#toMessageUserId").val(opt.data.toId);
						$("#toMessageUserCode").val(opt.data.toId);
						$("#toMessageSelUser").val(opt.data.toName);
						trasen.setNamesVal($('#editMessageForm'), opt.data);
						if (null != opt.data.toId && "" != opt.data.toId) {
							var chooseNums = opt.data.toId.split(",");
							$("#chooseNums").html(chooseNums.length);
						}
						if (null != opt.data.mobilePhone && "" != opt.data.mobilePhone) {
							var phoneNums = opt.data.mobilePhone.split(",");
							$("#phoneNums").html(phoneNums.length);
						}
						if("1" == opt.data.timing){
							$("#startTiming").removeAttr("disabled");
							$("#timingSwitch").attr("checked","checked");
						}
						$("#messageContentNums").html(opt.data.content.length);
						$("#messageContentCount").html(Math.ceil(opt.data.content.length / 70));

					}
					form.render();
				}
			});

			//人员选择
			$('#toNamePhone').funs('click', function () {
				var data = {
					isCheckDept: 'N',
					user_str: 'toMessageSelUser',
					user_id: "toMessageUserId",
					user_code: "toMessageUserCode",
					user_name_phone:'toNamePhone'
				};
				$.quoteFun('/common/userSel', {
					trasen: trasenTable,
					title: '人员选择',
					data: data,
					callback: function (selUserNameVal, userIdArr, userCodeArr, deptNameArr, deptUserNameArr,userNamePhoneArr) {
						$('#chooseNums').html(userCodeArr.length)
					}
				});
			})
			
			//重置
			$('#toNameReset').funs('click', function () {
				$("#toMessageSelUser").val("");
				$("#toMessageUserId").val("");
				$("#toMessageUserCode").val("");
				$("#toNamePhone").val("");
				$("#chooseNums").html("0");
			})
			$('#mobilePhoneReset').funs('click', function () {
				$("#mobilePhone").val("");
				$("#phoneNums").html("0");
			})

			//外部手机验证
			$("#mobilePhone").blur(function () {
				var mobileText = $("#mobilePhone").val();
				var mobileArray = new Array();
				if ('' != mobileText && mobileText.length >= 11) {
					for (var i = 0; i < mobileText.length; i++) {
						var mo = mobileText.substring(i, 11 + i);
						if (checkMobile(mo)) {
							mobileArray.push(mo);
						}
					}
					if (unique(mobileArray).length > 200) {
						layer.msg("导入的手机号码超过200限制");
					} else {
						$("#mobilePhone").val(unique(mobileArray).toString());
						$(".mobilePhone").next().find("b").html(unique(mobileArray).length);
					}
				} else {
					if (!checkMobile(mobileText)) {
						$("#mobilePhone").val("");
						$(".mobilePhone").next().find("b").html(0);
					}
				}
				$("#phoneNums").html(unique(mobileArray).length);
			});
			
			
			//定时发送时间控件
            laydate.render({
                elem: '#startTiming',
                type: 'datetime',
                format:'yyyy-MM-dd HH:mm',
                showBottom: true,
                trigger: 'click'
            });
            
            
            //监听是否定时发送
			form.on('switch(timingSwitch)', function (data) {
				if (data.elem.checked == true) {
					$("#timing").val("1");
					$("#startTiming").removeAttr("disabled");
					$("#startTiming").attr("lay-verify", "required");
				} else {
					$("#timing").val("0");
					$("#startTiming").val("");
					$("#startTiming").attr("disabled", "disabled");
					$("#startTiming").removeAttr("lay-verify");
				}
			});
            

			//发送
			form.on('submit(editMessageSubmit)', function (data) {
				var d = data.field;
				var url = '/ts-information/messageInternal/sendMessage?isDraft=0';
				if (!d.id) delete d.id;

				if(d.timing == 1 && d.toId == '' && d.mobilePhone ==''){
					layer.msg("定时发送接收人不能为空");
					return;
				}
				
				$.loadings();
				$.ajax({
					type: 'post',
					url: common.url + url,
					data: d,
					success: function (res) {
						$.closeloadings();
						if (res.success) {
							layer.closeAll();
							layer.msg(res.object);
							if(d.timing == 0){
								$("#outBoxMessageTab").click();
							}else{
								$("#draftsMessageTab").click();
							}
						} else {
							layer.closeAll();
							layer.msg(res.object);
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
				return false;
			});

			// 保存草稿
			form.on('submit(editMessageDraftSubmit)', function (data) {
				var d = data.field;
				var url;
				if (!d.id) delete d.id;

				var timing = $("#timing").val();
				if("1" == timing){
					layer.confirm('定时发送短信的时间将不生效，确定存草稿吗？', {
						btn: ['确定', '取消'],
						title: '提示',
						closeBtn: 0
					},function (index) {
						$("#timingSwitch").prop("checked", false);
						d.timing = "0";
						d.startTiming = "";
						sendDraftMessage(d);
	                });
				}else{
					sendDraftMessage(d);
				}
				return false;
			});
			
			function sendDraftMessage(d){
				$.loadings();
				$.ajax({
					type: 'post',
					url: common.url + '/ts-information/messageInternal/sendMessage?isDraft=1',
					data: d,
					success: function (res) {
						$.closeloadings();
						if (res.success) {
							layer.closeAll();
							layer.msg(res.object);
							$("#draftsMessageTab").click();
						} else {
							layer.msg(res.object || '保存失败');
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
			}

			//取消
			$('#closeMessage').funs('click', function () {
				
				var toNamePhone = $("#toNamePhone").val();
				var mobilePhone = $("#mobilePhone").val();
				var messageContent = $("#messageContent").val();
				
				if(toNamePhone || mobilePhone || messageContent){
					layer.confirm('填写内容未保存，确定取消吗？', {
						btn: ['确定', '取消'],
						title: '提示',
						closeBtn: 0
					},function (index) {
						layer.closeAll();
					});
				}else{
					layer.closeAll();
				}
			})

			$("#editMessageForm").off('input', '#messageContent').on('input', '#messageContent', function (event) {
				if ($(this).val().length >= 350) {
					$(this).val($(this).val().substring(0, 350))
				}
				var msg = $(this).val();
				$("#messageContentNums").html(msg.length);
				var msgNumber = Math.ceil(msg.length / 70);
				$("#messageContentCount").html(msgNumber);
				$("#msgNumber").val(msgNumber);
			});

			function checkMobile(value) {
				if ('' == stringTrim(value)) {
					return false;
				} else {
					if (value.indexOf(",") > -1) {
						return false;
					} else if (!(/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(stringTrim(value)))) {
						return false;
					} else {
						return true;
					}
				}
			}

			function stringTrim(s) {
				return s.replace(/^\s+|\s+$/gm, '');
			}

			function unique(arr) {
				var result = [],
					hash = {};
				for (var i = 0, elem;
					(elem = arr[i]) != null; i++) {
					if (!hash[elem]) {
						result.push(elem);
						hash[elem] = true;
					}
				}
				return result;
			}


		})
	};
});