'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        opt.$dom.html(html);
        common.overlayScrollbarsSet(' .scrollbar-box');

        incomingDispatches();
        // window.setInterval(incomingDispatches, 300000);

      /*  function incomingDispatchesNum() {
            $.ajax({
                url: '/ts-document/govSendfile/getNoreadSendfileNumber',
                type: 'get',
                success: function (res) {
                    if (res.success && res.object) {
                        if (res.object != 0) {
                            $('#indexIFrame .incoming-dispatches-num')
                                .text(res.object || 0)
                                .removeClass('none');
                        }
                    } else {
                        $('#indexIFrame .incoming-dispatches-num').addClass('none');
                    }
                },
            });
        }*/

        function incomingDispatches() {
           // incomingDispatchesNum();
            $.ajax({
                type: 'post',
                url: common.url + '/ts-document/govSendfile/getMySendfileList',
                data: {
                    pageSize: 10,
                    pageNo: 1,
                    sord: 'desc',
                    sidx: 'is_read,create_date',
                },
                success: function (res) {
                    $('#indexIFrame .incoming-dispatches').html('');
                    if (res.rows && res.rows.length > 0) {
                        $('#indexIFrame .incoming-dispatches').closest('.content-bg-con').addClass('noBg');
                        new common.simpleEvent({
                            el: '#indexIFrame .incoming-dispatches',
                            list: res.rows,
                            classes: 'box',
                            event: 'click',
                            print: function (item) {
                                var cl = '';
                                var icon = 'oaicon oa-icon-bianzu24 ';
                                var newStr = "";
                                if (item.isRead != 1) {
                                    cl = 'read';
                                    icon = 'oaicon oa-icon-bianzu7 ';
                                    newStr = '<p class="new">new<p>';
                                }
                                
                                var nowYear = new Date().format("yyyy");
                            	var infoYear = new Date(item.createDate).format("yyyy");
                            	var infoDate = "";
                            	if(nowYear == infoYear){
                            		infoDate = new Date(item.createDate).format("MM-dd");
                            	}else{
                            		infoDate = new Date(item.createDate).format("yyyy-MM-dd");
                            	}
                                
                                return (
                                //     '<div class="incoming-dispatches-item ' +
                                //     cl +
                                //     '">' +
                                //   /*  '<div class="mail"><i class="' +
                                //     icon +
                                //     '" aria-hidden="true"></i></div>' +*/
                                //     '<div style="overflow:hidden"><div class="first-title flex-box">' +
                                //     '<p class="flex-box-1 one-line">' +
                                //     (item.fileTitle || '') +
                                //     '</p>' + newStr +
                                //     '</div>' +
                                //     '<div class="second-title flex-box">' +
                                //     '<p class="flex-box-1 one-line"><span class="pd-lf-10">' +
                                //     (item.createUserName || '') + ' ' + (item.empDeptName || '') +
                                //     '</span><span class="pd-lf-11">' +
                                //     infoDate +
                                //     '</span>' +
                                //     '</p>' +
                                //     '</div></div></div>'

                                   `<div class="incoming-dispatches-item ${cl}">
                                        <div class="first-title">
                                            ${newStr}
                                            <p class="fileTitle one-line" style="flex: 1;">
                                                ${(item.fileTitle || '')}
                                            </p>
                                        </div>
                                        <div class="second-title">
                                            <p>
                                                ${(item.createUserName || '')}
                                            </p>
                                            <p class="padding-left-8">${infoDate}</p>
                                        </div>
                                    </div>
                                    `
                                );
                            },
                            func: function (item) {
                                if (item.fileId && item.fileId.indexOf(',') !== -1) {
                                    const ids = item.fileId.split(',');
                                    const fileData = ids.map((idsItem, index) => {
                                        return {
                                            id: idsItem,
                                            fileName: item.fileName.split(',')[index],
                                            originalName: item.originalName.split(',')[index],
                                        }
                                    })

                                    let str = ''
                                    fileData.forEach(fileItem => {
                                        str += `<li fileId="${fileItem.id}" fileName="${fileItem.fileName}" originalName="${fileItem.originalName}">
                                            <p class="title">${fileItem.originalName}</p>
                                            <span class="operation-title">查看</span>
                                        </li>`
                                    })

                                    let ulContent = `<ul>${str}</ul>`
                                    layer.open({
                                        type: 1,
                                        title: '正文列表',
                                        closeBtn: 1,
                                        shadeClose: false,
                                        area: ['450px', '250px'],
                                        skin: 'show-text-list',
                                        content: ulContent,
                                        success: function (layero, index) {
                                            $('.show-text-list ul li .operation-title').funs('click', function () {
                                                const fileId = $(this).parent().attr('fileId');
                                                const originalName = $(this).parent().attr('originalName');

                                                var url = 'http://127.0.0.1:9004/ts-document/attachment/downloadFile/' + fileId + '?fullfilename=' + originalName ;
                                                window.open(common.url + '/ts-preview/onlinePreview?url=' + encodeURIComponent(Base64.encode(url)));
                                                confirmSendfile(fileId);
                                            })
                                        }
                                    });
                                } else {
                                    var url = 'http://127.0.0.1:9004/ts-document/attachment/downloadFile/' + item.fileId + '?fullfilename=' + item.originalName ;
                                    window.open(common.url + '/ts-preview/onlinePreview?url=' + encodeURIComponent(Base64.encode(url)));
                                    confirmSendfile(item.fileId);
                                }
                            },
                        });
                        if ($('#indexIFrame .incoming-dispatches').height() > 320) {
                            $('#indexIFrame .incoming-dispatches .box:nth-last-child(1) .incoming-dispatches-item').css({
                                borderBottom: 'none',
                            });
                        }
                    } else {
                        $('#indexIFrame .incoming-dispatches').closest('.content-bg-con').removeClass('noBg');
                    }
                },
                error: function (res) {},
            });
        }

        //签收
        function confirmSendfile(fileId) {
            $.ajax({
                url: '/ts-document/govSendfile/confirmSendfile?pageOfficeId=' + fileId,
                type: 'post',
                success: function (res) {
                    incomingDispatches();
                },
            });
        }
    };
});
