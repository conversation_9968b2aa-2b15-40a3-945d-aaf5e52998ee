'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var form = layui.form,
            trasen = layui.trasen;

        layer.open({
            type: 1,
            title: opt.title,
            closeBtn: 1,
            shadeClose: false,
            area: ['440px', '330px'],
            skin: 'yourclass',
            content: html,
            success: function (layero, index) {
                getEnumDataList(enum_gender_type, '性别', 'gender');
                if (opt.data) {
                    trasen.setNamesVal(layero, opt.data);
                }
                form.render();
            },
        });

        function isCardNo(card) {
            // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
            var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
            if (reg.test(card) === false) {
                return false;
            }
        }

        // 保存
        form.on('submit(blacklistManageEdiformSave)', function (data) {
            var _url = common.url + '/ts-hrms/blacklist/save';
            if (opt.data) {
                _url = common.url + '/ts-hrms/blacklist/update';
            }
            //验证身份证号码
            var identityNumber = $("#blacklistManageAddForm [name='identityNumber']").val();
            if (isCardNo(identityNumber) == false) {
                layer.msg('身份证不合法！');
                return false;
            }
            var _data = JSON.stringify(data.field);
            $.ajax({
                type: 'post',
                contentType: 'application/json; charset=utf-8',
                url: _url,
                data: _data,
                success: function (res) {
                    if (res.success) {
                        opt.ref && opt.ref();
                        layer.closeAll();
                        layer.msg('保存成功！');
                    } else {
                        layer.msg(res.message || '操作失败');
                    }
                },
            });
            return false;
        });

        //取消
        $('#blacklistManageAddFormDiv #blacklistManageClose')
            .off('click')
            .on('click', function () {
                layer.closeAll();
            });
    };
});
