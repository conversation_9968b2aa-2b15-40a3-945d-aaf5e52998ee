'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                laydate = layui.laydate,
                util = layui.util;

            var trasenTable = new $.trasenTable('otherHrmsPartyMembersTable', {
                url: common.url + '/ts-hrms/api/partyMembers/list',
                pager: '#otherHrmsPartyMembersPage',
                mtype: 'get',
                sortname: 'party_status,create_date',
                sortorder: 'desc',
                shrinkToFit: true,
                postData: {
                  otherPartyMembers: 'Y'
                },
                colModel: [
                	{
                        label: '工号',
                        sortable: true,
                        align: 'center',
                        index: 'emp_code',
                        name: 'empCode',
                        width: 60
                    },
                    {
                        label: 'empName',
                        name: 'empName',
                        hidden: true
                    },
                    {
                        label: 'filesId',
                        name: 'filesId',
                        hidden: true
                    },
                    {
                        label: '姓名',
                        sortable: true,
                        index: 'emp_name',
                        name: 'empNameText',
                        width: 60,
                        formatter: function (cellvalue, options, row) {
                        	return '<p style="cursor: pointer;"><span class="otherPartyMembersDetails dealLink">' + row.empName + '</span></p>';
                        }
                    },
                    {
                        label: '科室',
                        sortable: true,
                        index: 'dept_name',
                        name: 'deptName',
                        width: 80
                    },
                    {
                        label: '身份证',
                        sortable: true,
                        align: 'center',
                        index: 'idcard',
                        name: 'idcard',
                        width: 140
                    },
                    {
                        label: 'postName',
                        name: 'postName',
                        hidden: true
                    },
                    {
                        label: '岗位',
                        sortable: true,
                        align: 'center',
                        index: 'post_name',
                        name: 'postNameText',
                        width: 60
                    },
                    {
                        label: 'politicalStatus',
                        name: 'politicalStatus',
                        hidden: true,
                    },
                    {
                        label: '政治面貌',
                        sortable: true,
                        name: 'politicalStatusText',
                        index: 'political_status',
                        width: 80
                    },
                    {
                        label: '入党时间',
                        sortable: true,
                        align: 'center',
                        index: 'party_date',
                        name: 'partyDate',
                        width: 80
                    },
                    {
                        label: '退休时间',
                        sortable: true,
                        align: 'center',
                        index: 'retire_date',
                        name: 'retireDate',
                        width: 80
                    },
                    {
                        label: '状态',
                        sortable: true,
                        align: 'center',
                        index: 'party_status',
                        name: 'partyStatusText',
                        width: 60,
                        formatter: function (cell, options, row) {
                        	if('1' == row.partyStatus){
                        		return '已审核';
                        	}else{
                        		return '<span style="color:red">待审核</span>';
                        	}
                        }
                    },
                    {
                        label: 'partyStatus',
                        name: 'partyStatus',
                        hidden: true
                    },
                    {
                        label: '创建时间',
                        sortable: true,
                        align: 'center',
                        index: 'create_date',
                        name: 'createDate',
                        width: 120,
                        formatter: function (cell, options, row) {
                        	return util.toDateString(cell,'yyyy-MM-dd HH:mm');
                        }
                    },
                    {
                        label: '创建人',
                        sortable: true,
                        align: 'center',
                        index: 'create_user_name',
                        name: 'createUserName',
                        width: 60
                    },
                    {
                        label: 'id',
                        name: 'id',
                        hidden: true,
                        key: true,
                    },
                    {
                        label: 'empId',
                        name: 'empId',
                        hidden: true,
                    }
                ],
                buidQueryParams: function () {
                    var search = $('#otherHrmsPartyMembersSearchForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });
            
            form.render();

            function refresh() {
                trasenTable.refresh();
            }
            
            //时间控件
            // laydate.render({
            //       elem: '#partyDateTimeOtherParty',
            //       range: '~',
            //       trigger: 'click',
            //       showBottom: true,
            //       done: function (value, date, endDate) {
            //           var dateArr = value.split(' ~ ');
            //           $('#partyDateStartTimeOtherParty').val(dateArr[0]);
            //           $('#partyDateEndTimeOtherParty').val(dateArr[1]);
            //       },
            // });
            $.each($('#otherHrmsPartyMembersBox .laydate-date'), function (i, v) {
				laydate.render({
					elem: v,
					trigger: 'click',
				});
			});
            
            //搜索
            $('#otherHrmsPartyMembersSearch').funs('click', function () {
                refresh();
            });

            //重置
            $('#otherHrmsPartyMembersResetBtn').funs('click', function () {
            	$("#otherHrmsPartyMembersSearchForm")[0].reset();
            	$("#partyDateStartTimeOtherParty").val('');
            	$("#partyDateEndTimeOtherParty").val('');
                refresh();
            });
            
          //查看
          $('body').off('click', '.otherPartyMembersDetails').on('click', '.otherPartyMembersDetails', function () {
              var rowData = trasenTable.getSelectRowData(); 
              rowData.userName = rowData.empName;
              rowData.userCode = rowData.empCode;
              $.quoteFun('/personnelmgr/partyMembers/add', {
                    data: rowData,
                    title: '详情',
                    optType:'details'
                });
          });
            //同步数据
            $('#otherHrmsPartyMembersBox')
            .off('click', '#syncHrmsPartyMembersOtherParty')
            .on('click', '#syncHrmsPartyMembersOtherParty', function () {
            	$.ajax({
                    type: 'post',
                    url: common.url + '/ts-hrms/api/partyMembers/syncEmployeeData',
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                        	 refresh();
                        } 
                        layer.closeAll();
                        layer.msg(res.message);
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            });
            
            
            //导出
            $('#otherHrmsPartyMembersBox')
                .off('click', '#exportHrmsPartyMembersOtherParty')
                .on('click', '#exportHrmsPartyMembersOtherParty', function () {
                    var opt = $('#otherHrmsPartyMembersSearchForm').serializeArray();
                    opt.push({
                      name: 'otherPartyMembers',
                      value: 'Y'
                    })
                    var params = "";
                    for (var i in opt) {
                        params += opt[i].name + "=" +opt[i].value + "&";
                    }
                    
                    window.location.href = common.url + '/ts-hrms/api/partyMembers/export?' + params.substring(0,params.length-1);
             });
        });
    };
    
});
