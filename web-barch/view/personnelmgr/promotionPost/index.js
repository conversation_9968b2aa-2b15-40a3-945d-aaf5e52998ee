"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'laytpl', 'trasen'], function() {
            var form = layui.form,
                laytpl = layui.laytpl,
                layer = layui.layer,
                laydate = layui.laydate,
                trasen = layui.trasen;
            
            
            // 表格渲染
            var trasenTable = new $.trasenTable("grid-table-promotionPostTable", {
                url: common.url + '/ts-hrms/personnelpromotionpost/list',
                pager: 'grid-table-promotionPostPager',
//                shrinkToFit: true,
//                multiselect: true,
                sortname: 't3.name', //设置默认的排序列
                sortorder: 'asc',
                colModel: [
                    { label: 'ID', name: 'id', index: 'id', editable: false, hidden: true },
                    { label: '员工id', name: 'employeeId',  hidden: true },
                    { label: '员工工号', name: 'employeeNo',  width: 100, editable: false, align: 'left' },
                    { label: '姓名', name: 'employeeName',  width: 100, editable: false, align: 'left' },
                    { label: '性别', name: 'gender',  width: 120, editable: false, align: 'center' },
                    { label: '科室', name: 'orgName',  width: 120, editable: false, align: 'left' },
                    { label: '当前岗位', name: 'postName',  width: 100, editable: false, align: 'left' },
                    { label: '开始任职日期', name: 'employDutyEquallyDate',  width: 150, editable: false, align: 'center' },
                    { label: '可升职日期', name: 'upgradeDate', width: 100, editable: false, align: 'center' },
//                    { label: '可升级岗位', name: 'createDate', width: 100, align: "center", editable: false},
                    { label: '员工状态', name: 'dictName', width: 60, editable: false, align: "center" },
                    { label: '操作', name: '', index: '', width: 100, editable: false, align: "center",    
                    	formatter: function (cellvalue, options, rowObject) {
                    		return '<fond class="alink" data-id="'+rowObject.employeeId+'">去处理</fond>';
                    	}, 
                    },
                ],
                queryFormId: 'promotionPostQueryForm'
//                shrinkToFit:true
            });
            
            $('#promotionPostDiv')
            .off('click', '.alink')
            .on('click', '.alink', function () {
                var employeeId = $(this).attr('data-id');
//                $.quoteFun('personnelmgr/employee/modules/add', {
//                    title: '编辑员工档案',
//                    data: rowData,
//                    type:2
//                });

                    $.ajax({
                        url: '/ts-basics-bottom/cusotmEmployee/findByIdAndDetails/' + employeeId,
                        method: 'post',
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('personnelmgr/employee/customPage/add', {
                                    type: 'edit',
                                    ref: refreshTable,
                                    data: res.object,
                                    employeeId: employeeId,
                                });
                            } else {
                                layer.msg(res.message || '查询个人信息失败');
                            }
                        },
                    });



                return false;
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 查询
            form.on('submit(promotionPostSearch)', function(data) {
                refreshTable();
            });

            // 重置
            form.on('submit(promotionPostRefresh)', function(data) {
                $("#promotionPostQueryForm input[name='orgName']").val("");
            	$("#promotionPostQueryForm input[name='employeeName']").val("");
            	form.render();
                refreshTable();
            });

            //导出
            $("#promotionPostDiv").off("click", "#promotionPostExport").on("click", "#promotionPostExport", function() {
            	var cause = $("#promotionPostDiv select[name='cause']").val();
                layer.confirm('确定要导出吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function(index) {
                	var date = $("#promotionPostDateSearch").val();
                    var exportParams = {
                        orgName:$("#promotionPostQueryForm input[name='orgName']").val(),
                        employeeName:$("#promotionPostQueryForm input[name='employeeName']").val(),
                        sortname: 't3.name', //设置默认的排序列
                        sortorder: 'asc'
                    }
                    var url = common.url + "/ts-hrms/personnelpromotionPost/downLoad?orgName="+exportParams.orgName+"&employeeName="+exportParams.employeeName+"&sortname=t3.name&sortorder=asc";
                    location.href = url;
                    layer.close(index);
                }, function() {});
            });
            

        });
    }
});