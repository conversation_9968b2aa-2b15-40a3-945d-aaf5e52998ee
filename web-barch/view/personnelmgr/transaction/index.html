<div class="content-box" id="transactionDiv">
    <div class="oa-nav-search">
        <form id="transactionQueryForm" class="layui-form areaButtonBoxL">
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">生效时间</span>
                <div class="shell-layer-input-box" style="width: 200px">
                    <input type="text" id="transactionDateSearch" autocomplete="off" class="layui-input layDate"
                        placeholder="请选择生效时间" readonly />
                    <input type="text" id="effectiveStartDate" name="effectiveStartDate" autocomplete="off"
                        class="layui-input layDate none" placeholder="调动开始日期" />
                    <input type="text" id="effectiveEndDate" name="effectiveEndDate" autocomplete="off"
                        class="layui-input layDate none" placeholder="调动结束日期" />
                </div>
            </div>
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">异动类型</span>
                <div class="shell-layer-input-box">
                    <input autocomplete="off" class="layui-input" id="loadChangeSelectChoose" type="text"/>
                    <input type="hidden" name='causes'>
                </div>
            </div>
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">编制类型</span>
                <div class="shell-layer-input-box">
                    <select name="establishmentType" id="establishmentTypeSel" lay-search></select>
                </div>
            </div>
            <div class="shell-search-box">
                <button type="button" class="layui-btn" lay-submit lay-filter="transactionSearch"
                    search-btn="transactionQuery-search">搜索</button>
            </div>
            <div class="shell-search-box">
                <button type="button" class="layui-btn oa-btn-reset" lay-submit lay-filter="transactionReset"><i
                        class="layui-icon layui-icon-refresh"></i></button>
            </div>
        </form>

        <div class="fr">
            <button type="button" class="layui-btn" id="transactionExport">导出</button>
        </div>
    </div>

    <div class="trasen-con-box">
        <div class="table-box" id="transactionTableBox">
            <!-- 表单 -->
            <table id="grid-table-transactionTable"></table>
            <!-- 分页 -->
            <div id="grid-table-transactionPager"></div>
        </div>
    </div>
</div>