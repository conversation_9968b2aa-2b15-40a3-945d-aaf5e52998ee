"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'table', 'treeSelect', 'zTreeSearch', 'upload'], function () {
            var form = layui.form,
                table = layui.table,
                trasen = layui.trasen,
                laydate = layui.laydate,
                upload = layui.upload,
                zTreeSearch = layui.zTreeSearch;
            var businessId = null;
            var businessId2 = null;
            var businessId3 = null;
           
            var allCityData = null;
            
            var employeePrqkAddFileuploadLenth = 0;
            var employeeZyqkAddFileuploadLenth = 0;
            $.ajax({
                async: false,
                type: 'get',
                url: '/view/personnelmgr/employee/modules/city.json',
                success(res) {
                    allCityData = res;
                }
            })

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                	
                	
                    //上传职称和证书的时候

                    /** 下拉列表渲染 **/
//                    getPositionList(); // 职务
                    getEnumDataList(enum_gender_type, "性别", "gender");
                    getDictInfoList(dict_employee_status, "员工状态", "employeeStatusAddEmployee");
                    getDictInfoList(dict_employee_category, "编制类别", "employeeCategory");
                    getDictInfoList(dict_salary_level_category, "薪级类别", "salaryLevelCategory");
                    getDictInfoList(dict_nationality_name, "民族", "nationality");
                    getDictInfoList(dict_blood_group, "血型", "bloodGroup");
                    getDictInfoList(dict_political_status, "政治面貌", "politicalStatus");
                    getDictInfoList(dict_marriage_status, "婚姻状况", "marriageStatus");
                    getDictInfoList(dict_employment_nature, "", "workNature");
                    getDictInfoList(dict_personal_identity, "岗位类别", "personalIdentity");
                    getDictInfoList(dict_establishment_type, "编制类型", "establishmentType");
                    getDictInfoList(dict_health_status, "健康状况", "healthStatus");
                    getDictInfoList(dict_post_category, "岗位类别", "postCategory");
                    getDictInfoList(dict_review_depart, "考勤审核科室", "reviewDepart");
                    getDictInfoList(dict_first_education_type, "第一学历", "firstEducationType");

                    getDictInfoList(dict_education_type, "最高学历", "educationType");

                    getDictInfoList(dict_post_type, "岗位类型", "postType");
                    getDictInfoList(dict_job_description_type, "岗位描述", "jobDescriptionType");
                    getDictInfoList(dict_authorized_org, "岗位描述", "authorizedOrg");
                    
                    
                    getDictInfoList(dict_operation_type, "执业类型", "operationType");


                    form.render(); // 渲染数据
                    if (opt.data) {
                        // 查询员工详情
                        $.ajax({
                        	async:false,
                            type: "post",
                            contentType: "application/json; charset=utf-8",
                            url: common.url + "/ts-hrms/employee/getEmployeeDetail/" + opt.data.employeeId,
                            success: function (res) {
                                if (res) {
                                    //$(".otherAdd").show();
                                    if (opt.entrance != "" && opt.entrance == "oneself") {
                                        $(".showDiv").show();
                                        $("#personalDetailsSubmit").hide();
                                    } else {
                                        $('#settingBasicListBox .contractDetailLeft .otherAdd').show()
                                        //                                		$(".otherAdd").show();
                                    }
                                    $('#thisUnitContinuousSeniority').val(getDiffYearByDate(res.object.entryDate)); // 本单位连续工龄
                                    $('#continuousSeniority').val(getDiffYearByDate(res.object.workStartDate)); // 连续工龄
                                    $("#employeeStatusAddEmployee").attr("disabled", true); // 员工状态不能直接修改

                                    if (res.object.postCategory) {
                                        getPostList(res.object.postCategory); // 岗位名称下拉列表
                                    }
                                    if (res.object.salaryLevelCategory) {
                                        getSalaryLevelList(res.object.salaryLevelCategory); // 薪级名称下拉列表
                                    }
                                    if (res.object.avatar) {
                                        $("#employeeAddForm #userAvatarImg").attr("src", res.object.avatar).show();
                                    }
                                    if (res.object.bornAddress && res.object.bornAddress != '') {
                                        var bornAddressArr = res.object.bornAddress.split('-');
                                        initProvince(allCityData);
                                        initCity(allCityData[bornAddressArr[0]]);
                                        initArea(allCityData[bornAddressArr[1]]);
                                        res.object.province = bornAddressArr[0];
                                        res.object.city = bornAddressArr[1];
                                        res.object.area = bornAddressArr[2];
                                    } else {
                                        initProvince(allCityData);
                                    }
                                    trasen.setNamesVal(layero, res.object); // 打开的整个页面内的渲染 所有数据填进去
                                    if (res.object.businessId) {
                                        businessId = res.object.businessId;
                                    }
                                    if (res.object.businessId2) {
                                        businessId2 = res.object.businessId2;
                                    }
                                    if (res.object.businessId2) {
                                        businessId3 = res.object.businessId3;
                                    }
                                    if(res.object.establishmentType != 1) {
                                    	$('#employeeCategorySpan').hide();
                                    	$('#employeeCategory').removeAttr('lay-verify');
                                    }
                                    getPracticeSituationTableList(opt.data.employeeId);
                                    
                                    //判断是否可以编辑聘任情况
                                    checkHistoryPost(opt.data.employeeId);
                                    
                                    form.render(); // 渲染数据
                                   
                                    if(opt.type == 2){  //编辑岗位信息
                                		$('.contractDetailLeft .control-item').eq(1).addClass('shell-active').siblings().removeClass('shell-active');
                                		$('#employeeAddForm').css('display','none');
                                		$('#employeeAddFileForm').css('display','none');
                                		$('.otherAddContent .contractDetailTabBox').eq(0).css('display','block');
                                	}else if(opt.type == 3){  //编辑职称信息
                                		$('.contractDetailLeft .control-item').eq(3).addClass('shell-active').siblings().removeClass('shell-active');
                                		$('#employeeAddForm').css('display','none');
                                		$('#employeeAddFileForm').css('display','none');
                                		$('.otherAddContent .contractDetailTabBox').eq(0).css('display','block');
                                	}
                                }
                            }
                       
                        });

                        /** 查询其他信息的列表 **/
                        getJobtitleInfoTableList(opt.data.employeeId); // 职称信息列表
                        getCertificateInfoTableList(opt.data.employeeId); // 证书信息列表
                        getPostInformationTableList(opt.data.employeeId); //岗位信息列表
                        getEducationTableList(opt.data.employeeId); // 学历信息列表
                        getWorkExperienceTableList(opt.data.employeeId); // 工作经历列表
                        getFamilyInfoTableList(opt.data.employeeId); // 家庭信息列表
                        getPapersBooksTableList(opt.data.employeeId); // 论文及著作列表
                        getRewardPenaltyTableList(opt.data.employeeId); // 奖惩记录列表
                        getStandardizedTrainingTableList(opt.data.employeeId); // 规范化培训信息列表
                    } else {
                        initProvince(allCityData);
                        form.render()
                    }
                    
 
                    
                    
                    //入职文件
                    $.multiplePublicUpload($("#employeeAddFileupload"), {
                        formId: "employeeAddFileForm",
                        fileTableEl: $("#grid-table-files-employeeAdd"),
                        deleted: true,
                        width: 800,
                        formData: {
                            businessId: businessId || randomString() 
                        },
                        callback: function (res) {
                        }
                    });
                    //执业情况文件
                    $.multiplePublicUpload($("#employeeZyqkAddFileupload"), {
                        formId: "employeeZyqkAddFileForm",
                        fileTableEl: $("#grid-table-files-Zyqk-employeeAdd"),
                        deleted: true,
                        width: 800,
                        formData: {
                            businessId: businessId3 || randomString() 
                        },
                        callback: function (res) {
                        	employeeZyqkAddFileuploadLenth = res.length;
                        }
                    });
                    //聘任情况文件
                    $.multiplePublicUpload($("#employeePrqkAddFileupload"), {
                        formId: "employeePrqkAddFileForm",
                        fileTableEl: $("#grid-table-files-Prqk-employeeAdd"),
                        deleted: true,
                        width: 800,
                        formData: {
                            businessId:businessId2 || randomString() 
                        },
                        callback: function (res) {
                        	employeePrqkAddFileuploadLenth = res.length;
                        }
                    });
                },
                end: function () {
                    opt.ref && opt.ref();
                    
                }
            });
            
            function checkHistoryPost(employeeId){
                $.ajax({
                    type: "get",
                    contentType: "application/json; charset=utf-8",
                    url: common.url + "/ts-hrms/historypostinformation/"+employeeId,
                    success: function (res) {
                    	if(res.success ){
                    		if(res.object && res.object.length > 0){
                    			//禁用
                    			$("#postInfoAddForm select[name='postCategory']").unbind();
                    			$("#postInfoAddForm select[name='postCategory']").addClass("layui-disabled");
                    		}
                    }}
                });
            }

            //监听编制类别
   /*         form.on('select(employeeCategory)', function (data) {
                //技术人员编制 就是设置为是专业技术人员
                if (data.value == 5 || data.value == 6) {
                    $("#settingBasicListBox #workNature").val("1");
                    form.render('select');
                } else {
                    $("#settingBasicListBox #workNature").val("2");
                    form.render('select');//select是固定写法 不是选择器
                }
            });*/


            // 左侧tab切换
            $(".contractDetailLeft .control-item").click(function () {
                $(this).addClass('shell-active').siblings().removeClass("shell-active");
                var getHrefId = $(this).attr("hrefId");
                $("#" + getHrefId).addClass('shell-active').siblings().removeClass("shell-active");
                $('#settingBasicListBox .contractDetailTabBox').hide();
                $('#settingBasicListBox #employeeAddFileForm').hide();
                if ($(this).index() == 0) {
                    $('#settingBasicListBox #employeeAddFileForm').show();
                }
                $('#settingBasicListBox .contractDetailTabBox').eq($(this).index()).show();
            });

            // 岗位类别监听事件
            form.on('select(postCategoryFilter)', function (data) {
                if (data.value) {
                    getPostList(data.value);
                } else {
                    $("#postId").html("");

                }
                form.render();
            });

            // 身份证号绑定输入事件
            $("#employeeAddForm").on("input propertychange", "#identityNumber", function () {
                var idCard = $(this).val() == null || $(this).val() == '' ? "" : $(this).val();
                if (idCard.length == 18) {
                    var birthday = getBirthdayFromIdCard(idCard);
                    $("#birthday").val(birthday);
                }
            });

            // 岗位名称点击事件
            $("#postNameBox").off("click").on("click", function () {
                if (!$('#postCategory').val()) {
                    layer.msg("请选择聘任岗位");

                }
            });

            //监听政治面貌
            form.on('select(politicalStatus)', function (data) {
                if (data.value == 1) {
                    $('#partyDate').attr('lay-verify', 'required');
                    $('#partyDateSpan').show();
                } else {
                    $('#partyDate').removeAttr('lay-verify');
                    $('#partyDateSpan').hide();
                }
            })
            
           
            //监听编制类型
            form.on('select(establishmentType)', function (data) {
                if (data.value == 1) {
                    $('#employeeCategory').attr('lay-verify', 'required');
                    $('#employeeCategorySpan').show();
                } else {
                    $('#employeeCategory').removeAttr('lay-verify');
                    $('#employeeCategorySpan').hide();
                }
            })
            // 薪级类别监听事件
            form.on('select(salaryLevelCategoryFilter)', function (data) {
                if (data.value) {
                    getSalaryLevelList(data.value);
                } else {
                    $("#salaryLevelId").html("");
                }
                form.render();
            });

            // 薪级名称点击事件
            $("#salaryNameBox").off("click").on("click", function () {
                if (!$('#salaryLevelCategory').val()) {
                    layer.msg("请选择薪级类别");
                }
            });

            // 监听岗位信息保存
            form.on('submit(postInfoAddSaveFilter)', function (data) {
            	
            	//判断附件是否为空
            	if(employeePrqkAddFileuploadLenth <= 0 ){
            		layer.msg('附件不能为空！');
            		return;
            	}
            	
                if ($("#employeeId").val()) {
                    data.field.employeeId = $("#employeeId").val();
                    data.field.businessId2 = $("#employeePrqkAddFileForm [name='businessId']").val();
                    data.field.postUpdate = '1';
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/employee/update",
                        data: JSON.stringify(data.field),
                        success: function (res) {
                            if (res.success) {
                            	getPostInformationTableList(opt.data.employeeId);
                                layer.msg('操作成功！');
                            } else {
                                layer.msg(res.message || '操作失败！');
                            }
                        }
                    });
                    return false;
                } else {
                    layer.msg('请先保存员工信息');
                    return false;
                }
            });
            
            //执业情况
            form.on('submit(practiceSituationSaveFilter)', function (data) {
            	
            	if(employeeZyqkAddFileuploadLenth <= 0){
            		layer.msg('附件不能为空！');
            		return false;
            	}
            	
                if ($("#employeeId").val()) {
                    data.field.employeeId = $("#employeeId").val();
                    data.field.businessId3 = $("#employeeZyqkAddFileForm [name='businessId']").val();
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/employee/update",
                        data: JSON.stringify(data.field),
                        success: function (res) {
                            if (res.success) {
                                layer.msg('操作成功！');
                            } else {
                                layer.msg(res.message || '操作失败！');
                            }
                        }
                    });
                    return false;
                } else {
                    layer.msg('请先保存员工信息');
                    return false;
                }
            });            
            

            // 监听聘任情况保存
            form.on('submit(employmentSituationFilter)', function (data) {
                return false
            });




            // 监听员工信息保存
            form.on('submit(employeeAddFormSaveFilter)', function (data) {
                var employeeId = $("#employeeId").val(); // 员工ID
                if (data.field.province && data.field.province != '') {
                    data.field.bornAddress = data.field.province + '-' + data.field.city + '-' + data.field.area;
                    if (data.field.area && data.field.area != '') {
                        data.field.bornAddressName = allCityData[86][data.field.province] + '-' + allCityData[data.field.province][data.field.city] + '-' + allCityData[data.field.city][data.field.area];
                    } else {
                        data.field.bornAddressName = allCityData[86][data.field.province] + '-' + allCityData[data.field.province][data.field.city];
                    }
                } else {
                    data.field.bornAddress = '';
                }
                data.field.businessId = $("#employeeAddFileForm [name='businessId']").val();
                if (isEmpty(employeeId)) {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/employee/save",
                        data: JSON.stringify(data.field),
                        success: function (res) {
                            if (res.success) {
                                layer.msg('保存成功！');
                                $("#employeeId").val(res.object.employeeId); // 员工ID反写
                                $("#employeeNo").val(res.object.employeeNo); // 员工工号反写
                                if (isEmpty($("#oldEmployeeNo").val())) {
                                    $("#oldEmployeeNo").val(res.object.employeeNo); // 编制号(老系统工号)
                                }
                                $(".otherAdd").show();
                            } else {
                                layer.msg(res.message || '操作失败！');
                            }
                        }
                    });
                } else {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/employee/update",
                        data: JSON.stringify(data.field),
                        success: function (res) {
                            if (res.success) {
                                layer.msg('保存成功！');
                            } else {
                                layer.msg(res.message || '操作失败！');
                            }
                        }
                    });
                }
                return false;
            });

            // 选择行事件
            $(".otherInfoTable tbody").off("click", "tr").on("click", "tr", function () {
                $(this).siblings().removeClass("currTr");
                $(this).addClass("currTr");
            });

            // 组织机构下拉选择树
            treeSelect();
            function treeSelect() {
                zTreeSearch.init('#systemmanageOrgDepUserPrevDep', {
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        $("#employeeOrgId").val(treeNode.id); // 组织机构ID
                        $("#orgName").val(treeNode.name); // 组织机构名称
                    }
                });
            }
            //头像
            upload.render({
                elem: '#employeeAddForm #uploadAvatarBtn',
                url: common.url + '/ts-hrms/file/v2/upload',
                // url: common.url + '/ts-base-data/file/v2/upload',
                before: function (obj) {
                },
                done: function (res) {
                    if (res.code > 0) {
                        return layer.msg('上传失败');
                    } else {
                        $('#employeeAddForm #userAvatar').val(res.object);
                        $("#employeeAddForm #userAvatarImg").attr("src", res.object).show();
                    }
                },
                error: function () {
                }
            });



            // 出生日期的时间选择器
            laydate.render({
                elem: '#birthday',
                trigger: 'click'
            });
            // 岗位受聘时间的时间选择器
            laydate.render({
                elem: '#unitStartDate',
                trigger: 'click'
            });
            // 入党时间的时间选择器
            laydate.render({
                elem: '#partyDate',
                trigger: 'click'
            });
            // 入职时间的时间选择器
            laydate.render({
                elem: '#entryDate',
                trigger: 'click',
                done: function (value) {
                    $('#thisUnitContinuousSeniority').val(getDiffYearByDate(value)); // 本单位连续工龄
                }
            });
            // 参加工作时间的时间选择器
            laydate.render({
                elem: '#workStartDate',
                trigger: 'click',
                done: function (value) {
                    $('#continuousSeniority').val(getDiffYearByDate(value)); // 连续工龄
                }
            });

            //起聘时间
            laydate.render({
                elem: '#startEmployDate',
                trigger: 'click',
            });
            //终聘时间
            laydate.render({
                elem: '#endEmployDate',
                trigger: 'click',
            });

            //现职务任职时间
            laydate.render({
                elem: '#jobDescriptionTypeTime',
                trigger: 'click',
            });

            //兼任职务时间
            laydate.render({
                elem: '#concurrentPositionTime',
                trigger: 'click',
            });

            //起聘时间
            laydate.render({
                elem: '#startEmployDate',
                trigger: 'click',
            });

            //终聘时间
            laydate.render({
                elem: '#endEmployDate',
                trigger: 'click',
            });
            
            
            //聘任日期
           laydate.render({
                elem: '#postInfoAddForm [name="employDutyDate"]',
                trigger: 'click',
            });

            //任同职级时间
            laydate.render({
                elem: '#postInfoAddForm [name="employDutyEquallyDate"]',
                trigger: 'click',
            });
            
            laydate.render({
                elem: '#practiceSituationForm [name="operationDate"]',
                trigger: 'click',
            });

            // 添加职称信息
            $("#jobtitleInfoAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/jobTitle/addJobTitle', {
                    title: '添加职称信息',
                });
            });

            // 编辑职称信息
            $("#jobtitleInfoEdit").click(function () {
                var id = $("#jobtitleInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/jobTitle/addJobTitle', {
                    title: '编辑职称信息',
                    data: { employeeId: employeeId, jobtitleInfoId: id },
                });
            });
            // 删除职称信息           
            $(".contractDetailTabBox").off("click", "#jobtitleInfoDel").on("click", "#jobtitleInfoDel", function () {
                var id = $("#jobtitleInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/jobtitleInfo/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getJobtitleInfoTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });
            
            
            //岗位信息  start
            // 添加职称信息
            $("#postInformationAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/historyPost/add', {
                    title: '添加职称信息',
                });
            });

            // 编辑岗位信息
            $("#postInformationEdit").click(function () {
                var id = $("#postInformationTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/historyPost/add', {
                    title: '岗位信息',
                    data: { employeeId: employeeId, id: id },
                });
            });
            // 删除职称信息           
            $(".contractDetailTabBox").off("click", "#postInformationDel").on("click", "#postInformationDel", function () {
                var id = $("#postInformationTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/historypostinformation/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getPostInformationTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });
            
            //岗位信息end


            // 添加执业情况
            $("#practiceSituationAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/practiceSituation/index', {
                    title: '添加执业情况',
                    employeeId: opt.data.employeeId,
                    ref: getPracticeSituationTableList
                });
            });

            // 编辑职称信息
            $("#practiceSituationEdit").click(function () {
                var id = $("#practiceSituationTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/practiceSituation/index', {
                    title: '编辑职执业情况',
                    employeeId: opt.data.employeeId,
                    data: { employeeId: employeeId, doctorCertifiedId: id },
                    ref: getPracticeSituationTableList
                });
            });
            // 删除职称信息           
            $(".contractDetailTabBox").off("click", "#practiceSituationEel").on("click", "#practiceSituationDel", function () {
                var id = $("#practiceSituationTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/doctorcertified/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                getPracticeSituationTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加证书信息
            $("#certificateInfoAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/certificateInfo/addCertificateInfo', {
                    title: '添加证书信息'
                });
            });
            // 编辑证书信息
            $("#certificateInfoEdit").click(function () {
                var id = $("#certificateInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/certificateInfo/addCertificateInfo', {
                    title: '编辑证书信息',
                    data: { employeeId: employeeId, certificateInfoId: id }
                });
            });
            // 删除证书信息       
            $(".contractDetailTabBox").off("click", "#certificateInfoDel").on("click", "#certificateInfoDel", function () {
                var id = $("#certificateInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/certificateInfo/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getCertificateInfoTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加学历信息
            $("#educationInfoAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/education/addEducation', {
                    title: '添加学历信息',
                });
            });
            // 编辑学历信息
            $("#educationInfoEdit").click(function () {
                var id = $("#educationInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/education/addEducation', {
                    title: '编辑学历信息',
                    data: { employeeId: employeeId, educationInfoId: id },
                });
            });
            // 删除学历信息  
            $(".contractDetailTabBox").off("click", "#educationInfoDel").on("click", "#educationInfoDel", function () {
                var id = $("#educationInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/educationInfo/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getEducationTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加工作经历
            $("#workExperienceAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/workExperience/addWorkExperience', {
                    title: '添加工作经历'
                });
            });
            // 编辑工作经历
            $("#workExperienceEdit").click(function () {
                var id = $("#workExperienceTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/workExperience/addWorkExperience', {
                    title: '编辑工作经历',
                    data: { employeeId: employeeId, workExperienceId: id }
                });
            });
            // 删除工作经历 
            $(".contractDetailTabBox").off("click", "#workExperienceDel").on("click", "#workExperienceDel", function () {
                var id = $("#workExperienceTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/workExperience/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getWorkExperienceTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加家庭信息
            $("#familyInfoAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/familyInfo/addFamilyInfo', {
                    title: '添加家庭信息'
                });
            });
            // 编辑家庭信息
            $("#familyInfoEdit").click(function () {
                var id = $("#familyInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/familyInfo/addFamilyInfo', {
                    title: '编辑家庭信息',
                    data: { employeeId: employeeId, familyInfoId: id }
                });
            });
            // 删除家庭信息 
            $(".contractDetailTabBox").off("click", "#familyInfoDel").on("click", "#familyInfoDel", function () {
                var id = $("#familyInfoTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/familyInfo/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getFamilyInfoTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加论文及著作
            $("#papersBooksAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/papersBooks/addPapersBooks', {
                    title: '添加论文及著作'
                });
            });
            // 编辑论文及著作
            $("#papersBooksEdit").click(function () {
                var id = $("#papersBooksTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/papersBooks/addPapersBooks', {
                    title: '编辑论文及著作',
                    data: { employeeId: employeeId, papersBooksId: id }
                });
            });
            // 删除论文及著作
            $(".contractDetailTabBox").off("click", "#papersBooksDel").on("click", "#papersBooksDel", function () {
                var id = $("#papersBooksTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/papersBooks/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getPapersBooksTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加奖惩记录
            $("#rewardPenaltyAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/rewardPenalty/addRewardPenalty', {
                    title: '添加奖惩记录'
                });
            });
            // 编辑奖惩记录
            $("#rewardPenaltyEdit").click(function () {
                var id = $("#rewardPenaltyTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/rewardPenalty/addRewardPenalty', {
                    title: '编辑奖惩记录',
                    data: { employeeId: employeeId, rewardPenaltyId: id }
                });
            });
            // 删除奖惩记录
            $(".contractDetailTabBox").off("click", "#rewardPenaltyDel").on("click", "#rewardPenaltyDel", function () {
                var id = $("#rewardPenaltyTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条需要删除的数据！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/rewardPenalty/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getRewardPenaltyTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });

            // 添加规范化培训信息
            $("#standardizedTrainingAdd").click(function () {
                $.quoteFun('/personnelmgr/employee/modules/standardizedTraining/addStandardizedTraining', {
                    title: '添加规范化培训信息'
                });
            });
            // 编辑规范化培训信息
            $("#standardizedTrainingEdit").click(function () {
                var id = $("#standardizedTrainingTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！');
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                $.quoteFun('/personnelmgr/employee/modules/standardizedTraining/addStandardizedTraining', {
                    title: '编辑规范化培训信息',
                    data: { employeeId: $("#employeeId").val(), standardizedTrainingId: id }
                });
            });
            // 删除规范化培训信息
            $(".contractDetailTabBox").off("click", "#standardizedTrainingDel").on("click", "#standardizedTrainingDel", function () {
                var id = $("#standardizedTrainingTable").find("tr.currTr").attr("data-id"); // 获取到记录ID
                if (isEmpty(id)) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                var employeeId = $("#employeeId").val(); // 员工ID
                layer.confirm('确定要删除该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/standardizedTraining/deletedById/" + id,
                        success: function (res) {
                            if (res.success) {
                                layer.close(layer.index);
                                getStandardizedTrainingTableList(employeeId);
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败');
                            }
                        }
                    });
                }, function () { });
            });


            form.on('select(provicneId)', function (data) {
                $('#city').html('<option value="">请选择城市</option>');
                $('#area').html('<option value="">请选择区域</option>');
                initCity(allCityData[data.value]);
                form.render();
                initArea(allCityData[$('#city option').val()])
                form.render();
            });

            form.on('select(cityId)', function (data) {
                $('#area').html('<option value="">请选择区域</option>');
                initArea(allCityData[data.value]);
                form.render();
            });

        });
    }
});

//省市区联动
function initProvince(data, defaultData) {
    for (var key in data[86]) {
        $('#province').append('<option value="' + key + '">' + data[86][key] + '</option>');
    }
}

function initCity(data, defaultData) {
    var html = ''
    for (var key in data) {
        html += '<option value="' + key + '">' + data[key] + '</option>'
        $('#city').html(html);
    }
}

function initArea(data, defaultData) {
    var html = ''
    for (var key in data) {
        html += '<option value="' + key + '">' + data[key] + '</option>';
    }
    $('#area').html(html);
}

/**
 * 获取职务列表
 */
function getPositionList() {
    var html = '<option value="">请选择职务</option>';
    var _data = {};
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-basics-bottom/position/getList",
        data: JSON.stringify(_data),
        async: false,
        success: function (res) {
            if (res.object != null) {
                $.each(res.object, function (i, v) {
                    html += '<option value="' + v.positionId + '">' + v.positionName + '</option>';
                });
                $("#positionId").html(html);
                $("#concurrentPosition").html(html);
            }
        }
    });
}

/**
* 查询学历信息表格列表
* @param employeeId 员工ID
*/
function getEducationTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/educationInfo/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    var fileList = getJobtitleInfoFileList(data[i].educationInfoId);
                    html +=
                        '<tr data-id="' + data[i].educationInfoId + '">' +
                        '<td>' + data[i].schoolName + '</td>' +
                        '<td>' + data[i].professional + '</td>' +
                        '<td>' + data[i].educationTypeText + '</td>' +
                        '<td>' + data[i].schoolSystemText + '</td>' +
                        '<td>' + data[i].learnWayText + '</td>' +
                        '<td>' + data[i].startTime + '</td>' +
                        '<td>' + data[i].endTime + '</td>' +
                        '<td>' + (data[i].highestLevel == "1" ? "是" : "否") + '</td>';
                    if (fileList != null && fileList.length > 0) {
                        html += '<td>';
                        $.each(fileList, function (i, item) {
                            html += "<a style='color:blue' href='" + common.url + '/ts-base-data/file/download/' + item.fileId + "'>" + item.fileName + "</a><br>";
                        })
                        html += '</td>';
                    } else {
                        html += '<td></td>';
                    }

                    html += '</tr>';
                }
                $("#educationInfoTable tbody").empty().append(html);
            }
        }
    });
}

//查询岗位信息列表
function getPostInformationTableList(employeeId) {
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/historypostinformation/"+employeeId,
//        data: JSON.stringify(param),
        async: false,
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                	 var fileList = getJobtitleInfoFileList(data[i].id);
                    html +=
                        '<tr data-id="' + data[i].id + '">' +
                        '<td>' + data[i].postCategory + '</td>' +
                        '<td>' + data[i].postId + '</td>' +
                        '<td>' + data[i].employDutyEquallyDate + '</td>' +
                        '<td>' + data[i].employDutyDuration + '</td>' ;
                        if (fileList != null && fileList.length > 0) {
                            html += '<td>';
                            $.each(fileList, function (i, item) {
                                html += "<a style='color:blue' href='" + common.url + '/ts-base-data/file/download/' + item.fileId + "'>" + item.fileName + "</a><br>";
                            })
                            html += '</td>';
                        } else {
                            html += '<td></td>';
                        }

                    html += '</tr>';
                }
                $("#postInformationTable tbody").empty().append(html);
            }
        }
    });
}

/**
* 查询职称信息表格列表
* @param employeeId 员工ID
*/
function getJobtitleInfoTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/jobtitleInfo/getList",
        data: JSON.stringify(param),
        async: false,
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    var fileList = getJobtitleInfoFileList(data[i].jobtitleInfoId);
                    html +=
                        '<tr data-id="' + data[i].jobtitleInfoId + '">' +
                        '<td>' + data[i].jobtitleCategoryText + '</td>' +
                        '<td>' + data[i].jobtitleLevelText + '</td>' +
                        '<td>' + data[i].jobtitleNameText + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].professionalName) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].acquisitionDate) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].assessmentAgency) + '</td>' +
                        '<td>' + data[i].acceptMethodText + '</td>' +
                        '<td>' + (data[i].highestLevel == "1" ? "是" : "否") + '</td>';
                    if (fileList != null && fileList.length > 0) {
                        html += '<td>';
                        $.each(fileList, function (i, item) {
                            html += "<a style='color:blue' href='" + common.url + '/ts-base-data/file/download/' + item.fileId + "'>" + item.fileName + "</a><br>";
                        })
                        html += '</td>';
                    } else {
                        html += '<td></td>';
                    }

                    html += '</tr>';
                }
                $("#jobtitleInfoTable tbody").empty().append(html);
            }
        }
    });
}

/**
 * 获取岗位名称下拉列表
 * @param categoryId 岗位类别ID
 * @returns
 */
function getPostList(categoryId) {
    var html = "<option value=''>请选择</option>";
    var _data = { postCategory: categoryId };

    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-basics-bottom/post/getList",
        data: JSON.stringify(_data),
        async: false,
        success: function (res) {
            if (res.object != null) {
                $.each(res.object, function (i, v) {
                    html += '<option value="' + v.postId + '">' + v.postName + '</option>';
                });
                $("#postNameBox #postId").html(html);
            }
        }
    });
}

/**
 * 获取薪级下拉列表
 * @param categoryId 薪级类别ID
 * @returns
 */
function getSalaryLevelList(categoryId) {
    var html = "<option value=''>请选择</option>";
    var _data = { salaryLevelCategory: categoryId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/salaryLevel/combobox",
        data: JSON.stringify(_data),
        async: false,
        success: function (res) {
            if (res.object != null) {
                $.each(res.object, function (i, v) {
                    html += '<option value="' + v.salaryLevelId + '">' + v.salaryLevelName + '</option>';
                });
                $("#salaryLevelId").html(html);
            }
        }
    });
}

/**
 * 获取执业情况表
 * @param employeeId 员工ID
 */
function getPracticeSituationTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/doctorcertified/list",
        data: JSON.stringify(param),
        async: false,
        success: function (res) {
            var data = res.rows;
            if (data && data.length > 0) {
                var html = "";
                for (var i = 0; i < data.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].doctorCertifiedId + '">' +
                        '<td>' + data[i].registerDate + '</td>' +
                        '<td>' + data[i].registerOrg + '</td>' +
                        '<td>' + data[i].registerScope + '</td>' +
                        '<td>' + data[i].registerNumber + '</td>';
                    html += '</tr>';
                }
                $("#practiceSituationTable tbody").empty().append(html);
            }
        }
    });
}

function getJobtitleInfoFileList(jobtitleInfoId) {
    var fileList = null;
    $.ajax({
        url: common.url + "/ts-base-data/file/list/" + jobtitleInfoId,
        type: 'post',
        async: false,
        success: function (resp) {
            fileList = resp.object;
        }
    })
    return fileList;
}

/**
 * 查询证书信息表格列表
 * @param employeeId 员工ID
 */
function getCertificateInfoTableList(employeeId) {
    var param = { employeeId: employeeId, approvalStatus: 4 };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/certificateInfo/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    var fileList = getJobtitleInfoFileList(data[i].certificateInfoId);
                    html +=
                        '<tr data-id="' + data[i].certificateInfoId + '">' +
                        '<td>' + data[i].certificateName + '</td>' +
                        '<td>' + data[i].certificateNumber + '</td>' +
                        '<td>' + data[i].certificateTypeText + '</td>' +
                        '<td>' + data[i].acceptMethodText + '</td>' +
                        '<td>' + data[i].certificateAgency + '</td>' +
                        '<td>' + data[i].acquisitionDate + '</td>';
                    if (fileList != null && fileList.length > 0) {
                        html += '<td>';
                        $.each(fileList, function (i, item) {
                            html += "<a style='color:blue' href='" + common.url + '/ts-base-data/file/download/' + item.fileId + "'>" + item.fileName + "</a><br>";
                        })
                        html += '</td>';
                    } else {
                        html += '<td></td>';
                    }

                    html += '</tr>';
                }
                $("#certificateInfoTable tbody").empty().append(html);
            }
        }
    });
}



/**
 * 查询工作经历表格列表
 * @param employeeId 员工ID
 */
function getWorkExperienceTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/workExperience/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].workExperienceId + '">' +
                        '<td>' + data[i].workUnit + '</td>' +
                        '<td>' + data[i].deptName + '</td>' +
                        '<td>' + data[i].post + '</td>' +
                        '<td>' + data[i].startTime + '</td>' +
                        '<td>' + data[i].endTime + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].witness) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].witnessPhone) + '</td>' +
                        '</tr>';
                }
                $("#workExperienceTable tbody").empty().append(html);
            }
        }
    });
}

/**
 * 查询家庭信息表格列表
 * @param employeeId 员工ID
 */
function getFamilyInfoTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/familyInfo/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].familyInfoId + '">' +
                        '<td>' + data[i].memberName + '</td>' +
                        '<td>' + data[i].relationshipText + '</td>' +
                        '<td>' + data[i].workUnit + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].post) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].politicalStatusText) + '</td>' +
                        '<td>' + data[i].contactNumber + '</td>' +
                        '</tr>';
                }
                $("#familyInfoTable tbody").empty().append(html);
            }
        }
    });
}

/**
 * 查询论文著作表格列表
 * @param employeeId 员工ID
 */
function getPapersBooksTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/papersBooks/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].papersBooksId + '">' +
                        '<td>' + data[i].papersBooksTitle + '</td>' +
                        '<td>' + data[i].papersBooksSpeciality + '</td>' +
                        '<td>' + data[i].publishDate + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].publishingOffice) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].serialNumber) + '</td>' +
                        '</tr>';
                }
                $("#papersBooksTable tbody").empty().append(html);
            }
        }
    });
}

/**
 * 查询奖惩记录表格列表
 * @param employeeId 员工ID
 */
function getRewardPenaltyTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/rewardPenalty/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].rewardPenaltyId + '">' +
                        '<td>' + data[i].rewardPenaltyTitle + '</td>' +
                        '<td>' + data[i].rewardPenaltyTypeText + '</td>' +
                        '<td>' + data[i].rewardPenaltyDate + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].rewardPenaltyUnit) + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].releaseDate) + '</td>' +
                        '</tr>';
                }
                $("#rewardPenaltyTable tbody").empty().append(html);
            }
        }
    });
}

/**
 * 查询规培信息表格列表
 * @param employeeId 员工ID
 */
function getStandardizedTrainingTableList(employeeId) {
    var param = { employeeId: employeeId };
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/standardizedTraining/getList",
        data: JSON.stringify(param),
        success: function (res) {
            var data = res.object;
            if (data) {
                var html = "";
                for (var i = 0; i < res.object.length; i++) {
                    html +=
                        '<tr data-id="' + data[i].standardizedTrainingId + '">' +
                        '<td>' + data[i].profession + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].certificateNo) + '</td>' +
                        '<td>' + data[i].acquisitionDate + '</td>' +
                        '<td>' + isNullRerturnEmptyStr(data[i].place) + '</td>' +
                        '<td>' + data[i].startTime + '</td>' +
                        '<td>' + data[i].endTime + '</td>' +
                        '</tr>';
                }
                $("#standardizedTrainingTable tbody").empty().append(html);
            }
        }
    });
}