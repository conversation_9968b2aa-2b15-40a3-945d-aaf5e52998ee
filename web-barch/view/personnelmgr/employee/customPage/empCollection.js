"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(["form", "laytpl", "upload", "laydate", "trasen"], function () {
      var trasen = layui.trasen;
      var form = layui.form;
      var laytpl = layui.laytpl;
      var upload = layui.upload;
      var laydate = layui.laydate;
      var wins = layer.open({
        type: 1,
        title: opt.title || "个人信息采集",
        closeBtn: 1,
        maxmin: false,
        shadeClose: false,
        skin: "emp-collection-box",
        area: ["100%", "100%"], //宽高
        content: html,
        success: function (layero, index) {
          const employeeId = opt.employeeId;
		  var _url = "http://newoa.liuyang120.com:9000/ts-basics-bottom/cusotmEmployee/grxxcjb/"+employeeId;
          $.ajax({
            type: "get",
            async: false,
            url:_url
            ,
            success: function (res) {
              if (res.success) {
                let data = res.object;

                $("#EmpCollection #caijishijian").text(data.caijishijian || "");
                $("#EmpCollection #employeeName").text(data.employeeName || "");
                $("#EmpCollection #genderText").text(data.genderText || "");
                $("#EmpCollection #nationalityName").text(
                  data.nationalityName || ""
                );
                $("#EmpCollection #birthday").text(data.birthday || "");
                $("#EmpCollection #orgName").text(data.orgName || "");
                $("#EmpCollection #zuigaoxueli").text(data.zuigaoxueli || "");
                $("#EmpCollection #establishmentTypeText").text(
                  data.establishmentTypeText || ""
                );
                $("#EmpCollection #empAge").text(data.empAge || "");
                $("#EmpCollection #workStartDate").text(
                  data.workStartDate || ""
                );
                $("#EmpCollection #entryDate").text(data.entryDate || "");
                $("#EmpCollection #positionName").text(data.positionName || "");
                $("#EmpCollection #personalIdentityName").text(
                  data.personalIdentityName || ""
                );
                $("#EmpCollection #phoneNumber").text(data.phoneNumber || "");
                $("#EmpCollection #politicalStatusText").text(
                  data.politicalStatusText || ""
                );
                $("#EmpCollection #partyDate").text(data.partyDate || "");
                $("#EmpCollection #xianzyouzhicheng").text(
                  data.xianzyouzhicheng || ""
                );
                $("#EmpCollection #address").text(data.address || "");
                $("#EmpCollection #birthplace").text(data.birthplace || "");
                $("#EmpCollection #idCard").text(data.idCard || "");

                // 学习经历
                {
                  let lyzXuexi = data.lyzXuexi || [{}];
                  let lyzXuexiTr = `<tr>
                                  <td rowspan=${
                                    lyzXuexi.length + 1
                                  }>学习经历</td>
                                    <td>学历</td>
                                    <td>入学时间</td>
                                    <td>毕业时间</td>
                                    <td colspan="2">毕业院校</td>
                                    <td>所学专业</td>
                                    <td>学位</td>
                                  </tr>`;
                  lyzXuexi.forEach((item, index) => {
                    let tr = "";
                    tr += `
                <tr>
                    <td>${item.v1 || ""}</td>
                    <td>${item.v2 || ""}</td>
                    <td>${item.v3 || ""}</td>
                    <td colspan="2">${item.v4 || ""}</td>
                    <td>${item.v5 || ""}</td>
                    <td>${item.v6 || ""}</td>
                  </tr>
                  `;
                    lyzXuexiTr += tr;
                  });
                  $("#EmpCollection table tbody").append(lyzXuexiTr);
                }

                // 工作经历
                let lyzYuanwai = data.lyzYuanwai || [{}];
                let lyzYuannei = data.lyzYuannei || [{}];
                let workLength = lyzYuanwai.length + lyzYuannei.length;

                let lyzYuanwaiTr = "";
                lyzYuanwai.forEach((item, index) => {
                  let tr = `
                <tr>
                    <td>${item.v1 || ""}</td>
                    <td>${item.v2 || ""}</td>
                    <td colspan="2">${item.v3 || ""}</td>
                    <td>${item.v4 || ""}</td>
                    <td>${item.v5 || ""}</td>
                  </tr>
                  `;
                  lyzYuanwaiTr += tr;
                });
                let lyzYuanneiTr = "";
                lyzYuannei.forEach((item, index) => {
                  let tr = `
                <tr>
                    <td>${item.v1 || ""}</td>
                    <td>${item.v2 || ""}</td>
                    <td colspan="2">${item.v3 || ""}</td>
                    <td>${item.v4 || ""}</td>
                    <td>${item.v5 || ""}</td>
                  </tr>
                  `;
                  lyzYuanneiTr += tr;
                });
                let workTr = `<tr>
                              <td rowspan=${workLength + 2}>工作经历</td>
                                <td rowspan=${
                                  lyzYuanwai.length + 1
                                }>外院任职经历</td>
                                <td>开始时间</td>
                                <td>结束时间</td>
                                <td colspan="2">工作单位</td>
                                <td>所在部门</td>
                                <td>岗位</td>
                              </tr>
                              ${lyzYuanwaiTr}
                            <tr>
                                <td rowspan=${
                                  lyzYuannei.length + 1
                                }>本院工作经历</td>
                                <td>开始时间</td>
                                <td>结束时间</td>
                                <td colspan="2">工作单位</td>
                                <td>所在部门</td>
                                <td>岗位</td>
                            </tr>
                            ${lyzYuanneiTr}
                              `;
                $("#EmpCollection table tbody").append(workTr);

                // 职称简历
                {
                  let lyzZhicheng = data.lyzZhicheng || [{}];
                  let lyzZhichengTr = `<tr>
                                    <td rowspan=${
                                      lyzZhicheng.length + 1
                                    }>职称简历</td>
                                      <td>职称类别</td>
                                      <td>职称取得时间</td>
                                      <td colspan="3">职称名称</td>
                                      <td colspan="2">专业名称</td>
                                    </tr>`;
                  lyzZhicheng.forEach((item, index) => {
                    let tr = "";
                    tr += `
                <tr>
                    <td>${item.v1 || ""}</td>
                    <td>${item.v2 || ""}</td>
                    <td colspan="3">${item.v3 || ""}</td>
                    <td colspan="2">${item.v4 || ""}</td>
                  </tr>
                  `;
                    lyzZhichengTr += tr;
                  });
                  $("#EmpCollection table tbody").append(lyzZhichengTr);
                }

                // 进修经历
                {
                  let lyzJinxiu = data.lyzJinxiu || [{}];
                  let lyzJinxiuTr = `<tr>
                                      <td rowspan=${
                                        lyzJinxiu.length + 1
                                      }>进修经历</td>
                                        <td colspan="2">进修开始时间</td>
                                        <td colspan="2">进修结束时间</td>
                                        <td colspan="3">进修医院</td>
                                      </tr>`;
                  lyzJinxiu.forEach((item, index) => {
                    let tr = "";
                    tr += `
                  <tr>
                      <td colspan="2">${item.v1 || ""}</td>
                      <td colspan="2">${item.v2 || ""}</td>
                      <td colspan="3">${item.v3 || ""}</td>
                    </tr>
                    `;
                    lyzJinxiuTr += tr;
                  });
                  $("#EmpCollection table tbody").append(lyzJinxiuTr);
                }

                // 规培经历
                {
                  let lyzGuipei = data.lyzGuipei || [{}];
                  let lyzGuipeiTr = `<tr>
                                    <td rowspan=${
                                      lyzGuipei.length + 1
                                    }>规培经历</td>
                                      <td colspan="2">规培开始时间</td>
                                      <td colspan="2">规培结束时间</td>
                                      <td colspan="3">规培医院</td>
                                    </tr>`;
                  lyzGuipei.forEach((item, index) => {
                    let tr = "";
                    tr += `
                <tr>
                    <td colspan="2">${item.v1 || ""}</td>
                    <td colspan="2">${item.v2 || ""}</td>
                    <td colspan="3">${item.v3 || ""}</td>
                  </tr>
                  `;
                    lyzGuipeiTr += tr;
                  });
                  $("#EmpCollection table tbody").append(lyzGuipeiTr);
                }

                //复制上月班次
                $("#EmpCollection")
                  .off("click", "#EmpCollectionExport")
                  .on("click", "#EmpCollectionExport", function () {
                    $("#EmpCollection #EmpCollectionFormTable tr td").css(
                      "border",
                      "1px solid #000"
                    );

                    $("#EmpCollection #EmpCollectionFormTable tr td").css(
                      "text-align",
                      "center"
                    );

                    $("#EmpCollection #caijishijian").css("text-align", "left");

                    tableToExcel(
                      "EmpCollectionFormTable",
                      "EmpCollectionExport",
                      "个人信息采集"
                    );

                    setTimeout(() => {
                      $("#EmpCollection #EmpCollectionFormTable tr td").css(
                        "border",
                        "0px solid #000"
                      );
                      $("#EmpCollection #caijishijian").css(
                        "text-align",
                        "center"
                      );
                    }, 500);
                  });

                function tableToExcel(tableid, btnname, exprotName) {
                  var uri = "data:application/vnd.ms-excel;base64,",
                    template =
                      '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
                    base64 = function (s) {
                      return window.btoa(unescape(encodeURIComponent(s)));
                    },
                    dataFormat = function (s, c) {
                      return s.replace(/{(\w+)}/g, function (m, p) {
                        return c[p];
                      });
                    };
                  //根据ID获取table表格HTML
                  var table = document.getElementById(tableid);
                  var ctx = {
                    worksheet: "Worksheet",
                    table: table.innerHTML,
                  };

                  var alink = document.createElement("a");
                  alink.href = uri + base64(dataFormat(template, ctx));
                  alink.download = `${exprotName}.xls`;
                  alink.click();
                }
              }
            },
          });
        },
      });
    });
  };
});
