<style type="text/css"></style>
<div class="content-box bg-trans" id="personnelmgrEmployeeDiv">
    <div class="trasen-con-box" style="left: 0; right: 0">
        <div class="oa-search-tree">
            <div style="padding: 5px 10px">
                <input type="text" name="name" class="layui-input" placeholder="请输入组织机构" id="rloeGroupTreeSearch" />
            </div>
            <div class="ztree-box scrollbar-box" style="top: 40px">
                <ul id="rloeGroupTree" class="ztree"></ul>
            </div>
        </div>

        <div class="transen-con-view-box">
            <div class="oa-nav-search">
                <form id="personnelmgrEmployeeQueryForm" class="layui-form">
                    <input type="hidden" name="orgId" id="orgId" />
                    <input type="hidden" name="orgIds" id="orgIds" />
                    <input type="hidden" name="userCode" id="userCode" />
                    <div class="layui-inline fl">
                        <!--                 <label class="layui-inline">工号</label> -->
                        <div class="layui-inline" style="width: 120px">
                            <input name="employeeNo" type="text" class="layui-input fl" placeholder="工号/姓名/简码" search-input="search" />
                        </div>
                    </div>
                    <!--             <div class="layui-inline fl mgl10"> -->
                    <!--                 <label class="layui-inline">姓名</label> -->
                    <!--                 <div class="layui-inline" style="width: 90px;"> -->
                    <!--                     <input name="employeeName" type="text" class="layui-input fl" placeholder="请输入姓名" search-input="search" /> -->
                    <!--                 </div> -->
                    <!--             </div> -->

                    <!-- 性别 -->
                    <div class="layui-inline fl mgl10">
                        <!--    				<span class="screenSelectTit">性别</span> -->
                        <div class="layui-inline" style="width: 90px">
                            <select name="sex" lay-verify="" lay-search="">
                                <option value="">性别</option>
                                <option value="0">男</option>
                                <option value="1">女</option>
                            </select>
                        </div>
                    </div>

                    <!--             <div class="layui-inline fl mgl10"> -->
                    <!--                 <label class="layui-inline">人员状态</label> -->
                    <!--                 <div class="layui-inline" style="width: 120px;" > -->
                    <!--                 	<select id="personnelmgr_employee_status" name="employeeStatus" ></select> -->
                    <!--                 </div> -->
                    <!--             </div>  -->

                    <!-- 个人身份 -->
                    <div class="layui-inline fl mgl10">
                        <div class="layui-inline" style="width: 90px">
                            <select id="personnelmgr_establishment_type" name="establishmentType" lay-search></select>
                        </div>
                    </div>

                    <!-- 个人身份 -->
                    <div class="layui-inline fl mgl10">
                        <!--                 <label class="layui-inline">个人身份</label> -->
                        <div class="layui-inline" style="width: 110px">
                            <select id="personnelmgr_personal_identity" name="personalIdentity" lay-search></select>
                        </div>
                    </div>

                    <div class="shell-search-box mgl10">
                        <button type="button" class="layui-btn" lay-submit="" lay-filter="personnelArchivesScreeningSub" search-btn="personEmployee" screen-search="personEmployee">搜索</button>
                        <!-- <button type="button" class="layui-btn" id="personnelArchivesScreenCRest2">重置</button>
                        <button type="button" class="layui-btn" id="personnelArchivesScreen" screen-box-tar="personEmployee">筛选</button> -->
                    </div>
                    <div class="shell-search-box">
                        <div class="search_list screen-search-input" screen-box-tar="personEmployee" screen-search-input="personEmployee" id="personnelArchivesScreen">
                            <i class="oaicon oa-icon-search_list"></i>
                        </div>
                        <button type="button" class="layui-btn oa-btn-reset" id="personnelArchivesScreenCRest2" screen-search="personEmployee" screen-clear="personEmployee"><i class="layui-icon layui-icon-refresh"></i></button>
                    </div>
                    <!-- <div class="shell-search-box screen-search-input">
                        <input type="text" class="layui-input" screen-search-input="personEmployee" readonly />
                    </div> -->
                </form>
                <div class="screen-box" screen-box="personEmployee">
                    <div class="screen-box_tit">更多查询条件</div>
                    <div class="screen-box_con">
                        <form class="layui-form row" id="personnelArchivesScreening">
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">出生日期</div>
                                </div>
                                <div class="layui-col-md7">
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="birthdayStartTime" id="emp_search_birthdayStartTime" autocomplete="off" placeholder="开始日期" class="layui-input layDate" />
                                    <span>-</span>
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="birthdayEndTime" id="emp_search_birthdayEndTime" autocomplete="off" placeholder="结束日期" class="layui-input layDate" />
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">工作日期</div>
                                </div>
                                <div class="layui-col-md7">
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="workStartDateStart" id="emp_search_workStartDateStart" autocomplete="off" placeholder="开始日期" class="layui-input layDate" />
                                    <span>-</span>
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="workStartDateEnd" id="emp_search_workStartDateEnd" autocomplete="off" placeholder="结束日期" class="layui-input layDate" />
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">入院日期</div>
                                </div>
                                <div class="layui-col-md7">
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="entryDateStart" id="emp_search_entryDateStart" autocomplete="off" placeholder="开始日期" class="layui-input layDate" />
                                    <span>-</span>
                                    <input type="text" style="display: inline-block; width: calc(50% - 10px)" name="entryDateEnd" id="emp_search_entryDateEnd" autocomplete="off" placeholder="结束日期" class="layui-input layDate" />
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">学历</div>
                                </div>
                                <div class="layui-col-md7">
                                    <select name="educationType" lay-verify="" lay-search="" id="personnelmgrEmployeeDiv_educationType"></select>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">政治面貌</div>
                                </div>
                                <div class="layui-col-md7">
                                    <select name="politicalStatus" lay-verify="" lay-search="" id="personnelmgrEmployeeDiv_politicalStatus"></select>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">专业英才</div>
                                </div>
                                <div class="layui-col-md7">
                                    <select name="doctorQualificationCertificate" lay-verify="" lay-search="">
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">杏林人才</div>
                                </div>
                                <div class="layui-col-md7">
                                    <select name="workNature" lay-verify="" lay-search="">
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-col-md3">
                                    <div class="title">中层干部</div>
                                </div>
                                <div class="layui-col-md7">
                                    <select name="midwife" lay-verify="" lay-search="">
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="screen-box_btn">
                        <span class="layui-btn" id="personnelArchivesScreeningSub" lay-submit="" lay-filter="personnelArchivesScreeningSub" screen-box-tar="personEmployee" screen-search="personEmployee">搜索</span>
                        <span class="layui-btn layui-btn-primary" id="personnelArchivesScreenCRest" screen-search="personEmployee">重置</span>
                        <span class="layui-btn layui-btn-primary" screen-box-tar="personEmployee">关闭</span>
                    </div>
                </div>
                <div class="fr">
                    <button type="button" class="layui-btn" id="personnelmgrEmployeeTableAdd" data-permission="on">新增</button>
                    <button type="button" class="layui-btn" id="personnelmgrEmployeeTableEditor" data-permission="on">编辑</button>
                    <button type="button" class="layui-btn" id="personnelmgrEmployeeTableEnable" data-permission="on">启用</button>
                    <!--             <button type="button" class="" id="personnelmgrZYTJxportBtn" data-permission="on">资源统计</button> -->
                    <div class="layui-btn layui-border-theme oa-more-btn">
                        更多
                        <i class="layui-icon layui-icon-down"></i>
                        <div class="oa-more-btn-box">
                            <button type="button" class="oa-more-btn-item" id="personnelmgrEmployeeTableBan" data-permission="on">禁用</button>
                            <button type="button" class="oa-more-btn-item" id="personnelmgrEmployeeTableImport" data-permission="on">导入</button>
                            <button type="button" class="oa-more-btn-item" id="personnelmgrEmployeeExportBtn" data-permission="on">导出</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="trasen-con-box">
                <div class="table-box">
                    <!-- 表单 -->
                    <table id="grid-table-personnelmgrEmployeeTable"></table>
                    <!-- 分页 -->
                    <div id="grid-pager-personnelmgrEmployeePager"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入弹窗 -->
<script type="text/html" id="personnelmgrEmployeeImportForm">
    <div class="layui-tab-content">
        <div class="personnelmgrEmployeeImportForm">
            <a class="layui-btn edi-button signIn" id="personnelmgrEmployeeImportBtn">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                员工导入
            </a>
            <a class="layui-btn edi-button edi-button-download" id="personnelmgrEmployeeDownTemplateBtn">
                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                员工模板下载
            </a>

            <br />
            <a class="layui-btn edi-button signIn" id="personnelmgrEducationImportBtn">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                学历导入
            </a>
            <a class="layui-btn edi-button edi-button-download" id="personnelmgrEducationDownTemplateBtn">
                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                学历模板下载
            </a>

            <br />
            <a class="layui-btn edi-button signIn" id="personnelmgrProfessionalImportBtn">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                职称导入
            </a>
            <a class="layui-btn edi-button edi-button-download" id="personnelmgrProfessionalDownTemplateBtn">
                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                职称模板下载
            </a>
        </div>
    </div>
</script>
