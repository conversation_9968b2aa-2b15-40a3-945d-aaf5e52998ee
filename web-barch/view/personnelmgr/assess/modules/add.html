<style></style>
<form id="assessAddForm" class="layui-form">
    <input type="hidden" name="id" />
		<input type="hidden" id="employeeName" name="employeeName" value="" />
		<input type="hidden" id="orgId" name="orgId" value="" />
		
    <div class="layui-content-box">
			
			<div class="layui-col-xs6">
			      <label class="shell-layui-form-label">
			          <span class="required">*</span>姓名
			      </label>
						<div class="shell-layer-input-box">
						    <select id="assessAddSelBox" name="employeeId" lay-verify="required" lay-search lay-filter="assessIdSelFilter"></select>
						</div>
			      
			  </div>
				
			<div class="layui-col-xs6">
			    <label class="shell-layui-form-label">
			        <span class="required">*</span>工号
			    </label>
			    <div class="shell-layer-input-box">
			        <input type="text" disabled="disabled" id="employeeNo" name="employeeNo" autocomplete="off" lay-verify="required" 
			        class="layui-input" placeholder="工号" />
			    </div>
			</div>
			
			<div class="layui-col-xs6">
			    <label class="shell-layui-form-label">
			        <span class="required">*</span>科室
			    </label>
			    <div class="shell-layer-input-box">
			        <input type="text" disabled="disabled" id="orgName" name="orgName" autocomplete="off" 
			        class="layui-input" placeholder="科室" />
			    </div>
			</div>
			
			<div class="layui-col-xs6">
			    <label class="shell-layui-form-label">
			        <span class="required">*</span>
			        考核年份
			    </label>
			    <div class="shell-layer-input-box">
			        <input type="text" autocomplete="off" id="assessYear" name="assessYear" lay-verify="required" class="layui-input" placeholder="请选择考核年份" />
			    </div>
			</div>
			
			<div class="layui-col-xs6" id="UnitAddFormItem">
			    <label class="shell-layui-form-label" id="assessOrgLabel">
			        <span class="required">*</span>上报单位
			    </label>
			    <div class="shell-layer-input-box">
					<select autocomplete="off" lay-filter="assessOrg" name="assessOrg" id="assessOrg" lay-verify="required" placeholder="上报单位" lay-search>
                <option value="">请选择</option>
                <option value="市人社局">市人社局</option>
                <option value="市组织部">市组织部</option>
            	</select>
			    </div>
			</div>
			
			<div class="layui-col-xs6">
				    <label class="shell-layui-form-label">
				        <span class="required">*</span>考核结果
				    </label>
				    <div class="shell-layer-input-box">
							<select lay-filter="assessResult" autocomplete="off" name="assessResult" id="assessResult" lay-verify="required" placeholder="考核结果" lay-search>
								<option value="">请先选择考核结果</option>
            	</select>
				    </div>
				</div>
				
				<div class="layui-col-xs12">
				    <label class="shell-layui-form-label">考核说明</label>
				    <div class="shell-layer-input-box">
				        <textarea name="remark" maxlength="200" class="layui-textarea"></textarea>
				    </div>
				</div>
				
        
        <div class="layui-col-xs12" id="fileUploadContent">
            <label class="shell-layui-form-label">附件</label>
            <div class="shell-layer-input-box">
            	<div class="fileUploadDiv" id="fileUploadDiv">
            		<img src="/static/img/other/upload.png" title="添加附件" class="addfileImg" />
            		<span class="addfile">添加附件</span>
            	</div>
            	<ul id="fileListUl"></ul>
            </div>
       </div>
    </div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn" lay-submit="" id="evaBaseSubmitCofirm" lay-filter="evaBaseSubmitCofirm">保存</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
    </div>
</form>
