"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
        layui.use(['form', 'trasen', 'upload'], function() {
            var form = layui.form,
                laydate = layui.laydate,
                upload = layui.upload,
                trasen = layui.trasen;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['700px', '400px'],
                skin: 'yourclass',
                content: html,
                success: function(layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal(layero, opt.data); // 渲染表格数据
                    }
                }
            });
            
			form.on('submit(trainPlanCancelSaveSub)', function (data) {
				// 模拟点击提交
				$('#cancellationReasonsSaveSub').trigger('click');
			});

            // 保存
            form.on('submit(cancellationReasonsSaveSub)', function(data) {
                var _url = common.url + "/ts-hrms/train/plan/cancel";
            	data.field.planState = 3;
            	var _data = JSON.stringify(data.field);
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: _url,
                    data: _data,
                    success: function(res) {
                        if (res.success) {
                        	opt.ref && opt.ref();
                            layer.msg('保存成功！');
                        } else {
                            layer.msg(res.message || '操作失败');
                        }
                        layer.closeAll();
                    }
                });
                return false;
            });
            
            // 取消
            $('#trainPlanCancelDiv #trainPlanCancelClose').off('click').on('click', function() {
                layer.closeAll();
            });

        });
    }
});