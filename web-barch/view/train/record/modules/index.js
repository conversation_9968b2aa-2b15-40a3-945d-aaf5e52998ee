"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
        layui.use(['form', 'laytpl', 'tree', 'layedit', 'laydate', 'trasen', 'workflow'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                layer = layui.layer,
                workflow = layui.workflow,
                trasen = layui.trasen;
            var trainPlanId = "";
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function(layero, index) {
                	initRecord(opt.data);
                	trainPlanId = opt.data;
                }
            });
            
            var trasenTable;
            
            function initTable(url,colModel){
            	$("#trainRecord .table-box").html('<table id="grid-table-trainRecordTable"></table><div id="grid-pager-trainRecordPager"></div>')
            	// 列表 '/ts-hrms/sysmenu/getlist'
	            trasenTable = new $.trasenTable("grid-table-trainRecordTable", {
	                url: common.url + url,
	                pager: 'grid-pager-trainRecordPager',
	                colModel: colModel,
	                shrinkToFit: true,
	                loadComplete: function () {},
					queryFormId: 'trainRecordform'
	            });
            }
            
            // 初始化需求
            function initRecord(trainPlanId){
            	var url = '/ts-hrms/train/record/getDataList?trainPlanId='+trainPlanId;
            	var colModel = [
            		{ label: 'id', name: 'trainRecordId', width: 95, editable: false,hidden: true},
                    { label: '工号', name: 'employeeNo', index: 'employee_no', width: 80, editable: false},
                    { label: '姓名', name: 'employeeName', index: 'employee_no',width: 60, editable: false},
                    { label: '科室Id', name: 'orgId', index: 'org_id', width: 60, editable: false,hidden: true},
                    { label: '科室', name: 'orgName', index: 'org_id', width: 60, editable: false},
                    { label: '培训主题Id', name: 'trainPlanId', index: 'train_plan_id', width: 120, editable: false,hidden: true},
                    { label: '培训主题', name: 'trainTopic', index: 'train_topic', width: 120, editable: false, sortable:false,hidden: true},
                    { label: '培训讲师', name: 'trainLecturer', index: 'train_lecturer', width: 80, editable: false,hidden: true},
                    { label: '学分', name: 'creditHour', index: 'credit_hour', width: 50, editable: false},
                    { label: '结果', name: 'result', index: 'result', width: 80, editable: false},
                    { label: '签到时间', name: 'signTime', index: 'sign_time', width: 80, editable: false},
                ]
            	initTable(url,colModel);
            }

            function refreshTable() {
                trasenTable.refresh()
            }
            
            form.on('submit(trainRecordSearch)', function (data) {
                refreshTable();
            });

/*            //新增
            $('#trainRecord').off('click', "#add").on('click', '#add', function () {
                opr = 'add';
                var uri = 'train/record/modules/add';
                var title = '新增培训记录';
                $.quoteFun(uri, {
                	title: title,
                    ref: refreshTable
                })
            });
            
            //修改
            $('#trainRecord').off('click', '#edit').on('click', '#edit', function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                opr = 'edit';
                var uri = 'train/record/modules/add';
                var title = '修改培训记录';
                $.quoteFun(uri, {
                    title: title,
                    data: rowData,
                    ref: refreshTable
                })
            });
            
            //删除
            $('#trainRecord').off('click', '#delete').on('click', '#delete', function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条需要删除的数据!')
                    return false;
                }
                layer.confirm('确定要删除当前选中数据？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                	var uri = '/ts-hrms/train/record/deletedById/' + rowData.trainRecordId;
                    trasen.post(common.url + uri, null, function (data) {
                        layer.closeAll();
                        refreshTable()
                    })
                })
            });
*/            
            //导出
            $("body").off("click", "#trainRecord #export").on("click", "#trainRecord #export", function () {
                layer.confirm('确定要导出吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var url = common.url + "/ts-hrms/train/record/export?";
                    var queryData = trasenTable.oTable.getGridParam("postData");
                    var exportParam = "";
                    for (var key in queryData) {
                        exportParam += (key + "=" + queryData[key] + "&")
                    }
                    //encodeURIComponent
                    location.href = (url + (exportParam) + "trainPlanId=" + trainPlanId);
                    layer.close(index);
                }, function () {
                });
            });
           
            // *************end
        });
    };

})

