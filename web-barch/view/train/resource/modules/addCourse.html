<style>
    #evaBaseFile {
        position: absolute;
        background: #009b8d;
        height: 20px;
        font-size: 12px;
        color: #fff;
        right: 5px;
        top: 4px;
        border-radius: 3px;
        line-height: 20px;
        padding: 0 5px;
        cursor: pointer;
    }
</style>
<div id="trainResourceModulesAddCourseDiv">
	<div class="layui-form-box" id="trainResourceModulesAddCourseHtmlDiv">
		<div class="layui-tab archivesTab layui-tab-oa-nav" >
            <ul class="layui-tab-title">
            	<li class="layui-this" title="基本信息" >基本信息</li>
            	<li title="附件" >附件</li>
            </ul>
            <div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<form class="layui-form" id="trainResourceModulesAddCourseForm">
			            <div class="shell-layer-content-box" style="padding-right: 30px;">
							<input type="hidden" name="trainCourseId" id="trainCourseId" value="">
			                <div class="layui-col-xs7">
			                    <label class="shell-layui-form-label"><span class="required">*</span>课程名称</label>
			                    <div class="shell-layer-input-box">
			                        <input type="text" autocomplete="off" name="courseTitle" lay-verify="required" class="layui-input" />
			                    </div>
			                </div>
			                <div class="layui-col-xs7">
			                    <label class="shell-layui-form-label"><span class="required">*</span>作者</label>
			                    <div class="shell-layer-input-box">
			                        <input type="text" autocomplete="off" name="author" lay-verify="required" class="layui-input" />
			                    </div>
			                </div>
			                <div class="layui-col-xs12">
			                    <label class="shell-layui-form-label"><span class="required">*</span>课程说明</label>
			                    <div class="shell-layer-input-box">
			                        <textarea class="shell-layer-area" id="remark" name="remark" style="width: 480px; height: 120px; border-color: rgba(0, 0, 0, .1);" lay-verify="required"></textarea>
			                    </div>
			                </div>
			                <!--<div class="layui-col-xs12">
			                    <label class="shell-layui-form-label">上传附件</label>
			                    <div class="shell-layer-input-box">
			                        <span id="evaBaseFile">选择上传文件</span>
			                        <input type="text" name="doc_path" autocomplete="off" class="layui-input" id="docFilePath" readonly="readonly" />
			                    </div>
			                </div>-->
			            </div>
			            <button type="button" class="layer_btn none" lay-submit="" lay-filter="trainResourceModulesAddCourseSaveSub" 
			            	id="trainResourceModulesAddCourseSaveSub">提交</button>
			        </form>
				</div>
				<div class="layui-tab-item">
					<!--附件-开始-->
                	<form id="trainResourceModulesAddCourseFile">
                        <input id="trainResourceModulesAddCourseFileupload" type="file" name="file" multiple>
                        <div class="table-box tra-table-box">
                            <table id="grid-table-files"></table>
                        </div>
                    </form>
                	<!--附件-结束-->
				</div>
		    </div>
		</div>
		
		<div class="layer-btn lay-handover-btnbox">
        	<button type="button" class="layer_btn"  lay-submit="" lay-filter="trainResourceModulesAddCourseSub">确定</button>
        	<a href="javascript:;" class="layer_btn layer_back" id="trainResourceModulesAddCourseClose">关闭</a>
        </div>
        
	</div>
    
</div>