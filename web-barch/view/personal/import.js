"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layer = layui.layer,
                layedit = layui.layedit;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['600px', '350px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {

                    //下载导入模板
                    $('#downloadImport').attr('href', opt.data.downloadUrl);
                    
                    //导入
                    $('#importFileUploadBtn').click(function () {
                        $('#importMessage').html('');
                    })
                    
                    var url = '';
                    if(opt.data.version == '2'){
                    	url = common.url + '/ts-oa/salary/salarySet/importSalaryTemplate?tableName='+opt.data.tableName + '&dateStr='+opt.data.dateStr;
                    }else{
                    	url = common.url + '/ts-oa/salary/salarySet/importTemplate?tableName='+opt.data.tableName + '&dateStr='+opt.data.dateStr;
                    }
                    
                    upload.render({
                        elem: '#importFileUploadBtn',
                        url: url
                        , accept: 'file'
                        , size: 500000  //500Mb
                        , multiple: true
                        , auto: true
                        , exts: 'xlsx|xls'
                        , done: function (res) {
                            $.closeloadings();
                            var html = '';
                            var data = res.object;
                            if (res.success) {//成功
                            	html += '<span>提示信息: </span><span>&nbsp;&nbsp;' + res.object + '</span>'
                            }else{
                            	html += '<span>提示信息: </span><span>&nbsp;&nbsp;' + res.message + '</span>'
                            }
                            $('#importMessage').append(html);
                        },
                        error: function () {
                            layer.msg("数据表不符合要求,请下载模板填写！")
                        }
                    });
                    form.render();
                },
                cancel: function () {
                    // 右上角关闭事件的逻辑
                    opt.ref();
                    layer.closeAll();
                }
            });
        })
    };
});