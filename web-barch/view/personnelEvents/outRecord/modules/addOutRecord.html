<div id="addOutRecordAdd">
    <div class="layui-tab-content">
	<form id="outRecordAddForm" class="layui-form">
		<input type="hidden" name="id" id="id" />
		<input type="hidden" name="applyUser" id="applyUser" />
		<input type="hidden" name="employeeId" id="employeeId" />
		<input type="hidden" name="applyUserName" id="applyUserName" />
		
		<div class="layui-content-box">
		
			<div class="layui-col-xs6">
				<label class="shell-layui-form-label">
					<span class="required">*</span>申请人
				</label>
				<div class="shell-layer-input-box">
					<div id="employeeAddSelBox"></div>
				   <!--  <select id="employeeAddSel"  lay-verify="required" lay-search lay-filter="incidentUserSelFilter"></select> -->
				</div>
			</div>
		
			<div class="layui-col-xs6">
				<label class="shell-layui-form-label">
					<span class="required">*</span>外出类型
				</label>
				<div class="shell-layer-input-box">
					<select id="outType" name="outType" lay-verify="required" lay-filter="outType" lay-search>
						 <option value="">请选择</option>
						 <option value="进修">进修</option>
						 <option value="规培">规培</option>
						 <option value="外出学习">外出学习</option>
						 <option value="外出会议">外出会议</option>
					</select>
				</div>
			</div>
			
			<div class="layui-col-xs6" id="provinceTypeDiv">
				<label class="shell-layui-form-label">
					<span class="required">*</span>省内或省外
				</label>
				<div class="shell-layer-input-box">
					<select id="provinceType" name="provinceType" lay-verify="required" lay-filter="provinceType" lay-search>
						 <option value="">请选择</option>
						 <option value="省内">省内</option>
						 <option value="省外">省外</option>
					</select>
				</div>
			</div>
			
			<div class="layui-col-xs6">
				<label class="shell-layui-form-label">
					<span class="required">*</span>岗位类别
				</label>
				<div class="shell-layer-input-box">
					<select id="jobs" name="jobs" lay-verify="required" lay-search lay-filter="jobs"></select>
				<!-- 	<select id="jobs" name="jobs" lay-verify="required" lay-filter="jobs">
						 <option value="">请选择</option>
						 <option value="医生">医生</option>
						 <option value="医技">医技</option>
						 <option value="护士">护士</option>
						 <option value="职能科室科员">职能科室科员</option>
						 <option value="药剂">药剂</option>
						 <option value="药剂类主任">药剂类主任</option>
						 <option value="职能科室主任">职能科室主任</option>
						 <option value="医技类主任">医技类主任</option>
						 <option value="护士长">护士长</option>
						 <option value="临床类主任">临床类主任</option>
					</select> -->
				</div>
			</div>
			
			<div class="layui-col-xs6" id="moralityitemDiv">
				<label class="shell-layui-form-label">
					<span class="required">*</span>外出时间
				</label>
				<div class="shell-layer-input-box">
						<input type="text"  placeholder="时间选择" autocomplete="off" class="layui-input edi-layui-input searchInput datetime" id="outRecordTimeForm" readonly="readonly" lay-verify="required" />
						<input type="hidden" name="startTime" class="layui-input" autocomplete="off" id="outStartTimeForm" />
						<input type="hidden" name="endTime" class="layui-input" autocomplete="off" id="outEndTimeForm" />
				</div>
			</div>
			
			<div class="layui-col-xs6">
				<label class="shell-layui-form-label">
					<span class="required">*</span>外出天数
				</label>
				<div class="shell-layer-input-box">
					 <input type="text"  name="outDays"  autocomplete="off" lay-verify="required"  class="layui-input"
					 id="outDays" onkeyup="this.value=this.value.toString().match(/^\d+(?:\.\d{0,1})?/)" />
				</div>
			</div>
			
			<div class="layui-col-xs6" >
				<label class="shell-layui-form-label">
					实际外出开始
				</label>
				<div class="shell-layer-input-box">
						<input type="text" placeholder="实际外出开始时间" type="" name="realStartTime" class="layui-input" autocomplete="off" id="realStartTime" />
				</div>
			</div>
			
			<div class="layui-col-xs6" >
				<label class="shell-layui-form-label">
					实际外出结束
				</label>
				<div class="shell-layer-input-box">
						<input type="text" placeholder="实际外出结束时间" name="realEndTime" class="layui-input" autocomplete="off" id="realEndTime" />
				</div>
			</div>
	

			
			<div class="layui-col-xs12">
				<label class="shell-layui-form-label">
					<span class="required">*</span>外出地点
				</label>
				<div class="shell-layer-input-box">
				   <textarea type="text" name="outAddress" style="height:80px" class="layui-input" lay-verify="required" 
				   placeholder="请输入外出地点" id="remark"></textarea>
				</div>
			</div>
			
			<div class="layui-col-xs12">
				<label class="shell-layui-form-label">
					<span class="required">*</span>外出事由
				</label>
				<div class="shell-layer-input-box">
				   <textarea type="text" name="outRemark" style="height:80px" class="layui-input" lay-verify="required" 
				   placeholder="请输入外出事由" id="outRemark"></textarea>
				</div>
			</div>

			<div class="layui-col-xs12" id="outRecordFileDiv">
				<label class="shell-layui-form-label">附件</label>
				<div class="shell-layer-input-box">
					  <div class="layui-upload-drag" id="outRecordFileUploadBtn">
						  <i class="layui-icon"></i>
						  <p>点击上传或将文件拖拽到此处</p>
					  </div>
					  <table class="layui-table" id="outRecordFileDataList" style="margin-top: 0; display: none; width: 500px">
						  <thead>
							  <tr>
								  <th>文件名</th>
								  <th>大小</th>
								  <th style="width: 56px">状态</th>
								  <th>操作</th>
							  </tr>
						  </thead>
						  <tbody id="outRecordDataList"></tbody>
					  </table>
				</div>
			</div>
			
			<div class="layui-col-xs12" id="infoAttachmentDiv" style="display: none;">
				 <label class="shell-layui-form-label">相关附件</label>
				 <div class="shell-layer-input-box">
					 <div class="infoAttachment" id="infoAttachment">
						 <ul id="fileList"></ul>
					 </div>
				 </div>
		   </div>
		</div>

		<div class="layer-btn archivesTabBtn">
			<button type="button" class="layui-btn layui-btn-normal" lay-submit="" id="outRecordSubmit" lay-filter="outRecordSubmitCofirm">确定</button>
			<button type="button" class="layui-btn layui-btn-primary" id="closeoutRecord">取消</button>
		</div>
	</form>
	</div>
</div>