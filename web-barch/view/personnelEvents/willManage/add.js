"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
		layui.use([ 'form', 'layedit', 'laydate', 'trasen', 'upload' ], function() {
	
		    var form = layui.form, 
		    laydate = layui.laydate, 
		    trasen = layui.trasen, 
		    upload = layui.upload;
		    
		    layer.open({
				type : 1,
				title : opt.title,
				closeBtn : 1,
				shadeClose : false,
				area : [ '800px', '450px' ],
				skin : 'yourclass',
				content : html,
				success : function(layero, index) {
					
					if (opt.data) {
				    	trasen.setNamesVal($('#willManageAddForm'), opt.data);
				    }
					
					if(opt.optType && 'details' == opt.optType){
						$("#willManageAddForm input").prop("disabled", true);
						$("#willManageAddForm select").prop("disabled", true);
						$("#willManageAddForm textarea").prop("disabled", true);
						$("#evaBaseSubmitCofirm").hide();
					}
					
					//时间控件
					laydate.render({
			            elem: '#executeTime',
			            type: 'date',
			            format: 'yyyy-MM-dd',
			            showBottom: true,
			            trigger: 'click',
			            //value: opt.data.finishTime || new Date(),
			           // min: new Date().toLocaleString('chinese', {hour12: false})
		           });
			
				    form.render();
				}
		    });
           
		   // 保存
		   form.on('submit(evaBaseSubmitCofirm)', function(data) {
				var d = data.field;
				var url;
				if (d.id) {
				    url = '/ts-hrms/api/willManage/update';
				} else {
				    url = '/ts-hrms/api/willManage/save';
				}
				if (!d.id) {
				    delete d.id;
				}
				$.loadings();
				$.ajax({
				    type : "post",
				    url : common.url + url,
				    data : JSON.stringify(d),
				    contentType: 'application/json;charset=UTF-8',
				    success : function(res) {
						$.closeloadings();
						if (res.success) {
						    layer.closeAll();
						    layer.msg(res.message);
						    opt.ref();
						}else{
							layer.msg(res.message);
						}
				    },
				    error : function(res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
				    }
				});
		    });
		})
    };
});