<style></style>
<div id="employeeJobtitleAddHtml">
    <div class="layui-form-box" id="employeeJobtitleAddHtmlDiv">
        <div class="layui-tab archivesTab layui-tab-oa-nav">
            <ul class="layui-tab-title">
                <li class="layui-this" title="基本信息">基本信息</li>
                <li title="附件">附件</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" id="JobtitleManageAddForm">
                        <div class="shell-layer-content-box" style="padding-right: 30px">
                            <!-- 人员姓名 -->
                            <input type="hidden" id="employeeName" name="employeeName" value="" />
                            <input type="hidden" id="jobtitleAppointId" name="jobtitleAppointId" value="" />

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    姓名
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="employeeAddSel" name="employeeId" lay-verify="required" readonly lay-search lay-filter="incidentUserSelFilter"></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">工号</label>
                                <div class="shell-layer-input-box">
                                    <input type="text" autocomplete="off" id="employeeNo" name="employeeNo" readonly lay-verify="required" class="layui-input layui-disabled" />
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    职称类别
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="jobtitleCategory" name="jobtitleCategory" lay-verify="required" lay-search lay-filter="jobtitleCategoryFilter"></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    职称级别
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="jobtitleLevel" name="jobtitleLevel" lay-verify="required" lay-search lay-filter="jobtitleLevelFilter"></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    职称名称
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="jobtitleName" name="jobtitleName" lay-verify="required" lay-search></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    获取途径
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="acceptMethod" name="acceptMethod" lay-verify="required" lay-search></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">评定时间</label>
                                <div class="shell-layer-input-box">
                                    <input type="text" autocomplete="off" id="assessTime" name="assessTime" class="layui-input" />
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">评定机构</label>
                                <div class="shell-layer-input-box">
                                    <input type="text" autocomplete="off" id="mechanismName" name="mechanismName" class="layui-input" />
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">证书编号</label>
                                <div class="shell-layer-input-box">
                                    <input type="text" autocomplete="off" id="certificateNumber" name="certificateNumber" class="layui-input" />
                                </div>
                            </div>

                            <div class="layui-col-xs6">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    是否最高职称
                                </label>
                                <div class="shell-layer-input-box">
                                    <select id="highestLevel" name="highestLevel" lay-verify="required" lay-search>
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                            </div>

                            <!--<div class="layui-col-xs12">
			                    <div class="layer-bom">
			                        <label class="layui-form-label">上传附件</label>
			                        <div class="layui-input-block contractArea contractupload">
			                            <div class="layui-upload">
			                                <button type="button" class="layui-btn layui-btn-xs" id="uploadBtn">
			                                    <i class="layui-icon">&#xe67c;</i>上传
			                                </button>
			                                <div class="layui-upload-list" id="uploadInfoEdi">
			
			                                </div>
			                                <input type="file" name="fileUpload" id="fileUpload" style="display:none;" />
			                            </div>
			                        </div>
			                    </div>
			                </div>-->

                            <div class="layui-col-xs12">
                                <label class="shell-layui-form-label">备注</label>
                                <div class="shell-layer-input-box">
                                    <textarea name="remark" maxlength="200" class="layui-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="layui-btn none" lay-submit="" lay-filter="employeeJobtitleAddSaveSub" id="employeeJobtitleAddSaveSub">提交</button>
                    </form>
                </div>
                <div class="layui-tab-item">
                    <!--附件-开始-->
                    <form id="employeeJobtitleAddFile">
                        <input id="employeeJobtitleAddFileupload" type="file" name="file" multiple />
                        <div class="table-box jqTable">
                            <table id="grid-table-files"></table>
                        </div>
                    </form>
                    <!--附件-结束-->
                </div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layui-btn" lay-submit="" lay-filter="employeeJobtitleAddSub">确定</button>
            <a href="javascript:;" class="layui-btn layui-btn-primary" id="employeeJobtitleClose">关闭</a>
        </div>
    </div>
</div>
