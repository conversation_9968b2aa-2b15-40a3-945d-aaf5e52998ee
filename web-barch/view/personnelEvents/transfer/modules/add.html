<style></style>
<div id="employeeTranserAddHtml">
    <div class="layui-form-box" id="trainResourceModulesAddCourseHtmlDiv">
        <div class="layui-tab archivesTab layui-tab-oa-nav">
            <ul class="layui-tab-title">
                <li class="layui-this" title="基本信息">基本信息</li>
                <li title="附件">附件</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" id="laborContractManageAddForm">
                        <div class="shell-layer-content-box" style="padding-right: 30px; overflow: visible">
                            <!-- 调动人员姓名 -->
                            <input type="hidden" id="employeeName" name="employeeName" value="" />
                            <!-- 性别 -->
                            <input type="hidden" id="genderPersonnelChangeAdd" name="gender" value="" />
                            <!-- 组织机构ID -->
                            <input type="hidden" id="oldOrgId" name="oldOrgId" value="" />
                            <input type="hidden" id="newOrgId" name="newOrgId" value="" />
                            <!-- 职位ID -->
                            <input type="hidden" id="oldPositionId" name="oldPositionId" value="" /> 
                            <!-- 调动类型 -->
                            <input type="hidden" id="personnelChangeId" name="personnelChangeId" value="" />

                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    姓名
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="data-value" id="employeeAddSel" name="employeeId" lay-verify="required" lay-search lay-filter="transferIdSelFilter"></select>
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    工号
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input layui-disabled data-value" type="text" autocomplete="off" id="employeeNo" name="employeeNo" readonly lay-verify="required" />
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    性别
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input layui-disabled data-value" type="text" autocomplete="off" id="genderTextPersonnelChangeAdd" readonly lay-verify="required" />
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    调动类型
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="data-value" id="changeTypeSel" name="changeType" lay-verify="required" lay-search></select>
                                </div>
                            </div>
                            <div id="YZW" class="layui-col-xs6 shell-layui-form-item"> 
                                <label class="shell-layui-form-label">
                                    原职务名称
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input layui-disabled data-value" type="text" autocomplete="off" id="oldPositionName" name="oldPositionName" readonly />
                                </div>
                            </div>
                            <div id="XZW" class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label" id="newPositionLabel">
                                    <span class="required" id="xzwReqImg">*</span>
                                    新职务名称
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="data-value" id="newPositionName" name="newPositionName" lay-verify="required" lay-filter="newPositionIdFilter" lay-search></select>
                                    <input id="newPositionId" name="newPositionId" type="hidden" class="layui-input" />
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    原部门名称
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input layui-disabled data-value" type="text" autocomplete="off" id="oldOrgName" name="oldOrgName" readonly />
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    新部门名称
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input data-value" type="text" autocomplete="off" lay-verify="required" name="newOrgName" placeholder="请选择新部门" id="trasferNewOrgDep" zTreeLick="click" />
                                    <!-- 			                        <i class="layui-edge"></i> -->
                                </div>
                            </div>
                            <!-- 岗位信息 -->
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label" id="oldPersonalIdentityLabel">
                                    原岗位
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="layui-disabled data-value" id="oldPersonalIdentity" disabled name="oldPersonalIdentity" lay-search></select>
                                </div>
                            </div>
                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label">
                                    <span class="required">*</span>
                                    现岗位
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="data-value" id="newPersonalIdentity" name="newPersonalIdentity" lay-verify="required" lay-search></select>
                                </div>
                            </div>

                            <div class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label" id="sxrqLabel">
                                    <span class="required">*</span>
                                    调动生效日期
                                </label>
                                <div class="shell-layer-input-box">
                                    <input class="layui-input data-value" type="text" autocomplete="off" id="changeStartDate" name="changeStartDate" lay-verify="required" />
                                </div>
                            </div>
							
                            <div id="ZCGB" class="layui-col-xs6 shell-layui-form-item">
                                <label class="shell-layui-form-label" id="zcLabel">
                                    <span class="required" id="zcgbReqImg">*</span>
                                    中层干部
                                </label>
                                <div class="shell-layer-input-box">
                                    <select class="data-value" id="zhongceng" name="shifouzhongcengganbu"  lay-verify="required"  lay-search="">
                                        <option value="">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                            </div>
							
							<div class="layui-col-xs6 shell-layui-form-item" id="RYLB">
							    <label class="shell-layui-form-label">
							        <span class="required">*</span>
							        人员类别
							    </label>
							    <div class="shell-layer-input-box">
							        <select class="data-value" id="orgAttributes" name="orgAttributes" lay-verify="required" lay-search></select>
							    </div>
							</div>
							
							<div class="layui-col-xs6 shell-layui-form-item" id="GWLX">
							    <label class="shell-layui-form-label">
							        <span class="required">*</span>
							         岗位类型
							    </label>
							    <div class="shell-layer-input-box">
							        <select class="data-value" id="postType" name="postType" lay-verify="required" lay-search></select>
							    </div>
							</div>
							
							<div id="GWSX" class="layui-col-xs6 shell-layui-form-item" >
							    <label class="shell-layui-form-label" id="zcLabel">
							        <span class="required">*</span>
							        岗位属性
							    </label>
							    <div class="shell-layer-input-box">
							        <select class="data-value"  name="jobAttributes"  lay-verify="required"  lay-search="">
							            <option value="">请选择</option>
							            <option value="专业技术人员（医师）">专业技术人员（医师）</option>
							            <option value="专业技术人员（技师）">专业技术人员（技师）</option>
										<option value="专业技术人员（护理）">专业技术人员（护理）</option>
										<option value="专业技术人员（药师）">专业技术人员（药师）</option>
										<option value="工勤人员">工勤人员</option>
										<option value="技术人员">技术人员</option>
										<option value="行政管理人员">行政管理人员</option>
										<option value="辅助卫技人员">辅助卫技人员</option>
							        </select>
							    </div>
							</div>
														
                            <div class="layui-col-xs12 shell-layui-form-item">
                                <label class="shell-layui-form-label">备注</label>
                                <div class="shell-layer-input-box">
                                    <textarea class="layui-textarea data-value" name="remark" maxlength="200"></textarea>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="layui-btn none" lay-submit="" lay-filter="employeeTranserSaveSub" id="employeeTranserSaveSub">提交</button>
                    </form>
                </div>
                <div class="layui-tab-item">
                    <!--附件-开始-->
                    <form id="employeeTranserFile">
                        <input id="employeeTranserFileupload" type="file" name="file" multiple />
                        <div class="table-box jqTable">
                            <table id="grid-table-files"></table>
                        </div>
                    </form>
                    <!--附件-结束-->
                </div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layui-btn" lay-submit="" lay-filter="employeeTranserAddSub">确定</button>
            <a href="javascript:;" class="layui-btn layui-btn-primary" id="employeeTranserClose">关闭</a>
        </div>
    </div>
</div>
