"use strict";
define(function(require, exports, module) {
	exports.init = function(opt, html) {
		layui.use(['form','trasen'], function() {
			var form = layui.form, 
				trasen = layui.trasen;
			
			layer.open({
                type : 1,
                title : opt.title,
                closeBtn : 1,
                shadeClose : false,
                area : [ '740px', '500px' ],
                skin : 'yourclass',
                content : html,
                success : function(layero, index) {
                    if(opt.data){
                    	$("#interviewManageAddHtml #employeeName").attr('readonly', true);
                        trasen.setNamesVal(layero, opt.data);
                    }
                }
            });
			
			//保存
			form.on('submit(hireManageAddSub)',function (data) {
                var _url = common.url + "/ts-hrms/hireManagement/save";
                if(opt.data){
                    _url = common.url + "/ts-hrms/hireManagement/update"
                }
                var _data = JSON.stringify(data.field);
                $.ajax({
                    type:"post",
                    contentType: "application/json; charset=utf-8",
                    url:_url,
                    data:_data,
                    success:function(res){
                        if(res.success){
                        	opt.ref && opt.ref();
                            layer.closeAll();
                            layer.msg('保存成功！');
                        }else{
                            layer.msg(res.message || '操作失败');
                        }
                    }
                });
                return false;
            });
			
			//取消
			$('#hireManageAddHtml #hireManageClose').off('click').on('click',function () {
                layer.closeAll();
            });
			
		});
		
	}
});