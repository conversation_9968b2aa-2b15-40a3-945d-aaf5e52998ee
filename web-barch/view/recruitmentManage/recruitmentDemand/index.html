<div id="recruitmentDemandDiv" class="content-box">
    <div class="oa-nav-search">
        <form id="recruitmentDemandQueryForm" class="layui-form">
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">姓名</span>
                <div class="shell-layer-input-box">
                    <input type="text" name="applicantName" class="layui-input" search-input="recruitmentDemand-search" placeholder="请输入姓名" />
                </div>
            </div>
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">招聘岗位</span>
                <div class="shell-layer-input-box">
                    <input type="text" name="recruitmentPost" class="layui-input" search-input="recruitmentDemand-search" placeholder="招聘岗位" />
                </div>
            </div>
            <div class="shell-search-box">
                <span class="shell-layer-input-boxTit">优先级</span>
                <div class="shell-layer-input-box">
                    <select id="prioritySearchSel" name="priority" lay-filter="selectChangeFilter" lay-search></select>
                </div>
            </div>
            <div class="shell-search-box">
                <button type="button" class="layui-btn" lay-submit lay-filter="recruitmentDemandSearch" search-btn="recruitmentDemand-search">搜索</button>
            </div>
            <div class="shell-search-box">
                <button type="button" class="layui-btn oa-btn-reset" lay-submit lay-filter="recruitmentDemandReset"><i class="layui-icon layui-icon-refresh"></i></button>
            </div>
        </form>

        <div class="fr">
            <button type="button" class="layui-btn" id="recruitmentDemandTableAdd" data-permission="on">新增</button>
            <button type="button" class="layui-btn" id="recruitmentDemandTableEditor" data-permission="on">编辑</button>
            <button type="button" class="layui-btn" id="recruitmentDemandTableDel" data-permission="on">删除</button>
        </div>
    </div>

    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table-recruitmentDemandTable"></table>
            <!-- 分页 -->
            <div id="grid-pager-recruitmentDemandPage"></div>
        </div>
    </div>
</div>
