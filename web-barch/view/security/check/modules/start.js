'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'laydate', 'trasen','zTreeSearch'], function () {
            var utils = require('util');
            var form = layui.form;
            var laydate = layui.laydate;
            var trasen = layui.trasen;
            var zTreeSearch = layui.zTreeSearch;
            var formData = {};
            var routeList = [];
            if (opt.routData) {
                formData.addressList = opt.routData.checkRouteDetails;
                for (var i = 0; i < formData.addressList.length; i++) {
                    formData.addressList[i].startCheckDetails = formData.addressList[i].checkRouteContents;
                    formData.addressList[i].addressId = formData.addressList[i].id;
                    delete formData.addressList[i].checkRouteContents;
                    delete formData.addressList[i].id;
                    delete formData.addressList[i].isDeleted;
                    delete formData.addressList[i].createDate;
                    delete formData.addressList[i].createUser;
                    delete formData.addressList[i].createUserName;
                    delete formData.addressList[i].updateDate;
                    delete formData.addressList[i].updateUser;
                    delete formData.addressList[i].updateUserName;
                    for (var j = 0; j < formData.addressList[i].startCheckDetails.length; j++) {
                        formData.addressList[i].startCheckDetails[j].checkResult = 0;
                        formData.addressList[i].startCheckDetails[j].businessId = utils.guid();
                        formData.addressList[i].startCheckDetails[j].checkContentId = formData.addressList[i].startCheckDetails[j].id;
                        delete formData.addressList[i].startCheckDetails[j].id;
                        delete formData.addressList[i].startCheckDetails[j].isDeleted;
                        delete formData.addressList[i].startCheckDetails[j].createDate;
                        delete formData.addressList[i].startCheckDetails[j].createUser;
                        delete formData.addressList[i].startCheckDetails[j].createUserName;
                        delete formData.addressList[i].startCheckDetails[j].updateDate;
                        delete formData.addressList[i].startCheckDetails[j].updateUser;
                        delete formData.addressList[i].startCheckDetails[j].updateUserName;
                    }
                }
                formData.routeId = opt.routData.id;
                formData.routeName = opt.routData.routeName;
                routeList = formData.addressList;
            }
            if (opt.data) {
                formData = opt.data;
                routeList = formData.addressList;
            }
            var routeName = opt.routData ? opt.routData.routeName : opt.data.routeName;
            var wins = layer.open({
                type: 1,
                title: routeName,
                closeBtn: false,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#securityCheckStart'), opt.data);
                        $('#securityCheckStart #inspectedDepartmentCodeSearch').val(opt.data.inspectedDepartmentName);
                    } else {
                        trasen.setNamesVal($('#securityCheckStart'), {
                            checkUserName: common.userInfo.deptname + '-' + common.userInfo.username + '（' + common.userInfo.mobileNo + '）',
                            checkUser: common.userInfo.usercode,
                            checkDate: new Date().format('yyyy-MM-dd'),
                        });
                    }
                    laydate.render({
                        elem: '#securityCheckStart [name="checkDate"]',
                        trigger: 'click',
                    });
                },
            });

            new $.selectPlug('#examineUserBox', {
                url: common.url + '/ts-oa/employee/list?empStatus=1',
                searchType: 'json',
                textName: 'empName', 
                valName: 'empCode', 
                inpValName: 'examineUser', 
                inpTextName: 'examineUserName', 
                layout: 'concat',
                labelConcatList: 'empDeptName',
                defaultText: opt.data ? opt.data.examineUserName : '',
                defaultVal: opt.data ? opt.data.examineUser : '',
                choice: true,
                callback: function (res) {},
            });

            new $.selectPlug('#otherUserBox', {
                url: common.url + '/ts-oa/employee/list?empStatus=1',
                searchType: 'json',
                textName: 'empName', 
                valName: 'empCode', 
                inpValName: 'otherUser', 
                inpTextName: 'otherUserName',
                layout: 'concat',
                labelConcatList: 'empDeptName',
                defaultText: opt.data ? opt.data.otherUserName : '',
                defaultVal: opt.data ? opt.data.otherUser : '',
                choice: true,
                callback: function (res) {
                },
            });
            tableTemp();
            function tableTemp() {
                $('#securityCheckStart #routeBody').html('');
                for (var i = 0; i < routeList.length; i++) {
                    var con = routeList[i].startCheckDetails;
                    for (var j = 0; j < con.length; j++) {
                        var tr = $('<tr data-index="' + i + '_' + j + '"></tr>');
                        if (j == 0) {
                            tr.append($('<td rowspan="' + con.length + '"><p style="font-weight:700;padding-bottom:16px">' + routeList[i].checkAddress + '</p><span ><i class="fa fa-map-marker" aria-hidden="true" style="color:#5260FF;margin-right:8px"></i>' + routeList[i].checkCoordinate + '</span></td>'));
                        }
                        tr.append($('<td>' + con[j].checkContent + '</td>'));
                        tr.append(
                            $(
                                '<td style="text-align:left;padding:4px"><form class="layui-form"><div>\
                                    <input type="radio" name="checkResult" value="0" title="达标"  >\
                                    <input type="radio" name="checkResult" value="1" title="不达标">\
                                    <a href="javascript:;" class="files">附件</a><input class="none" name="businessId"></div>\
                                    <div class="layui-form-item data-info">\
                                        <div class="layui-form-item">\
                                            <label class="layui-form-label" >说明</label>\
                                            <div class="layui-input-block">\
                                                <input type="text" name="remark" autocomplete="off" maxlength="50" class="layui-input" placeholder="请简要说明，不超过50个字"/>\
                                            </div>\
                                        </div>\
                                    </div>\
                                    <div class="layui-form-item data-info other-info none">\
                                        <div class="layui-form-item">\
                                            <label class="layui-form-label">交办人</label>\
                                            <input type="hidden" name="assignUserDeptCode"  />\
                                            <input type="hidden" name="assignUserDeptName"  />\
                                            <div class="layui-input-inline">\
                                                <div id="personSel_' +
                                    i +
                                    '_' +
                                    j +
                                    '"></div>\
                                            </div>\
                                        </div>\
                                </div></div>\
                             </form></td>'
                            )
                        );
                        if (con[j].checkResult == 1) {
                            tr.find('.other-info').removeClass('none');
                        }
                        trasen.setNamesVal(tr, con[j]);
                        $('#securityCheckStart #routeBody').append(tr);
                        (function (i, j) {
                            new $.selectPlug('#personSel_' + i + '_' + j, {
                                url: common.url + '/ts-oa/employee/list?empStatus=1',
                                selectKey: 'personSel_' + i + '_' + j,
                                searchType: 'json',
                                textName: 'empName', // 选项文字的key
                                valName: 'empCode', // 选项id的key
                                inpValName: 'assignUser', 
                                inpTextName: 'assignUserName', 
                                layout: 'concat',
                                labelConcatList: 'empDeptName',
                                defaultText: con[j].assignUserName || '',
                                defaultVal: con[j].assignUser || '',
                                callback: function (res) {
                                    if (res) {
                                        $('#personSel_' + i + '_' + j)
                                            .closest('.layui-form-item')
                                            .find('[name="assignUserDeptCode" ]')
                                            .val(res.empDeptCode);
                                        $('#personSel_' + i + '_' + j)
                                            .closest('.layui-form-item')
                                            .find('[name="assignUserDeptName" ]')
                                            .val(res.empDeptName);
                                    }
                                },
                            });
                        })(i, j);
                    }
                }
                form.render('radio');
            }
            $('#securityCheckStart ')
                .off('click', '.layui-form-radio')
                .on('click', '.layui-form-radio', function () {
                    var v = $(this).siblings('[name="checkResult"]:checked').val();
                    if (v == 1) {
                        $(this).closest('td').find('.other-info').removeClass('none');
                    } else {
                        $(this).closest('td').find('.other-info').addClass('none');
                    }
                });
            //附件
            $('#securityCheckStart ')
                .off('click', '.files')
                .on('click', '.files', function () {
                    var businessId = $(this).next().val();
                    $.quoteFun('/security/check/modules/files', {
                        businessId: businessId,
                        callback: function (arr) {},
                    });
                });

            function dealData() {
                var check = true;
                var d = trasen.getNamesVal($('#securityCheckStart').find('.layui-form-item').eq(0));
                if (!d.inspectedDepartmentId) {
                    layer.msg('被检查科室不能为空');
                    return false;
                }
                formData = $.extend(formData, d);
                $.each($('#securityCheckStart #routeBody tr'), function () {
                    var item = trasen.getNamesVal($(this));
                    var c = $(this).attr('data-index').split('_');
                    routeList[c[0]].startCheckDetails[c[1]] = $.extend(routeList[c[0]].startCheckDetails[c[1]], item);
                    if (item.checkResult == 1 && !item.assignUserName) {
                        layer.msg('交办人不能为空');
                        check = false;
                        return false;
                    }
                    if (item.checkResult == 1 && !item.remark) {
                        layer.msg('理由不能为空');
                        check = false;
                        return false;
                    }
                });
                return check;
            }

            treeSelect();
          function treeSelect() {
                zTreeSearch.init('#inspectedDepartmentCodeSearch', {
                    url: common.url + '/ts-basics-bottom/organization/getTree',
                    type: 'post',
                    checkbox: true,
                    condition: 'name',
                    choice: true,
                    choiceType: {
                        Y: '',
                        N: '',
                    },
                    allCheckVal: 'Y',
                    ztreeInitFun: function (treeObj) {
                      let ids = $("input[name='inspectedDepartmentId']").val();
                      if (!ids) return;
                      ids
                        .split(',')
                        .filter((id) => id)
                        .map((id) => {
                          let node = treeObj.getNodesByParam('id', id)[0];
                          if (!node) return;
                          treeObj.checkNode(node, true, true);
                          node.children && treeObj.expandNode(node, true);
                          let parentNode = node.getParentNode();
                          while (parentNode) {
                            treeObj.expandNode(parentNode, true);
                            parentNode = parentNode.getParentNode();
                          }
                        });
                    },
                    zTreeChoice: function(nodes, text){
                        let ids = nodes.map(item => item.id).join(',');
                        $("input[name='inspectedDepartmentId']").val(ids); 
                        $("input[name='inspectedDepartmentName']").val(nodes.map(item => item.name).join(',')); 
                        console.log($("input[name='inspectedDepartmentId']").val());
                        console.log($("input[name='inspectedDepartmentName']").val());
                    },
                    zTreeOnClick: function(treeId, treeNode) {
                        $("input[name='inspectedDepartmentId']").val(treeNode.id); 
                        $("input[name='inspectedDepartmentName']").val(treeNode.name); 
                    }
                });
            }

            $('#securityCheckStart #save')
                .off('click')
                .on('click', function () {
                    if (!dealData()) {
                        return false;
                    }
                    var url = '/ts-oa/api/startCheck/save';
                    if (formData.id) {
                        url = '/ts-oa/api/startCheck/update';
                    }
                    formData.addressList = routeList;
                    formData.status = 0;
                    $.ajax({
                        url: url,
                        contentType: 'application/json;utf-8',
                        method: 'post',
                        data: JSON.stringify(formData),
                        success: function (res) {
                            if (res.success) {
                                opt.ref();
                                layer.msg(res.message || '暂存成功');
                                res.object && (formData.id = res.object);
                            } else {
                                layer.msg(res.message || '暂存失败');
                            }
                        },
                    });
                    return false;
                });
            $('#securityCheckStart #sub')
                .off('click')
                .on('click', function () {
                    if (!dealData()) {
                        return false;
                    }
                    var url = '/ts-oa/api/startCheck/save';
                    if (opt.data || formData.id) {
                        url = '/ts-oa/api/startCheck/update';
                    }
                    formData.addressList = routeList;
                    if (!formData.status || formData.status == 0 || formData.status == 2) {
                        formData.status = 1;
                    }
                    $.ajax({
                        url: url,
                        contentType: 'application/json;utf-8',
                        method: 'post',
                        data: JSON.stringify(formData),
                        success: function (res) {
                            if (res.success) {
                                opt.ref();
                                layer.msg(res.message || '提交成功');
                                layer.close(wins);
                            } else {
                                layer.msg(res.message || '提交失败');
                            }
                        },
                    });
                    return false;
                });
            $('#securityCheckStart #close')
                .off('click')
                .on('click', function () {
                    layer.close(wins);
                });
        });
    };
});
