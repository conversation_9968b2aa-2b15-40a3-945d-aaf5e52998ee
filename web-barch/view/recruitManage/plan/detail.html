<style>
    #recruitManagePlanDetailDialog {
        height: 100%;
    }

    #recruitManagePlanDetailDialog ::-webkit-scrollbar {
        width: 7px;
        height: 5px;
        border-radius: 15px;
        -webkit-border-radius: 15px;
    }

    #recruitManagePlanDetailDialog ::-webkit-scrollbar-track-piece {
        background-color: #ffff;
        border-radius: 15px;
        -webkit-border-radius: 15px;
    }

    #recruitManagePlanDetailDialog ::-webkit-scrollbar-thumb:vertical {
        height: 5px;
        background-color: rgba(144, 147, 153, 0.5);
        border-radius: 15px;
        -webkit-border-radius: 15px;
    }

    #recruitManagePlanDetailDialog ::-webkit-scrollbar-thumb:horizontal {
        width: 7px;
        background-color: rgba(144, 147, 153, 0.5);
        border-radius: 15px;
        -webkit-border-radius: 15px;
    }

    #recruitManagePlanDetailDialog .hidden {
        display: none;
    }

    #recruitManagePlanDetailDialog > * {
        box-sizing: border-box;
    }

    #recruitManagePlanDetailDialog .plan_detail_top_box {
        display: flex;
        justify-content: space-between;
    }

    #recruitManagePlanDetailDialog .plan_detail_top_box .recruit_back {
        padding: 6px 8px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        cursor: pointer;
        background: #FAFAFA;
        border-radius: 2px;
        border: 1px solid #E7EBF0;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
    }

    #recruitManagePlanDetailDialog .plan_detail_top_box .recruit_back img {
        margin-right: 8px;
        width: 16px;
        height: 16px;
    }

    #recruitManagePlanDetailDialog .plan-detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 8px;
        font-size: 14px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        color: #333333;
    }

    #recruitManagePlanDetailDialog .plan-detail-top .planName {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
        margin-right: 24px;
    }

    #recruitManagePlanDetailDialog .plan-detail-top span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
    }

    #recruitManagePlanDetailDialog .plan-detail-top .planTime,
    #recruitManagePlanDetailDialog .plan-detail-top .planPostMax {
        margin-right: 24px;
    }

    #recruitManagePlanDetailDialog .recruit-bottom {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    #recruitManagePlanDetailDialog .recruit-bottom .recruit-progress {
        width: 106px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E7EBF0;
        cursor: pointer;
    }

    #recruitManagePlanDetailDialog .recruit-bottom .recruit-progress.active {
        background: #4b7aff !important;
        color: #fff !important;
    }

    #recruitManagePlanDetailDialog .recruit-bottom .recruit-progress.stage_actice {
        background: #fff;
        border: 1px solid #4b7aff;
        color: #4b7aff;
    }

    #recruitManagePlanDetailDialog .recruit-bottom img {
        width: 30px;
        height: 12px;
        margin: 0 16px;
    }

    #recruitManagePlanDetailDialog .recruit-content {
        height: calc(100% - 64px);
        width: 100%;
    }

    #recruitManagePlanDetailDialog .recruit-content .content-item {
        height: calc(100% - 15px);
        position: relative;
    }

    #recruitManagePlanDetailDialog .recruit-content .content-item .announcement-table-box {
        position: relative;
        width: 100%;
        height: 450px;
    }

    #recruitManagePlanDetailDialog .recruit-content .content-item .announcement-table-box #gview_RecruitMessageTable {
        height: 100%;
    }

    #recruitManagePlanDetailDialog .recruit-manage-notice {
        display: flex;
        align-items: center;
    }

    #recruitManagePlanDetailDialog .recruit-manage-notice img {
        margin-right: 8px;
        width: 36px;
        height: 36px;
    }

    #recruitManagePlanDetailDialog .tree-and-content {
        display: flex;
        width: 100%;
        height: 100%
    }

    #recruitManagePlanDetailDialog .tree-and-content .oa-search-tree {
        height: calc(100% - 90px);
        width: 160px;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E7EBF0;
    }

    #recruitManagePlanDetailDialog .tree-and-content .oa-search-tree .ztree-box {
        top: 0px !important;
    }

    #recruitManagePlanDetailDialog .tree-and-content #RecruitManagePlanPostBox li {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #666666;
        line-height: 19px;
        margin-top: 16px;
        cursor: pointer;
    }

    #recruitManagePlanDetailDialog .tree-and-content #RecruitManagePlanPostBox li.active {
        color: #5260FF;
    }

    #recruitManagePlanDetailDialog .announcement .ui-jqgrid-bdiv {
        top: 60px !important;
    }

    #recruitManagePlanDetailDialog .announcement .ui-jqgrid-htable {
        width: 100% !important;
    }

    #recruitManagePlanDetailDialog .announcement #RecruitMessageTable {
        width: 100% !important;
    }


    #recruitManagePlanDetailDialog .announcement .for-file-box {
      padding: 16px 0 8px;
      display: flex;
    }

    #recruitManagePlanDetailDialog .announcement .for-file-box span{
      display: inline-block;
      width: 70px;
    }

    #recruitManagePlanDetailDialog .announcement .for-file-box #PlanFileListBox {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
    }

    #recruitManagePlanDetailDialog .announcement .for-file-box #PlanFileListBox li {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        display: flex;
        align-items: center;
        margin-right: 16px;
        margin-bottom: 8px;
    }

    #recruitManagePlanDetailDialog .announcement .for-file-box #PlanFileListBox li img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
    }

    #recruitManagePlanDetailDialog .announcement .for-file-box #PlanFileListBox li .download-file-item {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #5260FF;
        line-height: 22px;
        margin-left: 8px;
    }

    #recruitManagePlanDetailDialog .content-box {
        height: 100%;
    }

    #recruitManagePlanDetailDialog .transen-con-view-box {
        left: 8px;
        padding: 0;
    }

    #recruitManagePlanDetailDialog .announcement .transen-con-view-box {
        left: 0 !important;
    }

    #recruitManagePlanDetailDialog .transen-con-view-box .trasen-con-box {
        left: 0px !important;
        width: 100%;
    }

    #recruitManagePlanDetailDialog .written .table-box,
    #recruitManagePlanDetailDialog .interview .table-box {
        top: 39px !important;
    }

    #recruitManagePlanDetailDialog .interview .oa-more-btn-item {
        width: 100%;
    }


    #recruitManagePlanDetailDialog .examination-status-tips {
        margin-left: 25px;
        font-size: 14px;
        color: #F59A25;
    }
</style>

<div id="recruitManagePlanDetailDialog">
  <div class="plan_detail_top_box">
    <div class="plan-detail-top">
      <p class="planName"></p>
      <span>报名时间：<span class="planTime"></span></span>
      <span>限报岗位：<span class="planPostMax"></span></span>
      <span>共招聘：<span class="planPostNum"></span>个岗位， <span class="planPersonTot"></span>人。</span>
    </div>
    <button class="recruit_back">
      <img src="../../static/img/other/recruitManageBack.png" alt="">
      <span>返回</span>
    </button>
  </div>
  <div class="recruit-bottom">
    <span class="recruit-progress" data-active="0">招聘公告</span>
    <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
    <span class="recruit-progress" data-active="1" id="ExaminationStep"></span>
    <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
    <span class="recruit-progress" data-active="2" id="WrittenStep"></span>
    <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
    <span class="recruit-progress" data-active="3" id="InterviewStep"></span>
  </div>
  <div class="tree-and-content">
    <!-- 左侧树 -->
    <div class="oa-search-tree">
      <div class="ztree-box scrollbar-box" style="top: 30px">
        <ul id="RecruitManagePlanPostBox"></ul>
        <input type="hidden" id="RecruitManagePostValue" value="">
      </div>
    </div>
    <div class="recruit-content">
      <div class="content-item announcement" show-active="0">

        <div class="recruit-manage-notice">
          <span>招聘公告：</span>
          <img src="../../static/img/other/icon-word.png" alt="">
          <a href='javascript:;' style='color:#5260FF;' id='ViewAnnouncementBtn'>查看详情</a>
        </div>
        <div class="for-file-box">
          <span>相关附件：</span>
          <ul id="PlanFileListBox">
          </ul>
        </div>

        <div class="announcement-table-box">
          <div class="transen-con-view-box scrollbar-box">
            <div class="trasen-con-box">
              <div class="table-box">
                <table id="RecruitMessageTable"></table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-item examination" show-active="1">
        <div class="content-box">
          <div class="transen-con-view-box">
            <div class="oa-nav">
              <a href="javascript:;" class="oa-nav_item active" data-value="0"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="2"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="3"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="1"></a>
              <span class="examination-status-tips"></span>
              <input type="hidden" id="RecruitManagePostStatusValue" value="0">
            </div>
            <div class="trasen-con-box">
              <div class="table-box" id="RecruitManagePlanPostTableBox">
                <!-- 表单 -->
                <table id="RecruitManagePlanPostTable"></table>
                <!-- 分页 -->
                <div id="RecruitManagePlanPostTablePage"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-item written" show-active="2">
        <div class="content-box">
          <div class="transen-con-view-box">
            <div class="oa-nav">
              <a href="javascript:;" class="oa-nav_item active" data-value="1"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="2"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="3"></a>
              <span class="examination-status-tips"></span>
              <input type="hidden" id="WrittenPostStatusValue" value="1">
            </div>
            <div class="trasen-con-box">
              <div class="oa-nav-search">
                <!--全部-->
                <form id="WrittenAllForm" class="layui-form">
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">学历</span>
                    <div class="shell-layer-input-box">
                      <!--<select name="educationTypeList" id="SecondEducationTypeListBox"></select>-->
                      <input autocomplete="off" class="layui-input" id="SecondEducationTypeListBox" type="text"/>
                      <input type="hidden" name="educationTypeList">
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">职称</span>
                    <div class="shell-layer-input-box">
                      <input name="professionalName" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="layui-btn" lay-submit lay-filter="WrittenFormSearch">搜索</button>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="search_list" screen-box-tar="WrittenFormMoreBox">
                      <i class="oaicon oa-icon-search_list"></i>
                    </button>
                    <button type="button" class="layui-btn oa-btn-reset" lay-submit lay-filter="WrittenFormReset"><i
                      class="layui-icon layui-icon-refresh"></i></button>
                  </div>
                </form>
                <!--通过-->
                <form id="WrittenPassForm" class="layui-form hidden">
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">身份证号码</span>
                    <div class="shell-layer-input-box">
                      <input name="identityCard" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">姓名</span>
                    <div class="shell-layer-input-box">
                      <input name="name" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">考试地点</span>
                    <div class="shell-layer-input-box">
                      <input name="examAddr" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="layui-btn" lay-submit lay-filter="WrittenFormSearch">搜索</button>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="layui-btn oa-btn-reset" lay-submit lay-filter="WrittenFormReset"><i
                      class="layui-icon layui-icon-refresh"></i></button>
                  </div>
                </form>
                <div class="fr">
                  <div id="WrittenAllBtn">
                    <button type="button" class="layui-btn" id="ImportInfomationBtn">导入准考证信息</button>
                    <a href="javascript:;" class="layui-btn layui-btn-primary" id="ImportGradesBtn">导入成绩</a>
                    <div class="layui-btn layui-border-theme oa-more-btn">
                      更多
                      <i class="layui-icon layui-icon-down"></i>
                      <div class="oa-more-btn-box">
                        <button type="button" class="oa-more-btn-item" id="ExportListBtn">导出名单</button>
                        <button type="button" class="oa-more-btn-item" id="ExemptionBtn">免试</button>
                      </div>
                    </div>
                  </div>
                  <div id="WrittenOtherBtn" class="hidden">
                    <button type="button" class="layui-btn" id="ExportListBtn">导出名单</button>
                    <a href="javascript:;" class="layui-btn layui-btn-primary" id="SMSNotificationBtn">短信通知</a>
                  </div>
                </div>


                <div class="screen-box" screen-box="WrittenFormMoreBox">
                  <div class="screen-box_tit">更多查询条件</div>
                  <div class="screen-box_con">
                    <form class="layui-form row" id="WrittenFormMore">
                      <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">身份证号码</span>
                        <div class="shell-layer-input-box">
                          <input name="identityCard" class="layui-input"/>
                        </div>
                      </div>
                      <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">姓名</span>
                        <div class="shell-layer-input-box">
                          <input name="name" class="layui-input"/>
                        </div>
                      </div>
                      <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">考试地点</span>
                        <div class="shell-layer-input-box">
                          <input name="examAddr" class="layui-input"/>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="screen-box_btn">
                    <span class="layui-btn" id="WrittenFormMoreSearch" screen-box-tar="WrittenFormMoreBox">搜索</span>
                    <span class="layui-btn layui-btn-primary" id="WrittenFormMoreReset">重置</span>
                    <span class="layui-btn layui-btn-primary" screen-box-tar="WrittenFormMoreBox">关闭</span>
                  </div>
                </div>
              </div>
              <div class="table-box" id="WrittenTableBox">
                <!-- 表单 -->
                <table id="WrittenTable"></table>
                <!-- 分页 -->
                <div id="WrittenTablePage"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-item interview" show-active="3">
        <div class="content-box">
          <div class="transen-con-view-box">
            <div class="oa-nav">
              <a href="javascript:;" class="oa-nav_item active" data-value="1"></a>
              <a href="javascript:;" class="oa-nav_item" data-value="2"></a>
              <span class="examination-status-tips"></span>
              <input type="hidden" id="interviewPostStatusValue" value="1">
            </div>
            <div class="trasen-con-box">
              <div class="oa-nav-search">
                <form id="interviewForm" class="layui-form">
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">姓名</span>
                    <div class="shell-layer-input-box">
                      <input name="name" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">身份证号码</span>
                    <div class="shell-layer-input-box">
                      <input name="identityCard" class="layui-input"/>
                    </div>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="layui-btn" lay-submit lay-filter="interviewFormSubmit">搜索
                    </button>
                  </div>
                  <div class="shell-search-box">
                    <button type="button" class="search_list" screen-box-tar="interviewFormMoreBox">
                      <i class="oaicon oa-icon-search_list"></i>
                    </button>
                    <button type="button" class="layui-btn oa-btn-reset" lay-submit lay-filter="interviewFormReset"><i
                      class="layui-icon layui-icon-refresh"></i></button>
                  </div>
                </form>
                <div class="fr">
                  <button type="button" class="layui-btn" id="interviewEnterInfo">导入准考证信息</button>
                  <a href="javascript:;" class="layui-btn layui-btn-primary" id="interviewEnterFraction">导入成绩</a>
                  <div class="layui-btn layui-border-theme oa-more-btn">
                    更多
                    <i class="layui-icon layui-icon-down"></i>
                    <div class="oa-more-btn-box">
                      <button type="button" class="oa-more-btn-item" id="interviewOutList">导出名单</button>
                      <button type="button" class="oa-more-btn-item" id="interviewMessage">短信通知</button>
                    </div>
                  </div>
                </div>

                <div class="screen-box" screen-box="interviewFormMoreBox">
                  <div class="screen-box_tit">更多查询条件</div>
                  <div class="screen-box_con">
                    <form class="layui-form row" id="interviewFormMore">
                      <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">考试地点</span>
                        <div class="shell-layer-input-box">
                          <input name="interviewAddr" class="layui-input"/>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="screen-box_btn">
                    <span class="layui-btn" id="interviewFormMoreSearch" screen-box-tar="interviewFormMoreBox">搜索</span>
                    <span class="layui-btn layui-btn-primary" id="interviewFormMoreReset">重置</span>
                    <span class="layui-btn layui-btn-primary" screen-box-tar="interviewFormMoreBox">关闭</span>
                  </div>
                </div>
              </div>
              <div class="table-box" id="interviewTableBox">
                <!-- 表单 -->
                <table id="interviewTable"></table>
                <!-- 分页 -->
                <div id="interviewTablePage"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
