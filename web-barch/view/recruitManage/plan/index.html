<style>
    #RecruitBox {
        width: 100%;
        height: 100%;
        background: #fff;
    }

    #recruitManagePlan .trasen-con-box {
        top: 55px;
    }

    #recruitManagePlan .recruit-manage-opt-btn {
        display: flex;
        align-items: center;
    }


    #recruitManagePlan .recruit-manage-opt-btn > img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
    }

    #recruitManagePlan .recruit-manage-opt-btn > span {
        font-size: 12px;
    }

    /* 全局附件上传*/
    .file-box {
        display: flex;
        align-items: center;
    }

    .new_file_style {
        cursor: pointer;
        width: 110px;
        height: 30px;
        box-sizing: border-box;
        padding: 0 14px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
    }

    .new_file_style img, .success_file_icon {
        width: 16px;
        height: 16px;
        margin-right: 5px;
    }

    /* 全局新操作样式*/
    .new_operate_style {
        display: flex;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #5260FF;
        line-height: 17px;
    }

    .new_operate_style .first_operate_btn {
        width: 80px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin: 0 8px;
        cursor: pointer;
    }

    .new_operate_style .new_operate_more {
        position: relative;
        cursor: pointer;
    }

    .new_operate_style .new_operate_more:hover .new_operate_more_box {
        display: block;
    }

    .new_operate_style .new_operate_more_box {
        display: none;
        position: absolute;
        width: auto;
        top: 10px;
        right: 100%;
        padding: 8px;
        z-index: 10001;
        background: #fff;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        box-shadow: 0 2px 10px 0px #d8d8d8;
        overflow: hidden;
    }

    .new_operate_style .new_operate_more_box > p {
        cursor: pointer;
        padding: 3px 5px;
    }
</style>

<div id="RecruitBox" class="content-box">
  <div id="recruitManagePlan">
    <div class="oa-nav-search">
      <form id="recruitManagePlanForm" class="layui-form">
        <div class="shell-search-box">
          <span class="shell-layer-input-boxTit">计划名称</span>
          <div class="shell-layer-input-box">
            <input type="text" name="name" class="layui-input" search-input="recruitManagePlan-search"
                   placeholder="请输入计划名称"/>
          </div>
        </div>
        <div class="shell-search-box">
          <span class="shell-layer-input-boxTit">报名月份</span>
          <div class="shell-layer-input-box">
            <input id="signUpMonth" name="signUpMonth" class="layui-input" search-input="recruitManagePlan-search"/>
          </div>
        </div>
        <div class="shell-search-box" id="PlanStatusBox">
          <span class="shell-layer-input-boxTit">是否发布</span>
          <div class="shell-layer-input-box">
            <select name="planStatus" lay-filter="recruitManagePlan-search" lay-search>
              <option value="0">全部</option>
              <option value="2">发布</option>
              <option value="1">未发布</option>
            </select>
          </div>
        </div>
        <div class="shell-search-box">
          <button type="button" class="layui-btn" lay-submit lay-filter="recruitManagePlanSearch">搜索</button>
        </div>
        <div class="shell-search-box">
          <button type="button" id="recruitManageTableRefresh" class="layui-btn oa-btn-reset"><i
            class="layui-icon layui-icon-refresh"></i></button>
        </div>
      </form>
      <div class="fr">
        <button type="button" class="layui-btn" id="recruitManagePlanAdd">新增</button>
      </div>
    </div>

    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="grid-table-recruitManagePlanTable"></table>
        <!-- 分页 -->
        <div id="grid-pager-recruitManagePlanPage"></div>
      </div>
    </div>
  </div>
</div>
