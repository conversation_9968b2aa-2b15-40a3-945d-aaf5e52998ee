'use strict';
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(['form', 'zTreeSearch', 'layer', 'element', 'trasen'], function () {
        let {
          form,
          laydate,
          layer,
          element,
          upload,
          trasen
        } = layui;
        let API = {
          // 下载准考证模板
          downloadTemplate: '/ts-hrms/recruitSignUpExam/download/template/importExam',
          // 导入准考证
          recruitSignUpExamSave: '/ts-hrms/recruitSignUpExam/save',
        }
        let templateArr = []
        let templateId = []

        let ImportInfomationDialog = layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          shadeClose: false,
          area: ['800px', '600px'],
          content: html,
          success: function (layero, index) {
            // 考试时间
            laydate.render({
              elem: '#ImportInfomationForm input[name="dateymd"]',
              trigger: 'click',
              min: new Date().Format('yyyy-MM-dd'),
            });

            laydate.render({
              elem: '#ImportInfomationForm input[name="datehm"]',
              type: 'time',
              range: true,
              trigger: 'click',
              format: 'HH:mm',
              classes: 'time_Hm',
              done: function (value, date) {
                if (value != '') {
                  let _time = value.split(' - ');
                  let _isOK = CompareDate('2020-01-01 ' + _time[0] + ':00', '2020-01-01 ' + _time[1] + ':00');
                  if (!_isOK) {
                    trasen.setNamesVal($('#ImportInfomationForm'), {
                      datehm: ''
                    })
                    layer.msg('结束时间不能小于或者等于开始时间');
                    $('#ImportInfomationForm input[name="datehm"]').focus()
                    throw '结束时间不能小于或者等于开始时间';
                  }
                }
              },
            });

            //比较时间
            function CompareDate (d1, d2) {
              return new Date(d1.replace(/-/g, '/')) < new Date(d2.replace(/-/g, '/'));
            }

            upload.render({
              elem: '#ImportInfomationUploadTemplateBtn',
              url: '/ts-basics-bottom/fileAttachment/upload?moduleName=hrm',
              accept: 'file',
              exts: 'xlsx',
              multiple: false,
              done: function (res, index, upload) {
                let filesHtml = "";
                $.each(res.object, function (i, item) {
                  templateArr = [{
                    fileName: item.fileName,
                    fileUrl: '/ts-basics-bottom/fileAttachment/downloadFile/' + item.fileId,
                  }];
                  let isDoc = common.isDoc(item.fileName);

                  filesHtml += isDoc ? `<li>
                                          <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                          ${item.fileRealName}
                                          <a class="previewA viewerDocBase" filename="${item.fileName}" fileid="${item.fileId}" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/${item.fileId}" href="javascript:void(0)">预览</a>
                                          <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                        </li>`
                    : '';

                  templateId[0] = item.fileId
                })
                $('#ImportInfomationForm #TemplateBox').html(filesHtml)
              }
            });

            //预览
            $('#ImportInfomationForm').off('click', '.viewerImg').on('click', '.viewerImg', function (e) {
              common.viewerImg(templateArr, '/ts-basics-bottom/fileAttachment/downloadFile/' + $(this).attr('fileurl'), 'hr');
              e.stopPropagation();
              return false;
            });

            //删除附件
            $('#ImportInfomationForm').off('click', '.deleteA').on('click', '.deleteA', function () {
              let fileId = $(this).attr("attr-id");
              if ($.inArray(fileId, templateId) != -1) {
                templateId.splice($.inArray(fileId, templateId), 1);
              }
              $(this).parent().remove();
            });

            form.on('submit(ImportInfomationFormSubmit)', function (data) {
              if (!templateId.length) {
                layer.msg('请上传准考信息附件!');
                return false
              }

              var d = data.field;
              const hmArr = d.datehm.split(' - ')
              d.examBeginTime = d.dateymd + ' ' + hmArr[0]
              d.examEndTime = d.dateymd + ' ' + hmArr[1]

              d.postId = opt.data.postId

              d.fileId = templateId.join(',') // 招聘岗位id

              delete d.dateymd
              delete d.datehm
              $.loadings();

              $.ajax({
                type: 'post',
                url: common.url + API.recruitSignUpExamSave,
                contentType: 'application/json;charset=UTF-8',
                data: JSON.stringify(d),
                success: function (res) {
                  $.closeloadings();
                  if (res.success && res.statusCode === 200) {
                    layer.msg('操作成功!');
                    opt.ref && opt.ref()
                    layer.close(ImportInfomationDialog);
                  } else {
                    let message = res.message

                    let reg = /\[/g
                    if (reg.test(message)) {
                      let str = ''
                      JSON.parse(message).forEach(item => str += `<p>${item}</p>`)
                      layer.alert(str, {area: ['700px', '350px']})
                    } else {
                      layer.alert(message)
                    }
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message, {time: 6000})
                },
              });
            })

            $('#ImportInfomationForm').off('click', '.download-template').on('click', '.download-template', function () {
              let xhr = new XMLHttpRequest();
              let url = common.url + API.downloadTemplate;
              xhr.open('post', url, true);
              xhr.responseType = "blob";
              xhr.setRequestHeader('Content-Type', 'application/json');
              xhr.send()
              xhr.onload = function () {
                if (this.status === 200) {
                  let url = window.URL.createObjectURL(new Blob([this.response]));
                  let link = document.createElement('a');
                  link.style.display = 'none';
                  link.href = url;
                  link.setAttribute('download', '准考信息模版.xlsx');
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link); //下载完成移除元素
                  window.URL.revokeObjectURL(url); //释放掉blob对象
                }
              }
            })

            // 关闭弹窗
            $('#ImportInfomationForm #CloseDialog').funs('click', function () {
              layer.close(ImportInfomationDialog);
            });
          }
        })
      }
    );
  };
});
