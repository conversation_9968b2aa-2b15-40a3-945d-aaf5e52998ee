"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
      $("#RecruitBox").append(html);

      layui.use(["form", "zTreeSearch", "layer", "element"], function () {
          var {form, layer} = layui;
          var API = {
            // 获取招聘计划岗位列表
            getRecruitPlanPostMessage: "/ts-hrms/recruitPlanPost/getList",
            // 获取招聘计划状态统计
            getRecruitPlanCount: "/ts-hrms/recruitPlan/planCount",
            // 获取招聘计划下的附件列表
            getFileAttachmentByBusinessId: "/ts-basics-bottom/fileAttachment/getFileAttachmentByBusinessId",
            // 获取招聘计划 岗位下的报名列表
            getRecruitSignUpList: "/ts-hrms/recruitSignUp/getList",
            // 获取招聘计划 岗位的报名统计
            getRecruitSignUpSignUpCount: "/ts-hrms/recruitSignUp/signUpCount",
            // 获取审查模版
            getFormTemplateApi: "/ts-hrms/recruitWebSite/get",
            // 获取笔试岗位统计
            getSignUpExamCount: "/ts-hrms/recruitSignUpExam/signUpExamCount",
            // 获取笔试列表
            getRecruitSignUpExamList: "/ts-hrms/recruitSignUpExam/getList",
            // 免试接口
            recruitSignUpExamExemption: "/ts-hrms/recruitSignUpExam/exemption",
            // 面试统计
            signUpInterviewCount: "/ts-hrms/recruitSignUpInterview/signUpInterviewCount",
            // 面试列表
            getRecruitSignUpInterview: "/ts-hrms/recruitSignUpInterview/getList",
            // 笔试导出名单
            downloadImportExam: "/ts-hrms/recruitSignUpExam/download/importExam",
            // 面试导出名单
            exportInterviewList: "/ts-hrms/recruitSignUpInterview/download/exportInterview",
            recruitPlanGetPower: "/ts-hrms/recruitPlan/getPower"
          };
          let templateName = "";
          let templateForm = {
            lyszyy: function (opt) {
              $.quoteFun("recruitManage/plan/templateForm/lyszyy", opt);
            },
            lyszyyInfo: function (opt) {
              $.quoteFun("recruitManage/plan/templateForm/lyszyyInfo", opt);
            }
          };
          let postList = null;
          let postTreeDom = null;
          let examinationStatusInput = null;
          let writtenStatusInput = null;
          let interviewStatusInput = null;

          let RecruitManagePlanPostTable = null;
          let writtenTable = null;
          let interviewTable = null;

          let freeExemptionList = [];

          let ACTIVE = 0;
          let planPostData = null;
          let firstAuditShow = false;
          let reviewAuditShow = false;

          let educationAll = null

          postTreeDom = $("#recruitManagePlanDetailDialog #RecruitManagePostValue");

          switch (opt.data.stage) {
            case '3':
              $('#recruitManagePlanDetailDialog #ExaminationStep').addClass('stage_actice')
              break;
            case '4':
              $('#recruitManagePlanDetailDialog #WrittenStep').addClass('stage_actice')
              break;
            case '5':
              $('#recruitManagePlanDetailDialog #InterviewStep').addClass('stage_actice')
              break;
          }

          // 获取模版名称
          getTemplateName();
          // 获取初审 复审资格
          getRecruitPlanGetPower();


          educationAll = new $.checkSelect('#recruitManagePlanDetailDialog #SecondEducationTypeListBox', {
            url: '/ts-hrms/recruitPlan/getEducationPage',
            type: 'post', //请求类型
            datatype: 'json',
            label: 'itemName',
            value: 'itemCode',
            condition: 'itemNameValue',
            callback: function (data) {
              $('#recruitManagePlanDetailDialog #WrittenAllForm [name="educationTypeList"]').val(data.map(item => item.itemNameValue).join(','))
            }
          });
          $('#recruitManagePlanDetailDialog #SecondEducationTypeListBox').attr('placeholder', '请选择学历')


          // 显示招聘计划名称 时间
          $("#recruitManagePlanDetailDialog .plan-detail-top .planName").text(opt.data.name);
          $("#recruitManagePlanDetailDialog .plan-detail-top .planTime").text(opt.data.intervalTime);

          // 招聘计划 限报岗位、报名人事、岗位个数
          let limitPostNumStr = Number(opt.data.limitPostNum) > 0 ? `${opt.data.limitPostNum}个` : "-";
          $("#recruitManagePlanDetailDialog .plan-detail-top .planPostMax").text(limitPostNumStr);

          // 共招聘多少个岗位 多少人
          $("#recruitManagePlanDetailDialog .plan-detail-top .planPostNum").text(opt.data.postNum);
          $("#recruitManagePlanDetailDialog .plan-detail-top .planPersonTot").text(opt.data.limitPeopleNum);

          initActiveContentHandle(ACTIVE);
          // 获取招聘计划 岗位列表数据 与计划统计数据
          getPlanStatusHandle();
          planPostData = getPlanMessageHandle(opt.data.id);
          // 渲染岗位树
          renderPostTree("first");

          var cols = [
            {label: "岗位编码", name: "postCode", width: 80, align: "center", sortable: false, fixed: true},
            {label: "招聘岗位", name: "postName", width: 80, align: "center", sortable: false, fixed: true},
            {label: "招聘人数", name: "limitNum", width: 80, align: "center", sortable: false, fixed: true},
            {label: "年龄上限", name: "limitAge", width: 80, align: "center", sortable: false, fixed: true},
            {label: "性别", name: "genderLable", width: 60, align: "center", sortable: false, fixed: true},
            {label: "最低学历", name: "minEducationLable", width: 80, align: "center", sortable: false, fixed: true},
            {label: "全日制", name: "educationTypeLable", width: 70, align: "center", sortable: false, fixed: true},
            {label: "应届", name: "graduateLable", width: 60, align: "center", sortable: false, fixed: true},
            {label: "专业/方向", name: "major", align: "center", sortable: false},
            {label: "其他要求", name: "other", align: "center", sortable: false}
          ];
          // 招聘计划岗位明细列表
          $.jgrid.gridUnload("RecruitMessageTable");
          var RecruitMessageTableTable = new $.trasenTable("RecruitMessageTable", {
            data: planPostData,
            datatype: "local",
            colModel: cols
          });
          jQuery("#RecruitMessageTable").jqGrid("setGroupHeaders", {
            useColSpanStyle: true,
            groupHeaders: [{
              startColumnName: "limitAge",
              numberOfColumns: 6,
              titleText: "岗位定编人数"
            }]
          });

          // 回显招聘公告
          $("#recruitManagePlanDetailDialog").off("click", "#ViewAnnouncementBtn").on("click", "#ViewAnnouncementBtn", function () {
            $.quoteFun("/recruitManage/plan/detailDialog/ViewAnnouncement", {
              data: {notice: opt.data.notice},
            });
          })

          // 回显附件
          if (opt.data.businessFileId) {
            getPlanFileHandle(opt.data.businessFileId);
          }

          form.render("select");

          $("#recruitManagePlanDetailDialog").off("click", ".recruit_back").on("click", ".recruit_back", function () {
            $("#RecruitBox #recruitManagePlanDetailDialog").remove();
            $("#RecruitBox #recruitManagePlan").show();
            opt.ref && opt.ref()
          })

          // 获取模版名称
          function getTemplateName () {
            $.ajax({
              type: "get",
              url: common.url + "/ts-hrms/recruitWebSite/get",
              dateType: "json",
              async: false,
              contentType: "application/json",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  templateName = res.object.template;
                }
              }
            });
          }

          // 获取模版名称
          function getRecruitPlanGetPower () {
            $.ajax({
              type: "post",
              url: common.url + API.recruitPlanGetPower,
              dateType: "json",
              async: false,
              contentType: "application/json",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  const codeArr = res.object.map(item => item.code);
                  firstAuditShow = codeArr.includes("firstAudit");
                  reviewAuditShow = codeArr.includes("reviewAudit");
                }
              }
            });
          }

          // 渲染 岗位树
          function renderPostTree (type) {
            postList = planPostData.map(postItem => {
              return {
                id: postItem.id,
                postName: postItem.postName,
                postCode: postItem.postCode,
                limitNum: postItem.limitNum,
                examBeginTime: postItem.examBeginTime,
                examEndTime: postItem.examEndTime,
                examContent: postItem.examContent,
                major: postItem.major,

                interviewBeginTime: postItem.interviewBeginTime,
                interviewEndTime: postItem.interviewEndTime,
                interviewContent: postItem.interviewContent
              };
            });

            // 初始 渲染树
            if (type === "first") {
              let postHtml = ``;
              postList.forEach((item, index) => {
                postHtml += `
                      <li class="${index === 0 ? "active" : ""}" id="${item.id}" postName="${item.postName}" postCode="${item.postCode}" limitNum="${item.limitNum}">
                        <span>${item.postCode}</span>
                        <p>${item.postName}</p>
                      </li>
                      `;
              });
              postTreeDom.val(postList[0].id);

              let ulBox = $("#recruitManagePlanDetailDialog #RecruitManagePlanPostBox");
              ulBox.html(postHtml);
            }
          }

          // 获取招聘计划状态统计
          function getPlanStatusHandle () {
            $.ajax({
              type: "post",
              url: common.url + API.getRecruitPlanCount,
              data: JSON.stringify({
                planId: opt.data.id
              }),
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  let signUpStr = res.object.signUp > 0 ? `审查(${res.object.signUp})` : "审查";
                  let signUpExamStr = res.object.signUpExam > 0 ? `笔试(${res.object.signUpExam})` : "笔试";
                  let signUpInterviewStr = res.object.signUpInterview > 0 ? `面试(${res.object.signUpInterview})` : "面试";

                  const progressItemDom = $("#recruitManagePlanDetailDialog .recruit-progress");
                  progressItemDom.eq(1).text(signUpStr);
                  progressItemDom.eq(2).text(signUpExamStr);
                  progressItemDom.eq(3).text(signUpInterviewStr);
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
          }

          // 获取招聘计划岗位
          function getPlanMessageHandle (planId) {
            var result = [];
            $.ajax({
              type: "post",
              url: common.url + API.getRecruitPlanPostMessage,
              async: false,
              data: JSON.stringify({
                planId
              }),
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  result = res.object;
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
            return result;
          }

          // 获取招聘计划附件
          function getPlanFileHandle (businessFileId) {
            let str = "";
            $.ajax({
              type: "get",
              url: common.url + API.getFileAttachmentByBusinessId,
              async: false,
              data: {
                businessId: businessFileId
              },
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  res.object.forEach(item => {
                    str += `
                       <li><img src='/static/img/other/icon_file_tips.png'>${item.originalName}<a id="${item.id}" realname="${item.originalName}" class="download-file-item" href="javascript:void(0)">下载</a></li>
                       `;
                  });
                  $("#recruitManagePlanDetailDialog #PlanFileListBox").append(str);
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
          }

          // 下载附件
          $("#recruitManagePlanDetailDialog #PlanFileListBox").off("click", ".download-file-item").on("click", ".download-file-item", function () {
            const fileId = $(this).attr("id");
            const fileName = $(this).attr("realname");
            let xhr = new XMLHttpRequest();
            let url = common.url + `/ts-basics-bottom/fileAttachment/downloadFile/${fileId}`;
            xhr.open("get", url, true);
            xhr.responseType = "blob";
            xhr.setRequestHeader("Content-Type", "application/json");
            xhr.send();
            xhr.onload = function () {
              if (this.status === 200) {
                let url = window.URL.createObjectURL(new Blob([this.response]));
                let link = document.createElement("a");
                link.style.display = "none";
                link.href = url;
                link.setAttribute("download", fileName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link); //下载完成移除元素
                window.URL.revokeObjectURL(url); //释放掉blob对象
              }
            };
            e.stopPropagation();
            return false;
          });

          // 切换进程
          $("#recruitManagePlanDetailDialog .recruit-bottom").off("click", ".recruit-progress").on("click", ".recruit-progress", function () {
            freeExemptionList = [];

            ACTIVE = $(this).data("active");
            initActiveContentHandle(ACTIVE);

            switch (ACTIVE) {
              case 1:
                examinationStatusInput = $("#recruitManagePlanDetailDialog .examination #RecruitManagePostStatusValue");
                const postActiveData = postList.find(item => item.id === postTreeDom.val());

                renderExaminationPostTotalHandle("", postActiveData.limitNum);
                renderExaminationPostStatusTableHandle();
                break;
              case 2:
                writtenStatusInput = $("#recruitManagePlanDetailDialog .written #WrittenPostStatusValue");
                renderWrittenPostTotalHandle();
                renderWrittenTableHandle();
                break;
              case 3:
                interviewStatusInput = $("#recruitManagePlanDetailDialog .interview #interviewPostStatusValue");
                renderInterviewPostTotalHandle();
                renderInterviewTableHandle();
                break;
              default:
                break;
            }
          });

          // 岗位树的点击事件
          $("#recruitManagePlanDetailDialog #RecruitManagePlanPostBox").off("click", "li").on("click", "li", function () {
            freeExemptionList = [];

            $(this).addClass("active").siblings().removeClass("active");
            const id = $(this).attr("id");
            postTreeDom.val(id);

            switch (ACTIVE) {
              case 1:
                const limitNum = $(this).attr("limitNum");
                renderExaminationPostTotalHandle(id, limitNum);
                renderExaminationPostStatusTableHandle(id);
                break;
              case 2:
                renderWrittenPostTotalHandle(id);
                renderWrittenTableHandle();
                break;
              case 3:
                renderInterviewPostTotalHandle(id);
                renderInterviewTableHandle();
                break;
              default:
                break;
            }
          });

          // 审查 岗位状态的点击事件
          $("#recruitManagePlanDetailDialog .examination .oa-nav").off("click", ".oa-nav_item").on("click", ".oa-nav_item", function () {
            $(this).addClass("active").siblings().removeClass("active");
            const value = $(this).data("value");
            examinationStatusInput.val(value);

            renderExaminationPostStatusTableHandle("", value);
          });

          // 岗位审查的状态统计
          function renderExaminationPostTotalHandle (postId, limitNum) {
            $.ajax({
              type: "post",
              url: common.url + API.getRecruitSignUpSignUpCount,
              async: false,
              data: JSON.stringify({
                postId: postId || postTreeDom.val(),
                planId: opt.data.id
              }),
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  $("#recruitManagePlanDetailDialog .examination .examination-status-tips").text(`该岗位招聘${limitNum}人，目前报名${res.object.all}人`);

                  const nav = $("#recruitManagePlanDetailDialog .examination .oa-nav_item");
                  let waitFirstAuditStr = res.object.waitFirstAudit > 0 ? `待初审(${res.object.waitFirstAudit})`
                    : "待初审";
                  let waitReviewAuditStr = res.object.waitReviewAudit > 0 ?
                    `待复审(${res.object.waitReviewAudit})`
                    : "待复审";
                  let passStr = res.object.pass > 0 ?
                    `已通过(${res.object.pass})`
                    : "已通过";
                  let failStr = res.object.fail > 0 ?
                    `未通过(${res.object.fail})`
                    : "未通过";
                  nav.eq(0).text(waitFirstAuditStr);
                  nav.eq(1).text(waitReviewAuditStr);
                  nav.eq(2).text(passStr);
                  nav.eq(3).text(failStr);
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
          }

          // 岗位审查 table渲染 以及审查的事件绑定
          function renderExaminationPostStatusTableHandle (postId, index) {
            $("#RecruitManagePlanPostTable").remove();
            $("#RecruitManagePlanPostTablePage").html("");
            $("#RecruitManagePlanPostTableBox").append("<table id=\"RecruitManagePlanPostTable\"></table>");//再新增一个grid的渲染容器
            let arr = [
              [
                {label: "编号", name: "signupCode", width: 130, fixed: true, sortable: false, align: "center"},
                {
                  label: "姓名",
                  name: "name",
                  width: 70,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "身份证号码", name: "identityCard", width: 150, fixed: true, sortable: false, align: "center"},
                {label: "性别", name: "genderLable", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "年龄", name: "age", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "学历", name: "educationLable", width: 60, fixed: true, sortable: false, align: "center"},
                {label: "全日制", name: "educationTypeLable", width: 60, fixed: true, sortable: false, align: "center"},
                {label: "是否应届", name: "graduateLable", width: 70, fixed: true, sortable: false, align: "center"},
                {label: "专业/方向", name: "major", width: 80, sortable: false, align: "center"},
                {label: "报名时间", name: "signUpTime", width: 100, sortable: false, align: "center"},
                {label: "ID", name: "postId", hidden: true},
                {
                  label: "操作",
                  name: "opt",
                  width: 60,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  hidden: !firstAuditShow,
                  formatter: function (cellvalue, options, rowObject) {
                    return `<a href='javascript:;' style='color:blue' pageNo='${rowObject.sequence}' id='FirstExaminationBtn'>审查</a>`;
                  }
                }
              ],
              [
                {label: "编号", name: "signupCode", width: 130, fixed: true, sortable: false, align: "center"},
                {
                  label: "姓名",
                  name: "name",
                  width: 70,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "性别", name: "genderLable", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "身份证号码", name: "identityCard", width: 150, fixed: true, sortable: false, align: "center"},
                {label: "报名时间", name: "signUpTime", width: 100, sortable: false, align: "center"},
                {label: "初审时间", name: "firstAuditTime", width: 100, sortable: false, align: "center"},
                {label: "初审人", name: "firstAuditEmp.empName", width: 100, sortable: false, align: "center"},
                {label: "复审时间", name: "reviewAuditTime", width: 100, sortable: false, align: "center"},
                {label: "复审人", name: "reviewAuditEmp.empName", width: 100, sortable: false, align: "center"},
                {label: "不通过环节", name: "failStageLable", width: 100, sortable: false, align: "center"},
                {label: "原因", name: "remarks", width: 100, sortable: false, align: "center"},
                {label: "ID", name: "postId", hidden: true}
              ],
              [
                {label: "编号", name: "signupCode", width: 130, fixed: true, sortable: false, align: "center"},
                {
                  label: "姓名",
                  name: "name",
                  width: 70,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "身份证号码", name: "identityCard", width: 150, fixed: true, sortable: false, align: "center"},
                {label: "性别", name: "genderLable", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "年龄", name: "age", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "学历", name: "educationLable", width: 60, fixed: true, sortable: false, align: "center"},
                {label: "全日制", name: "educationTypeLable", width: 60, fixed: true, sortable: false, align: "center"},
                {label: "是否应届", name: "graduateLable", width: 60, fixed: true, sortable: false, align: "center"},
                {label: "专业/方向", name: "major", width: 100, sortable: false, align: "center"},
                {label: "报名时间", name: "signUpTime", width: 100, sortable: false, align: "center"},
                {label: "初审时间", name: "firstAuditTime", width: 100, sortable: false, align: "center"},
                {label: "初审人", name: "firstAuditEmp.empName", width: 100, sortable: false, align: "center"},
                {label: "ID", name: "postId", hidden: true},
                {
                  label: "操作",
                  name: "opt",
                  width: 60,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  hidden: !reviewAuditShow,
                  formatter: function (cellvalue, options, rowObject) {
                    return `<a href='javascript:;' style='color:blue' pageNo='${rowObject.sequence}' id='SecondExaminationBtn'>审查</a>`;
                  }
                }
              ],
              [
                {label: "编号", name: "signupCode", width: 130, fixed: true, sortable: false, align: "center"},
                {
                  label: "姓名",
                  name: "name",
                  width: 70,
                  fixed: true,
                  sortable: false,
                  align: "center",
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "性别", name: "genderLable", width: 50, fixed: true, sortable: false, align: "center"},
                {label: "身份证号码", name: "identityCard", width: 170, fixed: true, sortable: false, align: "center"},
                {label: "报名时间", name: "signUpTime", width: 100, sortable: false, align: "center"},
                {label: "初审时间", name: "firstAuditTime", width: 100, sortable: false, align: "center"},
                {label: "初审人", name: "firstAuditEmp.empName", width: 55, sortable: false, align: "center"},
                {label: "复审时间", name: "reviewAuditTime", width: 100, sortable: false, align: "center"},
                {label: "复审人", name: "reviewAuditEmp.empName", width: 55, sortable: false, align: "center"},
                {label: "ID", name: "postId", hidden: true}
              ]
            ];
            RecruitManagePlanPostTable = new $.trasenTable("RecruitManagePlanPostTable", {
              url: common.url + API.getRecruitSignUpList,
              pager: "RecruitManagePlanPostTablePage",
              shrinkToFit: true,
              postData: {
                signUpStatus: index || examinationStatusInput.val(),
                postId: postId || postTreeDom.val(),
                planId: opt.data.id
              },
              colModel: arr[index || examinationStatusInput.val()]
            });

            // 审查-初审
            $("#RecruitManagePlanPostTable").off("click", "#FirstExaminationBtn").on("click", "#FirstExaminationBtn", function () {
              const pageNo = $(this).attr("pageNo");
              implementRenderFormHandle(pageNo, "first");
            });

            // 审查-复审
            $("#RecruitManagePlanPostTable").off("click", "#SecondExaminationBtn").on("click", "#SecondExaminationBtn", function () {
              const pageNo = $(this).attr("pageNo");
              implementRenderFormHandle(pageNo, "second");
            });

            function implementRenderFormHandle (pageNo, type) {
              const postActiveData = postList.find(item => item.id === postTreeDom.val());
              templateForm[templateName]({
                data: {
                  pageNo,
                  pageSize: 1,
                  postId: postTreeDom.val(),
                  planId: opt.data.id,
                  signUpStatus: examinationStatusInput.val()
                },
                type,
                major: postActiveData.major,
                ref: (data) => {
                  renderExaminationPostTotalHandle(data.postId, postActiveData.limitNum);
                  renderExaminationPostStatusTableHandle(data.postId, data.signUpStatus);
                  if (type === "second") {
                    getPlanStatusHandle();
                  }
                }
              });
            }
          }

          // 笔试表格页签状态切换
          $("#recruitManagePlanDetailDialog .written .oa-nav").off("click", ".oa-nav_item").on("click", ".oa-nav_item", function () {
            freeExemptionList = [];

            $(this).addClass("active").siblings().removeClass("active");
            const value = $(this).data("value");
            writtenStatusInput.val(value);


            $("#recruitManagePlanDetailDialog .written #WrittenAllBtn").hide();
            $("#recruitManagePlanDetailDialog .written #WrittenOtherBtn").hide();
            if (value === 1) {
              $("#recruitManagePlanDetailDialog .written #WrittenAllBtn").show();
            } else {
              $("#recruitManagePlanDetailDialog .written #WrittenOtherBtn").show();
            }

            hiddenWrittenFormHandle();
            switch (value) {
              case 1:
                $("#recruitManagePlanDetailDialog .written #WrittenAllForm").show();
                break;
              case 2:
                $("#recruitManagePlanDetailDialog .written #WrittenPassForm").show();
                break;
              case 3:
                $("#recruitManagePlanDetailDialog .written #WrittenAllForm").show();
                break;
            }

            $("#WrittenAllForm")[0].reset();
            $("#WrittenFormMore")[0].reset();
            $("#WrittenPassForm")[0].reset();
            educationAll.setSelData([]);
            $('#recruitManagePlanDetailDialog #WrittenAllForm [name="educationTypeList"]').val(null)
            $('#recruitManagePlanDetailDialog #SecondEducationTypeListBox').attr('placeholder', '请选择学历')
            renderWrittenTableHandle();
          });

          function hiddenWrittenFormHandle () {
            $("#recruitManagePlanDetailDialog .written #WrittenAllForm").hide();
            $("#recruitManagePlanDetailDialog .written #WrittenPassForm").hide();
          }

          // 岗位笔试的状态统计
          function renderWrittenPostTotalHandle (postId) {
            $.ajax({
              type: "post",
              url: common.url + API.getSignUpExamCount,
              async: false,
              data: JSON.stringify({
                postId: postId || postTreeDom.val(),
                planId: opt.data.id
              }),
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  const postActiveData = postList.find(item => item.id === postTreeDom.val());
                  const {examBeginTime, examEndTime, examContent} = postActiveData;

                  let writtenTips = $("#recruitManagePlanDetailDialog .written .examination-status-tips");
                  if (examBeginTime && examEndTime) {
                    writtenTips.text(`考试时间：${examBeginTime || ""}至${examEndTime || ""} 考试内容：${examContent || ""}`);
                  } else {
                    writtenTips.text(``);
                  }

                  const nav = $("#recruitManagePlanDetailDialog .written .oa-nav_item");
                  let writtenAll = res.object.all > 0 ? `参与人员(${res.object.all})` : "参与人员";
                  let writtenPass = res.object.pass > 0 ? `考试通过(${res.object.pass})` : "考试通过";
                  let writtenExemption = res.object.exemption > 0 ? `免试名单(${res.object.exemption})` : "免试名单";
                  nav.eq(0).text(writtenAll);
                  nav.eq(1).text(writtenPass);
                  nav.eq(2).text(writtenExemption);
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
          }

          function renderWrittenTableHandle (postId, examStatus) {
            $("#WrittenTable").remove();
            $("#WrittenTablePage").html("");
            $("#WrittenTableBox").append("<table id=\"WrittenTable\"></table>"); //再新增一个grid的渲染容器
            // 考试通过与免试名单 tabs 不展示 是否通过与是否免试table列
            let isHidden = writtenStatusInput.val() !== '1'

            // 表格渲染
            writtenTable = new $.trasenTable("WrittenTable", {
              url: common.url + API.getRecruitSignUpExamList,
              pager: "WrittenTablePage",
              shrinkToFit: true,
              multiselect: true,
              ajaxGridOptions: {
                contentType: "application/json; charset=utf-8"
              },
              postData: JSON.stringify({
                planId: opt.data.id,
                postId: postId || postTreeDom.val(),
                examStatus: examStatus || writtenStatusInput.val()
              }),
              // sortname: 'approval_status ASC,t1.change_start_date', //设置默认的排序列
              // sortorder: 'DESC',
              colModel: [
                {
                  label: "姓名", name: "name", sortable: false, align: "center", width: 80, fixed: true,
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "身份证号码", name: "identityCard", sortable: false, align: "center"},
                {label: "性别", name: "genderLable", sortable: false, align: "center", width: 80, fixed: true},
                {label: "学历", name: "educationLable", sortable: false, align: "center", width: 80, fixed: true},
                {label: "考生职称", name: "professionalName", sortable: false, align: "center"},
                {label: "考试地点", name: "examAddr", sortable: false, align: "center"},
                {label: "座位号", name: "seatNo", sortable: false, align: "center"},
                {label: "考试得分", name: "score", sortable: false, align: "center"},
                {
                  label: "是否通过",
                  name: "statusLable",
                  sortable: false,
                  align: "center",
                  width: 80,
                  fixed: true,
                  hidden: isHidden
                },
                {
                  label: "是否免试",
                  name: "exemptionStatusLable",
                  sortable: false,
                  align: "center",
                  width: 80,
                  fixed: true,
                  hidden: isHidden
                }
              ],
              onSelectAll: function (rowid, status, item, test) {
                if (status) {
                  $.extend(true, freeExemptionList, rowid);
                } else {
                  freeExemptionList = [];
                }
              },
              onSelectRow: function (rowid, status) {
                if (status) {
                  freeExemptionList.push(rowid);
                } else {
                  freeExemptionList.splice(freeExemptionList.indexOf(rowid), 1);
                }
              },
              buidQueryParams: function () {
                let data = {};
                let all = $("#WrittenAllForm").serializeArray();
                let allMore = $("#WrittenFormMore").serializeArray();
                let pass = $("#WrittenPassForm").serializeArray();
                let statusValue = writtenStatusInput.val();
                switch (statusValue) {
                  case "2":
                    for (var i in pass) {
                      data[pass[i].name] = pass[i].value;
                    }
                    break;
                  default:
                    for (var i in all) {
                      filterDateValue(all[i]);
                      allMore.push(all[i]);
                    }
                    for (var i in allMore) {
                      data[allMore[i].name] = allMore[i].value;
                    }
                    break;
                }
                return data;
              }
            });

            // 笔试table刷新
            function writtenTableRefreshTable () {
              writtenTable.refresh();
            }

            // 笔试查询
            form.on("submit(WrittenFormSearch)", function () {
              writtenTableRefreshTable();
            });

            // 笔试查询
            $("#recruitManagePlanDetailDialog .written #WrittenFormMoreSearch").funs("click", function () {
              writtenTableRefreshTable();
            });

            form.on("submit(WrittenFormReset)", function () {
              resetWrittenFormHandle()
            });

            $("#recruitManagePlanDetailDialog .written #WrittenFormMoreReset").funs("click", function () {
              resetWrittenFormHandle()
            });

            // 笔试重置
            function resetWrittenFormHandle () {
              $("#WrittenAllForm")[0].reset();
              $("#WrittenFormMore")[0].reset();
              $("#WrittenPassForm")[0].reset();
              educationAll.setSelData([]);
              $('#recruitManagePlanDetailDialog #WrittenAllForm [name="educationTypeList"]').val(null)
              $('#recruitManagePlanDetailDialog #SecondEducationTypeListBox').attr('placeholder', '请选择学历')
              form.render();
              writtenTableRefreshTable();
            }
          }

          // 笔试 免试
          $("#recruitManagePlanDetailDialog .written").off("click", "#ExemptionBtn").on("click", "#ExemptionBtn", function () {
            if (!freeExemptionList.length) {
              layer.msg("请选择免试人员");
              return false;
            }

            layer.confirm("确定将所选人员放入免试名单吗？", {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0
            }, function () {
              $.ajax({
                type: "post",
                url: common.url + API.recruitSignUpExamExemption,
                async: false,
                data: JSON.stringify(freeExemptionList),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                  if (res.success && res.statusCode === 200) {
                    freeExemptionList = [];

                    layer.msg("操作成功!");
                    renderWrittenTableHandle();
                    renderWrittenPostTotalHandle();
                    getPlanStatusHandle();
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message);
                }
              });
            });
          });

          // 笔试 导出名单
          $("#recruitManagePlanDetailDialog .written").off("click", "#ExportListBtn").on("click", "#ExportListBtn", function () {
            let data = {};
            let all = $("#WrittenAllForm").serializeArray();
            let allMore = $("#WrittenFormMore").serializeArray();
            let pass = $("#WrittenPassForm").serializeArray();
            let statusValue = writtenStatusInput.val();
            switch (statusValue) {
              case "2":
                for (var i in pass) {
                  data[pass[i].name] = pass[i].value;
                }
                break;
              default:
                for (var i in all) {
                  filterDateValue(all[i]);
                  allMore.push(all[i]);
                }
                for (var i in allMore) {
                  data[allMore[i].name] = allMore[i].value;
                }
                break;
            }
            data.postId = postTreeDom.val();
            data.planId = opt.data.id;
            data.examStatus = statusValue
            // data
            let xhr = new XMLHttpRequest();
            let url = common.url + API.downloadImportExam;
            xhr.open("post", url, true);
            xhr.responseType = "blob";
            xhr.setRequestHeader("Content-Type", "application/json");
            xhr.send(JSON.stringify(data));
            xhr.onload = function () {
              if (this.status === 200) {
                let url = window.URL.createObjectURL(new Blob([this.response]));
                let link = document.createElement("a");
                link.style.display = "none";
                link.href = url;
                link.setAttribute("download", "笔试名单.xlsx");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link); //下载完成移除元素
                window.URL.revokeObjectURL(url); //释放掉blob对象
              }
            };
          });

          // 笔试 导入准考证信息
          $("#recruitManagePlanDetailDialog .written").off("click", "#ImportInfomationBtn").on("click", "#ImportInfomationBtn", function () {
            $.quoteFun("/recruitManage/plan/detailDialog/ImportInfomation", {
              data: {
                postId: postTreeDom.val()
              },
              ref: () => {
                renderWrittenTableHandle();
                planPostData = getPlanMessageHandle(opt.data.id);
                renderPostTree();
                renderWrittenPostTotalHandle();
              },
              title: "导入准考信息"
            });
          });

          // 笔试 导入成绩
          $("#recruitManagePlanDetailDialog .written").off("click", "#ImportGradesBtn").on("click", "#ImportGradesBtn", function () {
            $.quoteFun("/recruitManage/plan/detailDialog/ImporTachievement", {
              data: {
                postId: postTreeDom.val()
              },
              ref: () => {
                renderWrittenTableHandle();
                renderWrittenPostTotalHandle();
                getPlanStatusHandle();
              },
              title: "导入成绩"
            });
          });

          // 笔试 短信通知
          $("#recruitManagePlanDetailDialog .written").off("click", "#SMSNotificationBtn").on("click", "#SMSNotificationBtn", function () {
            if (!freeExemptionList.length) {
              layer.msg("请选择需要短信通知的数据");
              return false;
            }

            $.quoteFun("/recruitManage/plan/detailDialog/sendMessage", {
              title: "短信通知",
              data: {
                signupIdList: freeExemptionList
              },
              ref: () => {
                freeExemptionList = [];
              }
            });
          });

          // 面试 岗位状态的点击事件
          $("#recruitManagePlanDetailDialog .interview .oa-nav").off("click", ".oa-nav_item").on("click", ".oa-nav_item", function () {
            freeExemptionList = [];

            $(this).addClass("active").siblings().removeClass("active");
            const value = $(this).data("value");
            interviewStatusInput.val(value);

            $("#interviewForm")[0].reset();
            $("#interviewFormMore")[0].reset();
            renderInterviewTableHandle();
          });

          // 岗位面试的状态统计
          function renderInterviewPostTotalHandle (postId) {
            $.ajax({
              type: "post",
              url: common.url + API.signUpInterviewCount,
              async: false,
              data: JSON.stringify({
                postId: postId || postTreeDom.val(),
                planId: opt.data.id
              }),
              contentType: "application/json;charset=UTF-8",
              success: function (res) {
                if (res.success && res.statusCode === 200) {
                  const postActiveData = postList.find(item => item.id === postTreeDom.val());
                  const {interviewBeginTime, interviewEndTime, interviewContent} = postActiveData;
                  let interviewTips = $("#recruitManagePlanDetailDialog .interview .examination-status-tips");

                  if (interviewBeginTime && interviewEndTime) {
                    interviewTips.text(`考试时间：${interviewBeginTime || ""}至${interviewEndTime || ""} 考试内容：${interviewContent || ""}`);
                  } else {
                    interviewTips.text(``);
                  }

                  const nav = $("#recruitManagePlanDetailDialog .interview .oa-nav_item");
                  let interviewAll = res.object.all > 0 ? `参与人员(${res.object.all})` : "参与人员";
                  let interviewPass = res.object.pass > 0 ? `考试通过(${res.object.pass})` : "考试通过";
                  nav.eq(0).text(interviewAll);
                  nav.eq(1).text(interviewPass);
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res.message);
              }
            });
          }

          // 面试列表
          function renderInterviewTableHandle (postId, interviewStatus) {
            $("#interviewTable").remove();
            $("#interviewTablePage").html("");
            $("#interviewTableBox").append("<table id=\"interviewTable\"></table>"); //再新增一个grid的渲染容器
            let isHidden = interviewStatusInput.val() === '2'

            // 表格渲染
            interviewTable = new $.trasenTable("interviewTable", {
              url: common.url + API.getRecruitSignUpInterview,
              pager: "interviewTablePage",
              shrinkToFit: true,
              multiselect: true,
              postData: {
                planId: opt.data.id,
                postId: postId || postTreeDom.val(),
                interviewStatus: interviewStatus || interviewStatusInput.val()
              },
              // sortname: 'approval_status ASC,t1.change_start_date', //设置默认的排序列
              // sortorder: 'DESC',
              colModel: [
                {
                  label: "姓名", name: "name", sortable: false, align: "center", width: 80, fixed: true,
                  formatter: function (cellvalue, options, row) {
                    return `<span signUpId="${row.id}" style="color: #5260ff;cursor:pointer;" id="viewSignUpInfoBtn">${row.name}</span>`;
                  }
                },
                {label: "身份证号码", name: "identityCard", sortable: false, align: "center"},
                {label: "性别", name: "genderLable", sortable: false, align: "center", width: 80, fixed: true},
                {label: "学历", name: "educationLable", sortable: false, align: "center", width: 80, fixed: true},
                {label: "考生职称", name: "professionalName", sortable: false, align: "center"},
                {label: "考试地点", name: "interviewAddr", sortable: false, align: "center"},
                {label: "顺序", name: "seatNo", sortable: false, align: "center"},
                {label: "考试得分", name: "score", sortable: false, align: "center"},
                {
                  label: "是否通过",
                  name: "statusLable",
                  sortable: false,
                  align: "center",
                  width: 80,
                  fixed: true,
                  hidden: isHidden
                }
              ],
              onSelectAll: function (rowid, status, item, test) {
                if (status) {
                  $.extend(true, freeExemptionList, rowid);
                } else {
                  freeExemptionList = [];
                }
              },
              onSelectRow: function (rowid, status) {
                if (status) {
                  freeExemptionList.push(rowid);
                } else {
                  freeExemptionList.splice(freeExemptionList.indexOf(rowid), 1);
                }
              },
              buidQueryParams: function () {
                let data = {};
                let all = $("#interviewForm").serializeArray();
                let allMore = $("#interviewFormMore").serializeArray();
                for (var i in all) {
                  allMore.push(all[i]);
                }
                for (var i in allMore) {
                  data[allMore[i].name] = allMore[i].value;
                }
                return data;
              }
            });
          }

          // 面试table刷新
          function interviewTableRefresh () {
            interviewTable.refresh();
          }

          // 面试查询
          form.on("submit(interviewFormSubmit)", function () {
            interviewTableRefresh();
          });

          // 面试查询
          $("#recruitManagePlanDetailDialog .interview #interviewFormMoreSearch").funs("click", function () {
            interviewTableRefresh();
          });

          // 面试重置
          $("#recruitManagePlanDetailDialog .interview #interviewFormMoreReset").funs("click", function () {
            interviewFormResetHandle()
          });

          // 面试重置
          form.on("submit(interviewFormReset)", function () {
            interviewFormResetHandle()
          });

          function interviewFormResetHandle () {
            $("#interviewForm")[0].reset();
            $("#interviewFormMore")[0].reset();
            form.render();
            interviewTableRefresh();
          }

          // 面试 导出名单
          $("#recruitManagePlanDetailDialog .interview").off("click", "#interviewOutList").on("click", "#interviewOutList", function () {
            let data = {};
            let all = $("#interviewForm").serializeArray();
            for (var i in all) {
              data[all[i].name] = all[i].value;
            }

            data.postId = postTreeDom.val();
            data.planId = opt.data.id;
            data.interviewStatus = interviewStatusInput.val()

            let xhr = new XMLHttpRequest();
            let url = common.url + API.exportInterviewList;
            xhr.open("post", url, true);
            xhr.responseType = "blob";
            xhr.setRequestHeader("Content-Type", "application/json");
            xhr.send(JSON.stringify(data));
            xhr.onload = function () {
              if (this.status === 200) {
                let url = window.URL.createObjectURL(new Blob([this.response]));
                let link = document.createElement("a");
                link.style.display = "none";
                link.href = url;
                link.setAttribute("download", "面试名单.xlsx");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link); //下载完成移除元素
                window.URL.revokeObjectURL(url); //释放掉blob对象
              }
            };
          });

          // 面试 导入准考证信息
          $("#recruitManagePlanDetailDialog .interview").off("click", "#interviewEnterInfo").on("click", "#interviewEnterInfo", function () {
            $.quoteFun("/recruitManage/plan/detailDialog/interviewImportInfomation", {
              data: {
                postId: postTreeDom.val()
              },
              ref: () => {
                planPostData = getPlanMessageHandle(opt.data.id);
                renderPostTree();
                renderInterviewPostTotalHandle();
                renderInterviewTableHandle();
              },
              title: "导入准考信息"
            });
          });

          // 面试 导入成绩
          $("#recruitManagePlanDetailDialog .interview").off("click", "#interviewEnterFraction").on("click", "#interviewEnterFraction", function () {
            $.quoteFun("/recruitManage/plan/detailDialog/interviewImporTachievement", {
              data: {
                postId: postTreeDom.val()
              },
              ref: () => {
                renderInterviewPostTotalHandle();
                renderInterviewTableHandle();
              },
              title: "导入成绩"
            });
          });

          // 面试 短信通知
          $("#recruitManagePlanDetailDialog .interview").off("click", "#interviewMessage").on("click", "#interviewMessage", function () {
            if (!freeExemptionList.length) {
              layer.msg("请选择需要短信通知的数据");
              return false;
            }

            $.quoteFun("/recruitManage/plan/detailDialog/sendMessage", {
              title: "短信通知",
              data: {
                signupIdList: freeExemptionList
              },
              ref: () => {
                freeExemptionList = [];
              }
            });
          });

          function initActiveContentHandle (ACTIVE) {
            if (ACTIVE === 0) {
              $("#recruitManagePlanDetailDialog .oa-search-tree").hide();
            } else {
              $("#recruitManagePlanDetailDialog .oa-search-tree").show();
            }

            const spanItem = $("#recruitManagePlanDetailDialog .recruit-bottom .recruit-progress");
            spanItem.eq(ACTIVE).addClass("active").siblings().removeClass("active");

            const clickItem = $("#recruitManagePlanDetailDialog .recruit-content").children().eq(ACTIVE);
            clickItem.show().siblings().hide();
          }


          function filterDateValue (data) {
            if (data.name === "educationTypeList") {
              data.value = data.value ? data.value.split(',') : null;
            }
          }

          $("#recruitManagePlanDetailDialog").off("click", "#viewSignUpInfoBtn").on("click", "#viewSignUpInfoBtn", function () {
            const id = $(this).attr('signupid')
            templateForm[templateName + 'Info']({
              data: {
                id
              }
            });
          })
        }
      );
    };
  }
);

//   $('#domTable').setGridParam({
//     postData: {
//       planId: opt.data.id,
//       postId: postTreeDom.val(),
//       examStatus: $('#recruitManagePlanDetailDialog .written #WrittenPostStatusValue').val()
//     }
//   }).trigger('reloadGrid')

// function initPostStatusHandel () {
//   // 详情进度切换 tree默认选择第一个
//   $('#recruitManagePlanDetailDialog #RecruitManagePlanPostBox').children().removeClass('active').eq(0).addClass('active')
//   postTreeDom.val(postList[0].id)
//
//   // 修改审查状态的值
//   $('#recruitManagePlanDetailDialog .examination #RecruitManagePostStatusValue').val('0')
//   $('#recruitManagePlanDetailDialog .examination .oa-nav_item').removeClass('active').eq(0).addClass('active')
//
//   // 修改笔试状态的值
//   $('#recruitManagePlanDetailDialog .written #WrittenPostStatusValue').val('1')
//   $('#recruitManagePlanDetailDialog .written .oa-nav_item').removeClass('active').eq(0).addClass('active')
//
//   // 修改面试状态的值
//   $('#recruitManagePlanDetailDialog .interview #interviewPostStatusValue').val('0')
//   $('#recruitManagePlanDetailDialog .interview .oa-nav_item').removeClass('active').eq(0).addClass('active')
// }
