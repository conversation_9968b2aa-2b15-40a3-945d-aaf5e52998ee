<style>
    #personnelHomePage {
        height: 100%;
        width: 100%;
        background-color: #efeff4;
    }

    #personnelHomePage .hidden {
        display: none;
    }

    #personnelHomePage .os-host:hover.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
        background: rgba(0, 0, 0, 0.2) !important;
    }

    #personnelHomePage .os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
        background: transparent !important;
    }

    #personnelHomePage .noData {
        background-repeat: no-repeat;
        background-position: center;
        background-image: url('/static/img/other/empty.png');
    }

    #personnelHomePage .noData:before {
        content: '暂 无 数 据';
        position: absolute;
        bottom: 20%;
        left: 0;
        right: 0;
        text-align: center;
        color: #333;
        opacity: 0.25;
    }

    #personnelHomePage .schedule-noData {
        background-repeat: no-repeat;
        background-position: center;
        background-image: url('/static/img/other/empty.png');
    }

    #personnelHomePage .schedule-noData:before {
        content: '本 周 暂 无 排 班 数 据';
        position: absolute;
        bottom: 20%;
        left: 0;
        right: 0;
        text-align: center;
        color: #333;
        opacity: 0.25;
    }

    #personnelHomePage .os-content {
        display: flex;
    }

    #personnelHomePage * {
        box-sizing: border-box !important;
    }

    #personnelHomePage .heard-header {
        height: 40px;
        padding: 0 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
    }

    #personnelHomePage .heard-header.no-box-shadow {
        box-shadow: none;
    }

    #personnelHomePage .heard-header .heard-nav {
        display: flex;
        align-items: center;
    }

    #personnelHomePage .heard-header .heard-nav .sign {
        width: 4px;
        height: 18px;
        display: inline-block;
        background: #5260FF;
        margin: 2px 8px 0 0;
        border-radius: 2px;
    }

    #personnelHomePage .heard-header .heard-header p {
        font-size: 14px;
        font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
        font-weight: bold;
        color: #333333;
        line-height: 19px;
        letter-spacing: 6px;
    }

    #personnelHomePage .heard-header .heard-operation {
        display: flex;
    }

    .personnel-home-page-left {
        min-width: 860px;
        width: 72.5%;
        height: 100%;
        margin-right: 8px;
    }

    .personnel-home-page-left .early-warning {
        padding: 0px 16px 8px;
        background-color: #fff;
        position: relative;
    }


    .personnel-home-page-left .direction {
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid #E4E4E4;
    }

    .personnel-home-page-left .left {
        position: absolute;
        left: 4px;
        top: 50%;
        transform: translateY(-50%) rotate(-90deg);
    }

    .personnel-home-page-left .right {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%) rotate(90deg);
    }

    .personnel-home-page-left .early-warning .early-warning-item-remove {
        overflow: hidden;
    }

    .personnel-home-page-left .early-warning .early-warning-item-remove .early-warning-item {
        display: flex;
        transform: translateX(0);
        transition: all 0.3s;
        cursor: pointer;
    }


    .early-warning-item li {
        display: flex;
        width: 16.66%;
        min-width: 138px;
    }

    .early-warning-item li .img-box {
        width: 50px;
        height: 50px;
        background: rgba(82, 96, 255, 0.08);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
    }

    .early-warning-item li .img-box img {
        width: 24px;
        height: 24px;
    }

    .early-warning-item li .font-box p {
        width: 80px;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        cursor: pointer;
    }

    .early-warning-item li .font-box .font-num {
        font-size: 26px;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #4b7aff;
        line-height: 30px;
    }

    .early-warning-item li .font-box .font-company {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: rgba(51, 51, 51, 0.7);
        padding-left: 8px;
    }

    .personnel-home-page-left .left-second-box {
        display: flex;
        width: 100%;
        height: 218px;
        margin: 8px 0;
    }

    .personnel-home-page-left .left-second-box .latest-training {
        width: 37.3%;
        min-width: 322px;
        height: 100%;
        margin-right: 8px;
        background-color: #fff;
        position: relative;
    }

    .personnel-home-page-left .left-second-box .latest-training .latest-training-content {
        width: 100%;
        height: calc(100% - 40px);
        overflow: hidden;
    }

    .left-second-box .latest-training .latest-training-content .latest-training-ul-box {
        width: 100%;
    }
    .left-second-box .latest-training .latest-training-content .latest-training-ul-box li{
        cursor: pointer;
    }

    .left-second-box .latest-training .latest-training-content .latest-training-item-li {
        width: 100%;
        height: 35px;
        padding: 0 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
    }

    .left-second-box .latest-training .latest-training-item-li .li-titlt {
        flex: 1 0;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .left-second-box .latest-training .latest-training-item-li .date {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: rgba(0, 0, 0, 0.3);
        line-height: 16px;
        padding-left: 8px;
    }

    .personnel-home-page-left .left-second-box .second-right-box {
        width: 62.6%;
        min-width: 530px;
        height: 100%;
        background-color: #fff;
    }

    .left-second-box .second-right-box .survey {
        padding: 8px;
    }

    .left-second-box .second-right-box .survey .background {
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E7EBF0;
    }

    .left-second-box .second-right-box .survey p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
    }

    .left-second-box .second-right-box .survey span {
        font-size: 24px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4b7aff;
        line-height: 30px;
    }

    .left-second-box .second-right-box .survey .top-item-box {
        display: flex;
        margin-bottom: 8px;
    }

    .left-second-box .second-right-box .survey .top-item-box .chief-employee {
        flex: 1;
        min-width: 144px;
        display: flex;
        padding: 7px 16px;
        margin-right: 8px;
    }

    .survey .top-item-box .chief-employee img {
        width: 54px;
        height: 54px;
        margin-right: 12px;
    }

    .survey .top-item-box .chief-employee .employee-box {
        width: 54px;
        height: 54px;
    }

    .survey .top-item-box .post-type .item {
        width: 25%;
        text-align: center;
    }

    .survey .bottom-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .survey .bottom-box .bottom-item {
        width: 24%;
        padding: 3px 8px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .survey .bottom-box .bottom-item .tips {
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: #ccc;
        width: 15px;
        height: 15px;
        border-radius: 50%;
    }

    .survey .bottom-box .bottom-item .tips:hover {
        background: #5260FF;
        cursor: pointer;
    }

    .survey .bottom-box .bottom-item:nth-child(-n + 4) {
        margin-bottom: 8px;
    }

    .left-second-box .second-right-box .survey .top-item-box .post-type {
        flex: 3;
        display: flex;
        min-width: 346px;
        padding: 8px 0px;
    }

    .personnel-home-page-left .left-three-box {
        width: 100%;
        height: 218px;
        display: flex;
    }

    /* 志愿者服务 start */
    .personnel-home-page-left .left-three-box .volunteer-service-box {
        width: 37.3%;
        min-width: 322px;
        height: 100%;
        position: relative;
        margin: 0 8px 8px 0;
        background-color: #fff;
    }

    .personnel-home-page-left .left-three-box .volunteer-service-box .total-duration {
        position: absolute;
        left: 5%;
        top: 23%;
        color: #333333;
        line-height: 20px;
    }

    .personnel-home-page-left .left-three-box .volunteer-service-box #VolunteerServiceBox {
        width: 100%;
        height: calc(100% - 40px);
    }

    /* 志愿者服务 end */


    .personnel-home-page-left .left-three-box .fixed-posts-and-staffing {
        width: 62.6%;
        height: 100%;
        min-width: 530px;
        background-color: #fff;
    }

    .personnel-home-page-left .left-three-box .fixed-posts-and-staffing #FixedPostsAndStaffing {
        height: calc(100% - 40px);
    }

    .personnel-home-page-left .left-four-box {
        width: 100%;
        height: 94px;
        margin: 8px 0;
        background-color: #fff;
    }

    .personnel-home-page-left .left-four-box .recruit-box {
        padding: 0 8px;
        margin-top: 8px;
    }

    .personnel-home-page-left .left-four-box .recruit-top {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: #EC7B25;
        line-height: 16px;
        display: flex;
        align-items: center;
    }

    .personnel-home-page-left .left-four-box .recruit-top img {
        width: 12px;
        height: 12px;
        margin: 0 8px 0 20px;
    }

    .personnel-home-page-left .left-four-box .recruit-box .recruit-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .personnel-home-page-left .left-four-box .recruit-box .recruit-bottom span {
        width: 106px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E7EBF0;
    }

    .personnel-home-page-left .left-four-box .recruit-box .recruit-bottom img {
        width: 30px;
        height: 12px;
    }

    .personnel-home-page-left .left-five-box {
        width: 100%;
        height: 210px;
        background-color: #fff;
    }

    .personnel-home-page-left .left-five-box .time-card-ul {
        display: flex;
    }

    .personnel-home-page-left .left-five-box .time-card-ul li {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        cursor: pointer;
    }

    .personnel-home-page-left .left-five-box .time-card-ul li:not(:first-child)::before {
        content: '|';
        padding: 0 5px;
        color: #666666;
    }

    .personnel-home-page-left .left-five-box .time-card-ul li.active {
        color: #5260FF;
    }

    .personnel-home-page-left .left-five-box #CheckWorkAttendance {
        height: calc(100% - 40px);
    }

    .personnel-home-age-right {
        min-width: 322px;
        width: 37.5%;
        height: 100%;
    }

    /* 提醒事项 start */
    /* .personnel-home-age-right .reminders-box {
      width: 100%;
      height: 162px;
      padding: 8px;
      background: #fff;
    }

    .personnel-home-age-right .reminders-box .heard-header {
      height: 19px !important;
      margin-bottom: 8px;
    }

    .personnel-home-age-right .reminders-box .reminders {
      width: 100%;
      height: 120px;
      box-sizing: border-box;
      padding: 8px;
      background: rgba(82, 96, 255, 0.12);
      display: flex;
      flex-wrap: wrap;
    }

    .personnel-home-age-right .reminders-box .reminders .reminder {
      width: 20%;
      position: relative;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .personnel-home-age-right .reminders .reminder:nth-child(-n+5) {
      margin-bottom: 12px;
    }

    .personnel-home-age-right .reminders .reminder .reminder-item-title {
      padding-top: 4px;
      font-size: 10px;
    }

    .personnel-home-age-right .reminders .reminderSign {
      width: 8px;
      height: 8px;
      background: #E24242;
      border-radius: 50%;
      position: absolute;
      top: 0;
      right: 10%;
      display: none;
    } */
    /* 提醒事项 end */

    /* 我的排班 start */
    .personnel-home-age-right .my-schedule-box {
        width: 100%;
        height: 324px;
        background-color: #fff;
    }

    .personnel-home-age-right .my-schedule-box .icon-date-i {
        font-size: 16px;
        margin: 0 16px;
        font-weight: 400;
        color: #5260FF;
        margin-top: 2px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-font {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-operation {
        display: flex;
        align-items: center;
        margin-left: 8px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-operation i {
        font-size: 24px;
        color: #CCCCCC;
        cursor: pointer;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-operation #ScheduleThisWeek {
        font-size: 14px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #333;
        line-height: 20px;
        margin: 0 8px;
        cursor: pointer;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-operation #ScheduleThisWeek.active {
        color: #5260FF;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content {
        display: flex;
        flex-direction: column;
        padding: 0 8px;
        height: calc(100% - 80px);
        position: relative;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li {
        height: 35px;
        display: flex;
        align-items: center;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .weekChinese {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: rgba(51, 51, 51, 0.3);
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .date {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        margin: 0 10px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .schedule-p-class {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        cursor: pointer;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .schedule-name {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 16px;
        margin: 0 16px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .schedule-time {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: rgba(51, 51, 51, 0.7);
        line-height: 16px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .no-data {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: rgba(51, 51, 51, 0.7);
        line-height: 16px;
        margin-left: 16px;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-content li .date.past-tense {
        color: rgba(51, 51, 51, 0.3);
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-bottom-info {
        display: flex;
        height: 40px;
        align-items: center;
        justify-content: space-around;
    }

    .personnel-home-age-right .my-schedule-box .my-schedule-bottom-info span {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
    }

    /* 我的排班 end */

    /* 常用流程 start */
    .personnel-home-age-right .common-processes {
        width: 100%;
        height: 320px;
        margin: 8px 0;
        background-color: #fff;
        position: relative;
    }

    .personnel-home-age-right .common-processes .scrollbar-box {
        width: 100%;
        height: calc(100% - 83px);
    }

    .personnel-home-age-right .common-processes .scrollbar-box .common-process,
    .personnel-home-age-right .common-processes .scrollbar-box .waitDeal {
        width: 100%;
    }

    .personnel-home-age-right .common-processes .processHeader {
        display: flex;
        height: 56px;
    }

    .personnel-home-age-right .common-processes .processHeader li {
        width: 20%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 17px;
        padding: 8px 0;
        cursor: pointer;
        position: relative;
        color: #999;
    }

    .personnel-home-age-right .common-processes .processHeader li .processnum {
        position: absolute;
        top: 5px;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        padding: 0px 4px;
        border-radius: 10px;
        color: #fff !important;
        background-color: red;
    }

    .personnel-home-age-right .common-processes .processHeader li i {
        font-size: 24px;
        color: #5260FF;
        opacity: 0.4;
    }

    .personnel-home-age-right .common-processes .processHeader li.active {
        color: #5260FF !important;
        opacity: 1;
    }

    .personnel-home-age-right .common-processes .processHeader li.active i {
        color: #5260FF !important;
        opacity: 1;
    }

    .personnel-home-age-right .common-processes .processHeader {
        display: flex;
    }


    .personnel-home-age-right .common-processes .common-process > div {
        box-sizing: border-box;
        font-size: 14px;
        color: #333;
        height: 35px;
        line-height: 35px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        cursor: pointer;
        user-select: none;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
    }

    .personnel-home-age-right .common-processes .common-process > div i {
        font-size: 14px;
        margin-right: 8px;
    }

    .personnel-home-age-right .common-processes .common-process > div:hover {
        background: rgba(205, 112, 112, 0.12);
    }

    .personnel-home-age-right .common-processes .waitDeal .box:hover {
        background: rgba(205, 112, 112, 0.12);
    }

    .personnel-home-age-right .common-processes .waitDeal .box:hover .first-title {
        color: #B875F8;
    }

    .personnel-home-age-right .common-processes .waitDeal .waitDeal-item {
        height: 48px;
        padding: 0 8px;
        cursor: pointer;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
    }

    .personnel-home-age-right .common-processes .waitDeal .waitDeal-item .first-title {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .personnel-home-age-right .common-processes .waitDeal .waitDeal-item .first-title .second-title {
        display: flex;
        font-weight: normal;
        font-size: 12px;
        color: #999;
        line-height: 20px;
    }

    .personnel-home-age-right .common-processes .waitDeal .waitDeal-item .first-title .type-name-style {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
    }

    .personnel-home-age-right .common-processes .waitDeal .deal {
        padding-left: 10px;
        color: #5260ff;
        position: relative;
        font-size: 14px;
        line-height: 44px;
    }

    .personnel-home-age-right .common-processes .waitDeal .deal:after {
        position: absolute;
        content: '';
        left: 0;
        top: 50%;
        margin-top: -3px;
        height: 6px;
        width: 6px;
        border-radius: 50%;
        background-color: #5260ff;
    }

    .personnel-home-age-right .common-processes .waitDeal .start {
        padding-left: 10px;
        color: #f93a4a;
        font-size: 14px;
        line-height: 44px;
        position: relative;
    }

    .personnel-home-age-right .common-processes .waitDeal .start:after {
        position: absolute;
        content: '';
        left: 0;
        top: 50%;
        margin-top: -3px;
        height: 6px;
        width: 6px;
        border-radius: 50%;
        background-color: #f93a4a;
    }

    .personnel-home-age-right .common-processes #TabsMorePage {
        background: #FAFAFA;
        width: 100%;
        height: 24px;
        position: absolute;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #5260FF;
        line-height: 16px;
        cursor: pointer;
    }

    /* 常用流程 end */

    /* 常用入口 start */
    .personnel-home-age-right .common-entrance {
        width: 100%;
        height: 210px;
        background-color: #fff;
        position: relative;
    }

    .personnel-home-age-right .common-entrance .common-entrance-content {
        overflow: hidden;
        height: calc(100% - 40px);
    }

    .personnel-home-age-right .common-entrance .common-entrance-content .common-entrance-page {
        display: flex;
        position: relative;
        height: calc(100% - 40px);
    }

    .common-entrance .common-entrance-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
    }

    .common-entrance .common-entrance-footer .footer-operate {
        width: 35px;
        height: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .common-entrance .operate_prev_page,
    .common-entrance .control_next_page {
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 12px solid #E4E4E4;
        transform: rotate(90deg);
    }

    .common-entrance .operate_prev_page {
        transform: rotate(-90deg);
    }

    .common-entrance .access-list-item {
        display: flex;
        flex-wrap: wrap;
    }

    .common-entrance .access-list-item .item-icon-Jump {
        height: 65px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .common-entrance .access-list-item .item-icon-Jump a {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .one-line {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    /* 常用入口 end */
</style>

<div class="scrollbar-box-y" id="personnelHomePage">
  <div class="personnel-home-page-left">
    <!-- 人事预警 -->
    <div class="early-warning">
      <div class="heard-header no-box-shadow">
        <div class="heard-nav"><i class="sign"></i>
          <p>人事预警</p>
        </div>
      </div>
      <div class="early-warning-item-remove">
        <ul class="early-warning-item">
        </ul>
      </div>
      <a href="javascript:;" class="direction left hidden" id="EarlyWarningLeftMore"></a>
      <a href="javascript:;" class="direction right hidden" id="EarlyWarningRightMore"></a>
    </div>
    <!-- second -->
    <div class="left-second-box">
      <!-- 最新培训 -->
      <div class="latest-training">
        <div class="heard-header">
          <div class="heard-nav"><i class="sign"></i>
            <p>最新培训</p>
          </div>
          <div class="heard-operation">
            <a href="#/train/plan" class="document-nav-more" _target="self">
              <i class="oaicon oa-icon-icon_shouye_gengduo" style="font-size: 20px;color: #999"></i>
            </a>
          </div>
        </div>
        <div class="scrollbar-box latest-training-content">
          <div class="latest-training-ul-box">
          </div>
        </div>
      </div>
      <!-- 职工概况 -->
      <div class="second-right-box">
        <div class="heard-header">
          <div class="heard-nav"><i class="sign"></i>
            <p>职工概况</p>
          </div>
        </div>
        <div class="survey" id="SurveyBox">
          <div class="top-item-box">
            <div class="chief-employee background">
              <img src="/static/img/personnelhomePage/chief-employee.png" alt="">
              <div class="employee-box">
                <p>总员工</p>
                <span id="ZRS"></span>
              </div>
            </div>
            <div class="post-type background">
              <div class="item">
                <span id="BN"></span>
                <p>在编</p>
              </div>
              <div class="item">
                <span id="HTZ"></span>
                <p>合同制</p>
              </div>
              <div class="item">
                <span id="TGTC"></span>
                <p>同工同酬</p>
              </div>
              <div class="item">
                <span id="LWPQ"></span>
                <p>劳务派遣</p>
              </div>
            </div>
          </div>
          <div class="bottom-box">
            <div class="bottom-item background" data-search="员工入职" title="本年度新入职员工数量">
              <p>入职</p>
              <span id="RZ"></span>
            </div>
            <div class="bottom-item background" data-search="离职" title="本年度离职员工数量">
              <p>离职</p>
              <span id="LZ"></span>
            </div>
            <div class="bottom-item background" data-search="返聘" title="本年度返聘员工数量">
              <p>返聘</p>
              <span id="FP"></span>
            </div>
            <div class="bottom-item background" data-search="退休" title="本年度退休员工数量">
              <p>退休</p>
              <span id="TX"></span>
            </div>
            <div class="bottom-item background" data-search="人员调动" title="本年度调动员工数量">
              <p>调动</p>
              <span id="DD"></span>
            </div>
            <div class="bottom-item background" data-search="升迁" title="本年度升迁员工数量">
              <p>升迁</p>
              <span id="SQ"></span>
            </div>
            <div class="bottom-item background" data-search="降级" title="本年度降职员工数量">
              <p>降职</p>
              <span id="JJ"></span>
            </div>
            <div class="bottom-item background" data-search="下乡" title="本年度下乡员工数量">
              <p>下乡</p>
              <span id="XX"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- three -->
    <div class="left-three-box">
      <!-- 志愿者服务 -->
      <div class="volunteer-service-box">
        <div class="heard-header">
          <div class="heard-nav"><i class="sign"></i>
            <p>志愿者服务</p>
          </div>
        </div>
        <span class="total-duration">总时长：<span class="total-num"></span>小时</span>
        <div id="VolunteerServiceBox"></div>
      </div>
      <!-- 定岗定编 -->
      <div class="fixed-posts-and-staffing">
        <div class="heard-header">
          <div class="heard-nav"><i class="sign"></i>
            <p>定岗定编</p>
          </div>
        </div>
        <div id="FixedPostsAndStaffing">
        </div>
      </div>
    </div>
    <!-- four -->
    <div class="left-four-box">
      <div class="heard-header no-box-shadow">
        <div class="heard-nav"><i class="sign"></i>
          <p id="RecruitTips"></p>
          <!-- <div class="recruit-top">
            <img src="../../static/img/personnelhomePage/icon-data.png" alt="">
            <span>2021年10月1日至2021年10月30日</span>
          </div> -->
        </div>
        <div class="heard-operation">
          <a href="#/recruitManage/plan" class="document-nav-more" _target="self">
            <i class="oaicon oa-icon-icon_shouye_gengduo" style="font-size: 20px;color: #999"></i>
          </a>
        </div>
      </div>
      <div class="recruit-box">
        <div class="recruit-bottom">
          <span>招聘公告</span>
          <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
          <span class="recruit-count">审查（2239）</span>
          <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
          <span class="recruit-count">笔试（1395）</span>
          <img src="../../static/img/personnelhomePage/icon-next-step.png" alt="">
          <span class="recruit-count">笔试（1395）</span>
        </div>
      </div>
    </div>
    <!-- five -->
    <div class="left-five-box">
      <!-- 考勤情况 -->
      <div class="heard-header no-box-shadow">
        <div class="heard-nav"><i class="sign"></i>
          <p>考勤情况</p>
        </div>
        <div>
          <ul class="time-card-ul">
            <li type="2">上月</li>
            <li type="1" class="active">本月</li>
            <li type="3">本年</li>
          </ul>
        </div>
      </div>
      <div id="CheckWorkAttendance"></div>
    </div>
  </div>
  <div class="personnel-home-age-right">
    <!-- 提醒事项 -->
    <!-- <div class="reminders-box">
      <div class="heard-header no-box-shadow">
        <div class="heard-nav"><i class="sign"></i>
          <p>考勤情况</p>
        </div>
      </div>
      <ul class="reminders">
        <li class="reminder">
          <div class="reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/email.svg">
          <span class="reminder-item-title">我的邮件</span>
        </li>
        <li class="reminder" id="myAttendance">
          <div class="reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/kqgl.svg">
          <span class="reminder-item-title">我的考勤</span>
        </li>
        <li class="reminder" id="fileAudit">
          <div class="reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/dash.svg">
          <span class="reminder-item-title">档案审核</span>
        </li>
        <li class="reminder" id="mySalary">
          <div class="reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/gzt.svg">
          <span class="reminder-item-title">工资条</span>
        </li>
        <li class="reminder" id="myLinkMan">
          <div class="reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/txl.svg">
          <span class="reminder-item-title">通讯录</span>
        </li>
        <li class="reminder" id="myMorality">
          <div class=" reminderSign"></div>
          <img width="24" height="24" alt="" src="/static/img/other/icon_yideyifeng.svg">
          <span class="reminder-item-title">医德医风</span>
        </li>
        <li class="reminder" id="myStartCheck">
          <div class=" reminderSign">
          </div>
          <img width="24" height="24" alt="" src="/static/img/other/icon_anquanducha.svg">
          <span class="reminder-item-title">安全检查</span>
        </li>
        <li class="reminder" id="myWorkSheet">
          <div class=" reminderSign">
          </div>
          <img width="24" height="24" alt="" src="/static/img/other/wodegongdan.svg">
          <span class="reminder-item-title">我的工单</span>
        </li>
        <li class="reminder" id="myWarningRecord">
          <div class=" reminderSign">
          </div>
          <img width="24" height="24" alt="" src="/static/img/other/icon_renshishijian.svg">
          <span class="reminder-item-title">人事预警</span>
        </li>
      </ul>
    </div> -->
    <!-- 我的排班 -->
    <div class="my-schedule-box">
      <div class="heard-header">
        <div class="heard-nav"><i class="sign"></i>
          <p>我的排班</p>
          <a class="icon-date-i" href="#/workforceManagement/workforce">
            <i class="
            oaicon
            oa-icon-icon_richengxiangqing
           ">
            </i>
          </a>

          <span class="my-schedule-font" id="ScheduleYearHomePage"></span>
          <span class="my-schedule-font">年</span>
          <span class="my-schedule-font" id="ScheduleMonthHomePage"></span>
          <span class="my-schedule-font">月</span>

          <div class="my-schedule-operation">
            <i class="fa fa-angle-left" aria-hidden="true" id="my-schedule-prev-week"></i>
            <span id="ScheduleThisWeek">本周</span>
            <i class="fa fa-angle-right" aria-hidden="true" id="my-schedule-next-week"></i>
          </div>
        </div>
      </div>
      <ul class="my-schedule-content">
      </ul>
      <div class="my-schedule-bottom-info">
        <span>上月出勤：<span id="cq">0</span>天</span>
        <span>休假：<span id="xj">0</span>天</span>
        <span>假勤：<span id="jq">0</span>天</span>
      </div>
    </div>
    <!-- 常用流程 -->
    <div class="common-processes">
      <ul class="processHeader">
        <li class="active" data-refTable="1">
          <i class="oaicon oa-icon-daiban1"></i>
          <span>待办<span class="processnum wait"></span></span>
        </li>
        <li data-refTable="2">
          <i class="oaicon oa-icon-jingban"></i>
          <span>经办<span class="processnum has"></span></span>
        </li>
        <li data-refTable="3">
          <i class="oaicon oa-icon-zaiban"></i>
          <span>在办<span class="processnum mydeal"></span></span>
        </li>
        <li data-refTable="4">
          <i class="oaicon oa-icon-chaosong"></i>
          <span>抄送<span class="processnum copy"></span></span>
        </li>
        <li data-refTable="0" id="processBtn">
          <i class="oaicon oa-icon-changyongliucheng"></i>
          <span>常用流程</span>
        </li>
      </ul>
      <div class="scrollbar-box">
        <div class="waitDeal"></div>
        <div class="common-process hidden"></div>
      </div>
      <div id="TabsMorePage">查看更多</div>
    </div>
    <!-- 常用入口 -->
    <div class="common-entrance">
      <div class="heard-header">
        <div class="heard-nav"><i class="sign"></i>
          <p>常用入口</p>
        </div>
        <div class="heard-operation">
          <i class="oaicon oa-icon-shezhi1" id="accessSet"></i>
        </div>
      </div>
      <div class="common-entrance-content">
        <div class="common-entrance-page"></div>
        <div class="common-entrance-footer">
          <div class="footer-operate">
            <a href="javascript:;" class="operate_prev_page">
            </a>
            <a href="javascript:;" class="control_next_page">
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
