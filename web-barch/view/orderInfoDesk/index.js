'use strict';
define(function (require, exports, module) {
  var init = function (opt, html) {
    return perform();
  };
  module.exports = {
    init: init
  };

  function perform () {
    $.loadings();
    var statusBtn = {
        待派单: [
          {title: '派单', class: 'distribute', icon: 'oaicon oa-icon-tiaogang'},
          {title: '认领', class: 'claim', icon: 'fa fa-bookmark'},
          {title: '编辑', class: 'edit', icon: 'fa fa-pencil-square-o'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'},
          {title: '终止', class: 'end', icon: 'fa fa-ban'}
        ],
        待接单: [
          {title: '重派', class: 'resend', icon: 'oaicon oa-icon-lungang'},
          {title: '编辑', class: 'edit', icon: 'fa fa-pencil-square-o'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'},
          {title: '催办', class: 'urge', icon: 'svg-icon icon_work_order_send_message'},
          {title: '终止', class: 'end', icon: 'fa fa-ban'}
        ],
        处理中: [
          {title: '重派', class: 'resend', icon: 'oaicon oa-icon-lungang'},
          {title: '催办', class: 'urge', icon: 'svg-icon icon_work_order_send_message'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'},
          {title: '暂停', class: 'pause', icon: 'fa fa-clock-o'},
          {title: '终止', class: 'end', icon: 'fa fa-ban'},
        ],
        待验收: [
          {title: '提交知识库', class: 'sendKnowledgeBase', icon: 'fa fa-leanpub'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'}
        ],
        待评价: [
          {title: '提交知识库', class: 'sendKnowledgeBase', icon: 'fa fa-leanpub'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'}
        ],
        已完成: [
          {title: '编辑', class: 'edit', icon: 'fa fa-pencil-square-o'},
          {title: '提交知识库', class: 'sendKnowledgeBase', icon: 'fa fa-leanpub'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'}
        ],
        已暂停: [
          {title: '开启', class: 'start', icon: 'fa fa-play-circle-o'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'}
        ],
        已终止: [
          {title: '查看原因', class: 'watchReason', icon: 'svg-icon icon_work_order_show_rasen'},
          {title: '复制', class: 'copy', icon: 'fa fa-copy'},
          {title: '撤回', class: 'reback', icon: 'fa fa-undo'}
        ],
        未接听: [
          {title: '回拨', class: 'callBack', icon: 'fa fa-whatsapp'},
          {title: '标记无效来电', class: 'markUseless', icon: 'svg-icon icon_work_order_mark_useless'}
        ],
        未建单: [
          {title: '建单', class: 'creatNew', icon: 'oaicon oa-icon-liuchengfaqi'},
          {title: '标记无效来电', class: 'markUseless', icon: 'svg-icon icon_work_order_mark_useless'}
        ]
      },
      emergencyType = {
        1: '非常急',
        2: '比较急',
        3: '常规处理'
      },
      affectScope = {
        1: '个人事件',
        2: '科室事件',
        3: '多科室事件',
        4: '全院事件'
      };
    //处理中列表
    var handleTableCol = [
        {
          label: '工单号',
          name: 'workNumber',
          hidden: false,
          align: 'center',
          width: 100,
          sortable: true,
          index: 'a.work_number',
          fixed: true,
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_dept_name',
          fixed: true
        },
        {
          label: '科室ID',
          name: 'repairManDeptId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_name',
          fixed: true
        },
        {
          label: '报修人ID',
          name: 'repairManId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '业务类型ID',
          name: 'fkFaultTypeId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '故障名',
          name: 'fkFaultTypeName',
          hidden: true,
          width: 150,
          editable: false,
          sortable: true,
          index: 'f1.full_path',
          align: 'left'
        },
        {
          label: '报修时间',
          name: 'createTime',
          hidden: false,
          width: 100,
          editable: false,
          sortable: true,
          index: 'a.create_time',
          align: 'center',
          fixed: true
        },
        {
          label: '报修电话',
          name: 'repairPhone',
          width: 110,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.repair_phone',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          width: 300,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.fault_deion',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '业务类型',
          name: 'fkFaultTypeName',
          hidden: false,
          width: 85,
          align: 'left',
          sortable: true,
          index: 'f1.full_path',
          fixed: true,
        },
        {
          label: '建单人',
          name: 'createByName',
          hidden: false,
          width: 65,
          align: 'left',
          sortable: true,
          index: 'a.create_by_name',
          fixed: true
        },
        {
          label: '要求日期',
          name: 'requiredCompletionTime',
          hidden: false,
          width: 90,
          editable: false,
          sortable: true,
          align: 'center',
          index: 'a.required_completion_time',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (!cell) {
              return '';
            }
            let days = dayjs().diff(dayjs(cell), 'day');
            let html =
              '<span style="' +
              (days >= -3 &&
              ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
                ? 'color: red;'
                : '') +
              `">${cell}</span>`;
            return html;
          }
        },
        {
          label: '处理人',
          name: 'fkUserName',
          hidden: false,
          width: 65,
          align: 'left',
          sortable: true,
          index: 'a.fk_user_name',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = '';
            if (cell) {
              html = `<span title="${
                row.assistName ? '协助人：' + row.assistName : ''
              }">${cell}</span>`;
            }
            return html;
          }
        },
        {
          label: '联系方式',
          name: 'fkUserPhone',
          sortable: true,
          index: 'a.fk_user_phone',
          hidden: false,
          width: 110,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '状态',
          name: 'workStatusName',
          hidden: false,
          width: 50,
          align: 'center',
          sortable: true,
          index: 'a.work_status',
          fixed: true
        },
        {
          label: '催办次数',
          name: 'hatenCount',
          hidden: false,
          width: 65,
          align: 'right',
          sortable: true,
          index: 'haten_count',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = `<p name="openUrgeDetail" 
                          data-rowId="${opt.rowId}"
                          style="cursor: pointer;">
                          <span class="dealLink">${cell}</span></p>`;
            return html;
          }
        },
        {
          label: '备注',
          name: 'remark',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修方式',
          name: 'repairType',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '紧急程度',
          name: 'faultEmergency',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '影响范围',
          name: 'faultAffectScope',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修地址',
          name: 'repairDeptAddress',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '设备名称',
          name: 'faultEquipmentName',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '处理人ID',
          name: 'fkUserId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '节点ID',
          name: 'pkWsTaskId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '上传附件',
          name: 'wsFileOutVoList',
          hidden: true,
          width: 150,
          editable: false,
          align: 'left'
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          resizable: false,
          editable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              workStatus = row.workStatusName,
              btnList = statusBtn[workStatus] || [];
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn handle${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      //未建单
      unbuildTableCol = [
        {
          label: '工单号',
          name: 'workNumber',
          hidden: true,
          align: 'center',
          width: 100,
          sortable: true,
          index: 'a.work_number',
        },
        {
          label: '来电ID',
          name: 'pkCustometLogId',
          hidden: true,
          align: 'left',
          width: 82,
          sortable: false
        },
        {
          label: '来电时间',
          name: 'visitTime',
          hidden: false,
          align: 'center',
          width: 166,
          sortable: true,
          index: 'a.create_time'
        },
        {
          label: '报修科室',
          name: 'visitUserDeptName',
          width: 100,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.visit_user_dept_name',
        },
        {
          label: '科室ID',
          name: 'visitUserDeptId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人ID',
          name: 'visitUserId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人',
          name: 'visitUserName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.visit_user_name',
        },
        {
          label: '报修电话',
          name: 'visitPhone',
          width: 110,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.visit_phone',
        },
        {
          label: '通话录音',
          name: 'fileUrl',
          width: 200,
          editable: false,
          align: 'center',
          sortable: false,
          fixed: true,
          formatter: renderCustomAudio,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '时长',
          name: 'duration',
          width: 40,
          editable: false,
          align: 'left',
          sortable: false,
          formatter: function (cell, opt, row) {
            if (cell) {
              let timeList = cell.split(','),
                allTime = 0;
              timeList.forEach(item => {
                allTime += Number(item);
              });

              let second = allTime % 60,
                minute = parseInt((allTime % 3600) / 60),
                hour = parseInt(allTime / 3600 / 60);

              let time =
                (hour ? (hour > 9 ? `${hour}:` : `0${hour}:`) : '') +
                `${minute > 9 ? minute : '0' + minute}:${
                  second > 9 ? second : '0' + second
                }`;
              return time;
            }
            return '';
          }
        },
        {
          label: '备注说明',
          name: 'remark',
          hidden: false,
          width: 100,
          align: 'left',
          sortable: false
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          resizable: false,
          fixed: true,
          editable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              btnList = statusBtn[row.remark] || [];
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn unbuild${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      //已完成
      finishedTableCol = [
        {
          label: '工单号',
          name: 'workNumber',
          hidden: false,
          align: 'center',
          width: 100,
          sortable: true,
          index: 'a.work_number',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          hidden: false,
          align: 'left',
          sortable: true,
          index: 'a.fault_deion',
          width: 150,
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '业务类型',
          name: 'fkFaultTypeName',
          hidden: false,
          width: 85,
          align: 'left',
          sortable: true,
          index: 'f1.full_path',
          fixed: true,
        },
        {
          label: '报修时间',
          name: 'createTime',
          width: 100,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.create_time',
          fixed: true
        },
        {
          label: '确认时间',
          name: 'confirmTime',
          width: 100,
          hidden: false,
          align: 'center',
          sortable: true,
          index: 'a.work_status,b.create_time',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (row.workStatus === '2' || row.workStatus === '6') {
              return row.confirmTime;
            } else {
              return '';
            }
          }
        },
        {
          label: '终止时间',
          name: 'terminationTime',
          width: 100,
          sortable: true,
          index: 'a.work_status,b.create_time',
          align: 'center',
          hidden: false,
          editable: false,
          fixed: true,
          formatter: function (cell, opt, row) {
            return row.workStatus === '8' ? row.terminationTime : '';
          }
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_dept_name',
          fixed: true
        },
        {
          label: '科室ID',
          name: 'repairManDeptId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人ID',
          name: 'repairManId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_name',
          fixed: true
        },
        {
          label: '报修电话',
          name: 'repairPhone',
          width: 110,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.repair_phone',
          fixed: true
        },
        {
          label: '故障类型ID',
          name: 'fkFaultTypeId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '故障名',
          name: 'fkFaultTypeName',
          hidden: true,
          width: 150,
          editable: false,
          sortable: true,
          index: 'f1.full_path',
          align: 'left'
        },
        {
          label: '处理人  ',
          name: 'fkUserName',
          hidden: false,
          width: 65,
          align: 'left',
          sortable: true,
          index: 'a.fk_user_name',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = '';
            if (cell) {
              html = `<span title="${
                row.assistName ? '协助人：' + row.assistName : ''
              }">${cell}</span>`;
            }
            return html;
          }
        },
        {
          label: '联系方式',
          name: 'fkUserPhone',
          sortable: true,
          index: 'a.fk_user_phone',
          hidden: false,
          width: 62,
          align: 'left',
          fixed: true
        },
        {
          label: '状态  ',
          name: 'workStatusName',
          hidden: false,
          width: 50,
          align: 'center',
          sortable: true,
          index: 'a.work_status',
          fixed: true
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          resizable: false,
          editable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              workStatus = row.workStatusName,
              btnList = statusBtn[workStatus] || [];
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn finished${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      //通话记录
      recordTableCol = [
        {
          label: '来电时间',
          name: 'visitTime',
          hidden: false,
          width: 160,
          align: 'center',
          sortable: true,
          index: 'a.create_time',
          fixed: true
        },
        {
          label: '报修科室',
          name: 'visitUserDeptName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.visit_user_dept_name',
          fixed: true
        },
        {
          label: '报修人',
          name: 'visitUserName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.visit_user_name',
          fixed: true
        },
        {
          label: '报修电话',
          name: 'visitPhone',
          width: 110,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.visit_phone',
          fixed: true
        },
        {
          label: '来电录音',
          name: 'fileUrl',
          hidden: false,
          width: 260,
          align: 'center',
          sortable: false,
          fixed: true,
          formatter: renderCustomAudio,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '类型',
          name: 'callTypeName',
          hidden: false,
          width: 50,
          align: 'left',
          sortable: true,
          index: 'a.call_type',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          width: 250,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.fault_deion',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '备注说明 ',
          name: 'remark',
          hidden: false,
          width: 120,
          align: 'left',
          sortable: false
        },
        {
          label: '工单号',
          name: 'workNumber',
          hidden: false,
          align: 'center',
          width: 100,
          sortable: true,
          index: 'a.work_number',
          fixed: true
        },
        {
          label: '来电ID',
          name: 'pkCustometLogId',
          hidden: true,
          align: 'left',
          width: 82,
          sortable: false
        },
        {
          label: '科室ID',
          name: 'visitUserDeptId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人ID',
          name: 'visitUserId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          resizable: false,
          editable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              btnList = statusBtn[row.remark] || [];
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn record${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      //待派单
      dispatchTableCol = [
        {
          label: '工单号',
          name: 'workNumber',
          align: 'center',
          width: 100,
          sortable: true,
          index: 'a.work_number',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          width: 250,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.fault_deion',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '业务类型',
          name: 'fkFaultTypeName',
          hidden: false,
          width: 85,
          align: 'left',
          sortable: true,
          index: 'f1.full_path',
          fixed: true,
        },
        {
          label: '报修时间',
          name: 'createTime',
          width: 100,
          editable: false,
          align: 'center',
          sortable: true,
          index: 'a.create_time',
          fixed: true
        },
        {
          label: '要求日期',
          name: 'requiredCompletionTime',
          sortable: true,
          index: 'a.required_completion_time',
          hidden: false,
          width: 90,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (!cell) {
              return '';
            }
            let days = dayjs().diff(dayjs(cell), 'day');
            let html =
              '<span style="' +
              (days >= -3 &&
              ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
                ? 'color: red;'
                : '') +
              `">${cell}</span>`;
            return html;
          }
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_dept_name',
          fixed: true
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_name',
          fixed: true
        },
        {
          label: '备注',
          name: 'remark',
          sortable: false,
          hidden: true,
          width: 80,
          editable: false,
          align: 'left'
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          editable: false,
          resizable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              workStatus = row.workStatusName,
              btnList = statusBtn[workStatus] || [];

            if (!isWorkSheetAdmin) {
              btnList = [
                {title: '认领', class: 'claim', icon: 'fa fa-bookmark'},
              ]
            }
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn dispatch${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      //全部工单
      allTableCol = [
        {
          label: '工单号',
          name: 'workNumber',
          hidden: false,
          align: 'center',
          width: 100,
          sortable: true,
          fixed: true,
          index: 'a.work_number',
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_dept_name',
          fixed: true
        },
        {
          label: '科室ID',
          name: 'repairManDeptId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.repair_man_name',
          fixed: true
        },
        {
          label: '报修人ID',
          name: 'repairManId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '故障类型ID',
          name: 'fkFaultTypeId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '故障名',
          name: 'fkFaultTypeName',
          hidden: true,
          width: 150,
          editable: false,
          sortable: true,
          index: 'f1.full_path',
          align: 'left'
        },
        {
          label: '报修时间',
          name: 'createTime',
          hidden: false,
          width: 100,
          editable: false,
          sortable: true,
          index: 'a.create_time',
          align: 'center',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          width: 300,
          editable: false,
          align: 'left',
          sortable: true,
          index: 'a.fault_deion',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '业务类型',
          name: 'fkFaultTypeName',
          hidden: false,
          width: 85,
          align: 'left',
          sortable: true,
          index: 'f1.full_path',
          fixed: true,
        },
        {
          label: '建单人',
          name: 'createByName',
          hidden: false,
          width: 65,
          align: 'left',
          sortable: true,
          index: 'a.create_by_name',
          fixed: true
        },
        {
          label: '要求日期',
          name: 'requiredCompletionTime',
          hidden: false,
          width: 90,
          editable: false,
          sortable: true,
          align: 'center',
          index: 'a.required_completion_time',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (!cell) {
              return '';
            }
            let days = dayjs().diff(dayjs(cell), 'day');
            let html =
              '<span style="' +
              (days >= -3 &&
              ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
                ? 'color: red;'
                : '') +
              `">${cell}</span>`;
            return html;
          }
        },
        {
          label: '处理人',
          name: 'fkUserName',
          hidden: false,
          width: 65,
          align: 'left',
          sortable: true,
          index: 'a.fk_user_name',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = '';
            if (cell) {
              html = `<span title="${
                row.assistName ? '协助人：' + row.assistName : ''
              }">${cell}</span>`;
            }
            return html;
          }
        },
        {
          label: '状态',
          name: 'workStatusName',
          hidden: false,
          width: 50,
          align: 'center',
          sortable: true,
          index: 'a.work_status',
          fixed: true
        },
        {
          label: '催办次数',
          name: 'hatenCount',
          hidden: false,
          width: 65,
          align: 'center',
          sortable: true,
          index: 'haten_count',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = `<p name="openUrgeDetail" 
                          data-rowId="${opt.rowId}"
                          style="cursor: pointer;">
                          <sapn class="dealLink">
                            ${cell}
                          </span>
                        </p>`;
            return html;
          }
        },
        {
          label: '备注',
          name: 'remark',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修方式',
          name: 'repairType',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '紧急程度',
          name: 'faultEmergency',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '影响范围',
          name: 'faultAffectScope',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '报修地址',
          name: 'repairDeptAddress',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '设备名称',
          name: 'faultEquipmentName',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '处理人ID',
          name: 'fkUserId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '节点ID',
          name: 'pkWsTaskId',
          hidden: true,
          width: 150,
          editable: false,
          sortable: false,
          align: 'left'
        },
        {
          label: '上传附件',
          name: 'wsFileOutVoList',
          hidden: true,
          width: 150,
          editable: false,
          align: 'left'
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          editable: false,
          resizable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
            var btns = '',
              workStatus = row.workStatusName,
              btnList = statusBtn[workStatus] || [];
            for (var i = 0; i < btnList.length; i++) {
              var item = btnList[i];
              btns +=
                `<button type="button" class="layui-btn allOrderTable${item.class}" row-id=` +
                opt.rowId +
                '><i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
                item.title +
                '</button> ';
            }
            html += btns + '</div></div>';
            return html;
          }
        }
      ],
      handleTable = null,
      unbuildTable = null,
      finishedTable = null,
      recordTable = null,
      dispatchTable = null,
      allTable = null,
      phoneTextOldVal = '',
      todayInfoTimer = null; //今日统计定时刷新
    // 节点
    var statusBarTimeNode = document.getElementById('orderInfoStatusBarTime'),
      stautsItemWrapNode = document.getElementById('orderInfoStautsItemWrap'),
      phoneToastNode = document.getElementById('orderInfoPhoneToast'),
      phoneToastTextNode = document.getElementById('orderInfoPhoneToastText'), // 来电弹屏设置
      incomingCallBox = document.getElementById('orderIncomingCallBox'), //来电弹屏DOM
      //   waitingListBox = $('#orderInfoDeskDiv .waiting-list')[0],// 等待处理列表盒子
      waitingListBox = document.getElementById('waitingListBox'),
      handleTableNode = document.getElementById('orderHandleForm'),
      unbuildTableNode = document.getElementById('orderUnbuildForm'),
      finishedTableNode = document.getElementById('orderFinishedForm'),
      recordTableNode = document.getElementById('orderRecordForm'),
      dispatchTableNode = document.getElementById('orderDispatchForm'),
      allTableNode = document.getElementById('orderAllOrderForm');
    let phoneInfoForm = {}, // 电话信息表单
      phoneWebSocket = null, // 来电webSocket
      closeSocketTimer = null, //关闭Socket计时器
      callData = {}, //来电信息
      waitingList = [], //等待列表信息
      currentIndex = -1, //当前选项卡的下标 默认5 全部
      isWorkSheetAdmin = false;//是否为服务台人员，默认为不是

    var API = {
        workSheetList: '/ts-worksheet/workSheet/workSheetList', // 数据列表
        callRecords:
          '/ts-worksheet/CustometServiceLog/selectCallRecordsPageList', //查询通话记录    未见单和通话记录调用  type为1则为未建单
        workSheetListBusCounts:
          '/ts-worksheet/workSheet/workSheetListBusCounts',
        getOAPeopleList: '/ts-worksheet/workSheetPeopple/getOAPeopleList',
        savePhoneStatus: '/ts-worksheet/CustometService/saveCustometService', //保存来电弹屏坐席设置接口
        getUserInfo: '/ts-worksheet/workSheetPeopple/loginPersonInfo', //获取当前登录人信息
        getPhoneInfo: '/ts-worksheet/CustometService/getOneCustometService', //获取当前登录人坐席信息
        getPhoneState:
          '/ts-worksheet/CustometService/getOneCustometServiceStatus', //获取当前坐席状态
        getWaitingList:
          '/ts-worksheet/CustometServiceLog/selectCreateWorkSheetCustometService', //获取等待列表数据
        changeMarkType: '/ts-worksheet/CustometServiceLog/modifyingCallRecords', //更改工单状态，在标记为无效来电时使用
        // getOrderEditInfo: '/ts-worksheet/workSheet/workSheetEditInfo/', //获取工单编辑详情
        getOrderEditInfo: '/ts-worksheet/workSheet/workSheetInfo/', //获取工单编辑详情
        getHastenList: '/ts-worksheet/workSheetHasten/getHastenInfoList/', //获取催办信息记录列表
        getKnowledgeBaseInfo:
          '/ts-worksheet/workSheet/selectSubmitKnowledgeBaseInfo/' //提交知识库所需解决方案信息
      },
      queryData = null,
      userInfo = {}; //当前登录信息

    let playingAudio = null, //播放的音频
      playingRefreshTimer = null, //播放当前时间段定时器
      allTime = 0, //总的播放时长
      playedLine = null, //播放进度条   避免刷新重复获取
      playedTimeSpan = null, //播放时间  避免刷新重复获取
      playingDOM = null //当前正在播放的标签所在DOM

    let audioMouseMove = function () {
    };

    //创建事件监听对象
    Event.create('refreshNowPageDialog').listen('#/orderInfoDesk', ()=>{
      const activeTab = $('#orderInfoDeskDiv .desk-options-item-text.desk-option-active-text')[0];
      if (!activeTab || !allTable) {
        return;
      }
      Event.create('workSheet').trigger('tableRefresh');
    });

    //创建事件监听对象
    Event.create('orderInfoDeskJumpHandle').listen('jump', (e) => {
      let active = e ? String(e.index) : 5;
      setTimeout(() => {
        $(`.desk-options-item-text[data-index="${active}"]`).trigger('click');
      }, 300)
    });

    Event.create('workSheetEvent').listen('answerCall', data => {
      openAddModal(data);
    });

    Event.create('workSheet').listen('tableRefresh', () => {
      var table = null;
      switch (Number(currentIndex)) {
        case 1:
          table = handleTable;
          break;
        case 2:
          table = unbuildTable;
          break;
        case 3:
          table = finishedTable;
          break;
        case 4:
          table = recordTable;
          break;
        case 0:
          table = dispatchTable;
          break;
        case 5:
          table = allTable;
          break;
      }
      table.refresh();
    });

    Event.create('mainMessage').listen('routeChange', data => {
      if (playingAudio) {
        Event.create('hashChangeLoadJs').remove('jsLoaded');
        pauseAudio();
      }
    })

    async function initPage () {
      // 加载用户信息
      await new Promise((resolve, reject) => {
        $.ajax({
          url: API.getUserInfo,
          type: 'GET',
          success: function (res) {
            if (res.success) {
              userInfo = res.object;
              resolve(userInfo);
            } else {
              reject();
            }
          },
          error: res => {
            reject();
          }
        });
      })
        .then(res => {
          isWorkSheetAdmin = res.webSocket || false;
          // 获取当前坐席信息
          getPhoneInform().then(res => {
          });
        })
        .catch(() => {
        });

      renderStatusBarTime(statusBarTimeNode);
      renderStatusItemWrap(stautsItemWrapNode); // 今日服务数据获取
      renderPohoneToastNode(phoneToastNode);

      //60s刷新一次数据
      todayInfoTimer && clearInterval(todayInfoTimer);
      todayInfoTimer = setInterval(() => {
        getTodayServiceInfo(); // 今日服务数据获取
      }, 60000);

      layui.use(['form', 'laydate', 'layer'], function () {
        var form = layui.form,
          laydate = layui.laydate;
        renderOrderList();
        if (!isWorkSheetAdmin) {
          $('.desk-options-item-text[data-index="0"]').trigger('click');
          $('.desk-options-item-text:not([data-index="0"])').css('display', 'none');
          $('#orderInfoDeskDiv .phone-toast .info-desk-font.phone-toast-text.flex').css('display', 'none');
          $(
            '#orderInfoDeskDiv #infoDeskStatusBar #orderInfoStautsItemWrap .status-item:nth-child(2)'
          ).off('click')
          $(
            '#orderInfoDeskDiv #infoDeskStatusBar #orderInfoStautsItemWrap .status-item:nth-child(2)'
          ).css('cursor', 'default')
        } else {
          $('.desk-options-item-text[data-index="1"]').trigger('click');
        }

        form.render();
        form.verify({
          phoneNumber: (value = '', item) => {
            //如果是手机 / 加上区号的座机号
            if (value.length == 11) {
              let phoneReg = new RegExp(/^1[3-9][0-9]{9}$/g); //手机号校验规则
              if (!phoneReg.test(value)) {
                return '请输入正确的手机号';
              }
            }
          }
        });
        laydate.render({
          elem: '#orderInfoStartDate'
        });
        laydate.render({
          elem: '#orderInfoEndDate'
        });
        // 来点弹屏开关事件监听
        form.on('switch(phoneSwtich)', phoneSwtichEvent);
        form.on('submit(phonInfoClose)', phoneInfoToastClose);

        //为弹幕弹框添加提交事件
        bindPhoneToastEvent();
        //为来电设置弹框 输入框添加 键盘监听事件
        bindPhoneInputEvent();
        bindClearPhoneInputEvent(); //为来电设置弹框 清空按钮添加点击事件
        bindeCreaderOrderEvent();
        updateOptionsBar();
      });
    }

    initPage();
    renderSearchBar();

    //改变播放图标类型
    function changePlayImageIcon (type) {
      let pauseImage = $('img[data-type="bofang"]', playingDOM),
        playImage = $('img[data-type="zanting"]', playingDOM);

      playImage.css('display', type ? 'block' : 'none');
      pauseImage.css('display', type ? 'none' : 'block');
    }

    //播放录音
    function playAudio (rowData = {}) {
      let {fileUrl, duration = 0} = rowData;
      duration ? null : duration = 0;
      fileUrl = location.origin + fileUrl;

      if (playingAudio && playingDOM) {
        if (playingAudio.src != fileUrl) {
          pauseAudio();
        } else {
          playingRefreshTimer && clearInterval(playingRefreshTimer);
          playingAudio.pause();
        }
      }

      playingDOM = $(this)[0];
      playingAudio = new Audio();
      playingAudio.src = fileUrl;
      allTime = Number(duration);
      playedTimeSpan = $('span', playingDOM)[0];
      playedLine = $('.custom-audio-timeline [name="percent"]', playingDOM);

      playingAudio.onended = () => {
        playingAudio.currentTime = 0;
        pauseAudio();
      }
      //资源出现问题时，报错
      // if(playingAudio.networkState == 3){
      //   layer.msg('资源加载错误，无法播放');
      //   changePlayImageIcon(false);
      //   return
      // }

      //获取进度条，设置播放进度
      let playedWidth = parseInt(playedLine.width());
      isNaN(playedWidth) ? playedWidth = 0 : null;
      const playedTime = (playedWidth / 160) * allTime;
      playingAudio.currentTime = playedTime;

      changePlayImageIcon(true);
      refreshPlayingLine();
      playingAudio.play();

      playingRefreshTimer && clearInterval(playingRefreshTimer);
      playingRefreshTimer = setInterval(() => {
        refreshPlayingLine();
      }, 1000);
    }

    //暂停播放
    function pauseAudio () {
      playingRefreshTimer && clearInterval(playingRefreshTimer);
      refreshPlayingLine();
      changePlayImageIcon(false);
      playingAudio.pause();

      playingDOM = null;
      playingAudio = null;
      allTime = 0;
      playedTimeSpan = null;
      playedLine = null;
    }

    //刷新进度条
    function refreshPlayingLine () {
      if (!playingDOM || !playingAudio) {
        pauseAudio();
        return
      }

      let currentTime = parseInt(playingAudio.currentTime),
        playedWidth = (currentTime / allTime) * 160;

      playedLine.css('width', playedWidth + 'px');
      let second = currentTime % 60,
        minute = parseInt((currentTime % 3600) / 60),
        hour = parseInt(currentTime / 3600 / 60);

      let time =
        (hour ? (hour > 9 ? `${hour}:` : `0${hour}:`) : '') +
        `${minute > 9 ? minute : '0' + minute}:${
          second > 9 ? second : '0' + second
        }`;
      playedTimeSpan.innerHTML = time;
    }

    /*----------------------渲染方法--------------------------*/

    // 时间文本
    function renderStatusBarTime () {
      statusBarTimeNode.innerHTML = getDate(new Date());
    }

    // 今日服务台状态
    function renderStatusItemWrap (stautsItemWrapNode) {
      var statusWrapNode = document.createElement('div'),
        status = {
          picked: {title: '已接', num: 0},
          missed: {title: '未接', num: 0},
          called: {title: '呼出', num: 0},
          resolve: {title: '电话解决', num: 0},
          build: {title: '提单', num: 0},
          noBuild: {title: '无效来电', num: 0}
        };
      const keys = Object.keys(status);
      keys.map((key, index) => {
        var titleNode = document.createElement('div'),
          numNode = document.createElement('div'),
          tNode = document.createElement('div');
        // 标题
        titleNode.innerHTML = status[key].title;
        titleNode.className = 'info-desk-font status-item-title';
        // 数字
        numNode.innerHTML = status[key].num;
        // 给不同的样式
        index < 3
          ? (numNode.className = 'status-item-num-red')
          : (numNode.className = 'status-item-num-blue');
        tNode.appendChild(titleNode);
        tNode.appendChild(numNode);
        tNode.className = 'flex status-item';
        statusWrapNode.appendChild(tNode);
        statusWrapNode.className = 'flex';
      });
      stautsItemWrapNode.appendChild(statusWrapNode);
      $(
        '#orderInfoDeskDiv #infoDeskStatusBar #orderInfoStautsItemWrap .status-item:nth-child(2)'
      ).bind('click', function () {
        $('.desk-options-item-text:nth-child(6)').click();
      });
      getTodayServiceInfo();
    }

    //获取今日服务数据
    function getTodayServiceInfo () {
      $.ajax({
        url: '/ts-worksheet/CustometServiceLog/serviceDeskStatisticsToday',
        type: 'GET',
        success: function (res) {
          if (!res.success) {
            return;
          }
          let {yj, wj, hc, dhjj, jd, wxjd} = res.object;
          let list = [yj, wj, hc, dhjj, jd, wxjd],
            nodeList = $('.status-item >div:last-child');

          if (!nodeList.length) {
            return;
          }

          for (let i = 0; i < 6; i++) {
            nodeList[i].innerHTML = list[i];
          }
        }
      });
    }

    // 改变座机信息弹窗显示
    function renderPohoneToastNode (phoneToastNode) {
      phoneToastNode.style.visibility = 'hidden';
    }

    // 表格渲染
    function renderTable (url, table, pager, tableNode, workStatus, data) {
      return new $.trasenTable(tableNode, {
        url: url + (workStatus ? '?workStatus=' + workStatus : ''),
        pager: pager,
        shrinkToFit: true,
        sortname: 'a.create_time',
        colModel: table,
        postData: data,
        // ajaxGridOptions: {
        //   contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        //   async: false
        // },
        loadComplete: function(res){
          $('#orderInfoDeskDiv .page-total-count span')[0].innerHTML = res.totalCount || 0
        },
        buidQueryParams: function () {
          updateOptionsBar();
          getTodayServiceInfo();
          return queryData;
        }
      });
    }

    // 选项卡更改更改内容 绑定事件
    // 根据选项卡更改当前显示表单  tab点击事件
    function renderOrderList () {
      // handleTable = renderTable(API.workSheetList, handleTableCol, 'orderHandlePager', 'orderHandleTable', 10)
      // bindBtnEvent('handle',handleTable,updateOptionsBar)

      $('#orderInfoDeskDiv .desk-options-item-text').click(function (e) {
        var tar = e.target,
          clickIndex = tar.getAttribute('data-index');

        //初始删除 type 属性
        if (queryData) {
          queryData.type = null;
        }
        if (currentIndex == clickIndex) return;
        playingAudio && pauseAudio();
        $('#orderInfoDeskDiv .desk-options-item-text').removeClass(
          'desk-option-active-text'
        );

        // $('.desk-options-item-text')[currentIndex].className = 'desk-options-item-text'
        $('#orderInfoDeskDiv .desk-options-item-text')[['1', '2', '3', '4', '5', '0'][clickIndex]].className +=
          ' desk-option-active-text';
        $('#orderInfoDeskDiv .oa-nav-search .queryForm').hide();
        let showIndex = ['4', '0', '1', '2', '3', '5'][clickIndex];
        $('#orderInfoDeskDiv .oa-nav-search .queryForm')
          .eq(showIndex)
          .show();

        currentIndex = clickIndex;
        switch (currentIndex) {
          case '1':
            reset(handleTableNode, [
              unbuildTableNode,
              finishedTableNode,
              recordTableNode,
              dispatchTableNode,
              allTableNode,
            ]);
            if (!handleTable) {
              handleTable = renderTable(
                API.workSheetList,
                handleTableCol,
                'orderHandlePager',
                'orderHandleTable',
                10,
                {
                  workStatusG: '2,3,7'
                }
              );
              bindBtnEvent('handle', handleTable);
            }
            bindTableActionEvent(handleTable);
            break;
          case '2':
            reset(unbuildTableNode, [
              handleTableNode,
              finishedTableNode,
              recordTableNode,
              dispatchTableNode,
              allTableNode
            ]);
            queryData ? (queryData.type = '1') : (queryData = {type: '1'});
            if (!unbuildTable) {
              unbuildTable = renderTable(
                API.callRecords,
                unbuildTableCol,
                'orderUnbuildPager',
                'orderUnbuildTable',
                undefined,
                {type: '1'}
              );
              bindBtnEvent('unbuild', unbuildTable, updateOptionsBar);
            }
            bindTableActionEvent(unbuildTable);
            //表格点击故障描述展示工单详情
            $('#orderUnbuildTable span[name="showOtherAudio"]').funs(
              'click',
              e => {
                let row = e.target.getAttribute('data-row');
                let html = '';
                row.split(',').forEach(item => {
                  html += `<audio controls src="http://${window.location.host}${item}"
                style="height: 30px; margin: 5px; width: 250px;" ></audio>`;
                });
                layer.open({
                  title: '更多录音',
                  area: ['400px', '400px'],
                  content: html
                });
              }
            );
            break;
          case '3':
            reset(finishedTableNode, [
              handleTableNode,
              unbuildTableNode,
              recordTableNode,
              dispatchTableNode,
              allTableNode
            ]);
            if (!finishedTable) {
              finishedTable = renderTable(
                API.workSheetList,
                finishedTableCol,
                'orderFinishedPager',
                'orderFinishedTable',
                11
              );
              bindBtnEvent('finished', finishedTable, updateOptionsBar);
            }
            bindTableActionEvent(finishedTable);
            break;
          case '4':
            reset(recordTableNode, [
              handleTableNode,
              finishedTableNode,
              unbuildTableNode,
              dispatchTableNode,
              allTableNode
            ]);
            if (!recordTable) {
              recordTable = renderTable(
                API.callRecords,
                recordTableCol,
                'orderRecordPager',
                'orderRecordTable'
              );
              bindBtnEvent('record', recordTable, updateOptionsBar);
            }
            bindTableActionEvent(recordTable);
            $('#orderRecordTable span[name="showOtherAudio"]').funs(
              'click',
              e => {
                let row = e.target.getAttribute('data-row');
                let html = '';
                row.split(',').forEach(item => {
                  html += `<audio controls src="http://${window.location.host}${item}"
                style="height: 30px; margin: 5px; width: 250px;" ></audio>`;
                });
                layer.open({
                  title: '更多录音',
                  area: ['400px', '400px'],
                  content: html
                });
              }
            );
            break;
          case '0':
            reset(dispatchTableNode, [
              recordTableNode,
              handleTableNode,
              finishedTableNode,
              unbuildTableNode,
              allTableNode
            ]);
            if (!dispatchTable) {
              dispatchTable = renderTable(
                API.workSheetList,
                dispatchTableCol,
                'orderDispatchPager',
                'orderDispatchTable',
                12
              );
              bindBtnEvent('dispatch', dispatchTable, updateOptionsBar);
            }
            bindTableActionEvent(dispatchTable);
            break;
          case '5':
            reset(allTableNode, [
              recordTableNode,
              handleTableNode,
              finishedTableNode,
              unbuildTableNode,
              dispatchTableNode
            ]);
            if (!allTable) {
              allTable = renderTable(
                API.workSheetList,
                allTableCol,
                'orderAllOrderPager',
                'orderAllOrderTable',
                13
              );
              bindBtnEvent('allOrderTable', allTable, updateOptionsBar);
            }
            bindTableActionEvent(allTable);
            break;
          default:
            break;
        }

        $('#orderInfoDeskDiv .oa-nav-search [btnType="searchBtn"]')[
          showIndex
          ].click();
      });
    }

    //刷新Tab数据
    function updateOptionsBar () {
      let doms = $('form#orderInfo1-searchForm input[name]'),
      hiddenDoms = $('form#orderInfo1-searchFormHidden input[name]');

      let data = {};
      doms.map((index,item)=>{
        data[item.name] = item.value
      })
      hiddenDoms.map((index,item)=>{
        if(String(item.value)){
          data[item.name] = item.value
        }
      })
      
      $.ajax({
        url: API.workSheetListBusCounts + '/5',
        type: 'GET',
        data,
        success: function (res) {
          if (res.success == false) {
            layer.msg(res.message || '处理中，未建单统计数据加载失败');
            return;
          }
          let object = res.object || {};
          var num = parseInt(object[10]);
          let noCreate = parseInt(object[12]);
          $('.desk-options-item-text')[1].innerHTML =
            '待派单(' + parseInt(object[15]) + ')';
          $('.desk-options-item-text')[2].innerHTML = '处理中(' + num + ')';
          $('.desk-options-item-text')[3].innerHTML =
            '未建单(' + noCreate + ')';

          $('#orderInfoDeskDiv #orderOptionsBar .red-dot').css(
            'display',
            object[13] ? 'flex' : 'none'
          );
          $('#orderInfoDeskDiv #orderOptionsBar .red-dot div')[0].innerHTML =
            object[13];

          if (
            $(
              '#orderInfoDeskDiv #orderOptionsBar .desk-options-item-text.has-dot'
            ).hasClass('desk-option-active-text')
          ) {
            $('#orderInfoDeskDiv #orderOptionsBar .red-dot').css(
              'display',
              'none'
            );
          }
        }
      });
    }

    /*---------------------事件绑定--------------------------*/

    //获取坐席信息
    function getPhoneInform () {
      return new Promise((resolve, reject) => {
        //获取当前我的坐席的信息，更新数据
        $.ajax({
          url: API.getPhoneInfo + '/' + userInfo.userId,
          type: 'get',
          success: res => {
            if (res.success) {
              let {
                phone,
                fkUserId,
                fkUserDeptId,
                custometServiceStatus,
                pkCustometServiceId,
                playScreen,
                playVoice
              } = res.object;
              phoneInfoForm = {
                phoneNumber: Number(phone),
                fkUserId,
                fkUserDeptId,
                pkCustometServiceId,
                custometServiceStatus,
                phoneSwtich: playScreen ? 'on' : undefined,
                videoSwtich: playVoice ? 'on' : undefined
              };
              let form = layui.form;
              form.val('phoneInfoForm', phoneInfoForm);
              form.render('', 'phoneInfoForm');
              // Number(custometServiceStatus) && playScreen
              //   ? $('#orderInfoDeskDiv #orderInfoPhoneToastText .layui-form-switch').addClass('layui-form-onswitch')
              //   : null;

              $(
                '#orderInfoDeskDiv #orderInfoPhoneToastInfo .phone-info-input'
              ).change();
              resolve();
            } else {
              reject();
            }
          },
          error: res => {
            reject();
          }
        });
      }).catch(res => {
        return res;
      });
    }

    // 电话弹屏开关设置
    function phoneSwtichEvent (data) {
      // 开关启动提交数据开启弹窗
    }

    // 电话弹屏信息设置弹窗
    function bindPhoneToastEvent () {
      var form = layui.form;
      phoneToastTextNode.addEventListener('click', function (e) {
        if (
          phoneInfoForm.phoneNumber == '' ||
          phoneInfoForm.phoneNumber == null
        ) {
          $('#orderInfoDeskDiv .pohone-info-status-wrap>input').attr(
            'disabled',
            true
          );
        } else {
          $('#orderInfoDeskDiv .pohone-info-status-wrap>input').attr(
            'disabled',
            false
          );
        }

        if (phoneToastNode.style.visibility == 'visible') {
          phoneInfoToastClose();
        } else {
          phoneToastNode.style.visibility = 'visible';
          getPhoneInform().catch(() => {
          });
        }
        form.on('radio(radioRelaxed)', function (val) {
          if (val.value == '0') {
            form.val('phoneInfoForm', {phoneSwtich: undefined})
          } else {
            form.val('phoneInfoForm', {phoneSwtich: 'on'})
          }
        });
        // form.on('switch(phoneSwtich)', function(val) {
        //   const { custometServiceStatus } = form.val('phoneInfoForm') ;
        //   if(!Number(custometServiceStatus)){
        //     layer.msg('休息中无法设置来电弹屏');
        //     form.val('phoneInfoForm', {phoneSwtich: undefined});
        //   }
        // })
        form.on('submit(phonInfoSubmit)', phoneInfoProxy());
        form.render('', 'phoneInfoForm');
      });
    }

    // 来电弹屏设置窗口 我的坐席添加输入监听事件
    function bindPhoneInputEvent () {
      $('#orderInfoDeskDiv #orderInfoPhoneToastInfo .phone-info-input').on(
        'input paste change',
        function (e) {
          let val = e.target.value,
            form = layui.form;
          if (val == '' && phoneTextOldVal != '') {
            $('#orderInfoDeskDiv .pohone-info-status-wrap>input').attr(
              'disabled',
              true
            );
            form.render('', 'phoneInfoForm');
          } else if (val != '' && phoneTextOldVal == '') {
            $('#orderInfoDeskDiv .pohone-info-status-wrap>input').attr(
              'disabled',
              false
            );
            form.render('', 'phoneInfoForm');
          }
          phoneTextOldVal = val;
        }
      );
    }

    // 来电弹屏设置窗口 我的坐席清空按钮事件
    function bindClearPhoneInputEvent () {
      $('#orderInfoDeskDiv #orderInfoPhoneToastInfo #clearPhone').click(
        function (e) {
          $(
            '#orderInfoDeskDiv #orderInfoPhoneToastInfo .phone-info-input'
          )[0].value = '';
          $(
            '#orderInfoDeskDiv #orderInfoPhoneToastInfo .phone-info-input'
          ).trigger('change');
        }
      );
    }

    // 关闭按钮事件 来电弹屏设置窗口
    function phoneInfoToastClose () {
      phoneToastNode.style.visibility = 'hidden';
      phoneTextOldVal = phoneInfoForm.phoneNumber;
      // phoneInfoForm.phoneSwtich == 'on'
      //   ? $(
      //       '#orderInfoDeskDiv #orderInfoPhoneToastText .layui-form-switch'
      //     ).addClass('layui-form-onswitch')
      //   : $(
      //       '#orderInfoDeskDiv #orderInfoPhoneToastText .layui-form-switch'
      //     ).removeClass('layui-form-onswitch');
      let form = layui.form;
      form.val('phoneInfoForm', phoneInfoForm);
      form.render('', 'phoneInfoForm');
    }

    // 创建工单弹窗
    function bindeCreaderOrderEvent () {
      $('[name="createOrderBtn"]').funs('click', function () {
        var table = null;
        switch (Number(currentIndex)) {
          case 1:
            table = handleTable;
            break;
          case 2:
            table = unbuildTable;
            break;
          case 3:
            table = finishedTable;
            break;
          case 4:
            table = recordTable;
            break;
          case 0:
            table = dispatchTable;
            break;
          case 5:
            table = allTable;
            break;
        }
        $.quoteFun('orderInfoDesk/modules/add', {
          title: '创建工单',
          ref: function () {
            table.refresh();
          }
        });
      });
    }

    //绑定操作按钮事件
    function bindBtnEvent (type, table, updateOptionsBar) {
      $('.' + type + 'copy').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        data.entryType = 'copy';
        if (type == 'record') {
          $.ajax({
            url: API.getOrderEditInfo + data.workNumber,
            type: 'GET',
            success: function (res) {
              if (res.success) {
                data = Object.assign(
                  {},
                  res.object.wsWsSheetInfoOutVo,
                  {
                    fkFaultTypeId: res.object.wsWsSheetInfoOutVo.fkFaultTypeId,
                    fkFaultTypeName: res.object.wsWsSheetInfoOutVo.fkFaultType,
                    wsFileOutVoList: res.object.wsFileOutVoList
                  }
                );
                // data.faultTypeBoxName = data.fkFaultTypeName
                $.quoteFun('orderInfoDesk/modules/add', {
                  title: '复制',
                  data: data,
                  ref: function () {
                    table.refresh();
                  }
                });
              } else {
                layer.msg(res.message || '出错啦');
              }
            }
          });
        } else {
          $.quoteFun('orderInfoDesk/modules/add', {
            title: '复制',
            data: data,
            ref: function () {
              table.refresh();
            }
          });
        }
      });
      $('.' + type + 'end').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        data.entryType = 'end';
        $.quoteFun('myWorkOrder/modules/handle', {
          title: '终止',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });
      $('.' + type + 'urge').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        if (type == 'record') data.workStatusName = data.remark;
        $.quoteFun('orderInfoDesk/modules/urge', {
          title: '催办',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });
      $('.' + type + 'pause').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        data.entryType = 'pause';
        $.quoteFun('myWorkOrder/modules/handle', {
          title: '暂停',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });
      $('.' + type + 'resend').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        $.quoteFun('orderInfoDesk/modules/distribute', {
          title: '重派',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });
      $('.' + type + 'distribute').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          //   data = table.getSourceRowData(rowId)
          data = table.getSourceRowData(rowId);
        $.quoteFun('orderInfoDesk/modules/distribute', {
          title: '派单',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });
      $('.' + type + 'edit').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        //进行数据命名同步，与add.js内命名同步
        // data.faultTypeBoxName = data.fkFaultTypeName;
        if (type == 'record') {
          $.ajax({
            url: API.getOrderEditInfo + data.workNumber,
            type: 'GET',
            success: function (res) {
              if (res.success) {
                data = Object.assign(
                  {},
                  res.object.wsWsSheetInfoOutVo,
                  {
                    fkFaultTypeId: res.object.wsWsSheetInfoOutVo.fkFaultTypeId,
                    fkFaultTypeName: res.object.wsWsSheetInfoOutVo.fkFaultType,
                    wsFileOutVoList: res.object.wsFileOutVoList
                  }
                );
                // data.faultTypeBoxName = data.fkFaultTypeName
                $.quoteFun('orderInfoDesk/modules/add', {
                  title: '编辑',
                  data: data,
                  ref: function () {
                    table.refresh();
                  }
                });
              } else {
                layer.msg(res.message || '出错啦');
              }
            }
          });
        } else {
          $.quoteFun('orderInfoDesk/modules/add', {
            title: '编辑',
            data: data,
            finishedRenderType: type === 'finished',
            ref: function () {
              table.refresh();
            }
          });
        }
      });
      $('.' + type + 'start').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        $.quoteFun('myWorkOrder/modules/start', {
          title: '开启',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      });

      //提交知识库事件
      $('.' + type + 'sendKnowledgeBase').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          rowData = table.getSourceRowData(rowId),
          data = {
            knowledgeTitle: rowData.faultDeion,
            recommendedWorkHours: rowData.workHours
          };

        $.ajax({
          url: API.getKnowledgeBaseInfo + rowData.workNumber,
          type: 'GET',
          async: false,
          success: function (res) {
            if (res.success) {
              data.takeRemark = res.object.takeRemark;
            } else {
              layer.msg(res.message || '出错啦');
            }
          }
        });

        if (type == 'record') {
          $.ajax({
            url: API.getOrderEditInfo + rowData.workNumber,
            type: 'GET',
            async: false,
            success: function (res) {
              if (res.success) {
                rowData = Object.assign({}, res.object.wsWsSheetInfoOutVo, { wsFileOutVoList: res.object.wsFileOutVoList});
                data = {
                  knowledgeTitle: rowData.faultDeion,
                  recommendedWorkHours: rowData.workHours
                };
                $.quoteFun('myWorkOrder/modules/addLibrary', {
                  title: '新增知识点',
                  data,
                  ref: function () {
                    table.refresh();
                  }
                });
              } else {
                layer.msg(res.message || '出错啦');
              }
            }
          });
        } else {
          $.quoteFun('myWorkOrder/modules/addLibrary', {
            title: '新增知识点',
            data,
            ref: function () {
              table.refresh();
            }
          });
        }
      });

      //建单操作
      $('.' + type + 'creatNew').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        data.repairPhone = data.visitPhone;
        data.repairManDeptId = data.visitUserDeptId;
        data.repairManDeptName = data.visitUserDeptName;
        data.repairManId = data.visitUserId;
        data.repairManName = data.visitUserName;
        batchDelete(data, [
          'visitPhone',
          'visitUserDeptId',
          'visitUserDeptName',
          'visitUserName',
          'visitUserId',
          'remark'
        ]);

        $.quoteFun('orderInfoDesk/modules/add', {
          title: '创建工单',
          data,
          editType: '1', //建单操作
          ref: function () {
            table.refresh();
          }
        });
      });
      //回拨操作
      $('.' + type + 'callBack').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = common.deepClone(table.getSourceRowData(rowId));

        data.repairPhone = data.visitPhone;
        data.repairManDeptId = data.visitUserDeptId;
        data.repairManDeptName = data.visitUserDeptName;
        data.repairManId = data.visitUserId;
        data.repairManName = data.visitUserName;

        batchDelete(data, [
          'visitPhone',
          'visitUserDeptId',
          'visitUserDeptName',
          'visitUserName',
          'visitUserId',
          'remark'
        ]);

        $.quoteFun('orderInfoDesk/modules/add', {
          title: '创建工单',
          data,
          editType: '2', //回拨
          ref: function () {
            table.refresh();
          }
        });
      });
      //标记无效来电
      $('.' + type + 'markUseless').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        layer.open({
          title: '提示',
          content: '确定标记为无效来电？',
          btn: ['确定', '取消'],
          btn1: function (index, layero) {
            $.ajax({
              url: API.changeMarkType,
              type: 'POST',
              contentType: 'application/json; charset=utf-8',
              data: JSON.stringify({
                pkCustometLogId: data.pkCustometLogId,
                callType: data.callType,
                callWorkStatus: 2
              }),
              success: function (res) {
                layer.close(index);
                if (res.success) {
                  layer.msg('已成功标记为无效来电');
                  table.refresh();
                } else {
                  layer.msg(res.message || '操作失败');
                }
              }
            });
          },
          btn2: function (index, layero) {
            layer.close(index);
          }
        });
      });
      //查看终止原因
      $('.' + type + 'watchReason').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        layer.open({
          title: '终止详情',
          content: `
                <div>
                    <div>终止人：${data.terminationByName}</div>
                    <div>终止时间：${data.terminationTime}</div>
                    <div>终止原因：${data.terminationRemark}</div>
                </div>
            `,
          btns: ['确定'],
          btn1: function (index, layero) {
            layer.close(index);
          }
        });
      });
      //认领
      $('.' + type + 'claim').funs('click', function (e) {
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);
        $.quoteFun('orderInfoDesk/modules/distribute', {
          title: '认领',
          data: data,
          ref: function () {
            table.refresh();
          }
        });
      })
      //撤回
      $('.' + type + 'reback').funs('click', function(e){
        var tNode = e.target,
          rowId = tNode.getAttribute('row-id'),
          data = table.getSourceRowData(rowId);

        layer.open({
          title: '提示'
          , content: '确定撤回该工单？'
          , btn: ['确定', '取消']
          , btn1: function (index, layero) {
            let saveData = {
              pkWsTaskId: data.pkWsTaskId,
              workNumber: data.workNumber
            }
            $.ajax({
              url: '/ts-worksheet/workSheet/workSheetToRecoverEnd',
              type: 'post',
              data: JSON.stringify(saveData),
              contentType: 'application/json',
              success: function (res) {
                if (res.success) {
                  layer.msg('撤回成功');
                  layer.close(index);
                  table.refresh();
                } else {
                  layer.msg(res.message || '撤回失败')
                }
              }
            })
          }
          , btn2: function (index, layero) {
            layer.close(index)
          }
        });
      })
    }

    //绑定点击事件
    function bindTableActionEvent (table) {
      //表格点击故障描述展示工单详情
      $('#orderInfoDeskDiv span[name="openFaultAllInfo"]', table).funs(
        'click',
        e => {
          let rowId = e.target.getAttribute('data-rowId'),
            rowData = table.getSourceRowData(rowId);
          $.quoteFun('orderInfoDesk/modules/distribute', {
            title: '工单详情',
            data: rowData
          });
        }
      );

      //表格点击催办次数展示催办详情
      $('#orderInfoDeskDiv span[name="openUrgeDetail"]', table).funs(
        'click',
        e => {
          let rowId = e.target.getAttribute('data-rowId'),
            rowData = table.getSourceRowData(rowId);
          $.ajax({
            url: API.getHastenList + rowData.workNumber,
            type: 'get',
            success: function (res) {
              if (res.success == false) {
                layer.msg(res.message || '催办记录加载失败');
                return;
              }
              let lineHtml = '';
              (res.object || []).forEach(item => {
                lineHtml += `
                  <li class="layui-timeline-item">
                    <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                    <div class="layui-timeline-content layui-text">
                      <div class="layui-timeline-title">
                        <span>${item.createByName}</span>
                        <span class='hasten-time'>${item.createTime}</span>
                      </div>
                    </div>
                  </li>
                  `;
              });

              if (!res.object || !res.object.length) {
                lineHtml = `<div style="height: 100%; line-height: 130px;">暂无数据</div>`;
              }

              $.quoteFun('orderInfoDesk/modules/hastenList', {
                title: '催办记录',
                data: lineHtml
              });
            }
          });
        }
      );

      //点击通讯录，跳转通讯录页面
      $('#orderInfoDeskDiv .address-book-wrap').funs('click', e => {
        window.location.href = location.origin + '/#/personal/linkMan';
      });

      //鼠标点击播放事件
      $('#orderInfoDeskDiv .custom-audio-content', table).funs(
        'click',
        function (e) {
          const rowId = $(this)[0].getAttribute('data-index'),
            rowData = table.getSourceRowData(rowId),
            {fileUrl, duration = 0} = rowData,
            type = e.target.getAttribute('data-type');

          if (!type) {
            return
          }

          if (type === 'bofang') {
            //暂停上一个播放的音频
            if (playingDOM && playingAudio) {
              pauseAudio();
            }
            playAudio.call(this, rowData);
          } else {
            pauseAudio();
          }
        }
      );

      //鼠标移入进度条事件
      $('#orderInfoDeskDiv .custom-audio-timeline', table).funs(
        'mouseenter',
        function (e) {
          const hoverDom = $(this)[0],
            hoverIndex = hoverDom.getAttribute('data-index'),
            hoverRowData = table.getSourceRowData(hoverIndex),
            hoverLineLeft = hoverDom.getBoundingClientRect().left;

          let {fileUrl, duration = 0} = hoverRowData;
          duration ? null : duration = 0;

          let playDot = $('.play-dot', this)[0];
          playDot.style.display = 'block';


          $(this).append(`
            <div class="audio-timeline-toast-box">
              00:00
            </div>
          `)
          setTimeout(() => {
            $('.audio-timeline-toast-box', $(this)).addClass('show')
          })

          audioMouseMove = function (event) {
            let mouseX = event.clientX,
              toPlayWidth = mouseX - hoverLineLeft;

            toPlayWidth > 160
              ? (toPlayWidth = 160)
              : toPlayWidth < 0
                ? (toPlayWidth = 0)
                : null;

            let toPlayTime = parseInt((toPlayWidth / 160) * duration),
              second = toPlayTime % 60,
              minute = parseInt((toPlayTime % 3600) / 60),
              hour = parseInt(toPlayTime / 3600 / 60);
            let time =
              (hour ? (hour > 9 ? `${hour}:` : `0${hour}:`) : '') +
              `${minute > 9 ? minute : '0' + minute}:${
                second > 9 ? second : '0' + second
              }`;

            playDot.style.left = toPlayWidth + 'px';
            if (!$('.audio-timeline-toast-box', hoverDom)[0]) {
              return
            }
            $('.audio-timeline-toast-box', hoverDom).css('left', toPlayWidth > 122 ? '122px' : toPlayWidth + 'px');
            $('.audio-timeline-toast-box', hoverDom)[0].innerHTML = time;
          }
          document.addEventListener('mousemove', audioMouseMove);
        }
      )

      //鼠标移出事件
      $('#orderInfoDeskDiv .custom-audio-timeline', table).funs(
        'mouseleave',
        function (e) {
          const leaveDom = $(this)[0];
          document.removeEventListener('mousemove', audioMouseMove);

          let playDot = $('.play-dot', this)[0];
          playDot.style.display = 'none';
          $('.audio-timeline-toast-box', leaveDom).remove();
        }
      )

      //鼠标点击进度条
      $('#orderInfoDeskDiv .custom-audio-timeline', table).funs(
        'click',
        function (e) {
          const changeLineDom = $(this)[0],
            changeLineLeft = changeLineDom.getBoundingClientRect().left,
            changeIndex = changeLineDom.getAttribute('data-index'),
            changeRowData = table.getSourceRowData(changeIndex);

          let {fileUrl, duration = 0} = changeRowData;
          duration ? null : duration = 0;

          let playDot = $('.play-dot', this)[0];
          playDot.style.display = 'none';
          $('.audio-timeline-toast-box', changeLineDom).remove();
          document.removeEventListener('mousemove', audioMouseMove);

          let mouseX = e.clientX,
            toPlayWidth = mouseX - changeLineLeft;

          toPlayWidth > 160
            ? (toPlayWidth = 160)
            : toPlayWidth < 0
              ? (toPlayWidth = 0)
              : null;
          const toPlayTime = (toPlayWidth / 160) * duration;
          $('[name="percent"]', changeLineDom).css('width', toPlayWidth);

          playAudio.call(
            e.target.parentNode.parentNode.parentNode,
            changeRowData
          )
        }
      )

      //导出事件
      $('#orderInfoDeskDiv [name="export"]').off('click').bind('click', function (e){
        layui.use('form',function(){
          let form = layui.form,
            index = e.target.getAttribute('data-index'),
            workStatus = e.target.getAttribute('data-status'),
            tableId = e.target.getAttribute('data-table'),
            tableParam = $('#orderInfoDeskDiv #'+tableId).jqGrid('getGridParam')
          let data = Object.assign(
            form.val(`orderInfo${index}`),
            form.val(`orderInfo${index}-hidden`),
            {
              // pageNo: tableParam.page,
              // pageSize: tableParam.rowNum,
              pageNo: 1,
              pageSize: 999999999,
              sidx: tableParam.sortname,
              sord: tableParam.sortorder
            }
          );
          
          if(['2', '4'].indexOf(index)>=0){
            common.downFile({
              url: '/ts-worksheet/CustometServiceLog/exportExcel',
              data
            })
          }else {
            data.workStatus = workStatus;
            common.downFile({
              url: '/ts-worksheet/workSheet/exportExcel',
              data
            })
          }
        })
        
      })
    }

    /*---------------------内部方法--------------------------*/
    function queryDataRule (queryData) {
      if (new Date(queryData.beginTime) > new Date(queryData.endTime)) {
        return '起始时间不能大于终止时间';
      }
      return false;
    }

    // 显示隐藏表
    function reset (tarNode, otherNodes) {
      tarNode.style.display = 'block';
      otherNodes.map(node => {
        node.style.display = 'none';
      });
    }

    // 更新表格数据
    function refresh (tabel) {
      return tabel.refresh();
    }

    // 更新选项卡数据

    // 弹屏设置表单 确认操作
    function phoneInfoProxy () {
      return data => {
        let {phoneNumber, custometServiceStatus, phoneSwtich, videoSwtich} = data.field,
          oldStatus = phoneInfoForm.custometServiceStatus || '0';

        // if(!phoneNumber){
        //   layer.msg('我的坐席不能为空')
        //   return
        // }

        let saveData = {
          fkUserDeptId: userInfo.deptId,
          fkUserId: userInfo.userId,
          // phone: phoneNumber,
          pkCustometServiceId: phoneInfoForm.pkCustometServiceId || undefined,
          playScreen: phoneSwtich ? 1 : 0,
          playVoice: videoSwtich ? 1 : 0,
          // custometServiceStatus: Number(custometServiceStatus)
        };
        new Promise((resolve, reject) => {
          $.ajax({
            url: API.getPhoneState,
            type: 'GET',
            data: 'fkUserId=' + userInfo.userId + '&phone=' + saveData.phone,
            success: function (res) {
              if (res.success) {
                resolve();
              } else if (!res.success && res.message) {
                resolve(res.message);
              } else {
                reject();
              }
            }
          });
        }).then(res => {
          if (res) {
            layer.open({
              title: '提示',
              content: res,
              btn: ['确定', '取消'],
              btn1: (index, layero) => {
                layer.close(index);
                changePhoneInfo(saveData);
              },
              btn2: (index, layero) => {
                layer.close(index);
              }
            });
          } else {
            changePhoneInfo(saveData);
          }
        });
      };
    }

    function changePhoneInfo (saveData) {
      $.ajax({
        url: API.savePhoneStatus,
        type: 'post',
        contentType: 'application/json;charset=UTF-8',
        data: JSON.stringify(saveData),
        success: res => {
          if (res.success) {
            Object.assign(phoneInfoForm, {
              phoneSwtich: saveData.playScreen ? 'on' : undefined,
              videoSwtich: saveData.playVoice ? 'on' : undefined,
              phoneNumber: saveData.phone,
              custometServiceStatus: saveData.custometServiceStatus
            });
            phoneToastNode.style.visibility = 'hidden';
            layer.msg('操作成功');

            window.dispatchEvent(
              new CustomEvent('sendToNewFrameMessage', {
                detail: {
                  type: 'tsWorkSheetConnectWebSocket',
                  data: {
                    playScreen: saveData.playScreen,
                    playVoice: saveData.playVoice
                  }
                }
              })
            );
          } else {
            phoneToastNode.style.visibility = 'hidden';
            layer.msg(res.message || '出错啦');
          }
        }
      });
    }

    // 日期
    function getDate (date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? '0' + d : d;
      return y + '年' + m + '月' + d + '日';
    }

    //刷新表格，打开新增框
    function openAddModal (data) {
      $.quoteFun('orderInfoDesk/modules/add', {
        title: '创建工单',
        data: data,
        ref: function () {
          Event.create('workSheet').trigger('tableRefresh');
        }
      });
    }

    //批量删除对象属性值
    function batchDelete (obj, list) {
      if (Object.prototype.toString.call(obj) != '[object Object]') {
        return;
      }
      if (!list || Object.prototype.toString.call(list) != '[object Array]') {
        return;
      }

      list.forEach(key => {
        delete obj[key];
      });
    }

    function renderSearchBar () {
      layui.use(['zTreeSearch', 'form', 'laydate'], function () {
        var zTreeSearch = layui.zTreeSearch,
          form = layui.form,
          laydate = layui.laydate;

        //全部 -搜索渲染
        //渲染搜索的报修范围选择器
        $('#orderInfoDeskDiv [render="time"]').each(function (index, item) {
          laydate.render({
            elem: item,
            format: 'yyyy-MM-dd',
            type: 'date',
            trigger: 'click'
          });
        });

        //渲染报修科室
        $('#orderInfoDeskDiv [render="repairDep"]').each(function (index, item) {
          let repaireManId = item.getAttribute('data-repairman-id'),
            repaireManDom = $(`#orderInfoDeskDiv #${repaireManId}`);
          repaireManDom.empty();
          zTreeSearch.init(`#orderInfoDeskDiv [depNameVal="repairManDeptName${index}"]`, {
            url: common.url + '/ts-basics-bottom/organization/getTree',
            type: 'post',
            checkbox: false,
            condition: 'name',
            zTreeOnClick: function (treeId, treeNode) {
              if (treeNode) {
                $(`#orderInfoDeskDiv [depIdVal="repairManDeptId${index}"]`).val(treeNode.id);
                let options = {
                  url: '/ts-basics-bottom/employee/getEmployeePageList',
                  datatype: 'POST', // 请求方式
                  searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                  textName: 'employeeName', 
                  valName: 'employeeId', 
                  inpTextName: 'repairName', 
                  inpValName: 'repairManId', 
                  condition: 'employeeName',
                  required: 'none',
                  layout: 'concat',
                  labelConcatList: 'orgName',
                  data: {
                    orgId: treeNode.id,
                    pageSize: 10
                  },
                  callback: function (res) {
                    if (res) {
                    }
                  }
                };
                new $.selectPlug(`#orderInfoDeskDiv #${repaireManId}`, options);
              }
            },
            callback: function () {
              $(`#orderInfoDeskDiv [depIdVal="repairManDeptId${index}"]`).val('');
              let options = {
                url: '/ts-basics-bottom/employee/getEmployeePageList',
                datatype: 'POST', // 请求方式
                searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                textName: 'employeeName', 
                valName: 'employeeId', 
                inpTextName: 'repairName', 
                inpValName: 'repairManId', 
                condition: 'employeeName',
                required: 'none',
                layout: 'concat',
                labelConcatList: 'orgName',
                data: {
                  pageSize: 10
                },
                callback: function (res) {
                  if (res) {
                  }
                }
              };
              new $.selectPlug(`#orderInfoDeskDiv #${repaireManId}`, options);
            }
          });
          $(`#orderInfoDeskDiv [depNameVal="repairManDeptName${index}"]`).bind(
            'input propertychange',
            function (e) {
              $(`#orderInfoDeskDiv [depIdVal="repairManDeptId${index}"]`)[0].value = '';
            }
          );
          $(`#orderInfoDeskDiv [depNameVal="repairManDeptName${index}"]`).bind(
            'blur',
            function () {
              $(`#orderInfoDeskDiv [depIdVal="repairManDeptId${index}"]`)[0].value
                ? null
                : ($(`#orderInfoDeskDiv [depNameVal="repairManDeptName${index}"]`)[0].value = '');
            }
          );
        });

        //渲染报修人
        let options = {
          url: '/ts-basics-bottom/employee/getEmployeePageList',
          datatype: 'POST', // 请求方式
          searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
          textName: 'employeeName', 
          valName: 'employeeId', 
          inpTextName: 'repairName', 
          inpValName: 'repairManId', 
          condition: 'employeeName',
          required: 'none',
          layout: 'concat',
          labelConcatList: 'orgName',
          data: {
            pageSize: 10
          },
          callback: function (res) {
            if (res) {
            }
          }
        };
        $('#orderInfoDeskDiv [render="repairMan"]').each(function (index, item) {
          new $.selectPlug("#orderInfoDeskDiv [repairMan='" + index + "']", options);
        });
        // 渲染处理科室
        $('#orderInfoDeskDiv [render="fkDep"]').each(function (index, item) {
          if (index == 2) index = 4;
          let fkDeptManId = item.getAttribute('data-repairman-id'),
            fkDeptManDom = $(`#orderInfoDeskDiv #${fkDeptManId}`);
          fkDeptManDom.empty();
          zTreeSearch.init(`#orderInfoDeskDiv [depNameVal="fkDeptName${index}"]`, {
            url: common.url + '/ts-basics-bottom/organization/getTree',
            type: 'post',
            checkbox: false,
            condition: 'name',
            zTreeOnClick: function (treeId, treeNode) {
              if (treeNode) {
                $(`#orderInfoDeskDiv [depIdVal="fkDeptId${index}"]`).val(treeNode.id);
                let options = {
                  url: '/ts-basics-bottom/employee/getEmployeePageList',
                  datatype: 'POST', // 请求方式
                  searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                  textName: 'employeeName', 
                  valName: 'employeeId', 
                  inpTextName: 'fkDeptName', 
                  inpValName: 'fkDeptManId', 
                  condition: 'employeeName',
                  required: 'none',
                  layout: 'concat',
                  labelConcatList: 'orgName',
                  data: {
                    orgId: treeNode.id,
                    pageSize: 10
                  },
                  callback: function (res) {
                    if (res) {
                    }
                  }
                };
                new $.selectPlug(`#orderInfoDeskDiv #${fkDeptManId}`, options);
              }
            },
            callback: function () {
              $(`#orderInfoDeskDiv [depIdVal="fkDeptId${index}"]`).val('');
              let options = {
                url: '/ts-basics-bottom/employee/getEmployeePageList',
                datatype: 'POST', // 请求方式
                searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                textName: 'employeeName', 
                valName: 'employeeId', 
                inpTextName: 'fkDeptName', 
                inpValName: 'fkDeptManId', 
                condition: 'employeeName',
                required: 'none',
                layout: 'concat',
                labelConcatList: 'orgName',
                data: {
                  pageSize: 10
                },
                callback: function (res) {
                  if (res) {
                  }
                }
              };
              new $.selectPlug(`#orderInfoDeskDiv #${fkDeptManId}`, options);
            }
          });
          $(`#orderInfoDeskDiv [depNameVal="fkDeptName${index}"]`).bind(
            'input propertychange',
            function (e) {
              $(`#orderInfoDeskDiv [depIdVal="fkDeptId${index}"]`)[0].value = '';
            }
          );
          $(`#orderInfoDeskDiv [depNameVal="fkDeptName${index}"]`).bind(
            'blur',
            function () {
              $(`#orderInfoDeskDiv [depIdVal="fkDeptId${index}"]`)[0].value
                ? null
                : ($(`#orderInfoDeskDiv [depNameVal="fkDeptName${index}"]`)[0].value = '');
            }
          );
        });
        //渲染处理人
        let workSheetSearch1fkUserName = {
          url: '/ts-worksheet/workSheetPeopple/getPeopleInfoList',
          datatype: 'POST', // 请求方式
          searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
          textName: 'name', 
          valName: 'userId', 
          inpTextName: 'fkUserName', 
          inpValName: 'userId', 
          condition: 'employeeName',
          layout: 'concat',
          labelConcatList: 'deptName',
          data: {
            pageSize: 10
          },
          callback: function (res) {
          }
        };
        $('#orderInfoDeskDiv [render="handler"]').each(function (index, item) {
          new $.selectPlug(
            "#orderInfoDeskDiv [handler='" + index + "']",
            JSON.parse(JSON.stringify(workSheetSearch1fkUserName))
          );
        });

        $.ajax({
          url: '/ts-basics-bottom/dictItem/getDictItemByTypeCode',
          type: 'GET',
          data: {typeCode: 'WORK_HOURS'},
          success: function (res) {
            if (!res.success || !Array.isArray(res.object)) {
              return;
            }

            let workStatusOptions = '<option value="">全部</option>';
            res.object.forEach(item => {
              workStatusOptions += `<option value="${item.itemNameValue}">${item.itemName}</option>`;
            });
            $('#orderInfoDeskDiv [name="workStatus"]').each(function (index, item) {
              $(item).append(workStatusOptions);
            });

            form.render();
          }
        });

        //添加搜索点击事件
        $('#orderInfoDeskDiv [btnType="searchBtn"]').each(function (index, item) {
          let btnIndex = index + 1;
          $(
            `#orderInfoDeskDiv #orderInfo${btnIndex}-searchBtn, #orderInfoDeskDiv #orderInfo${btnIndex}-searchBtnHidden`
          )
            .off('click')
            .bind('click', e => {
              let data = Object.assign(
                form.val(`orderInfo${btnIndex}`),
                form.val(`orderInfo${btnIndex}-hidden`)
              );
              delete data.fkUserFullName;
              delete data.repairName;
              delete data.repairManDeptName;
              filterParams(data);
              queryData = data;
              switch (btnIndex) {
                case 1:
                  handleTable.refresh();
                  break;
                case 2:
                  unbuildTable.refresh();
                  break;
                case 3:
                  finishedTable.refresh();
                  break;
                case 4:
                  recordTable.refresh();
                  break;
                case 5:
                  dispatchTable.refresh();
                  break;
                case 6:
                  allTable.refresh();
                  break;
                default:
                  break;
              }
              // searchedList[index] = 1;
            });
        });
        //添加重置事件
        $('#orderInfoDeskDiv [btntype="resetBtn"]').each(function (index, item) {
          let btnIndex = index + 1;
          $(`#orderInfoDeskDiv #orderInfo${btnIndex}-reset, #orderInfoDeskDiv #orderInfo${btnIndex}-refresh`).funs(
            'click',
            e => {
              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-searchFormHidden`)[0] &&
              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-searchFormHidden`)[0].reset();
              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-searchForm`)[0].reset();
              $(
                `#orderInfoDeskDiv #orderInfo${btnIndex}-fkUserName input, #orderInfoDeskDiv #orderInfo${btnIndex}-repairName input`
              ).val(''); //清空数据

              //强行清除缓存
              $(
                `#orderInfoDeskDiv #orderInfo${btnIndex}-fkUserName .SelectPullDown, #orderInfoDeskDiv #orderInfo${btnIndex}-repairName .SelectPullDown`
              ).addClass('SelectPullDownId'); //添加上类名
              $(
                `#orderInfoDeskDiv #orderInfo${btnIndex}-fkUserName .SelectPullDownId, #orderInfoDeskDiv #orderInfo${btnIndex}-repairName .SelectPullDownId`
              ).trigger('keyup'); //触发事件，清空缓存数据
              $(
                `#orderInfoDeskDiv #orderInfo${btnIndex}-fkUserName .SelectPullDownId, #orderInfoDeskDiv #orderInfo${btnIndex}-repairName .SelectPullDownId`
              ).removeClass('SelectPullDownId'); //清除添加的类名，避免造成影响

              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-repairManDeptId`).val('');
              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-fkDeptId`).val('');
              form.render();
              $(`#orderInfoDeskDiv #orderInfo${btnIndex}-searchBtn`).trigger('click');
              // searchedList[index] = 0;
            }
          );
        });

        /**@desc 添加工单状态多选 */
        new $.checkSelect('#orderInfoDeskDiv #orderStatus', {
            datatype: 'local',
            data: [
              {
                label: '待接单',
                value: '2'
              },
              {
                label: '处理中',
                value: '3'
              },
              {
                label: '待验收',
                value: '4'
              },
              {
                label: '已暂停',
                value: '7'
              }
            ],
            label: 'label',
            value: 'value',
            default: [
              {
                label: '待接单',
                value: '2'
              },
              {
                label: '处理中',
                value: '3'
              },
              {
                label: '已暂停',
                value: '7'
              }
            ],
            callback: function (res) {
              let dom = $('#orderInfoDeskDiv [name="workStatusG"]')
              if(!res || !res.length){
                dom.val('')
              }else {
                dom.val(res.map(item=>item.value).join(','))
              }
            }
        })
        $('#orderInfoDeskDiv [name="workStatusG"]').val('2,3,7')
      });
    }

    //过滤掉空属性
    function filterParams (obj) {
      for (let key in obj) {
        if (obj[key] == '') {
          obj[key] = null;
        }
      }
    }

    //计算故障描述
    function computedFaultDeion (cell, opt, row) {
      let html = `<p style="cursor: pointer;">`;
      if (cell) {
        let isEmergency = row.faultEmergency < 3 ? true : false,
          isReback =
            (row.backUserName ? true : false) && row.workStatusName == '待派单',
          isNotHandled =
            (row.noPassCounts > 0 ? true : false) &&
            row.workStatusName == '处理中';
        
        if(row.workSheetType == 5){
          let url = encodeURI('/view-new/processView/see/index.html?' + 'workflowNo=' + row.workflowNo + '&businessId=' + row.lbusinessId + '&wfInstanceId=' + row.workflowInstId + '&currentStepNo=end' + '&role=deal&type=see');
          html += `<a class="come-from-tech" target="_blank" href="${url}" >
            <img src="../../static/img/other/from_process.svg" style="height: 22px;" />  
          </a>`
        }
        
        html += 
          (isEmergency
            ? row.faultEmergency == 2
              ? `<span
                    title="影响范围：${affectScope[row.faultAffectScope]}"
                    style="
                      display: inline-block; 
                      background: #FFEFDF;
                      border-radius: 9px;
                      border: 1px solid #FF6010;
                      color:#FF6010; 
                      line-height: 17px;
                      font-size: 12px;
                      padding: 0 9px;
                      "
                  >比较急</span>`
              : `<span 
                    title="影响范围：${affectScope[row.faultAffectScope]}"
                    style="
                      line-height: 17px;
                      display: inline-block; 
                      background: #FFEEEF;
                      border-radius: 9px;
                      border: 1px solid #F93A4A;
                      font-size: 12px;
                      color:#F93A4A;
                      padding: 0 9px;
                      "
                  >非常紧急</span>`
            : '') +
              (isReback
                ? `<span
                      title="退回${row.backRemark ? '，原因：' + row.backRemark : ''}"
                      style="
                        color: #999999;
                        line-height: 17px;
                        background: #F5F5F5;
                        border-radius: 9px;
                        border: 1px solid #999999;
                        font-size: 12px;
                        padding: 0 9px;
                        "
                    >退回</span>`
                : '') +
                (isNotHandled
            ? `<span
                  title="提单人退回${row.noPassCounts}次"
                  style="
                    color: #999999;
                    line-height: 17px;
                    background: #F5F5F5;
                    border-radius: 9px;
                    border: 1px solid #999999;
                    font-size: 12px;
                    padding: 0 9px;
                    "
                >打回</span>`
            : '') +
          `<span 
              class="dealLink"
              name="openFaultAllInfo" 
              data-rowId="${opt.rowId}"
              title="${cell}"
              >
              ${cell}</span></p>`;
      }
      return html;
    }

    //渲染通话录音
    function renderCustomAudio (cell, opt, row) {
      let html = '';
      if (row.fileCount > 1) {
        html = `<span
        name="showOtherAudio"
        data-row="${cell}"
        style="text-decoration: underline; color: #4395ff; cursor: pointer;"
        >
        共有${row.fileCount}条录音</span>`;
      } else {
        if (cell) {
          let allTime = row.duration,
            second = allTime % 60,
            minute = parseInt((allTime % 3600) / 60),
            hour = parseInt(allTime / 3600 / 60);

          let time =
            (hour ? (hour > 9 ? `${hour}:` : `0${hour}:`) : '') +
            `${minute > 9 ? minute : '0' + minute}:${
              second > 9 ? second : '0' + second
            }`;

          html = `
            <div data-index="${opt.rowId}"  
              data-time="${allTime}" class="custom-audio-content">
              <div style="display: flex; align-items: center;">
                <img
                  src="/static/img/other/icon_work_order_bofang.png"
                  data-type="bofang"
                  data-index="${opt.rowId}"
                  name="custom-audio-btn" 
                />

                <img
                  src="/static/img/other/icon_work_order_zanting.png" 
                  style="display: none;"
                  data-type="zanting"
                  data-index="${opt.rowId}"
                  name="custom-audio-btn" 
                />

                <div style="position: relative;">
                  <span data-time="0">${
            allTime >= 3600 ? '00:00:00' : '00:00'
          }</span>
                  /
                  <span>${time}</span>
                  <div data-index="${opt.rowId}" 
                    class="custom-audio-timeline" 
                    style=" width: 160px; height: 7px; bottom: -5px;">
                    <div 
                      name="percent"
                      style="background-color: #5260ff; height: 2px; border-radius: 2px; width: 0; position: absolute;"
                      ></div>
                    <div style="
                      height: 2px;
                      border-radius: 2px;
                      background-color: #CCC;
                    "></div>
                    <div class="play-dot"></div>
                  </div>
                </div>
              </div>
            </div>
          `;
        }
      }
      return html;
    }

    //处理渲染待派单页面
    function handleRenderWatingPage () {
      if (!$('.desk-options-item-text[data-index="0"]').length) {
      }
      setTimeout(() => {
        if (!$('.desk-options-item-text[data-index="0"]').length) {
        }


      })
    }
  }
});
