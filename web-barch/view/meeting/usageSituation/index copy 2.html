<style>
    #usageSituation .jqTable shell-layer-table-page .ui-jqgrid {
        left: 0;
        width: 100% !important;
    }

    #usageSituation .changeDay {
        cursor: pointer;
        margin: 0 10px;
    }

    #usageSituation .changeDay:hover {
        color: #5260ff;
    }

    #usageSituation .localDay {
        padding: 0 10px;
        border: 1px solid #ddd;
        line-height: 28px;
        border-radius: 4px;
        cursor: pointer;
    }

    #usageSituation .localDay:hover {
        color: #5260ff;
        border-color: #5260ff;
    }

    #usageSituation .localDay.active {
        background-color: #5260ff;
        color: #fff;
        border-color: #5260ff;
    }

    #usageSituation .color_bg {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: #ac92ec;
        border: 1px solid #dddddd;
        vertical-align: top;
        margin-top: 7px;
        margin-left: 20px;
        margin-right: 5px;
    }

    #usageSituation .weekTable table {
        color: #333;
        table-layout: fixed;
    }

    #usageSituation .weekTable table th,
    #usageSituation .weekTable table td {
        border: 1px solid #eee;
    }

    #usageSituation .weekTable table th {
        height: 38px;
        font-weight: 400 !important;
    }

    #usageSituation .weekTable table table td {
        height: 41px;
    }

    #usageSituation .room {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #333;
        background-color: #f5f5f5;
        border-bottom: 1px solid #eee;
        text-indent: 20px;
        font-weight: 400;
        cursor: pointer;
    }

    #usageSituation .day-room {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #333;
        background-color: #f5f5f5;
        border-bottom: 1px solid #eee;
        text-indent: 20px;
        font-weight: 400;
        cursor: pointer;
    }

    #usageSituation .day-room:hover {
        background-color: #edefff;
    }

    #usageSituation .day-room.active {
        background-color: #edefff;
    }

    #usageSituation .room:hover {
        background-color: #edefff;
    }

    #usageSituation .room.active {
        background-color: #edefff;
    }

    #usageSituation .meets {
        vertical-align: top;
    }

    #usageSituation .meets .meeting {
        height: 22px;
        line-height: 22px;
        margin-bottom: 1px;
        font-size: 14px;
        color: #666;
        padding: 0 10px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    #usageSituation .selTime {
        background-color: #edefff;
    }

    #usageSituation .meets .onUse {
        background-color: #ac92ec;
    }

    #usageSituation .meets .onOrder {
        background-color: #4fc1e9;
    }

    #usageSituation .onUse {
        background-color: #ac92ec;
    }

    #usageSituation .onOrder {
        background-color: #4fc1e9;
    }

    #usageSituation #weekBody,
    #usageSituation #dayBody {
        user-select: none;
    }

    .roomDetail {
        position: fixed;
        top: 200px;
        left: 320px;
        z-index: 150;
        box-sizing: border-box;
        padding: 10px;
        background-color: #404040;
        background-color: rgba(0, 0, 0, 0.75);
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        width: 300px;
        height: 275px;
        font-size: 12px;
    }

    .roomDetail:after {
        position: absolute;
        content: '';
        top: 50%;
        left: -5px;
        margin-top: -5px;
        border-right: 5px solid #404040;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
    }

    .roomDetail .img_bg {
        height: 120px;
        background-color: #f5f5f5;
        border-radius: 2px;
        text-align: center;
        line-height: 100px;
        color: #333;
        border: 1px solid transparent;
    }

    .roomDetail .img_bg .img {
        height: 84px;
        margin-top: 20px;
        width: 100%;
        background-image: url(/static/img/other/meet_01.png);
        background-repeat: no-repeat;
        background-size: auto 80px;
        background-position: center;
    }

    .roomDetail .img_bg .state_tit {
        line-height: 20px;
        text-align: left;
        text-indent: 20px;
        position: relative;
        top: -10px;
        display: none;
    }

    .roomDetail .img_bg .state_tit:after {
        position: absolute;
        content: '';
        height: 6px;
        width: 6px;
        background-color: #666;
        border-radius: 50%;
        left: 10px;
        top: 8px;
    }

    .roomDetail.isNormal .img_bg .img {
        background-image: url(/static/img/other/meet_02.png);
    }

    .roomDetail.isNormal .img_bg .state_tit.isNormal {
        display: block;
        color: #5260ff;
    }

    .roomDetail.isNormal .img_bg .state_tit.isNormal:after {
        background-color: #5260ff;
    }

    .roomDetail.isStop .img_bg .state_tit.isStop {
        display: block;
        color: #666;
    }

    .roomDetail.isStop .img_bg .state_tit.isStop:after {
        background-color: #666;
    }

    .roomDetail .meet-con {
        margin-top: 10px;
        color: #fff;
        line-height: 18px;
    }
</style>
<div class="content-box scrollbar-box-y" id="usageSituation">
    <div class="oa-nav-search">
        <div class="shell-search-box oa-btn-nav" style="width: 71px">
            <span class="oa-btn-nav_item active">日</span>
            <span class="oa-btn-nav_item">周</span>
        </div>
        <div class="shell-search-box dayCon none" style="color: #666; font-weight: 400; width: 180px">
            <i class="layui-icon layui-icon-left changeDay" id="prev-date"></i>
            <span id="day-year">2020</span>
            年
            <span id="day-month">11</span>
            月
            <span id="day-date">2</span>
            日
            <i class="layui-icon layui-icon-right changeDay" id="next-date"></i>
        </div>
        <div class="shell-search-box weekCon" style="color: #666; font-weight: 400">
            <i class="layui-icon layui-icon-left changeDay" id="prev-week"></i>
            <span id="begin-year">2020</span>
            年
            <span id="begin-month">11</span>
            月
            <span id="begin-date">2</span>
            日
            <span style="padding: 0 4px">-</span>
            <span id="end-year">2020</span>
            年
            <span id="end-month">11</span>
            月
            <span id="end-date">2</span>
            日
            <i class="layui-icon layui-icon-right changeDay" id="next-week"></i>
        </div>
        <div class="shell-search-box">
            <span class="localDay dayCon none" id="localDay">本日</span>
            <span class="localDay weekCon" id="localWeek">本周</span>
        </div>
        <div class="shell-search-box" style="color: #666; font-size: 12px">
            <i class="color_bg"></i>
            占用
            <i class="color_bg" style="background-color: #4fc1e9"></i>
            预定中
            <i class="color_bg" style="background-color: #fff"></i>
            空
            <!-- <i class="color_bg" style="background-color: #f5f5f5;"></i>停用 -->
        </div>
        <div class="shell-search-box" style="margin-left: 20px; color: #666; font-size: 12px">单位:时</div>
        <div class="fr" style="color: #666666">
            当前可用会议室:(
            <span style="color: #4876ff" id="roomAvailableNumber">0</span>
            )
            <button class="layui-btn layui-btn-normal" id="meetingApproval" style="margin-left: 10px">会议室预约</button>
        </div>
    </div>
    <div class="weekTable" style="margin-top: -1px">
        <table style="width: 100%; border-collapse: collapse">
            <thead>
                <tr>
                    <th style="width: 299px">

                        <button class="layui-btn layui-btn-normal" id="meetingSubscribe" style="margin-left: 10px">订阅管理</button>

                    </th>
                    <th class="weekHead">
                        <span id="weekHead-0" style="margin-right: 5px"></span>
                        星期一
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-1" style="margin-right: 5px"></span>
                        星期二
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-2" style="margin-right: 5px"></span>
                        星期三
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-3" style="margin-right: 5px"></span>
                        星期四
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-4" style="margin-right: 5px"></span>
                        星期五
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-5" style="margin-right: 5px"></span>
                        星期六
                    </th>
                    <th class="weekHead">
                        <span id="weekHead-6" style="margin-right: 5px"></span>
                        星期日
                    </th>
                    <th class="dayHead none" style="width: 224px">00:00-07:59</th>
                    <th class="dayHead none">8:00</th>
                    <th class="dayHead none">9:00</th>
                    <th class="dayHead none">10:00</th>
                    <th class="dayHead none">11:00</th>
                    <th class="dayHead none">12:00</th>
                    <th class="dayHead none">13:00</th>
                    <th class="dayHead none">14:00</th>
                    <th class="dayHead none">15:00</th>
                    <th class="dayHead none">16:00</th>
                    <th class="dayHead none">17:00</th>
                    <th class="dayHead none">18:00</th>
                    <th class="dayHead none">19:00</th>
                    <th class="dayHead none">20:00</th>
                    <th class="dayHead none" style="width: 84px">21:00-23:59</th>
                </tr>
            </thead>
            <tbody id="weekBody"></tbody>
            <tbody class="none" id="dayBody"></tbody>
        </table>
    </div>
    <div>
        <div class="jqTable shell-layer-table-page">
            <table id="meetingDetailListTable"></table>
            <div id="meetingDetailListPage"></div>
        </div>
    </div>
    <div class="roomDetail isNormal none">
        <div class="img_bg">
            <div class="img"></div>
            <p class="state_tit isNormal">
                <span></span>
                当前正常
            </p>
            <p class="state_tit isStop">
                <span></span>
                当前停用
            </p>
        </div>
        <div class="meet-con">
            <p>
                会议室名称：
                <span id="meet-name"></span>
            </p>
            <p>
                楼栋楼层：
                <span id="meet-location"></span>
            </p>
            <p>
                资源类型：
                <span id="meet-video"></span>
            </p>
            <p>
                会议室容量：
                <span id="meet-capacitance"></span>
            </p>
            <p>
                管理员：
                <span id="meet-manageOrgName"></span>
            </p>
            <p>
                会议室设备：
                <span id="meet-hardware"></span>
            </p>
        </div>
    </div>
</div>
