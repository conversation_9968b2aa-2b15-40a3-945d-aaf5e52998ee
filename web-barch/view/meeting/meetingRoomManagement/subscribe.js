"use strict";
define(function (require, exports, module) {
	exports.init = function (opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {

			var form = layui.form,
				laydate = layui.laydate,
				trasen = layui.trasen,
				upload = layui.upload;
			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['1000px', '650px'],
				skin: 'yourclass',
				content: html,
				success: function (layero, index) {
					form.render();
				}
			});
		})
	};

});