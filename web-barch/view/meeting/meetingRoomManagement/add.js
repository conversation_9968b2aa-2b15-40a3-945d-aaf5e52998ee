"use strict";
define(function (require, exports, module) {
	exports.init = function (opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {

			var form = layui.form,
				laydate = layui.laydate,
				trasen = layui.trasen,
				upload = layui.upload;

			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['800px', '650px'],
				skin: 'yourclass',
				content: html,
				success: function (layero, index) {
					form.render();
				}
			});

			form.on('checkbox(needCheckup)', function (data) {
				if (data.elem.checked == true) {
					data.elem.value = "1";
				} else {
					data.elem.value = "0";
				}
			});
			upload.render({
				elem: '#evaBaseAddForm #meeting-img',
				url: common.url + '/ts-document/attachment/fileUpload?module=meeting',
				accept: 'file',
				accept: 'image',
				before: function (res) {},
				done: function (res, index, upload) {
					if (res.success) {
						$("#evaBaseAddForm [name='emphasis']").val(res.object[0].filePath);
						$('#evaBaseAddForm #meeting-img').attr('src', '/ts-document/attachment/' + res.object[0].filePath)
					} else {
						layer.msg("操作错误.");
					}
				},
				error: function (index, upload) {}
			});

			//管理员
			$('#meetingBoardroomManage_SelUser').funs('click', function () {
				var data = {
					isCheckDept: 'N',
					user_str: 'meetingBoardroomManage_SelUser',
					user_id: "meetingBoardroomManageSelUserId",
					user_code: "meetingBoardroomManageSelUserCode",
					dept_code: "meetingBoardroomManageSelDeptCode"
				};
				$.quoteFun('/common/userSel', {
					trasen: trasenTable,
					title: '人员选择',
					data: data
				});
			})

			//使用权限
			$('#meetingRoomManagementSelUser').funs('click', function () {
				var data = {
					isCheckDept: 'Y',
					user_str: 'meetingRoomManagementSelUser',
					user_id: "meetingRoomManagementSelUserId",
					user_code: "meetingRoomManagementSelUserCode",
					dept_code: "meetingRoomManagementSelDeptCode"
				};
				$.quoteFun('/common/userSel', {
					trasen: trasenTable,
					title: '人员选择',
					data: data
				});
			})

			// 保存
			form.on('submit(meetingRoomManagementSubmitCofirm)', function (data) {
				var d = data.field;
				var url;
				if (d.id) {
					url = '/ts-resource/boardroom/place/update';
				} else {
					url = '/ts-resource/boardroom/place/save';
				}
				if (!d.id) {
					delete d.id;
				}
				$.loadings();
				$.ajax({
					type: "post",
					url: common.url + url,
					data: d,
					success: function (res) {
						$.closeloadings();
						if (res.success) {
							layer.closeAll();
							layer.msg('操作成功');
							opt.ref();
						} else {
							layer.closeAll();
							layer.msg('操作失败');
							opt.ref();
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
			});

			//编辑时数据回显(有id代表是编辑)
			if (opt.boardroomId) {
				edit(opt.boardroomId, form);
			}


			//对应流程 下拉赋值 (会议室流程 cloudTemplateId = 4)
			$.ajax({
				type: "post",
				url: common.url + '/ts-workflow/workflow/definition/findByWfInst',
				contentType: "application/json;charset=UTF-8",
				data: JSON.stringify({
					cloudTemplateId: 4
				}),
				async: false,
				success: function (res) {
					if (res.object) {
						var dataList = res.object;
						var postType = ' <option value="">请选择</option>';
						if (opt.data) { //修改
							for (var i = 0; i < dataList.length; i++) {
								if (opt.data.processId == dataList[i].wfDefinitionId) {
									postType += '<option  value="' + dataList[i].workflowNo + '" selected="selected">' + dataList[i].workflowName + '</option>';
								} else {
									postType += '<option value="' + dataList[i].workflowNo + '">' + dataList[i].workflowName + '</option>';
								}
							}
						} else { //增加
							for (var i = 0; i < dataList.length; i++) {
								postType += '<option value="' + dataList[i].workflowNo + '">' + dataList[i].workflowName + '</option>';
							}
						}
						$('#defaultProcessid').html(postType);
						form.render();
					}
				}
			});

		})
	};



	// 编辑时查询数据
	function edit(boardroomId, form) {
		$.ajax({
			type: "post",
			url: common.url + "/ts-resource/boardroom/place/find/" + boardroomId,
			data: null,
			success: function (res) {
				if (res.success) {
					// 回显数据
					$('#evaBaseAddForm input[name="id"]').val(res.object.id);
					$('#evaBaseAddForm input[name="name"]').val(res.object.name);
					$('#evaBaseAddForm input[name="location"]').val(res.object.location);
					$('#evaBaseAddForm input[name="capacitance"]').val(res.object.capacitance);
					$('#evaBaseAddForm input[name="domainId"]').val(res.object.domainId);
					$('#evaBaseAddForm input[name="defaultProcessid"]').val(res.object.defaultProcessid);
					$('#evaBaseAddForm input[name="emphasis"]').val(res.object.emphasis);
					if (res.object.emphasis) {
						$('#evaBaseAddForm #meeting-img').attr('src', '/ts-document/attachment/' + res.object.emphasis)
					}
					echoCheckbox(res.object, form); // 回显复选框
					$('#meetingRoomManagementSelUser').val(res.object.useAuthority);
					$('#evaBaseAddForm input[name="boardroomAdminUserCode"]').val(res.object.boardroomAdminUserCode);
					$('#evaBaseAddForm input[name="boardroomAdminDeptCode"]').val(res.object.boardroomAdminDeptCode);
					$('#meetingBoardroomManage_SelUser').val(res.object.manageAuthority);
					$('#evaBaseAddForm input[name="manageUserCode"]').val(res.object.manageUserCode);
					$('#evaBaseAddForm input[name="manageUserDept"]').val(res.object.manageUserDept);
					$("#defaultProcessid").val(res.object.defaultProcessid);
					form.render();
				} else {
					layer.msg("查询失败");
				}
			},
			error: function (res) {
				res = JSON.parse(res.responseText);
				layer.msg(res.message);
			}
		});
	}

	//回显复选框
	function echoCheckbox(obj, form) {
		if (obj.projector == 1) {
			$('input[name="projector"]').prop('checked', true);
		} else {
			$('input[name="projector"]').prop('checked', false);
		}
		if (obj.computer == 1) {
			$('input[name="computer"]').prop('checked', true);
		} else {
			$('input[name="computer"]').prop('checked', false);
		}
		if (obj.whiteboard == 1) {
			$('input[name="whiteboard"]').prop('checked', true);
		} else {
			$('input[name="whiteboard"]').prop('checked', false);
		}
		if (obj.voicetube == 1) {
			$('input[name="voicetube"]').prop('checked', true);
		} else {
			$('input[name="voicetube"]').prop('checked', false);
		}
		if (obj.airconditioner == 1) {
			$('input[name="airconditioner"]').prop('checked', true);
		} else {
			$('input[name="airconditioner"]').prop('checked', false);
		}
		if (obj.heating == 1) {
			$('input[name="heating"]').prop('checked', true);
		} else {
			$('input[name="heating"]').prop('checked', false);
		}
		$('select[name="domainId"]').val(obj.domainId);
		$("input[name='isVideo'][value=" + obj.isVideo + "]").attr("checked", true);
		form.render();
	}
});