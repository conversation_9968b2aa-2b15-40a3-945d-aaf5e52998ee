<style type="text/css">
    .myAgenda-timeSelect {
        position: relative;
    }

    .myAgenda-timeSelect .myAgenda-time-item {
        color: #333;
        width: 30px;
        height: 28px;
        line-height: 28px;
        border: 1px solid #dcdcdc;
        text-align: center;
        padding: 0 3px;
        border-radius: 3px;
        display: inline-block;
        cursor: pointer;
    }

    .myAgenda-timeSelect .myAgenda-calendar-picker {
        color: #666;
        display: flex;
        position: absolute;
        top: 50%;
        left: 50%;
    }
    .myAgenda-timeSelect .myAgenda-calendar-picker i {
        cursor: pointer;
    }
    .myAgenda-timeSelect .myAgenda-calendar-picker i:hover {
        color: #5260ff;
    }
</style>
<div class="content-box" id="myAgenda">
    <div class="oa-nav">
        <a href="javascript:;" class="oa-nav_item active" value="0">待开始</a>
        <a href="javascript:;" class="oa-nav_item" value="1">进行中</a>
        <a href="javascript:;" class="oa-nav_item" value="-1">已结束</a>
        <a href="javascript:;" class="oa-nav_item" value="">全部</a>
    </div>
    <div class="oa-nav-search">
        <div class="myAgenda-timeSelect">
            <div class="shell-search-box oa-btn-nav">
                <span class="oa-btn-nav_item active" date-type="month">月</span>
                <span class="oa-btn-nav_item" date-type="all">全部</span>
            </div>

            <div class="myAgenda-calendar-picker">
                <i class="layui-icon layui-icon-left myAgenda-bforeBtn"></i>
                <font id="myAgendaCalendar" style="margin: 0 10px"></font>
                <i class="layui-icon layui-icon-right myAgenda-afterBtn"></i>
            </div>
        </div>
    </div>
    <div class="trasen-con-box" style="">
        <div class="table-box">
            <table id="myAgendaTable"></table>
            <!-- 分页 -->
            <div id="myAgendaPage"></div>
        </div>
    </div>
</div>
