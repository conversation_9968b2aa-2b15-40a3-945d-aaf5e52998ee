"use strict";

define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }

    //索引树
    var perform = function() {
        let API = {
            getKnowledgeTree: '/ts-worksheet/knowledgeType/getKnowledgeTypeTreeAllList',//获取知识库列表树
        };

        layui.use(['form', 'trasen', 'zTreeSearch'], function() {
            var form = layui.form,
                layer = layui.layer,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;
            var curentIndex = 0
            var zIndex = 11
            //  左侧知识类型树列表
            documentTree();
            function documentTree() {
                trasen.ztree('#orderKnowledgeBaseTree', {
                    url: common.url + "/ts-basics-bottom/jobtitleBasic/getJobtitleTree",
                    type: 'post',
                    checkbox: false,
                    open: 'all',
                    zTreeOnClick: function(event, treeId, treeNode) {
                        $("#jobtitleBasicPid").val(treeNode.id);
                        refreshTable();
                    }
                });
            }

            //  索引树
            $(document).off('input', '#orderKnowledgeBaseInput').on('input', '#orderKnowledgeBaseInput', function() {
                var treeObj = $.fn.zTree.getZTreeObj("orderKnowledgeBaseTree");
                var val = $(this).val();
                setTimeout(function() {
                    $.fn.ztreeQueryHandler(treeObj, val);
                }, 200);
            });


            //表格
            var SysGropuTable = new $.trasenTable('knowledgeBaseTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#knowledgeBaseTablePage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '知识主题',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '处理工时',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                    },
                    {
                        label: '有用次数',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '贡献人',
                        sortable: false,
                        name: 'createUserName',
                        width: 130,
                        align: 'left',
                    },
                    {
                        label: '贡献时间',
                        sortable: false,
                        name: 'createDate',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }

            //表格
            var SysGropuTable = new $.trasenTable('examineTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#examineTablePage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '知识主题',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '处理工时',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                    },
                    {
                        label: '有用次数',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '贡献人',
                        sortable: false,
                        name: 'createUserName',
                        width: 130,
                        align: 'left',
                    },
                    {
                        label: '贡献时间',
                        sortable: false,
                        name: 'createDate',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }

            //未通过表格
            var SysGropuTable = new $.trasenTable('failedTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#failedTablePage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '知识主题',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '不通过原因',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                    },
                    {
                        label: '贡献人',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '提交时间',
                        sortable: false,
                        name: 'createUserName',
                        width: 130,
                        align: 'left',
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }

            //表格
            var SysGropuTable = new $.trasenTable('removedTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#removedTablePage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '知识主题',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '移除原因',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                    },
                    {
                        label: '有用次数',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '移除时间',
                        sortable: false,
                        name: 'createUserName',
                        width: 130,
                        align: 'left',
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }

            //表格
            var SysGropuTable = new $.trasenTable('draftsTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#draftsTablePage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '知识主题',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '处理工时',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                    },
                    {
                        label: '创建时间',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }
            //点击按钮切换
            $('.head-name').click(function (e){
                var clickIndex = e.target.getAttribute('data-index')
                if(curentIndex == clickIndex)
                    return
                $('.head-name')[curentIndex].className = 'head-name'
                $('.head-name')[clickIndex].className += ' head-name-active'
                curentIndex = clickIndex
                // $('.trasen-con-box')[zIndex].css('z-index', '15')
                // $('.head-name')[clickIndex].css('z-index', '11')
                switch (parseInt(clickIndex) ){
                    case 0:
                        $('#orderKnowledgeBaseTable0').css('z-index', zIndex)
                        break;
                    case 1:
                        $('#orderKnowledgeBaseTable1').css('z-index', zIndex)
                        break;
                    case 2:
                        $('#orderKnowledgeBaseTable2').css('z-index', zIndex)
                        break;
                    case 3:
                        $('#orderKnowledgeBaseTable3').css('z-index', zIndex)
                        break;
                    case 4:
                        $('#orderKnowledgeBaseTable4').css('z-index', zIndex)
                        break;
                }
                zIndex++
            })
        });
    }


});
