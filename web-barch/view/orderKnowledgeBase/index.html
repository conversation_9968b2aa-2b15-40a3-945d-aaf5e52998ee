<style>
    #orderKnowledgeBase{
        width: 100%;
        height: 100%;
    }

    #orderKnowledgeBase .flex-row{
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        height: 30px;
        line-height: 30px;
        /* padding: 8px 0px; */
        padding-bottom: 8px;
        background-color: #fff;
        border-bottom: 1px solid #eee;
        position: relative;
    }

    #orderKnowledgeBase .flex-column{
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
    }
    #orderKnowledgeBase .left-box{
        float: left;
        width: 200px;
        height: 100%;
        background-color: #FFFFFF;
    }

    #orderKnowledgeBase .head{
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        width: 450px;
        height: 36px;
        margin-left: 8px;
        font-size: 14px;
        background-color: #E8ECF2;
    }

    #orderKnowledgeBase .head-name{
        font-weight: 400;
        margin: 2px 1px 2px 1px;
        color: #333333;
        width: 90px;
        height: 32px;
        text-align: center;
        line-height: 32px;
    }

    /*#orderKnowledgeBase .head-name:active{*/
    /*    color: rgb(2, 167, 240);*/
    /*}*/

    #orderKnowledgeBase .head-name-active {
        color: #5260ff !important;
        font-weight: 600 !important;
        background: #FFFFFF;
    }

    #orderKnowledgeBase .search-input{
        width: 240px;
        height: 30px !important;
        margin: 0 0 0 400px;
    }

    #orderKnowledgeBase .btn-search{
        border: none;
        margin: 0 0 0 20px;
        width: 68px;
        height: 30px;
        background: #5260ff;
        border-radius: 4px;
    }


    #orderKnowledgeBase .btn-add{
        border: none;
        margin: 0 0 0 10px;
        width: 68px;
        height: 30px;
        background: #fc7070;
        border-radius: 4px;
    }

    #orderKnowledgeBase .btn-export{
        border: none;
        margin: 0 0 0 40px;
        width: 68px;
        height: 30px;
        background: rgba(127, 127, 127, 1);
        border-radius: 4px;
    }

    #orderKnowledgeBase .table-box{
        position: absolute;
        left: 0;
        right: 0px;
        top: 0;
        bottom: 0;
        /* padding: 0 10px; */
        box-sizing: border-box;
    }

    #orderKnowledgeBase .body {
        position: absolute;
        left: 217px;
        right: 0;
        top: 0;
        bottom: 0;
        padding: 8px;
        background-color: #fff;
        border-radius: 4px;
    }
</style>
<div class="content-box bg-trans" id="orderKnowledgeBase" style="padding: 0;">
    <div class="oa-search-tree">
        <div style="padding: 5px 10px">
            <input type="text" class="layui-input" name="name" placeholder="请输入关键词搜索 " id="orderKnowledgeBaseInput" />
        </div>
        <div class="ztree-box scrollbar-box" style="top: 35px">
            <ul class="ztree" id="orderKnowledgeBaseTree"></ul>
        </div>
    </div>
    <div class="transen-con-view-box">
        <div class="flex-row">
            <div class="head">
                <div class="head-name head-name-active" data-index='0'>知识库(950)</div>
                <div class="head-name" data-index='1'>审核中(4)</div>
                <div class="head-name" data-index='2'>未通过(60)</div>
                <div class="head-name" data-index='3'>已移除(10)</div>
                <div class="head-name" data-index='4'>草稿箱(6)</div>
            </div>
            <input type="text" name="title" class="layui-input search-input" placeholder="请输入标题">
            <div class="layui-form-item search-btn-wrap">
                <button type="button" class="layui-btn btn-search" lay-submit=""
                        lay-filter="searchInfoSubmit">搜索</button>
                <button type="button" class="layui-btn btn-add" id="myorderCreaderOrderBtn">新增</button>
                <button type="button" class="layui-btn btn-export" lay-submit=""
                        lay-filter="searchInfoSubmit">导出</button>
            </div>
        </div>

        <!-- 知识库 -->
        <div class="trasen-con-box" id="orderKnowledgeBaseTable0" style="top: 50px;z-index: 10" >
            <div class="table-box">
                <!-- 表格 -->
                <table id="knowledgeBaseTable"></table>
                <!-- 分页 -->
                <div id="knowledgeBaseTablePage"></div>
            </div>
        </div>

        <!-- 审核中 -->
        <div class="trasen-con-box" id="orderKnowledgeBaseTable1" style="top: 50px;z-index: 9">
            <div class="table-box">
                <table id="examineTable"></table>
                <!-- 分页 -->
                <div id="examineTablePage"></div>
            </div>
        </div>

        <!-- 未通过 -->
        <div class="trasen-con-box" id="orderKnowledgeBaseTable2" style="top: 50px;z-index: 8">
            <div class="table-box">
                <table id="failedTable"></table>
                <!-- 分页 -->
                <div id="failedTablePage"></div>
            </div>
        </div>

        <!-- 已移除 -->
        <div class="trasen-con-box" id="orderKnowledgeBaseTable3"style="top: 50px;z-index: 7">
            <div class="table-box">
                <table id="removedTable"></table>
                <!-- 分页 -->
                <div id="removedTablePage"></div>
            </div>
        </div>

        <!-- 草稿箱 -->
        <div class="trasen-con-box" id="orderKnowledgeBaseTable4"style="top: 50px;z-index: 6">
            <div class="table-box">
                <table id="draftsTable"></table>
                <!-- 分页 -->
                <div id="draftsTablePage"></div>
            </div>
        </div>
    </div>
</div>
