'use strict';
define(function (require, exports, module) {
  var init = function () {
    return perform();
  };
  module.exports = {
    init: init,
  };
  var perform = function () {
    layui.use(['form', 'laydate', 'upload', 'trasen', 'zTreeSearch', 'treeSelect', 'element'], function () {
      var form = layui.form,
        layer = layui.layer,
        upload = layui.upload,
        laydate = layui.laydate,
        trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch,
        element = layui.element;
      $.ajax({
        url: common.url + '/ts-hrms/statisticalReport/getZJRYZCBB',
        type: 'post',
        success: function (res) {
          if (res.success) {
            let tableTr = ''
            let hideTableTr = ''
            for (const key in res.object) {
              const item = res.object[key]
              tableTr += `<tr><td style="border-right: 1px solid #8088df">${key}</td>`
              hideTableTr += `<tr><td>${key}</td>`
              for (const childKey in item) {
                const childItem = item[childKey]
                for (const realKey in childItem) {
                  const realItem = childItem[realKey]
                  let pengdingArr = []
                  let reviewStr = ''
                  let reviewDialog = false
                  let num = 5
                  if (realItem) { // 过滤掉空字符串
                    if (typeof realItem === 'string') { // 过滤掉数字类型（只保留字符串）
                      pengdingArr = realItem.split(',')
                      reviewStr = pengdingArr.slice(0, num).join(',')
                      if (pengdingArr.length > num) {
                        reviewDialog = true
                        reviewStr += '......'
                      }
                    }
                  }
                  tableTr += `
                             <td 
                              class="${reviewDialog ? 'review-dialog' : ''}" 
                              style="${realKey === '其他'? 'border-right: 1px solid #8088df' : ''}"
                              data-reviewStr="${realItem}"
                              data-key="${key}"
                              data-thkey="${childKey}"
                              data-realkey="${realKey}"
                            >
                              ${reviewStr || realItem}
                            </td>
                             `
                  hideTableTr += `<td>${realItem}</td>`
                }
              }
              tableTr += `</tr>`
              hideTableTr += `</tr>`
            }
            $('#TechnicalPersonnel #TechnicalPersonnelTableBox tbody').html(tableTr)
            $('#TechnicalPersonnel #TechnicalPersonnelHideTableBox tbody').html(hideTableTr)
          }
        }
      })
	  
    })

    //导出
    $('#TechnicalPersonnel')
      .off('click', '.review-dialog')
      .on('click', '.review-dialog', function (e) {
        e.stopPropagation();
        var key = $(this).attr('data-key');
        var thkey = $(this).attr('data-thkey');
        var realkey = $(this).attr('data-realkey');

        var reviewStr = $(this).attr('data-reviewStr');
        $.quoteFun('personnelReport/technicalPersonnel/modules/message', {
          title: `${thkey} - ${realkey} - ${key}`,
          data: {
            reviewStr
          },
        });
      });

    //导出
    $('#TechnicalPersonnel')
      .off('click', '#exportBtn')
      .on('click', '#exportBtn', function () {
        tableToExcel('TechnicalPersonnelHideTableBox', 'exportBtn');
      });

    function tableToExcel(tableid, btnname) {
      var uri = 'data:application/vnd.ms-excel;base64,',
        template =
        '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
        base64 = function (s) {
          return window.btoa(unescape(encodeURIComponent(s)));
        },
        format = function (s, c) {
          return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
          });
        };
      //根据ID获取table表格HTML
      var table = document.getElementById(tableid);
      var ctx = {
        worksheet: 'Worksheet',
        table: table.innerHTML
      };

      var alink = document.createElement('a');
      alink.href = uri + base64(format(template, ctx));
      alink.download = '专技人员职称报表.xls';
      alink.click();
    }
  };
});