"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'upload', 'trasen', 'zTreeSearch', 'treeSelect', 'element'], function() {
            var form = layui.form,
                layer = layui.layer,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch,
                element = layui.element;

            // 表格渲染
            var oldCodeName;
            
            var trasenTable;
            init();
            function init(){
            	trasenTable = new $.trasenTable("grid-table-employeeDistributionTable", {
	                url: common.url + '/ts-hrms/statisticalReport/getEmployeeDistributionReportTable',
	                pager: 'grid-pager-rlzytjbbCountPager',
//	                sortname: 't1.create_date',
					postData: {
						orgIdList: []
					},
	                autoScroll: false,
	                shrinkToFit: false,
	                colModel: [
	                    { label: '科室名称', name: 'ksmc',  width: 80, editable: false,frozen:true,align:"center" ,sortable:false},
	                    { label: '总人数', name: 'zrs',  width: 50, editable: false ,frozen:true,align:"center",sortable:false},
	                    { label: '在编', name: 'zbrs',  width: 55, editable: false ,align:"center",sortable:false},
	                    { label: '编外', name: 'bars',  width: 55, editable: false ,align:"center",sortable:false},
	                    { label: '备案制', name: 'bazrs', width: 55, editable: false ,align:"center",sortable:false},
						

						//无证	初级	中级	高级
						{ label: '无证', name: 'gwxx-1-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-1-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-1-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-1-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-1-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-1-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-1-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-1-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-1-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-1-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},
						

						{ label: '无证', name: 'gwxx-2-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-2-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-2-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-2-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-2-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-2-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-2-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-2-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-2-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-2-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-3-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-3-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-3-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-3-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-3-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-3-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-3-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-3-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-3-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-3-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-4-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-4-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-4-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-4-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-4-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-4-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-4-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-4-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-4-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-4-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-5-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-5-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-5-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-5-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-5-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-5-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-5-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-5-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-5-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-5-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-6-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-6-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-6-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-6-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-6-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-6-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-6-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-6-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-6-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-6-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-7-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-7-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-7-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-7-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-7-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-7-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-7-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-7-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-7-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-7-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-8-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-8-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-8-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-8-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-8-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-8-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-8-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-8-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-8-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-8-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-9-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-9-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-9-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-9-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-9-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-9-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-9-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-9-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-9-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-9-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},

						{ label: '无证', name: 'gwxx-10-zb-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-10-zb-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-10-zb-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-10-zb-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-10-zb-zgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '无证', name: 'gwxx-10-bw-wz', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '初级', name: 'gwxx-10-bw-cj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '中级', name: 'gwxx-10-bw-zj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '副高级', name: 'gwxx-10-bw-fgj', width: 55, editable: false ,align:"center",sortable:false},
						{ label: '正高级', name: 'gwxx-10-bw-zgj', width: 55, editable: false ,align:"center",sortable:false},
					
	                ],
	                gridComplete: completeMethod,
					queryFormId: 'employeeDistributionForm',
	                footerrow: true,
	                rownumbers:false
	            });
            	

				jQuery("#grid-table-employeeDistributionTable").jqGrid('setGroupHeaders', {
					useColSpanStyle: true,
        		    groupHeaders:[
        		    {startColumnName: 'gwxx-1-zb-wz', numberOfColumns: 10, titleText: '财务'},
					{startColumnName: 'gwxx-2-zb-wz', numberOfColumns: 10, titleText: '工勤'},
					{startColumnName: 'gwxx-3-zb-wz', numberOfColumns: 10, titleText: '管理'},
					{startColumnName: 'gwxx-4-zb-wz', numberOfColumns: 10, titleText: '护工'},
					{startColumnName: 'gwxx-5-zb-wz', numberOfColumns: 10, titleText: '护理'},
					{startColumnName: 'gwxx-6-zb-wz', numberOfColumns: 10, titleText: '临床'},
					{startColumnName: 'gwxx-7-zb-wz', numberOfColumns: 10, titleText: '信息'},
					{startColumnName: 'gwxx-8-zb-wz', numberOfColumns: 10, titleText: '药械'},
					{startColumnName: 'gwxx-9-zb-wz', numberOfColumns: 10, titleText: '医技-检验'},
					{startColumnName: 'gwxx-10-zb-wz', numberOfColumns: 10, titleText: '医技-影像'}	   
				]

        		  });


        	  jQuery("#grid-table-employeeDistributionTable").jqGrid('setGroupHeaders', {
				useColSpanStyle: true, 


					groupHeaders:[
						{startColumnName:"gwxx-1-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-1-bw-wz",numberOfColumns:5,titleText:"编外"},
						
						{startColumnName:"gwxx-2-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-2-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-3-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-3-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-4-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-4-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-5-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-5-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-6-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-6-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-7-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-7-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-8-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-8-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-9-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-9-bw-wz",numberOfColumns:5,titleText:"编外"},

						{startColumnName:"gwxx-10-zb-wz",numberOfColumns:5,titleText:"在编"},
						{startColumnName:"gwxx-10-bw-wz",numberOfColumns:5,titleText:"编外"}
					]
        		  

        		  });

				  jQuery("#grid-table-employeeDistributionTable").jqGrid('setGridParam', { rowNum: -1 }).trigger('reloadGrid');  
				  //还原成原先状态
				


	            form.render();
            }
            
            /*统计功能 */
        	function completeMethod(){
           	var data = {}; 
    	      	var colNames=$("#grid-table-employeeDistributionTable").jqGrid('getGridParam','colNames');
    			var colModel=$("#grid-table-employeeDistributionTable").jqGrid('getGridParam','colModel');
    			var table = "";
    			var newColumnName = [];
    		    var newColumnValue = [];
    		   //i =3 开始  基本工资
    			for (var i=1;i<colNames.length;i++) {
    				var columnName = colModel[i].name;
    				var count = $("#grid-table-employeeDistributionTable").getCol(columnName,false,'sum');
    				data[columnName] = count;
    			}
    			data['ksmc'] = "合计";
    			$("#grid-table-employeeDistributionTable").jqGrid("footerData", "set", data);
    			
    			  $("#employeeDistributionDiv .ui-jqgrid-sdiv").addClass('ui-jqgrid-sdiv-box');
                  $("#employeeDistributionDiv .ui-jqgrid-bdiv").addClass('ui-jqgrid-bdiv-box-bottom');
                  $("#employeeDistributionDiv .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                  $('#employeeDistributionDiv .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
        		
            }
            
            form.render(); // 渲染数据
            
            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }
            
            $(".areaButtonBoxR").off("click", "#employeeDistributionExportBtn").on("click", "#employeeDistributionExportBtn", function () {
            	layer.confirm('确定要导出人员分布情况报表吗？', {
								btn: ['确定', '取消'],
								title: '提示',
								closeBtn: 0
							}, function (index) {
								var url = common.url + "/ts-hrms/statisticalReport/exportEmployeeDistributionReportTable?";
								var queryData = trasenTable.oTable.getGridParam("postData");
								var exportParam = "";
								for (var key in queryData) {
									exportParam += (key + "=" + queryData[key] + "&")
								}
								location.href = (url + (exportParam));
								layer.close(index);
							}, function () {
							});
            });


			// 科室文档库树列表
			documentTree();

            function documentTree() {
                trasen.ztree('#employeeDistributionGroupTree', {
                    url: common.url + "/ts-basics-bottom/organization/getTree2",
                    type: 'post',
                    checkbox: true,
                    open: 'all',
                    zTreeOnCheck: function(event, treeId, treeNode) {
                    	var userTreeObj=$.fn.zTree.getZTreeObj("employeeDistributionGroupTree");
                        var userNodes=userTreeObj.getCheckedNodes(true);
                        var orgIdList = new Array;
                        $.each(userNodes,function(i,item){
                        	orgIdList.push(item.id);
                        })
						
                        $("#employeeDistributionForm #employeeDistributionOrgIdList").val(orgIdList.join(','));
                        refreshTable();
                    }
                });
            }


			
            // 科室文档库索引树
            $(document).off('input', '#employeeDistributionGroupTreeSearch').on('input', '#employeeDistributionGroupTreeSearch', function() {
                var treeObj = $.fn.zTree.getZTreeObj("employeeDistributionGroupTree");
                var val = $(this).val();
                setTimeout(function() {
                    $.fn.ztreeQueryHandler(treeObj, val);
                }, 200);
            });

        });
    }
});