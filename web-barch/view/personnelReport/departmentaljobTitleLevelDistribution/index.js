'use strict';
define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'trasen', 'zTreeSearch', 'treeSelect'], function () {
            var form = layui.form,
                layer = layui.layer,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;

            var trasenTable;
            var footerDataList;
            // 动态表格
            $.ajax({
                type: 'post',
                contentType: 'application/json; charset=utf-8',
                url: common.url + '/ts-hrms/statisticalReport/getJobTitleLevelReportTableHead',
                success: function (res) {
                    if (res.success) {
                        res.object.map(function(i){
                            i.align = "left"
                        })
                        // 表格渲染
                        trasenTable = new $.trasenTable('departmentaljobTitleLevelDistributionTable', {
                            url: common.url + '/ts-hrms/statisticalReport/getJobTitleLevelReportTableData',
                            //                            pager: 'departmentaljobTitleLevelDistributionPager',
                            postData: {
                                orgIdList: [],
                            },
                            colModel: res.object,
                            footerrow: true,
                            rowNum: 1000,
                            rowList: [1000, 1000, 1000],
                            gridComplete: function () {
                                var rows = $('#departmentaljobTitleLevelDistributionTable').jqGrid('getRowData');
                                var allCountID = $('#departmentaljobTitleLevelDistributionTable').jqGrid('getDataIDs');
                                rows.push($('#departmentaljobTitleLevelDistributionTable').jqGrid('getRowData', allCountID[allCountID.length - 1]));
                                var getNameList = {};
                                for (var i = 2; i < res.object.length; i++) {
                                    getNameList[res.object[i].name] = 0;
                                }
                                for (var i = 0, l = rows.length; i < l; i++) {
                                    for (var names in getNameList) {
                                        getNameList[names] = getNameList[names] + parseFloat(rows[i][names] || 0);
                                    }
                                }
                                getNameList['orgName'] = '合计';
                                footerDataList = getNameList;
                                $('#departmentaljobTitleLevelDistributionTable').jqGrid('footerData', 'set', getNameList);
                                $('#departmentaljobTitleLevelDistributionBox .ui-jqgrid-sdiv').addClass('ui-jqgrid-sdiv-box');
                                $('#departmentaljobTitleLevelDistributionBox .ui-jqgrid-bdiv').addClass('ui-jqgrid-bdiv-box-bottom');
                                $('#departmentaljobTitleLevelDistributionBox .ui-jqgrid-sdiv .tableSelectTd').removeClass('tableSelectTd');
                                $('#departmentaljobTitleLevelDistributionBox .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
                            },
                            queryFormId: 'departmentaljobTitleLevelDistributionForm',
                        });
                    }
                },
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 科室文档库树列表
            documentTree();

            function documentTree() {
                trasen.ztree('#jobTitleLevelRloeGroupTree', {
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    checkbox: true,
                    open: 'all',
                    /* zTreeOnClick: function(event, treeId, treeNode) {
                        if (isEmpty(treeNode.pid) || "0" == treeNode.pid) {
                            $("#departmentaljobTitleDistributionForm #jobTitleOrgIdList").val('');
                        } else {
                            $("#departmentaljobTitleDistributionForm #jobTitleOrgIdList").val(treeNode.id);
                        }
                        refreshTable();
                    },*/
                    zTreeOnCheck: function (event, treeId, treeNode) {
                        var userTreeObj = $.fn.zTree.getZTreeObj('jobTitleLevelRloeGroupTree');
                        var userNodes = userTreeObj.getCheckedNodes(true);
                        var orgIdList = new Array();
                        $.each(userNodes, function (i, item) {
                            orgIdList.push(item.id);
                        });
                        $('#departmentaljobTitleLevelDistributionForm #jobTitleLevelOrgIdList').val(orgIdList.join(','));
                        refreshTable();
                    },
                });
            }

            // 科室文档库索引树
            $(document)
                .off('input', '#jobTitleLevelRloeGroupTreeSearch')
                .on('input', '#jobTitleLevelRloeGroupTreeSearch', function () {
                    var treeObj = $.fn.zTree.getZTreeObj('jobTitleLevelRloeGroupTree');
                    var val = $(this).val();
                    setTimeout(function () {
                        $.fn.ztreeQueryHandler(treeObj, val);
                    }, 200);
                });

            // 查询
            form.on('submit(departmentaljobTitleLevelDistributionSearch)', function (data) {
                refreshTable();
            });

            // 图表
            $('#groupOrgjobTitleLevelChart')
                .off('click')
                .on('click', function () {
                    $.quoteFun('/personnelReport/departmentaljobTitleLevelDistribution/groupJobTitleLevelChart', {
                        title: '职称分布图',
                        data: footerDataList,
                    });
                });

            // 导出报表
            $('#groupOrgjobTitleLevelExport')
                .off('click')
                .on('click', function () {
                    var url = common.url + '/ts-hrms/statisticalReport/exportJobTitleLevelReport?';
                    var queryData = trasenTable.oTable.getGridParam('postData');
                    var exportParam = '';
                    for (var key in queryData) {
                        exportParam += key + '=' + queryData[key] + '&';
                    }
                    location.href = url + exportParam;
                });

            function getJQAllData() {
                //拿到grid对象
                var obj = $('#jgGridId');
                //获取grid表中所有的rowid值
                var rowIds = trasenTable.getDataIDs();
                //初始化一个数组arrayData容器，用来存放rowData
                var arrayData = new Array();
                if (rowIds.length > 0) {
                    for (var i = 0; i < rowIds.length; i++) {
                        //rowData=obj.getRowData(rowid);//这里rowid=rowIds[i];
                        arrayData.push(trasenTable.getRowData(rowIds[i]));
                    }
                }
                return arrayData;
            }
        });
    };
});
