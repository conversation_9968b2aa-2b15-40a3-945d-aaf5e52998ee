"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }

    module.exports = {
        init: init
    }

    var perform = function () {
        layui.use(['form', 'laydate'], function () {
            var form = layui.form,
            laydate = layui.laydate;
            
            var trasenTable = new $.trasenTable("grid-table-sysUsage", {
                url: common.url + '/ts-information/api/sysUsage/list',
                rowNum: 20,
                pager: 'grid-pager-sysUsage',
                mtype: 'get',
                sortname: "usageMonth desc,customer",
				sortorder: 'desc',
                shrinkToFit: true,
                colModel: [{
	                    label: '月份',
	                    sortable: true,
	                    name: 'usagemonth',
	                    index: 'usageMonth',
	                    width: 120
	                },{
                        label: '客户名称',
                        sortable: true,
                        name: 'customer',
                        index: 'customer',
                        width: 200
                    },
                    {
                        label: '员工',
                        sortable: true,
                        name: 'empnumbers',
                        index: 'empNumbers',
                        width: 80
                    },
                    {
                        label: 'PC月访问量',
                        sortable: true,
                        name: 'pcaccess',
                        index: 'pcAccess',
                        width: 80
                    },
                    {
                        label: 'PC总访问量',
                        sortable: true,
                        name: 'pcaccesstotal',
                        index: 'pcAccessTotal',
                        width: 80
                    },
                    {
                        label: 'WX月访问量',
                        sortable: true,
                        name: 'wxaccess',
                        index: 'wxAccess',
                        width: 80
                    },
                    {
                        label: 'WX总访问量',
                        sortable: true,
                        name: 'wxaccesstotal',
                        index: 'wxAccessTotal',
                        width: 80
                    },
                    {
                        label: '流程',
                        sortable: true,
                        name: 'workflow',
                        index: 'workFlow',
                        width: 80
                    },
                    {
                        label: '日程',
                        sortable: true,
                        name: 'schedulenumbers',
                        index: 'scheduleNumbers',
                        width: 80
                    },
                    {
                        label: '邮件',
                        sortable: true,
                        name: 'email',
                        index: 'email',
                        width: 80
                    },
                    {
                        label: '信息发布',
                        sortable: true,
                        name: 'information',
                        index: 'information',
                        width: 80
                    },
                    {
                        label: '短信',
                        sortable: true,
                        name: 'message',
                        index: 'message',
                        width: 80
                    },
                    {
                        label: '发文',
                        sortable: true,
                        name: 'sendfile',
                        index: 'sendfile',
                        width: 80
                    },
                    {
                        label: '收文',
                        sortable: true,
                        name: 'receivefile',
                        index: 'receivefile',
                        width: 80
                    },
                    {
                        label: '会议室',
                        sortable: true,
                        name: 'boardroom',
                        index: 'boardroom',
                        width: 80
                    },
                    {
                        label: '科室文档',
                        sortable: true,
                        name: 'document',
                        index: 'document',
                        width: 80
                    },
                    {
                        label: '个人文档',
                        sortable: true,
                        name: 'personaldocument',
                        index: 'personalDocument',
                        width: 80
                    },
                    {
                        label: '医德医风',
                        sortable: true,
                        name: 'morality',
                        index: 'morality',
                        width: 80
                    },
                    {
                        label: '巡检',
                        sortable: true,
                        name: 'patrol',
                        index: 'patrol',
                        width: 80
                    },
                    {
                        label: '基本工资',
                        sortable: true,
                        name: 'jbgz',
                        index: 'jbgz',
                        width: 80
                    },
                    {
                        label: '绩效工资',
                        sortable: true,
                        name: 'jxgz',
                        index: 'jxgz',
                        width: 80
                    }
                ],
                queryFormId: 'sysUsageQueryForm'
            });
            
          //时间控件
          laydate.render({
              elem: '#usagemonth',
              type: 'month',
              trigger: 'click'
             // value: new Date()
          });

          function resfreshTable() {
              trasenTable.refresh()
          }
          
          //重置
          $('#sysUsageResetBtn').funs('click', function () {
        	  $("#customer").val("");
        	  $("#usagemonth").val("");
        	  resfreshTable();
          })
          
          form.on('submit(sysUsageSearch)', function () {
              resfreshTable()
              return false
          })
          
          //导出
          $('#sysUsageBox')
              .off('click', '#exportSysUsage')
              .on('click', '#exportSysUsage', function () {
                  var opt = $('#sysUsageQueryForm').serializeArray();
                  var params = "";
                  for (var i in opt) {
                      params += opt[i].name + "=" +opt[i].value + "&";
                  }
                  
                  window.location.href = common.url + '/ts-information/api/sysUsage/export?' + params.substring(0,params.length-1);
           });

        })
    }
})