"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
        var form = layui.form,
            trasen = layui.trasen;

        layer.open({
            type: 1,
            title: opt.title,
            closeBtn: 1,
            shadeClose: false,
            area: ['85%', '90%'],
            skin: 'yourclass',
            content: html,
            success: function(layero, index) {
                var _data = {};
                var myselfItemId = ""; // 自己的项目ID
                if (opt.data) {
                    $("#calculationFormula").val(opt.data.countFormula);
                    $("#calculationFormulaText").val(opt.data.countFormulaText);
                    myselfItemId = opt.data.salaryItemId; // 用于编辑时，选择薪酬项目排除当前编辑项目
                }
                _data.dataCategory = 1;
                // 计算公式里的薪酬项
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: common.url + "/ts-hrms/salaryItem/getCalculationFormulaItemList",
                    data: JSON.stringify(_data),
                    success: function(res) {
                        if (res.success) {
                            $("#salaryChooseItem").html('');
                            for (var i = 0; i < res.object.length; i++) {
                                $("#salaryChooseItem").append('<li data-id="' + res.object[i].salaryItemId + '">' + res.object[i].salaryItemName + '</li>');
                            }
                            form.render();
                        }
                    }
                });
                getAttendanceChooseItemList(myselfItemId);
                form.render();
								
								
            }
        });

        // 考勤项目的点击事件
        $(".formulaBtn").on("click", "li", function() {
            var itemId = $(this).attr("data-id");
            var itemText = $(this).text(); // 项目的文本值
            var calculationFormula = "";
            if (isEmpty(itemId)) {
                calculationFormula = $("#calculationFormula").val() + itemText;
            } else {
                // 项目ID前必须加“_”符号，后端接口计算时需要
                itemId = "_" + itemId;
                calculationFormula = $("#calculationFormula").val() + itemId;
            }
            var calculationFormulaText = $("#calculationFormulaText").val() + itemText;
            $("#calculationFormula").val(calculationFormula);
            $("#calculationFormulaText").val(calculationFormulaText);
        });

        // 清空计算公式
        $("#addCalculationFormulaReset").click(function() {
            $("#calculationFormula").val("");
            $("#calculationFormulaText").val("");
        });

        // 保存的按钮
        $("#addCalculationFormulaSave").on("click", function() {
            var calculationFormula = $("#calculationFormula").val();

            var calculationFormulaText = $("#calculationFormulaText").val();

            if (calculationFormulaText.indexOf("/0") != -1) {
                layer.msg('公式错误！');
                return false;
            }
						let parent = window.parent;
						if(isNewOA) {
							parent = parent[0];
						}
            // 子页面给父页面input框赋值
            parent.$('#countFormula').val(calculationFormula);
            parent.$('#countFormulaText').val(calculationFormulaText);
            layer.close(layer.index); // 关闭当前所在弹出层
        });


        //取消
        $('#addCalculationFormulaFormDiv #addCalculationFormulaClose').off('click').on('click', function() {
            layer.close(layer.index);
        });

    }
});

/**
 * 获取考勤可选择的项目列表
 * @returns
 */
function getAttendanceChooseItemList(itemId) {
    var _data = {};
    _data.dataCategory = 2;
    _data.salaryItemId = itemId;
    $.ajax({
        type: "post",
        contentType: "application/json; charset=utf-8",
        url: common.url + "/ts-hrms/salaryItem/getCalculationFormulaItemList",
        data: JSON.stringify(_data),
        async: false,
        success: function(res) {
            if (res.success) {
                $("#attendanceChooseItem").empty();
                for (var i = 0; i < res.object.length; i++) {
                    $("#attendanceChooseItem").append('<li data-id="' + res.object[i].salaryItemId + '">' + res.object[i].salaryItemName + '</li>');
                }
            }
        }
    });
}