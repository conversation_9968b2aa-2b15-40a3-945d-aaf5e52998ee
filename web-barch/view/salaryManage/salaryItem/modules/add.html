<style>
    .calculationBox,
    .amountBox {
        display: none;
    }
</style>
<div id="salaryManageItemAddFormDiv">
    <div class="layui-tab-content">
        <form class="layui-form" id="salaryManageItemAddForm">
            <!-- 薪酬项目ID -->
            <input type="hidden" name="salaryItemId" value="" />
            <!-- 薪酬项目数据类别 -->
            <input type="hidden" name="dataCategory" value="1" />
            <!-- 计算公式 -->
            <input type="hidden" id="countFormula" name="countFormula" value="" />

            <div class="layui-content-box">
                <div class="layui-col-xs6">
                    <label class="shell-layui-form-label">
                        <span class="required">*</span>
                        项目名称：
                    </label>
                    <div class="shell-layer-input-box">
                        <input type="tex" maxlength="50" name="salaryItemName" value="" lay-verify="required" autocomplete="off" class="layui-input" />
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="shell-layui-form-label">排序：</label>
                    <div class="shell-layer-input-box">
                        <input type="number" oninput="if(value.length>5)value=value.slice(0,5)" name="serialNumber" value="" autocomplete="off" class="layui-input" />
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="shell-layui-form-label">
                        <span class="required">*</span>
                        计算类型：
                    </label>
                    <div class="shell-layer-input-box">
                        <select id="countType" lay-verify="required" lay-search="" name="countType">
                            <option value="">请选择计算类型</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="shell-layui-form-label">
                        <span class="required">*</span>
                        项目类型：
                    </label>
                    <div class="shell-layer-input-box salaryItemTypeBox">
                        <select id="salaryItemType" lay-verify="required" lay-search="" name="salaryItemType" lay-filter="salaryItemType">
                            <option value="">请选择项目类型</option>
                        </select>
                    </div>
                </div>
                <!-- <div class="layui-col-xs6">
                    <label class="shell-layui-form-label"> 参考上月：</label>
                    <div class="shell-layer-input-box">
                        <input type="radio" name="reference" value="是" title="是" checked="">
                        <input type="radio" name="reference" value="否" title="否">

                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="shell-layui-form-label">是否实发：</label>
                    <div class="shell-layer-input-box">
                        <input type="radio" name="actual" value="是" title="是" checked="">
                        <input type="radio" name="actual" value="否" title="否">

                    </div>
                </div> -->
                <div class="layui-col-xs6 calculationBox">
                    <label class="shell-layui-form-label">
                        <span class="required">*</span>
                        计算公式：
                    </label>
                    <div class="shell-layer-input-box calculation">
                        <input type="text" autocomplete="off" id="countFormulaText" name="countFormulaText" lay-verify="required" class="layui-input" placeholder=" " />
                    </div>
                </div>
                <div class="layui-col-xs6 amountBox">
                    <label class="shell-layui-form-label">
                        <span class="required">*</span>
                        默认金额：
                    </label>
                    <div class="shell-layer-input-box amount">
                        <input type="number" oninput="if(value.length>7)value=value.slice(0,7)" autocomplete="off" id="salaryItemAmount" name="salaryItemAmount" lay-verify="required" class="layui-input" placeholder="元" />
                    </div>
                </div>
                <div class="layui-col-xs12">
                    <label class="shell-layui-form-label">说明：</label>
                    <div class="shell-layer-input-box">
                        <textarea name="remark" maxlength="200" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
            <div class="layer-btn archivesTabBtn">
                <!--添加保存 addformSave-->
                <!--编辑保存 ediformSave-->
                <button type="button" class="layui-btn" lay-submit="" lay-filter="salaryManageItemEdiformSave">保存</button>
                <a href="javascript:;" class="layui-btn layui-btn-primary" id="salaryManageItemEdiformClose">关闭</a>
            </div>
        </form>
    </div>
</div>
