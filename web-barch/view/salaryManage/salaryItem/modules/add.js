"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
        var form = layui.form,
            trasen = layui.trasen;

        layer.open({
            type: 1,
            title: opt.title,
            closeBtn: 1,
            shadeClose: false,
            area: ['55%', '60%'],
            skin: 'yourclass',
            content: html,
            success: function(layero, index, res) {
                getEnumDataList(enum_salary_count_type, "计算类型", "countType");
                getEnumDataList(enum_salary_item_type, "项目类型", "salaryItemType");

                if (opt.data) { // 编辑时若有数据则填充数据
                    trasen.setNamesVal(layero, opt.data);
                    showBySalaryItemType(opt.data.salaryItemType);
                }
                form.render(); // 渲染数据
            }
        });

        form.on('select(salaryItemType)', function(data) {
            showBySalaryItemType(data.value);
        });

        // 表格刷新
        function refreshTable() {
            trasenTable.refresh();
        }
        // 计算公式
        $("#countFormulaText").on("click", function() {
            $.quoteFun('salaryManage/salaryItem/modules/addCalculationFormula', {
                title: '计算公式',
                data: opt.data,
                ref: refreshTable
            })
        });
        // 保存
        form.on('submit(salaryManageItemEdiformSave)', function(data) {
            var _url = common.url + "/ts-hrms/salaryItem/save";
            if (opt.data) {
                _url = common.url + "/ts-hrms/salaryItem/update"
            }
            var _data = JSON.stringify(data.field);
            $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url: _url,
                data: _data,
                success: function(res) {
                    if (res.success) {
                        opt.ref && opt.ref();
                        layer.closeAll();
                        layer.msg('操作成功！');
                    } else {
                        layer.msg(res.message || '操作失败！');
                        return false;
                    }
                }
            });
            return false;
        })

        // 取消
        $('#salaryManageItemAddFormDiv #salaryManageItemEdiformClose').off('click').on('click', function() {
            layer.closeAll();
        });
    }
});

/**
 * 根据项目类型显示
 * @param type 项目类型
 * @returns
 */
function showBySalaryItemType(type) {
    if (type == 2 ) {
        $('.amountBox').hide();
        $('.calculationBox').show();
        $('#countFormulaText').attr("lay-verify", 'required');

        $("#salaryItemAmount").attr("lay-verify", null); // 去掉固定金额必填校验
    } else if (type == 4) {
        $('.calculationBox').hide();
        $('.amountBox').show();
        $('#salaryItemAmount').attr("lay-verify", 'required');


        $("#countFormulaText").attr("lay-verify", null); // 去掉计算公式必填校验
    } else {
        $('.calculationBox').hide();
        $('.amountBox').hide();
        $("#countFormulaText").attr("lay-verify", null); // 去掉计算公式必填校验
        $("#salaryItemAmount").attr("lay-verify", null); // 去掉固定金额必填校验
		$("#salaryItemAmount").val(""); 
    }
}