<style>
    .layer-bom {
        display: inline-block !important;
    }

    .shell-layer-input-box {
        width: 80%;
        display: inline-block;
        padding-left: 0 !important;
    }

    /* 键盘部分开始 */

    .keyboard ul {
        width: 600px;
        height: 90px;
        /* border: 1px solid red; */
        margin: auto;
    }

    .keyboard li {
        float: left;
        width: 50px;
        text-align: center;
        height: 40px;
        line-height: 40px;
        background-color: #70b603;
        margin: 4px;
        border-radius: 2px;
    }

    /* 键盘部分结束 */
    /* 薪酬项 考勤项 开始*/

    .optionsBox {
        /* background-color: blueviolet; */
        margin-top: 45px;
    }

    .options h6 {
        width: 500px;
        text-align: center;
        font-weight: normal;
        font-size: 16px;
        margin-bottom: 5px;
    }

    .options ul li {
        width: 110px;
        height: 40px;
        line-height: 40px;
        margin: 4px;
        float: left;
        box-sizing: content-box;
        text-align: center;
        color: white;
    }

    .remuneration {
        width: 480px;
        margin-left: 15px;
    }

    .timeAttendance {
        width: 480px;
    }

    .remuneration ul li {
        background-color: #ffb800;
    }

    .timeAttendance ul li {
        background-color: #1e9fff;
    }

    /* 薪酬项 考勤项 结束*/
</style>
<div id="addCalculationFormulaFormDiv">
    <form class="layui-form" id="calculationFormulaAddForm">
        <!-- 计算公式 -->
        <input type="hidden" id="calculationFormula" value="" />

        <div class="layui-content-box" style="overflow-x: hidden">
            <div class="layui-form-item">
                <label class="layui-form-label">
                    <span class="required">*</span>
                    计算公式：
                </label>
                <div class="shell-layer-input-box">
                    <input type="text" autocomplete="off" id="calculationFormulaText" name="calculationFormulaText" lay-verify="required" readonly="readonly" class="layui-input" />
                </div>
                <button type="button" class="layui-btn" id="addCalculationFormulaReset">清空公式</button>
            </div>

            <div class="keyboard formulaBtn">
                <ul>
                    <li>0</li>
                    <li>1</li>
                    <li>2</li>
                    <li>3</li>
                    <li>4</li>
                    <li>5</li>
                    <li>6</li>
                    <li>7</li>
                    <li>8</li>
                    <li>9</li>
                    <li>+</li>
                    <li>-</li>
                    <li>*</li>
                    <li>/</li>
                    <li>.</li>
                    <li>(</li>
                    <li>)</li>
                    <li>[</li>
                    <li>]</li>
                    <li></li>
                </ul>
            </div>

            <div class="optionsBox">
                <div class="options remuneration fl formulaBtn">
                    <h6>薪酬项</h6>
                    <ul id="salaryChooseItem">
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                    </ul>
                </div>
                <div class="options timeAttendance fr formulaBtn">
                    <h6>考勤项</h6>
                    <ul id="attendanceChooseItem">
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                        <li></li>
                    </ul>
                </div>
                <div style="clear: both"></div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <!--添加保存 addformSave-->
            <!--编辑保存 ediformSave-->
            <button type="button" class="layui-btn" id="addCalculationFormulaSave">保存</button>
            <a href="javascript:;" class="layui-btn layui-btn-primary" id="addCalculationFormulaClose">取消</a>
        </div>
    </form>
</div>
