"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'trasen'], function() {
            var form = layui.form,
                layer = layui.layer,
                laydate = layui.laydate,
                trasen = layui.trasen;
            
            getEnumDataList(enum_salary_item_type, "项目类型", "salaryItemTypeListSel"); // 项目类型
            form.render("select");

            // 表格渲染
            var trasenTable = new $.trasenTable("grid-table-salaryManageItemTable", {
                url: common.url + '/ts-hrms/salaryItem/list',
                pager: 'grid-pager-salaryManageItemPager',
                shrinkToFit: true,
                postData: {dataCategory: 1},
                colModel: [
                    { label: '数据类别', name: 'dataCategory', index: 'data_category', hidden: true },
                    { label: '项目名称', name: 'salaryItemName', index: 'salary_item_name', width: 150, editable: false,align:'left' },
                    { label: '项目类型', name: 'salaryItemTypeText', index: 'salary_item_type', width: 100, editable: false,align:'left' },
                    { label: '计算类型', name: 'countTypeText', index: 'count_type', width: 100, editable: false,align:'left' },
                    { label: '计算公式', name: 'countFormulaText', index: 'count_formula', width: 500, editable: false,align:'left' },
                    { label: '项目金额', name: 'salaryItemAmount', index: 'salary_item_amount', width: 50, editable: false,align:'left' },
                    { label: '排序', name: 'serialNumber', index: 'serial_number', width: 50, editable: false,align:'left' },
                    { label: 'ID', name: 'salaryItemId', hidden: true },
                    { label: '计算公式ID', name: 'countFormula', hidden: true },
                    { label: '计算类型ID', name: 'countType', hidden: true },
                    { label: '项目类型ID', name: 'salaryItemType', hidden: true },
                    { label: '说明', name: 'remark', hidden: true }
                ],
                queryFormId: 'salaryManageItemQueryForm'
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 查询
            form.on('submit(salaryManageItemSearch)', function(data) {
                refreshTable();
            });
            
            //重置
            $('#salaryManageItemReset').funs('click', function () {
                $('#salaryManageItemQueryForm')[0].reset();
                refreshTable();
            });

            // 监听列表下拉选项查询变更事件
            form.on('select(selectChangeFilter)', function(data) {
            	refreshTable();
            });

            // 编辑薪酬项目
            $("#salaryManageItemDiv").off('click', '#salaryManageItemTableEditor').on("click", "#salaryManageItemTableEditor", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                if (rowData.salaryItemType == 1) {
                	layer.msg('系统类型的项目不能编辑！')
                    return false;
                }
                $.quoteFun('salaryManage/salaryItem/modules/add', {
                    title: '编辑薪酬项目',
                    data: rowData,
                    ref: refreshTable
                })

            });

            // 新增薪酬项目
            $("#salaryManageItemDiv").off("click", "#salaryManageItemAdd").on("click", "#salaryManageItemAdd", function() {
                $.quoteFun('salaryManage/salaryItem/modules/add', {
                    title: '新增薪酬项目',
                    ref: refreshTable
                })
            });
            
            // 删除薪酬项目
            $("#salaryManageItemDiv").off("click", "#salaryManageItemTableDel").on("click", "#salaryManageItemTableDel", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条需要删除的数据!')
                    return false;
                }
                if (rowData.salaryItemType == "1") {
                	layer.msg('系统类型的项目不能删除！')
                    return false;
                }
                layer.confirm('确定要删除该条记录吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function() {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/salaryItem/deletedById/" + rowData.salaryItemId,
                        success: function(res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function() {});
            });
        });
    }
});