"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen'], function () {
            var form = layui.form;
            var trasen = layui.trasen;
            var numArr = [0, 0, 0, 0, 0, 0];
            var allData;
            var tab = 0;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $.ajax({
                        url: '/ts-basics-bottom/employee/findByEmployeeId/' + opt.data.employeeId,
                        type: 'post',
                        success: function (res) {
                            if (res.success) {
                                trasen.setNamesVal($('#contractInfoHtml #baseInfoForm'), res.object);
                                $('#contractInfoHtml #employeeName').html(res.object.employeeName);
                                $('#contractInfoHtml #sex').html(res.object.genderText);
                                if(res.object.avatar != null ){
                                    $('#contractInfoHtml #avatar').attr("src",res.object.avatar);
                                }
                            }
                        }
                    })
                    getContractInfo();
                }
            });

            function getContractInfo() {
                $('#contractInfoHtml #box').html('');
                numArr = [0, 0, 0, 0, 0, 0]
                $.ajax({
                    url: '/ts-hrms/contract/getAllByEmployeeId/' + opt.data.employeeId,
                    type: 'post',
                    success: function (res) {
                        if (res.success) {
                            allData = res.object;
                            numArr[0] = allData.length;
                            for (var i = 0; i < allData.length; i++) {
                                numArr[allData[i].signType]++;
                                allData[i].contractPeriod = getContractPeriod(allData[i]);
                                var cform = $('#contractInfoFormHtml').html();
                                $('#contractInfoHtml #box').append(cform);
                                var thisform = $('#contractInfoHtml #box .layui-form').eq(i);
                                thisform.attr('data-tab', allData[i].signType);
                                if (allData[i].remind == 1) {
                                    thisform.find('.remindCheckBox').attr('checked', 'checked');
                                }
                                thisform.find('.contractInfoRemark').html(allData[i].remark);
                                thisform.find('.contractTypeName').html(allData[i].contractTypeName);

                                for (var j = 0; j < allData[i].accessoryList.length; j++) {
                                    var file = allData[i].accessoryList[j];
                                    var fileBox = thisform.find('.fileBox');
                                    if(file.relieve == 1) {
                                        fileBox = thisform.find('.relieveFileBox');
                                    }
                                    fileBox.append(
                                        '<div class="file_item">' +
                                            '<span class="file_name"><a href="' + file.filePath + '">' + file.fileRealName + '</a></span>' + 
                                            ( common.isImg(file.fileRealName) || common.isDoc(file.fileRealName) ? 
                                            '<span class="file_preview" data-name="' + file.fileRealName + '" data-id="' + file.id + '">预览</span>' : '' )
                                            +
                                            '</div>'
                                    );
                                }

                                if (allData[i].signType == '1') { //已签订
                                    thisform.find('.stamp').html('<span style="font-size:18px;">' + allData[i].signTypeName + '</span>');
                                    thisform.find('.stamp_3').addClass('color_1');
                                } else if (allData[i].signType == '2') {
                                    thisform.find('.stamp').html('<span tsyle="font-size:18px;">' + allData[i].signTypeName + '</span>');
                                    thisform.find('.stamp_3').addClass('color_2');
                                    thisform.find('.options button').eq(1).addClass('none'); //续签按钮
                                    thisform.find('.options button').eq(2).addClass('none'); //解除按钮
                                } else if (allData[i].signType == '3') { //过期
                                    thisform.find('.stamp').html('<span style="font-size:18px;">' + allData[i].signTypeName + '</span>');
                                    thisform.find('.stamp_3').addClass('color_3');
                                    thisform.find('.options button').eq(2).addClass('none'); //解除按钮
                                } else if (allData[i].signType == '4') { //已解除
                                    thisform.find('.stamp').html('<span style="font-size:18px;">' + allData[i].signTypeName + '<br><span style="font-size:12px;">' + allData[i].relieveDate + '</span></span>');
                                    thisform.find('.options button').eq(1).addClass('none'); //续签按钮
                                    thisform.find('.options button').eq(2).addClass('none'); //解除按钮
                                    thisform.find('.stamp_3').addClass('color_4');
                                    thisform.find('.isRelieve').removeClass('none');
                                    thisform.find('.relieveExplain').html(allData[i].relieveExplain);
                                } else if (allData[i].signType == '5') { //以删除
                                    thisform.find('.stamp').html('<span style="font-size:18px;">' + allData[i].signTypeName + '<br><span style="font-size:12px;">' + allData[i].signTime + '</span></span>');
                                    thisform.find('.options').remove(); //解除按钮
                                    thisform.find('.stamp_3').addClass('color_5');
                                }

                                trasen.setNamesVal(thisform, allData[i]);
                                form.render();
                            }
                            for (var i = 0; i < numArr.length; i++) {
                                $('#contractInfoHtml .oa-nav .num').eq(i).html('(' + numArr[i] + ')');
                            }

                            $('#contractInfoHtml .oa-nav .oa-nav_item').eq(tab).trigger('click');
                        }
                    }
                })
            }

            function getContractPeriod(row) {
                if (row.allotedTime == 1) {
                    if (row.allotedTime && row.allotedTime != '') {
                        if (row.allotedType == 'Y') {
                            return row.allotedDate + '年';
                        } else if (row.allotedType == 'M') {
                            return row.allotedDate + '月';
                        } else if (row.allotedType == 'D') {
                            return row.allotedDate + '天';
                        } else {
                            return ''
                        }
                    } else {
                        return '';
                    }
                } else {
                    return '';
                }
            }

            //预览
            $('#contractInfoHtml .file_preview').funs('click', function () {
                var index = $(this).parents(".layui-form").index();
                var filename = $(this).attr('data-name')
                var fileid = $(this).attr('data-id');
                var filelist = allData[index].accessoryList;

                if (common.isImg(filename)) {
                    var path = "";
                    var picArr = [];
                    for (var i = 0; i < filelist.length; i++) {
                        if (filelist[i].id == fileid) {
                            path = filelist[i].filePath;
                        }
                        picArr.push({
                            fileName: filelist[i].fileName,
                            fileUrl: filelist[i].filePath
                        })
                    }
                    common.viewerImg(picArr, path, 'hr');
                }

                if (common.isDoc(filename)) {
                    var xlxsFile;
                    for (var i = 0; i < filelist.length; i++) {
                        if (filelist[i].id == fileid) {
                            xlxsFile = filelist[i];
                        }
                    }
                    common.viewerDoc2(xlxsFile.fileId, xlxsFile.fileName);
                }
                return false
            });

            // 签订合同
            $('#contractInfoHtml #signingTheContractBtn').funs('click', function () {
                $.quoteFun('/contract/contractManagement/signingTheContract/index', {
                    title: '签订合同',
                    refresh: opt.refresh,
                    refContract: getContractInfo,
                    employee:{
                        employeeName:opt.data.employeeName,
                        employeeNo:opt.data.employeeNo
                    }
                });
            });

            //编辑合同 
            $('#contractInfoHtml .signTheRecordTableEdit').funs('click', function () {
                var index = $(this).parents(".layui-form").index();
                var data = allData[index];
                $.quoteFun('/contract/contractManagement/signingTheContract/index', {
                    title: '编辑合同',
                    refresh: opt.refresh,
                    type: 'edit',
                    data: data,
                    refContract: getContractInfo
                });
            });

            //续签 
            $('#contractInfoHtml .signTheRecordTableRenew').funs('click', function () {
                var index = $(this).parents(".layui-form").index();
                var data = allData[index];
                $.quoteFun('/contract/contractManagement/signingTheContract/index', {
                    title: '续签合同',
                    refresh: opt.refresh,
                    data: data,
                    type: 'renew',
                    refContract: getContractInfo
                });
            });

            //解除合同
            $('#contractInfoHtml .signTheRecordTableDissmis').funs('click', function () {
                var index = $(this).parents(".layui-form").index();
                var data = allData[index];
                $.quoteFun('/contract/contractManagement/dismissContract/index', {
                    title: '解除合同',
                    data: data,
                    refresh: opt.refresh,
                    refContract: getContractInfo
                });
            });

            //删除合同
            $('#contractInfoHtml .signTheRecordTableDelete').funs('click', function () {
                var index = $(this).parents(".layui-form").index();
                var data = allData[index];
                var mylayer = layer.open({
                    content: '确定删除该数据吗？',
                    title: '删除',
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        $.ajax({
                            url: '/ts-hrms/contract/remove',
                            type: 'post',
                            data: JSON.stringify(data),
                            contentType: 'application/json',
                            success: function (res) {
                                if (res.success) {
                                    opt.refresh();
                                    getContractInfo();
                                    layer.close(mylayer);
                                    layer.msg('删除成功!');
                                } else {
                                    layer.msg(res.message || '操作失败!')
                                }
                            }
                        })
                    }
                })
            });

            $('#contractInfoHtml .oa-nav .oa-nav_item').click(function () {
                $(this).addClass('active').siblings().removeClass('active');
                tab = $(this).index();
                if (tab == 0) {
                    $('#contractInfoHtml #box .layui-form').show();
                } else {
                    $('#contractInfoHtml #box .layui-form').hide();
                }
                $('#contractInfoHtml #box [data-tab=' + tab + ']').show();
            });

            $('#contractInfoHtml #close').off('click').on('click', function () {
                layer.closeAll();
            });
			
			$('#contractInfoHtml #closeSigningTheContractBtn').off('click').on('click', function () {
			    layer.closeAll();
			});

        });
    };
});