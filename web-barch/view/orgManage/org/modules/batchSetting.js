"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    var wfPermissionsList = [];
    layui.use(['form', 'trasen', 'zTreeSearch', 'treeSelect'], function () {
      var form = layui.form,
        trasen = layui.trasen;
      layer.open({
        type: 1,
        title: opt.title,
        closeBtn: 1,
        shadeClose: false,
        area: '500px',
        skin: 'yourclass',
        content: html,
        success: async function (layero, index) {
            fromRender();
            bindBtnEvent();
              //人员选择
            $("#batchSetting .userTransfer")
            .off("click")
            .on("click", function () {
              let type = $(this).attr('keyType');
              let arr = [];
              if ($('#batchSetting #oldEmployeeCode').val() && type == 1) {
                arr.push({
                  name: $('#batchSetting #oldEmployeeName').val(),
                  code: $('#batchSetting #oldEmployeeCode').val()
                })
              }
              if ($('#batchSetting #newEmployeeCode').val() && type == 2) {
                arr.push({
                  name: $('#batchSetting #newEmployeeName').val(),
                  code: $('#batchSetting #newEmployeeCode').val()
                })
              }
              $.quoteFun('/commonPage/userDeptGroupRadio/index', {
                data: {
                  user: true,
                  dept: true,
                  userList: arr,
                },
                callBack: function (list, seluser, dept) {
                  if (type == 1) {
                    $('#batchSetting #oldEmployeeName').val(seluser[0].name);
                    $('#batchSetting #oldEmployeeCode').val(seluser[0].code);
                  } else {
                    $('#batchSetting #newEmployeeName').val(seluser[0].name);
                    $('#batchSetting #newEmployeeCode').val(seluser[0].code);
                  }
                },
              });
            });
        }
      });
      form.render();
    })
    
    //按钮点击事件
    function bindBtnEvent(){
        let form =layui.form;
        //确认添加事件
        form.on('submit(deviceUpholdConfirm)',(data)=>{
            let postData = {
              roleId:data.field.roleId,
              oldEmployeeCode:data.field.oldEmployeeCode,
              newEmployeeCode:data.field.newEmployeeCode,
            };
            $.ajax({
                type: "POST",
                url: '/ts-basics-bottom/organization/bacthReplaceLeader',
                data: JSON.stringify(postData),
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if(res.success){
                        opt.ref();
                        layer.closeAll();
                        layer.msg('批量设置成功');
                    }
                    else{
                        layer.msg(res.message||"批量设置失败");
                    }
                }
            });
        });

        //绑定取消按钮点击事件
        $('#deviceUpholdCancel').click((e)=>{
            layer.closeAll();
        });
    }
    function fromRender() {
      let leaderData = []
      commonDictCache.getDict('LDJS', function (dict) {
          for (var i = 0; i < dict.length; i++) {
              var item = {
                  roleId: dict[i].itemNameValue,
                  roleName: dict[i].itemName,
              };
              leaderData.push(item);
          }
          // }
          let options = {
            searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'roleName', 
            valName: 'roleId', 
            inpTextName: 'roleName', 
            inpValName: 'roleId', 
            required: 'required',
            data: leaderData ||[],
            callback: function(res) {
              $('#batchSetting [name="roleId"]').val(res.roleId);
            }
          }
          $('#batchSetting [name="wfStep"]').css('display', 'flex');
          new $.selectPlug('#roleName', options)
      });
    }
  }
})