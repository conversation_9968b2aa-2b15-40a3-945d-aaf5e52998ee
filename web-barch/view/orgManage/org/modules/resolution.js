'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'treeSelect', 'zTreeSearch'], function () {
            var form = layui.form,
                trasen = layui.trasen,
                laydate = layui.laydate,
                zTreeSearch = layui.zTreeSearch;
            //拖拽
            var drag = {
                class_name: null, //允许放置的容器
                permitDrag: false, //是否允许移动标识

                _x: 0, //节点x坐标
                _y: 0, //节点y坐标
                _left: 0, //光标与节点坐标的距离
                _top: 0, //光标与节点坐标的距离

                old_elm: null, //拖拽原节点
                tmp_elm: null, //跟随光标移动的临时节点
                new_elm: null, //拖拽完成后添加的新节点

                //初始化
                init: function (className) {
                    //允许拖拽节点的父容器的classname(可按照需要，修改为id或其他)
                    drag.class_name = className;
                    //监听鼠标按下事件，动态绑定要拖拽的节点（因为节点可能是动态添加的）
                    $('.' + drag.class_name).on('mousedown', 'ul li', function (event) {
                        //当在允许拖拽的节点上监听到点击事件，将标识设置为可以拖拽
                        drag.permitDrag = true;
                        //获取到拖拽的原节点对象
                        drag.old_elm = $(this);
                        //执行开始拖拽的操作
                        drag.mousedown(event);
                        return false;
                    });

                    //监听鼠标移动
                    $(document).mousemove(function (event) {
                        //判断拖拽标识是否为允许，否则不进行操作
                        if (!drag.permitDrag) return false;
                        //执行移动的操作
                        drag.mousemove(event);
                        return false;
                    });

                    //监听鼠标放开
                    $(document).mouseup(function (event) {
                        //判断拖拽标识是否为允许，否则不进行操作
                        if (!drag.permitDrag) return false;
                        //拖拽结束后恢复标识到初始状态
                        drag.permitDrag = false;
                        //执行拖拽结束后的操作
                        drag.mouseup(event);
                        return false;
                    });
                },

                //按下鼠标 执行的操作
                mousedown: function (event) {
                    //1.克隆临时节点，跟随鼠标进行移动
                    drag.tmp_elm = $(drag.old_elm).clone();
                    //2.计算 节点 和 光标 的坐标
                    drag._x = $(drag.old_elm).offset().left - $(drag.old_elm).parents('.resolutionPoP').offset().left;
                    drag._y = $(drag.old_elm).offset().top - $(drag.old_elm).parents('.resolutionPoP').offset().top - 35;
                    var e = event || window.event;
                    drag._left = e.pageX - drag._x;
                    drag._top = e.pageY - drag._y;

                    //3.修改克隆节点的坐标，实现跟随鼠标进行移动的效果
                    $(drag.tmp_elm).css({
                        position: 'absolute',
                        'background-color': '#FF8C69',
                        left: drag._x,
                        top: drag._y,
                    });

                    //4.添加临时节点
                    var tmp = $(drag.old_elm).parent().append(drag.tmp_elm);
                    drag.tmp_elm = $(tmp).find(drag.tmp_elm);
                    $(drag.tmp_elm).css('cursor', 'move');
                },

                //移动鼠标 执行的操作
                mousemove: function (event) {

                    //2.计算坐标
                    var e = event || window.event;
                    var x = e.pageX - drag._left;
                    var y = e.pageY - drag._top;
                    var maxL = $(document).width() - $(drag.old_elm).outerWidth();
                    var maxT = $(document).height() - $(drag.old_elm).outerHeight();
                    //不允许超出浏览器范围
                    x = x < 0 ? 0 : x;
                    x = x > maxL ? maxL : x;
                    y = y < 0 ? 0 : y;
                    y = y > maxT ? maxT : y;
                    //3.修改克隆节点的坐标
                    $(drag.tmp_elm).css({
                        left: x,
                        top: y,
                    });

                    //判断当前容器是否允许放置节点
                    $.each($('.' + drag.class_name + ' ul'), function (index, value) {
                        //获取容器的坐标范围 (区域)
                        var box_x = $(value).offset().left; //容器左上角x坐标
                        var box_y = $(value).offset().top; //容器左上角y坐标
                        var box_width = $(value).outerWidth(); //容器宽
                        var box_height = $(value).outerHeight(); //容器高

                        //给可以放置的容器加背景色
                        if (e.pageX > box_x && e.pageX < box_x - 0 + box_width && e.pageY > box_y && e.pageY < box_y - 0 + box_height) {
                            //判断是否不在原来的容器下（使用坐标进行判断：x、y任意一个坐标不等于原坐标，则表示不是原来的容器）
                            if ($(value).offset().left !== drag.old_elm.parent().offset().left || $(value).offset().top !== drag.old_elm.parent().offset().top) {
                                $(value).css('background-color', '#FFEFD5');
                            }
                        } else {
                            //恢复容器原背景色
                            $(value).css('background-color', '#FFFFF0');
                        }
                    });
                },

                //放开鼠标 执行的操作
                mouseup: function (event) {
                    //移除临时节点
                    $(drag.tmp_elm).remove();

                    //判断所在区域是否允许放置节点
                    var e = event || window.event;

                    $.each($('.' + drag.class_name + ' ul'), function (index, value) {
                        //获取容器的坐标范围 (区域)
                        var box_x = $(value).offset().left; //容器左上角x坐标
                        var box_y = $(value).offset().top; //容器左上角y坐标
                        var box_width = $(value).outerWidth(); //容器宽
                        var box_height = $(value).outerHeight(); //容器高

                        //判断放开鼠标位置是否想允许放置的容器范围内
                        if (e.pageX > box_x && e.pageX < box_x - 0 + box_width && e.pageY > box_y && e.pageY < box_y - 0 + box_height) {
                            //判断是否不在原来的容器下（使用坐标进行判断：x、y任意一个坐标不等于原坐标，则表示不是原来的容器）
                            if ($(value).offset().left !== drag.old_elm.parent().offset().left || $(value).offset().top !== drag.old_elm.parent().offset().top) {
                                //向目标容器添加节点并删除原节点
                                var tmp = $(drag.old_elm).clone();
                                var newObj = $(value).append(tmp);
                                $(drag.old_elm).remove();
                                //获取新添加节点的对象
                                drag.new_elm = $(newObj).find(tmp);
                            }
                        }
                        //恢复容器原背景色
                        $(value).css('background-color', '#FFFFF0');
                    });
                },
            };

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['920px', '500px'],
                skin: 'yourclass resolutionPoP',
                content: html,
                success: function (layero, index) {
                    //出入允许拖拽节点的父容器，一般是ul外层的容器
                    drag.init('resolution_divLeft');
                },
            });

            //加载选中科室人员
            loadEmp();
            function loadEmp() {
                var _data = { eqOrgId: opt.orgId };
                _data = JSON.stringify(_data);
              
                $.ajax({
                    type: 'post',
                    data: _data,
                    dataType: 'json',
                    contentType: 'application/json;charset=utf-8',
                    url: common.url + '/ts-basics-bottom/employee/getEmployeeList',
                    success: function (res) {
                        if (res.object != null && res.object.length > 0) {
                            var html = '';
                            for (var i = 0; i < res.object.length; i++) {
                                html += "<li data_empId='" + res.object[i].employeeId + "' data_empNo='" + res.object[i].employeeNo + "' data_empName='" + res.object[i].employeeName + "'>" + res.object[i].employeeName + '</li>';
                            }
                            $('#resolutionMoveEmp').append(html);
                            $('.resolution_list ul').css('height', $('#resolutionMoveEmp').innerHeight() + 'px');
                        }
                    },
                });
            }

            //保存数据
            form.on('submit(orgManageResolutionSub)', function (data) {
                var _data = [];
                var deptCode1 = $('#resolutionDeptCode1').val();
                var deptCode2 = $('#resolutionDeptCode2').val();
                var empList1 = $('#resolution_list1').find('li');
                var empList2 = $('#resolution_list2').find('li');

                if (!empList1.length && !empList2.length) {
                    layer.msg('请拆分人员');
                    return false;
                }

                if (empList1 != null && empList1.length > 0) {
                    if (deptCode1 == null || deptCode1 == '') {
                        layer.msg('请选择科室1');
                        return false;
                    }
                    var d = {
                        employeeIds: [],
                        organizationId: deptCode1,
                        organizationName: $('#resolutionDept1').val(),
                    };
                    for (var i = 0; i < empList1.length; i++) {
                        var _empId = empList1.eq(i).attr('data_empId');
                        var _empName = empList1.eq(i).attr('data_empName');
                        var _empNo = empList1.eq(i).attr('data_empNo');
                        d.employeeIds.push(_empId);
                    }
                    _data.push(d);
                }

                if (empList2 != null && empList2.length > 0) {
                    if (deptCode2 == null || deptCode2 == '') {
                        layer.msg('请选择科室2');
                        return false;
                    }
                    var d = {
                        employeeIds: [],
                        organizationId: deptCode2,
                        organizationName: $('#resolutionDept2').val(),
                    };
                    for (var i = 0; i < empList2.length; i++) {
                        var _empId = empList2.eq(i).attr('data_empId');
                        var _empName = empList2.eq(i).attr('data_empName');
                        var _empNo = empList2.eq(i).attr('data_empNo');
                        d.employeeIds.push(_empId);
                    }
                    _data.push(d);
                }

                _data = JSON.stringify({
                    newOrganizationList: _data,
                    oldOrganization: {
                        organizationId: opt.orgId,
                        organizationName: opt.orgName,
                    },
                });
                $.ajax({
                    type: 'post',
                    contentType: 'application/json; charset=utf-8',
                    url: common.url + '/ts-basics-bottom/organization/split',
                    data: _data,
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('保存成功！');
                            opt.ref && opt.ref();
                        } else {
                            layer.msg(res.message || '操作失败');
                        }
                    },
                });
                return false;
            });

            $('#orgManageResolutionClose')
                .off('click')
                .on('click', function () {
                    layer.closeAll();
                });
            $('#orgManageResolutionClose #save')
                .off('click')
                .on('click', function () {
                    $('[lay-filter="orgManageResolutionSub"]').trigger('click');
                    return false;
                });

            // 组织机构下拉选择树
            treeSelect1();
            function treeSelect1() {
                zTreeSearch.init('#resolutionDept1', {
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        if (treeNode) {
                            $('#resolutionDeptCode1').val(treeNode.id); // 组织机构ID
                        }
                    },
                });
            }

            // 组织机构下拉选择树
            treeSelect2();
            function treeSelect2() {
                zTreeSearch.init('#resolutionDept2', {
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        if (treeNode) {
                            $('#resolutionDeptCode2').val(treeNode.id); // 组织机构ID
                        }
                    },
                });
            }
        });
    };
});
