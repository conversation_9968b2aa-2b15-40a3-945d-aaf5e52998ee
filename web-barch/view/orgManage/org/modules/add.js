'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'zTreeSearch'], function () {
            var form = layui.form;
            var trasen = layui.trasen;
            var zTreeSearch = layui.zTreeSearch;

            var leaderData = [];
            var wins = layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['980px', '500px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
					getDictInfoList(dict_org_flag, '组织类型', 'orgFlagTypeSel');
					form.render('select');
                    if (opt.data) {
                        $('#orgManageOrgAddHtml #orgManageOrgCode').attr('readonly', true);
                        trasen.setNamesVal(layero, opt.data);
                    }
                    commonDictCache.getDict('LDJS', function (dict) {
                        for (var i = 0; i < dict.length; i++) {
                            var item = {
                                roleId: dict[i].itemNameValue,
                                roleName: dict[i].itemName,
                                employeeIdList: [],
                                employeeNameList: [],
                                showFramework: 0,
                                synNextOrg: 0,
                            };
                            if (opt.data) {
                                for (var j = 0; j < opt.data.leaders.length; j++) {
                                    var l = opt.data.leaders[j];
                                    if (opt.data.leaders[j].roleId == item.roleId) {
                                        var ids = [];
                                        var names = [];
                                        if (l.employeeIdList) {
                                            for (var x = 0; x < l.employeeIdList.length; x++) {
                                                ids.push(l.employeeIdList[x].employeeNo);
                                                names.push(l.employeeIdList[x].employeeName);
                                            }
                                        }
                                        l.employeeIdList = ids;
                                        l.employeeNameList = names;
                                        item.employeeIdList = l.employeeIdList;
                                        item.employeeNameList = l.employeeNameList;
                                        item.showFramework = l.showFramework || 0;
                                        item.synNextOrg = l.synNextOrg || 0;
                                    }
                                }
                            }
                            leaderData.push(item);
                        }
                        // }
                        tableTemp();
                    });
                    form.render('select');
                    opt.title == '编辑组织' && renderOrganizationPersonalList();
                },
            });
            //领导表格
            function tableTemp() {
                for (var i = 0; i < leaderData.length; i++) {
                    var _tr = $('<tr data-index="' + i + '"></tr>');
                    var role = $('<td>' + leaderData[i].roleName + '</td>');
                    _tr.append(role);
                    var leads = $('<td><div class="leader-box"></div></td>');
                    leads.find('.leader-box').text(leaderData[i].employeeNameList.join(','));
                    _tr.append(leads);
                    var synNextOrg = $('<td><label class="labelCheckbox"><input type="checkbox" name="synNextOrg" value="1" class="self-checkbox-switch"><div class="self-checkbox-switch-icon"></div></label></td>');
                    if (leaderData[i].synNextOrg == 1) {
                        synNextOrg.find('input').prop('checked', true);
                    }
                    _tr.append(synNextOrg);
                    var showFramework = $('<td><label class="labelCheckbox"><input type="checkbox" name="showFramework" value="1" class="self-checkbox-switch"><div class="self-checkbox-switch-icon"></div></label></td>');
                    if (leaderData[i].showFramework == 1) {
                        showFramework.find('input').prop('checked', true);
                    }
                    _tr.append(showFramework);
                    $('#orgManageOrgAddHtml #leaderTable tbody').append(_tr);
                }
            }

            /**@desc 组织人员 */
            function renderOrganizationPersonalList() {
              $.ajax({
                url: '/ts-basics-bottom/organization/selectEmpListByDeptCode',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify([opt.data.organizationId]),
                success: function(res) {
                  if(res.success == false) {
                    return;
                  }
                  let content = $('#orgManageOrgAddHtml #organizationPersonArea'),
                    userList = (res.object || []).map(item=>{
                      return `<div class="organization-user-item" data-key="${item.employeeId}">${item.employeeName}</div>`;
                    }).join(''),
                    parent = content.closest('.layui-col-md12');

                  parent.removeAttr('style');
                  content.empty();
                  content.append(userList);
                  Sortable.create(content[0], {
                    draggable: '.organization-user-item',
                  })
                } 
              })
            }

            //领导人员设置
            $('#orgManageOrgAddHtml')
                .off('click', '.leader-box')
                .on('click', '.leader-box', function () {
                    var box = $(this);
                    var index = $(this).closest('tr').attr('data-index');
                    var arr = [];
                    var lead = leaderData[index];
                    for (var i = 0; i < lead.employeeIdList.length; i++) {
                        arr.push({
                            name: lead.employeeNameList[i],
                            code: lead.employeeIdList[i],
                        });
                    }
                    $.quoteFun('/commonPage/userDeptGroup/index', {
                        data: {
                            user: true,
                            dept: true,
                            userList: arr,
                        },
                        callBack: function (list, seluser, dept) {
                            var ids = [];
                            var names = [];
                            for (var i = 0; i < seluser.length; i++) {
                                ids.push(seluser[i].code);
                                names.push(seluser[i].name);
                            }
                            lead.employeeIdList = ids;
                            lead.employeeNameList = names;
                            box.text(names.join(','));
                        },
                    });
                });

            function setCheck() {
                var synNextOrgs = $('#orgManageOrgAddHtml [name="synNextOrg"]');
                for (var i = 0; i < synNextOrgs.length; i++) {
                    var index = $(synNextOrgs[i]).closest('tr').attr('data-index');
                    leaderData[index].synNextOrg = $(synNextOrgs[i]).prop('checked') * 1;
                }
                var showFrameworks = $('#orgManageOrgAddHtml [name="showFramework"]');
                for (var i = 0; i < showFrameworks.length; i++) {
                    var index = $(showFrameworks[i]).closest('tr').attr('data-index');
                    leaderData[index].showFramework = $(showFrameworks[i]).prop('checked') * 1;
                }
            }

            // 组织机构下拉选择树
            treeSelect();
            function treeSelect() {
                zTreeSearch.init('#orgManageOrgParentName', {
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        if (treeNode) {
                            $('#orgManageOrgParentId').val(treeNode.id); // 组织机构ID
                            $('#orgManageOrgParentName').val(treeNode.name); // 组织机构名称
                        }
                    },
                    callback: function () {
                        $('#orgManageOrgParentId').val(''); // 组织机构ID
                        $('#orgManageOrgParentName').val(''); // 组织机构名称
                    },
                });
            }

            form.on('submit(orgManageOrgAddSub)', function (data) {
                var d = data.field;
                // 上级机构名称为空的时候清空上级机构ID
                if (isEmpty($('#orgManageOrgParentName').val())) {
                    data.field.parentId = '';
                }

                var _url = common.url + '/ts-basics-bottom/organization/add';
                if (opt.data&&(opt.data.organizationId!="")) {
                    _url = common.url + '/ts-basics-bottom/organization/edit';
                    d = $.extend(opt.data, d);
                }
                d.leaders = leaderData;
                setCheck();
                if(opt.title == '编辑组织') {
                  d.hrmsEmployeeList = [];
                  $.each($('#orgManageOrgAddHtml #organizationPersonArea .organization-user-item'), function(index, dom) {
                    let employeeId = dom.getAttribute('data-key'),
                      empSort = index + 1;
                    d.hrmsEmployeeList.push({
                      employeeId,
                      empSort,
                      name: dom.innerText
                    })
                  })
                }
                var _data = JSON.stringify(d);
                $.ajax({
                    type: 'post',
                    contentType: 'application/json; charset=utf-8',
                    url: _url,
                    data: _data,
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('保存成功！');
                            opt.ref && opt.ref();
                            opt.documentTree && opt.documentTree();
                        } else {
                            layer.msg(res.message || '操作失败');
                        }
                    },
                });
                return false;
            });

            $('#orgManageOrgAddHtml #orgManageOrgClose')
                .off('click')
                .on('click', function () {
                    layer.closeAll();
                });
            $('#orgManageOrgAddHtml #save')
                .off('click')
                .on('click', function () {
                    $('[lay-filter="orgManageOrgAddSub"]').trigger('click');
                    return false;
                });
        });
    };
});
