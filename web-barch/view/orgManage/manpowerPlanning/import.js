'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var upfile;
        layui.use(['form', 'upload'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var wins = layer.open({
                type: 1,
                title: '人力规划导入',
                closeBtn: 1,
                shadeClose: false,
                area: ['485px', '255px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    upload.render({
                        elem: '#orgManpowerPlanningImportFile',
                        // url: '/ts-document/attachment/fileUpload?module=oa',
                        accept: 'file',
                        acceptMime: 'file/xlsx,file/xls',
                        exts: 'xlsx|xls',
                        auto: false,
                        choose: function (obj) {
                            //将每次选择的文件追加到文件队列
                            // var files = obj.pushFile();
                            //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                            obj.preview(function (index, file, result) {
                                upfile = file;
                                $('#orgManpowerPlanningImportFile').prev().val(file.name);
                                
                            });
                        },
                        done: function (res) {
                            res.object && res.object[0] && $('#orgManpowerPlanningImport [name="fileId"]').val(res.object[0].fileId);
                        },
                    });
                    form.render('select');
                    form.on('select(orgManpowerPlanningImportType)', function (data) {
                        if (data.value == 1) {
                            $('#orgManpowerPlanningImport .importRem').text('增量导入将在已有的数据基础上新增');
                        } else {
                            $('#orgManpowerPlanningImport .importRem').text('全量导入将已有的数据清除后重新导入');
                        }
                    });
                },
            });
            form.on('submit(orgManpowerPlanningImportSave)', function (data) {
                var formData = new FormData();
                formData.append('file', upfile);
                formData.append('type', data.field.type);
                openW();
                function openW() {
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: false,
                        area: ['485px', '255px'],
                        skin: 'yourclass',
                        content: '<div style="text-align:center;height:100%;line-height:300px">正在处理组织数据，请耐心等候...</div>',
                        success: function (layero, i) {
                            $.ajax({
                                method: 'post',
                                contentType: false,
                                url: '/ts-basics-bottom/organizationAllocation/import',
                                data: formData,
                                processData: false,
                                success: function (res) {
                                    layer.close(i);
                                    if (res.success) {
                                        opt.ref && opt.ref();
                                        layer.confirm(res.message || '', 
                                        { 
                                            icon: 1, 
                                            btn: ['关闭'], 
                                            area: ['485px', '255px'], 
                                            title: '处理结果',
                                            closeBtn: 0 
                                        }, function (i) {
                                            layer.close(i);
                                        });
                                    } else {
                                        layer.confirm(res.message || '', 
                                        { 
                                            icon: 7, 
                                            btn: ['关闭'], 
                                            area: ['485px', '255px'], 
                                            title: '处理结果',
                                            closeBtn: 0
                                         }, function (i) {
                                            layer.close(i);
                                        });
                                    }
                                },
                            });
                        },
                    });
                }
                return false;
            });

            $('#orgManpowerPlanningImport')
                .off('click', '#save')
                .on('click', '#save', function () {
                    $('#orgManpowerPlanningImport [lay-filter="orgManpowerPlanningImportSave"]').trigger('click');
                    return false;
                });
            $('#orgManpowerPlanningImport')
                .off('click', '#close')
                .on('click', '#close', function () {
                    layer.close(wins);
                    return false;
                });
        });
    };
});
