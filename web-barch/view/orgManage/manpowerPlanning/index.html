<style scoped>
    #orgManpowerPlanning .ui-jqgrid-bdiv {
        top: 60px !important;
    }
    .row-opera-box span {
        margin: 0 5px;
        cursor: pointer;
    }
</style>
<div class="content-box" id="orgManpowerPlanning">
    <div class="oa-nav">
        <a href="javascript:;" class="oa-nav_item active">定岗定编</a>
        <a href="javascript:;" class="oa-nav_item">人床比</a>
    </div>
    <div class="trasen-con-box orgManpowerPlanning-box">
        <div class="oa-search-tree" style="border: 1px solid #ccc">
            <div class="shell-search-tree">
                <div style="padding: 8px 8px">
                    <input class="layui-input" type="text" placeholder="请输入组织机构" id="orgManpowerPlanningTreeSearch" />
                </div>
            </div>
            <div class="ztree-box scrollbar-box" style="top: 40px">
                <ul id="orgManpowerPlanningTree" class="ztree"></ul>
            </div>
        </div>
        <div class="transen-con-view-box scrollbar-box">
            <div class="oa-nav-search">
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">组织机构名称</span>
                    <div class="shell-layer-input-box" style="width: 160px">
                        <input type="text" name="orgName" class="layui-input" autocomplete="off" search-input="orgManpowerPlanning-search" placeholder="请输入组织机构名称" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <button type="button" class="layui-btn" id="search" search-btn="orgManpowerPlanning-search">搜索</button>
                </div>
                <div class="fr">
                    <button class="layui-btn" id="import">导入</button>
                    <button class="layui-btn" id="export">导出</button>
                </div>
            </div>
            <div class="trasen-con-box">
                <div class="table-box">
                    <table id="orgManpowerPlanning-grid-table"></table>
                </div>
            </div>
        </div>
    </div>
    <div class="trasen-con-box none orgManpowerPlanning-box">
        <div class="layui-form" id="orgManpowerPlanningBedForm">
            <div class="layui-form-item">
                <label class="layui-form-label">人床比标准：</label>
                <div class="layui-input-block">
                    <div class="layui-inline" style="width: 120px">
                        <select lay-search name="rcb_type">
                            <option value=">">大于</option>
                            <option value=">=">大于等于</option>
                            <option value="=">等于</option>
                            <option value="<">小于</option>
                            <option value="<=">小于等于</option>
                        </select>
                    </div>
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" name="rcb_num1" class="layui-input" />
                    </div>
                    :
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" name="rcb_num2" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">医护比标准：</label>
                <div class="layui-input-block">
                    <div class="layui-inline" style="width: 120px">
                        <select lay-search name="yhb_type">
                            <option value=">">大于</option>
                            <option value=">=">大于等于</option>
                            <option value="=">等于</option>
                            <option value="<">小于</option>
                            <option value="<=">小于等于</option>
                        </select>
                    </div>
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="yhb_num1" />
                    </div>
                    :
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="yhb_num2" />
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">医床比标准：</label>
                <div class="layui-input-block">
                    <div class="layui-inline" style="width: 120px">
                        <select lay-search name="ycb_type">
                            <option value=">">大于</option>
                            <option value=">=">大于等于</option>
                            <option value="=">等于</option>
                            <option value="<">小于</option>
                            <option value="<=">小于等于</option>
                        </select>
                    </div>
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="ycb_num1" />
                    </div>
                    :
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="ycb_num2" />
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">护床比标准：</label>
                <div class="layui-input-block">
                    <div class="layui-inline" style="width: 120px">
                        <select lay-search name="hcb_type">
                            <option value=">">大于</option>
                            <option value=">=">大于等于</option>
                            <option value="=">等于</option>
                            <option value="<">小于</option>
                            <option value="<=">小于等于</option>
                        </select>
                    </div>
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="hcb_num1" />
                    </div>
                    :
                    <div class="layui-inline" style="width: 60px; margin-left: 10px">
                        <input type="number" class="layui-input" name="hcb_num2" />
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" id="orgManpowerPlanningBedFormSave">保存</button>
                </div>
            </div>
        </div>
    </div>
</div>
