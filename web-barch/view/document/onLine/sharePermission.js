"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layedit = layui.layedit,
                //$ = layui.$,
                zTreeSearch = layui.zTreeSearch;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['450px', '350px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#sharePermissionForm'), opt.data);
                        $('#id').val(opt.data.id);
                        $('#onLineName').val(opt.data.name);
                        if (opt.data.authType) {
                            var authType = opt.data.authType;
                            if (authType == 2) {//私密的 查看跟编辑框都显示
                                $("#editablePerson").attr("style", "display:block;");//显示div
                                $("#viewablePeople").attr("style", "display:block;");
                            }
                            if (authType == 3) {
                                $("#editablePerson").attr("style", "display:none;");
                                $("#viewablePeople").attr("style", "display:none;");
                            }
                            if (authType == 1) {
                                $("#editablePerson").attr("style", "display:none;");
                                $("#viewablePeople").attr("style", "display:none;");
                            }
                            if (authType == 0) {
                                $("#editablePerson").attr("style", "display:block;");
                                $("#viewablePeople").attr("style", "display:none;");
                            }
                        }
                    }
                    form.render();
                }
            });

            //监听下拉选择
            form.on('select(authType)', function (data) {
                var value = data.value;
                if (value == 0) {
                    $("#editablePerson").attr("style", "display:block;");
                    $("#viewablePeople").attr("style", "display:none;");
                }
                if (value == 1) {
                    $("#editablePerson").attr("style", "display:none;");
                    $("#viewablePeople").attr("style", "display:none;");
                }
                if (value == 2) {
                    $("#editablePerson").attr("style", "display:block;");//显示div
                    $("#viewablePeople").attr("style", "display:block;");//显示div
                }
                if (value == 3) {
                    $("#editablePerson").attr("style", "display:none;");
                    $("#viewablePeople").attr("style", "display:none;");
                }
            });

            // 人员选择 可查看人
            $('#readUserNames').funs('click', function () {
                /*var userId = $('#readUserCodes').val();
                var userName = $('#readUserNames').val();
                if (userId) {
                    var data = {
                        userId: userId,
                        userName: userName
                    }
                } else {
                    var data = "";
                }
                $.quoteFun('/document/onLine/userSel', {
                    trasen: trasenTable,
                    title: '选择可查看人',
                    data: data,
                    none: 100
                });*/
                var data = {
                    isCheckDept:'N',
                    user_str:'readUserNames',
                    user_id:"readUserCodes",
                    user_code:"readUserCodes",
                    dept_code:"columnManagementSelDeptId"
                };
                $.quoteFun('/common/userSel', {
                    trasen : trasenTable,
                    title : '人员选择',
                    data :data
                });
            })

            //可编辑人
            $('#writeUserNames').funs('click', function () {
                /*var userId = $('#writeUserCodes').val();
                var userName = $('#writeUserNames').val();
                if (userId) {
                    var data = {
                        userId: userId,
                        userName: userName
                    }
                } else {
                    var data = "";
                }
                $.quoteFun('/document/onLine/userSel', {
                    trasen: trasenTable,
                    title: '选择可编辑人',
                    data: data,
                    none: 200
                });*/
                var data = {
                    isCheckDept:'N',
                    user_str:'writeUserNames',
                    user_id:"writeUserCodes",
                    user_code:"writeUserCodes",
                    dept_code:"columnManagementSelDeptId"
                };
                $.quoteFun('/common/userSel', {
                    trasen : trasenTable,
                    title : '人员选择',
                    data :data
                });
            })

            // 保存
            form.on('submit(sharePermissionSubmitCofirm)', function (data) {
                var d = data.field;
                var name = $('#onLineName').val();
                d.name = name;
                var url;
                if (d.id) {
                    url = '/ts-document/onlineDocument/permissionSettings';
                } else {
                    layer.msg('数据异常！');
                    return false;
                }
                if (!d.id) {
                    delete d.id;
                }
                $.loadings();
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    dateType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            $('#name').val("");
                            opt.ref();
                        } else {
                            layer.closeAll();
                            layer.msg('操作成功');
                            $('#name').val("");
                            opt.ref();
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });

            });

        })
    };
});