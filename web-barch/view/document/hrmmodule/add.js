"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layedit = layui.layedit,
                zTreeSearch = layui.zTreeSearch;

            var fileArray = new Array();
            var oldFileArray = new Array();

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['650px', '450px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                        $('#id').val(opt.data.id)
                    }
                    form.render();
                }
            });

            //附件上传
            var personalDocumentFileDataList = $('#personalDocumentFileDataList'),
                uploadListIns = upload.render({
                    elem: '#personalDocumentFileUploadBtn',
                    url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                    accept: 'file',
                    multiple: true,
                    auto: true,
                    choose: function (obj) {
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        $("#personalDocumentFileList").show();
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">',
                                '<td>' + file.name + '</td>',
                                '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>',
                                '<td>等待上传</td>',
                                //'<td>',
                               // '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>',
                                //'</td>',
                                '</tr>'].join(''));

                            //删除
                         /*   tr.find('.demo-delete').on('click', function () {
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                //删除数组中指定元素
                                fileArray = $.grep(fileArray, function (n, i) {
                                    return n['index'] != index;
                                });
                            });*/

                            personalDocumentFileDataList.append(tr);
                        });
                    }
                    , done: function (res, index, upload) {
                        if (res.success == true) { //上传成功
                            var tr = personalDocumentFileDataList.find('tr#upload-' + index),
                                tds = tr.children();
                            tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                            res.object[0].index = index;
                            fileArray.push(res.object[0]);
                            return delete this.files[index]; //删除文件队列已经上传成功的文件
                        }
                        this.error(index, upload);
                    }
                    , error: function (index, upload) {
                        var tr = personalDocumentFileDataList.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                    }
                });


            // 保存
            form.on('submit(personalDocumentSubmitCofirm)', function (data) {
                layer.closeAll();
                layer.msg('操作成功');
                opt.ref();
            });

        })
    };
});