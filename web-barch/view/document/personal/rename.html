<style>
    .addMsgImg .MsgPhoto {
        width: 150px;
        height: 80px;
        border: 1px solid #ccc;
        border-radius: 3px;
        padding: 3px;
        margin-bottom: 5px;
    }

    .addMsgImg .MsgPhotoBtn {
        background: #5260ff;
        color: #fff;
        line-height: 28px;
        border-radius: 3px;
        border: 0;
        width: 100px;
    }

    .addMsgImg .UserNamePhoto {
        width: 180px;
        height: 28px;
        position: absolute;
        top: 0;
    }

    .msgTemplateInputBox {
        display: none;
    }

    .formTabBox span {
        line-height: 30px;
        position: relative;
        top: -1px;
        float: left;
        padding: 0 20px;
        cursor: pointer;
    }

    .formTabBox .on {
        border-bottom: 3px solid #5260ff;
        color: #5260ff;
    }

    #pathListBox p {
        line-height: 30px;
        padding: 10px;
        border-bottom: 1px solid #ccc;
    }

    #pathListBox span {
        float: right;
        cursor: pointer;
        line-height: 24px;
        height: 24px;
        background: #ff0000;
        color: #fff;
        padding: 0 15px;
        margin-top: 3px;
        font-size: 12px;
    }
</style>
<form id="evaBaseAddForm" class="layui-form">
    <input type="hidden" name="id" id="id" value="" />
    <div class="layui-content-box">
        <div class="msgTemplateInputBox" style="display: block">
            <div class="layui-col-xs10">
                <label class="shell-layui-form-label">原文件名</label>
                <div class="shell-layer-input-box">
                    <input type="text" name="originalName" id="originalName" autocomplete="off" lay-verify="required" class="layui-input" />
                </div>
            </div>

            <div class="layui-col-xs10">
                <label class="shell-layui-form-label">
                    <span style="color: #ff0000">*</span>
                    新文件名
                </label>
                <div class="shell-layer-input-box">
                    <input type="text" name="newOriginalName" id="newOriginalName" autocomplete="off" lay-verify="required" class="layui-input" />
                </div>
            </div>
        </div>
    </div>
    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="documentSubmitCofirm">保存</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
    </div>
</form>
