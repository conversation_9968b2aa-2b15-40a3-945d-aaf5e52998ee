<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>文件格式/zip</title>
    <g id="文件格式/zip" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="zip">
            <path d="M27,0 L36,9 L36,34.0000194 C36,35.1045782 35.1045695,36 34,36 L2,36 C0.8954305,36 0,35.1045782 0,34.0000194 L0,2.00032978 C0,0.895770998 0.8954305,0 2,0 L27,0 Z M20,25 L16,25 L16,32 L20,32 L20,25 Z M19,28 L19,31 L17,31 L17,28 L19,28 Z M18,21 L15,21 L15,24 L18,24 L18,21 Z M21,18 L18,18 L18,21 L21,21 L21,18 Z M18,15 L15,15 L15,18 L18,18 L18,15 Z M21,12 L18,12 L18,15 L21,15 L21,12 Z M18,9 L15,9 L15,12 L18,12 L18,9 Z M21,6 L18,6 L18,9 L21,9 L21,6 Z M18,3 L15,3 L15,6 L18,6 L18,3 Z" id="形状结合" fill="#3E76F0"></path>
            <path d="M27,0 L36,9 L29,9 C27.8954305,9 27,8.1045695 27,7 L27,0 L27,0 Z" id="三角形" fill="#9EBAF7"></path>
        </g>
    </g>
</svg>