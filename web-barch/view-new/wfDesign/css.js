(function () {
    var version = new Date().getTime();

    function loadStyles(url) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        var head = document.getElementsByTagName('head')[0];
        head.appendChild(link);
    }

    function loadStyleAfter(url) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        var body = document.getElementsByTagName('body')[0];
        body.appendChild(link);
    }
    loadStyles('/js/plug/layui/css/layui.css');
    loadStyles('/static/css/ui.jqgrid.css');
    loadStyles('/static/css/jquery-ui.css');
    loadStyles('/static/fonts/awesome/font-awesome.min.css');
    loadStyles('/static/fonts/oaIcon/iconfont.css');
    loadStyles('/static/css/zTreeStyle.css');
    loadStyles('/static/css/common.css?' + version);
    window.onload = function () {
        loadStyleAfter('/js/plug/overlayScrollbars/OverlayScrollbars.css');
        loadStyleAfter('/js/plug/overlayScrollbars/os-theme-thick-dark.css');
        loadStyleAfter('/js/plug/gooflow/GooFlow.css');
        loadStyleAfter('/js/plug/viewer/viewer.min.css');
        loadStyleAfter('/static/css/oa.css?' + version);
    };
})();
