<!DOCTYPE html>
<html lang="en">
  <head>
    <title>流程查看</title>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta content="webkit" name="renderer" />
    <script src="/js/plug/jQuery/jquery.js"></script>
    <script src="/js/plug/jQuery/jquery.cookie.js"></script>
    <script src="/js/plug/layui/layui.js"></script>
    <script src="/js/common/config.js"></script>
    <script src="/js/plug/sea.js"></script>
    <script src="/js/plug/seajs-css.js"></script>
    <script src="/js/src/sea.config.js"></script>
    <script src="../css.js?v1.3"></script>
    <script src="/ts-document/pageoffice.js"></script>
    <script src="/view-new/processView/xlsx.js"></script>
  </head>

  <style>
    #HandledByAddNodes {
      display: none;
    }
  </style>

  <body>
    <div id="top">
      <div class="left-wrap">
        <div id="wfTypeInfo" class="left-icon">
          流程<br />在办
        </div>
        <div class="left-info-wrap">
          <p id="wfName"></p>
          <p id="userInfo"></p>
        </div>
      </div>
      <div class="right-wrap">
        <div class="right-btn-wrap none">
          <button class="btn btn_none" id="print">
              <i class="fa fa-print" aria-hidden="true"></i>
              打印
          </button>
          <button class="btn btn_none" id="export">
            <i class="fa fa-file-word-o" aria-hidden="true"></i>
            导出PDF
          </button>
		  <button class="btn btn_none" id="exportWord">
			<i class="fa fa-file-word-o" aria-hidden="true"></i>
			导出WORD
		  </button>
        </div>
        <div class="right-tips" id="equipmentBudget"></div>
      </div>
    </div>
    <div id="nav">
      <span class="nav-item active">
        流程表单
      </span>
      <span class="nav-item">
        流程信息
      </span>
    </div>
    <div id="content">
      <div class="content-box-start form-content-box" id="inner_content">
        <div id="form_content" class="layui-form inner_content">
          <div id="form_box" style="overflow-x: auto"></div>
          <button class="none" lay-submit="" lay-filter="formSubmit"></button>
        </div>
      </div>
      <div class="content-box form-content-box none" id="inner_content">
        <div class="inner_content">
          <div id="taskBox"></div>
          <div id="wfBox" style="overflow: hidden"></div>
        </div>
        <!-- <div class="inner_content none">
                    <div class="jqTable">
                        <table id="taskFileTable"></table>
                    </div>
                </div> -->
      </div>
    </div>
    <div id="foot">
      <div class="content-box" style="text-align: right">
        <!-- 发文文按钮 -->
        <div
          style="display: inline-block; margin-right: 8px"
          class="fwbtn"
        ></div>
        <div style="display: inline-block" class="commonbtn fr">
          <button
            class="layui-btn fl"
            id="Revoke"
            style="background: #2869fb !important; border: 1px solid #2869fb"
          >
            撤销
          </button>
          <button
            class="layui-btn fl"
            id="HandledByAddNodes"
            style="background: #2869fb !important; border: 1px solid #2869fb"
          >
            加签
          </button>
          <button
            class="layui-btn fl none"
            id="editConfirm"
            style="background: #70b603 !important; border: 1px solid #70b603"
          >
            修改提交
          </button>
          <!-- 办理 -->
          <button
            class="layui-btn fl"
            id="wfCopy"
            data-btn-code="10"
            style="background: #009688 !important; border: 1px solid #009688"
          >
            抄送
          </button>
          <button
            class="layui-btn"
            style="background-color: red !important; border: 1px solid red"
            id="cancleWin"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
    <div id="print_box" class="layui-form"></div>
  </body>
</html>
<script>
  layui.config({
    base: '/js/plug/layui/lay/',
    version: true,
  });
  layui.use(['trasen', 'element', 'laydate'], function () {
    var element = (layui.element = layui.element);
    layui.trasen = layui.trasen;
    seajs.use('/js/utils/layVerify.js');
    element.render('nav');
  });
</script>
<script src="../core.js?v1.7"></script>
