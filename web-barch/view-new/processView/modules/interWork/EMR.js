'use strict';
/**
 * 互通通用选择
 * **/
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var selData;
        var dataList = [];
        layui.use(['laydate', 'form', 'laytpl'], function () {
            var layDate = layui.laydate;
            var layForm = layui.form;
            var laytpl = layui.laytpl;
            var trasenTable;
            var wins = layer.open({
                type: 1,
                title: '查询电子病历文书',
                closeBtn: 1,
                shadeClose: false,
                area: ['1200px', '80%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    layForm.render('radio');
                    $('#zy').text(opt.number);
                    //
                    function setzy() {
                        var html = '';
                        for (var i = 0; i < opt.data.length; i++) {
                            html += '<li  key="' + opt.data[i].id + '" index="' + i + '"  title="' + opt.data[i].name + '">' + opt.data[i].name + '</li>';
                        }
                        $('#interWorkEMRDialog .zy').html(html);
                    }
                    setzy();
                    $('#interWorkEMRDialog .zy')
                        .off('click', 'li')
                        .on('click', 'li', function () {
                            if ($(this).hasClass('sel')) {
                                return false;
                            }
                            $(this).addClass('sel').siblings().removeClass('sel');
                            var id = $(this).attr('key');
                            selData = opt.data[$(this).index()];
                            getPay(id);
                        });
                    $('#interWorkEMRDialog .zy li').eq(0).trigger('click');
                    //获取电子病历
                    function getPay(id) {
                        trasenTable && $.jgrid.gridUnload('grid-table-interWorkDialog');
                        trasenTable = new $.trasenTable('grid-table-interWorkDialog', {
                            datatype: 'json',
                            url: '/ts-external/emrApi/getMedicalRecord',
                            // multiboxonly: query.multiselect,
                            multiselect: true,
                            pager: 'grid-pager-interWorkDialog',
                            mtype: 'post',
                            shrinkToFit: true,
                            sortorder: 'desc',
                            sortname: 'create_date',
                            postData: {
                                id: id,
                            },
                            rowNum: 1000,
                            // rowList: [100, 200],
                            jsonReader: {
                                root: 'object', // json中代表实际模型数据的入口
                                page: 'pageNo', // json中代表当前页码的数据
                                total: 'pageCount', // json中代表页码总数的数据
                                records: 'totalCount', // json中代表数据行总数的数据
                                userdata: 'object',
                                repeatitems: false,
                            },
                            colModel: [
                                {
                                    label: '电子病历文书(已签)',
                                    name: 'name',
                                    width: 300,
                                    sortable: false,
                                    align: 'center',
                                },
                            ],
                        });
                    }

                    function getTableData() {
                        var arr = [];
                        var ids = trasenTable.getSelectAllRowIDs();
                        for (var i = 0; i < ids.length; i++) {
                            arr.push(trasenTable.getSourceRowData(ids[i]));
                        }
                        return arr;
                    }
                    //提交
                    $(layero)
                        .off('click', '#interWorkEMRCheck')
                        .on('click', '#interWorkEMRCheck', function () {
                            var data = getTableData();
                            if (data.length == 0) {
                                layer.msg('请先选择电子病历');
                                return false;
                            }
                            opt.callBack(data);
                            layer.close(wins);
                            return false;
                        });
                    //关闭
                    $(layero)
                        .off('click', '#close')
                        .on('click', '#close', function () {
                            layer.close(wins);
                        });
                },
            });
        });
    };
});
