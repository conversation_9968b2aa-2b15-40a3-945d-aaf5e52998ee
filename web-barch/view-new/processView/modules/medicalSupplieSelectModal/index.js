define(function (require, exports, module) {
  'use strict';
  exports.init = function (opt, html) {
    layui.use(['form', 'trasen', 'zTreeSearch', 'upload'], function () {
      var form = layui.form,
        trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch,
        layUpload = layui.upload,
        medicalSupplieTable = null,
        selectedData = [];

      layer.open({
        type: 1,
        title: '基本物品字典',
        closeBtn: 1,
        maxmin: false,
        shadeClose: false,
        area: ['80%', '70%'], //宽高
        content: html,
        success: function (layero, layIndex) {
          selectedData = opt.data || [];
          commonDictCache.getDict('HIS_PHARMACY', function (arr) {
            let selArr = arr.map((item) => {
              return {
                label: item.itemName,
                value: item.itemCode,
              };
            });
            new $.selectPlug(
              '#medicalSupplieSelectModalContent #drgRoomDeptSelBox',
              {
                searchType: 'local',
                data: selArr,
                textName: 'label',
                valName: 'value',
                inpValName: 'drgRoomDeptId',
                inpTextName: 'drgRoomDeptName',
                placeholder: '请选择药房',
                callback: function (res) {},
              }
            );
          });
          render();
        },
      });

      function render() {
        medicalSupplieTable = new $.trasenTable(
          'medicalSupplieSelectModalContent #grid-table-medicalSupplieTable',
          {
            url: '/ts-external/hisApi/QueryBasicsDrgDictionary',
            pager: '#grid-page-medicalSuppliePage',
            mtype: 'GET',
            shrinkToFit: true,
            rowNum: 15,
            multiselect: true, //复选框
            colModel: [
              {
                label: '药品id',
                name: 'drugId',
                key: true,
                hidden: true,
              },
              {
                label: '品名',
                name: 'chemicalName',
              },
              {
                label: '规格',
                name: 'spec',
                width: 60,
              },
              {
                label: '单位',
                name: 'unitName',
                width: 30,
                align: 'center',
              },
              {
                label: '生产厂家',
                name: 'factName',
              },
              {
                label: '供货单位',
                name: 'supplierName',
              },
              {
                label: '进货价',
                name: 'buyPrice',
                align: 'right',
                width: 60,
              },
            ],
            buidQueryParams: function () {
              let queryArray =
                $('#medicalSupplieSearchForm').serializeArray() || [];
              return queryArray.reduce((prev, next) => {
                let { name, value = '' } = next;
                name && (prev[name] = value);
                return prev;
              }, {});
            },
            gridComplete: function () {
              selectedData.map((item) => {
                $(this).setSelection(item.drugId, false);
              });
            },
            onSelectRow: function (rowId, checked) {
              if (checked) {
                let rowData = $(this).getRowData(rowId);
                selectedData.push(rowData);
              } else {
                let deleteIndex = selectedData.findIndex(
                  (item) => item.drugId == rowId
                );
                deleteIndex >= 0 && selectedData.splice(deleteIndex, 1);
              }
            },
          }
        );

        // setSelection
      }
      function refresh() {
        if (medicalSupplieTable) {
          medicalSupplieTable.refresh();
        }
      }

      /**@desc 搜索 */
      $('#medicalSupplieSearchBtn').funs('click', function () {
        refresh();
      });
      /**@desc 重置 */
      $('#medicalSupplieResetBtn').funs('click', function () {
        if ($('#medicalSupplieSearchForm')[0]) {
          $('#medicalSupplieSearchForm')[0].reset();
        }
        refresh();
      });
      /**@desc 关闭 */
      $('#medicalSupplieSelectModalContent [name="cancelBtn"]').funs(
        'click',
        function () {
          layer.closeAll();
        }
      );
      /**@desc 确认 */
      $(
        '#medicalSupplieSelectModalContent [name="medicalSupplieConfirmBtn"]'
      ).funs('click', function () {
        opt.callBack(selectedData);
        layer.closeAll();
      });
    });
  };
});
