<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>CodeMirror: Autoresize Demo</title>
    <link rel="stylesheet" href="../lib/codemirror.css">
    <script src="../lib/codemirror.js"></script>
    <script src="../mode/css/css.js"></script>
    <link rel="stylesheet" href="../doc/docs.css">

    <style type="text/css">
      .CodeMirror {
        border: 1px solid #eee;
        height: auto;
      }
      .CodeMirror-scroll {
        overflow-y: hidden;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>CodeMirror: Autoresize demo</h1>

    <form><textarea id="code" name="code">
.CodeMirror {
  border: 1px solid #eee;
  height: auto;
}
.CodeMirror-scroll {
  overflow-y: hidden;
  overflow-x: auto;
}
</textarea></form>

<p>By setting a few CSS properties, CodeMirror can be made to
automatically resize to fit its content.</p>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true
      });
    </script>

  </body>
</html>
