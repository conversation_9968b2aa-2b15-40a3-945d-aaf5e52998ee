<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>CodeMirror: Vim bindings demo</title>
    <link rel="stylesheet" href="../lib/codemirror.css">
    <script src="../lib/codemirror.js"></script>
    <script src="../lib/util/dialog.js"></script>
    <script src="../lib/util/searchcursor.js"></script>
    <script src="../mode/clike/clike.js"></script>
    <script src="../keymap/vim.js"></script>
    <link rel="stylesheet" href="../doc/docs.css">
    <link rel="stylesheet" href="../lib/util/dialog.css">

    <style type="text/css">
      .CodeMirror {border-top: 1px solid #eee; border-bottom: 1px solid #eee;}
    </style>
  </head>
  <body>
    <h1>CodeMirror: Vim bindings demo</h1>

    <form><textarea id="code" name="code">
#include "syscalls.h"
/* getchar:  simple buffered version */
int getchar(void)
{
  static char buf[BUFSIZ];
  static char *bufp = buf;
  static int n = 0;
  if (n == 0) {  /* buffer is empty */
    n = read(0, buf, sizeof buf);
    bufp = buf;
  }
  return (--n >= 0) ? (unsigned char) *bufp++ : EOF;
}
</textarea></form>

    <form><textarea id="code2" name="code2">
        I am another file! You can yank from my neighbor and paste here.
</textarea></form>

<p>The vim keybindings are enabled by
including <a href="../keymap/vim.js">keymap/vim.js</a> and setting
the <code>keyMap</code> option to <code>"vim"</code>. Because
CodeMirror's internal API is quite different from Vim, they are only
a loose approximation of actual vim bindings, though.</p>

    <script>
      CodeMirror.commands.save = function(){ alert("Saving"); };
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "text/x-csrc",
        keyMap: "vim",
        showCursorWhenSelecting: true
      });
      var editor2 = CodeMirror.fromTextArea(document.getElementById("code2"), {
        lineNumbers: true,
        mode: "text/x-csrc",
        keyMap: "vim",
        showCursorWhenSelecting: true
      });
    </script>

  </body>
</html>
