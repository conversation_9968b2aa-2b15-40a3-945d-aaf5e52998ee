<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>CodeMirror: PHP mode</title>
    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="../../lib/util/matchbrackets.js"></script>
    <script src="../htmlmixed/htmlmixed.js"></script>
    <script src="../xml/xml.js"></script>
    <script src="../javascript/javascript.js"></script>
    <script src="../css/css.js"></script>
    <script src="../clike/clike.js"></script>
    <script src="php.js"></script>
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
    <link rel="stylesheet" href="../../doc/docs.css">
  </head>
  <body>
    <h1>CodeMirror: PHP mode</h1>

<form><textarea id="code" name="code">
<?php
function hello($who) {
	return "Hello " . $who;
}
?>
<p>The program says <?= hello("World") ?>.</p>
<script>
	alert("And here is some JS code"); // also colored
</script>
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "application/x-httpd-php",
        indentUnit: 4,
        indentWithTabs: true,
        enterMode: "keep",
        tabMode: "shift"
      });
    </script>

    <p>Simple HTML/PHP mode based on
    the <a href="../clike/">C-like</a> mode. Depends on XML,
    JavaScript, CSS, HTMLMixed, and C-like modes.</p>

    <p><strong>MIME types defined:</strong> <code>application/x-httpd-php</code> (HTML with PHP code), <code>text/x-php</code> (plain, non-wrapped PHP code).</p>
  </body>
</html>
