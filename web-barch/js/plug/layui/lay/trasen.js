'use strict';
layui.define(['form', 'table', 'layer', 'laypage', 'laydate', 'element', 'upload', 'cityAddress', 'uploadList', 'deptSel'], function (exports) {
    //do something
    var $ = layui.jquery;
    var form = layui.form;
    var laypage = layui.laypage;
    var layer = layui.layer;
    var table = layui.table;
    var element = layui.element;
    var upload = layui.upload;
    var cityAddress = layui.cityAddress;
    var uploadList = layui.uploadList;
    var deptSel = layui.deptSel;

    var ajaxloadingcount = 0,
        screenLayer,
        loading;

    exports('trasen', {
        /**
         * 组织机构选择
         * @param msg
         */
        orgSelect: function (options) {
            var def = {
                form: form,
                element: element,
                dom: '[trasen_org="select"]:input',
                title: '选择使用范围',
                tab: [{
                        code: 1,
                        type: 'tree',
                        elem: 'company',
                        title: '按机构',
                        src: common.url + '/ts-hr/organization/getTree',
                    },
                    {
                        code: 2,
                        type: 'tree',
                        elem: 'department',
                        title: '按部门',
                        src: common.url + '/ts-hr/organization/getTree',
                    },
                    {
                        code: 3,
                        type: 'table',
                        elem: 'persen',
                        title: '按人员',
                        src: common.url + '/ts-hr/employee/list',
                    },
                ],
                callback: function (data) {
                    console.log(data);
                },
            };
            def = $.extend({}, def, options);
            new $.orgSelect(def);
        },

        /**
         * 加载中
         * @param msg
         */
        loading: function (msg) {
            loading = layer.load(); //上传loading;
        },
        /**
         * 加载完成
         * @param msg
         */
        closeLoading: function () {
            layer.closeAll('loading'); //关闭loading
        },
        /**
         * 消息成功
         * @param msg
         */
        info: function (msg) {
            layer.msg(msg, {
                icon: 1,
                time: 3000
            });
        },
        ajax: function (options) {
            var that = this;
            if (typeof options === 'object') {
                var params = options.data ? options.data : {};
                var unloading = params.unloading;
                if (!unloading) {
                    that.loading('正在执行...');
                    ajaxloadingcount++;
                }
                $.ajax({
                    type: options.type,
                    url: options.url,
                    dataType: 'json',
                    contentType: options.contentType || 'application/json; charset=utf-8',
                    timeout: options.timeout ? options.timeout : 60000, //超时1分钟
                    data: params,
                    success: function (data) {
                        if (!unloading) {
                            ajaxloadingcount--;
                            if (ajaxloadingcount <= 0) that.closeLoading();
                        }
                        if (data.success) {
                            if (data.message) {
                                that.info(data.message || '操作成功');
                            }
                            if (options.success) {
                                options.success(data);
                            }
                        } else {
                            var message = '';
                            if (data.message) {
                                message = data.message;
                            } else {
                                message = '服务器返回未知错误！';
                            }
                            if (options.error) {
                                options.error(message);
                            } else {
                                that.error(message);
                            }
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        if (!unloading) {
                            ajaxloadingcount--;
                            if (ajaxloadingcount <= 0) that.closeLoading();
                        }
                        var message = '您的网络不太给力哦，请稍后再试!';
                        if (options.error) {
                            options.error(message);
                        } else {
                            that.error(message);
                        }
                    },
                });
            }
        },
        /**
         * 异步POST请求
         * @param url
         * url地址
         * @param params
         * 参数
         * @param success
         * 成功回调方法
         * @param error
         * 失败回调方法
         */
        post: function (url, params, success, error) {
            this.ajax({
                url: url,
                type: 'post',
                data: params,
                success: success,
                error: error
            });
        },
        /**
         * 异步GET请求
         * @param url
         * url地址
         * @param success
         * 成功回调方法
         * @param error
         * 失败回调方法
         */
        get: function (url, success, error) {
            this.ajax({
                url: url,
                type: 'get',
                success: success,
                error: error
            });
        },
        /**
         *获取dom节点值(注:节点必须包含name属性)
         * @param $names
         * @returns {{}}
         */
        getNamesVal: function ($form) {
            var that = this;
            var $names;
            if ($form.size() == 1 && !$form.attr('name')) {
                $names = $form.find('[name]');
            }
            var row = {};
            $names.each(function () {
                var me = $(this);
                if (that.isTextField(me[0].tagName)) {
                    var inputtype = me.attr('type');
                    if ('radio' == inputtype) {
                        if (me.is(':checked')) {
                            row[me.attr('name')] = $(this).val();
                        }
                    } else if ('checkbox' == inputtype) {
                        if (!row[me.attr('name')]) {
                            row[me.attr('name')] = [];
                        }
                        if (me.is(':checked')) {
                            row[me.attr('name')].push($(this).val());
                        }
                    } else {
                        row[me.attr('name')] = $(this).val();
                    }
                } else {
                    row[me.attr('name')] = $(this).text();
                }
            });
            for (var key in row) {
                if ($.isArray(row[key])) {
                    row[key] = row[key].join(',');
                }
            }
            return row;
        },
        /**
         * 给dom节点批量赋值(注:节点必须包含name属性)
         */
        setNamesVal: function ($form, obj, has) {
            var $names;
            var that = this;
            if ($form.size() == 1 && !$form.attr('name')) {
                $names = $form.find('[name]');
            }
            if (obj instanceof Array) {
                if (obj.length > 0) {
                    obj = obj[0];
                } else {
                    return;
                }
            }
            for (var key in obj) {
                var name = key;
                var $el = $form.find('[name="' + name + '"]');
                if ($el.length) {
                    if (that.isTextField($el[0].tagName)) {
                        var type = $el.attr('type');
                        if (type == 'radio') {
                            for (var i = 0; i < $el.length; i++) {
                                if (obj[name] == $($el[i]).val().toString()) {
                                    $($el[i]).prop('checked', true);
                                } else {
                                    $($el[i]).prop('checked', false);
                                }
                            }
                        } else if (type == 'checkbox') {
                            if (obj[name]) {
                                var vs = obj[name].toString().split(',');
                                for (var i = 0; i < $el.length; i++) {
                                    if (vs.indexOf($($el[i]).val().toString()) != -1) {
                                        $($el[i]).prop('checked', true);
                                    } else {
                                        $($el[i]).prop('checked', false);
                                    }
                                }
                            } else {
                                $el.prop('checked', false);
                            }
                        } else {
                            $el.val(obj[name]);
                        }
                    } else {
                        $el.html(obj[name]);
                    }
                }
            }
        },
        isTextField: function (tag) {
            return tag == 'INPUT' || tag == 'input' || tag == 'SELECT' || tag == 'select' || tag == 'textarea' || tag == 'TEXTAREA';
        },
        /**
         * ztree树
         **/
        ztree: function (dom, options) {
            var that = this;
            var setting = {
                check: {
                    enable: options.checkbox,
                },
                data: {
                    simpleData: {
                        enable: true,
                    },
                },
                view: options.view || {},
                callback: {
                    beforeAsync: true,
                    onCheck: function (event, treeId, treeNode) {
                        // 选择事件
                        if (options.zTreeOnCheck) {
                            options.zTreeOnCheck(event, treeId, treeNode);
                        }
                    },
                    onClick: function (event, treeId, treeNode) {
                        // 点击事件
                        if (options.zTreeOnClick) {
                            options.zTreeOnClick(event, treeId, treeNode);
                        }
                    },
                },
            };
            $.ajax({
                url: options.url,
                type: options.type || 'post', 
                data: options.data || {},
                success: function (res) {
                    if (res.success && res.object) {
                        var ztreeo = $.fn.zTree.init($(dom), setting, res.object);
                        if (options.zTreeAsyncSuccess) {
                            options.zTreeAsyncSuccess(ztreeo)
                        }
                        //展开所有节点
                        if (options.open == 'all') {
                            ztreeo.expandAll(true);
                        }
                        if (options.open == 'first') {
                            var nodesList = ztreeo.getNodesByParam('level', 0);
                            for (var i = 0; i < nodesList.length; i++) {
                                ztreeo.expandNode(nodesList[i], true, false, false);
                            }
                        }
                        if (options.zTreeOnSuccess) {
                            options.zTreeOnSuccess();
                        }
                    }
                },
            });
        },
        // 附件上传
        uploadList: uploadList.funs,
        //省市区联动
        cityAddress: cityAddress.funs,
        //
        //部门选择
        deptSel: deptSel.funs,
    });
});