<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsOmMeauMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsOmMeau">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_om_meau_id" jdbcType="VARCHAR" property="pkOmMeauId"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="input_content" jdbcType="VARCHAR" property="inputContent"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
    </resultMap>

    <sql id="OmMeauColums">
        pk_om_meau_id,
        dept_id,
        dept_name,
        input_content,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        px,
        mac,
        file_required
    </sql>

    <insert id="insertOmMeau">
        insert into ws_om_meau(
            pk_om_meau_id,
            dept_id,
            dept_name,
            input_content,
            create_by,
            create_time,
            update_by,
            update_time,
            delete_status,
            mac,
            file_required
        )
        values(
        #{pkOmMeauId},
        #{deptId},
        #{deptName},
        #{inputContent},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{mac},
        #{fileRequired}
        )
    </insert>
    <update id="updateOmMeau">
        update ws_om_meau set
        dept_id = #{deptId},
        dept_name = #{deptName},
        input_content = #{inputContent},
        update_by = #{updateBy},
        update_time = #{updateTime},
        delete_status = #{deleteStatus},
        px = #{px},
        file_required = #{fileRequired}
        where pk_om_meau_id = #{pkOmMeauId}
    </update>
    <delete id="deleteOmMeau">
        update ws_om_meau set delete_status = 1
        where pk_om_meau_id in(
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
    </delete>
    <select id="selectOmMeauList" resultType="cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo">
        select <include refid="OmMeauColums" />
        from ws_om_meau
        where delete_status = 0
    </select>
    <select id="seleteOneOmMeau" resultType="cn.trasen.worksheet.module.entity.WsOmMeau">
        select <include refid="OmMeauColums" />
        from ws_om_meau
        where
        delete_status = 0
        <if test="null != pkOmMeauId and '' != pkOmMeauId ">
            and pk_om_meau_id = #{pkOmMeauId}
        </if>
        <if test="null != deptId and '' != deptId ">
            and dept_id = #{deptId}
        </if>
        <if test="null != inputContent and '' != inputContent ">
            and input_content = #{inputContent}
        </if>
    </select>
    <select id="selectOmMeauAllList" resultType="cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo">
        select <include refid="OmMeauColums" />
        from ws_om_meau
        where delete_status = 0
        ORDER BY px
    </select>
    <select id="seleteOneOmMeauByWorkNumber" resultType="cn.trasen.worksheet.module.entity.WsOmMeau">
        SELECT <include refid="OmMeauColums"/>
        from (select business_dept_id,work_number from ws_ws_sheet) a left join ws_om_meau b on a.business_dept_id = b.dept_id
        where a.work_number = #{workNumber} and b.delete_status = 0

    </select>
</mapper>