<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsCustometLogMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsCustometLog">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_customet_log_id" jdbcType="VARCHAR" property="pkCustometLogId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="call_type" jdbcType="TINYINT" property="callType"/>
        <result column="equipment_id" jdbcType="VARCHAR" property="equipmentId"/>
        <result column="fk_usre_id" jdbcType="VARCHAR" property="fkUserId"/>
        <result column="visit_phone" jdbcType="VARCHAR" property="visitPhone"/>
        <result column="call_phone" jdbcType="VARCHAR" property="callPhone"/>
    </resultMap>

    <sql id="CustometLogColums">
        pk_customet_log_id
        ,
        work_number,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        call_type,
        call_work_status,
        equipment_id,
        fk_user_id,
        visit_phone,
        call_phone,
        visit_user_id,
        visit_user_name,
        visit_user_dept_id,
        visit_user_dept_name,
        is_read,
        business_dept_id
    </sql>

    <insert id="insertWsCustometLog">
        insert into ws_customet_log(<include refid="CustometLogColums"/>)
        values (
        #{pkCustometLogId},
        #{workNumber},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{callType},
        #{callWorkStatus},
        #{equipmentId},
        #{fkUserId},
        #{visitPhone},
        #{callPhone},
        #{visitUserId},
        #{visitUserName},
        #{visitUserDeptId},
        #{visitUserDeptName},
        #{isRead},
        #{businessDeptId}
        )
    </insert>
    <update id="updateWsCustometLog">
        update ws_customet_log
        set call_type = #{callType},
        call_work_status = #{callWorkStatus},
        is_read = #{isRead},
        update_time = #{updateTime},
        update_by = #{updateBy}
        <if test="null != workNumber and ''!=workNumber">
            ,work_number = #{workNumber}
        </if>
        where pk_customet_log_id = #{pkCustometLogId}
    </update>
    <update id="updateBatchWsCustometLog">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE ws_customet_log
            set
            call_type = #{item.callType},
            call_work_status = #{item.callWorkStatus}
            WHERE pk_customet_log_id = #{item.pkCustometLogId}
        </foreach>
    </update>
    <update id="updateIsReadByDeptId">
        UPDATE ws_customet_log set is_read = 1
        where business_dept_id in
        (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
    </update>
    <select id="selectAllList" resultType="cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo">
        select
        <include refid="CustometLogColums"/>, create_time as visitTime
        from ws_customet_log
        where delete_status = 0
        <if test="null != pkCustometLogId and '' != pkCustometLogId">
            and pk_customet_log_id != #{pkCustometLogId}
        </if>
        <if test="null != callType and '' != callType">
            and call_type = #{callType}
        </if>
        <if test="null != callWorkStatus and '' != callWorkStatus">
            and call_work_status = #{callWorkStatus}
        </if>
        <if test="null != fkUserId and '' != fkUserId">
            and fk_user_id = #{fkUserId}
        </if>
        and work_number is null
        order by create_time desc

    </select>
    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsCustometLog">
        select
        <include refid="CustometLogColums"/>
        from ws_customet_log
        where pk_customet_log_id = #{pkCustometLogId}
    </select>
    <select id="selectCallRecordsPageList"
            resultType="cn.trasen.worksheet.module.dto.outVo.WsCustometLogCallRecordsOutVo">
        SELECT a.pk_customet_log_id,b.pk_ws_sheet_id,d.pk_ws_task_id,a.work_number,a.create_time visitTime,
        a.visit_user_id,a.visit_user_name,a.visit_user_dept_id,a.visit_user_dept_name,a.visit_phone,
        b.fault_deion,c.file_url,a.call_type,
        CASE
        WHEN a.call_type = 0 THEN '未接听'
        WHEN a.call_type = 1 THEN '已接' END call_type_name,
        CASE
        WHEN b.work_status IS NOT NULL THEN b.work_status
        WHEN b.work_status IS NULL AND a.call_work_status = 0 THEN '未接听'
        WHEN b.work_status IS NULL AND a.call_work_status = 1 THEN '未建单'
        WHEN b.work_status IS NULL AND a.call_work_status = 2 THEN '无效来电' END remark
        FROM ws_customet_log a
        LEFT JOIN ws_ws_sheet b ON a.work_number = b.work_number
        LEFT JOIN (select fk_customet_log_id,GROUP_CONCAT(file_url) file_url from ws_file_file GROUP BY
        fk_customet_log_id) c ON a.pk_customet_log_id = c.fk_customet_log_id
        LEFT JOIN (SELECT pk_ws_task_id, work_number
        FROM ws_ws_task
        WHERE complete = 0
        AND delete_status = 0) d ON a.work_number = d.work_number
        where a.delete_status = 0 and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        <if test='null != type and ""!=type'>
            and a.work_number is null
            <choose>
                <when test='"1" == type'>
                    and a.call_work_status in (0,1)
                </when>
                <when test='"2" == type'>
                    and a.call_work_status = 1
                </when>
                <when test='"3" == type'>
                    and a.call_work_status = 0
                </when>
            </choose>
        </if>
        <if test="null != workStatus and ''!=workStatus">
            and b.work_status = #{workStatus}
        </if>
        <if test="null != beginCallTime">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') = DATE_FORMAT(#{beginCallTime},'%Y-%m-%d')
        </if>
        <if test="null != endCallTime ">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') = DATE_FORMAT(#{endCallTime},'%Y-%m-%d')
        </if>
        <if test="null != callType and ''!=callType">
            and a.call_type = #{callType}
        </if>
        <if test="null != beginTime">
            AND DATE_FORMAT(b.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
        </if>
        <if test="null != endTime ">
            AND DATE_FORMAT(b.CREATE_TIME,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        </if>
        <if test="null != repairManDeptId and ''!=repairManDeptId">
            and a.visit_user_dept_id = #{repairManDeptId}
        </if>
        <if test="null != repairManId and ''!=repairManId">
            and a.visit_user_id = #{repairManId}
        </if>
    </select>
    <select id="selectListByCustometLog" resultType="cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo">
        select
        <include refid="CustometLogColums"/>, create_time as visitTime
        from ws_customet_log
        where delete_status = 0
        <if test="null != pkCustometLogId and '' != pkCustometLogId">
            and pk_customet_log_id = #{pkCustometLogId}
        </if>
        <if test="null != callType and '' != callType">
            and call_type = #{callType}
        </if>
    </select>
    <select id="selectAllById" resultType="cn.trasen.worksheet.module.entity.WsCustometLog">
        select
        <include refid="CustometLogColums"/>, create_time as visitTime
        from ws_customet_log
        where delete_status = 0
        and pk_customet_log_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="selectOneByWorkNumber" resultType="cn.trasen.worksheet.module.entity.WsCustometLog">
        select
        <include refid="CustometLogColums"/>
        from ws_customet_log
        where work_number = #{workNumber}
    </select>
    <select id="workOrderNotProcessedCounts" resultType="java.util.Map">
        SELECT
        (
            SELECT count( 1 ) FROM ws_customet_log a
            WHERE is_read = 0
            <!-- 根据数据权限展示科室所有业务数量 -->
            and ( a.business_dept_id in
                    (<foreach collection="fkDeptId" index="index" item="item" separator=",">#{item}</foreach>)
                )
        ) isRead,
        (
            select count(1)
            from ws_customet_log a
            where call_work_status in (0, 1)
            AND work_number IS NULL
            <!-- 根据数据权限展示科室所有业务数量 -->
            and a.business_dept_id in
                (<foreach collection="fkDeptId" index="index" item="item" separator=",">#{item}</foreach>)
        ) wjd
        FROM
        DUAL
    </select>
    <select id="serviceDeskStatisticsToday" resultType="java.util.Map">
        SELECT a.yj,
        b.wj,
        c.hc,
        d.dhjj,
        e.jd,
        f.wxjd
        FROM (SELECT count(1) yj
        FROM ws_customet_log a
        WHERE (call_type = 1 or call_work_status = 2)
        and a.business_dept_id in
            (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
      	<choose>
      		<when test="_databaseId=='kingbase'">
		        and a.create_time &gt; TO_DATE(#{beginTime},
		        '%Y-%m-%d %H:%i:%s')
		        AND a.create_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) a,
      		</when>
      		<otherwise>
		        and a.create_time &gt; STR_TO_DATE(#{beginTime},
		        '%Y-%m-%d %H:%i:%s')
		        AND a.create_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) a,
      		</otherwise>
      	</choose>
        (SELECT count(1) wj
        FROM ws_customet_log a
        WHERE call_type = 0
        and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        <choose>
        	<when test="_databaseId=='kingbase'">
		        and a.create_time &gt; TO_DATE(#{beginTime},
		        '%Y-%m-%d %H:%i:%s')
		        AND a.create_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) b,
        	</when>
        	<otherwise>
		        and a.create_time &gt; STR_TO_DATE(#{beginTime},
		        '%Y-%m-%d %H:%i:%s')
		        AND a.create_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) b,
        	</otherwise>
        </choose>
        (SELECT count(1) hc
        FROM ws_customet_log a
        WHERE call_work_status = 5
        and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        <choose>
        	<when test="_databaseId=='kingbase'">
		        and a.update_time &gt; TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) c,        	
        	</when>
        	<otherwise>
		        and a.update_time &gt; STR_TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) c,
        	</otherwise>
        </choose>
        (SELECT count(1) dhjj
        FROM ws_customet_log a
        WHERE a.call_work_status = 4
        and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        <choose>
        	<when test="_databaseId=='kingbase'">
		        and a.update_time &gt; TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) d,
        	</when>
        	<otherwise>
		        and a.update_time &gt; STR_TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) d,
        	</otherwise>
        </choose>
        (select count( DISTINCT c.work_number) jd from (
            SELECT a.work_number
            FROM ws_ws_sheet a
            <choose>
            	<when test="_databaseId=='kingbase'">
		            WHERE create_time &gt; TO_DATE(
		            #{beginTime}, '%Y-%m-%d %H:%i:%s')
		            and business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
		            AND create_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
		            union all
		            SELECT a.work_number FROM ws_ws_sheet a left join ws_ws_task b on a.work_number = b.work_number
		            WHERE TAKE_REMARK LIKE '工单流转%' AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		            AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
            	</when>
            	<otherwise>
		            WHERE create_time &gt; STR_TO_DATE(
		            #{beginTime}, '%Y-%m-%d %H:%i:%s')
		            and business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
		            AND create_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
		            union all
		            SELECT a.work_number FROM ws_ws_sheet a left join ws_ws_task b on a.work_number = b.work_number
		            WHERE TAKE_REMARK LIKE '工单流转%' AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		            AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
            	</otherwise>
            </choose>
            and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        ) c
        ) e,
        (SELECT count(1) wxjd
        FROM ws_customet_log a
        WHERE call_work_status = 2
        and a.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        <choose>
        	<when test="_databaseId=='kingbase'">
		        and a.update_time &gt; TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) f
        	</when>
        	<otherwise>
		        and a.update_time &gt; STR_TO_DATE(
		        #{beginTime}, '%Y-%m-%d %H:%i:%s')
		        AND a.update_time &lt; STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')) f
        	</otherwise>
        </choose>
    </select>
	
    <select id="numberOfDailyRepairReports" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ws_customet_log
        WHERE visit_phone = #{phone}
        <choose>
        	<when test="_databaseId=='kingbase'">
		          AND TO_DATE(create_time, '%Y-%m-%d') = TO_DATE(now(), '%Y-%m-%d')
        	</when>
        	<otherwise>
		          AND STR_TO_DATE(create_time, '%Y-%m-%d') = STR_TO_DATE(now(), '%Y-%m-%d')
        	</otherwise>
        </choose>
    </select>
    <select id="selectDayByDeptId"
            resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkReportCallsListOutVo">
        SELECT
            a.visit_user_name fkUserName,
            a.visit_user_dept_name fkUserDeptName,
            count( 1 ) count
        FROM
            ws_customet_log a
        WHERE
            a.business_dept_id = #{fkDeptId}
       <choose>
       	<when test="_databaseId=='kingbase'">
            AND DATE_FORMAT( a.create_time, '%Y-%m-%d' ) = DATE_FORMAT( CURRENT_DATE, '%Y-%m-%d' )
       	</when>
       	<otherwise>
       	  	AND DATE_FORMAT( a.create_time, '%Y-%m-%d' ) = DATE_FORMAT( now(), '%Y-%m-%d' )
       	</otherwise>
       </choose>
        GROUP BY a.visit_user_id
    </select>
    <select id="fillFileWorkNumberInfo" resultType="java.util.Map">
        SELECT
            a.pk_customet_log_id,
               a.work_number
        FROM
            ws_customet_log a
                LEFT JOIN ws_file_file b ON a.pk_customet_log_id = b.fk_customet_log_id
        WHERE
            a.work_number IS NOT NULL and b.work_number is null

    </select>
    
    <update id="updateCalltype">
    	update ws_customet_log set call_type = 1,call_work_status = 1
		where pk_customet_log_id in (
		select pk_customet_log_id from (
			select t1.pk_customet_log_id from ws_customet_log t1
			 LEFT JOIN ws_file_file t2 on t1.pk_customet_log_id = t2.fk_customet_log_id
			 where t1.delete_status = 0 and t1.call_type = 0 and t2.file_url is not null 
			) t
		)
    </update>
</mapper>