<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsKnowledgeTypeMapper">

    <sql id="knowledgeTypeColums">
        pk_knowledge_type_id
        ,
    create_by,
    create_time,
    update_by,
    update_time,
    delete_status,
    full_path,
    parent_id,
    category_name,
    knowledge_status
    </sql>


    <insert id="insertKnowledgeType">
        insert into ws_knowledge_type(<include refid="knowledgeTypeColums"/>)
        values(
        #{pkKnowledgeTypeId},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{fullPath},
        #{parentId},
        #{categoryName},
        #{knowledgeStatus}
        )

    </insert>

    <update id="updateKnowledgeType">
        update ws_knowledge_type
        set update_by        = #{updateBy},
            update_time      = #{updateTime},
            full_path        = #{fullPath},
            parent_id        = #{parentId},
            category_name    = #{categoryName},
            knowledge_status = #{knowledgeStatus}
        where pk_knowledge_type_id = #{pkKnowledgeTypeId}

    </update>

    <update id="updateBatchStatus">
        UPDATE ws_knowledge_type
        set knowledge_status = #{status}
        where pk_knowledge_type_id in
        (
        <foreach collection="idsList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <update id="deleteBatch">
        UPDATE ws_knowledge_type
        set delete_status = 1
        where pk_knowledge_type_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <delete id="deleteKnowledgeType">
        update ws_knowledge_type
        set delete_status = 1
        where pk_knowledge_type_id = #{pkKnowledgeTypeId}

    </delete>

    <select id="selectKnowledgeTypePageList" resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeListOutVo">
        select
        <include refid="knowledgeTypeColums"/>
        ,parent_id pid
        from ws_knowledge_type
        where delete_status = 0
        <if test="null != categoryName and '' != categoryName">
            and category_name like concat('%',#{categoryName},'%')
        </if>
        <if test="null != list ">
            and pk_knowledge_type_id in(<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
    </select>

    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsKnowledgeType">
        select
        <include refid="knowledgeTypeColums"/>
        from ws_knowledge_type
        where pk_knowledge_type_id = #{pkKnowledgeTypeId}
    </select>


    <select id="selectKnowledgeTypeAllList" resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo">
        select
        pk_knowledge_type_id id,
        pk_knowledge_type_id code,
        full_path fullPath,
        parent_id pid,
        category_name name,
        count(1) count
        from ws_knowledge_type a
        where delete_status = 0
        <if test="null != categoryName and '' != categoryName">
            and category_name like concat('%',#{categoryName},'%')
        </if>
        GROUP BY a.pk_knowledge_type_id,full_path,parent_id,category_name
    </select>

    <select id="selectKnowledgeTypeAllListContainsDisable"
            resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo">
        select pk_knowledge_type_id id,
               pk_knowledge_type_id code,
               full_path            fullPath,
               parent_id            pid,
               category_name        name
        from ws_knowledge_type a
        where a.delete_status = 0 and knowledge_status = 1
    </select>
</mapper>