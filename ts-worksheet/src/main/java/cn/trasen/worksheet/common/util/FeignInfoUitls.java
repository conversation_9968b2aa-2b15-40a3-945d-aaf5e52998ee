package cn.trasen.worksheet.common.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import cn.trasen.homs.bean.base.EmployeeListReq;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsEmployeeSaveReq;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.PageDataReq;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.service.SpringContextWorkSheet;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/6/23 11:10
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
public class FeignInfoUitls {

    private static HrmsEmployeeFeignService hrmsEmployeeFeignService = SpringContextWorkSheet.getBean(HrmsEmployeeFeignService.class);



    /**
     * 根据用户id填充用户姓名，用户所属部门id，姓名
     *
     * @param obj    需填充对象
     * @param colums 填充字段用户id字段名
     * @param ids    用户id
     *               colums长度需与ids数据长度需一致，两个数组内数据需一一对应（用户id字段名对应用户id）
     */
    public static Object fillNameById(Object obj, List<String> colums, List<String> ids) {
        PlatformResult<List<EmployeeResp>> employeeDetailByIds = hrmsEmployeeFeignService.getEmployeeDetailByIds(ids);
        if (employeeDetailByIds.isSuccess() && !CollectionUtils.isEmpty(employeeDetailByIds.getObject())) {
            Map<String, EmployeeResp> employeeRespMap = employeeDetailByIds.getObject()
                    .stream()
                    .collect(Collectors.toMap(EmployeeResp::getEmployeeId, employeeResp -> employeeResp));
            for (int i = 0; i < colums.size(); i++) {
                if (PropertyUtils.isReadable(obj, colums.get(i))) {
                    // 获取对象所有字段名
                    List<String> collect = Arrays.stream(obj.getClass().getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
                    EmployeeResp employeeResp = employeeRespMap.get(ids.get(i));
                    if (null == employeeResp) {
                        continue;
                    }
                    String idName = colums.get(i).substring(0, colums.get(i).length() - 2) + "Name";
                    String phone ="";
                    if(colums.get(i).contains("repair")){
                        phone= colums.get(i).substring(0,6) + "Phone";
                    }else{
                        phone = colums.get(i).substring(0, colums.get(i).length() - 2) + "Phone";
                    }
                    String deptId = colums.get(i).substring(0, colums.get(i).length() - 2) + "DeptId";
                    String deptName = colums.get(i).substring(0, colums.get(i).length() - 2) + "DeptName";
                    try {
                        // 赋值
                        if (collect.contains(colums.get(i))) {
                            PropertyUtils.setSimpleProperty(obj, colums.get(i), ids.get(i));
                        }
                        if (collect.contains(phone)) {
                            PropertyUtils.setSimpleProperty(obj, phone, employeeResp.getPhoneNumber());
                        }
                        if (collect.contains(idName)) {
                            PropertyUtils.setSimpleProperty(obj, idName, employeeResp.getEmployeeName());
                        }
                        if (collect.contains(deptId)) {
                            PropertyUtils.setSimpleProperty(obj, deptId, employeeResp.getOrgId());
                        }
                        if (collect.contains(deptName)) {
                            PropertyUtils.setSimpleProperty(obj, deptName, employeeResp.getOrgName());
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    } catch (NoSuchMethodException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            throw new BusinessException("未查询到用户信息");
        }
        return obj;
    }

    /**
     * 根据用户id获取个人信息
     *
     * @param userId 用户id
     * @return
     */
    public static EmployeeResp getUserNameById(String userId) {
        PlatformResult<EmployeeResp> employeeDetailById = hrmsEmployeeFeignService.findByEmployeeId(userId);
        if (employeeDetailById.isSuccess()) {
            return employeeDetailById.getObject();
        }
        throw new BusinessException("未查到人员信息");
    }

    /**
     * 根据用户工号获取个人信息
     *
     * @param userCode 用户工号
     * @return
     */
    public static EmployeeResp getUserByCode(String userCode) {
        PlatformResult<EmployeeResp> employeeDetailByCode = hrmsEmployeeFeignService.getEmployeeDetailByCode(userCode);
        if (employeeDetailByCode.isSuccess()) {
            return employeeDetailByCode.getObject();
        }
        return null;
    }

    /**
     * 根据手机号获取个人信息
     *
     * @param phone 手机号码
     * @return
     */
    public static EmployeeResp getUserNameByPhone(String phone) {
        try {
            log.info("------------------------------调用基础服务查询用户信息息开始");
            PlatformResult<EmployeeResp> employeeDetailById = hrmsEmployeeFeignService.findByEmployeePhoneNumber(phone.trim(),
                    UserLoginService.getTokenByUserCode(CommonlyConstants.Om.HardwareInfo.OM_TOKEN)
            );
            if (employeeDetailById.isSuccess()) {
                return employeeDetailById.getObject();
            } else {
                log.error("------------------------------调用基础服务查询用户信息息失败："+employeeDetailById.getMessage());
                return null;
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("------------------------------调用基础服务查询用户信息息失败："+e.getMessage());
        }
        return null;
    }

    /**
     * 根据人员id获取个人信息
     *
     * @param ids 人员id
     * @return
     */
    public static List<EmployeeResp> getUserListByIds(List<String> ids) {
        // 无token，模拟登录
        if(StringUtils.isEmpty(UserInfoHolder.getToken())){
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
        }
        PlatformResult<List<EmployeeResp>> employeeDetailByIds = hrmsEmployeeFeignService.getEmployeeDetailByIds(ids);
        if (employeeDetailByIds.isSuccess()) {
            return employeeDetailByIds.getObject();
        } else {
            return null;
        }
    }

    /**
     * SSO添加人员
     * @param wsExternalPersonnel
     * @return
     */
    public static String addEmployeeResp(WsExternalPersonnel wsExternalPersonnel) {
        // 初始化人员信息（目前缺填充角色、密码）
        HrmsEmployeeSaveReq employeeSaveReq = new HrmsEmployeeSaveReq();
        employeeSaveReq.setEmployeeId(IdUtils.getId());
        employeeSaveReq.setEmployeeNo(wsExternalPersonnel.getPhone());
        employeeSaveReq.setEmployeeName(wsExternalPersonnel.getUserName());
        employeeSaveReq.setEmpPassword(wsExternalPersonnel.getUserPassword());
        employeeSaveReq.setOrgId(wsExternalPersonnel.getDefaultOrgId());
        employeeSaveReq.setRoleCode(wsExternalPersonnel.getDefaultRoleCode());
       
        PlatformResult<EmployeeResp> employeeRespPlatformResult = hrmsEmployeeFeignService.addEmployee(employeeSaveReq, UserInfoHolder.getToken());
        if (!employeeRespPlatformResult.isSuccess()) {
            throw new BusinessException("OA系统保存人员失败");
        }
        return employeeSaveReq.getEmployeeId();
    }

    /**
     * SSO编辑人员
     * @param employeeSaveReq
     * @return
     */
    public static void updateEmployeeResp(HrmsEmployeeSaveReq employeeSaveReq) {
        PlatformResult platformResult = hrmsEmployeeFeignService.updateEmployee(employeeSaveReq);
        if (!platformResult.isSuccess()) {
            throw new BusinessException("OA系统保存人员失败");
        }
    }


    /**
     * 查询指定机构的所有员工
     * @param eqOrgId 机构id
     */
    public static List<EmployeeResp> getEmployeeRespAllListByEqOrgId(String eqOrgId) {
        // 无token，模拟登录
        if(StringUtils.isEmpty(UserInfoHolder.getToken())){
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
        }
        PageDataReq<EmployeeListReq> dataReq = new PageDataReq<>();
        dataReq.setPageNum(1);
        dataReq.setPageSize(9999);
        EmployeeListReq employeeReq = new EmployeeListReq();
        employeeReq.setEqOrgId(eqOrgId);
        dataReq.setData(employeeReq);
        
        DataSet<EmployeeResp> employeePageList = hrmsEmployeeFeignService.getEmployeePageList(dataReq);
        if (null != employeePageList.getRows()) {
            return employeePageList.getRows();
        }
        return null;
    }

}
