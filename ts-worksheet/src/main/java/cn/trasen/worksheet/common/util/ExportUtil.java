package cn.trasen.worksheet.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

public class ExportUtil {


    /**
     * @description:
     * @param: excel_name
     * @param: headList
     * @param: fieldList
     * @param: dataList
     * @param: response
     * @param: request
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/4 16:49
     */
    public static void createExcel(String excel_name, List<String> headList,
                                   List<String> fieldList, List<Map<String, Object>> dataList, HttpServletResponse response, HttpServletRequest request)
            throws Exception {
        createExcel(excel_name, excel_name, headList,
                fieldList, dataList, response, request);
    }

    /**
     * @param excel_name 生成的Excel文件路径+名称
     * @param headList   Excel文件Head标题集合
     * @param dataList   Excel文件数据内容部分
     * @throws Exception
     */
    public static void createExcel(String excel_name, String fileName, List<String> headList,
                                   List<String> fieldList, List<Map<String, Object>> dataList, HttpServletResponse response, HttpServletRequest request)
            throws Exception {
        // 创建新的Excel 工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = null;
        if (StringUtils.isBlank(excel_name) == false) {
            sheet = workbook.createSheet(excel_name);
        } else {
            sheet = workbook.createSheet();

        }
        // 冻结第一行
        sheet.createFreezePane(0, 1, 0, 1);
        // 冻结第二行
        sheet.createFreezePane(0, 2, 0, 2);

        //创建第一行样式
        XSSFCellStyle style0 = workbook.createCellStyle();// 生成一个样式
        XSSFFont font0 = workbook.createFont();    //生产一个字体
        font0.setColor(HSSFColor.BLACK.index);    //字体颜色
        font0.setFontHeightInPoints((short) 20);    //字体大小
        font0.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);    //字体变粗
        font0.setFontName("黑体");
        style0.setAlignment(XSSFCellStyle.ALIGN_CENTER);//水平居中
        style0.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);//垂直居中
        style0.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        style0.setFont(font0);

        if (StringUtils.isBlank(excel_name) == false) {
            //创建第一行
            XSSFRow row0 = sheet.createRow((short) 0);
            XSSFCell cell0 = row0.createCell((short) 0);
            cell0.setCellValue(excel_name);
            cell0.setCellStyle(style0);
            Integer endC = headList.size() - 1;
            hbCell(sheet, workbook, 0, 0, 0, endC);//合并主标题
        }
        // 生成一个样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillForegroundColor(HSSFColor.WHITE.index);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);//水平居中
        style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);//垂直居中
        // 生成一个字体
        XSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.BLACK.index);
        font.setFontHeightInPoints((short) 12);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        font.setFontName("黑体");
        // 把字体应用到当前的样式
        style.setFont(font);

        // 生成并设置另一个样式
        XSSFCellStyle style2 = workbook.createCellStyle();
        style2.setFillForegroundColor(HSSFColor.WHITE.index);
        style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 生成另一个字体
        XSSFFont font2 = workbook.createFont();
        font2.setColor(HSSFColor.BLACK.index);
        font2.setFontHeightInPoints((short) 11);
        font2.setFontName("黑体");
        // 把字体应用到当前的样式
        style2.setFont(font2);
        // ===============================================================

        XSSFRow row = null;
        if (StringUtils.isBlank(excel_name) == false) {
            row = sheet.createRow((short) 1);
        } else {
            row = sheet.createRow((short) 0);
        }
        row.setHeightInPoints(18);
        //HSSFRow row = sheet.createRow((short)1);
        for (int i = 0; i < headList.size(); i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            cell.setCellStyle(style);
            cell.setCellValue(headList.get(i));
        }
        // ===============================================================

        for (int n = 0; n < dataList.size(); n++) {
            // 在索引0的位置创建行（最顶端的行）
            XSSFRow row_value = sheet.createRow(n + 2);
            // 设置行高
            row_value.setHeightInPoints(18);
            Map<String, Object> dataMap = dataList.get(n);
            // ===============================================================
            for (int i = 0; i < fieldList.size(); i++) {

                // 在索引0的位置创建单元格（左上端）
                XSSFCell cell = row_value.createCell(i);
                // 定义单元格为字符串类型
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                //设置单元格的样式
                cell.setCellStyle(style2);
                // 在单元格中输入一些内容
                cell.setCellValue(objToString(dataMap.get(fieldList.get(i))));
            }
            // ===============================================================
        }

        // 内容自适应列宽
        setCellSizeColumn(sheet, headList.size());
        setRowSizeColumn(sheet);

        Date time = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format = new SimpleDateFormat("yyyyMMddHHmmss");
        String excelFileName = format.format(time);
        excelFileName = fileName + excelFileName;

        excelFileName = URLEncoder.encode(excelFileName, "UTF-8");
        excelFileName = URLDecoder.decode(excelFileName, "ISO8859_1");
        response.setHeader("Content-disposition", "attachment;filename=" + excelFileName + ".xls");

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.close();
    }


    /**
     * 内容自适应列宽
     *
     * @param sheet
     * @param columnLength 列数
     */
    private static void setCellSizeColumn(XSSFSheet sheet, int columnLength) {
        for (int columnNum = 0; columnNum <= columnLength; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 255;
            for (int rowNum = 0; rowNum < sheet.getPhysicalNumberOfRows(); rowNum++) {
                XSSFRow currentRow; // 当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    XSSFCell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == HSSFCell.CELL_TYPE_STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        // 单元格内容为时间数据，特殊处理增加列宽
                        if(DateUtils.isDate(currentCell.getStringCellValue())){
                            length += 2;
                        }
                        if (length > 100) {
                            XSSFCellStyle cellStyle = currentCell.getCellStyle();
                            cellStyle.setWrapText(true);
                            currentCell.setCellStyle(cellStyle);
                        }
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            columnWidth *= 255;
            sheet.setColumnWidth(columnNum, columnWidth > 25500 ? 25500 : columnWidth);
        }
    }


    /**
     * 内容自适应行高
     *
     * @param sheet
     */
    private static void setRowSizeColumn(XSSFSheet sheet) {
        for (int rowNum = 2; rowNum < sheet.getPhysicalNumberOfRows(); rowNum++) {
            XSSFRow currentRow; // 当前行未被使用过
            if (sheet.getRow(rowNum) == null) {
                currentRow = sheet.createRow(rowNum);
            } else {
                currentRow = sheet.getRow(rowNum);
            }
            calcAndSetRowHeigt(currentRow);
        }
    }


    /**
     * 根据行内容重新计算行高
     */
    public static void calcAndSetRowHeigt(XSSFRow sourceRow) {
        for (int cellIndex = sourceRow.getFirstCellNum(); cellIndex <= sourceRow.getPhysicalNumberOfCells(); cellIndex++) {
            //行高
            double maxHeight = sourceRow.getHeight();
            XSSFCell sourceCell = sourceRow.getCell(cellIndex);
            //单元格的内容
            String cellContent = getCellContentAsString(sourceCell);
            if (null == cellContent || "".equals(cellContent)) {
                continue;
            }
            //单元格的宽高及单元格信息
            Map<String, Object> cellInfoMap = getCellInfo(sourceCell);
            Integer cellWidth = (Integer) cellInfoMap.get("width");
            Integer cellHeight = (Integer) cellInfoMap.get("height");
            if (cellHeight > maxHeight) {
                maxHeight = cellHeight;
            }
            System.out.println("单元格的宽度 : " + cellWidth + "    单元格的高度 : " + maxHeight + ",    单元格的内容 : " + cellContent);
            XSSFCellStyle cellStyle = sourceCell.getCellStyle();
            XSSFFont font = cellStyle.getFont();
            //字体的高度
            short fontHeight = font.getFontHeight();
            //cell内容字符串总宽度
            double cellContentWidth = cellContent.getBytes().length * 2 * 256;
            //字符串需要的行数 不做四舍五入之类的操作
            double stringNeedsRows = (double) cellContentWidth / cellWidth;
            //小于一行补足一行
            if (stringNeedsRows < 1.0) {
                stringNeedsRows = 1.0;
            }
            //需要的高度 (Math.floor(stringNeedsRows) - 1) * 40
            // 为两行之间空白高度
            double stringNeedsHeight = (double) fontHeight * stringNeedsRows;
            // 需要重设行高
            if (stringNeedsHeight > maxHeight) {
                maxHeight = stringNeedsHeight;
                //超过原行高三倍 则为5倍 实际应用中可做参数配置	    		i
                if (maxHeight / cellHeight > 5) {
                    maxHeight = 5 * cellHeight;
                }
                //最后取天花板防止高度不够
                maxHeight = Math.ceil(maxHeight);
                //重新设置行高 同时处理多行合并单元格的情况
                Boolean isPartOfRowsRegion = (Boolean) cellInfoMap.get("isPartOfRowsRegion");
                if (isPartOfRowsRegion) {
                    Integer firstRow = (Integer) cellInfoMap.get("firstRow");
                    Integer lastRow = (Integer) cellInfoMap.get("lastRow");
                    //平均每行需要增加的行高
                    double addHeight = (maxHeight - cellHeight) / (lastRow - firstRow + 1);
                    for (int i = firstRow; i <= lastRow; i++) {
                        double rowsRegionHeight = sourceRow.getSheet().getRow(i).getHeight() + addHeight;
                        sourceRow.getSheet().getRow(i).setHeight((short) rowsRegionHeight);
                    }
                } else {
                    sourceRow.setHeight((short) maxHeight);
                }
            }
            System.out.println("字体高度 : " + fontHeight + ",    字符串宽度 : " + cellContentWidth + ",    字符串需要的行数 : " + stringNeedsRows + ",   需要的高度 : " + stringNeedsHeight + ",   现在的行高 : " + maxHeight);
        }
    }

    /**
     * 解析一个单元格得到数据
     *
     * @param cell
     * @return
     */
    private static String getCellContentAsString(XSSFCell cell) {
        if (null == cell) {
            return "";
        }
        String result = "";
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                String s = String.valueOf(cell.getNumericCellValue());
                if (s != null) {
                    if (s.endsWith(".0")) {
                        s = s.substring(0, s.length() - 2);
                    }
                }
                result = s;
                break;
            case Cell.CELL_TYPE_STRING:
                result = (cell.getStringCellValue() + "").trim();
                break;
            case Cell.CELL_TYPE_BLANK:
                break;
            case Cell.CELL_TYPE_BOOLEAN:
                result = String.valueOf(cell.getBooleanCellValue());
                break;
            case Cell.CELL_TYPE_ERROR:
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 获取单元格及合并单元格的宽度     * @param cell     * @return
     */
    private static Map<String, Object> getCellInfo(XSSFCell cell) {
        XSSFSheet sheet = cell.getSheet();
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();
        boolean isPartOfRegion = false;
        int firstColumn = 0;
        int lastColumn = 0;
        int firstRow = 0;
        int lastRow = 0;
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {

            CellRangeAddress ca = sheet.getMergedRegion(i);
            firstColumn = ca.getFirstColumn();
            lastColumn = ca.getLastColumn();
            firstRow = ca.getFirstRow();
            lastRow = ca.getLastRow();
            if (rowIndex >= firstRow && rowIndex <= lastRow) {
                if (columnIndex >= firstColumn && columnIndex <= lastColumn) {
                    isPartOfRegion = true;
                    break;
                }
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        Integer width = 0;
        Integer height = 0;
        boolean isPartOfRowsRegion = false;
        if (isPartOfRegion) {
            for (int i = firstColumn; i <= lastColumn; i++) {
                width += sheet.getColumnWidth(i);
            }
            for (int i = firstRow; i <= lastRow; i++) {
                height += sheet.getRow(i).getHeight();
            }
            if (lastRow > firstRow) {
                isPartOfRowsRegion = true;
            }
        } else {
            width = sheet.getColumnWidth(columnIndex);
            height += cell.getRow().getHeight();
        }
        map.put("isPartOfRowsRegion", isPartOfRowsRegion);
        map.put("firstRow", firstRow);
        map.put("lastRow", lastRow);
        map.put("width", width);
        map.put("height", height);
        return map;
    }


    /**
     * 导出考勤模板数据
     *
     * @param excel_name
     * @param headList
     * @param fieldList
     * @param dataList
     * @param textlist
     * @param response
     * @param request
     * @throws Exception
     */
    public static void createCheckattendanceExcel(String excel_name, List<String> headList,
                                                  List<String> fieldList, List<Map<String, Object>> dataList, String[] textlist,
                                                  HttpServletResponse response, HttpServletRequest request)
            throws Exception {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        HSSFSheet sheet = workbook.createSheet(excel_name);

        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 25);

        //创建第一行样式
        HSSFCellStyle style0 = workbook.createCellStyle();// 生成一个样式
        HSSFFont font0 = workbook.createFont();    //生产一个字体
        font0.setColor(HSSFColor.BLACK.index);    //字体颜色
        font0.setFontHeightInPoints((short) 20);    //字体大小
        font0.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);    //字体变粗
        style0.setAlignment(HSSFCellStyle.ALIGN_CENTER);    //居中
        style0.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        style0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        style0.setFont(font0);

        //创建第一行
        HSSFRow row0 = sheet.createRow((short) 0);
        HSSFCell cell0 = row0.createCell((short) 0);
        cell0.setCellValue(excel_name);
        cell0.setCellStyle(style0);
        Integer endC = headList.size() - 1;
        hbCell(sheet, workbook, 0, 0, 0, endC);//合并主标题

        for (int i = 0; i < headList.size(); i++) {
            sheet.autoSizeColumn(i);
            //sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 35 / 10);
            if (i == 0) {
                sheet.setColumnWidth(i, 2500);
            }
            if (i == 1) {
                sheet.setColumnWidth(i, 3000);
            }
            if (i == 2) {
                sheet.setColumnWidth(i, 5000);
            }
            if (i == 3) {
                sheet.setColumnWidth(i, 5000);
            }
            if (i >= 4) {
                sheet.setColumnWidth(i, 1500);
            }

        }
        sheet.createFreezePane(1, 2, 1, 2);

        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillForegroundColor(HSSFColor.SKY_BLUE.index);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        HSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.VIOLET.index);
        font.setFontHeightInPoints((short) 12);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 把字体应用到当前的样式
        style.setFont(font);

        // 生成并设置另一个样式
        HSSFCellStyle style2 = workbook.createCellStyle();
        style2.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);
        style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 生成另一个字体
        HSSFFont font2 = workbook.createFont();
        font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        // 把字体应用到当前的样式
        style2.setFont(font2);
        // ===============================================================


        HSSFRow row = sheet.createRow((short) 1);
        for (int i = 0; i < headList.size(); i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            cell.setCellStyle(style);
            cell.setCellValue(headList.get(i));
        }
        // ===============================================================

        for (int n = 0; n < dataList.size(); n++) {
            // 在索引0的位置创建行（最顶端的行）
            HSSFRow row_value = sheet.createRow(n + 2);
            Map<String, Object> dataMap = dataList.get(n);
            // ===============================================================
            for (int i = 0; i < fieldList.size(); i++) {

                ////模板中员工类型下拉选择
                //if(i == 2){
                //	//  起始行、终止行、起始列、终止列
                //	setHSSFValidation(sheet,typelist,n+2,n+2,i,i);
                //}

                if (i >= 4) {
                    //起始行、终止行、起始列、终止列
                    setHSSFValidation(sheet, textlist, n + 2, n + 2, i, i);
                }
                // 在索引0的位置创建单元格（左上端）
                HSSFCell cell = row_value.createCell(i);
                // 定义单元格为字符串类型
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                //设置单元格的样式
                cell.setCellStyle(style2);
                // 在单元格中输入一些内容
                cell.setCellValue(objToString(dataMap.get(fieldList.get(i))));
            }
            // ===============================================================
        }

        Date time = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = format.format(time);

        fileName = excel_name + fileName;
        fileName = URLEncoder.encode(fileName, "UTF-8");
        fileName = URLDecoder.decode(fileName, "ISO8859_1");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.close();
    }

    /**
     * 导出审计合同未归档模板数据
     *
     * @param excel_name
     * @param headList
     * @param fieldList
     * @param dataList
     * @param response
     * @param request
     * @throws Exception
     */
    public static void createAuditDossierExcel(String excel_name, List<String> headList,
                                               List<String> fieldList, List<Map<String, Object>> dataList, HttpServletResponse
                                                       response, HttpServletRequest request)
            throws Exception {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        HSSFSheet sheet = workbook.createSheet(excel_name);

        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 25);

        //创建第一行样式
        HSSFCellStyle style0 = workbook.createCellStyle();// 生成一个样式
        HSSFFont font0 = workbook.createFont();    //生产一个字体
        font0.setColor(HSSFColor.BLACK.index);    //字体颜色
        font0.setFontHeightInPoints((short) 20);    //字体大小
        font0.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);    //字体变粗
        style0.setAlignment(HSSFCellStyle.ALIGN_CENTER);    //居中
        style0.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        style0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        style0.setFont(font0);

        //创建第一行
        HSSFRow row0 = sheet.createRow((short) 0);
        HSSFCell cell0 = row0.createCell((short) 0);
        cell0.setCellValue(excel_name);
        cell0.setCellStyle(style0);
        Integer endC = headList.size() - 1;
        hbCell(sheet, workbook, 0, 0, 0, endC);//合并主标题

        for (int i = 0; i < headList.size(); i++) {
            sheet.autoSizeColumn(i);
            if (i == 0) {
                sheet.setColumnWidth(i, 6000);
            }
            if (i == 1) {
                sheet.setColumnWidth(i, 5000);
            }
            if (i == 2) {
                sheet.setColumnWidth(i, 6000);
            }
            if (i == 3) {
                sheet.setColumnWidth(i, 3000);
            }
            if (i == 4) {
                sheet.setColumnWidth(i, 12000);
            }
            if (i == 5) {
                sheet.setColumnWidth(i, 12000);
            }
            if (i == 6) {
                sheet.setColumnWidth(i, 3000);
            }
        }
        sheet.createFreezePane(1, 2, 1, 2);

        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillForegroundColor(HSSFColor.SKY_BLUE.index);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        HSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.VIOLET.index);
        font.setFontHeightInPoints((short) 12);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 把字体应用到当前的样式
        style.setFont(font);

        // 生成并设置另一个样式
        HSSFCellStyle style2 = workbook.createCellStyle();
        style2.setFillForegroundColor(HSSFColor.LIGHT_YELLOW.index);
        style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 生成另一个字体
        HSSFFont font2 = workbook.createFont();
        font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        // 把字体应用到当前的样式
        style2.setFont(font2);
        // ===============================================================


        HSSFRow row = sheet.createRow((short) 1);
        for (int i = 0; i < headList.size(); i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            cell.setCellStyle(style);
            cell.setCellValue(headList.get(i));
        }
        // ===============================================================

        for (int n = 0; n < dataList.size(); n++) {
            // 在索引0的位置创建行（最顶端的行）
            HSSFRow row_value = sheet.createRow(n + 2);
            Map<String, Object> dataMap = dataList.get(n);
            // ===============================================================
            for (int i = 0; i < fieldList.size(); i++) {
                // 在索引0的位置创建单元格（左上端）
                HSSFCell cell = row_value.createCell(i);
                // 定义单元格为字符串类型
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                //设置单元格的样式
                cell.setCellStyle(style2);
                // 在单元格中输入一些内容
                cell.setCellValue(objToString(dataMap.get(fieldList.get(i))));
            }
            // ===============================================================
        }

        Date time = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = format.format(time);

        fileName = excel_name + fileName;
        fileName = URLEncoder.encode(fileName, "UTF-8");
        fileName = URLDecoder.decode(fileName, "ISO8859_1");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.close();
    }

    /**
     * @param headList Excel文件Head标题集合
     * @param dataList Excel文件数据内容部分
     * @throws Exception
     */
    public static void createMonthCheckattendanceExcel(String corpname, String title, String deptname,
                                                       List<String> headList, List<LinkedHashMap<String, Object>> dataList, List<String> fieldList, HttpServletResponse
                                                               response, HttpServletRequest request)
            throws Exception {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        HSSFSheet sheet = workbook.createSheet(title);

        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 25);

        //创建第一行样式
        HSSFCellStyle style0 = workbook.createCellStyle();// 生成一个样式
        HSSFFont font0 = workbook.createFont();    //生产一个字体
        font0.setColor(HSSFColor.BLACK.index);    //字体颜色
        font0.setFontHeightInPoints((short) 20);    //字体大小
        font0.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);    //字体变粗
        style0.setAlignment(HSSFCellStyle.ALIGN_CENTER);    //居中
        // style0.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        style0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        style0.setFont(font0);

        Integer endC = headList.size() - 1;

        //创建第一行
        HSSFRow row0 = sheet.createRow((short) 0);
        HSSFCell cell0 = row0.createCell((short) 0);
        cell0.setCellValue(corpname);
        cell0.setCellStyle(style0);
        hbCell(sheet, workbook, 0, 0, 0, endC);//合并主标题

        //创建第二行
        HSSFRow row1 = sheet.createRow((short) 1);
        HSSFCell cell1 = row1.createCell((short) 0);
        cell1.setCellValue(title);
        cell1.setCellStyle(style0);
        hbCell(sheet, workbook, 1, 1, 0, endC);//合并主标题  起始行号，终止行号， 起始列号，终止列号

        //创建第三行
        HSSFRow row2 = sheet.createRow((short) 2);
        HSSFCell cell2 = row2.createCell((short) 0);
        cell2.setCellValue(deptname);
        HSSFCellStyle style2 = workbook.createCellStyle();// 生成一个样式
        HSSFFont font2 = workbook.createFont();    //生产一个字体
        font2.setColor(HSSFColor.BLACK.index);    //字体颜色
        font2.setFontHeightInPoints((short) 16);    //字体大小
        font2.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);    //字体变粗
        style2.setAlignment(HSSFCellStyle.ALIGN_RIGHT);    //居中
        style2.setFillPattern(CellStyle.SOLID_FOREGROUND);
        style2.setFont(font2);
        cell2.setCellStyle(style2);
        hbCell(sheet, workbook, 2, 2, 0, endC);//合并主标题  起始行号，终止行号， 起始列号，终止列号


        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        HSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.BLACK.index);
        font.setFontHeightInPoints((short) 14);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 把字体应用到当前的样式
        style.setFont(font);
        HSSFRow row = sheet.createRow((short) 3);
        for (int i = 0; i < headList.size(); i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            cell.setCellStyle(style);
            cell.setCellValue(headList.get(i));
            if (i == 1 || i == 2) {
                sheet.setColumnWidth(i, 5000);
            } else if (i == endC) {
                sheet.setColumnWidth(i, 8000);
            } else {
                sheet.autoSizeColumn(i);
                sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 16 / 10);
            }
        }

        //第一个参数表示要冻结的列数；
        //第二个参数表示要冻结的行数，
        //第三个参数表示右边区域可见的首列序号，从1开始计算；
        //第四个参数表示下边区域可见的首行序号，也是从1开始计算，这里是冻结列，所以为0；
        sheet.createFreezePane(0, 4, 0, 4);

        // 生成一个字体
        // 生成一个样式
        HSSFCellStyle style3 = workbook.createCellStyle();
        // 设置这些样式
        style3.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style3.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style3.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style3.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style3.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style3.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        HSSFFont font3 = workbook.createFont();
        font3.setColor(HSSFColor.BLACK.index);
        font3.setFontHeightInPoints((short) 12);
        font3.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 把字体应用到当前的样式
        style3.setFont(font3);

        for (int n = 0; n < dataList.size(); n++) {
            // 在索引0的位置创建行（最顶端的行）
            HSSFRow row_value = sheet.createRow(n + 4);
            Map<String, Object> dataMap = dataList.get(n);
            // ===============================================================
            for (int i = 0; i < fieldList.size(); i++) {

                // 在索引0的位置创建单元格（左上端）
                HSSFCell cell = row_value.createCell(i);
                // 定义单元格为字符串类型
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                //设置单元格的样式
                cell.setCellStyle(style3);
                // 在单元格中输入一些内容
                cell.setCellValue(objToString(dataMap.get(fieldList.get(i))));
            }
            // ===============================================================
        }

        Date time = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = format.format(time);

        fileName = title + fileName;
        fileName = URLEncoder.encode(fileName, "UTF-8");
        fileName = URLDecoder.decode(fileName, "ISO8859_1");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.close();
    }

    public static HSSFSheet setHSSFValidation(HSSFSheet sheet,
                                              String[] textlist, int firstRow, int endRow, int firstCol,
                                              int endCol) {
        // 加载下拉列表内容
        DVConstraint constraint = DVConstraint.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow,
                endRow, firstCol, endCol);
        // 数据有效性对象
        HSSFDataValidation data_validation_list = new HSSFDataValidation(
                regions, constraint);
        sheet.addValidationData(data_validation_list);
        return sheet;
    }


    private static String objToString(Object obj) {
        if (obj == null) {
            return "";
        } else {
            if (obj instanceof String) {
                return (String) obj;
            } else {
                return obj.toString();
            }
        }
    }

    public static void hbCell(XSSFSheet sheet, XSSFWorkbook wb, Integer one, Integer two, Integer
            three, Integer four) {
        int border = 1;
        CellRangeAddress region = new CellRangeAddress(one, two, three, four);  //合并单元格
        sheet.addMergedRegion(region);
        RegionUtil.setBorderBottom(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderLeft(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderRight(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderTop(border, region, sheet, wb);  //合并单元格添加边框
    }

    public static void hbCell(HSSFSheet sheet, HSSFWorkbook wb, Integer one, Integer two, Integer
            three, Integer four) {
        int border = 1;
        CellRangeAddress region = new CellRangeAddress(one, two, three, four);  //合并单元格
        sheet.addMergedRegion(region);
        RegionUtil.setBorderBottom(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderLeft(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderRight(border, region, sheet, wb);   //合并单元格添加边框
        RegionUtil.setBorderTop(border, region, sheet, wb);  //合并单元格添加边框
    }


}
