package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Data
public class WsSysConfigSaveInputVo {

    @ApiModelProperty(value = "系统配置id")
    private String pkSysConfigId;

    @ApiModelProperty(value = "是否自动验收（0否1是）")
    private String automatedAcceptance;

    @ApiModelProperty(value = "验收天数")
    private String acceptanceDays;

    @ApiModelProperty(value = "验收默认分数")
    private String acceptanceDefaultScore;

    @ApiModelProperty(value = "是否自动评价（0否1是）")
    private String automatedEvaluate;

    @ApiModelProperty(value = "评价天数")
    private String evaluateDays;

    @ApiModelProperty(value = "评价默认分数")
    private String evaluateDefaultScore;

    @ApiModelProperty(value = "是否开启电话分机（0否1是）")
    private String telExt;

    @ApiModelProperty(value = "是否开启多院区（0否1是）")
    private String multiHospitalDistrict;

    @ApiModelProperty(value = "报修地址是否必填（0否1是）")
    private Integer repairAddressRequired;

    @ApiModelProperty(value = "要求接单时间")
    private Float requestReceivingTime;


}