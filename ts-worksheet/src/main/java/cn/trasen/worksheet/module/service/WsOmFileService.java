package cn.trasen.worksheet.module.service;

import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.entity.WsOmFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/21 18:09
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsOmFileService {

    int insertOmFile(WsOmFile wsOmFile);

    WsOmFile selectByUrl(String url);

    List<WsOmFile> selectListOmFileByTime(Date date);

    int updateBatchWhetherToUpload(List<WsOmFile> wsOmFile);

    int updateWhetherToUpload(WsOmFile wsOmFile);

    /**
     * 拉取OM服务器录音文件
     * @param workNumber 工单编号
     * @return
     */
    WsFileOutVo pullTheFile(String workNumber);
}
