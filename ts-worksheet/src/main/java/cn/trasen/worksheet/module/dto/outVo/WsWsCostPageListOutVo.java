package cn.trasen.worksheet.module.dto.outVo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WsWsCostPageListOutVo {

    @ApiModelProperty(value = "费用id")
    private String pkWsCostId;

    @ApiModelProperty(value = "处理科室id")
    private String businessDeptId;

    @ApiModelProperty(value = "处理科室")
    private String businessDeptName;

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "费用")
    private Float money;

    @ApiModelProperty(value = "费用描述")
    private String costDeion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "费用发生时间(yyyy-mm-dd HH:MM)")
    private Date costTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "费用填报时间(yyyy-mm-dd HH:MM)")
    private Date createTime;

    @ApiModelProperty(value = "填报科室id")
    private String fillDeptId;

    @ApiModelProperty(value = "填报科室")
    private String fillDeptName;

    @ApiModelProperty(value = "填报人")
    private String fillUser;

    @ApiModelProperty(value = "填报人id")
    private String fillUserId;

    @ApiModelProperty(value = "费用状态（0未报销，1审核中、已报销2）见字典，编码为COST_STATUS")
    private Integer costStatus;

    @ApiModelProperty(value = "费用状态（0未报销，1审核中、已报销2）")
    private String costStatusName;

    @ApiModelProperty(value = "附件数量")
    private String fileCount;

    @ApiModelProperty(value = "附件业务id")
    private String files;

}