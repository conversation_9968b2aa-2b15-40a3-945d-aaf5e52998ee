package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Table(name = "ws_external_personnel")
@Setter
@Getter
public class WsExternalPersonnelStatusInputVo {

    @ApiModelProperty(value = "外部人员id")
    private String pkExternalPersonnelId;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "状态（0停用1启用）")
    private int status;


}