package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_hospital_district")
@Setter
@Getter
public class WsHospitalDistrict implements Supplier {
    @Column(name = "pk_hospital_district_id")
    private String pkHospitalDistrictId;

    /**
     * 院区名称
     */
    @Column(name = "hospital_district_name")
    @ApiModelProperty(value = "院区名称")
    private String hospitalDistrictName;

    /**
     * 院区状态（0停用1启用）
     */
    @Column(name = "hospital_district_status")
    @ApiModelProperty(value = "院区状态（0停用1启用）")
    private String hospitalDistrictStatus;

    /**
     * 创建人ID
     */
    @Column(name = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @Column(name = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除标记（0未删除，1已删除）
     */
    @Column(name = "delete_status")
    @ApiModelProperty(value = "逻辑删除标记（0未删除，1已删除）")
    private Integer deleteStatus;

    public WsHospitalDistrict(String pkHospitalDistrictId, String hospitalDistrictStatus) {
        this.pkHospitalDistrictId = pkHospitalDistrictId;
        this.hospitalDistrictStatus = hospitalDistrictStatus;
    }
    public WsHospitalDistrict() {
    }

    @Override
    public WsHospitalDistrict get() {
        return this;
    }
}