package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Setter
@Getter
public class ExternalPersonnelPageListOutVo {

    @ApiModelProperty(value = "外部人员id")
    private String pkExternalPersonnelId;

    @ApiModelProperty(value = "所属机构")
    private String institutionalAffiliations;

    @ApiModelProperty(value = "人员姓名")
    private String userName;

    @ApiModelProperty(value = "联系方式")
    private String phone;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "职责说明")
    private String jobDeion;

    @ApiModelProperty(value = "状态（0停用1启用）")
    private int status;

    @ApiModelProperty(value = "所属管辖科室id")
    private String belongsDeptId;

    @ApiModelProperty(value = "所属管辖科室名称")
    private String belongsDeptName;
}