package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsFaultType;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WsFaultTypeMapper extends Mapper<WsFaultType> {

    int insertFaultType(WsFaultType faultType);

    int updateFaultType(WsFaultType faultType);

    WsFaultType selectOneById(String pkFaultTypeId);

    WsFaultType selectOneByCategoryNameAndParentId(@Param("categoryName") String categoryName,
                                                   @Param("parentId") String parentId,
                                                   @Param("fkDeptId") String fkDeptId);


    int deleteFaultType(String pkFaultTypeId);

    /**
     * 查询未设置故障类型处理人的故障类型数量
     * @return
     */
    int faultTypePeopleIsNullCounts(@Param("fkDeptId") String fkDeptId);

    /**
     * 分页列表
     *
     * @param page
     * @param fkDeptId       科室id
     * @param categoryName   分类名称
     * @param list 故障类型id
     * @return
     */
    List<FaultTypeListOutVo> selectFaultTypePageList(Page page,
                                                     @Param("fkDeptId") String fkDeptId,
                                                     @Param("categoryName") String categoryName,

                                                     @Param("list") List<String> list);
    /**
     * 故障类型
     * @param status 0包含停用数据 1仅启用数据
     * @param fkDeptId  科室id
     * @param categoryName  故障类型名称
     * @return
     */
    List<FaultTypeTreeOutVo> selectFaultTypeAllList(@Param("status")String status,
                                                    @Param("fkDeptId")String fkDeptId,
                                                    @Param("categoryName")String categoryName);

    /**
     * 查询所有故障类型分类
     * @return
     */
    List<WsFaultType> getFaultTypeAllList();

    /**
     * 查询所有故障类型，包含停用不包含逻辑删除
     * @return
     */
    List<FaultTypeTreeOutVo> selectFaultTypeAllListContainsDisable();

    int updateBatchStatus(@Param("list") List<String> list,@Param("faultStatus") String faultStatus);

    int deleteBatch(@Param("list") List<String> list);
}