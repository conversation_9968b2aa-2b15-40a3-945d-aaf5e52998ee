package cn.trasen.worksheet.module.service.impl;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.util.FileUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsOmFile;
import cn.trasen.worksheet.module.mapper.WsOmFileMapper;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import cn.trasen.worksheet.module.service.WsFileService;
import cn.trasen.worksheet.module.service.WsOmFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date: 2021/7/21 18:10
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsOmFileServiceImpl implements WsOmFileService {

    @Autowired
    private WsOmFileMapper wsOmFileMapper;
    @Autowired
    private WsFileService wsFileService;
    @Autowired
    private WsCustometLogService wsCustometLogService;
    @Value("${om.filePassWord}")
    private String filePassWord;

    @Override
    public int insertOmFile(WsOmFile wsOmFile) {
        return wsOmFileMapper.insertOmFile(wsOmFile);
    }

    @Override
    public WsOmFile selectByUrl(String url) {
        return wsOmFileMapper.selectByUrl(url);
    }

    @Override
    public List<WsOmFile> selectListOmFileByTime(Date date) {
        return wsOmFileMapper.selectListOmFileByTime(date);
    }

    @Override
    public int updateBatchWhetherToUpload(List<WsOmFile> wsOmFile) {
        return wsOmFileMapper.updateBatchWhetherToUpload(wsOmFile);
    }

    @Override
    public int updateWhetherToUpload(WsOmFile wsOmFile) {
        return wsOmFileMapper.updateWhetherToUpload(wsOmFile);
    }

    /**
     * 拉取OM服务器录音文件
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public WsFileOutVo pullTheFile(String workNumber) {
        return Optional.ofNullable(wsOmFileMapper.selectOneByfkCustometLogId(wsCustometLogService.selectOneByWorkNumber(workNumber).getPkCustometLogId()))
                .map(temp -> {
                    WsFileOutVo wsFileOutVo = FileUtils.fileUploadByUrl(temp.getUrl(),filePassWord);
                    WsFileFile wsFileFile = new WsFileFile();
                    MyBeanUtils.copyBeanNotNull2Bean(wsFileOutVo, wsFileFile);
                    wsFileFile.setPkWsFileId(IdUtils.getId());
                    wsFileFile.setWorkNumber(workNumber);
                    wsFileFile.setFkCustometLogId(temp.getFkCustometLogId());
                    temp.setWhetherToUpload(CommonlyConstants.YesOrNo.YES);
                    updateWhetherToUpload(temp);
                    wsFileService.insertFile(wsFileFile);
                    return wsFileOutVo;
                })
                .orElseThrow(() -> new BusinessException("未查询到未拉取的录音文件"));
    }
}
