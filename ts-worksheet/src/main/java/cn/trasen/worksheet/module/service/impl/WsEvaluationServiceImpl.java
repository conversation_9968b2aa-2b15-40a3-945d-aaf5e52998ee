package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;

import cn.hutool.core.date.DateTime;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsEvaluateInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsEvaluationTopOutVo;
import cn.trasen.worksheet.module.entity.WsWsEvaluation;
import cn.trasen.worksheet.module.entity.WsWsTask;
import cn.trasen.worksheet.module.mapper.WsWsEvaluationMapper;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;

/**
 * <AUTHOR>
 * @date: 2021/6/21 16:18
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsEvaluationServiceImpl implements WsEvaluationService {

    @Autowired
    private WsWsEvaluationMapper wsEvaluationMapper;
    @Autowired
    private WsSheetTaskService wsSheetTaskService;
    @Autowired
    private WsSheetService wsSheetService;


    @Override
    public void insert(WsWsEvaluation wsEvaluation) {
        wsEvaluationMapper.insert(wsEvaluation);
    }

    @Override
    public void insertBatch(List<WsWsEvaluation> wsEvaluationList) {
        wsEvaluationMapper.insertBatch(wsEvaluationList);
    }

    /**
     * 工单综合评分
     *
     * @param fkUserId 用户id（不传为查询所有工单）
     * @return
     */
    @Override
    public Map<String, Object> getComprehensiveScoreOfWorkOrder(String fkDeptId,String fkUserId, String beginTime, String endTime) {
        return wsEvaluationMapper.getComprehensiveScoreOfWorkOrder(fkDeptId,fkUserId, beginTime, endTime);
    }

    /**
     * 完成质量分析-总评分、处理速度、服务态度、技术水平
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public Map<String, Object> geTScoreAnalysis(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        // 工单总评分
        return getComprehensiveScoreOfWorkOrder(
                wsWorkSheetStatisticalInputVo.getFkUserDeptId(),
                null,
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime()
        );

    }
    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级
     * @return
     */
    @Override
    public Map<String, Object> getEvaluationLevel(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        return wsEvaluationMapper.getEvaluationLevel(wsWorkSheetStatisticalInputVo);
    }

    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分
     * @return
     */
    @Override
    public Map<String, Object> getEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        Map<String, Object> resultMap = Maps.newHashMap();
        // 初始化时间
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        List<Map<String, Object>> evaluationAverageScore = wsEvaluationMapper.getEvaluationAverageScore(wsWorkSheetStatisticalInputVo);
        // 无数据补零
        DateUtils.timeIntervalZeroize(
                wsWorkSheetStatisticalInputVo.getDayOrMonthType(),
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime(),
                evaluationAverageScore,
                "avgProcessSpeed,avgServiceAttituude,avgTechnicalLevel,avgSum"
        );
        resultMap.put("dayOrMonthType",wsWorkSheetStatisticalInputVo.getDayOrMonthType());
        resultMap.put("list",
                // 时间升序
                evaluationAverageScore.stream()
                        .sorted(Comparator.comparing(o -> DateUtils.stringtoDate(o.get("date") + "",wsWorkSheetStatisticalInputVo.getDayOrMonthType())))
                        .collect(Collectors.toList()));
        ;
        return resultMap;

    }

    /**
     * 各科室工单评价各类型平均分及总评分平均分
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getDeptEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        return wsEvaluationMapper.getDeptEvaluationAverageScore(wsWorkSheetStatisticalInputVo);
    }

    /**
     * 查询用户处理工单评价分
     *
     * @param fkDeptId  科室id
     * @param beginTime
     * @param endTime
     * @param limit     返回条数
     * @return
     */
    @Override
    public List<WsEvaluationTopOutVo> getUserEvaluationAverageScore(String fkDeptId, String beginTime, String endTime, Integer limit) {
        List<String> list = null;
        if(!StringUtil.isEmpty(fkDeptId)){
            list = Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue()));
        }
        return wsEvaluationMapper.getUserEvaluationAverageScore(list, beginTime, endTime, limit);
    }


    /**
     * 查询各科室某段时间内，科室评分
     *
     * @param page
     * @return
     */
    @Override
    public List<Map<String, Object>> getEvaluationGroupByDeptAverageScore(Page page) {
        return wsEvaluationMapper.getEvaluationGroupByDeptAverageScore(page,DateUtils.getMonthStart(), DateUtils.getCurrentTime());
    }


    /**
     * 评价
     */
    @Override
    public PlatformResult workSheetToEvaluate(WsWsEvaluateInputVo wsEvaluateInputVo) {
        WsWsTask wsTask = Optional.ofNullable(wsSheetTaskService.selectOneWsTaskById(wsEvaluateInputVo.getPkWsTaskId()))
                .map(temp -> temp.get())
                .orElseThrow(new BusinessException("未查询到数据"));
        if(IndexEnum.ONE.getValue() == wsTask.getComplete()){
            throw new BusinessException("当前步骤已有其他人操作过，请刷新当前页面");
        }
        // 插入评价记录
        WsWsEvaluation wsEvaluation = new WsWsEvaluation();
        wsEvaluation.setWorkNumber(wsTask.getWorkNumber());
        wsEvaluation.setPkWsEvaluationId(IdUtils.getId());
        wsEvaluation.setProcessSpeed(wsEvaluateInputVo.getProcessSpeed());
        wsEvaluation.setServiceAttituude(wsEvaluateInputVo.getServiceAttituude());
        wsEvaluation.setTechnicalLevel(wsEvaluateInputVo.getTechnicalLevel());
        insert(wsEvaluation);

        // 更新工单业务主表
        wsSheetService.workSheetComplete(
                wsSheetService.selectOneWsSheet(wsTask.getWorkNumber()),
                WorkSheetStatusEnum.COMPLETED.getValue(),
                new DateTime(),
                Float.parseFloat(IndexEnum.ZERO.getValue() + "")
        );
        // 拼接备注
        String remark = CommonlyConstants.Evaluation.PROCESS_SPEED + wsEvaluateInputVo.getProcessSpeed() + "颗星，"
                + CommonlyConstants.Evaluation.SERVICE_ATTITUUDE + wsEvaluateInputVo.getServiceAttituude() + "颗星，"
                + CommonlyConstants.Evaluation.TECHNICAL_LEVEL + wsEvaluateInputVo.getTechnicalLevel() + "颗星。";
        wsTask.setTakeRemark(wsTask.getTakeRemark()+ CommonlyConstants.CuttOperator.CUT +remark);
        // 当前节点步骤更新为已完成
        wsSheetTaskService.workSheetTaskComplete(wsTask, WorkSheetStatusEnum.COMPLETED.getValue());
//        wsSheetTaskService.updateWsTaskById(wsTask);
        return PlatformResult.success("工单已完成");
    }
}
