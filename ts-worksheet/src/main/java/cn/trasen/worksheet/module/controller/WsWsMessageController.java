package cn.trasen.worksheet.module.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.service.WsWsMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "消息管理")
@RestController
public class WsWsMessageController {

    @Autowired
    private WsWsMessageService wsWsMessageService;

    @ControllerLog(description="分页列表")
    @ApiOperation(value = "分页列表", notes = "分页列表")
    @GetMapping("/message/selectMessagePageList")
    public PlatformResult selectMessagePageList(Page page, int isRead) {
        return PlatformResult.success(wsWsMessageService.selectMessagePageList(page, isRead));
    }

    @ControllerLog(description="查看详情")
    @ApiOperation(value = "查看详情", notes = "查看详情")
    @GetMapping("/message/selectOneWsWsMessageListOutVoById/{pkWsMessageId}/{isRead}")
    public PlatformResult selectOneWsWsMessageListOutVoById(@PathVariable String pkWsMessageId, @PathVariable int isRead) {
        return PlatformResult.success(wsWsMessageService.selectOneWsWsMessageListOutVoById(pkWsMessageId, isRead));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="全部置为已读")
    @ApiOperation(value = "全部置为已读", notes = "全部置为已读")
    @GetMapping("/message/updateMessageAllByFkUserId")
    public PlatformResult updateMessageAllByFkUserId() {
    	try {
    		return PlatformResult.success(wsWsMessageService.updateMessageAllByFkUserId());
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }
}
