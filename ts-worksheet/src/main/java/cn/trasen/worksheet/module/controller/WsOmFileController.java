package cn.trasen.worksheet.module.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.service.WsOmFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/7/22 10:51
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "OM录音文件管理")
@RestController
public class WsOmFileController {

    @Autowired
    private WsOmFileService wsOmFileService;

    @ControllerLog(description="拉取OM录音文件")
    @ApiOperation(value = "拉取录音文件", notes = "拉取录音文件")
    @GetMapping("/om/pullTheFile/{workNumber}")
    public PlatformResult pullTheFile(@PathVariable @ApiParam(value = "工单编号") String workNumber){
        return PlatformResult.success(wsOmFileService.pullTheFile(workNumber));
    }
}
