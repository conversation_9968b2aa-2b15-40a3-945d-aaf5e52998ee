package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/21 11:19
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WebSocketMsgOutVo {

    /**
     *  类型 0为等候列表1为来电弹屏，2为直接打开创建工单页面,3挂断自动应酬
     */
    private String type;

    /**
     *  通话记录id
     */
    private String pkCustometLogId;
    /**
     *  工单编号
     */
    private String workNumber;

    /**
     *  报修人科室id
     */
    private String repairManDeptId;

    /**
     *  报修人科室
     */
    private String repairManDeptName;

    /**
     *  报修人id
     */
    private String repairManId;

    /**
     *  报修人头像
     */
    private String repairAvatar;

    /**
     *  报修人
     */
    private String repairManName;

    /**
     * 报修人，当天报修次数
     */
    private int count;

    /**
     *  报修人联系方式
     */
    private String repairPhone;

    private List<WsCustometLogOutVo> wsCustometLogOutVoList;

    public WebSocketMsgOutVo() {
        super();
    }
}
