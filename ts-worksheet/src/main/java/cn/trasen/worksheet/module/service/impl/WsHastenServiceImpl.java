package cn.trasen.worksheet.module.service.impl;

import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.WorkSheetTaskEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsWsHatenInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsHatenListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsHatenOutVo;
import cn.trasen.worksheet.module.entity.WsWsHasten;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.entity.WsWsTask;
import cn.trasen.worksheet.module.mapper.WsWsHastenMapper;
import cn.trasen.worksheet.module.service.WsHastenService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;

/**
 * <AUTHOR>
 * @date: 2021/7/1 11:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsHastenServiceImpl implements WsHastenService {

    @Autowired
    private WsWsHastenMapper wsHastenMapper;
    @Autowired
    private WsSheetTaskService wsSheetTaskService;
    @Autowired
    private WsSheetService wsSheetService;

    @Transactional
    @Override
    public int save(WsWsHatenInputVo wsHatenInputVo) {
        WsWsHasten wsHasten = new WsWsHasten();
        wsHasten.setPkWsHastenId(IdUtils.getId());
        BeanUtils.copyProperties(wsHatenInputVo, wsHasten);
        // 催办信息加入节点
        WsWsSheet wsWsSheet = wsSheetService.selectOneWsSheet(wsHasten.getWorkNumber());
        WsWsTask wsTask = new WsWsTask();
        wsTask.setPkWsTaskId(IdUtils.getId());
        wsTask.setWorkNumber(wsWsSheet.getWorkNumber());
        wsTask.setTaskName(WorkSheetTaskEnum.HASTEN.getValue());
        wsTask.setTakeRemark("");
        wsTask.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
        wsTask.setComplete(IndexEnum.ZERO.getValue());
        wsTask.setAssist(IndexEnum.ZERO.getValue());
        wsTask.setFkUserId(wsWsSheet.getFkUserId());
        wsTask.setFkUserName(wsWsSheet.getFkUserName());
        wsTask.setFkUserDeptId(wsWsSheet.getFkUserDeptId());
        wsTask.setFkUserDeptName(wsWsSheet.getFkUserDeptName());
        wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsTask.setWorkStatus(wsWsSheet.getWorkStatus());
        wsSheetTaskService.insertWsTask(wsTask);
        wsHastenMapper.insertHasten(wsHasten);
        // 消息推送
        wsSheetTaskService.assembledMessages(wsHasten.getWorkNumber(), null,null);
        return IndexEnum.ONE.getValue();
    }

    /**
     * 查询催办次数，最新催办时间
     *
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public WsWsHatenOutVo getHastenInfo(String workNumber) {
        return wsHastenMapper.getHastenInfo(workNumber);
    }

    /**
     * 查询催办信息
     *
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public List<WsWsHatenListOutVo> getHastenInfoList(String workNumber) {
        return wsHastenMapper.getHastenInfoList(workNumber);
    }


}
