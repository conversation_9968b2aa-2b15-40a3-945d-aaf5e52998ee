<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.morality.dao.MoralityClassMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.morality.model.MoralityClass">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="class_title" jdbcType="VARCHAR" property="classTitle" />
    <result column="total_score" jdbcType="INTEGER" property="totalScore" />
    <result column="class_sort" jdbcType="INTEGER" property="classSort" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
	<collection property="moralityItemList" column="id"
				ofType="cn.trasen.oa.morality.model.MoralityItem"
				javaType="ArrayList"
				select="cn.trasen.oa.morality.dao.MoralityItemMapper.getMoralityItemList" />
  </resultMap>
  
  <select id="selectMoralityList" resultMap="BaseResultMap">
  		select * from toa_morality_class
  		where is_deleted = 'N'
  		order by class_sort
  </select>
  
  <select id="selectTotalScore" resultType="Long">
  		select sum(total_score) from toa_morality_class
  		where is_deleted = 'N'
  </select>
  

</mapper>