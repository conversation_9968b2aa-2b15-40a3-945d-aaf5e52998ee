<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.patrol.dao.CheckItemChildMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.patrol.model.CheckItemChild">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="start_check_id" jdbcType="VARCHAR" property="startCheckId" />
    <result column="check_item_id" jdbcType="VARCHAR" property="checkItemId" />
    <result column="item_child_id" jdbcType="VARCHAR" property="itemChildId" />
    <result column="item_child_name" jdbcType="VARCHAR" property="itemChildName" />
    <result column="check_value" jdbcType="VARCHAR" property="checkValue" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  
  <select id="getChildList" parameterType="cn.trasen.oa.patrol.model.CheckItemChild" resultType="cn.trasen.oa.patrol.model.CheckItemChild">
  	
  	select s.*,i.item_child_describe,i.item_child_type,i.item_child_required,i.item_child_content,i.item_child_describe from toa_check_item_child s 
	inner join toa_patrol_item_child i on s.item_child_id = i.id
	
	where 1 = 1 and s.is_deleted = 'N'
	
	<if test="checkItemId!=null and checkItemId!=''">
		and s.check_item_id =  #{checkItemId}
	</if>
	
	order by s.seq asc
  </select>
</mapper>