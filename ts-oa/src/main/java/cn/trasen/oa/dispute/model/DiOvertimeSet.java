package cn.trasen.oa.dispute.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "di_overtime_set")
@Setter
@Getter
public class DiOvertimeSet {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 事件类型id
     */
    @Column(name = "event_type_id")
    @ApiModelProperty(value = "事件类型id")
    private String eventTypeId;

    /**
     * 事件类型名称
     */
    @Column(name = "event_type_name")
    @ApiModelProperty(value = "事件类型名称")
    private String eventTypeName;

    /**
     * 状态  0禁用  1启用
     */
    @ApiModelProperty(value = "状态  0禁用  1启用")
    private String status;

    /**
     * 超时时间
     */
    @ApiModelProperty(value = "超时时间")
    private String overtime;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}