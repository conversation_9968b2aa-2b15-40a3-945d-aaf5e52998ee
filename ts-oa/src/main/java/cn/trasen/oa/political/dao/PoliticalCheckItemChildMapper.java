package cn.trasen.oa.political.dao;

import java.util.List;

import cn.trasen.oa.political.model.PoliticalCheckItemChild;
import tk.mybatis.mapper.common.Mapper;

public interface PoliticalCheckItemChildMapper extends Mapper<PoliticalCheckItemChild> {
	
	/**
	 * 
	* @Title: getChildList  
	* @Description: 根据条件获取巡查明细
	* @Params: @param record
	* @Params: @return      
	* @Return: List<CheckItemChild>
	* <AUTHOR>
	* @date:2021年12月28日
	* @Throws
	 */
	List<PoliticalCheckItemChild> getChildList(PoliticalCheckItemChild record);
}