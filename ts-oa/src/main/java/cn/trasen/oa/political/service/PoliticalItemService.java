package cn.trasen.oa.political.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.political.model.PoliticalItem;

/**
 * @ClassName PoliticalItemService
 * @Description TODO
 * @date 2021��12��21�� ����2:38:36
 * <AUTHOR>
 * @version 1.0
 */
public interface PoliticalItemService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021��12��21�� ����2:38:36
	 * <AUTHOR>
	 */
	Integer save(PoliticalItem record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021��12��21�� ����2:38:36
	 * <AUTHOR>
	 */
	Integer update(PoliticalItem record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021��12��21�� ����2:38:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PoliticalItem
	 * @date 2021��12��21�� ����2:38:36
	 * <AUTHOR>
	 */
	PoliticalItem selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PoliticalItem>
	 * @date 2021��12��21�� ����2:38:36
	 * <AUTHOR>
	 */
	DataSet<PoliticalItem> getDataSetList(Page page, PoliticalItem record);
}
