package cn.trasen.oa.political.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "toa_political_check_item")
@Setter
@Getter
public class PoliticalCheckItem {
    private String id;

    /**
     * 发起检查id
     */
    @Column(name = "start_check_id")
    @ApiModelProperty(value = "发起检查id")
    private String startCheckId;

    /**
     * 巡查项id
     */
    @Column(name = "political_item_id")
    @ApiModelProperty(value = "巡查项id")
    private String politicalItemId;

    /**
     * 巡查项名称
     */
    @Column(name = "political_item_name")
    @ApiModelProperty(value = "巡查项名称")
    private String politicalItemName;

    /**
     * 检查状态 0 未检查 1 已检查
     */
    @Column(name = "check_status")
    @ApiModelProperty(value = "检查状态 0 未检查 1 已检查")
    private String checkStatus;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer seq;
    
    @Transient
    private List<PoliticalCheckItemChild> itemChildList;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
}