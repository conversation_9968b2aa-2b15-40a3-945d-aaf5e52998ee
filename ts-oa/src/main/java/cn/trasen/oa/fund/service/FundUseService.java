package cn.trasen.oa.fund.service;

import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.fund.model.FundEntry;
import cn.trasen.oa.fund.model.FundUse;

/**
 * @ClassName FundUseService
 * @Description TODO
 * @date 2022��9��30�� ����9:11:37
 * <AUTHOR>
 * @version 1.0
 */
public interface FundUseService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	Integer save(FundUse record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	Integer update(FundUse record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return FundUse
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	Map<String, Object> selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<FundUse>
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	DataSet<Map<String, Object>> getDataSetList(Page page, FundEntry record);
	
	
	
	/**
	 * @Title updateStatus
	 * @Description 更新状态
	 * @param record
	 * @return Integer
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	Integer updateStatus(FundUse record);
	
	
	
	/**
	 * @Title update
	 * @Description 审批通过
	 * @param record
	 * @return Integer
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	Integer pass(FundUse record);
	
	/**
	 * @Title update
	 * @Description 审批不通过
	 * @param record
	 * @return Integer
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	Integer fail(FundUse record);
	
	
	
	Map<String, Object> selectApprovalDetails(FundUse record);
	
}
