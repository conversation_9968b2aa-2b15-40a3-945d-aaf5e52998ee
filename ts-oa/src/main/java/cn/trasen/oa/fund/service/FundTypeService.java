package cn.trasen.oa.fund.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.fund.model.FundType;

/**
 * @ClassName FundTypeService
 * @Description TODO
 * @date 2022��9��26�� ����3:25:05
 * <AUTHOR>
 * @version 1.0
 */
public interface FundTypeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��9��26�� ����3:25:05
	 * <AUTHOR>
	 */
	Integer save(FundType record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2022��9��26�� ����3:25:05
	 * <AUTHOR>
	 */
	Integer update(FundType record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2022��9��26�� ����3:25:05
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return FundType
	 * @date 2022��9��26�� ����3:25:05
	 * <AUTHOR>
	 */
	FundType selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<FundType>
	 * @date 2022��9��26�� ����3:25:05
	 * <AUTHOR>
	 */
	DataSet<FundType> getDataSetList(Page page, FundType record);
	
	List<FundType>  getFundTypeTree(FundType record);
}
