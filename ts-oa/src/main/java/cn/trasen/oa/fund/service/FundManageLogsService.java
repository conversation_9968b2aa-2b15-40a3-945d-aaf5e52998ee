package cn.trasen.oa.fund.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.fund.model.FundManageLogs;

/**
 * @ClassName FundManageLogsService
 * @Description TODO
 * @date 2022��10��10�� ����3:17:21
 * <AUTHOR>
 * @version 1.0
 */
public interface FundManageLogsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	Integer save(FundManageLogs record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	Integer update(FundManageLogs record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return FundManageLogs
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	FundManageLogs selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<FundManageLogs>
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	DataSet<FundManageLogs> getDataSetList(Page page, FundManageLogs record);
	
	
	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return FundManageLogs
	 * @date 2022��10��10�� ����3:17:21
	 * <AUTHOR>
	 */
	List<FundManageLogs> getListBybusinessId(String ibusinessId);  
}
