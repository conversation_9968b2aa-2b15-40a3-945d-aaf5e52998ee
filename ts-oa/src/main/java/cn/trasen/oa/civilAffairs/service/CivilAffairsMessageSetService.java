package cn.trasen.oa.civilAffairs.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsMessageSet;

/**
 * @ClassName CivilAffairsMessageSetService
 * @Description TODO
 * @date 2024��4��24�� ����4:57:15
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsMessageSetService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��4��24�� ����4:57:15
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsMessageSet record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��4��24�� ����4:57:15
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsMessageSet record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��4��24�� ����4:57:15
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsMessageSet
	 * @date 2024��4��24�� ����4:57:15
	 * <AUTHOR>
	 */
	CivilAffairsMessageSet selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsMessageSet>
	 * @date 2024��4��24�� ����4:57:15
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsMessageSet> getDataSetList(Page page, CivilAffairsMessageSet record);
	
	List<Map<String,Object>> getNoticeSourceSubject();
	
	//只查询最近一条数据
	CivilAffairsMessageSet getCivilAffairsMessageSetOne(CivilAffairsMessageSet record);
}
