package cn.trasen.oa.civilAffairs.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.civilAffairs.dao.CivilFamilyInformationMapper;
import cn.trasen.oa.civilAffairs.model.CivilFamilyInformation;
import cn.trasen.oa.civilAffairs.service.CivilFamilyInformationService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilFamilyInformationServiceImpl
 * @Description TODO
 * @date 2024��1��25�� ����2:30:10
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CivilFamilyInformationServiceImpl implements CivilFamilyInformationService {

	@Autowired
	private CivilFamilyInformationMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CivilFamilyInformation record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CivilFamilyInformation record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CivilFamilyInformation record = new CivilFamilyInformation();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CivilFamilyInformation selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CivilFamilyInformation> getDataSetList(Page page, CivilFamilyInformation record) {
		Example example = new Example(CivilFamilyInformation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CivilFamilyInformation> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<CivilFamilyInformation> getPageList(Page page, CivilFamilyInformation record) {
		// TODO Auto-generated method stub
		List<CivilFamilyInformation> records = mapper.getPageList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
