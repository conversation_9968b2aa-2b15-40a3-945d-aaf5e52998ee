<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilContractInformationMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilContractInformation">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="personnel_id" jdbcType="VARCHAR" property="personnelId" />
    <result column="signing_date" jdbcType="VARCHAR" property="signingDate" />
    <result column="due_date" jdbcType="VARCHAR" property="dueDate" />
    <result column="nursing_expenses" jdbcType="VARCHAR" property="nursingExpenses" />
    <result column="board_expenses" jdbcType="VARCHAR" property="boardExpenses" />
    <result column="other" jdbcType="VARCHAR" property="other" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
      <result column="start_date" jdbcType="VARCHAR" property="startDate" />
      <result column="partya_sign_name" jdbcType="VARCHAR" property="partyaSignName" />
      <result column="partyb_representative_sign" jdbcType="VARCHAR" property="partybRepresentativeSign" />
      <result column="partyb_relation" jdbcType="VARCHAR" property="partybRelation" />
      <result column="partyb_identity_number" jdbcType="VARCHAR" property="partybIdentityNumber" />
      <result column="partyb_telephone" jdbcType="VARCHAR" property="partybTelephone" />
      <result column="nursing_level" jdbcType="VARCHAR" property="nursingLevel" />
           
  </resultMap>
  
   <select id="getPageList" resultType="cn.trasen.oa.civilAffairs.model.CivilContractInformation" parameterType="cn.trasen.oa.civilAffairs.model.CivilContractInformation">
	  	
		  select  s.name,s.identity_number as identityNumber,a.*,hljb.item_name as nursingLevelText,xb.item_name as sexText,rybq.item_name as wardText from civil_affairs_personnel_info s inner join civil_contract_information a on s.personnel_id=a.personnel_id
		  			     left join  civil_admission_information ry on s.personnel_id=ry.personnel_id and ry.is_deleted='N'  
		  				left join (
                               SELECT
                               A.*
                               FROM
                               COMM_DICT_ITEM A
                               LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                               WHERE
                               B.TYPE_CODE = 'civil_nursing_level'
                               AND B.IS_DELETED = 'N'
                               AND A.IS_DELETED = 'N'
                               AND A.IS_ENABLE = '1'
                               AND A.sso_org_code = #{ssoOrgCode}
                        ) hljb on a.nursing_level=hljb.item_name_value
		  				left join (
                               SELECT
                               A.*
                               FROM
                               COMM_DICT_ITEM A
                               LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                               WHERE
                               B.TYPE_CODE = 'SEX_TYPE'
                               AND B.IS_DELETED = 'N'
                               AND A.IS_DELETED = 'N'
                               AND A.IS_ENABLE = '1'
                               AND A.sso_org_code = #{ssoOrgCode}
                        ) xb on s.sex=xb.item_name_value
		  				left join  (
                               SELECT
                               A.*
                               FROM
                               COMM_DICT_ITEM A
                               LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                               WHERE
                               B.TYPE_CODE = 'civil_admission_ward'
                               AND B.IS_DELETED = 'N'
                               AND A.IS_DELETED = 'N'
                               AND A.IS_ENABLE = '1'
                               AND A.sso_org_code = #{ssoOrgCode}
                         ) rybq on ry.ward=rybq.item_name_value
		  				where 1 =1  and s.is_deleted='N' and a.is_deleted='N'
		  <if test="personnelId !=null and personnelId !=''">
		  	and s.personnel_id = #{personnelId}
		  </if>
		  <if test="name !=null and name !=''">
		  	and s.name like concat('%',#{name},'%') 
		  </if>
		  <if test="identityNumber !=null and identityNumber !=''">
		  	and s.identity_number like concat('%',#{identityNumber},'%') 
		  </if>
		  <if test="objectType !=null">
		  	and s.object_type=#{objectType}
		  </if>
		   <if test="signingDateBeging !=null and signingDateBeging !='' and signingDateEnd !=null and signingDateEnd !='' ">
		  	and a.signing_date  between  #{signingDateBeging} and  #{signingDateEnd}
		  </if>
	  </select>
	  
</mapper>