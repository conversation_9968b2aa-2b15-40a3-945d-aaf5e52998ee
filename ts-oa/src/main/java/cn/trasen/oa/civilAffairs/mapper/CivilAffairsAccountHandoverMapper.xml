<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAffairsAccountHandoverMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAffairsAccountHandover">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="account_management_id" jdbcType="VARCHAR" property="accountManagementId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="account_balance" jdbcType="DECIMAL" property="accountBalance" />
    <result column="registration_date" jdbcType="DATE" property="registrationDate" />
    <result column="deliverer" jdbcType="VARCHAR" property="deliverer" />
    <result column="recipient" jdbcType="VARCHAR" property="recipient" />
    <result column="certifier" jdbcType="VARCHAR" property="certifier" />
    <result column="handover_date" jdbcType="DATE" property="handoverDate" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  
      <select id="selectPageList" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsAccountHandover" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsAccountHandover">
	  	
		  select a.*,khh.item_name AS bankText  from civil_affairs_account_handover a inner join civil_affairs_account_management b on a.account_management_id=b.id and b.is_deleted='N' 
		  		LEFT JOIN (
					  SELECT
					  A.*
					  FROM
					  COMM_DICT_ITEM A
					  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
					  WHERE
					  B.TYPE_CODE = 'civil_account_management_bank'
					  AND B.IS_DELETED = 'N'
					  AND A.IS_DELETED = 'N'
					  AND A.IS_ENABLE = '1'
		  			  AND A.sso_org_code = #{ssoOrgCode}
			    ) khh ON b.bank=khh.item_name_value
		  where 1 =1  and a.is_deleted='N' 
		  
		  <if test="accountManagementId !=null and accountManagementId !=''">
		  	and a.account_management_id    = #{accountManagementId}
		  </if>
		  <if test="name !=null and name !=''">
		  	and a.name like concat('%',#{name},'%') 
		  </if>
		  <if test="deliverer !=null and deliverer !=''">
		  	and a.deliverer concat('%',#{deliverer},'%') 
		  </if>
		  <if test="recipient !=null and recipient !=''">
		  	and a.recipient like concat('%',#{recipient},'%') 
		  </if>
		  <if test="certifier !=null and certifier !=''">
		  	and a.certifier like concat('%',#{certifier},'%') 
		  </if>
		  <if test="idCard !=null and idCard !=''">
		  	and a.id_card like concat('%',#{idCard},'%') 
		  </if>
		  <if test="account !=null and account !=''">
		  	and a.account like concat('%',#{account},'%') 
		  </if>
		  <if test="type !=null ">
		  	and a.type  = #{type}
		  </if>
		  <if test="handoverDateBegin !=null and handoverDateBegin !='' and handoverDateEnd !=null and handoverDateEnd !=''">
		  	and a.handover_date  between #{handoverDateBegin}   and  #{handoverDateEnd}  
		  </if>
	  </select>
</mapper>