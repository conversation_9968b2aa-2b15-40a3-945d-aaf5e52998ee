package cn.trasen.oa.civilAffairs.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsAnnualBudget;
import cn.trasen.oa.civilAffairs.service.CivilAffairsAnnualBudgetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsAnnualBudgetController年度预算~~专项资金管理
 * @Description TODO
 * @date 2023��12��18�� ����4:01:39
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsAnnualBudgetController")
public class CivilAffairsAnnualBudgetController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsAnnualBudgetController.class);

	@Autowired
	private CivilAffairsAnnualBudgetService civilAffairsAnnualBudgetService;

	/**
	 * @Title saveCivilAffairsAnnualBudget
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilAffairsAnnualBudget/save")
	public PlatformResult<String> saveCivilAffairsAnnualBudget(@RequestBody CivilAffairsAnnualBudget record) {
		try {
			civilAffairsAnnualBudgetService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsAnnualBudget
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilAffairsAnnualBudget/update")
	public PlatformResult<String> updateCivilAffairsAnnualBudget(@RequestBody CivilAffairsAnnualBudget record) {
		try {
			civilAffairsAnnualBudgetService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsAnnualBudgetById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsAnnualBudget>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilAffairsAnnualBudget/{id}")
	public PlatformResult<CivilAffairsAnnualBudget> selectCivilAffairsAnnualBudgetById(@PathVariable String id) {
		try {
			CivilAffairsAnnualBudget record = civilAffairsAnnualBudgetService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsAnnualBudgetById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilAffairsAnnualBudget/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsAnnualBudgetById(@PathVariable String id) {
		try {
			civilAffairsAnnualBudgetService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsAnnualBudgetList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAnnualBudget>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsAnnualBudget/list")
	public DataSet<CivilAffairsAnnualBudget> selectCivilAffairsAnnualBudgetList(Page page, CivilAffairsAnnualBudget record) {
		return civilAffairsAnnualBudgetService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsAnnualBudgetPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAnnualBudget>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/CivilAffairsAnnualBudget/PageList")
	public DataSet<CivilAffairsAnnualBudget> selectCivilAffairsAnnualBudgetPageList(Page page, CivilAffairsAnnualBudget record) {
		return civilAffairsAnnualBudgetService.getPageList(page, record);
	}
	
	/**
	 * @Title selectCivilAffairsAnnualBudgetPageList
	 * @Description 专项资金：首页按年度统计
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAnnualBudget>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "专项资金：首页按年度统计", notes = "专项资金：首页按年度统计")
	@GetMapping("/api/CivilAffairsAnnualBudget/selectCountByDate")
	public Map<String,Object> selectCountByDate(CivilAffairsAnnualBudget record) {
		return civilAffairsAnnualBudgetService.selectCountByDate(record);
	}
	
	
	/**
	 * @Title selectSubjectCountByEntry
	 * @Description 专项资金预算分布图统计(科目)
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAnnualBudget>
	 * @date 2023��12��18�� ����4:01:39
	 * <AUTHOR>
	 */
	@ApiOperation(value = "专项资金预算分布图统计(科目)", notes = "专项资金预算分布图统计(科目)")
	@GetMapping("/api/CivilAffairsAnnualBudget/selectSubjectCountByEntry")
	public List<CivilAffairsAnnualBudget> selectSubjectCountByEntry(CivilAffairsAnnualBudget record) {
		return civilAffairsAnnualBudgetService.selectSubjectCountByEntry(record);
	}
}
