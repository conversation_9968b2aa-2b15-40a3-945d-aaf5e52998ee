package cn.trasen.oa.civilAffairs.controller;

import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.dao.CivilResidenceInformationMapper;
import cn.trasen.oa.civilAffairs.model.CivilResidenceInformation;
import cn.trasen.oa.civilAffairs.service.CivilResidenceInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilResidenceInformationController 户籍信息
 * @Description TODO
 * @date 2024��1��25�� ����2:32:26
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilResidenceInformationController")
public class CivilResidenceInformationController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilResidenceInformationController.class);

	@Autowired
	private CivilResidenceInformationService civilResidenceInformationService;
	
	@Autowired
	private CivilResidenceInformationMapper civilResidenceInformationMapper;
	

	/**
	 * @Title saveCivilResidenceInformation
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilResidenceInformation/save")
	public PlatformResult<String> saveCivilResidenceInformation(@RequestBody CivilResidenceInformation record) {
		try {
			civilResidenceInformationService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilResidenceInformation
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilResidenceInformation/update")
	public PlatformResult<String> updateCivilResidenceInformation(@RequestBody CivilResidenceInformation record) {
		try {
			if(record != null && StringUtils.isNotBlank(record.getId())){
				civilResidenceInformationService.update(record);
			}else{
				civilResidenceInformationService.save(record);
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilResidenceInformationById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilResidenceInformation>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilResidenceInformation/{id}")
	public PlatformResult<CivilResidenceInformation> selectCivilResidenceInformationById(@PathVariable String id) {
		try {
			CivilResidenceInformation record = civilResidenceInformationService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilResidenceInformationById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilResidenceInformation/delete/{id}")
	public PlatformResult<String> deleteCivilResidenceInformationById(@PathVariable String id) {
		try {
			civilResidenceInformationService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilResidenceInformationList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilResidenceInformation>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilResidenceInformation/list")
	public DataSet<CivilResidenceInformation> selectCivilResidenceInformationList(Page page, CivilResidenceInformation record) {
		return civilResidenceInformationService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilResidenceInformationPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilResidenceInformation>
	 * @date 2024��1��25�� ����2:32:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "关联主表字段列表", notes = "关联主表字段列表")
	@GetMapping("/api/CivilResidenceInformation/PageList")
	public DataSet<CivilResidenceInformation> selectCivilResidenceInformationPageList(Page page, CivilResidenceInformation record) {
		return civilResidenceInformationService.getPageList(page, record);
	}
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出列表", notes = "导出列表")
	@GetMapping("/api/CivilResidenceInformation/exportPageList")
	public void exportPageList(HttpServletRequest request, HttpServletResponse response, Page page, CivilResidenceInformation record) {
	record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
	List<CivilResidenceInformation> records = civilResidenceInformationMapper.getPageList(page,record);
		
	// 导出文件名称
	String name = "户籍信息.xls";
	
	// 模板位置
	String templateUrl = "template/civilResidenceInformation.xls";
	// 导出数据列表
		try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
