<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAffairsGoodsManagementMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAffairsGoodsManagement">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
     <result column="goods_name_pym" jdbcType="VARCHAR" property="goodsNamePym" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="inventory_number" jdbcType="INTEGER" property="inventoryNumber" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_name_pym" jdbcType="VARCHAR" property="supplierNamePym" />
    <result column="purchase_date" jdbcType="DATE" property="purchaseDate" />
    <result column="deliverer" jdbcType="VARCHAR" property="deliverer" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
   <select id="selectPageList" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsGoodsManagement" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsGoodsManagement">
	  	
		  select *from  civil_affairs_goods_management where 1 =1   and is_deleted='N'
		  <if test="goodsName !=null and goodsName !=''">
		  	and (goods_name like concat('%',#{goodsName},'%') or goods_name_pym  like concat('%',#{goodsName},'%') )
		  </if>
		  <if test="supplierName !=null and supplierName !=''">
		  	and (supplier_name like concat('%',#{supplierName},'%') or supplier_name_pym  like concat('%',#{supplierName},'%') )
		  </if>
	  </select>
</mapper>