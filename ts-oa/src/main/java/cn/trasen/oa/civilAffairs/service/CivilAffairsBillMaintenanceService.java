package cn.trasen.oa.civilAffairs.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsBillMaintenance;

/**
 * @ClassName CivilAffairsBillMaintenanceService
 * @Description TODO
 * @date 2024��5��21�� ����10:50:38
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsBillMaintenanceService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsBillMaintenance record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsBillMaintenance record);
	
	Integer updateStatus(CivilAffairsBillMaintenance record);
	

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsBillMaintenance
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	CivilAffairsBillMaintenance selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsBillMaintenance>
	 * @date 2024��5��21�� ����10:50:38
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsBillMaintenance> getDataSetList(Page page, CivilAffairsBillMaintenance record);
	
	DataSet<CivilAffairsBillMaintenance> selectPageList(Page page, CivilAffairsBillMaintenance record);
	
}
