<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAdmissionTransferWardMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAdmissionTransferWard">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="admission_id" jdbcType="VARCHAR" property="admissionId" />
    <result column="transfer_date" jdbcType="VARCHAR" property="transferDate" />
    <result column="before_ward" jdbcType="VARCHAR" property="beforeWard" />
    <result column="after_ward" jdbcType="VARCHAR" property="afterWard" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
</mapper>