package cn.trasen.oa.civilAffairs.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsFundingAvailabilityMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsFundingAvailability;
import cn.trasen.oa.civilAffairs.service.CivilAffairsFundingAvailabilityService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilAffairsFundingAvailabilityServiceImpl
 * @Description TODO
 * @date 2023��12��18�� ����4:03:52
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CivilAffairsFundingAvailabilityServiceImpl implements CivilAffairsFundingAvailabilityService {

	@Autowired
	private CivilAffairsFundingAvailabilityMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CivilAffairsFundingAvailability record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CivilAffairsFundingAvailability record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CivilAffairsFundingAvailability record = new CivilAffairsFundingAvailability();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CivilAffairsFundingAvailability selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CivilAffairsFundingAvailability> getDataSetList(Page page, CivilAffairsFundingAvailability record) {
		Example example = new Example(CivilAffairsFundingAvailability.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if (StringUtils.isNotBlank(record.getYear())) {//年度
			 example.and().andEqualTo("year", record.getYear());
	        }
		 if (StringUtils.isNotBlank(record.getEntryName())) {//项目名称
			 example.and().andEqualTo("entryName", record.getEntryName());
	        }
		 if (StringUtils.isNotBlank(record.getEntryCode())) {//项目编码
			 example.and().andEqualTo("entryCode", record.getEntryCode());
	        }
		 if (StringUtils.isNotBlank(record.getFileNumber())) {//文号
	            example.and().andLike("fileNumber", "%" + record.getFileNumber() + "%");
	        }
		 if (null != record.getReceiveDate()) {//到位日期
	            example.and().andLike("receiveDate", "%" + record.getReceiveDate() + "%");
	        }
		 
		 
		List<CivilAffairsFundingAvailability> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<CivilAffairsFundingAvailability> getPageList(Page page, CivilAffairsFundingAvailability record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		// TODO Auto-generated method stub
		List<CivilAffairsFundingAvailability> records = mapper.getPageList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
