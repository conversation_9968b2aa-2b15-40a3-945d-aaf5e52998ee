<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAffairsAuthorityMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAffairsAuthority">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="person_id" jdbcType="VARCHAR" property="personId" />
    <result column="person_name" jdbcType="VARCHAR" property="personName" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
    <select id="selectCivilAffairsAuthorityByUserId" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsAuthority" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsAuthority">
  	select * from civil_affairs_authority s where s.is_deleted = 'N' 
  	<if test="personId!=null and personId!=''">
  		and FIND_IN_SET(#{personId},person_id)  
  	</if>
  
  </select>
</mapper>