package cn.trasen.oa.civilAffairs.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsFieldGroup;
import cn.trasen.oa.civilAffairs.service.CivilAffairsFieldGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsFieldGroupController自定义分组表
 * @Description TODO
 * @date 2023��12��18�� ����5:00:18
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsFieldGroupController")
public class CivilAffairsFieldGroupController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsFieldGroupController.class);

	@Autowired
	private CivilAffairsFieldGroupService civilAffairsFieldGroupService;

	/**
	 * @Title saveCivilAffairsFieldGroup
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����5:00:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增自定义档案分组")
	@PostMapping("/api/CivilAffairsFieldGroup/save")
	public PlatformResult<String> saveCivilAffairsFieldGroup(@RequestBody CivilAffairsFieldGroup record) {
		try {
			//验证新增分组是否存在,表是否存在了
			boolean flag = civilAffairsFieldGroupService.checkName(record);
			if(flag) {
				int count = civilAffairsFieldGroupService.save(record);
				if(count>0) {
            		return PlatformResult.success();
            	}else {
            		return PlatformResult.failure();	
            	}
        	}else {
        		return PlatformResult.failure("分组名称已存在,请修改");	
        	}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsFieldGroup
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����5:00:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "修改自定义档案分组")
	@PostMapping("/api/CivilAffairsFieldGroup/update")
	public PlatformResult<String> updateCivilAffairsFieldGroup(@RequestBody CivilAffairsFieldGroup record) {
		try {
			int count = civilAffairsFieldGroupService.update(record);
			if(count>0) {
        		return PlatformResult.success();
        	}else {
        		return PlatformResult.failure();	
        	}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	 	@ApiOperation(value = "根据列表修改自定义人员档案分组", notes = "根据列表修改自定义人员档案分组")
	    @PostMapping("/api/CivilAffairsFieldGroup/updateList")
	    public PlatformResult<CivilAffairsFieldGroup> updateList(@RequestBody List<CivilAffairsFieldGroup> records) {
	        try {
	        	civilAffairsFieldGroupService.updateList(records);
	            return PlatformResult.success();
	        } catch (Exception e) {
	        	return PlatformResult.failure(e.getMessage());
	        }
	    }

	/**
	 * 
	 * @Title selectCivilAffairsFieldGroupById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsFieldGroup>
	 * @date 2023��12��18�� ����5:00:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "根据id查询自定义人员档案分组")
	@GetMapping("/api/CivilAffairsFieldGroup/{id}")
	public PlatformResult<CivilAffairsFieldGroup> selectCivilAffairsFieldGroupById(@PathVariable String id) {
		try {
			CivilAffairsFieldGroup record = civilAffairsFieldGroupService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsFieldGroupById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��18�� ����5:00:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "根据id删除自定义档案分组")
	@PostMapping("/api/CivilAffairsFieldGroup/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsFieldGroupById(@PathVariable String id) {
		try {
			civilAffairsFieldGroupService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsFieldGroupList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsFieldGroup>
	 * @date 2023��12��18�� ����5:00:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilAffairsFieldGroup/list")
	public DataSet<CivilAffairsFieldGroup> selectCivilAffairsFieldGroupList(Page page, CivilAffairsFieldGroup record) {
		return civilAffairsFieldGroupService.getDataSetList(page, record);
	}
	
	
	 	@ApiOperation(value = "获取字段列表分组及分组明细", notes = "获取字段分组列表及分组明细(带权限)")
	    @PostMapping("/employeeField/getList")
	    public PlatformResult<List<CivilAffairsFieldGroup>> getList(CivilAffairsFieldGroup record) {
	        try {
	        	List<CivilAffairsFieldGroup> list = civilAffairsFieldGroupService.getFieldAndJurisdictionListByGroupid(record);
	            return PlatformResult.success(list);
	        } catch (Exception e) {
	        	e.printStackTrace();
	        	return PlatformResult.failure(e.getMessage());
	        }
	    }
	 
	 
	 	@ApiOperation(value = "获取字段列表分组及分组明细", notes = "获取字段分组列表及分组明细")
	    @PostMapping("/employeeField/getDataList")
	    public PlatformResult<List<CivilAffairsFieldGroup>> getDataList(CivilAffairsFieldGroup record) {
	        try {
	        	List<CivilAffairsFieldGroup> list = civilAffairsFieldGroupService.getDataList(record);
	            return PlatformResult.success(list);
	        } catch (Exception e) {
	        	e.printStackTrace();
	        	return PlatformResult.failure(e.getMessage());
	        }
	    }
	    
}
