package cn.trasen.oa.civilAffairs.controller;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.civilAffairs.dao.CivilHardshipProtectionMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsMessageSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsPersonnelInfo;
import cn.trasen.oa.civilAffairs.model.CivilHardshipProtection;
import cn.trasen.oa.civilAffairs.service.CivilAffairsMessageSetService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsPersonnelInfoService;
import cn.trasen.oa.civilAffairs.service.CivilHardshipProtectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilHardshipProtectionController  特困保障
 * @Description TODO
 * @date 2024��1��25�� ����2:28:24
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilHardshipProtectionController")
public class CivilHardshipProtectionController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilHardshipProtectionController.class);

	@Autowired
	private CivilHardshipProtectionService civilHardshipProtectionService;

	@Autowired
	private CivilHardshipProtectionMapper civilHardshipProtectionMapper;
	
	@Autowired
	private CivilAffairsPersonnelInfoService civilAffairsPersonnelInfoService;

	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	@Autowired
    private CivilAffairsMessageSetService  civilAffairsMessageSetService;
	
	/**
	 * @Title saveCivilHardshipProtection
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CivilHardshipProtection/save")
	public PlatformResult<String> saveCivilHardshipProtection(@RequestBody CivilHardshipProtection record) {
		try {
			Example example = new Example(CivilHardshipProtection.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("personnelId",record.getPersonnelId());
			criteria.andEqualTo("hardshipDate",record.getHardshipDate());
			List<CivilHardshipProtection> civilHardshipProtectionList = civilHardshipProtectionMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(civilHardshipProtectionList)) {
				return PlatformResult.failure("该月份日期已经存在特困标准,不可重复新增");
			}else {
				civilHardshipProtectionService.save(record);
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title copyLastMonthSave
	 * @Description 复制上月标准
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "复制上月标准", notes = "复制上月标准")
	@PostMapping("/api/CivilHardshipProtection/copyLastMonthSave")
	public PlatformResult<String> copyLastMonthSave(@RequestBody CivilHardshipProtection record) {
		try {
			Date now = new Date();
			SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM");
			
			Example example = new Example(CivilHardshipProtection.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("personnelId",record.getPersonnelId());
			criteria.andEqualTo("hardshipDate",sdf.format(now));
			List<CivilHardshipProtection> civilHardshipProtectionList = civilHardshipProtectionMapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(civilHardshipProtectionList)) {
				return PlatformResult.failure("当月已经存在特困标准,不可复制");
			}else {
				CivilHardshipProtection civilHardshipProtection =  civilHardshipProtectionMapper.selectLastMonthData(record);//查询上月数据
				if(civilHardshipProtection !=null) {
					civilHardshipProtectionService.copyLastMonthSave(civilHardshipProtection);//查询到上月数据、复制
					return PlatformResult.success();
				}else {
					return PlatformResult.failure("未有比当前月份小的数据,无法复制");
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	 private void sendMessage(String content,List<String> receiverList,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                //.receiver(receiver)  //接收人
	                .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-oa/civil-affairs-objects/civil-affairs-objects").source("民政对象")//跳转前端地址
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }

	/**
	 * @Title updateCivilHardshipProtection
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CivilHardshipProtection/update")
	public PlatformResult<String> updateCivilHardshipProtection(@RequestBody CivilHardshipProtection record) {
		try {
			if(record != null && StringUtils.isNotBlank(record.getId())){
				civilHardshipProtectionService.update(record);
			}else{
				Example example = new Example(CivilHardshipProtection.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria.andEqualTo("personnelId",record.getPersonnelId());
				criteria.andEqualTo("hardshipDate",record.getHardshipDate());
				List<CivilHardshipProtection> civilHardshipProtectionList = civilHardshipProtectionMapper.selectByExample(example);
				if(CollectionUtils.isNotEmpty(civilHardshipProtectionList)) {
					return PlatformResult.failure("该月份日期已经存在特困标准,不可重复新增");
				}else {
					civilHardshipProtectionService.save(record);
				}
				
				CivilAffairsPersonnelInfo civilAffairsPersonnelInfo = civilAffairsPersonnelInfoService.selectById(record.getPersonnelId());
				if(civilAffairsPersonnelInfo !=null) {
					String content = civilAffairsPersonnelInfo.getName()+ "特困保障于"+record.getHardshipDate()+"变更为"+record.getHardshipStandard()+"元/月,请知悉";
				    String subject = "民政对象——保障标准提醒";
				    String url =appConfigProperties.getWxDomain() + "";//微信端地址
				    
				    CivilAffairsMessageSet civilAffairsMessageSet  =  civilAffairsMessageSetService.getCivilAffairsMessageSetOne(new CivilAffairsMessageSet());	
				    
				    if (civilAffairsMessageSet != null && StringUtils.isNotEmpty(civilAffairsMessageSet.getGuaranteeUser())){//保障标准提醒人员
				    	sendMessage(content, Arrays.asList(civilAffairsMessageSet.getGuaranteeUser().split(",")), url, subject,"3");
				    }
				    //sendMessage(content, civilAffairsPersonnelInfo.getCreateUser(), url, subject,"3");//给创建人发送信息
				}
			}
			
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilHardshipProtectionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CivilHardshipProtection/{id}")
	public PlatformResult<CivilHardshipProtection> selectCivilHardshipProtectionById(@PathVariable String id) {
		try {
			CivilHardshipProtection record = civilHardshipProtectionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilHardshipProtectionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CivilHardshipProtection/delete/{id}")
	public PlatformResult<String> deleteCivilHardshipProtectionById(@PathVariable String id) {
		try {
			civilHardshipProtectionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilHardshipProtectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CivilHardshipProtection/list")
	public DataSet<CivilHardshipProtection> selectCivilHardshipProtectionList(Page page, CivilHardshipProtection record) {
		return civilHardshipProtectionService.getDataSetList(page, record);
	}
	
	/**
	 * @Title selectCivilHardshipProtectionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "关联主表分页列表", notes = "关联主表分页列表")
	@GetMapping("/api/CivilHardshipProtection/PageList")
	public DataSet<CivilHardshipProtection> selectCivilHardshipProtectionPageList(Page page, CivilHardshipProtection record) {
		return civilHardshipProtectionService.getPageList(page, record);
	}
	
	/**
	 * @Title selectHardshipCategoriesCount
	 * @Description 查询保障类型：特困、孤儿分布
	 * @param page
	 * @param record
	 * @return DataSet<CivilHardshipProtection>
	 * @date 2024��1��25�� ����2:28:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询保障类型：特困、孤儿分布", notes = "查询保障类型：特困、孤儿分布")
	@GetMapping("/api/CivilHardshipProtection/selectHardshipCategoriesCount")
	public List<Map<String , Object>> selectHardshipCategoriesCount(CivilHardshipProtection record) {
		return civilHardshipProtectionService.selectHardshipCategoriesCount(record);
	}
	
	/**
	* @date 2023年05月16日 14:48
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出列表", notes = "导出列表")
	@GetMapping("/api/CivilHardshipProtection/exportPageList")
	public void exportPageList(HttpServletRequest request, HttpServletResponse response, Page page, CivilHardshipProtection record) {
		
	List<CivilHardshipProtection> records = civilHardshipProtectionMapper.getPageList(page,record);
		
	// 导出文件名称
	String name = "特困保障.xls";
	
	// 模板位置
	String templateUrl = "template/civilHardshipProtection.xls";
	// 导出数据列表
		try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	 * @Title selectHardshipCategoriesCount
	 * @Description 根据年度、查询每月特困保障人数、新增人数、核减人数
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据年度、查询每月特困保障人数、新增人数、核减人数(首页统计)", notes = "根据年度、查询每月特困保障人数、新增人数、核减人数(首页统计)")
	@GetMapping("/api/CivilHardshipProtection/selectHardshipMonthDetails")
	public Map<String , Object> selectHardshipMonthDetails(CivilHardshipProtection record) {
		return civilHardshipProtectionService.selectHardshipMonthDetails(record);
	}
}
