package cn.trasen.oa.civilAffairs.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "civil_affairs_annual_budget")
@Setter
@Getter
public class CivilAffairsAnnualBudget {
    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private String year;

    /**
     * 项目编码
     */
    @Column(name = "entry_code")
    @ApiModelProperty(value = "项目编码")
    private String entryCode;

    /**
     * 项目名称
     */
    @Column(name = "entry_name")
    @ApiModelProperty(value = "项目名称")
    private String entryName;

    /**
     * 科目编码
     */
    @Column(name = "subject_code")
    @ApiModelProperty(value = "科目编码")
    private String subjectCode;

    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    private String subject;

    /**
     * 预算金额
     */
    @Column(name = "budget_amount")
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    @ApiModelProperty(value = "到位资金")
    private String receiveAmount;
    
    @Transient
    @ApiModelProperty(value = "到位率")
    private String receiveRate;
    
    @Transient
    @ApiModelProperty(value = "支出金额")
    private String expendAmount;
    
    @Transient
    @ApiModelProperty(value = "支出进度")
    private String expendRate;
    
    @Transient
    @ApiModelProperty(value = "项目文本")
    private String entryText;
    
    @Transient
    @ApiModelProperty(value = "科目文本")
    private String subjectText;
    
    @Transient
    @ApiModelProperty(value = "查询年份")
    private String queryYear;
    
    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;
    
}