<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAffairsChangeLedgerMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="manage_id" jdbcType="VARCHAR" property="manageId" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="amount_collected" jdbcType="DECIMAL" property="amountCollected" />
    <result column="expenditure_amount" jdbcType="DECIMAL" property="expenditureAmount" />
    <result column="balance_amount" jdbcType="DECIMAL" property="balanceAmount" />
    <result column="ledger_items" jdbcType="VARCHAR" property="ledgerItems" />
    <result column="relatives_autograph" jdbcType="VARCHAR" property="relativesAutograph" />
      <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
     <result column="status" jdbcType="VARCHAR" property="status" />
      <result column="review_status" jdbcType="VARCHAR" property="reviewStatus" />
  </resultMap>
  
    <select id="selectPageList" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger">
	  	
		 select a.*,b.name,b.identity_number as identityNumber,b.object_type as objectType,rybq.item_name as wardText from  civil_affairs_change_ledger a inner join civil_affairs_change_manage b on a.manage_id=b.id and b.is_deleted='N' 
		 	left join  (
				SELECT
				A.*
				FROM
				COMM_DICT_ITEM A
				LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
				WHERE
				B.TYPE_CODE = 'civil_admission_ward'
				AND B.IS_DELETED = 'N'
				AND A.IS_DELETED = 'N'
				AND A.IS_ENABLE = '1'
				AND A.sso_org_code = #{ssoOrgCode}
			) rybq on b.ward=rybq.item_name_value
		 	where 1 =1  and a.is_deleted='N'
		  	 <if test="manageId !=null and manageId !=''">
		  		and a.manage_id = #{manageId}
		 	 </if>
		 	 <if test="year !=null and year !=''">
		  		and a.register_date  like concat('',#{year},'%')
		 	 </if>
		 	 <if test="billNumber !=null and billNumber !=''">
		  		and a.bill_number  like concat('%',#{billNumber},'%')
		 	 </if>
		 	 <if test="serialNumber !=null and serialNumber !=''">
		  		and a.serial_number  like concat('',#{serialNumber},'%')
		 	 </if>
		 	 <if test="registerDateBegin !=null and registerDateBegin !='' and registerDateEnd !=null and registerDateEnd !=''">
		  		and a.register_date between #{registerDateBegin} and  #{registerDateEnd}
		 	 </if>
		 	 <if test="reviewStatus !=null and reviewStatus !=''">
		  		and a.review_status = #{reviewStatus}  and a.status=1
		 	 </if>
		 	  <if test="personnelId !=null and personnelId !=''">
		  		and b.personnel_id = #{personnelId}
		 	 </if>
		 	 <if test="ward !=null and ward !=''">
		  		and b.ward = #{ward}
		 	 </if>
		 	 <if test="objectType !=null and objectType !=''">
		  		and b.object_type = #{objectType}
		 	 </if>
		 	    <if test="condition !=null and condition !=''">
			  	and (b.name like concat('%',#{condition},'%') or b.pym like concat('%',#{condition},'%') or b.identity_number like concat('%',#{condition},'%') ) 
			  </if>
			   <if test="deptCode !=null and deptCode !=''">
		  		and b.dept_code = #{deptCode}
		 	 </if>
	  </select>
	  
	   <update id="updateByBillNumber" parameterType="String">
	    update civil_affairs_change_ledger set status=2
	   		   where manage_id = #{manageId}  and  (bill_number=#{billNumber} or  serial_number=#{billNumber})
	  </update>
	  
	  
	  <select id="selectAmount" resultType="Map" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger">
		 select ifnull(sum(a.amount_collected),0) as amountCollected,ifnull(sum(expenditure_amount),0) as expenditureAmount,(ifnull(sum(a.amount_collected),0)-ifnull(sum(expenditure_amount),0)) as  amount 
		 from  civil_affairs_change_ledger a inner join civil_affairs_change_manage b on a.manage_id=b.id and b.is_deleted='N' where 1 =1  and a.is_deleted='N'
		 		and a.status=1
		  	 <if test="manageId !=null and manageId !=''">
		  		and a.manage_id = #{manageId}
		 	 </if>
		 	 <if test="year !=null and year !=''">
		  		and a.register_date  like concat('',#{year},'%')
		 	 </if>
		 	 <if test="billNumber !=null and billNumber !=''">
		  		and a.bill_number  like concat('%',#{billNumber},'%')
		 	 </if>
		 	 <if test="serialNumber !=null and serialNumber !=''">
		  		and a.serial_number  like concat('',#{serialNumber},'%')
		 	 </if>
		 	 <if test="personnelId !=null and personnelId !=''">
		  		and b.personnel_id = #{personnelId}
		 	 </if>
	  </select>
	  
	   <update id="updateByNumber" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger">
	    update civil_affairs_change_ledger set expenditure_amount=#{expenditureAmount},ledger_items=#{ledgerItems}
	   		   where manage_id = #{manageId}  and serial_number=#{serialNumber}
	  </update>
	  
	  
	   <select id="selectMessageData" resultType="Map" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger">
		 select ifnull(sum(a.amount_collected),0) as amountCollected,ifnull(sum(a.expenditure_amount),0) as expenditureAmount,(ifnull(sum(a.amount_collected),0)-ifnull(sum(a.expenditure_amount),0)) as  amount,
		 b.name,b.identity_number as identityNumber,b.change_balance as changeBalance,c.telephone 
		 from  civil_affairs_change_ledger a inner join civil_affairs_change_manage b on a.manage_id=b.id and b.is_deleted='N'
		 		left  join (select personnel_id,SUBSTRING_INDEX(GROUP_CONCAT( distinct telephone order by create_date desc), ',', 1) as telephone   from  civil_family_information where is_deleted='N' and telephone is not null group by personnel_id order by create_date desc
		 		)c  on  b.personnel_id=c.personnel_id
		 	where 1 =1  and a.is_deleted='N' 
		 	 <if test="ids != null and ids.size() > 0">
				and ( a.id in
				<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
			           #{item}
			       </foreach>
			       )
			</if>
		  	<!--  <if test="manageId !=null and manageId !=''">
		  		and a.manage_id = #{manageId}
		 	 </if>
		 	 <if test="year !=null and year !=''">
		  		and a.register_date  like concat('',#{year},'%')
		 	 </if>
		 	 <if test="billNumber !=null and billNumber !=''">
		  		and a.bill_number  like concat('%',#{billNumber},'%')
		 	 </if>
		 	 <if test="serialNumber !=null and serialNumber !=''">
		  		and a.serial_number  like concat('',#{serialNumber},'%')
		 	 </if>
		 	 <if test="registerDateBegin !=null and registerDateBegin !='' and registerDateEnd !=null and registerDateEnd !=''">
		  		and a.register_date between #{registerDateBegin} and  #{registerDateEnd}
		 	 </if>
		 	 <if test="reviewStatus !=null and reviewStatus !=''">
		  		and a.review_status = #{reviewStatus}
		 	 </if>
		 	  <if test="personnelId !=null and personnelId !=''">
		  		and b.personnel_id = #{personnelId}
		 	 </if>
		 	 <if test="ward !=null and ward !=''">
		  		and b.ward = #{ward}
		 	 </if>
		 	 <if test="objectType !=null and objectType !=''">
		  		and b.object_type = #{objectType}
		 	 </if>
		 	   <if test="condition !=null and condition !=''">
			  	and (b.name like concat('%',#{condition},'%') or b.pym like concat('%',#{condition},'%') or b.identity_number like concat('%',#{condition},'%') ) 
			  </if> -->
			  group  by b.name,b.identity_number,b.change_balance,c.telephone
	  </select>
</mapper>