<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilArchivalInformationMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilArchivalInformation">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="personnel_id" jdbcType="VARCHAR" property="personnelId" />
    <result column="archival_number" jdbcType="VARCHAR" property="archivalNumber" />
    <result column="cabinet_number" jdbcType="VARCHAR" property="cabinetNumber" />
    <result column="box_number" jdbcType="VARCHAR" property="boxNumber" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <!--  <select id="getPageList" resultType="Map" parameterType="cn.trasen.oa.civilAffairs.model.CivilArchivalInformation">
	  	
		  select s.name,s.identity_number as identityNumber,a.*, b.* from civil_affairs_personnel_info s inner join civil_archival_information a on s.personnel_id=a.personnel_id  
		  				LEFT JOIN (select   personnel_id,sum(case when file_type='身份证' then 1 else 0 end) as '身份证',sum(case when file_type='户口' then 1 else 0 end) as '户口',
                        sum(case when file_type='DNA检测' then 1 else 0 end) as 'DNA检测',sum(case when file_type='捡拾信息' then 1 else 0 end) as '捡拾信息', 
                         sum(case when file_type='入院登记表' then 1 else 0 end) as '入院登记表',sum(case when file_type='入站登记表' then 1 else 0 end) as '入站登记表', 
                         sum(case when file_type='离站登记表' then 1 else 0 end) as '离站登记表',sum(case when file_type='离院确认书' then 1 else 0 end) as '离院确认书', 
                         sum(case when file_type='审批表' then 1 else 0 end) as '审批表',sum(case when file_type='核对报告' then 1 else 0 end) as '核对报告', 
                         sum(case when file_type='核对授权书' then 1 else 0 end) as '核对授权书',sum(case when file_type='安置文件' then 1 else 0 end) as '安置文件', 
                         sum(case when file_type='残疾证' then 1 else 0 end) as '残疾证',sum(case when file_type='儿童福利证' then 1 else 0 end) as '儿童福利证', 
                         sum(case when file_type='社保卡' then 1 else 0 end) as '社保卡',sum(case when file_type='年审资料' then 1 else 0 end) as '年审资料', 
                         sum(case when file_type='体检材料' then 1 else 0 end) as '体检材料',sum(case when file_type='其他资料' then 1 else 0 end) as '其他资料'
                        from civil_archiva_files where is_deleted='N'   group by personnel_id)b  on s.personnel_id=b.personnel_id 
		  				where 1 =1  and s.is_deleted='N' and a.is_deleted='N'
		  <if test="personnelId !=null and personnelId !=''">
		  	and s.personnel_id = #{personnelId}
		  </if>
		  <if test="name !=null and name !=''">
		  	and s.name like concat('%',#{name},'%')
		  </if>
		  <if test="identityNumber !=null and identityNumber !=''">
		  	and s.identity_number like concat('%',#{identityNumber},'%')
		  </if>
		  <if test="objectType !=null">
		  	and s.object_type=#{objectType}
		  </if>
		   <if test="archivalNumber !=null and archivalNumber !=''">
		  and a.archival_number = #{archivalNumber}
		  </if>
		  <if test="cabinetNumber !=null and cabinetNumber !=''">
		  and a.cabinet_number = #{cabinetNumber}
		  </if>
		  <if test="boxNumber !=null and boxNumber !=''">
		  and a.box_number = #{boxNumber}
		  </if>
	  </select> -->
	  
	  <select id="getPageList" resultType="java.util.Map"  parameterType="cn.trasen.oa.civilAffairs.model.CivilArchivalInformation">
		  select a.personnel_id AS personnelId,a.id,s.name,s.identity_number AS identityNumber,a.archival_number AS archivalNumber,a.cabinet_number cabinetNumber,a.box_number boxNumber,
		<if test="req.itemNameValue != null and req.itemNameValue.size() > 0">
	      <foreach collection="req.itemNameValue" index="index"
	               item="item" open="" separator="," close="">
	        sum(CASE WHEN  b.file_type=#{item} THEN 1 else 0 END) AS #{item}
	      </foreach>
	      ,
	    </if>
	    	a.update_user_name AS updateUserName,a.update_date AS updateDate
		   from civil_affairs_personnel_info s inner join civil_archival_information a on s.personnel_id=a.personnel_id  
		   LEFT JOIN civil_archiva_files b on s.personnel_id=b.personnel_id and b.is_deleted='N'
		  				where 1 =1  and s.is_deleted='N' and a.is_deleted='N'
		  <if test="req.personnelId !=null and req.personnelId !=''">
		  	and s.personnel_id = #{req.personnelId}
		  </if>
		  <if test="req.name !=null and req.name !=''">
		  	and s.name like concat('%',#{req.name},'%')
		  </if>
		  <if test="req.identityNumber !=null and req.identityNumber !=''">
		  	and s.identity_number like concat('%',#{req.identityNumber},'%')
		  </if>
		  <if test="req.objectType !=null">
		  	and s.object_type=#{req.objectType}
		  </if>
		   <if test="req.archivalNumber !=null and req.archivalNumber !=''">
		  and a.archival_number = #{req.archivalNumber}
		  </if>
		  <if test="req.cabinetNumber !=null and req.cabinetNumber !=''">
		  and a.cabinet_number = #{req.cabinetNumber}
		  </if>
		  <if test="req.boxNumber !=null and req.boxNumber !=''">
		  and a.box_number = #{req.boxNumber}
		  </if>
		  group by  a.personnel_id,a.id,s.name,s.identity_number,a.archival_number,a.cabinet_number,a.box_number,a.update_user_name,a.update_date
	  </select>
	  
	     <select id="selectArchivalNumberMax" resultType="String" parameterType="Map">
		  select  ifnull(max(archival_number)+1,concat(date_format(NOW(),'%Y'),'0001')) as archivalNumberMax from  civil_archival_information where 1 =1  and is_deleted='N'
		  and   left(archival_number,4) = date_format(NOW(),'%Y') 
		  <if test="year !=null and year !=''">
		  	and left(archival_number,4) = #{year}
		  </if>
		 
	  </select>
	  
	  
	   <select id="getfilesTypeByPersonnelId" resultType="Map" parameterType="cn.trasen.oa.civilAffairs.model.CivilArchivalInformation">
		 SELECT  a.file_type,b.ITEM_NAME,count(1) as wjsl  FROM  civil_archiva_files  A 
				LEFT JOIN (
				   SELECT
				   A.*
				   FROM
				   COMM_DICT_ITEM A
				   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
				   WHERE
				   B.TYPE_CODE = 'civil_file_type'
				   AND B.IS_DELETED = 'N'
				   AND A.IS_DELETED = 'N'
				   AND A.IS_ENABLE = '1'
		   		   AND A.sso_org_code = #{ssoOrgCode}
			    ) b on a.file_type=b.ITEM_NAME_VALUE
				WHERE  a.IS_DELETED='N' 
		  <if test="personnelId !=null and personnelId !=''">
		  	and a.personnel_id = #{personnelId}
		  </if>
		  group by a.file_type,b.ITEM_NAME
	  </select>
	  
	   <select id="getItemNameValueList" resultType="java.lang.String" parameterType="cn.trasen.oa.civilAffairs.model.CivilArchivalInformation">
		       SELECT
			   A.ITEM_NAME_VALUE
			   FROM
			   COMM_DICT_ITEM A
			   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
			   WHERE
			   B.TYPE_CODE = 'civil_file_type'
			   AND B.IS_DELETED = 'N'
			   AND A.IS_DELETED = 'N'
			   AND A.IS_ENABLE = '1'
			   AND A.sso_org_code = #{ssoOrgCode}
	  </select>
	  
</mapper>