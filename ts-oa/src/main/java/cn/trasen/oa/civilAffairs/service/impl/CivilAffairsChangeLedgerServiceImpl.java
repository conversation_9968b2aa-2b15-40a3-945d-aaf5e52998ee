package cn.trasen.oa.civilAffairs.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.MessageInternalReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.message.ExternalMessageFeignService;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsChangeLedgerMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeLedger;
import cn.trasen.oa.civilAffairs.model.CivilAffairsChangeManage;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeLedgerService;
import cn.trasen.oa.civilAffairs.service.CivilAffairsChangeManageService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilAffairsChangeLedgerServiceImpl
 * @Description TODO
 * @date 2024��5��22�� ����4:45:50
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CivilAffairsChangeLedgerServiceImpl implements CivilAffairsChangeLedgerService {

	@Autowired
	private CivilAffairsChangeLedgerMapper mapper;

	@Autowired
	private CivilAffairsChangeManageService changeManageService;
	
	@Autowired 
	private ExternalMessageFeignService externalMessageFeignService;
	 	
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(CivilAffairsChangeLedger record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
	/*	if(StringUtils.isNotBlank(record.getType()) && "2".equals(record.getType())) { //判断是否为2支出
			updateManageChangeBalance(record);//更新对象余额
		}else {
			record.setType("1");//默认为1收入
		} */
		record.setStatus("1");//是否作废状态
		if(StringUtils.isNotBlank(record.getReviewStatus())) {
		}else {
			record.setReviewStatus("1");//默认审核状态
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CivilAffairsChangeLedger record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		/*if(StringUtils.isNotBlank(record.getType()) && "2".equals(record.getType())) {//判断是否为2支出
			updateManageChangeBalance(record);//更新对象余额
		}else {
			record.setType("1");//默认为1收入
		}*/
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CivilAffairsChangeLedger record = new CivilAffairsChangeLedger();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CivilAffairsChangeLedger selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CivilAffairsChangeLedger> getDataSetList(Page page, CivilAffairsChangeLedger record) {
		Example example = new Example(CivilAffairsChangeLedger.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CivilAffairsChangeLedger> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<CivilAffairsChangeLedger> selectPageList(Page page, CivilAffairsChangeLedger record) {
		// TODO Auto-generated method stub
		Boolean right = UserInfoHolder.getRight("CIVIL_CHANGE_MANAGE");//是否零钱管理员角色，否则只能看自己科室的数据
		if(!right){
			record.setDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<CivilAffairsChangeLedger> records = mapper.selectPageList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	//支出、新增、编辑、更新对象余额
	@Transactional(readOnly = false)
	public void updateManageChangeBalance(CivilAffairsChangeLedger record) {
		// TODO Auto-generated method stub
		CivilAffairsChangeManage civilAffairsChangeManage = new CivilAffairsChangeManage();
		civilAffairsChangeManage.setId(record.getManageId());//对象ID
		civilAffairsChangeManage.setChangeBalance(record.getBalanceAmount().subtract(record.getExpenditureAmount()));//零钱金额+余额
		changeManageService.update(civilAffairsChangeManage);
	}

	@Override
	public Map<String, Object> selectAmount(CivilAffairsChangeLedger record) {
		// TODO Auto-generated method stub
		return mapper.selectAmount(record);
	}

	@Override
	@Transactional(readOnly = false)
	public Integer batchUpdateReviewStatus(CivilAffairsChangeLedger record) {
		// TODO Auto-generated method stub
		for (String id : record.getIds()) {
			CivilAffairsChangeLedger changeLedger = mapper.selectByPrimaryKey(id);
			changeLedger.setReviewStatus("2");//审核状态
			changeLedger.setUpdateDate(new Date());
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				changeLedger.setUpdateUser(user.getUsercode());
				changeLedger.setUpdateUserName(user.getUsername());
			}
			mapper.updateByPrimaryKeySelective(changeLedger);
		}
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public void familyMessagePush(CivilAffairsChangeLedger record) {
		// TODO Auto-generated method stub
		List<Map<String,Object>> resultMapList = mapper.selectMessageData(record);//查询短信推送人员和内容
		
		for (Map<String, Object> map : resultMapList) {
			if(map.containsKey("telephone") && StringUtils.isNotBlank(map.get("telephone").toString())){//判断手机号码是否为空
				MessageInternalReq messageInternalReq = new MessageInternalReq();
				
				messageInternalReq.setMobilePhone(map.get("telephone").toString());
				messageInternalReq.setContent(record.getRegisterDateBegin()+"~"+record.getRegisterDateEnd()+ ","+map.get("name")+"零钱到账:"
				  +map.get("amountCollected")+"元,支出:"+map.get("expenditureAmount")+"元,现结存余额:"+map.get("changeBalance")+"元,请知悉"); 
				messageInternalReq.setMsgType("external");
				messageInternalReq.setIsDraft("0");
				messageInternalReq.setTiming("0");
				messageInternalReq.setSenderId("admin");
				messageInternalReq.setSenderName("admin");
				messageInternalReq.setCreateUase("admin");
				messageInternalReq.setCreateUserName("admin");
				externalMessageFeignService.sendExternalMessage(messageInternalReq);//短信推送feign
			}
		}
		
	}
}
