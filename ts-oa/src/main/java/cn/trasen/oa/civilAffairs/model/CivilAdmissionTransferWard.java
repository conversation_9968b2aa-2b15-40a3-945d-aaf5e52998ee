package cn.trasen.oa.civilAffairs.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "civil_admission_transfer_ward")
@Setter
@Getter
public class CivilAdmissionTransferWard {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 入院id
     */
    @Column(name = "admission_id")
    @ApiModelProperty(value = "入院id")
    private String admissionId;

    /**
     * 转科日期
     */
    @Column(name = "transfer_date")
    @ApiModelProperty(value = "转科日期")
    private String transferDate;

    /**
     * 变更前病区
     */
    @Column(name = "before_ward")
    @ApiModelProperty(value = "变更前病区")
    private String beforeWard;

    /**
     * 变更后病区
     */
    @Column(name = "after_ward")
    @ApiModelProperty(value = "变更后病区")
    private String afterWard;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    //查询条件
    @Transient
    @ApiModelProperty(value = "转科开始日期")
    private String transferDateBegin;
    
    @Transient
    @ApiModelProperty(value = "转科结束日期")
    private String transferDateEnd;
    
    @Transient
    @ApiModelProperty(value = "变更前病区文本")
    private String beforeWardText;
    
    @Transient
    @ApiModelProperty(value = "变更后病区文本")
    private String afterWardText;
}