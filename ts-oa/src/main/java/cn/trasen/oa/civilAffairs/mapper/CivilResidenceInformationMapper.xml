<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilResidenceInformationMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilResidenceInformation">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="personnel_id" jdbcType="VARCHAR" property="personnelId" />
    <result column="householder_name" jdbcType="VARCHAR" property="householderName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="householder_nature" jdbcType="VARCHAR" property="householderNature" />
    <result column="householder_relationship" jdbcType="VARCHAR" property="householderRelationship" />
    <result column="native_place" jdbcType="VARCHAR" property="nativePlace" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="migration_date" jdbcType="VARCHAR" property="migrationDate" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="relocation_location" jdbcType="VARCHAR" property="relocationLocation" />
    <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus" />
    <result column="agent" jdbcType="VARCHAR" property="agent" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="storage_location" jdbcType="VARCHAR" property="storageLocation" />
  </resultMap>
  
   <select id="getPageList" resultType="cn.trasen.oa.civilAffairs.model.CivilResidenceInformation" parameterType="cn.trasen.oa.civilAffairs.model.CivilResidenceInformation">
	  	
		  select s.identity_number as identityNumber,a.*,hzxz.item_name as householderNatureText,hzgx.item_name as householderRelationshipText,
		  bdyy.item_name as changeReasonText,hyzk.item_name as maritalStatusText  from civil_affairs_personnel_info s 
		  													inner join civil_residence_information a on s.personnel_id=a.personnel_id
		  													 left join (
                                                                   SELECT
                                                                   A.*
                                                                   FROM
                                                                   COMM_DICT_ITEM A
                                                                   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                                                                   WHERE
                                                                   B.TYPE_CODE = 'civil_householder_nature'
                                                                   AND B.IS_DELETED = 'N'
                                                                   AND A.IS_DELETED = 'N'
                                                                   AND A.IS_ENABLE = '1'
                                                                   AND A.sso_org_code = #{ssoOrgCode}
                                                             ) hzxz on a.householder_nature=hzxz.item_name_value
		  													 left join (
                                                                   SELECT
                                                                   A.*
                                                                   FROM
                                                                   COMM_DICT_ITEM A
                                                                   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                                                                   WHERE
                                                                   B.TYPE_CODE = 'civil_householder_relationship'
                                                                   AND B.IS_DELETED = 'N'
                                                                   AND A.IS_DELETED = 'N'
                                                                   AND A.IS_ENABLE = '1'
                                                                   AND A.sso_org_code = #{ssoOrgCode}
                                                             ) hzgx on a.householder_relationship=hzgx.item_name_value
		  													 left join (
                                                                   SELECT
                                                                   A.*
                                                                   FROM
                                                                   COMM_DICT_ITEM A
                                                                   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                                                                   WHERE
                                                                   B.TYPE_CODE = 'civil_change_reason'
                                                                   AND B.IS_DELETED = 'N'
                                                                   AND A.IS_DELETED = 'N'
                                                                   AND A.IS_ENABLE = '1'
                                                                   AND A.sso_org_code = #{ssoOrgCode}
                                                             ) bdyy on a.change_reason=bdyy.item_name_value
		  													 left join (
                                                                   SELECT
                                                                   A.*
                                                                   FROM
                                                                   COMM_DICT_ITEM A
                                                                   LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
                                                                   WHERE
                                                                   B.TYPE_CODE = 'marriage_status'
                                                                   AND B.IS_DELETED = 'N'
                                                                   AND A.IS_DELETED = 'N'
                                                                   AND A.IS_ENABLE = '1'
                                                                   AND A.sso_org_code = #{ssoOrgCode}
                                                             ) hyzk on a.marital_status=hyzk.item_name_value
		  				where 1 =1  and s.is_deleted='N' and a.is_deleted='N'
		  <if test="personnelId !=null and personnelId !=''">
		  	and s.personnel_id = #{personnelId}
		  </if>
		  <if test="name !=null and name !=''">
		  	and s.name like concat('%',#{name},'%') 
		  </if>
		  <if test="identityNumber !=null and identityNumber !=''">
		  	and s.identity_number like concat('%',#{identityNumber},'%') 
		  </if>
		  <if test="objectType !=null">
		  	and s.object_type=#{objectType}
		  </if>
		   <if test="householderName !=null and householderName !=''">
		  	and a.householder_name like concat('%',#{householderName},'%') 
		  </if>
	  </select>
</mapper>