package cn.trasen.oa.civilAffairs.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "civil_affairs_change_expenditure")
@Setter
@Getter
public class CivilAffairsChangeExpenditure {
    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 管理对象主键ID
     */
    @Column(name = "manage_id")
    @ApiModelProperty(value = "管理对象主键ID")
    private String manageId;

    /**
     * 流水号
     */
    @Column(name = "serial_number")
    @ApiModelProperty(value = "流水号")
    private String serialNumber;
    
    /**
     * 采购日期
     */
    @Column(name = "purchase_date")
    @ApiModelProperty(value = "采购日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date purchaseDate;

    /**
     * 物品主键id
     */
    @Column(name = "goods_id")
    @ApiModelProperty(value = "物品主键id")
    private String goodsId;
    
    /**
     * 物品名称
     */
    @Column(name = "goods_name")
    @ApiModelProperty(value = "物品名称")
    private String goodsName;

    /**
     * 物品名称拼音码
     */
    @Column(name = "goods_name_pym")
    @ApiModelProperty(value = "物品名称拼音码")
    private String goodsNamePym;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer number;

    /**
     * 总价
     */
    @Column(name = "total_price")
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;
    

	 /**
	* 规格型号
	*/
	@ApiModelProperty(value = "规格型号")
	private String model;
	
	
	/**
	* 供应商名称/品牌名称
	*/
	@Column(name = "supplier_name")
	@ApiModelProperty(value = "品牌")
	private String supplierName;

    /**
     * 领用人
     */
    @ApiModelProperty(value = "领用人")
    private String recipient;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态1正常,2已作废")
    private String status;
    
    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;
    
    //查询条件 采购日期开始
    @Transient
    @ApiModelProperty(value = "采购日期开始")
    private String purchaseDateBegin;
    
    //查询条件 采购日期结束
    @Transient
    @ApiModelProperty(value = "采购日期结束")
    private String purchaseDateEnd;
    
    @Transient
    @ApiModelProperty(value = "导出表头商品名称List")
    private List<String> goodsNameList;
    
    @Transient
    @ApiModelProperty(value = "接收新增商品字表数据")
    private List<CivilAffairsChangeExpenditureChild> changeExpenditureChildList;
    
    @Transient
    @ApiModelProperty(value = "所属病区名称")
    private String wardText;
    
    @Transient
    @ApiModelProperty(value = "编辑前总价")
    private BigDecimal totalPriceBefore;
    
    @Transient
    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;
    
    @Transient
    @ApiModelProperty(value = "姓名")
    private String name;
    
    @Transient//查询条件
    @ApiModelProperty(value = "科室")
    private String deptCode;
    
    @Transient
    @ApiModelProperty(value = "附件名称")
    private String originalName;
    
    @Transient
    @ApiModelProperty(value = "附件路径")
    private String filePath;
    
    @Transient
    @ApiModelProperty(value = "病区")
    private String ward;
    
    @ApiModelProperty(value = "备注")
    private String remarks;
    
}