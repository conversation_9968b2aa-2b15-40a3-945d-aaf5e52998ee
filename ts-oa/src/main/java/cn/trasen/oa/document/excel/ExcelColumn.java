package cn.trasen.oa.document.excel;


import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.lang.annotation.*;
import java.lang.reflect.Field;

/**
 * @Description: Excel导出帮助类
 * @Date: 2020/2/25 09:23
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface ExcelColumn {
	
	String value() default "";//Excel标题
	
	int col() default 0;//Excel从左往右排列位置
	
    String name() default "";//列名

    int index() default 0;//列的顺序

    short width() default 10;//列宽

    String di() default "";//excel定位，例如A2,A10,B2,B10

    HorizontalAlignment leftAndRight() default HorizontalAlignment.CENTER;//字体默认靠左

    //注解内部类 列的配置(主表)
    class ExcellColumnConfig {
        private int index;//列顺序
        private short width;//列宽
        private String columnName;//列名
        private Field field;//对应属性
        private String di;//定位

        /**
         * @Title: getIndex <BR>
         * @Description: <BR>
         * @return: int <BR>
         */
        public int getIndex() {
            return index;
        }

        /**
         * @Title: setIndex <BR>
         * @Description: <BR>
         * @return: int <BR>
         */
        public void setIndex(int index) {
            this.index = index;
        }

        /**
         * @Title: getWidth <BR>
         * @Description: <BR>
         * @return: short <BR>
         */
        public short getWidth() {
            return width;
        }

        /**
         * @Title: setWidth <BR>
         * @Description: <BR>
         * @return: short <BR>
         */
        public void setWidth(short width) {
            this.width = width;
        }

        /**
         * @Title: getColumnName <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public String getColumnName() {
            return columnName;
        }

        /**
         * @Title: setColumnName <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }

        /**
         * @Title: getField <BR>
         * @Description: <BR>
         * @return: Field <BR>
         */
        public Field getField() {
            return field;
        }

        /**
         * @Title: setField <BR>
         * @Description: <BR>
         * @return: Field <BR>
         */
        public void setField(Field field) {
            this.field = field;
        }

        /**
         * @Title: getDi <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public String getDi() {
            return di;
        }

        /**
         * @Title: setDi <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public void setDi(String di) {
            this.di = di;
        }


    }

    //注解内部类 列的配置(从表)
    class ExcellColumnConfigSlave {
        private int index;//列顺序
        private short width;//列宽
        private String columnName;//列名
        private Field field;//对应属性
        private HorizontalAlignment leftAndRight;//字体定位

        /**
         * @Title: getIndex <BR>
         * @Description: <BR>
         * @return: int <BR>
         */
        public int getIndex() {
            return index;
        }

        /**
         * @Title: setIndex <BR>
         * @Description: <BR>
         * @return: int <BR>
         */
        public void setIndex(int index) {
            this.index = index;
        }

        /**
         * @Title: getWidth <BR>
         * @Description: <BR>
         * @return: short <BR>
         */
        public short getWidth() {
            return width;
        }

        /**
         * @Title: setWidth <BR>
         * @Description: <BR>
         * @return: short <BR>
         */
        public void setWidth(short width) {
            this.width = width;
        }

        /**
         * @Title: getColumnName <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public String getColumnName() {
            return columnName;
        }

        /**
         * @Title: setColumnName <BR>
         * @Description: <BR>
         * @return: String <BR>
         */
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }

        /**
         * @Title: getField <BR>
         * @Description: <BR>
         * @return: Field <BR>
         */
        public Field getField() {
            return field;
        }

        /**
         * @Title: setField <BR>
         * @Description: <BR>
         * @return: Field <BR>
         */
        public void setField(Field field) {
            this.field = field;
        }

        /**
         * @Title: getLeftAndRight <BR>
         * @Description: <BR>
         * @return: HorizontalAlignment <BR>
         */
        public HorizontalAlignment getLeftAndRight() {
            return leftAndRight;
        }

        /**
         * @Title: setLeftAndRight <BR>
         * @Description: <BR>
         * @return: HorizontalAlignment <BR>
         */
        public void setLeftAndRight(HorizontalAlignment leftAndRight) {
            this.leftAndRight = leftAndRight;
        }

    }
}
