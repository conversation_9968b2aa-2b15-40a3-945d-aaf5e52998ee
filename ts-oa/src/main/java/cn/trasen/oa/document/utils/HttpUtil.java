package cn.trasen.oa.document.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.trasen.homs.core.utils.WebUtils;

/**
 * @ClassName: HttpUtil
 * @Description: TODO
 * @Author: hezz#trasen.cn
 * @Date: 2020/7/31 17:15
 */
public class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * @Title: post
     * @Description: post方法
     * @param url
     * @param data
     * @return
     * @date 2018年6月13日 下午2:08:32
     * <AUTHOR>
     */
    public static String post(String url, String data) {
        return post(url, data, null);
    }

    /**
     * @Title: post
     * @Description: post map
     * @param url
     * @param data
     * @return
     * @date 2018年10月10日 下午5:41:05
     * <AUTHOR>
     */
    public static String post(String url, Map<String, Object> data) {
        InputStream is = null;
        BufferedReader bf = null;
        CloseableHttpClient client = null;
        String body = "";
        try {
            // 新cookieStore
            client = HttpClients.custom().setDefaultCookieStore(makeCookie(url)).build();
            HttpPost httPost = new HttpPost(url);
            // 参数
            List<NameValuePair> paramList = new ArrayList<>();
            String token = WebUtils.getRequest().getParameter("token");
            if(StringUtils.isNotBlank(token)){
                data.put("token", token);
            }
            if (data != null && data.size() > 0) {
                Set<String> keySet = data.keySet();
                for (String key : keySet) {
                    if(data.get(key) != null){
                        paramList.add(new BasicNameValuePair(key, data.get(key).toString()));
                    }
                }
                httPost.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));
            }
            HttpResponse response = client.execute(httPost);
            if (response != null && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                is = response.getEntity().getContent();
                bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                StringBuffer buffer = new StringBuffer();
                String line = "";
                while ((line = bf.readLine()) != null) {
                    buffer.append(line);
                }
                body = buffer.toString();
                log.debug("post result: {}", body);
            } else {
                log.error("post url:{}, error code: {}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(bf);
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
        }
        return body;
    }

    /**
     * @Title: post
     * @Description: post方法
     * @param url
     * @param data
     * @param cookieStore
     * @return
     * @date 2018年6月7日 下午4:16:11
     * <AUTHOR>
     */
    public static String post(String url, String data, CookieStore cookieStore) {
        InputStream is = null;
        BufferedReader bf = null;
        CloseableHttpClient client = null;
        String body = "";
        try {
            // 新cookieStore
            if(cookieStore == null) {
                client = HttpClients.custom().setDefaultCookieStore(makeCookie(url)).build();
            } else {
                client = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            }
            HttpPost httPost = new HttpPost(url);
            // 参数
            StringEntity entity = new StringEntity(data, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httPost.setEntity(entity);
            HttpResponse response = client.execute(httPost);
            if (response != null && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                is = response.getEntity().getContent();
                bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                StringBuffer buffer = new StringBuffer();
                String line = "";
                while ((line = bf.readLine()) != null) {
                    buffer.append(line);
                }
                body = buffer.toString();
                log.debug("post result: {}", body);
            } else {
                log.error("post url:{}, error code: {}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(bf);
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
        }
        return body;
    }

    /**
     * @Title: get
     * @Description: get方法
     * @param url
     * @return
     * @date 2018年6月13日 下午2:10:20
     * <AUTHOR>
     */
    public static String get(String url) {
        return get(url, null);
    }

    /**
     * @Title: get
     * @Description: get方法
     * @param url
     * @param cookieStore
     * @return
     * @date 2018年6月7日 下午4:16:11
     * <AUTHOR>
     */
    public static String get(String url, CookieStore cookieStore) {
        InputStream is = null;
        BufferedReader bf = null;
        String body = "";
        CloseableHttpClient client = null;
        try {
            // 新增cookieStore
            if(cookieStore == null) {
                client = HttpClients.custom().setDefaultCookieStore(makeCookie(url)).build();
            } else {
                client = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            }
            String token = WebUtils.getRequest().getParameter("token");
            if(StringUtils.isNotBlank(token)){
                if(url.contains("?")){
                    url+="&token="+token;
                }else{
                    url+="?token="+token;
                }
            }
            HttpGet httpGet = new HttpGet(url);
            HttpResponse response = client.execute(httpGet);
            if (response != null && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                is = response.getEntity().getContent();
                bf = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                StringBuffer buffer = new StringBuffer();
                String line = "";
                while ((line = bf.readLine()) != null) {
                    buffer.append(line);
                }
                body = buffer.toString();
            } else {
                log.error("get url:{}, error code: {}", url, response.getStatusLine().getStatusCode());
            }
            log.debug("get result: {}", body);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(bf);
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                }
            }
        }
        return body;
    }

    /**
     *
     * @Title: makeCookie
     * @Description: 设置cookie
     * @param url
     * @return
     * @date 2018年6月7日 下午4:15:44
     * <AUTHOR>
     */
    public static CookieStore makeCookie(String url) {
        CookieStore cookieStore = new BasicCookieStore();
        BasicClientCookie basicClientCookie = null;
        // 判断request是否为空
        HttpServletRequest request = WebUtils.getRequest();
        System.out.println(request);

        if(request != null) {
            Cookie[] cookies = request.getCookies();
            if (null != cookies) {
                for (Cookie cookie : cookies) {
                    basicClientCookie = new BasicClientCookie(cookie.getName(), cookie.getValue());
                    basicClientCookie.setVersion(0);
                    basicClientCookie.setDomain(url.split(":")[1].replace("//", ""));
                    basicClientCookie.setPath("/");
                    cookieStore.addCookie(basicClientCookie);
                }
            }
        }
        return cookieStore;
    }

	public static String getPreviewURL(String url) {
		try {
            HttpClient client = HttpClientBuilder.create().build();
            //发送get请求
            HttpGet request = new HttpGet(url);
            HttpResponse response = client.execute(request);

            /**请求发送成功，并得到响应**/
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                /**读取服务器返回过来的json字符串数据**/
                String strResult = EntityUtils.toString(response.getEntity());

                return strResult;
            }
        }
        catch (IOException e) {
            e.printStackTrace();
        }

        return null;
	}
}
