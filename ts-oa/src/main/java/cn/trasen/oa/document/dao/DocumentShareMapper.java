package cn.trasen.oa.document.dao;

import java.util.List;

import cn.trasen.homs.bean.hrms.HrmsOrganization;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.document.model.Document;
import cn.trasen.oa.document.model.DocumentShare;
import tk.mybatis.mapper.common.Mapper;

public interface DocumentShareMapper extends Mapper<DocumentShare> {

    /**
     * 查询科室文档分享列表数据
     *
     * @param document
     * @param page
     * @return
     */
    List<Document> findDocumentShareList(Document document, Page page);
    
    List<HrmsOrganization> getHrmsOrganizationList(String ssoOrgCode);

}
