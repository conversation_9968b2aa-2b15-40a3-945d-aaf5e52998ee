package cn.trasen.oa.document.po;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: Excel导入外部档案信息扩展类
 * @Date: 2020/3/24 14:14
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Setter
@Getter
public class DossierPo {

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Excel(name = "标题")
    private String dossierTitle;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    @Excel(name = "文号")
    private String dossierFileNum;

    /**
     * 文件年度
     */
    @ApiModelProperty(value = "文件年度")
    @Excel(name = "文件年度")
    private String dossierYear;

    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型")
    @Excel(name = "文档类型")
    private String dossierFromFileType;

    /**
     * 归档机构
     */
    @ApiModelProperty(value = "归档机构")
    @Excel(name = "归档机构")
    private String filingAgency;

    /**
     * 保存期限(1:永久,2:长期,3:短期)
     */
    @ApiModelProperty(value = "保存期限(1:永久;2:长期;3:短期)")
    @Excel(name = "保存期限(1:永久;2:长期;3:短期)")
    private String dossierKeepLimit;

    /**
     * 关联文号
     */
    @ApiModelProperty(value = "关联文号")
    @Excel(name = "关联文号")
    private String dossierAssofileNum;

    /**
     * 件号
     */
    @ApiModelProperty(value = "件号")
    @Excel(name = "件号")
    private String dossierDocNum;

    /**
     * 责任者
     */
    @ApiModelProperty(value = "责任者")
    @Excel(name = "责任者")
    private String dossierDuty;

    /**
     * 是否归档(0:未归档;1:已归档)
     */
    @ApiModelProperty(value = "是否归档(0:未归档;1:已归档)")
    @Excel(name = "是否归档(0:未归档;1:已归档)")
    private String dossierStatus;

}
