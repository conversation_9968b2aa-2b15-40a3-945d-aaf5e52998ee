package cn.trasen.oa.im.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "user_message")
@Setter
@Getter
public class UserMessage {
	
	@Id
    private String id;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String senduser;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人")
    private String receiveuser;

    /**
     * 群ID
     */
    @ApiModelProperty(value = "群ID")
    private String groupid;

    /**
     * 是否已读 0 未读  1 已读
     */
    @ApiModelProperty(value = "是否已读 0 未读  1 已读")
    private Integer isread;

    /**
     * 类型 0 单聊消息  1 群消息
     */
    @ApiModelProperty(value = "类型 0 单聊消息  1 群消息")
    private Integer type;

    private String createuser;

    private Date createdate;

    private Date updatedate;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String content;
    
    
    @ApiModelProperty(value = "删除标识")
    private String isdeleted;
}