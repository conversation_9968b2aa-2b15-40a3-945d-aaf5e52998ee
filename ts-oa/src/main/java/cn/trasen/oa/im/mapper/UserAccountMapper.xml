<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.im.dao.UserAccountMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.im.model.UserAccount">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="disablestate" jdbcType="INTEGER" property="disablestate" />
    <result column="isdel" jdbcType="INTEGER" property="isdel" />
    <result column="createdate" jdbcType="TIMESTAMP" property="createdate" />
    <result column="updatedate" jdbcType="TIMESTAMP" property="updatedate" />
    <result column="updateuser" jdbcType="VARCHAR" property="updateuser" />
  </resultMap>
  
  <insert id="bacthInsertOrUpdate" parameterType="java.util.List">
  	insert into user_account (id,account,password,disablestate,isdel,createdate)
      values
       <foreach collection="userAccountList" item="userAccount" separator=",">
          (
          #{userAccount.id},
          #{userAccount.account},
          #{userAccount.password},
          #{userAccount.disablestate},
          #{userAccount.isdel},
          #{userAccount.createdate}
          )
       </foreach> 
       on duplicate key update
       account = values(account),
       disablestate = values(disablestate),
       isdel = values(isdel)
  </insert>
  
</mapper>