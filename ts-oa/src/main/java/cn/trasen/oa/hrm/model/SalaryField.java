package cn.trasen.oa.hrm.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_salary_field")
@Setter
@Getter
public class SalaryField {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 字段名称
     */
    @Column(name = "field_name")
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段描述
     */
    @Column(name = "field_title")
    @ApiModelProperty(value = "字段描述")
    private String fieldTitle;

    /**
     * 所在表ID
     */
    @Column(name = "field_table_id")
    @ApiModelProperty(value = "所在表ID")
    private String fieldTableId;

    /**
     * 字段所在表
     */
    @Column(name = "field_table_name")
    @ApiModelProperty(value = "字段所在表")
    private String fieldTableName;
    
    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_name")
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    
    /**
     * 排序
     */
    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private int sort;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}