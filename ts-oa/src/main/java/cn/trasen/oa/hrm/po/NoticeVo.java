package cn.trasen.oa.hrm.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NoticeVo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	  /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 通知类型   1 短信   2邮件   3微信推送
     */
    @ApiModelProperty(value = "通知类型   1 短信   2邮件   3微信推送")
    private String noticeType;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;

    /**
     * 发送人名称
     */
    @ApiModelProperty(value = "发送人名称")
    private String senderName;
    
    /**
     * 接收人,多个用逗号隔开,发送所有人传all
     */
    @ApiModelProperty(value = "接收人,多个用逗号隔开,发送所有人传all")
    private String receiver;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String subject;

    /**
     * 微信推送类型 暂时只支持 1卡片消息 2文本消息  
     */
    @ApiModelProperty(value = "微信推送类型 暂时只支持 1卡片消息 2文本消息  ")
    private String wxSendType;

    /**
     * 卡片消息跳转URL
     */
    @ApiModelProperty(value = "卡片消息跳转URL")
    private String url;

    /**
     * 提醒内容
     */
    @ApiModelProperty(value = "提醒内容")
    private String content;

    /**
     * 返回结果
     */
    @ApiModelProperty(value = "返回结果")
    private String result;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Transient
    private String token;
    
    @Transient
    private String toUrl = "/index";  //提示消息点击要跳转的url

}
