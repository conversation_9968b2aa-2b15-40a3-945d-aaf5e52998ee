package cn.trasen.oa.hrm.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.oa.hrm.model.AttendanceReport;
import cn.trasen.oa.hrm.po.JdGridTableEntity;
import cn.trasen.oa.hrm.po.StepFrom;

/**
 * @ClassName AttendanceReportService
 * @Description TODO
 * @date 2023��2��2�� ����11:32:03
 * <AUTHOR>
 * @version 1.0
 */
public interface AttendanceReportService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��2�� ����11:32:03
	 * <AUTHOR>
	 */
	void save(Map<String, Object> record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��2�� ����11:32:03
	 * <AUTHOR>
	 */
	void update(Map<String, Object> record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��2�� ����11:32:03
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return AttendanceReport
	 * @date 2023��2��2�� ����11:32:03
	 * <AUTHOR>
	 */
	AttendanceReport selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<AttendanceReport>
	 * @date 2023��2��2�� ����11:32:03
	 * <AUTHOR>
	 */
	List<Map<String,String>> getDataSetList(Page page, AttendanceReport record);

	/**
	 * 
	 * @MethodName: getTableHeadCols
	 * @Description: TODO
	 * <AUTHOR>
	 * @return List<JdGridTableEntity>
	 * @date 2023-02-02 11:34:46
	 */
	List<JdGridTableEntity> getTableHeadCols(String attendanceManage);

	/**
	 * 
	 * @MethodName: importTemplateData
	 * @Description: TODO
	 * <AUTHOR>
	 * @param list
	 * @param attendanceReport
	 * @return String
	 * @date 2023-02-04 10:03:17
	 */
	String importTemplateData(List<List<String>> list,AttendanceReport attendanceReport);

	/**
	 * 
	 * @MethodName: submitApproval
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-02-04 10:03:07
	 */
	void submitApproval(AttendanceReport record);

	/**
	 * 
	 * @MethodName: selecAllTreeNode
	 * @Description: TODO
	 * <AUTHOR>
	 * @param areaId
	 * @return List<TreeModel>
	 * @date 2023-02-04 02:28:55
	 */
	List<TreeModel> selecAllTreeNode(String reportDate,String attendanceManage);

	/**
	 * 
	 * @MethodName: selectApprovalData
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-02-04 03:42:14
	 */
	List<Map<String, String>> selectApprovalData(AttendanceReport record);

	/**
	 * 
	 * @MethodName: cancelApproval
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-02-04 03:50:46
	 */
	void cancelApproval(AttendanceReport record);

	/**
	 * 
	 * @MethodName: completeTask
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-02-04 04:06:42
	 */
	void completeTask(StepFrom record);

	/**
	 * 
	 * @MethodName: finishExamine
	 * @Description: TODO
	 * <AUTHOR>
	 * @param transferRecordId void
	 * @date 2023-02-04 04:23:36
	 */
	void finishExamine(String transferRecordId);

	/**
	 * 
	 * @MethodName: isReport
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return Boolean
	 * @date 2023-02-09 09:49:52
	 */
	String getReportStatus(AttendanceReport record);

	void updateStatus();

	/**
	 * 
	 * @MethodName: attendanceReportDetails
	 * @Description: TODO
	 * <AUTHOR>
	 * @return List<Map<String,Object>>
	 * @date 2023-03-10 11:44:19
	 */
	List<Map<String, Object>> attendanceReportDetails(AttendanceReport record);

	/**
	 * 
	 * @MethodName: batchDelete
	 * @Description: TODO
	 * <AUTHOR>
	 * @param ids void
	 * @date 2023-06-15 03:09:57
	 */
	void batchDelete(String ids);
}
