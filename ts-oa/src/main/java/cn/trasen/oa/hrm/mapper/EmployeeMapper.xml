<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.hrm.dao.EmployeeMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.oa.hrm.model.Employee">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="EMP_PAYROLL" jdbcType="VARCHAR" property="empPayroll"/>
        <result column="EMP_DEPT_ID" jdbcType="VARCHAR" property="empDeptId"/>
        <result column="EMP_DEPT_NAME" jdbcType="VARCHAR" property="empDeptName"/>
        <result column="EMP_DEPT_CODE" jdbcType="VARCHAR" property="empDeptCode"/>
        <result column="EMP_NAME" jdbcType="VARCHAR" property="empName"/>
        <result column="EMP_NAME_PINYIN" jdbcType="VARCHAR" property="empNamePinyin"/>
        <result column="EMP_CODE" jdbcType="VARCHAR" property="empCode"/>
        <result column="EMP_PASSWORD" jdbcType="VARCHAR" property="empPassword"/>
        <result column="EMP_NICK_NAME" jdbcType="VARCHAR" property="empNickName"/>
        <result column="EMP_SEX" jdbcType="DECIMAL" property="empSex"/>
        <result column="EMP_PHONE" jdbcType="VARCHAR" property="empPhone"/>
        <result column="EMP_PHONE_SECOND" jdbcType="VARCHAR" property="empPhoneSecond"/>
        <result column="EMP_BUSINESS_PHONE" jdbcType="VARCHAR" property="empBusinessPhone"/>
        <result column="EMP_TELECOM_BUSINESS_PHONE" jdbcType="VARCHAR" property="empTelecomBusinessPhone"/>
        <result column="EMP_UNICOM_BUSINESS_PHONE" jdbcType="VARCHAR" property="empUnicomBusinessPhone"/>
        <result column="EMP_SHORT_PHONE" jdbcType="VARCHAR" property="empShortPhone"/>
        <result column="EMP_HEAD_IMG" jdbcType="VARCHAR" property="empHeadImg"/>
        <result column="EMP_SIGNIMG" jdbcType="VARCHAR" property="empSignimg"/>
        <result column="SIGNATURE_IMG_NAME" jdbcType="VARCHAR" property="signatureImgName"/>
        <result column="SIGNATURE_IMGSAVE_NAME" jdbcType="VARCHAR" property="signatureImgsaveName"/>
        <result column="EMP_HIREDATE" jdbcType="TIMESTAMP" property="empHiredate"/>
        <result column="EMP_FIREDATE" jdbcType="TIMESTAMP" property="empFiredate"/>
        <result column="FIRE_REASON" jdbcType="VARCHAR" property="fireReason"/>
        <result column="EMP_IDCARD" jdbcType="VARCHAR" property="empIdcard"/>
        <result column="EMP_BIRTH" jdbcType="TIMESTAMP" property="empBirth"/>
        <result column="EMP_AGE" jdbcType="DECIMAL" property="empAge"/>
        <result column="EMP_STATUS" jdbcType="DECIMAL" property="empStatus"/>
        <result column="EMP_PHOTO" jdbcType="VARCHAR" property="empPhoto"/>
        <result column="EMP_NATION" jdbcType="VARCHAR" property="empNation"/>
        <result column="EMP_NATIVEPLACE" jdbcType="VARCHAR" property="empNativeplace"/>
        <result column="EMP_IS_MARRIAGE" jdbcType="DECIMAL" property="empIsMarriage"/>
        <result column="EMP_POLITY" jdbcType="VARCHAR" property="empPolity"/>
        <result column="EMP_ADDRESS" jdbcType="VARCHAR" property="empAddress"/>
        <result column="EMP_CARD_ADDRESS" jdbcType="VARCHAR" property="empCardAddress"/>
        <result column="EMP_EMAIL" jdbcType="VARCHAR" property="empEmail"/>
        <result column="EMP_DUTY_ID" jdbcType="VARCHAR" property="empDutyId"/>
        <result column="EMP_DUTY_NAME" jdbcType="VARCHAR" property="empDutyName"/>
        <result column="EMP_TITLE_ID" jdbcType="VARCHAR" property="empTitleId"/>
        <result column="EMP_TITLE_NAME" jdbcType="VARCHAR" property="empTitleName"/>
        <result column="EMP_TYPE" jdbcType="VARCHAR" property="empType"/>
        <result column="USER_ACCOUNTS" jdbcType="VARCHAR" property="userAccounts"/>
        <result column="USER_SIMPLE_NAME" jdbcType="VARCHAR" property="userSimpleName"/>
        <result column="KEY_VALIDATE" jdbcType="DECIMAL" property="keyValidate"/>
        <result column="KEY_SERIAL" jdbcType="VARCHAR" property="keySerial"/>
        <result column="IS_AD_CHECK" jdbcType="VARCHAR" property="isAdCheck"/>
        <result column="USER_IS_SLEEP" jdbcType="VARCHAR" property="userIsSleep"/>
        <result column="USER_IS_DELETED" jdbcType="VARCHAR" property="userIsDeleted"/>
        <result column="IS_SMS_REMINDER" jdbcType="DECIMAL" property="isSmsReminder"/>
        <result column="IS_VOICE_REMINDER" jdbcType="DECIMAL" property="isVoiceReminder"/>
        <result column="IS_WX_REMINDER" jdbcType="DECIMAL" property="isWxReminder"/>
        <result column="IS_DISPLAY_PHONE_NO" jdbcType="DECIMAL" property="isDisplayPhoneNo"/>
        <result column="IS_USE_SIGNATURE" jdbcType="DECIMAL" property="isUseSignature"/>
        <result column="IS_BIRTHDAY_PROTECT" jdbcType="DECIMAL" property="isBirthdayProtect"/>
        <result column="USER_ISACTIVE" jdbcType="DECIMAL" property="userIsactive"/>
        <result column="USER_ISFORMALUSER" jdbcType="DECIMAL" property="userIsformaluser"/>
        <result column="USER_ISSUPER" jdbcType="DECIMAL" property="userIssuper"/>
        <result column="USER_SUPER_BEGIN" jdbcType="TIMESTAMP" property="userSuperBegin"/>
        <result column="USER_SUPER_END" jdbcType="TIMESTAMP" property="userSuperEnd"/>
        <result column="BROWSERANGE" jdbcType="VARCHAR" property="browserange"/>
        <result column="BROWSERANGE_NAME" jdbcType="VARCHAR" property="browserangeName"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted"/>
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode"/>
        <result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode"/>
        <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="USER_IS_DELETED" jdbcType="VARCHAR" property="userIsDeleted"/>
        <result column="EMP_NAME_PINYIN" jdbcType="VARCHAR" property="empNamePinyin"/>
        <result column="OPEN_ID" jdbcType="VARCHAR" property="openId"/>
        <result column="YEAR_WORK" jdbcType="VARCHAR" property="yearWork"/>
        <result column="YEAR_NUMBER" jdbcType="VARCHAR" property="yearNumber"/>
        <result column="YEAR_DAYS" jdbcType="VARCHAR" property="yearDays"/>
        <result column="UPLOAD_FILE_SIZE" jdbcType="VARCHAR" property="uploadFileSize"/>
        <result column="agent_id" jdbcType="VARCHAR" property="agentId"/>
        <result column="agent_name" jdbcType="VARCHAR" property="agentName"/>
        <result column="is_enable_process_agent" jdbcType="DECIMAL" property="isEnableProcessAgent"/>
        <result column="agent_start_time" jdbcType="TIMESTAMP" property="agentStartTime"/>
        <result column="agent_end_time" jdbcType="TIMESTAMP" property="agentEndTime"/>
    </resultMap>

    <!--查询员工信息列表-->
    <select id="selectToaEmployeeList" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
        SELECT
			a.employee_id ID,
			a.EMP_PAYROLL,
			a.org_id EMP_DEPT_ID,
			<if test="orgUserCodeList != null and orgUserCodeList.size() > 0">
	         		org.org_name as empDeptName,
			</if>
			<if test="orgUserCodeList == null">
				c.name as empDeptName,
			</if>
			c.code EMP_DEPT_CODE,
			a.employee_name EMP_NAME,
			a.employee_no EMP_CODE,
			a.gender EMP_SEX,
			a.phone_number EMP_PHONE,
			a.emp_business_phone EMP_BUSINESS_PHONE,
			a.avatar EMP_HEAD_IMG,
			a.emp_signimg EMP_SIGNIMG,
			a.SIGNATURE_IMG_NAME,
			a.entry_date EMP_HIREDATE,
			i.quit_date EMP_FIREDATE,
			a.identity_number EMP_IDCARD,
			a.birthday EMP_BIRTH,
			a.emp_age EMP_AGE,
			a.employee_status EMP_STATUS,
			a.email EMP_EMAIL,
			position.position_id EMP_DUTY_ID,
			position.position_name EMP_DUTY_NAME,
			a.employee_no USER_ACCOUNTS,
			a.IS_SMS_REMINDER,
			a.IS_VOICE_REMINDER,
			a.IS_WX_REMINDER,
			a.IS_DISPLAY_PHONE_NO,
			a.IS_USE_SIGNATURE,
			a.CREATE_USER,
			a.CREATE_USER_NAME,
			a.CREATE_DATE,
			a.UPDATE_USER,
			a.UPDATE_USER_NAME,
			a.UPDATE_DATE,
			a.IS_DELETED,
			a.name_spell EMP_NAME_PINYIN,
			a.YEAR_WORK,
			a.YEAR_NUMBER,
			a.YEAR_DAYS,
			o.option_name,
			i.plgw,
			i.gwdj,
			i.salary_level_id as salaryLevel,
			a.employee_status,
			a1.item_name AS postName,
			i.technical,
			a4.education_type_name as emp_study_experience
		FROM cust_emp_base a
		left join cust_emp_info i on a.employee_id = i.info_id
		left join comm_organization c on c.organization_id = a.org_id
		left join comm_position position on a.position_id = position.position_id
		left join hrms_newsalary_option_emp m on a.employee_id = m.employee_id and m.is_deleted = 'N'
		left join hrms_newsalary_option o on m.option_id = o.id and o.is_deleted = 'N'
		LEFT JOIN (
			SELECT
			a.*
			FROM
			comm_dict_item a
			LEFT JOIN comm_dict_type b ON a.DIC_TYPE_ID = b.ID
			WHERE
			b.TYPE_CODE = 'personal_identity'
			AND b.IS_DELETED = 'N'
			AND a.IS_DELETED = 'N'
			AND a.SSO_ORG_CODE = #{ssoOrgCode}
		) a1 ON a.personal_identity = a1.item_code

		left join ( SELECT  ed.employee_id,MIN(ed.highest_level) highest_level,  max(dict.ITEM_NAME) as education_type_name
		FROM hrms_education_info ed
		left join (
			SELECT
			a.*
			FROM
			comm_dict_item a
			LEFT JOIN comm_dict_type b ON a.DIC_TYPE_ID = b.ID
			WHERE
			b.TYPE_CODE = 'education_type'
			AND b.IS_DELETED = 'N'
			AND a.IS_DELETED = 'N'
			AND a.SSO_ORG_CODE = #{ssoOrgCode}
		) dict on ed.education_type =  dict.item_code
		WHERE ed.is_deleted = 'N' GROUP BY      ed.employee_id )a4 on a4.employee_id = a.employee_id

		<if test="orgUserCodeList != null and orgUserCodeList.size() > 0">
        	left join ts_thps.thps_org org on org.org_code = a.sso_org_code 
        </if>
        
        WHERE a.IS_DELETED = 'N' and a.is_enable = '1' and a.employee_status IS NOT NULL and a.employee_status in ('1','5','6','9','11','12','88','99')
        
        <if test="orgUserCodeList != null and orgUserCodeList.size() > 0">
        	 and a.employee_no in
	        <foreach collection="orgUserCodeList" index="index" item="item" open="(" separator="," close=")">
	            #{item,jdbcType=VARCHAR}
	        </foreach>
        </if>
        <if test="optionId !=null and optionId != ''">
            and o.id = #{optionId}
        </if>
        <if test="postLevelId != null and postLevelId != ''">
            and i.gwdj = #{postLevelId}
        </if>
        <if test="salaryLevelId != null and salaryLevelId != ''">
            and i.salary_level_id = #{salaryLevelId}
        </if>
        <if test="orgUserCodeList == null or orgUserCodeList.size() == 0">
        	and a.sso_org_code = #{ssoOrgCode}
        </if>
        
        <if test="empName != null and empName != '' and empName.indexOf(',') >= 0">
             AND find_in_set(a.employee_name, #{empName})
        </if>
        <if test="empName != null and empName != '' and empName.indexOf(',') == -1">
            and (
            (a.employee_name LIKE CONCAT('%',#{empName},'%'))
            or (a.employee_no LIKE CONCAT('%',#{empName},'%'))
            )
        </if>
        
        <if test="seachKey != null and seachKey != ''">
            and (
            (a.employee_name LIKE CONCAT('%',#{seachKey},'%'))
            or (a.employee_no LIKE CONCAT('%',#{seachKey},'%'))
            or (c.name LIKE CONCAT(CONCAT('%', #{seachKey}), '%'))
            )
        </if>
        
        <if test="userAccounts != null and userAccounts != ''">
            AND a.employee_no like  CONCAT(CONCAT('%', #{userAccounts}), '%')
        </if>
        <if test="empDeptName != null and empDeptName != '' and orgUserCodeList == null">
            AND c.name like  CONCAT(CONCAT('%', #{empDeptName}), '%')
        </if>
        <if test="empDeptName != null and empDeptName != '' and orgUserCodeList != null">
            AND org.org_name like  CONCAT(CONCAT('%', #{empDeptName}), '%')
        </if>
        <if test="empDeptCode != null and empDeptCode != ''">
            AND c.code = #{empDeptCode}
        </if>
        <if test="deptCodeSeach != null and deptCodeSeach != '' and deptChildsList == null">
            AND c.code = #{deptCodeSeach}
        </if>
        <if test="empDeptId != null and empDeptId != ''">
            AND c.code = #{empDeptId,jdbcType=VARCHAR}
        </if>
        <if test="moveIn != null and moveIn != ''">
            AND c.organization_id = #{moveIn,jdbcType=VARCHAR}
        </if>
        <if test="userSel == null and empCode != null and empCode != ''">
            AND a.employee_no = #{empCode}
        </if>
        <if test="userSel != null and empCode != null and empCode != ''">
            AND a.employee_no like CONCAT(CONCAT('%', #{empCode}), '%')
        </if>
        <if test="empSex != null">
            AND a.gender = #{empSex}
        </if>
        <if test="empDutyId != null and empDutyId !=''">
            AND position.position_id = #{empDutyId}
        </if>
        <if test="empHiredate != null">
            AND date_format( a.entry_date, '%Y-%m-%d' ) = date_format( #{empHiredate} , '%Y-%m-%d' )
        </if>
        <if test="empNamePinyin != null and empNamePinyin !=''">
            AND a.name_spell like  CONCAT(CONCAT('%', #{empNamePinyin}), '%')
        </if>
        <if test="empStatus != null and empStatus !=''">
            AND a.employee_status = #{empStatus}
        </if>
        <if test="empPayroll != null and empPayroll !=''">
            AND a.EMP_PAYROLL like CONCAT(CONCAT('%', #{empPayroll}), '%')
        </if>
        <!-- 经开医院添加人员类别查询 ORG_ATTRIBUTES -->
        <if test="orgAttributes != null and orgAttributes !=''">
       	 	AND a.org_attributes = #{orgAttributes}
        </if>
        <if test="postName != null and postName !=''">
       	 	AND a1.item_name like CONCAT(CONCAT('%', #{postName}), '%')
        </if>
        <if test="deptChildsList != null and deptChildsList.size() > 0">
        	AND a.org_id in
	        <foreach collection="deptChildsList" index="index" item="dId" open="(" separator="," close=")">
	            #{dId,jdbcType=VARCHAR}
	        </foreach>
        </if>
        <if test="sidx != null and sidx != '' ">
				order by ${sidx}  ${sord}
		</if>
		<if test="sidx == null">
        	order by a.create_date desc
		</if>
     <!--    ORDER BY a.EMP_CODE -->
    </select>

    <select id="selectByUserCode" resultType="cn.trasen.oa.hrm.model.Employee">
       SELECT
			t1.employee_id ID,
			t1.phone_number EMP_PHONE,
			t1.employee_no EMP_CODE,
			t1.employee_name  EMP_NAME,
			t1.gender EMP_SEX,
			t1.avatar EMP_HEAD_IMG,
			org.`name` EMP_DEPT_NAME,
			t3.position_name EMP_DUTY_NAME,
			OPEN_ID,
			t1.email EMP_EMAIL 
		FROM
			cust_emp_base t1
			left join comm_organization org on t1.org_id = org.organization_id
			left join comm_position t3 on t1.position_id = t3.position_id
        WHERE employee_no IN
        <foreach collection="userCodeArray" item="userCode" open="(" separator="," close=")">
            #{userCode}
        </foreach>
    </select>

    <!--查询个人设置的基础信息-->
    <select id="personalInformationSettings" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
        SELECT
        	a.employee_id ID,
			a.EMP_PAYROLL,
			a.org_id EMP_DEPT_ID,
			c.name as empDeptName,
			c.code EMP_DEPT_CODE,
			a.employee_name EMP_NAME,
			a.employee_no EMP_CODE,
			a.gender EMP_SEX,
			a.phone_number EMP_PHONE,
			a.emp_business_phone EMP_BUSINESS_PHONE,
			a.avatar EMP_HEAD_IMG,
			a.emp_signimg EMP_SIGNIMG,
			a.SIGNATURE_IMG_NAME,
			a.entry_date EMP_HIREDATE,
			i.quit_date EMP_FIREDATE,
			a.identity_number EMP_IDCARD,
			a.birthday EMP_BIRTH,
			a.emp_age EMP_AGE,
			a.employee_status EMP_STATUS,
			a.email EMP_EMAIL,
			position.position_id EMP_DUTY_ID,
			position.position_name EMP_DUTY_NAME,
			a.employee_no USER_ACCOUNTS,
			a.IS_SMS_REMINDER,
			a.IS_VOICE_REMINDER,
			a.IS_WX_REMINDER,
			a.IS_DISPLAY_PHONE_NO,
			a.IS_USE_SIGNATURE,
			a.CREATE_USER,
			a.CREATE_USER_NAME,
			a.CREATE_DATE,
			a.UPDATE_USER,
			a.UPDATE_USER_NAME,
			a.UPDATE_DATE,
			a.IS_DELETED,
			a.name_spell EMP_NAME_PINYIN,
			a.YEAR_WORK,
			a.YEAR_NUMBER,
			a.YEAR_DAYS,
			i.plgw,
			i.gwdj,
			i.salary_level_id as salaryLevel
        FROM
        cust_emp_base a
        left join cust_emp_info i on a.employee_id = i.info_id
        left join comm_organization c on c.organization_id = a.org_id
		left join comm_position position on a.position_id = position.position_id
        WHERE a.IS_DELETED = 'N' AND (a.employee_no = #{empCode})
    </select>

    <select id="empCodeList" resultType="String">
  		SELECT lower(employee_no) from cust_emp_base WHERE IS_DELETED = 'N'
  	</select>

    <!--通过empCode查询是否已经存在该员工-->
    <select id="selectEmployeeByEmpCode" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
  		SELECT
			s.employee_id ID,
			s.EMP_PAYROLL,
			s.org_id EMP_DEPT_ID,
			a5.name as empDeptName,
			a5.code EMP_DEPT_CODE,
			s.employee_name EMP_NAME,
			s.employee_no EMP_CODE,
			s.gender EMP_SEX,
			s.phone_number EMP_PHONE,
			s.emp_business_phone EMP_BUSINESS_PHONE,
			s.avatar EMP_HEAD_IMG,
			s.emp_signimg EMP_SIGNIMG,
			s.SIGNATURE_IMG_NAME,
			s.entry_date EMP_HIREDATE,
			i.quit_date EMP_FIREDATE,
			s.identity_number EMP_IDCARD,
			s.birthday EMP_BIRTH,
			s.emp_age EMP_AGE,
			s.employee_status EMP_STATUS,
			s.email EMP_EMAIL,
			a1.position_id EMP_DUTY_ID,
			a1.position_name EMP_DUTY_NAME,
			s.employee_no USER_ACCOUNTS,
			s.IS_SMS_REMINDER,
			s.IS_VOICE_REMINDER,
			s.IS_WX_REMINDER,
			s.IS_DISPLAY_PHONE_NO,
			s.IS_USE_SIGNATURE,
			s.CREATE_USER,
			s.CREATE_USER_NAME,
			s.CREATE_DATE,
			s.UPDATE_USER,
			s.UPDATE_USER_NAME,
			s.UPDATE_DATE,
			s.IS_DELETED,
			s.name_spell EMP_NAME_PINYIN,
			s.YEAR_WORK,
			s.YEAR_NUMBER,
			s.YEAR_DAYS,
			i.plgw,
			i.gwdj,
			i.salary_level_id as salaryLevel,
			a2.item_name AS postName,
			a2.item_code AS postId,
			s.entry_date AS entryDate,
			a15.item_name AS orgAttributes,
			s.job_attributes AS jobAttributes,
			a1.position_name AS empDutyName,
			a1.position_id AS empDutyId,
			a3.item_name AS postType,
			s.positive_time AS positiveTime,
			a4.jobtitleCategoryName AS empPosttitle,
			org.org_name AS ssoOrgName,
			a5.org_flag AS orgFlag,
			s.carNo,
			s.organization_parttime_id,
			s.organization_parttime_name,
			i.work_start_date,
			i.technical,
			s.hosp_code,
			a16.education_type_name,
			i.operation_scope,
			a17.item_name AS operationName,
			a18.assessment_date,
			a18.inaugural_date
		FROM
			cust_emp_base s
			left join cust_emp_info i on s.employee_id = i.info_id 
			LEFT JOIN ts_thps.thps_org org ON s.sso_org_code = org.org_code
			LEFT JOIN comm_position a1 ON s.position_id = a1.position_id
			LEFT JOIN comm_organization a5 ON a5.organization_id = s.org_id
			LEFT JOIN (
				SELECT
					a.* 
				FROM
					COMM_DICT_ITEM a
					LEFT JOIN COMM_DICT_TYPE b ON a.DIC_TYPE_ID = b.ID
				WHERE
					b.TYPE_CODE = 'personal_identity' 
					AND b.IS_DELETED = 'N' 
					AND a.IS_DELETED = 'N'
				    AND A.IS_ENABLE = '1'
				    AND A.sso_org_code = #{ssoOrgCode}
				) a2 ON s.personal_identity = a2.item_code
			LEFT JOIN (
				SELECT
					a.* 
				FROM
					COMM_DICT_ITEM a
					LEFT JOIN COMM_DICT_TYPE b ON a.DIC_TYPE_ID = b.ID
				WHERE
					b.TYPE_CODE = 'post_type' 
					AND b.IS_DELETED = 'N' 
					AND a.IS_DELETED = 'N'
				    AND A.IS_ENABLE = '1'
				    AND A.sso_org_code = #{ssoOrgCode}
				) a3 ON s.post_type = a3.item_code
			LEFT JOIN (
				SELECT
					info.employee_id,
					b.jobtitle_basic_name AS jobtitleCategoryName 
				FROM
					hrms_jobtitle_info info
					INNER JOIN comm_jobtitle_basic b ON info.jobtitle_name = b.jobtitle_basic_id 
				WHERE
					info.is_deleted = 'N' 
					AND highest_level = '1' 
				GROUP BY
					info.employee_id,
					b.jobtitle_basic_name 
				) a4 ON a4.employee_id = s.employee_id
			LEFT JOIN (
				SELECT
					a.* 
				FROM
					COMM_DICT_ITEM a
					LEFT JOIN comm_dict_type b ON a.DIC_TYPE_ID = b.ID 
				WHERE
					b.TYPE_CODE = 'ORG_ATTRIBUTES' 
					AND b.IS_DELETED = 'N' 
					AND a.IS_DELETED = 'N'
				    AND A.IS_ENABLE = '1'
				    AND A.sso_org_code = #{ssoOrgCode}
				) a15 ON s.org_attributes = a15.item_code
			LEFT JOIN (
					SELECT
						ed.employee_id,
						ed.school_name,
						ed.education_type,
						dict.ITEM_NAME AS education_type_name,
						ed.start_time,
						ed.end_time,
						ed.professional,
						ed.degree_number,
						ed.certificate_number
						FROM
						hrms_education_info ed
					LEFT JOIN (
						SELECT
							A.*
						FROM
						COMM_DICT_ITEM A
						LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
						WHERE
						B.TYPE_CODE = 'education_type'
						AND B.IS_DELETED = 'N'
						AND A.IS_DELETED = 'N'
						AND A.IS_ENABLE = '1'
						AND A.sso_org_code = #{ssoOrgCode}
				) dict ON ed.education_type = dict.item_code
					WHERE
					ed.is_deleted = 'N'
					AND ed.highest_level = 1
					GROUP BY
					ed.employee_id
				) a16 ON a16.employee_id = s.employee_id
				LEFT JOIN (
				SELECT
					a.* 
				FROM
					COMM_DICT_ITEM a
					LEFT JOIN COMM_DICT_TYPE b ON a.DIC_TYPE_ID = b.ID
				WHERE
					b.TYPE_CODE = 'operation_type' 
					AND b.IS_DELETED = 'N' 
					AND a.IS_DELETED = 'N'
				    AND A.IS_ENABLE = '1'
				     AND a.sso_org_code = #{ssoOrgCode}
				) a17 ON i.operation_type = a17.item_code
				LEFT JOIN (
					SELECT
					info.employee_id,
					info.assessment_date,
					info.inaugural_date
					FROM
					hrms_jobtitle_info info
					WHERE
					info.is_deleted = 'N'
					AND info.highest_level = 1
					GROUP BY
					info.employee_id
				) a18 ON a18.employee_id = s.employee_id
  			WHERE s.employee_no = #{empCode}
  	</select>

    <select id="getEmployeeByBirthday" resultType="cn.trasen.oa.hrm.model.Employee">
	 SELECT
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.birthday
	    FROM  cust_emp_base a
	    LEFT JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
		WHERE date_format( a.birthday,'%m-%d') = date_format(now(),'%m-%d') and a.employee_status in ('1','5','6','9','12','88','99')
  	</select>
  	
    <select id="getBirthdayByMonth" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="String">
	 SELECT
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.birthday as birth
	    FROM  cust_emp_base a
	    LEFT JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
		WHERE date_format( a.birthday,'%m') = #{month} and a.employee_status in ('1','5','6','9','12','88','99')
  	</select>
  	
  	<!--查询当天入职员工信息-->
    <select id="getEmployeeByHiredate" resultType="cn.trasen.oa.hrm.model.Employee">
		  SELECT
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS
	    FROM  cust_emp_base a
	    LEFT JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
		WHERE date_format(a.entry_date,'%m-%d') = date_format(now(),'%m-%d') and a.employee_status in ('1','5','6','9','12','88','99')
  	</select>

    <!--批量增加员工信息-->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO TOA_EMPLOYEE (ID, EMP_NAME,EMP_SEX, EMP_CODE,
            EMP_PAYROLL,EMP_DEPT_ID,EMP_DEPT_CODE,EMP_DEPT_NAME,
            EMP_STATUS,USER_ACCOUNTS,EMP_NAME_PINYIN,
            CREATE_DATE,IS_DELETED,USER_IS_DELETED,
            IS_SMS_REMINDER,IS_VOICE_REMINDER,IS_WX_REMINDER,IS_DISPLAY_PHONE_NO,IS_USE_SIGNATURE)
            VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.empName,jdbcType=VARCHAR},
            #{item.empSex,jdbcType=DECIMAL}, #{item.empCode,jdbcType=VARCHAR},
            #{item.empPayroll,jdbcType=VARCHAR}, #{item.empDeptId,jdbcType=VARCHAR},
            #{item.empDeptCode,jdbcType=VARCHAR}, #{item.empDeptName,jdbcType=VARCHAR},
            #{item.empStatus,jdbcType=DECIMAL},
            #{item.userAccounts,jdbcType=VARCHAR}, #{item.empNamePinyin,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=DECIMAL},#{item.userIsDeleted,jdbcType=VARCHAR},
            #{item.isSmsReminder,jdbcType=DECIMAL},#{item.isVoiceReminder,jdbcType=DECIMAL},
            #{item.isWxReminder,jdbcType=DECIMAL},#{item.isDisplayPhoneNo,jdbcType=DECIMAL},
            #{item.isUseSignature,jdbcType=DECIMAL})
        </foreach>
    </insert>

    <!--批量修改员工信息-->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";" open="begin" close=";end;">
            UPDATE TOA_EMPLOYEE SET ID = #{item.id,jdbcType=DECIMAL}
            <if test="item.empDutyId != null and item.empDutyId != ''">
                ,EMP_DUTY_ID = #{item.empDutyId}
            </if>
            <if test="item.empDeptName != null and item.empDeptName != ''">
                ,EMP_DEPT_NAME = #{item.empDeptName}
            </if>
            <if test="item.browserange != null and item.browserange != ''">
                ,BROWSERANGE = #{item.browserange}
            </if>
            <if test="item.browserangeName != null and item.browserangeName != ''">
                ,BROWSERANGE_NAME = #{item.browserangeName}
            </if>
            WHERE 1 = 1
            <if test="item.id != null and item.id != ''">
                AND ID = #{item.id,jdbcType=VARCHAR}
            </if>
            <if test="item.empCode != null and item.empCode != ''">
                AND EMP_CODE = #{item.empCode,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>

    <!--同步用户信息 批量修改员工信息-->
    <update id="syncBatchUpdate" parameterType="java.util.List">
    	 <foreach collection="list" item="item" index="index" open="" close="" separator=";">
	        UPDATE TOA_EMPLOYEE
	        <set>
	        	<if test="item.empCode != null and item.empCode != ''">
	               	EMP_CODE = #{item.empCode,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empName != null and item.empName != ''">
	                EMP_NAME = #{item.empName,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empSex != null">
	                EMP_SEX = #{item.empSex},
	            </if>
	            <if test="item.empDeptId != null and item.empDeptId != ''">
	                EMP_DEPT_ID = #{item.empDeptId,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empDeptCode != null and item.empDeptCode != ''">
	               	EMP_DEPT_CODE = #{item.empDeptCode,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empDeptName != null and item.empDeptName != ''">
	                EMP_DEPT_NAME = #{item.empDeptName,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empDutyName != null and item.empDutyName != ''">
	                EMP_DUTY_NAME = #{item.empDutyName,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empPhone != null and item.empPhone != ''">
	                EMP_PHONE = #{item.empPhone,jdbcType=VARCHAR},
	            </if>
	            <if test="item.empPassword != null and item.empPassword != ''">
	                EMP_PASSWORD = #{item.empPassword,jdbcType=VARCHAR},
	            </if>
	            <if test="item.openId != null and item.openId != ''">
	                OPEN_ID = #{item.openId,jdbcType=VARCHAR},
	            </if>
	             <if test="item.empStatus != null">
	                EMP_STATUS = #{item.empStatus},
	            </if>
	        </set>
	        WHERE ID = #{item.id,jdbcType=VARCHAR}
   		 </foreach>
    </update>

    <select id="getEmployeeList" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
        SELECT
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.open_id
	    FROM  cust_emp_base a
		LEFT JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
        WHERE a.IS_DELETED = 'N'  and a.sso_org_code = #{ssoOrgCode} and a.employee_status in ('1','5','6','9','12','88','99')
        <if test="empName != null and empName != ''">
            AND a.employee_name like  CONCAT(CONCAT('%', #{empName}), '%')
        </if>
        <if test="empCode != null and empCode != ''">
            AND a.employee_no = #{empCode}
        </if>
        <if test="empDeptName != null and empDeptName != ''">
            AND org.name like  CONCAT(CONCAT('%', #{empDeptName}), '%')
        </if>
        <if test="empDeptCode != null and empDeptCode != ''">
            AND org.code = #{empDeptCode}
        </if>
        <if test="searchKey != null and searchKey != ''">
            AND  (org.name like  CONCAT(CONCAT('%', #{searchKey}), '%') or a.employee_name like  CONCAT(CONCAT('%', #{searchKey}), '%'))
        </if>
    </select>


    <!--根据科室code查询科室人员列表-->
    <select id="getUsersByDeptCode" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
        select
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.open_id
	    from cust_emp_base a
		left JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
        where a.IS_DELETED = 'N'  and a.employee_status in ('1','99')
        and org.code in
        <foreach collection="deptCodeArray" item="deptCode" open="(" separator="," close=")">
            #{deptCode}
        </foreach>
        order by a.name_spell asc
    </select>

    <select id="getUsersCountByDeptCode" resultType="long">
        SELECT COUNT(1) FROM cust_emp_base a
        WHERE a.employee_status in ('1','99') AND a.IS_DELETED = 'N'
        <if test="deptCodeArray != null">
            AND a.org_id in
            <foreach collection="deptCodeArray" item="deptCode" open="(" separator="," close=")">
                #{deptCode}
            </foreach>
        </if>
    </select>
    
    <select id="selectByEntity" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="cn.trasen.oa.hrm.model.Employee">
         select
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.open_id,
			a.phone_number as empPhone
	    from cust_emp_base a
		left JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
        WHERE a.employee_status IS NOT NULL AND a.IS_DELETED = 'N'
        <if test="userAccounts != null and userAccounts != ''">
            AND (a.employee_no = #{userAccounts} or a.phone_number = #{userAccounts})
        </if>
        <if test="empPhone != null and empPhone != ''">
            AND a.phone_number = #{empPhone}
        </if>
    </select>
    
    <select id="selectByEmpCode" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="String">
         select
	        a.employee_id ID,
	        org.organization_id EMP_DEPT_ID,
	        org.name EMP_DEPT_NAME,
	        org.code EMP_DEPT_CODE,
	        a.employee_name EMP_NAME,
	        a.employee_no EMP_CODE,
	        a.EMP_PAYROLL,
	        position.position_name EMP_DUTY_NAME,
	        a.gender EMP_SEX,
	        a.avatar EMP_HEAD_IMG,
	        a.EMP_AGE,
	        a.employee_status EMP_STATUS,
	        a.employee_no USER_ACCOUNTS,
	        a.entry_date EMP_HIREDATE,
	        a.name_spell EMP_NAME_PINYIN,
	        a.YEAR_WORK,
	        a.YEAR_NUMBER,
	        a.YEAR_DAYS,
	        a.open_id
	    from cust_emp_base a
		left JOIN comm_organization org ON org.organization_id = a.org_id
		left join comm_position position on position.position_id = a.position_id
        WHERE a.employee_status IS NOT NULL AND a.IS_DELETED = 'N'
        AND lower(a.employee_no) = #{empCode}
    </select>

    <update id="updateOpenId" parameterType="java.lang.String">
        update cust_emp_base SET OPEN_ID = NULL WHERE employee_no = #{userCode}
    </update>
    
    <select id="selectOpenId" resultType="String">
    	SELECT OPEN_ID FROM cust_emp_base
    	WHERE OPEN_ID IS NOT NULL AND OPEN_ID != ''
    </select>
    
    <select id="selectEmpSignimg" resultType="String" parameterType="String">
    	select EMP_SIGNIMG from cust_emp_base
		where employee_no = #{userCode}
    </select>
    
    <select id="selectEmpUploadFileSize" resultType="String" parameterType="String">
    	select UPLOAD_FILE_SIZE from cust_emp_base
		where EMP_CODE = #{empCode}
    </select>
    
    <select id="selectByEmpPayroll" resultType="cn.trasen.oa.hrm.model.Employee" parameterType="String">
    	select employee_no as EMP_CODE,EMP_PAYROLL from cust_emp_base
		where EMP_PAYROLL = #{empPayroll}
    </select>
    
    <select id="selectEmpCodeByEmpPayroll" resultType="Map" parameterType="String">
    	select employee_no empCode,EMP_PAYROLL empPayroll from cust_emp_base
		where sso_org_code = #{ssoOrgCode} and EMP_PAYROLL in 
		<foreach collection="empPayrollList" item="empPayroll" index="index" open="(" separator="," close=")">
              #{empPayroll}
        </foreach>
    </select>
    
    <select id="selectEmpPayrollByEmpCode" resultType="String" parameterType="String">
    	select EMP_PAYROLL from cust_emp_base
		where employee_no = #{currentUserCode}
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			and sso_org_code = #{ssoOrgCode}
		</if>
    </select>
    
    <update id="updateEmpYearNumber">
        update TOA_EMPLOYEE SET YEAR_NUMBER = 0;
        update cust_emp_base SET year_number = 0;
    </update>
    
    <update id="updateAgent"  parameterType="cn.trasen.oa.hrm.model.Employee">
        update toa_employee 
        set agent_id =#{agentId},
        agent_name =#{agentName},
        agent_start_time=#{agentStartTime},
        agent_end_time=#{agentEndTime},
        is_enable_process_agent=#{isEnableProcessAgent}
        where id = #{id}
    </update>
    
    
    <select id="selectEmpAgentId" resultType="String" parameterType="String">
    	select employee_no from cust_emp_base
		where find_in_set(#{userCode},agent_ids) and IS_ENABLE_PROCESS_AGENT = 1 
		and (AGENT_START_TIME &lt;= CURRENT_DATE and AGENT_END_TIME >= CURRENT_DATE)
    </select>
    
     <select id="selectEmployeeList" resultType="Map">
    		select
    		t2.employee_id empId,
				t2.employee_no empCode,
				t2.employee_no userAccounts,
				t2.employee_name empName,
				t3.`name` empDeptName,
				t2.org_id empDeptCode,
				t2.identity_number empIdcard,
				CONVERT(t2.employee_status, SIGNED) empStatus,
				CONVERT(t2.gender, SIGNED) empSex,
				date_format(t2.birthday, '%Y-%m-%d') empBirth,
				t2.phone_number empPhone,
				a2.item_name as personalIdentityName,
				t2.emp_age as empAge,
				a3.item_name as establishmentType,
				t2.entry_date as entryDate,
				a4.item_name as orgAttributes,
				a5.item_name as politicalStatus,
				i.party_date as partyDate,
				a6.item_name as marriageStatus,
				t2.is_deleted as isDeleted,
				t2.create_date as createDate
			from
				cust_emp_base t2
				LEFT JOIN cust_emp_info i on t2.employee_id = i.info_id
				left join comm_organization t3 on  t2.org_id = t3.organization_id
				left join (
					 SELECT
					 A.*
					 FROM
					 COMM_DICT_ITEM A
					 LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
					 WHERE
					 B.TYPE_CODE = 'personal_identity'
					 AND B.IS_DELETED = 'N'
					 AND A.IS_DELETED = 'N'
					 AND A.IS_ENABLE = '1'
					 AND A.sso_org_code = #{ssoOrgCode}
					 ) a2 on t2.personal_identity = a2.item_code
				LEFT JOIN (
					 SELECT
					 A.*
					 FROM
					 COMM_DICT_ITEM A
					 LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
					 WHERE
					 B.TYPE_CODE = 'establishment_type'
					 AND B.IS_DELETED = 'N'
					 AND A.IS_DELETED = 'N'
					 AND A.IS_ENABLE = '1'
					 AND A.sso_org_code = #{ssoOrgCode}
				) a3 on t2.personal_identity = a3.item_code
				LEFT JOIN (
					 SELECT
					 A.*
					 FROM
					 COMM_DICT_ITEM A
					 LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
					 WHERE
					 B.TYPE_CODE = 'org_attributes'
					 AND B.IS_DELETED = 'N'
					 AND A.IS_DELETED = 'N'
					 AND A.IS_ENABLE = '1'
					 AND A.sso_org_code = #{ssoOrgCode}
				 ) a4 on t2.personal_identity = a4.item_code
			LEFT JOIN (
				 SELECT
				 A.*
				 FROM
				 COMM_DICT_ITEM A
				 LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
				 WHERE
				 B.TYPE_CODE = 'political_status'
				 AND B.IS_DELETED = 'N'
				 AND A.IS_DELETED = 'N'
				 AND A.IS_ENABLE = '1'
				 AND A.sso_org_code = #{ssoOrgCode}
			) a5 on t2.personal_identity = a5.item_code
			LEFT JOIN (
				 SELECT
				 A.*
				 FROM
				 COMM_DICT_ITEM A
				 LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
				 WHERE
				 B.TYPE_CODE = 'marriage_status'
				 AND B.IS_DELETED = 'N'
				 AND A.IS_DELETED = 'N'
				 AND A.IS_ENABLE = '1'
				 AND A.sso_org_code = #{ssoOrgCode}
			) a6 on t2.personal_identity = a6.item_code
		<!-- where t2.IS_DELETED = 'N' and t2.employee_status in (1,6,9,12,99) and t3.`name`!= '水电班' and t2.employee_id != 'admin' -->
    </select>
    
    <update id="modifyEmployeeStatus" parameterType="String" >
        update toa_employee set emp_status = #{status}
        where emp_code = #{usercode};
        
        update cust_emp_base set employee_status = #{status}
        where employee_no = #{usercode}
    </update>
    
     <update id="updateHrmsEmployeeWorkYear" parameterType="cn.trasen.oa.hrm.model.Employee" >
        update cust_emp_base set year_work = #{yearWork},year_days = #{yearDays}
        where employee_no = #{empCode}
    </update>
    
    <update id="initOrgData" parameterType="cn.trasen.oa.hrm.utils.OrgData">
		INSERT INTO `ts_thps`.`thps_org` (`org_id`,`org_code`,`org_name`,`STATUS`,`create_date`,`create_user`,`update_date`,`update_user`,`IS_DELETED`,
		`PARENT_CODE`,`CREATE_USER_NAME`,`UPDATE_USER_NAME`,`MAR_CATG_CODE`,`MAR_CATG_NAME`)
		VALUES(#{orgId},#{orgCode},#{orgName},'1','2024-01-16 02:54:22','admin','2024-01-16 02:54:22','admin','N',#{parentCode},'admin','admin','YLJG','医疗机构');
	
		INSERT INTO `ts_thps`.`thps_dept` (`id`,`corpcode`,`deptcode`,`deptname`,`level`,`deptlevel`,`STATUS`,`createuser`,`createtime`,
		`updateuser`,`updatetime`,`CREATE_USER_NAME`,`UPDATE_USER_NAME`,`IS_DELETED`,`IDTYPE`,`ORGFLAG` )VALUES
		(#{deptId},#{orgCode},#{deptId},#{orgName},'0','0','1','admin','2024-01-16 02:57:49','admin','2024-01-16 02:57:49','admin','admin','N','1','1');
		
		INSERT INTO `ts_thps`.thps_user (id,corpcode,usercode,username,PASSWORD,oldpassword,STATUS,appuser,apptime,updateuser,updatetime,EMPLOYEE_ID)
		VALUES(#{userId},#{orgCode},#{userCode},#{userName},'1000:7118e88aa62aa611886cc004293d3c4b:317836173e1e999a9222422d5da63367','PAPGvf0RpOsc3T+8XkFMvA==49.67',
				1,#{userCode},'2024-3-2 17:03:51','admin','2024-3-2 17:03:51',#{userId});		

		INSERT INTO `ts_thps`.THPS_EMPLOYEE (ID,NAME,NATIONALITY,BIRTHDAY,JOB_STATUS,JOB_STATUS_NAME,SEX,HEAD_IMAGE_URL,CARD_NO,CARD_TYPE,
		STATUS,CREATE_DATE,CREATE_USER,CREATE_USER_NAME,UPDATE_USER,UPDATE_USER_NAME,IS_DELETED,JOB_TYPE,JOB_TYPE_NAME ) 
		VALUES( #{userId},#{userName},'汉族','1111-11-11','在编','在编','1','','******************','01',
		1,'2024-3-2 17:03:50','admin','admin','admin','admin','N','01','医生');
		
		INSERT INTO `ts_thps`.thps_user_org_map (ID,IS_DELETED,CREATE_USER,CREATE_DATE,UPDATE_USER,UPDATE_DATE,UPDATE_USER_NAME,CREATE_USER_NAME,USER_ID,
		ORG_ID,DEAD_DATE,EFFECTIVE_DATE,DEFAULT_DEPT_CODE,DEFAULT_DEPT_ID,STATUS)
		VALUES(#{orgMapId},'N','admin','2024-3-2 17:03:51','admin','2024-3-2 17:03:51','admin','admin',#{userId},
		#{orgId},'2124-3-2 0:00:00','2024-3-2 0:00:00',#{deptId},#{deptId},1);
		
		INSERT INTO `comm_organization` (`organization_id`,`code`,`name`,`parent_id`,`tree_ids`,`is_enable`,`org_level`,`seq_no`,`org_flag`,
		`personnel_allocation`,`create_date`,`create_user`,`update_date`,`update_user`,`is_deleted`,`sort`,`sso_org_code`)
		VALUES(#{deptId},#{deptId},#{orgName},'',#{deptId},'1','1','1','1','0','2023-01-12 17:27:19','admin','2024-01-20 17:07:38','admin','N','0',#{orgCode});

		INSERT INTO `cust_emp_base` (`employee_id`,`old_employee_no`,`his_employee_no`,`employee_no`,`employee_name`,`org_id`,
		`employee_status`,is_deleted,is_enable,is_sms_reminder,is_voice_reminder,is_wx_reminder,is_display_phone_no,is_use_signature,user_is_deleted,sso_org_code)
		VALUES(#{userId},#{userCode},#{userCode},#{userCode},#{userName},#{deptId},'1','N','1','1','1','1','1','1','N',#{orgCode});

		INSERT INTO `toa_employee` (`ID`,`EMP_DEPT_ID`,`EMP_DEPT_NAME`,`EMP_DEPT_CODE`,`EMP_NAME`,`EMP_CODE`,`EMP_SEX`,
		EMP_STATUS,USER_ACCOUNTS,IS_SMS_REMINDER,IS_VOICE_REMINDER,IS_WX_REMINDER,IS_DISPLAY_PHONE_NO,IS_USE_SIGNATURE,IS_DELETED,sso_org_code)
		VALUES(#{userId},#{deptId},#{orgName},#{deptId},#{userName},#{userCode},1,1,#{userCode},1,1,1,1,1,'N',#{orgCode});
		
		INSERT INTO `toa_sys_setting` (`ID`,`HOSPITAL_LOGO`,`TOP_LOGO`,`LOGIN_PAGE_BACKGROUND`,`LOGIN_TOP_LOGO`,
		`CREATE_USER`,`CREATE_USER_NAME`,`CREATE_DATE`,`UPDATE_USER`,`UPDATE_USER_NAME`,`UPDATE_DATE`,`IS_DELETED`,`ORG_CODE`,
		`watermark_list`,`watermark_text`,`password_preset`,`password_length`,`password_rule`,`remind_password`,`remind_signature`,
		`web_title`,`mobile_platform`,`salary_type`,`forget_pwd`,`verify_code`,`im_switch`,`worksheet_switch`,`account_login`,
		`lockscreen`,`remember_pwd`,`parttime_switch`,`anonymous_box`,`is_default`,`platform_type`,`platform_login_type`,`sso_org_code`,
		`sso_org_name`,`SMS_CODE`,`verify_code_type`)
		VALUES(#{settingId},'698090489444745216','699144934001696769','697379483491946496','698090542473330689','admin',
			'admin','2021-08-30 15:45:39','admin','管理员','2023-10-27 20:03:42','',#{orgCode},'','','123456','8','1,2,3,4','1',
			'0','协同办公平台','1','1,2','1','1','0','0','0','0','1','0','0','0','2','1',#{orgCode},#{orgName},'0','1');

		INSERT INTO `toa_portal_theme` (`id`,`title`,`is_default`,`create_user`,`create_user_name`,`create_time`,
		`is_deleted`,`all_reader`,`fixed_height`,`sso_org_code`,`sso_org_name`)
		VALUES(#{themeId},#{orgName},'1',#{userCode},#{userName},'2024-01-16 11:36:57','N','1','1',#{orgCode},#{orgName});

		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`,
		 `create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId1}, #{themeId}, '1', '2', '1', '', 'N', '0', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`, 
		`create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId2}, #{themeId}, '4', '2', '1', '', 'N', '1', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
		INSERT INTO `toa_portal_element` (`id`, `theme_id`, `element_type`, `element_column`, `element_show`, `element_channel`, `is_deleted`, `sord`, 
		`create_time`, `create_user`, `create_user_name`, `width_type`, `height_type`, `element_medical_business`, `element_name`, `sso_org_code`) 
		VALUES (#{elementId3}, #{themeId}, '11', '2', '1', '', 'N', '2', '2024-03-04 14:33:17', #{userCode}, #{userName}, NULL, NULL, '1', '', #{orgCode});
		
	</update>
	
    <select id="getPostCategory" resultType="java.util.Map">
   	 	SELECT post_id as postId,post_name as postName FROM comm_post WHERE is_deleted = 'N'
    </select>
    
    <select id="getSalaryLevelCategory" resultType="java.util.Map">
    	SELECT salary_level_id as salaryLevelId,salary_level_name as salaryLevelName FROM  hrms_salary_level  WHERE is_deleted = 'N'
  	</select>

    <select id="selectEmpCodeList" resultType="java.lang.String">
        select employee_no empCode from cust_emp_base
        where employee_no in
        <foreach collection="empNoList" item="empNo" index="index" open="(" separator="," close=")">
            #{empNo}
        </foreach>
    </select>
    
     <select id="getEmployeeListByModel" resultMap="BaseResultMap" parameterType="cn.trasen.oa.hrm.model.Employee">
     	SELECT
			a.employee_id ID,
			a.employee_no EMP_CODE,
			a.entry_date AS EMP_HIREDATE,
			a.YEAR_WORK,
			a.YEAR_NUMBER,
			a.YEAR_DAYS 
		FROM
			cust_emp_base a 
		WHERE
			a.IS_DELETED = 'N' 
			AND a.employee_status IN ( '1', '5', '6', '9', '12', '88', '99' )
     	<if test="empCode != null and empCode != ''">
            AND a.employee_no = #{empCode}
        </if>
        <if test="ssoOrgCode != null and ssoOrgCode != ''">
           and a.sso_org_code = #{ssoOrgCode}
        </if>
    </select>
    
</mapper>
