package cn.trasen.oa.hrm.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.hrm.model.Employee;
import cn.trasen.oa.hrm.model.EmployeeTransfer;

import java.util.List;

/**
 * @Description: 员工调动记录Service层
 * @Date: 2020/1/11 16:46
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
public interface EmployeeTransferService {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 查询员工调动记录列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: java.util.List<cn.trasen.oa.hrm.model.EmployeeTransfer>
     **/
    List<EmployeeTransfer> getDataList(Page page, EmployeeTransfer employeeTransfer);

    /**
     * @Author: Liz<PERSON><PERSON>o
     * @Description: 新增员工调动记录
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    int insert(EmployeeTransfer employeeTransfer);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 新增员工调动记录(公共调用)
     * @Date: 2020/1/15 9:18
     * @Param: employee 员工信息  originPosition 原科室
     * @return: int
     **/
    int insertEmployeeTransfer(Employee employee, String originPosition);

}
