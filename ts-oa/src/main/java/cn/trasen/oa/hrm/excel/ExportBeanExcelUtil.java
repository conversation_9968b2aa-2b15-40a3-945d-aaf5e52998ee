package cn.trasen.oa.hrm.excel;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @Description: Excel通用导出
 * @Date: 2020/1/16 09:23
 * @Author: Liz<PERSON>huo
 * @Company: 湖南创星
 */

public class ExportBeanExcelUtil {

    private static int rowNum = 0;//开始行数

    private static int addNum = 1;//行数添加比例值

    /**
     * @param title   工作薄标题
     * @param <T>     泛型类
     * @return HSSFWorkbook 对象
     */
    @SuppressWarnings("rawtypes")
    public static <T> byte[] exportExcell(Map<String, Object> map, Class master, Class slave, String title) {
        //1.解析注解  属性名 和 列名
        List<ExcelColumn.ExcellColumnConfig> columnConfigListMaster = processAnnotationMaster(master);
        List<ExcelColumn.ExcellColumnConfigSlave> columnConfigList = processAnnotationSlave(slave);
        //2. 按照指定顺序 对列进行排序
        if (slave != null) {
            columnConfigList = sortColumn(columnConfigList);
        }
        if (master != null) {
            columnConfigListMaster = sortColumnMaster(columnConfigListMaster);
        }
        //3.创建工作薄
        HSSFWorkbook wb = new HSSFWorkbook();
        //设置样式
        HSSFCellStyle style = setStyle(wb);//表头样式
        HSSFSheet sheet = wb.createSheet(title);
        //标题样式
        createHeard(wb, sheet, title, columnConfigList);
        //4.初始化列宽 表头
        initTableHeader(sheet, columnConfigList, style);
        //5. 导出数据
        createTableBody(sheet, columnConfigList, columnConfigListMaster, map, wb);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            wb.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (wb != null) {
                    wb.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return os.toByteArray();
    }

    //创建标题
    public static void createHeard(HSSFWorkbook wb, HSSFSheet sheet, String title, List<ExcelColumn.ExcellColumnConfigSlave> columnConfigList) {
        // 标题字体
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("宋体");
        fontStyle.setFontHeightInPoints((short) 15);
        fontStyle.setBold(true);
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(fontStyle);
        HSSFRow row = sheet.createRow(0);
        HSSFCell cell = row.createCell(0);
        cell.setCellValue(title);
        cell.setCellStyle(style);
        //合并单元格
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, columnConfigList.size() - 1);
        sheet.addMergedRegion(region);
    }

    /**
     * <p> @Title: setStyle</p>
     * <p> @Description: 设置excel样式</p>
     * <p> @Param: </p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     */
    private static HSSFCellStyle setStyle(HSSFWorkbook wb) {
        //表头样式
        HSSFCellStyle style = wb.createCellStyle();
        style.setWrapText(true);
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        //字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("宋体");
        fontStyle.setFontHeightInPoints((short) 11);
        fontStyle.setBold(true);
        style.setFont(fontStyle);

        return style;
    }

    /**
     * <p> @Title: createColStyle</p>
     * <p> @Description: 设置表体样式</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFCellStyle</p>
     * <p> <AUTHOR>
     */
    private static HSSFCellStyle createColStyle(HSSFWorkbook wb) {
        HSSFCellStyle style = wb.createCellStyle();
        style.setWrapText(true);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    /**
     * 根据 数据 集合 创建表体
     *
     * @param sheet             工作薄对象
     * @param columnConfigArray 排序后的
     * @param <T>               泛型
     */
    @SuppressWarnings("unchecked")
    private static <T> void createTableBody(HSSFSheet sheet, List<ExcelColumn.ExcellColumnConfigSlave> columnConfigArray, List<ExcelColumn.ExcellColumnConfig> columnConfigArrayMaster, Map<String, Object> map, HSSFWorkbook wb) {

        int lineNo = rowNum + addNum + 1;
        HSSFRow row;
        HSSFCell cell;
        try {
            if (map.get("master") != null) {//存在主表数据
                int size = columnConfigArrayMaster.size();
                Object t = map.get("master");
                int rowNum = -1;//存储行数
                String cellDatas = "";//表头上一行数据
                for (int i = 0; i < size; i++) {
                    ExcelColumn.ExcellColumnConfig excellColumnConfig = columnConfigArrayMaster.get(i);
                    String di = excellColumnConfig.getDi();//获取定位
                    CellAddress address = new CellAddress(di);
                    if (address.getRow() != rowNum) {//说明不在同一行
                        rowNum = address.getRow();//赋值新行数
                        //row = sheet.createRow(address.getRow());
                    } else {
                        row = sheet.getRow(address.getRow());
                    }
                    String cellName = columnConfigArrayMaster.get(i).getColumnName();//列名
                    //cell = row.createCell(address.getColumn());
                    Field field = excellColumnConfig.getField();
                    field.setAccessible(true);
                    String cellData = "无";
                    if (field.get(t) != null) {
                        cellData = field.get(t).toString();//获取数据
                    }
                    cellDatas += cellName + ":" + cellData + "    ";
                    // cell.setCellType(CellType.STRING);
                    //cell.setCellValue(cellName+":"+cellData);

                    //合并单元格(4个参数，分别为起始行，结束行，起始列，结束列)
                    // CellRangeAddress region = new CellRangeAddress(rowNum, rowNum, address.getColumn(), address.getColumn()+1);
                    // sheet.addMergedRegion(region);
                }
                row = sheet.createRow(1);
                row.setHeight((short) 400);

                row = sheet.createRow(2);
                cell = row.createCell(0);
                cell.setCellType(CellType.STRING);
                cell.setCellValue(cellDatas);
                CellRangeAddress region = new CellRangeAddress(2, 2, 0, columnConfigArray.size() - 1);
                sheet.addMergedRegion(region);
            }
            //从表数据
            List<T> list = (List<T>) map.get("oa");
            int size = columnConfigArray.size();
            for (T t : list) {
                row = sheet.createRow(lineNo++);
                for (int i = 0; i < size; i++) {
                    cell = row.createCell(i);
                    ExcelColumn.ExcellColumnConfigSlave excellColumnConfig = columnConfigArray.get(i);
                    Field field = excellColumnConfig.getField();
                    field.setAccessible(true);
                    String cellData = null;
                    if (field.get(t) != null) {
                        cellData = field.get(t).toString();//获取数据
                    }
                    cell.setCellType(CellType.STRING);
                    cell.setCellValue(cellData);
                    HSSFCellStyle style = createColStyle(wb);//表体样式
                    HorizontalAlignment ha = excellColumnConfig.getLeftAndRight();
                    style.setAlignment(ha);
                    cell.setCellStyle(style);
                }
            }
            rowNum = 0;//初始化开始行
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 初始化 表头 设置列宽
     *
     * @param sheet             工作薄 对象
     * @param columnConfigArray 排序后的
     */
    private static void initTableHeader(HSSFSheet sheet, List<ExcelColumn.ExcellColumnConfigSlave> columnConfigArray, HSSFCellStyle style) {
        HSSFRow row = sheet.createRow(rowNum + addNum);
        int size = columnConfigArray.size();
        HSSFCell cell;
        for (int i = 0; i < size; i++) {
            cell = row.createCell(i);
            ExcelColumn.ExcellColumnConfigSlave excellColumnConfig = columnConfigArray.get(i);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(excellColumnConfig.getColumnName());
            cell.setCellStyle(style);
            short width = excellColumnConfig.getWidth();
            sheet.setColumnWidth(i, width * 256);
        }
    }

    /**
     * 解析 注解(主表)
     *
     * @param master Class
     * @return columnConfig 集合
     */
    @SuppressWarnings("rawtypes")
    private static List<ExcelColumn.ExcellColumnConfig> processAnnotationMaster(Class master) {
        if (master == null) {
            return null;
        }
        Field[] fields = master.getDeclaredFields();
        List<ExcelColumn.ExcellColumnConfig> columnConfigList = new ArrayList<>();
        for (Field field : fields) {
            ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
            if (annotation != null) {
                //列顺序
                int index = annotation.index();
                //列宽度
                short width = annotation.width();
                //当前列名
                String name = annotation.name().equals("") ? field.getName() : annotation.name();
                //定位
                String di = annotation.di();
                if (StringUtils.isNotBlank(di)) {
                    CellAddress address = new CellAddress(di);
                    if (address.getRow() > rowNum) {
                        rowNum = address.getRow();
                    }
                }
                ExcelColumn.ExcellColumnConfig excellColumnConfig = new ExcelColumn.ExcellColumnConfig();
                excellColumnConfig.setColumnName(name);
                excellColumnConfig.setField(field);
                excellColumnConfig.setIndex(index);
                excellColumnConfig.setWidth(width);
                excellColumnConfig.setDi(di);
                columnConfigList.add(excellColumnConfig);
            }
        }
        return columnConfigList;

    }

    /**
     * 解析 注解(从表)
     *
     * @param slave Class
     * @return columnConfig 集合
     */
    @SuppressWarnings("rawtypes")
    private static List<ExcelColumn.ExcellColumnConfigSlave> processAnnotationSlave(Class slave) {
        if (slave == null) {
            return null;
        }
        Field[] fields = slave.getDeclaredFields();
        List<ExcelColumn.ExcellColumnConfigSlave> columnConfigList = new ArrayList<>();
        for (Field field : fields) {
            ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
            if (annotation != null) {
                //列顺序
                int index = annotation.index();
                //列宽度
                short width = annotation.width();
                //当前列名
                String name = annotation.name().equals("") ? field.getName() : annotation.name();
                //字体定位
                HorizontalAlignment leftAndRight = annotation.leftAndRight();
                ExcelColumn.ExcellColumnConfigSlave excellColumnConfig = new ExcelColumn.ExcellColumnConfigSlave();
                excellColumnConfig.setColumnName(name);
                excellColumnConfig.setField(field);
                excellColumnConfig.setIndex(index);
                excellColumnConfig.setWidth(width);
                excellColumnConfig.setLeftAndRight(leftAndRight);
                columnConfigList.add(excellColumnConfig);
            }
        }
        return columnConfigList;

    }

    /**
     * 排序列 按照 columnConfig 中的index 排序
     *
     * @param columnConfigList columnCofig集合
     * @return columnConfig 排序后的
     */
    private static List<ExcelColumn.ExcellColumnConfigSlave> sortColumn(List<ExcelColumn.ExcellColumnConfigSlave> columnConfigList) {
        Collections.sort(columnConfigList, new Comparator<ExcelColumn.ExcellColumnConfigSlave>() {
            @Override
            public int compare(ExcelColumn.ExcellColumnConfigSlave o1, ExcelColumn.ExcellColumnConfigSlave o2) {
                return o1.getIndex() - o2.getIndex();
            }
        });

        return columnConfigList;
    }

    private static List<ExcelColumn.ExcellColumnConfig> sortColumnMaster(List<ExcelColumn.ExcellColumnConfig> columnConfigList) {
        Collections.sort(columnConfigList, new Comparator<ExcelColumn.ExcellColumnConfig>() {
            @Override
            public int compare(ExcelColumn.ExcellColumnConfig o1, ExcelColumn.ExcellColumnConfig o2) {
                return o1.getIndex() - o2.getIndex();
            }
        });

        return columnConfigList;
    }
}
