<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.hrm.dao.EmployeeTransferMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.hrm.model.EmployeeTransfer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="EMP_ID" jdbcType="VARCHAR" property="empId" />
    <result column="EMP_NAME" jdbcType="VARCHAR" property="empName" />
    <result column="ORIGIN_POSITION" jdbcType="VARCHAR" property="originPosition" />
    <result column="NEW_POSITION" jdbcType="VARCHAR" property="newPosition" />
    <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
    <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="EMP_PAYROLL" jdbcType="VARCHAR" property="empPayroll" />
  </resultMap>

  <select id="selectEmployeeTransferList" resultMap="BaseResultMap"
          parameterType="cn.trasen.oa.hrm.model.EmployeeTransfer">
      SELECT
			a.*,
			b.EMP_PAYROLL AS empPayroll 
		FROM
			TOA_EMPLOYEE_TRANSFER a
			LEFT JOIN cust_emp_base b ON a.EMP_ID = b.employee_id 
		WHERE
			a.IS_DELETED = 'N'
  </select>

</mapper>