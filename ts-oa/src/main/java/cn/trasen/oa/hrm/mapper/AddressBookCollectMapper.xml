<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.hrm.dao.AddressBookCollectMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.oa.hrm.model.AddressBookCollect">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="EMP_ID" jdbcType="VARCHAR" property="empId"/>
        <result column="EMP_NAME" jdbcType="VARCHAR" property="empName"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted"/>
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode"/>
        <result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode"/>
        <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <select id="selectAddressBookCollectList" resultType="cn.trasen.oa.hrm.model.AddressBookCollect"
            parameterType="cn.trasen.oa.hrm.model.AddressBookCollect">
       SELECT
        a.id,
        a.EMP_ID,
        a.EMP_NAME,
        g.`name` AS empDeptName,
        b.avatar AS empHeadImg,
        t3.position_name AS empDutyName,
        b.gender AS empSex
        FROM
        TOA_ADDRESS_BOOK_COLLECT a
        LEFT JOIN cust_emp_base b ON a.EMP_ID = b.employee_no
		LEFT JOIN comm_organization g ON b.org_id = g.organization_id
		left join comm_position as t3 on b.position_id = t3.position_id
        WHERE a.IS_DELETED = 'N'
        <if test="createUser !=null and createUser != ''">
            AND a.CREATE_USER = #{createUser}
        </if>
        <if test="empName !=null and empName!=''">
            AND a.EMP_NAME like CONCAT(CONCAT('%', #{empName}), '%')
        </if>
        <if test="empDeptName !=null and empDeptName!=''">
            AND g.name like CONCAT(CONCAT('%', #{empDeptName}), '%')
        </if>
        <if test="empDutyName !=null and empDutyName!=''">
            AND t3.position_name like CONCAT(CONCAT('%', #{empDutyName}), '%')
        </if>
   </select>

</mapper>