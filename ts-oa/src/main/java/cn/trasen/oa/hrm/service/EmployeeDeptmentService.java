package cn.trasen.oa.hrm.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.hrm.model.Employee;
import cn.trasen.oa.hrm.model.EmployeeDeptment;

import java.util.List;

/**
 * @Description: 员工关联科室
 * @Date: 2020/1/13 15:08
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
public interface EmployeeDeptmentService {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 查询员工关联科室列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: java.util.List<cn.trasen.oa.hrm.model.EmployeeTransfer>
     **/
    List<EmployeeDeptment> getDataList(Page page, EmployeeDeptment employeeDeptment);

    /**
     * @Author: <PERSON><PERSON><PERSON>o
     * @Description: 新增员工关联科室
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    int insert(EmployeeDeptment employeeDeptment);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 修改员工关联科室
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    int update(EmployeeDeptment employeeDeptment);

    /**
     * 根据员工ID删除员工关联科室
     *
     * @param empId
     * @return
     */
    int deleteByEmpId(String empId);


    /**
     * @Author: Lizhihuo
     * @Description: 新增员工关联科室(公共调用)
     * @Date: 2020/1/15 9:18
     * @Param: employee 员工信息  empId员工ID
     * @return: int
     **/
    int insertEmployeeDeptment(String empId, Employee employee);
}
