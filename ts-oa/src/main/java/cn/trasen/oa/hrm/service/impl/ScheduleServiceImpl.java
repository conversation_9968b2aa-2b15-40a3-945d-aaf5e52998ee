package cn.trasen.oa.hrm.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.hrms.CommInterfaceRegisterResp;
import cn.trasen.homs.bean.hrms.MedHisEmployeeResp;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.hrms.CommInterfaceRegisterFeignService;
import cn.trasen.homs.utils.HttpClient;
import cn.trasen.oa.hrm.dao.ScheduleMapper;
import cn.trasen.oa.hrm.model.CommHolidayYear;

import cn.trasen.oa.hrm.model.DoctorSheduleResp;
import cn.trasen.oa.hrm.model.Schedule;
import cn.trasen.oa.hrm.service.EmployeeService;
import cn.trasen.oa.hrm.service.ScheduleService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class ScheduleServiceImpl implements ScheduleService{
	
	@Value("${hisRequestVersion:}")
	private String hisRequestVersion;

	@Resource
	private ScheduleMapper scheduleMapper;
	
	@Resource
	private EmployeeService employeeService;
	
	@Resource
	private DictItemFeignService dictItemFeignService;
	
	@Resource
	private CommInterfaceRegisterFeignService commInterfaceRegisterFeignService;
	
	private static final String REGISTER_SCHEDULE_SUBJECT = "查询出多名患者的预约挂号信息，请点击查看详情！";
	
	@Override
	@Transactional(readOnly = false)
	public void insert(Schedule record) {
		record.setId(String.valueOf(IdWork.id.nextId()));
		record.setCreateDate(new Date());
		record.setCreateUser(UserInfoHolder.getCurrentUserCode());
		if(StringUtils.isNotBlank(record.getRemindTime()) && !"0".equals(record.getRemindTime())) {
			record.setIsRemind(1);
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		scheduleMapper.insertSelective(record);
		
		//新增以后的日程信息
		 if (record.getRepeatStatus() !=null && record.getRepeatStatus() > 0) {
	            addAfter(record);
	     }
	}
	
	@Transactional(readOnly = false)
	public void addAfter(Schedule record) {
		List<Schedule> scheduleList = new ArrayList<>();
        if (record.getRepeatStatus().equals(1)) {
            long betweenDay = DateUtil.betweenDay(record.getScheduleDate(), record.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                	Schedule schedule = BeanUtil.copyProperties(record, Schedule.class);
                	schedule.setId(String.valueOf(IdWork.id.nextId()));
                	schedule.setScheduleDate(
                            DateUtil.offset(record.getScheduleDate(), DateField.DAY_OF_YEAR, (1 + i))
                    );
                	scheduleList.add(schedule);
                }
            }
        } else if (record.getRepeatStatus().equals(2)) {
            long betweenDay = DateUtil.betweenWeek(record.getScheduleDate(), record.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                	Schedule schedule = BeanUtil.copyProperties(record, Schedule.class);
                	schedule.setId(String.valueOf(IdWork.id.nextId()));
                	schedule.setScheduleDate(
                            DateUtil.offset(record.getScheduleDate(), DateField.WEEK_OF_YEAR, (1 + i))
                    );
                	scheduleList.add(schedule);
                }
            }
        } else if (record.getRepeatStatus().equals(3)) {
        	 long betweenDay = DateUtil.betweenWeek(record.getScheduleDate(), record.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                	Schedule schedule = BeanUtil.copyProperties(record, Schedule.class);
                	schedule.setId(String.valueOf(IdWork.id.nextId()));
                	schedule.setScheduleDate(
                            DateUtil.offset(record.getScheduleDate(), DateField.MONTH, (1 + i))
                    );
                	scheduleList.add(schedule);
                }
            }
        }
        for (Schedule schedule : scheduleList) {
        	try {
        		scheduleMapper.insertSelective(schedule);
        	} catch (Exception ex) {
                log.error(JSON.toJSONString(schedule), ex);
            }
        }
        
	}

	@Override
	public List<String> selectScheduleDate(Map<String,String> params) {
		params.put("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return scheduleMapper.selectScheduleDate(params);
	}

	@Override
	public List<Schedule> selectSchedule(Schedule record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return scheduleMapper.selectSchedule(record);
	}

	@Override
	@Transactional(readOnly = false)
	public Schedule selectById(String id) {
		Schedule schedule = scheduleMapper.selectByPrimaryKey(id);
		if(null != schedule) {
			schedule.setIsRemind(2);
			scheduleMapper.updateByPrimaryKeySelective(schedule);
		}
		return schedule;
	}

	@Override
	@Transactional(readOnly = false)
	public void update(Schedule record) {
		if(StringUtils.isNotBlank(record.getRemindTime()) 
				&& !"0".equals(record.getRemindTime())) {
			record.setIsRemind(1);
		}
		scheduleMapper.updateByPrimaryKeySelective(record);
	}

	@Override
	@Transactional(readOnly = false)
	public void delete(String id) {
		scheduleMapper.deleteByPrimaryKey(id);
	}

	@Override
	public List<Schedule> selectRemindSchedule() {
		return scheduleMapper.selectRemindSchedule();
	}

	@Override
	@Transactional(readOnly = false)
	public void batchInsert(List<Schedule> record) {
		if(record != null && record.size() > 0) {
			
			for (Schedule schedule : record) {
				schedule.setIsHys("1");
				schedule.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				scheduleMapper.insertSelective(schedule);
			}
		}
	}

	@Override
	public Map<String, List<Schedule>> getScheduleListByDate(Schedule record) {
		//根据当前账号机构编码过滤
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<Schedule> list = scheduleMapper.getScheduleListByDate(record);
		
		//查询当月生日员工信息
//		String scheduleDateStr = record.getScheduleDateStr();
//		String[] split = scheduleDateStr.split("-");
//		
//		List<Employee> empList = employeeService.getBirthdayByMonth(split[1]);
//		
//		for (Employee employee : empList) {
//			Schedule schedule = new Schedule();
//			schedule.setScheduleStartTime("全天");
//			schedule.setScheduleSubject(employee.getEmpName() + "生日");
//			String[] split2 = employee.getBirth().split("-");
//			String dateStr = DateUtil.year(new Date()) + "-" +  split2[1] + "-" + split2[2];
//			String[] split3 = dateStr.split(" ");
//			schedule.setScheduleDateStr(split3[0]);
//			schedule.setScheduleEndTime("全天");
//			schedule.setId(String.valueOf(IdWork.id.nextId()));
//			schedule.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
//			schedule.setScheduleType("生日提醒");
//			list.add(schedule);
//		}
		
		Map<String, List<Schedule>> collect = list.stream().collect(Collectors.groupingBy(Schedule::getScheduleDateStr));
		return collect;
	}

	@Override
	public List<Schedule> selectRemindScheduleCurrent() {
		return scheduleMapper.selectRemindScheduleCurrent();
	}

	@Override
	public List<Schedule> selectScheduleTime(String id,String scheduleDate, String scheduleStartTime, String currentUserCode) {
		return scheduleMapper.selectScheduleTime(id,scheduleDate,scheduleStartTime,currentUserCode,UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMeetingData(Date meetingDate, String startTime, String endTime) {
		scheduleMapper.deleteMeetingData(DateUtil.format(meetingDate, "yyyy-MM-dd"), startTime, endTime,UserInfoHolder.getCurrentUserCorpCode());
	}
	
	/**
	 * 获取 医生预约挂号信息是否在日程中提醒 标识
	 * @return
	 */
	private boolean getDoctorSheduleFlag(){
		//根据数据字典 CUSTOM_CODE  DOCTOT_SHEDULE 1-开启，0-禁用  医生预约挂号信息是否在日程中提醒
		PlatformResult<List<DictItemResp>> dictItemByTypeCodeResult = dictItemFeignService.getDictItemByTypeCodeAndSsoOrgCode("CUSTOM_CODE", "*PUBLIC*");
		if(dictItemByTypeCodeResult.isSuccess()){
			List<DictItemResp> dictItemRespList = dictItemByTypeCodeResult.getObject();
			for(DictItemResp dict : dictItemRespList){
				String itemCode = dict.getItemCode();
				String itemNameValue = dict.getItemNameValue();
				if(!ObjectUtils.isEmpty(itemNameValue) && "DOCTOT_SHEDULE".equals(itemCode) && "1".equals(itemNameValue)){
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * 查询患者挂号信息
	 * @param commInterfaceRegister
	 * @return
	 */
	private JSONArray getOutPatient(CommInterfaceRegisterResp commInterfaceRegister, String startRegDate, String endRegDate, String doctorId){
		Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
		log.info("===========开始获取患者预约挂号数据================");
		Map<String,Object> requestParams = new HashMap<>();
		//拉取his挂号一周的数据
		//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
		if(StringUtils.isNotBlank(hisRequestVersion) && "2".equals(hisRequestVersion)) {
			requestParams.put("IsCompress", "false");
			requestParams.put("ServiceName", "OuterPatPayService");//服务名
			requestParams.put("InterfaceName", "QueryOutPatient");//接口名
			requestParams.put("TimeOut", 15000);//超时时间
			
			Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	       	paramsMap.put("pageIndex", "1");
	       	paramsMap.put("pageSize", "1000000");
	    	paramsMap.put("sortOrder", "0");
	    	paramsMap.put("regTypes", ListUtil.of("1","2"));
	    	paramsMap.put("startRegDate", startRegDate);
	    	paramsMap.put("endRegDate", endRegDate);
	    	if(!ObjectUtils.isEmpty(doctorId)){
	    		paramsMap.put("doctorId", doctorId);
	    	}
	       	
	       	requestParams.put("Parameter",paramsMap);
	       	
		}else {
			requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
			requestParams.put("pageIndex", "1");
			requestParams.put("pageSize", "1000000");
			requestParams.put("sortOrder", "0");
			requestParams.put("regTypes", ListUtil.of("1","2"));
			requestParams.put("startRegDate", startRegDate);
			requestParams.put("endRegDate", endRegDate);
	    	if(!ObjectUtils.isEmpty(doctorId)){
	    		requestParams.put("doctorId", doctorId);
	    	}
		}
		
		String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
		
		String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
		
		String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
		
		JSONObject reuslt = JSON.parseObject(bodyStr);
		
		JSONArray jsonArray = new JSONArray();
		if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
			JSONObject data = reuslt.getJSONObject("data");
			JSONObject Value = data.getJSONObject("Value");
			jsonArray = Value.getJSONArray("list");
		}else {
			jsonArray = reuslt.getJSONArray("list");
		}
		log.info("===========获取患者预约挂号数据结束================");
		return jsonArray;
	}

	@Override
	public List<DoctorSheduleResp> getDoctorShedule(Schedule record) {
		List<DoctorSheduleResp> result = new ArrayList<DoctorSheduleResp>();
		
		DataSet<CommInterfaceRegisterResp> dataSet = commInterfaceRegisterFeignService.selectCommInterfaceRegisterList("2", "集成平台-查询挂号信息", "1");
		List<CommInterfaceRegisterResp> list = dataSet.getRows();
		if(CollUtil.isNotEmpty(list)){
			CommInterfaceRegisterResp commInterfaceRegister = list.get(0);
			Date scheduleDate = record.getScheduleDate();
			if(null == scheduleDate){
				return Collections.emptyList();
			}
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String startRegDate = sdf.format(scheduleDate) + " 00:00:00";
			String endRegDate = sdf.format(scheduleDate) + " 23:59:59";
			
			//查询med_his_employee表获取his的用户，和cust_emp_base 的employee_no关联
			List<MedHisEmployeeResp> hisEmpList = scheduleMapper.selectMedHisEmployeeResp();
			if(CollUtil.isEmpty(hisEmpList)){
				return Collections.emptyList();
			}
			String doctorId = null;
            if (hisEmpList != null && hisEmpList.size() > 0) {
            	for(MedHisEmployeeResp sc : hisEmpList){
            		if(UserInfoHolder.getCurrentUserCode().equals(sc.getEmployeeNo()) && UserInfoHolder.getCurrentUserCorpCode().equalsIgnoreCase(sc.getSsoOrgCode())){
            			doctorId = sc.getEmployeeId();
            			break;
            		}
            	}
            }
            if(null != doctorId){
            	JSONArray jsonArray = getOutPatient(commInterfaceRegister, startRegDate, endRegDate, doctorId);
            	log.info("获取患者挂号信息的数据：" + jsonArray.toJSONString());
            	
            	if(null != jsonArray && jsonArray.size() > 0) {
            		for (int i = 0; i < jsonArray.size(); i++) {
            			DoctorSheduleResp doctorShedule = new DoctorSheduleResp();
            			JSONObject obj = jsonArray.getJSONObject(i);
            			doctorShedule.setSsoOrgCode(obj.getString("orgCode"));//医院编码
            			doctorShedule.setQueueNo(Integer.valueOf(obj.getString("queueNo")));//排队号
            			doctorShedule.setPatientName(obj.getString("patientName"));//患者姓名
            			doctorShedule.setSexName(obj.getString("sexName"));//患者性别
            			doctorShedule.setAge(obj.getString("age"));//患者年龄
            			doctorShedule.setVisitNo(obj.getString("visitNo"));//门诊号
            			String status = obj.getString("status");//0-正常 1-取消 2-待取号
            			if(!ObjectUtils.isEmpty(status) && !"0".equals(status)){//只返回正常的挂号信息
            				continue;
            			}
            			String regSource = obj.getString("regSource");//挂号来源：0-未知 1-窗口2-自助设备3-移动终端 4-医生站 12-互联网医院 88-体检 99-转诊
            			if(!ObjectUtils.isEmpty(regSource)){
            				switch(regSource){
	            				case "0":
	            					doctorShedule.setRegSource("未知");
	            					break;
	            				case "1":
	            					doctorShedule.setRegSource("窗口");
	            					break;
	            				case "2":
	            					doctorShedule.setRegSource("自助设备");
	            					break;
	            				case "3":
	            					doctorShedule.setRegSource("移动终端");
	            					break;
	            				case "4":
	            					doctorShedule.setRegSource("医生站");
	            					break;
	            				case "12":
	            					doctorShedule.setRegSource("互联网医院");
	            					break;
	            				case "88":
	            					doctorShedule.setRegSource("体检");
	            					break;
	            				case "99":
	            					doctorShedule.setRegSource("转诊");
	            					break;
            				}
            			}
            			doctorShedule.setMobile(obj.getString("mobile"));//联系电话
            			doctorShedule.setRegDate(obj.getString("regDate"));//挂号日期
            			result.add(doctorShedule);
            		}
            	}
            }
		}
		if(result.size() > 0){
			//按照排队号升序排序
			Collections.sort(result, (p1, p2) -> Integer.compare(p1.getQueueNo(), p2.getQueueNo()));
		}
		return result;
	}

	@Override
	@Transactional(readOnly = false)
	public void generateRegisterSchedule() {
		//医生预约挂号信息是否在日程中提醒开关，默认关闭
		if(getDoctorSheduleFlag()){
			DataSet<CommInterfaceRegisterResp> dataSet = commInterfaceRegisterFeignService.selectCommInterfaceRegisterList("2", "集成平台-查询挂号信息", "1");
			List<CommInterfaceRegisterResp> list = dataSet.getRows();
			if(CollUtil.isNotEmpty(list)){
				CommInterfaceRegisterResp commInterfaceRegister = list.get(0);
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Calendar ca = Calendar.getInstance();
				String startRegDate = sdf.format(ca.getTime()) + " 00:00:00";
				ca.add(Calendar.DAY_OF_YEAR, 7);
				String endRegDate = sdf.format(ca.getTime()) + " 23:59:59";
				
				JSONArray jsonArray = getOutPatient(commInterfaceRegister, startRegDate, endRegDate, null);
				
				if(null != jsonArray && jsonArray.size() > 0) {
					//查询当前日期内的挂号信息日程数据，用于判断是否重复
					Schedule queryRecord = new Schedule();
					queryRecord.setStartDate(startRegDate);
					queryRecord.setEndDate(endRegDate);
					queryRecord.setScheduleCategory("1");
					List<Schedule> scheduleList = scheduleMapper.selectSchedule(queryRecord);
					List<String> doctorScheduleList = new ArrayList<>();
                    if (scheduleList != null && scheduleList.size() > 0) {
                    	for(Schedule sc : scheduleList){
                    		//机构编码转小写，便于匹配his的机构编码
                    		doctorScheduleList.add(sc.getCreateUser() + "&" + sc.getSsoOrgCode().toLowerCase() + "&" + sc.getScheduleDate());
                    	}
                    }
                    
					//查询med_his_employee表获取his的用户，和cust_emp_base 的employee_no关联
					List<MedHisEmployeeResp> hisEmpList = scheduleMapper.selectMedHisEmployeeResp();
					if(CollUtil.isEmpty(hisEmpList)){
						return;
					}
					Map<String, MedHisEmployeeResp> hisEmpMap = new HashMap<>();
                    if (hisEmpList != null && hisEmpList.size() > 0) {
                    	for(MedHisEmployeeResp sc : hisEmpList){
                    		//机构编码转小写，便于匹配his的机构编码
                    		hisEmpMap.put(sc.getEmployeeId() + "&" + sc.getSsoOrgCode().toLowerCase(), sc);
                    	}
                    }

	       			List<String> existDoctorId = new ArrayList<>();//用于去重
					for (int i = 0; i < jsonArray.size(); i++) {
		       			JSONObject obj = jsonArray.getJSONObject(i);
		       			String orgCode = obj.getString("orgCode").toLowerCase();//医院编码，转成小写用于匹配
		       			String regDate = obj.getString("regDate");//挂号日期
		       			try {
							Date date = sdf.parse(regDate);
							regDate = sdf.format(date);
						} catch (ParseException e1) {
							log.info("挂号日期转换报错");
						}
		       			
//		       			map.put("regType", obj.getString("regType"));//挂号类型 1-门诊 2-急诊 3-体检
		       			String doctorId = obj.getString("doctorId");//挂号医生id
		       			//判断是否已经存在日程，是否有关联的医生
		       			if(hisEmpMap.containsKey(doctorId + "&" + orgCode) && !existDoctorId.contains(doctorId + "&" + orgCode + regDate)){
		       				existDoctorId.add(doctorId + "&" + orgCode + regDate);
		       				MedHisEmployeeResp employee = hisEmpMap.get(doctorId + "&" + orgCode);
		       				if(!doctorScheduleList.contains(employee.getEmployeeNo() + "&" + orgCode + regDate)){
		       					//添加日程数据
		       					Schedule record = new Schedule();
		       					record.setId(String.valueOf(IdWork.id.nextId()));
		       					record.setCreateDate(new Date());
		       					record.setCreateUser(employee.getEmployeeNo());
		       					try {
									record.setScheduleDate(sdf.parse(regDate));
								} catch (ParseException e) {
									log.error("挂号日期转换异常！");
								}
		       					record.setIsRemind(0);
		       					record.setSsoOrgCode(orgCode);
		       					record.setScheduleSubject(REGISTER_SCHEDULE_SUBJECT);
		       					record.setScheduleContent(REGISTER_SCHEDULE_SUBJECT);
		       					record.setScheduleType("工作日程");
		       					record.setScheduleCategory("1");
		       					record.setScheduleStartTime("00:00");
		       					record.setScheduleEndTime("23:59");
		       					scheduleMapper.insertSelective(record);
		       				}
		       			}
		       			
					}
				}
			}
			
		}
	}

	@Override
	public List<CommHolidayYear> getHolidayList(Schedule record) {
		String scheduleDateStr = record.getScheduleDateStr();
		String[] split = scheduleDateStr.split("-");
		
		return scheduleMapper.getHolidayList(split[0], UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	public Map<String, List<String>> getHolidayDateList(String startTime, String endTime, String ssoOrgCode) {
		Map<String, List<String>> holidayDateMap = new HashMap<>();
		try {
			List<String> holidayList = new ArrayList<>();//节假日
			List<String> compensatoryDayList = new ArrayList<>();//调休日
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
			String startYear = startTime.split("-")[0];
			setHolidayDateList(scheduleMapper.getHolidayList(startYear, ssoOrgCode), holidayList, compensatoryDayList);
			String endYear = endTime.split("-")[0];
			while(!startYear.equals(endYear)){//加一年
				Date start = sdf.parse(startYear);
				Calendar calendar = Calendar.getInstance();
		        calendar.setTime(start);
		        calendar.add(Calendar.YEAR, 1); // 加1年（减1年用-1）
		        Date nextYear = calendar.getTime();
		        startYear = sdf.format(nextYear);
		        setHolidayDateList(scheduleMapper.getHolidayList(startYear, ssoOrgCode), holidayList, compensatoryDayList);
			}
			holidayDateMap.put("holiday", holidayList);
			holidayDateMap.put("compensatoryDay", compensatoryDayList);
			return holidayDateMap;
		} catch (ParseException e) {
			
		}
		return holidayDateMap;
	}
	
	/**
	 * 解析节假日和调休日
	 * @param holidayYearList
	 * @param holidayList
	 * @param compensatoryDayList
	 */
	private void setHolidayDateList(List<CommHolidayYear> holidayYearList, List<String> holidayList, List<String> compensatoryDayList){
		for(CommHolidayYear holidayYear : holidayYearList){
			String vacation = holidayYear.getVacation();//节假日数组
			String remark = holidayYear.getRemark();//调休日数组
			if(!ObjectUtils.isEmpty(vacation)){
				String[] vacationArr = vacation.split("\\|");
				for(String v : vacationArr){
					holidayList.add(v);
				}
			}
			if(!ObjectUtils.isEmpty(remark)){
				String[] remarkArr = remark.split("\\|");
				for(String v : remarkArr){
					compensatoryDayList.add(v);
				}
			}
		}
	}
	
}
