package cn.trasen.oa.hrm.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.trasen.homs.bean.sso.ThpsDept;
import cn.trasen.oa.hrm.excel.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "TOA_EMPLOYEE")
@Setter
@Getter
public class Employee {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 发薪号
     */
    @Column(name = "EMP_PAYROLL")
    @ApiModelProperty(value = "发薪号")
    private String empPayroll;

    /**
     * 部门ID
     */
    @Column(name = "EMP_DEPT_ID")
    @ApiModelProperty(value = "部门ID")
    private String empDeptId;

    /**
     * 部门名称
     */
    @Column(name = "EMP_DEPT_NAME")
    @ApiModelProperty(value = "部门名称")
    @ExcelColumn(name = "部门", index = 3, width = 25)
    private String empDeptName;

    /**
     * 部门Code
     */
    @Column(name = "EMP_DEPT_CODE")
    @ApiModelProperty(value = "部门Code")
    private String empDeptCode;

    /**
     * 员工姓名
     */
    @Column(name = "EMP_NAME")
    @ApiModelProperty(value = "员工姓名")
    @ExcelColumn(name = "员工姓名", index = 1, width = 20)
    private String empName;

    /**
     * 员工工号
     */
    @Column(name = "EMP_CODE")
    @ApiModelProperty(value = "员工工号")
    private String empCode;

    /**
     * 密码
     */
    @Transient
    @ApiModelProperty(value = "密码")
    private String empPassword;

    /**
     * 员工昵称
     */
    @Column(name = "EMP_NICK_NAME")
    @ApiModelProperty(value = "员工昵称")
    @Deprecated
    private String empNickName;

    /**
     * 性别  0：男  1：女
     */
    @Column(name = "EMP_SEX")
    @ApiModelProperty(value = "性别  0：男  1：女")
    private Short empSex;

    /**
     * 员工电话
     */
    @Column(name = "EMP_PHONE")
    @ApiModelProperty(value = "员工电话")
    @ExcelColumn(name = "手机", index = 5, width = 20)
    private String empPhone;
    
    /**
     * 员工电话2
     */
    @Column(name = "EMP_PHONE_SECOND")
    @ApiModelProperty(value = "员工电话2")
    @ExcelColumn(name = "手机2", index = 6, width = 20)
    private String empPhoneSecond;

    /**
     * 移动短号
     */
    @Column(name = "EMP_BUSINESS_PHONE")
    @ApiModelProperty(value = "移动短号")
    @ExcelColumn(name = "移动短号", index = 7, width = 20)
    private String empBusinessPhone;
    
    /**
     * 电信短号
     */
    @Column(name = "EMP_TELECOM_BUSINESS_PHONE")
    @ApiModelProperty(value = "电信短号")
    @ExcelColumn(name = "电信短号", index = 8, width = 20)
    private String empTelecomBusinessPhone;
    
    /**
     * 联通短号
     */
    @Column(name = "EMP_UNICOM_BUSINESS_PHONE")
    @ApiModelProperty(value = "联通短号")
    @ExcelColumn(name = "联通短号", index = 9, width = 20)
    private String empUnicomBusinessPhone;

    /**
     * 办公电话
     */
    @Column(name = "EMP_SHORT_PHONE")
    @ApiModelProperty(value = "办公电话")
    @ExcelColumn(name = "办公电话", index = 10, width = 20)
    private String empShortPhone;

    /**
     * 头像,存放头像文件的路径
     */
    @Column(name = "EMP_HEAD_IMG")
    @ApiModelProperty(value = "头像,存放头像文件的路径")
    private String empHeadImg;

    /**
     * 邮件签名
     */
    @Column(name = "EMP_SIGNIMG")
    @ApiModelProperty(value = "邮件签名")
    private String empSignimg;

    /**
     * 签章图片
     */
    @Column(name = "SIGNATURE_IMG_NAME")
    @ApiModelProperty(value = "签章图片")
    private String signatureImgName;

    /**
     * 签章图片存储名
     */
    @Column(name = "SIGNATURE_IMGSAVE_NAME")
    @ApiModelProperty(value = "签章图片存储名")
    @Deprecated
    private String signatureImgsaveName;

    /**
     * 入司（职）日期
     */
    @Column(name = "EMP_HIREDATE")
    @ApiModelProperty(value = "入司（职）日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date empHiredate;

    /**
     * 离职日期
     */
    @Column(name = "EMP_FIREDATE")
    @ApiModelProperty(value = "离职日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Deprecated
    private Date empFiredate;

    /**
     * 离职原因
     */
    @Column(name = "FIRE_REASON")
    @ApiModelProperty(value = "离职原因")
    @Deprecated
    private String fireReason;

    /**
     * 身份证号
     */
    @Column(name = "EMP_IDCARD")
    @ApiModelProperty(value = "身份证号")
    private String empIdcard;

    /**
     * 出生年月
     */
    @Column(name = "EMP_BIRTH")
    @ApiModelProperty(value = "出生年月")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date empBirth;

    /**
     * 员工年龄
     */
    @Column(name = "EMP_AGE")
    @ApiModelProperty(value = "员工年龄")
    private Short empAge;

    /**
     * 员工状态  1：正常  2：停用  
     */
    @Column(name = "EMP_STATUS")
    @ApiModelProperty(value = "员工状态  1：正常  4：离职  5：延聘  6：院内返聘  7：死亡  8：退休   9：借调  10：长期病休  99试用期  88专属账号")
    private Short empStatus;

    /**
     * 照片
     */
    @Column(name = "EMP_PHOTO")
    @ApiModelProperty(value = "照片")
    @Deprecated
    private String empPhoto;

    /**
     * 民族
     */
    @Column(name = "EMP_NATION")
    @ApiModelProperty(value = "民族")
    @Deprecated
    private String empNation;

    /**
     * 籍贯
     */
    @Column(name = "EMP_NATIVEPLACE")
    @ApiModelProperty(value = "籍贯")
    @Deprecated
    private String empNativeplace;

    /**
     * 婚姻状况  1: 已婚 2 未婚 3 保密
     */
    @Column(name = "EMP_IS_MARRIAGE")
    @ApiModelProperty(value = "婚姻状况  1: 已婚 2 未婚 3 保密")
    @Deprecated
    private Short empIsMarriage;

    /**
     * 政治面貌
     */
    @Column(name = "EMP_POLITY")
    @ApiModelProperty(value = "政治面貌")
    @Deprecated
    private String empPolity;

    /**
     * 现居住地址
     */
    @Column(name = "EMP_ADDRESS")
    @ApiModelProperty(value = "现居住地址")
    @Deprecated
    private String empAddress;

    /**
     * 身份证地址
     */
    @Column(name = "EMP_CARD_ADDRESS")
    @ApiModelProperty(value = "身份证地址")
    @Deprecated
    private String empCardAddress;

    /**
     * 电子邮箱
     */
    @Column(name = "EMP_EMAIL")
    @ApiModelProperty(value = "电子邮箱")
    @ExcelColumn(name = "电子邮件", index = 4, width = 25)
    private String empEmail;

    /**
     * 职务ID
     */
    @Column(name = "EMP_DUTY_ID")
    @ApiModelProperty(value = "职务ID")
    private String empDutyId;

    /**
     * 职务名称
     */
    @Column(name = "EMP_DUTY_NAME")
    @ApiModelProperty(value = "职务名称")
    private String empDutyName;

    /**
     * 职称ID
     */
    @Column(name = "EMP_TITLE_ID")
    @ApiModelProperty(value = "职称ID")
    @Deprecated
    private String empTitleId;

    /**
     * 职称名称
     */
    @Column(name = "EMP_TITLE_NAME")
    @ApiModelProperty(value = "职称名称")
    private String empTitleName;

    /**
     * 员工类型（非编制、正式、在编、合同）
     */
    @Column(name = "EMP_TYPE")
    @ApiModelProperty(value = "员工类型（非编制、正式、在编、合同）")
    private String empType;

    /**
     * 用户帐号
     */
    @Column(name = "USER_ACCOUNTS")
    @ApiModelProperty(value = "用户帐号")
    private String userAccounts;

    /**
     * 用户简码
     */
    @Column(name = "USER_SIMPLE_NAME")
    @ApiModelProperty(value = "用户简码")
    @Deprecated
    private String userSimpleName;

    /**
     * 是否iKey验证 0 不需要验证 1 需要
     */
    @Column(name = "KEY_VALIDATE")
    @ApiModelProperty(value = "是否iKey验证 0 不需要验证 1 需要")
    @Deprecated
    private Short keyValidate;

    /**
     * iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）
     */
    @Column(name = "KEY_SERIAL")
    @ApiModelProperty(value = "iKey序列（从其他的地方同步过来了、业务不做处理、后面有接口要用）")
    @Deprecated
    private String keySerial;

    /**
     * 是否是域验证 否：0，是：1
     */
    @Column(name = "IS_AD_CHECK")
    @ApiModelProperty(value = "是否是域验证 否：0，是：1")
    @Deprecated
    private String isAdCheck;

    /**
     * 是否是休眠用户 否：0，是：1
     */
    @Column(name = "USER_IS_SLEEP")
    @ApiModelProperty(value = "是否是休眠用户 否：0，是：1")
    @Deprecated
    private String userIsSleep;

    /**
     * 是否接收短信提醒  否：0，是：1
     */
    @Column(name = "IS_SMS_REMINDER")
    @ApiModelProperty(value = "是否接收短信提醒  否：0，是：1")
    private String isSmsReminder;

    /**
     * 是否接收语音提醒  否：0，是：1
     */
    @Column(name = "IS_VOICE_REMINDER")
    @ApiModelProperty(value = "是否接收语音提醒  否：0，是：1")
    private String isVoiceReminder;

    /**
     * 是否接收微信消息推送  否：0，是：1
     */
    @Column(name = "IS_WX_REMINDER")
    @ApiModelProperty(value = "是否接收微信消息推送  否：0，是：1")
    private String isWxReminder;

    /**
     * 是否显示个人手机号码  否：0，是：1
     */
    @Column(name = "IS_DISPLAY_PHONE_NO")
    @ApiModelProperty(value = "是否显示个人手机号码  否：0，是：1")
    private String isDisplayPhoneNo;

    /**
     * 是否使用电子签章  否：0，是：1
     */
    @Column(name = "IS_USE_SIGNATURE")
    @ApiModelProperty(value = "是否使用电子签章  否：0，是：1")
    private String isUseSignature;

    /**
     * 是否开启生日显示保护  否：0，是：1
     */
    @Column(name = "IS_BIRTHDAY_PROTECT")
    @ApiModelProperty(value = "是否开启生日显示保护  否：0，是：1")
    @Deprecated
    private String isBirthdayProtect;

    /**
     * 是否活动用户  0 :不是  1：是
     */
    @Column(name = "USER_ISACTIVE")
    @ApiModelProperty(value = "是否活动用户  0 :不是  1：是")
    @Deprecated
    private String userIsactive;

    /**
     * 是否正式用户  0:不是 1：是
     */
    @Column(name = "USER_ISFORMALUSER")
    @ApiModelProperty(value = "是否正式用户  0:不是 1：是")
    @Deprecated
    private String userIsformaluser;

    /**
     * 是否特权用户  0：不是  1：是
     */
    @Column(name = "USER_ISSUPER")
    @ApiModelProperty(value = "是否特权用户  0：不是  1：是")
    @Deprecated
    private String userIssuper;

    /**
     * 特权开始时间
     */
    @Column(name = "USER_SUPER_BEGIN")
    @ApiModelProperty(value = "特权开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Deprecated
    private Date userSuperBegin;

    /**
     * 特权结束时间
     */
    @Column(name = "USER_SUPER_END")
    @ApiModelProperty(value = "特权结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Deprecated
    private Date userSuperEnd;

    /**
     * 院区编码
     */
    @Column(name = "BROWSERANGE")
    @ApiModelProperty(value = "")
    @Deprecated
    private String browserange;

    /**
     * 院区名称
     */
    @Column(name = "BROWSERANGE_NAME")
    @ApiModelProperty(value = "默认组织范围名称")
    @Deprecated
    private String browserangeName;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人编码
     */
    @Column(name = "CREATE_USER_CODE")
    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    @Deprecated
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 用户是否删除 N 未被删除 Y 已被删除
     */
    @Column(name = "USER_IS_DELETED")
    @ApiModelProperty(value = "用户是否删除 N 未被删除 Y 已被删除")
    @Deprecated
    private String userIsDeleted;

    /**
     * 企业微信授权用户ID
     */
    @Column(name = "OPEN_ID")
    @ApiModelProperty(value = "企业微信授权用户ID")
    private String openId;

    /**
     * 用户拼音
     */
    @Column(name = "EMP_NAME_PINYIN")
    @ApiModelProperty(value = "用户拼音")
    private String empNamePinyin;
    
    /**
     * 工龄
     */
    @Column(name = "YEAR_WORK")
    @ApiModelProperty(value = "工龄")
    private String yearWork;
    
    /**
     * 年假天数
     */
    @Column(name = "YEAR_DAYS")
    @ApiModelProperty(value = "年假天数")
    private String yearDays;
    
    /**
     * 已修年假天数
     */
    @Column(name = "YEAR_NUMBER")
    @ApiModelProperty(value = "已修年假天数")
    private String yearNumber;
    
    /**
     * 允许上传附件大小（M）
     */
    @Column(name = "UPLOAD_FILE_SIZE")
    @ApiModelProperty(value = "允许上传附件大小（M）")
    private String uploadFileSize;
    
    
    /**
     * 代理人姓名
     */
    @Column(name = "agent_name")
    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    /**
     * 代理人ID
     */
    @Column(name = "agent_id")
    @ApiModelProperty(value = "代理人ID")
    private String agentId;
    
    /**
     * 是否启用流程代理   否：0，是：1
     */
    @Column(name = "is_enable_process_agent")
    @ApiModelProperty(value = "是否启用流程代理   否：0，是：1 ")
    private Short isEnableProcessAgent;
    

    /**
     * 代理开始时间
     */
    @Column(name = "agent_start_time")
    @ApiModelProperty(value = "代理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date agentStartTime;

    /**
     * 代理结束时间
     */
    @Column(name = "agent_end_time")
    @ApiModelProperty(value = "代理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date agentEndTime;


    @Transient
    private String moveIn;

    /**
     * 用来定义搜索时间、操作方法
     */
    @Transient
    private String method;

    /**
     * 员工ID字符串
     */
    @Transient
    @ApiModelProperty(value = "员工ID字符串")
    private String empIdList;

    /**
     * 兼职科室编码
     */
    @Transient
    @ApiModelProperty(value = "兼职科室编码")
    private String deptIdList;

    /**
     * 兼职科室名称
     */
    @Transient
    @ApiModelProperty(value = "兼职科室名称")
    private String deptNameList;

    /**
     * 附件id
     */
    @Transient
    @ApiModelProperty(value = "附件ID")
    private String uploadedFile;

    /**
     * 科室主任
     */
    @Transient
    @ApiModelProperty(value = "科室主任")
    private String deptDirector;

    @Transient
    private String corpcode;

    /**
     * 用来判断是不是管理员
     */
    @Transient
    private String isHrmAdmin;

    /**
     * 科室秘书
     */
    @Transient
    @ApiModelProperty(value = "科室秘书")
    private String clerkName;

    /**
     * 护士长
     */
    @Transient
    @ApiModelProperty(value = "护士长")
    private String headnurseName;

    /**
     * 医疗主任
     */
    @Transient
    @ApiModelProperty(value = "医疗主任")
    private String medicalDirectorName;

    /**
     * 部门领导
     */
    @Transient
    @ApiModelProperty(value = "部门领导")
    private String departmentHeadName;

    /**
     * 分管领导
     */
    @Transient
    @ApiModelProperty(value = "分管领导")
    private String managementLeadName;

    /**
     * 直接领导
     */
    @Transient
    @ApiModelProperty(value = "直接领导")
    private String directLeadershipName;

    /**
     * 部门长
     */
    @Transient
    @ApiModelProperty(value = "部门长")
    private String departmentPhorName;

    /**
     * 性别名称
     */
    @Transient
    @ApiModelProperty(value = "性别名称")
    //@ExcelColumn(name = "性别", index = 2, width = 10)
    private String empSexName;

    /**
     * 状态名称
     */
    @Transient
    @ApiModelProperty(value = "状态名称")
    private String empStatusName;

    /**
     * 个人简介
     */
    @Transient
    @ApiModelProperty(value = "个人简介")
    private String empDescribe;

    /**
     * 学历
     */
    @Transient
    @ApiModelProperty(value = "学历")
    private String empStudyExperience;

    /**
     * 学位
     */
    @Transient
    @ApiModelProperty(value = "学位")
    private String empDegree;

    /**
     * 毕业日期
     */
    @Transient
    @ApiModelProperty(value = "毕业日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduateDate;

    /**
     * 职业
     */
    @Transient
    @ApiModelProperty(value = "职业")
    private String empPosition;

    /**
     * 职称
     */
    @Transient
    @ApiModelProperty(value = "职称")
    private String empPosttitle;
    
    @Transient
    @ApiModelProperty(value = "岗位类型")
    private String postType;
    

    /**
     * 部门实体
     */
    @Transient
    @ApiModelProperty(value = "部门实体")
    private ThpsDept thpsDept;


    /**
     * 入司日期 导出数据所用
     */
    @Transient
    @ApiModelProperty(value = "入司日期 导出数据所用")
    private String hiredate;

    /**
     * 出生年月 导出数据所用
     */
    @Transient
    @ApiModelProperty(value = "出生年月 导出数据所用")
    private String birth;

    /**
     * 员工集合（给微信端流程代理用）
     */
    @Transient
    @ApiModelProperty(value = "员工集合（给微信端流程代理用）")
    private List<Employee> empList;
    
    @Transient
    @ApiModelProperty(value = "是否查询子数据  Y是  N否")
    private String searchChild;
    
    
    @Transient
    @ApiModelProperty(value = "查询用部门编号")
    private String deptCodeSeach;
    
    @Transient
    @ApiModelProperty(value = "人员查询页面参数")
    private String userSel;
    
    @Transient
    private List<String> deptChildsList;
    
    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String postName;
    
    @Transient
    @ApiModelProperty(value = "岗位id")
    private String postId;
    
    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入院日期(入职日期)")
    private Date entryDate;
    
    @Transient
    @ApiModelProperty(value = "转正日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String positiveTime;

    @Transient
    @ApiModelProperty(value = "排序字段")
    private String sidx;
    
    @Transient
    @ApiModelProperty(value = "排序")
    private String sord;
    
    @Transient
    @ApiModelProperty(value = "人员类别")
    private String orgAttributes;
    
    @Transient
    @ApiModelProperty(value = "岗位属性")
    private String jobAttributes;
    
    
    @Transient
    @ApiModelProperty(value = "当前默认科室id")
    private String organizationParttimeId;
    
    @Transient
    @ApiModelProperty(value = "当前默认科室名称")
    private String organizationParttimeName;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
    
    @Transient
    private String carNo;
    
    @Transient
    private String seachOrgChildAll;
    
    @Transient
    private String orgFlag;
    
    @Transient
    private String seachKey;
    
    @Transient
    private List<String> ssoOrgCodeList;
    
    @Transient
    private List<String> orgUserCodeList;

    @Transient
    @ApiModelProperty(value = "员工当前薪酬组id")
    private String optionId;

    @Transient
    @ApiModelProperty(value = "员工当前薪酬组")
    private String optionName;

    @Transient
    @ApiModelProperty(value = "岗位名称(薪酬)")
    private String plgw;

    @Transient
    @ApiModelProperty(value = "岗位等级(薪酬)")
    private String gwdj;

    @Transient
    @ApiModelProperty(value = "岗位等级id")
    private String postLevelId;

    @Transient
    @ApiModelProperty(value = "薪级等级id")
    private String salaryLevelId;

    @Transient
    @ApiModelProperty(value = "薪级等级(薪酬)")
    private String salaryLevel;

    @Transient
    @ApiModelProperty(value = "员工状态(薪酬)")
    private String employeeStatus;
    
    @Transient
    private String workStartDate;
    
    @Transient
    private String technical;
    
    @Transient
    private String hospName;
    
    @Transient
    private String educationTypeName;//最高学历
    
    @Transient
    private String operationScope;//执业范围
    
    @Transient
    private String operationName;//执业类别
    
    @Transient
    private String assessmentDate;//职称评定日期
    
    @Transient
    private String inauguralDate;//职称任职日期
    
    
}
