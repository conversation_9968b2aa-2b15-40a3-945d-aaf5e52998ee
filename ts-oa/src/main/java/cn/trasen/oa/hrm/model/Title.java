package cn.trasen.oa.hrm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 职称表
 * @Date: 2020/5/14 14:55
 * @Param:
 * @return:
 **/
@Table(name = "TOA_TITLE")
@Setter
@Getter
public class Title {
    /**
     * ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 职称编号
     */
    @Column(name = "TITLE_CODE")
    @ApiModelProperty(value = "职称编号")
    private String titleCode;

    /**
     * 职称名称
     */
    @Column(name = "TITLE_NAME")
    @ApiModelProperty(value = "职称名称")
    private String titleName;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人编码
     */
    @Column(name = "CREATE_USER_CODE")
    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;
}