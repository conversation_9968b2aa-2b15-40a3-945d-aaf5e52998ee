package cn.trasen.oa.hrm.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.bean.hrms.MedHisEmployeeResp;
import cn.trasen.oa.hrm.model.CommHolidayYear;
import cn.trasen.oa.hrm.model.Schedule;
import tk.mybatis.mapper.common.Mapper;

public interface ScheduleMapper extends Mapper<Schedule> {

	List<String> selectScheduleDate(Map<String,String> params);

	List<MedHisEmployeeResp> selectMedHisEmployeeResp();

	List<Schedule> selectSchedule(Schedule record);
	
	List<Schedule> selectRemindSchedule();
	
	List<Schedule> selectRemindScheduleCurrent();

	List<Schedule> getScheduleListByDate(Schedule record);

	List<Schedule> selectScheduleTime(@Param("id")String id,@Param("scheduleDate")String scheduleDate, 
			@Param("scheduleStartTime")String scheduleStartTime,@Param("currentUserCode")String currentUserCode,@Param("ssoOrgCode")String ssoOrgCode);

	void deleteMeetingData(@Param("meetingDate")String meetingDate,@Param("startTime") String startTime, @Param("endTime")String endTime, @Param("ssoOrgCode")String ssoOrgCode);

	List<CommHolidayYear> getHolidayList(@Param("year")String year, @Param("ssoOrgCode")String ssoOrgCode);
	
}