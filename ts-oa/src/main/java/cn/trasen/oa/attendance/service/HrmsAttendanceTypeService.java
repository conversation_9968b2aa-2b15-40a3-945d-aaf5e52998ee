package cn.trasen.oa.attendance.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.attendance.model.HrmsAttendanceType;

/**
 * @ClassName HrmsAttendanceTypeService
 * @Description TODO
 * @date 2023��8��18�� ����5:13:31
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsAttendanceTypeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��8��18�� ����5:13:31
	 * <AUTHOR>
	 */
	Integer save(HrmsAttendanceType record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��8��18�� ����5:13:31
	 * <AUTHOR>
	 */
	Integer update(HrmsAttendanceType record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��8��18�� ����5:13:31
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsAttendanceType
	 * @date 2023��8��18�� ����5:13:31
	 * <AUTHOR>
	 */
	HrmsAttendanceType selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsAttendanceType>
	 * @date 2023��8��18�� ����5:13:31
	 * <AUTHOR>
	 */
	DataSet<HrmsAttendanceType> getDataSetList(Page page, HrmsAttendanceType record);
	
	/**
	 * 获取所有数据
	 * @param page
	 * @param record
	 * @return
	 */
	List<HrmsAttendanceType> getAllList(HrmsAttendanceType record);

	/**返回考勤类型和对应的标识
	 * @return
	 */
	List<HrmsAttendanceType> getMark();

	/**
	 * 获取考勤类型
	 * @return
	 */
	Map<String, Object> getAllType();

	void syncType();
}
