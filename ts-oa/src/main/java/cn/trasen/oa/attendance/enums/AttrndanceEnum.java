package cn.trasen.oa.attendance.enums;

import lombok.Getter;



/**   
 * @Title: GenderTypeEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 性别枚举类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月10日 上午11:04:02 
 * @version V1.0   
 */
@Getter
public enum AttrndanceEnum {

	ATTRNDANCE_TYPE_2("病假", "B"),
	
	ATTRNDANCE_TYPE_3("事假", "S"),
	
	ATTRNDANCE_TYPE_4("产假", "Y"),
	
	ATTRNDANCE_TYPE_5("婚假", "H"),
	
	ATTRNDANCE_TYPE_6("探亲假", "T"),
	
	ATTRNDANCE_TYPE_7("流产假", "L"),
	
	ATTRNDANCE_TYPE_8("丧假", "X"),
	
	ATTRNDANCE_TYPE_9("陪产假", "P"),
	
	ATTRNDANCE_TYPE_1("公休假", "公"),
	
	ATTRNDANCE_TYPE_10("补休假", "补");

	
	private final String key;
	private final String val;

	private AttrndanceEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (AttrndanceEnum item : AttrndanceEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}

	/**
	 * @Title: getKeyByVal
	 * @Description: 根据val获得key值
	 * @param val
	 * @Return String
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:30:51
	 */
	public static String getKeyByVal(String val) {
		for (AttrndanceEnum item : AttrndanceEnum.values()) {
			if (item.val.equals(val)) {
				return item.key;
			}
		}
		return "";
	}

}