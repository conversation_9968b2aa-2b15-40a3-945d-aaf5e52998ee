package cn.trasen.oa.attendance.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_attendance_details")
@Setter
@Getter
public class HrmsAttendanceDetails {
    /**
     * 主键ID
     */
	@Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 审核科室关联
     */
    @Column(name = "examine_org")
    @ApiModelProperty(value = "审核科室关联")
    private String examineOrg;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 考勤项目ID
     */
    @Column(name = "attendance_type_id")
    @ApiModelProperty(value = "考勤项目ID")
    private String attendanceTypeId;

    /**
     * 考勤项目
     */
    @Column(name = "attendance_type_name")
    @ApiModelProperty(value = "考勤项目")
    private String attendanceTypeName;

    /**
     * 考勤标志
     */
    @Column(name = "attendance_type_mark")
    @ApiModelProperty(value = "考勤标志")
    private String attendanceTypeMark;

    /**
     * 考勤日期
     */
    @Column(name = "attendance_date")
    @ApiModelProperty(value = "考勤日期")
    private String attendanceDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Transient
    private HrmsAttendanceType attendanceType;
   
    @Transient
    private List<String> dates;
  
    @Transient
    private String reportOrgId;  //上报科室id
    
    @Transient
    private String startDate;   //查询开始时间
  
    @Transient
    private String	 endDate;   //查询结束时间
    
    @Transient
    private String    searchMonth;  //月份
    
    @Transient
    private String    searchStartMonth;  //开始月份
    
    @Transient
    private String    searchEndMonth;  //结束月份
    
    @Transient
    private List<String>    searchOrgId;  //查询科室
    
    @Transient
    private String    searchOrgName;  //科室名称
    
    @Transient
    private List<String> typeList;   //考勤类型查询条件
    
    @Transient
    private List<String> months;   //关联月份
    
    @Transient
    private String employeeIds;   //考勤类型查询条件
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Transient
    private Integer fadingjia;  //法定假
    
    @Transient
    private String personalIdentity;   //岗位查询条件
    
    
}