package cn.trasen.oa.attendance.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.attendance.model.HrmsAttendanceExamine;
import cn.trasen.oa.attendance.service.HrmsAttendanceExamineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsAttendanceExamineController
 * @Description TODO
 * @date 2023��8��18�� ����5:14:19
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsAttendanceExamineController")
public class HrmsAttendanceExamineController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsAttendanceExamineController.class);

	@Autowired
	private HrmsAttendanceExamineService hrmsAttendanceExamineService;

	/**
	 * @Title saveHrmsAttendanceExamine
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��8��18�� ����5:14:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/attendance/attendanceExamine/save")
	public PlatformResult<String> saveHrmsAttendanceExamine(@RequestBody HrmsAttendanceExamine record) {
		try {
			hrmsAttendanceExamineService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsAttendanceExamine
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��8��18�� ����5:14:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/attendance/attendanceExamine/update")
	public PlatformResult<String> updateHrmsAttendanceExamine(@RequestBody HrmsAttendanceExamine record) {
		try {
			hrmsAttendanceExamineService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsAttendanceExamineById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsAttendanceExamine>
	 * @date 2023��8��18�� ����5:14:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/attendance/attendanceExamine/{id}")
	public PlatformResult<HrmsAttendanceExamine> selectHrmsAttendanceExamineById(@PathVariable String id) {
		try {
			HrmsAttendanceExamine record = hrmsAttendanceExamineService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据日期和科室查询上报科室的数据", notes = "根据日期和科室查询上报科室的数据")
	@GetMapping("/attendance/attendanceExamine/findDetail")
	public PlatformResult<HrmsAttendanceExamine> findDetail(HrmsAttendanceExamine record) {
		try {
			HrmsAttendanceExamine bean = hrmsAttendanceExamineService.findDetail(record);
			return PlatformResult.success(bean);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "取消上报", notes = "取消上报")
	@GetMapping("/attendance/attendanceExamine/cancel")
	public PlatformResult<String> cancel( String cancelOrgId,String cancelMonth) {
		try {
			hrmsAttendanceExamineService.cancel(cancelOrgId,cancelMonth);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsAttendanceExamineById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��8��18�� ����5:14:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/attendance/attendanceExamine/delete/{id}")
	public PlatformResult<String> deleteHrmsAttendanceExamineById(@PathVariable String id) {
		try {
			hrmsAttendanceExamineService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsAttendanceExamineList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsAttendanceExamine>
	 * @date 2023��8��18�� ����5:14:19
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/attendance/attendanceExamine/list")
	public DataSet<HrmsAttendanceExamine> selectHrmsAttendanceExamineList(Page page, HrmsAttendanceExamine record) {
		return hrmsAttendanceExamineService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/attendance/attendanceExamine/getDatalist")
	public PlatformResult<List<HrmsAttendanceExamine>> getDatalist(HrmsAttendanceExamine record) {
		try {
			List<HrmsAttendanceExamine> list = hrmsAttendanceExamineService.getDatalist(record);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询科室是否上报", notes = "查询科室是否上报")
	@GetMapping("/attendance/attendanceExamine/getExamineByDeptAndMonth")
	public PlatformResult<Boolean> getExamineByDeptAndMonth(HrmsAttendanceExamine record) {
		try {
			Boolean re = hrmsAttendanceExamineService.getExamineByDeptAndMonth(record);
			return PlatformResult.success(re);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "部门机构树查询", notes = "部门机构树查询")
	@GetMapping("/attendance/attendanceExamine/getZTreeList")
	public PlatformResult<List<TreeModel>> getZTreeList() {
		try {
			List<TreeModel> record = hrmsAttendanceExamineService.selecAllTreeNode();
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
