package cn.trasen.oa.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.oa.civilAffairs.controller.CivilAffairsPersonnelInfoController;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 服务对象入院和离院数据定时任务-对接膳食系统
 */
@Slf4j
@Component
public class CivilAffairsPersonnelInfoJob {
	
	@Autowired
	private CivilAffairsPersonnelInfoController civilAffairsPersonnelInfoController;
	
	/**
	 * 智障患者管理系统的url地址
	 */
	@Value("${intellectual-disability-url:0}")
	private String intellectualDisabilityUrl;
	
	/**
	 * 每10分执行一次，抽取服务对象入院和离院数据
	 */
	@Scheduled(cron = "0 0/10 * * * ?")
	public void saveNewTechPatientInfo() {
		if("0".equals(intellectualDisabilityUrl)){
			return;
		}
		try {
			log.info("=========定时同步服务对象入院和离院数据==========");
			civilAffairsPersonnelInfoController.syncPersonnelFromIntellectualDisability();
    		
		} catch (Exception e) {
			log.error("定时同步服务对象入院和离院数据执行失败：" + e.getMessage(),e);
		}
	}
}
