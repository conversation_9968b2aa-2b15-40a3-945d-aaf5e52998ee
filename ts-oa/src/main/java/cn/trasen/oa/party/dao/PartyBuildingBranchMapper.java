package cn.trasen.oa.party.dao;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.oa.party.model.PartyBuildingBranch;
import tk.mybatis.mapper.common.Mapper;

public interface PartyBuildingBranchMapper extends Mapper<PartyBuildingBranch> {

	Map<String, Object> selectByMemberCode(@Param("memberCode")String memberCode);
	
	PartyBuildingBranch selectBybranchId(@Param("branchId")String branchId);
	
}