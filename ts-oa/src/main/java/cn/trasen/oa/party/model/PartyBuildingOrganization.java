package cn.trasen.oa.party.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "toa_party_building_organization")
@Setter
@Getter
public class PartyBuildingOrganization {
	
	@Id
	@ApiModelProperty(value = "主键")
    private String id;

    /**
     * 党组织全称
     */
    @Column(name = "organization_name")
    @ApiModelProperty(value = "党组织全称")
    private String organizationName;

    /**
     * 党组织简称
     */
    @Column(name = "short_name")
    @ApiModelProperty(value = "党组织简称")
    private String shortName;

    /**
     * 上级党组织
     */
    @Column(name = "superior_name")
    @ApiModelProperty(value = "上级党组织")
    private String superiorName;

    /**
     * 党组织编码
     */
    @Column(name = "organization_code")
    @ApiModelProperty(value = "党组织编码")
    private String organizationCode;

    /**
     * 组织类别
     */
    @Column(name = "organization_type")
    @ApiModelProperty(value = "组织类别")
    private String organizationType;

    /**
     * 行政区划分关系
     */
    @Column(name = "organization_adr")
    @ApiModelProperty(value = "行政区划分关系")
    private String organizationAdr;

    /**
     * 行政区域（省）
     */
    @Column(name = "organization_province")
    @ApiModelProperty(value = "行政区域（省）")
    private String organizationProvince;

    /**
     * 行政区域（市）
     */
    @Column(name = "organization_city")
    @ApiModelProperty(value = "行政区域（市）")
    private String organizationCity;

    /**
     * 行政区域（区）
     */
    @Column(name = "organization_area")
    @ApiModelProperty(value = "行政区域（区）")
    private String organizationArea;

    /**
     * 行政区域code
     */
    @Column(name = "organization_area_code")
    @ApiModelProperty(value = "行政区域code")
    private String organizationAreaCode;
    
    /**
     * 党组织联系人
     */
    @Column(name = "organization_contact_code")
    @ApiModelProperty(value = "党组织联系人code")
    private String organizationContactCode;
    
    /**
     * 党组织联系人
     */
    @Column(name = "organization_contact")
    @ApiModelProperty(value = "党组织联系人")
    private String organizationContact;

    /**
     * 联系人手机
     */
    @Column(name = "organization_contact_phone")
    @ApiModelProperty(value = "联系人手机")
    private String organizationContactPhone;

    /**
     * 建立日期
     */
    @Column(name = "creation_date")
    @ApiModelProperty(value = "建立日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date creationDate;

    /**
     * 详细地址
     */
    @Column(name = "organization_address")
    @ApiModelProperty(value = "详细地址")
    private String organizationAddress;

    /**
     * 备注
     */
    @Column(name = "organization_remark")
    @ApiModelProperty(value = "备注")
    private String organizationRemark;

    /**
     * 附件
     */
    @Column(name = "organization_files")
    @ApiModelProperty(value = "附件")
    private String organizationFiles;
    
    @Column(name = "organization_status")
    @ApiModelProperty(value = "状态  1建设中   2已撤销")
    private Integer organizationStatus;
    
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "操作记录集合")
    private List<PartyBuildingOrganizationOperate> operateList;
    
    @Transient
    @ApiModelProperty(value = "班子信息")
    private List<PartyBuildingBranch> BranchList;
    
    @Transient
    @ApiModelProperty(value = "换届次数")
    private Integer number;
    
    @Transient
    @ApiModelProperty(value = "未按期换届")
    private Integer termNumber;
    
    @Transient
    @ApiModelProperty(value = "操作状态")
    private Integer  OperateType;
    
    
    @Transient
    @ApiModelProperty(value = "支部实体")
    private PartyBuildingTeam partyBuildingTeam;
    
}