package cn.trasen.oa.party.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_party_building_flow")
@Setter
@Getter
public class PartyBuildingFlow {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 状态  1流入党员,2流出党员,3出国出境
     */
    @Column(name = "flow_status")
    @ApiModelProperty(value = "状态  1流入党员,2流出党员,3出国出境")
    private Integer flowStatus;

    /**
     * 流动人编号
     */
    @Column(name = "flow_user_code")
    @ApiModelProperty(value = "流动人编号")
    private String flowUserCode;

    /**
     * 流动人姓名
     */
    @Column(name = "flow_user_name")
    @ApiModelProperty(value = "流动人姓名")
    private String flowUserName;

    /**
     * 所在科室编码
     */
    @Column(name = "flow_dept_code")
    @ApiModelProperty(value = "所在科室编码")
    private String flowDeptCode;

    /**
     * 所在科室名称
     */
    @Column(name = "flow_dept_name")
    @ApiModelProperty(value = "所在科室名称")
    private String flowDeptName;

    /**
     * 性别
     */
    @Column(name = "flow_gender")
    @ApiModelProperty(value = "性别")
    private String flowGender;

    /**
     * 年龄
     */
    @Column(name = "flow_age")
    @ApiModelProperty(value = "年龄")
    private String flowAge;

    /**
     * 出生日期
     */
    @Column(name = "flow_birthday")
    @ApiModelProperty(value = "出生日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date flowBirthday;

    /**
     * 手机号码
     */
    @Column(name = "flow_phone")
    @ApiModelProperty(value = "手机号码")
    private String flowPhone;

    /**
     * 党员类型,1正式党员,2发展党员
     */
    @ApiModelProperty(value = "党员类型,1正式党员,2发展党员")
    private Integer type;

    /**
     * 所属阶段 0申请入党 1列为入党积极分子  3拟定发展对象  4拟定预备党员  5正式党员
     */
    @Column(name = "process_status")
    @ApiModelProperty(value = "'所属阶段 0申请入党 1列为入党积极分子  3拟定发展对象  4拟定预备党员  5正式党员")
    private Integer processStatus;
    
    /**
     * 申请入党日期
     */
    @Column(name = "apply_date")
    @ApiModelProperty(value = "申请入党日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date applyDate;

    /**
     * 谈话人
     */
    @Column(name = "apply_talker_code")
    @ApiModelProperty(value = "谈话人")
    private String applyTalkerCode;

    /**
     * 谈话人名称
     */
    @Column(name = "apply_talker_name")
    @ApiModelProperty(value = "谈话人名称")
    private String applyTalkerName;

    /**
     * 谈话日期
     */
    @Column(name = "apply_talk_date")
    @ApiModelProperty(value = "谈话日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date applyTalkDate;

    /**
     * 谈话状态 0未谈话  1已谈话
     */
    @Column(name = "apply_talk_status")
    @ApiModelProperty(value = "谈话状态 0未谈话  1已谈话")
    private Integer applyTalkStatus;

    /**
     * 积极份子时间
     */
    @Column(name = "activist_date")
    @ApiModelProperty(value = "积极份子时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date activistDate;

    /**
     * 拟定发展对象日期
     */
    @Column(name = "develop_date")
    @ApiModelProperty(value = "拟定发展对象日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date developDate;

    /**
     * 入党介绍人id
     */
    @Column(name = "develop_introducer_id")
    @ApiModelProperty(value = "入党介绍人id")
    private String developIntroducerId;

    /**
     * 入党介绍人名称
     */
    @Column(name = "develop_introducer_name")
    @ApiModelProperty(value = "入党介绍人名称")
    private String developIntroducerName;

    /**
     * 拟定预备党员日期
     */
    @Column(name = "prepare_date")
    @ApiModelProperty(value = "拟定预备党员日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date prepareDate;

    /**
     * 正式党员日期
     */
    @Column(name = "full_date")
    @ApiModelProperty(value = "正式党员日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date fullDate;

    /**
     * 入党日期
     */
    @Column(name = "join_date")
    @ApiModelProperty(value = "入党日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date joinDate;

    /**
     * 流出党组织id
     */
    @Column(name = "outflow_organization_id")
    @ApiModelProperty(value = "流出党组织id")
    private String outflowOrganizationId;

    /**
     * 流出党组织名称(简称)
     */
    @Column(name = "outflow_organization_short_name")
    @ApiModelProperty(value = "流出党组织名称(简称)")
    private String outflowOrganizationShortName;

    /**
     * 流出所属党支部
     */
    @Column(name = "outflow_branch_id")
    @ApiModelProperty(value = "流出所属党支部")
    private String outflowBranchId;

    /**
     * 流出所属党支部名称
     */
    @Column(name = "outflow_branch_name")
    @ApiModelProperty(value = "流出所属党支部名称")
    private String outflowBranchName;

    /**
     * 流出日期
     */
    @Column(name = "outflow_date")
    @ApiModelProperty(value = "流出日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date outflowDate;

    /**
     * 流出地(单位)
     */
    @Column(name = "outflow_place")
    @ApiModelProperty(value = "流出地(单位)")
    private String outflowPlace;

    /**
     * 流出地联系人
     */
    @Column(name = "outflow_place_name")
    @ApiModelProperty(value = "流出地联系人")
    private String outflowPlaceName;

    /**
     * 流出地联系电话
     */
    @Column(name = "outflow_place_phone")
    @ApiModelProperty(value = "流出地联系电话")
    private String outflowPlacePhone;

    /**
     * 流入党组织id
     */
    @Column(name = "inflow_organization_id")
    @ApiModelProperty(value = "流入党组织id")
    private String inflowOrganizationId;

    /**
     * 流入党组织名称(简称)
     */
    @Column(name = "inflow_organization_short_name")
    @ApiModelProperty(value = "流入党组织名称(简称)")
    private String inflowOrganizationShortName;

    /**
     * 流入所属党支部
     */
    @Column(name = "inflow_branch_id")
    @ApiModelProperty(value = "流入所属党支部")
    private String inflowBranchId;

    /**
     * 流入所属党支部名称
     */
    @Column(name = "inflow_branch_name")
    @ApiModelProperty(value = "流入所属党支部名称")
    private String inflowBranchName;

    /**
     * 流入日期
     */
    @Column(name = "inflow_date")
    @ApiModelProperty(value = "流入日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date inflowDate;

    /**
     * 流入地(单位)
     */
    @Column(name = "inflow_place")
    @ApiModelProperty(value = "流入地(单位)")
    private String inflowPlace;

    /**
     * 流入地联系人
     */
    @Column(name = "inflow_place_name")
    @ApiModelProperty(value = "流入地联系人")
    private String inflowPlaceName;

    /**
     * 流入地联系电话
     */
    @Column(name = "inflow_place_phone")
    @ApiModelProperty(value = "流入地联系电话")
    private String inflowPlacePhone;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;

    /**
     * 出国日期
     */
    @Column(name = "go_abroad_date")
    @ApiModelProperty(value = "出国日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date goAbroadDate;

    /**
     * 出国地
     */
    @Column(name = "go_abroad_place")
    @ApiModelProperty(value = "出国地")
    private String goAbroadPlace;

    /**
     * 应归日期
     */
    @Column(name = "due_date")
    @ApiModelProperty(value = "应归日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date dueDate;

    /**
     * 实归日期
     */
    @Column(name = "actual_date")
    @ApiModelProperty(value = "实归日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date actualDate;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 岗位名称
     */
    @Column(name = "post_name")
    @ApiModelProperty(value = "岗位名称")
    private String postName;
    
    /**
     * 存放头像的路径
     */
    @Column(name = "head_img")
    @ApiModelProperty(value = "存放头像的路径")
    private String headImg;
    
    
    @Transient
    @ApiModelProperty(value = "流入开始时间（查询用）")
    private String inflowStartDate;
    
    @Transient
    @ApiModelProperty(value = "流入结束时间（查询用）")
    private String inflowEndDate;
    
    @Transient
    @ApiModelProperty(value = "流出开始时间（查询用）")
    private String outflowStartDate;
    
    @Transient
    @ApiModelProperty(value = "流出结束时间（查询用）")
    private String outflowEndDate;
    
    @Transient
    @ApiModelProperty(value = "出国(出境)开始时间（查询用）")
    private String goAbroadStartDate;
    
    @Transient
    @ApiModelProperty(value = "出国(出境)结束时间（查询用）")
    private String goAbroadEndDate;
}