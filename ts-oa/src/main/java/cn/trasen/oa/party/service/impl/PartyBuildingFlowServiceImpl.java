package cn.trasen.oa.party.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.party.dao.PartyBuildingBranchMapper;
import cn.trasen.oa.party.dao.PartyBuildingBranchMemberMapper;
import cn.trasen.oa.party.dao.PartyBuildingFlowMapper;
import cn.trasen.oa.party.dao.PartyBuildingManageMapper;
import cn.trasen.oa.party.model.PartyBuildingApply;
import cn.trasen.oa.party.model.PartyBuildingBranch;
import cn.trasen.oa.party.model.PartyBuildingBranchMember;
import cn.trasen.oa.party.model.PartyBuildingFlow;
import cn.trasen.oa.party.model.PartyBuildingManage;
import cn.trasen.oa.party.service.PartyBuildingApplyService;
import cn.trasen.oa.party.service.PartyBuildingBranchMemberService;
import cn.trasen.oa.party.service.PartyBuildingFlowService;
import cn.trasen.oa.party.service.PartyBuildingManageService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PartyBuildingFlowServiceImpl
 * @Description TODO
 * @date 2023��7��6�� ����3:22:31
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PartyBuildingFlowServiceImpl implements PartyBuildingFlowService {

	@Autowired
	private PartyBuildingFlowMapper mapper;
	
	@Autowired
	private PartyBuildingApplyService partyBuildingApplyService;

	@Autowired
	private PartyBuildingBranchMemberService partyBuildingBranchMemberService;
	
	@Autowired
	private PartyBuildingBranchMemberMapper branchMemberMapper;
	
	@Autowired
	private PartyBuildingBranchMapper partyBuildingBranchMapper;
	
	@Autowired
	private PartyBuildingManageMapper partyBuildingManageMapper;
	
	@Autowired
	private PartyBuildingManageService partyBuildingManageService;
	
	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(PartyBuildingFlow record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		if(record.getFlowStatus() !=null && "1".equals(record.getFlowStatus().toString()) ){	//流入、发展中的党员
			
			PartyBuildingBranchMember branchMember = new PartyBuildingBranchMember();
			branchMember.setBranchId(record.getFlowUserCode());
			branchMember.setMemberCode(record.getInflowBranchId());
			branchMember.setMemberName(record.getInflowBranchName());
			partyBuildingBranchMemberService.save(branchMember);//新增支部流入人员
			
			PartyBuildingApply  partyBuildingApply  = new  PartyBuildingApply();
			partyBuildingApply.setApplyStatus(record.getProcessStatus());
			partyBuildingApply.setApplyUserCode(record.getFlowUserCode());
			partyBuildingApply.setApplyUserName(record.getFlowUserName());
			partyBuildingApply.setApplyDeptCode(record.getFlowDeptCode());
			partyBuildingApply.setApplyDeptName(record.getFlowDeptName());
			partyBuildingApply.setApplyGender(record.getFlowGender());
			partyBuildingApply.setApplyAge(record.getFlowAge());
			partyBuildingApply.setApplyBirthday(record.getFlowBirthday());
			partyBuildingApply.setApplyPhone(record.getFlowPhone());
			partyBuildingApply.setApplyDate(record.getApplyDate());
			partyBuildingApply.setApplyTalkerCode(record.getApplyTalkerCode());
			partyBuildingApply.setApplyTalkerName(record.getApplyTalkerName());
			partyBuildingApply.setApplyTalkDate(record.getApplyTalkDate());
			partyBuildingApply.setApplyTalkStatus(record.getApplyTalkStatus());
			partyBuildingApply.setActivistDate(record.getActivistDate());
			partyBuildingApply.setDevelopDate(record.getDevelopDate());
			partyBuildingApply.setDevelopIntroducerId(record.getDevelopIntroducerId());
			partyBuildingApply.setDevelopIntroducerName(record.getDevelopIntroducerName());
			partyBuildingApply.setPrepareDate(record.getPrepareDate());
			partyBuildingApply.setFullDate(record.getFullDate());
			partyBuildingApply.setIsInflow(1);
			partyBuildingApplyService.save(partyBuildingApply);
		}
		
		if(record.getFlowStatus() !=null && "2".equals(record.getFlowStatus().toString()) ){	//流出、支部成员减少
			PartyBuildingBranchMember branchMember = new PartyBuildingBranchMember();
			branchMember.setBranchId(record.getFlowUserCode());
			branchMember.setMemberCode(record.getInflowBranchId());
			branchMember.setIsDeleted("Y");//将该支部成员删除
			partyBuildingBranchMemberService.updateByBranchMemberCode(branchMember);
			
			PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(record.getFlowUserCode());//查询党员管理是否有该成员，更新成员支部信息
			if(null !=partyBuildingManage){
				partyBuildingManage.setBranchId("");//是否置空
				partyBuildingManage.setOrganizationId("");//是否置空
				partyBuildingManage.setManageStatus(7);
				partyBuildingManage.setIsOutflow(1);
				partyBuildingManageService.update(partyBuildingManage);
			}
			
			String content = record.getFlowUserName()+dateFormat.format(record.getOutflowDate())+"本党组织流出"+record.getInflowOrganizationShortName()+"党组织，请知悉！";//消息
		    String subject = "党员管理—流出党员提醒";
		    String url =appConfigProperties.getWxDomain() + "";//微信端地址
		    if(StringUtils.isNotBlank(record.getOutflowBranchId())){
		    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getOutflowBranchId());
		    	if(null != partyBuildingBranch){
		    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
		    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
		    		}if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
		    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
		    		}
		    	}
		    }
		}
		
		if(record.getFlowStatus() !=null && "3".equals(record.getFlowStatus().toString()) ){	//流出、支部成员减少
			PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(record.getFlowUserCode());//查询党员管理是否有该成员，更新成员支部信息
			if(null !=partyBuildingManage){
				partyBuildingManage.setManageStatus(5);
				partyBuildingManage.setIsAbroad(1);
				partyBuildingManageService.update(partyBuildingManage);
			}
			
			String content = record.getFlowUserName()+dateFormat.format(record.getGoAbroadDate())+"已出国出境，请知悉！";//消息
		    String subject = "党员管理—党员出国出境提醒";
		    String url =appConfigProperties.getWxDomain() + "";//微信端地址
		    if(StringUtils.isNotBlank(record.getInflowBranchId())){
		    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getInflowBranchId());
		    	if(null != partyBuildingBranch){
		    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
		    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
		    		}if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
		    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
		    		}
		    	}
		    }
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PartyBuildingFlow record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKey(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PartyBuildingFlow record = new PartyBuildingFlow();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		PartyBuildingFlow partyBuildingFlow = mapper.selectByPrimaryKey(id);
		if(partyBuildingFlow.getFlowStatus() !=null && "2".equals(partyBuildingFlow.getFlowStatus().toString()) ){	//流出删除
			PartyBuildingBranchMember  branchMember= branchMemberMapper.selectByCode(partyBuildingFlow.getFlowUserCode());//查询该成员支部信息、然后更新
			if(branchMember == null && StringUtils.isNotBlank(partyBuildingFlow.getOutflowBranchId())){//返回原来党支部
				PartyBuildingBranchMember  branchMemberRecord = new PartyBuildingBranchMember();
				branchMemberRecord.setMemberCode(partyBuildingFlow.getFlowUserCode());
				branchMemberRecord.setMemberName(partyBuildingFlow.getFlowUserName());
				branchMemberRecord.setBranchId(partyBuildingFlow.getOutflowBranchId());
				partyBuildingBranchMemberService.save(branchMemberRecord);
			}
			
			PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(partyBuildingFlow.getFlowUserCode());//查询党员管理是否有该成员，更新成员支部信息
			if(null !=partyBuildingManage){
				partyBuildingManage.setBranchId(partyBuildingFlow.getOutflowBranchId());
				partyBuildingManage.setOrganizationId(partyBuildingFlow.getOutflowOrganizationId());
				partyBuildingManage.setIsOutflow(0);
				partyBuildingManageService.update(partyBuildingManage);
			}
			
			String content = record.getFlowUserName()+"由本党组织流出"+record.getInflowOrganizationShortName()+"党组织已删除，请知悉！";//消息
		    String subject = "党员管理—流出党员提醒";
		    String url =appConfigProperties.getWxDomain() + "";//微信端地址
		    if(StringUtils.isNotBlank(record.getOutflowBranchId())){
		    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getOutflowBranchId());
		    	if(null != partyBuildingBranch){
		    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
		    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
		    		}if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
		    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
		    		}
		    	}
		    }
		}
		
		if(partyBuildingFlow.getFlowStatus() !=null && "3".equals(partyBuildingFlow.getFlowStatus().toString()) ){	//流出删除
			PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(partyBuildingFlow.getFlowUserCode());//查询党员管理是否有该成员，更新成员支部信息
			if(null !=partyBuildingManage){
				partyBuildingManage.setIsAbroad(0);
				partyBuildingManageService.update(partyBuildingManage);
			}
			
			String content = record.getFlowUserName()+"由本党组织出国出境已删除，请知悉！";//消息
		    String subject = "党员管理—出国出境党员提醒";
		    String url =appConfigProperties.getWxDomain() + "";//微信端地址
		    if(StringUtils.isNotBlank(record.getOutflowBranchId())){
		    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getOutflowBranchId());
		    	if(null != partyBuildingBranch){
		    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
		    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
		    		}if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
		    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
		    		}
		    	}
		    }
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PartyBuildingFlow selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PartyBuildingFlow> getDataSetList(Page page, PartyBuildingFlow record) {
		Example example = new Example(PartyBuildingFlow.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getFlowUserName())){
			criteria.andLike("flowUserName", "%" + record.getFlowUserName() + "%");
		}
		if(record.getFlowStatus() !=null){
			criteria.andEqualTo("flowStatus",record.getFlowStatus());
		}
		if(record.getType() != null){
			criteria.andEqualTo("type",record.getType());
		}
		if(StringUtils.isNotBlank(record.getInflowStartDate()) && StringUtils.isNotBlank(record.getInflowEndDate())){
			criteria.andBetween("inflowDate", record.getInflowStartDate(), record.getInflowEndDate());
		}
		if(StringUtils.isNotBlank(record.getOutflowStartDate()) && StringUtils.isNotBlank(record.getOutflowEndDate())){
			criteria.andBetween("outflowDate", record.getOutflowStartDate(), record.getOutflowEndDate());
		}
		if(StringUtils.isNotBlank(record.getGoAbroadStartDate()) && StringUtils.isNotBlank(record.getGoAbroadEndDate())){
			criteria.andBetween("goAbroadDate", record.getGoAbroadStartDate(), record.getGoAbroadEndDate());
		}
		 boolean isInfoAdmin = UserInfoHolder.getRight("PARTY_BUILDING");//党建管理员
	        if(isInfoAdmin){
	        }else{
	        	criteria.andEqualTo("createUser",UserInfoHolder.getCurrentUserCode());
	        }
		List<PartyBuildingFlow> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	 private void sendMessage(String content,String receiver,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                .receiver(receiver)  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-political/party-member-management/flow-party-member").source("智慧党建")
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }
}
