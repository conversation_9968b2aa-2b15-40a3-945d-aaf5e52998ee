package cn.trasen.oa.party.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.party.dao.PartyBuildingBranchMapper;
import cn.trasen.oa.party.dao.PartyBuildingBranchMemberMapper;
import cn.trasen.oa.party.dao.PartyBuildingManageMapper;
import cn.trasen.oa.party.dao.PartyBuildingRetireMapper;
import cn.trasen.oa.party.model.PartyBuildingBranch;
import cn.trasen.oa.party.model.PartyBuildingBranchMember;
import cn.trasen.oa.party.model.PartyBuildingManage;
import cn.trasen.oa.party.model.PartyBuildingRetire;
import cn.trasen.oa.party.service.PartyBuildingBranchMemberService;
import cn.trasen.oa.party.service.PartyBuildingManageService;
import cn.trasen.oa.party.service.PartyBuildingRetireService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PartyBuildingRetireServiceImpl
 * @Description TODO
 * @date 2023��7��7�� ����4:36:25
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PartyBuildingRetireServiceImpl implements PartyBuildingRetireService {

	@Autowired
	private PartyBuildingRetireMapper mapper;
	
	@Autowired
	private PartyBuildingBranchMemberMapper branchMemberMapper;
	
	@Autowired
	private PartyBuildingManageMapper  partyBuildingManageMapper;
	
	@Autowired
	private PartyBuildingManageService  partyBuildingManageService;
	
	@Autowired
	private PartyBuildingBranchMapper partyBuildingBranchMapper;

	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(PartyBuildingRetire record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		PartyBuildingBranchMember  branchMember= branchMemberMapper.selectByCode(record.getHretireUserCode());//查询该成员支部信息、然后更新
		if (null != branchMember){
			if(StringUtils.isNotBlank(record.getChangeBranchId())){
				branchMember.setBranchId(record.getChangeBranchId());
			}
		branchMemberMapper.updateByPrimaryKeySelective(branchMember);
		}
//		if (null != branchMember){
//			branchMember.setBranchId(record.getRetireBranchId());
//			branchMemberMapper.updateByPrimaryKeySelective(branchMember);
//		}else{
//			PartyBuildingBranchMember branchMemberrecord = new PartyBuildingBranchMember();
//			branchMemberrecord.setMemberCode(record.getHretireUserCode());
//			branchMemberrecord.setMemberName(record.getRetireUserName());
//			branchMemberrecord.setBranchId(record.getRetireBranchId());
//			branchMemberService.save(branchMemberrecord);
//		}
		
		PartyBuildingManage  partyBuildingManage= partyBuildingManageMapper.selectByUserCode(record.getHretireUserCode());//查询用户党员信息,然后更新支部
		if (null != partyBuildingManage){
			partyBuildingManage.setBranchId(record.getChangeBranchId());
			PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getChangeBranchId());
			if(partyBuildingBranch != null){
				partyBuildingManage.setOrganizationId(partyBuildingBranch.getOrganizationId());
			}
			partyBuildingManage.setIsRetire(1);
			partyBuildingManageService.update(partyBuildingManage);
		}
		
		
		String content = record.getRetireUserName()+dateFormat.format(record.getRetireDate())+ "已退休，请知悉！";//删除推送消息
	    String subject = "党员管理—退休党员提醒";
	    String url =appConfigProperties.getWxDomain() + "";//微信端地址
	    if(StringUtils.isNotBlank(record.getRetireBranchId())){
	    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getRetireBranchId());
	    	if(null != partyBuildingBranch){
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
	    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
	    		}
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
	    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
	    		}
	    	}
	    }
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PartyBuildingRetire record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PartyBuildingRetire record = new PartyBuildingRetire();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		PartyBuildingRetire  retireRecord = mapper.selectByPrimaryKey(id);//查询该成员信息
		
		PartyBuildingBranchMember  branchMember= branchMemberMapper.selectByCode(retireRecord.getHretireUserCode());//查询该成员支部信息、然后更新
		if (null != branchMember){
			if(StringUtils.isNotBlank(retireRecord.getRetireBranchId())){
				branchMember.setBranchId(retireRecord.getRetireBranchId());//更新回原来的支部信息
			}
		branchMemberMapper.updateByPrimaryKeySelective(branchMember);
		}
		
		PartyBuildingManage  partyBuildingManage= partyBuildingManageMapper.selectByUserCode(retireRecord.getHretireUserCode());//删除、更新党员操作退休状态
		if (null != partyBuildingManage){
			//partyBuildingManage.setBranchId(record.getRetireBranchId());
			partyBuildingManage.setIsRetire(0);//删除、更新党员操作退休状态
			partyBuildingManageService.update(partyBuildingManage);
		}
		
		
		String content = retireRecord.getRetireUserName()+ "不记为退休党员，请知悉！";//删除推送消息
	    String subject = "党员管理—退休党员提醒";
	    String url =appConfigProperties.getWxDomain() + "";//微信端地址
	    if(StringUtils.isNotBlank(retireRecord.getRetireBranchId())){
	    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(retireRecord.getRetireBranchId());
	    	if(null != partyBuildingBranch){
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
	    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
	    		}
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
	    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
	    		}
	    	}
	    }
	    
	    
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PartyBuildingRetire selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PartyBuildingRetire> getDataSetList(Page page, PartyBuildingRetire record) {
		Example example = new Example(PartyBuildingRetire.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getRetireUserName())){
			criteria.andLike("retireUserName", "%" + record.getRetireUserName() + "%");
		}
		if(record.getRetireType() != null){
			criteria.andEqualTo("retireType",record.getRetireType());
		}
		if(StringUtils.isNotBlank(record.getRetireStartDate()) && StringUtils.isNotBlank(record.getRetireEndDate())){
			criteria.andBetween("retireDate", record.getRetireStartDate(), record.getRetireEndDate());
		}
		boolean isInfoAdmin = UserInfoHolder.getRight("PARTY_BUILDING");//党建管理员
        if(isInfoAdmin){
        }else{
        	criteria.andEqualTo("createUser",UserInfoHolder.getCurrentUserCode());
        }
		List<PartyBuildingRetire> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	
	 private void sendMessage(String content,String receiver,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                .receiver(receiver)  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-political/party-member-management/retired-member").source("智慧党建")
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }
}
