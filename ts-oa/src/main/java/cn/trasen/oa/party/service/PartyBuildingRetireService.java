package cn.trasen.oa.party.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.party.model.PartyBuildingRetire;

/**
 * @ClassName PartyBuildingRetireService
 * @Description TODO
 * @date 2023��7��7�� ����4:36:25
 * <AUTHOR>
 * @version 1.0
 */
public interface PartyBuildingRetireService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��7��7�� ����4:36:25
	 * <AUTHOR>
	 */
	Integer save(PartyBuildingRetire record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��7��7�� ����4:36:25
	 * <AUTHOR>
	 */
	Integer update(PartyBuildingRetire record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��7��7�� ����4:36:25
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PartyBuildingRetire
	 * @date 2023��7��7�� ����4:36:25
	 * <AUTHOR>
	 */
	PartyBuildingRetire selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PartyBuildingRetire>
	 * @date 2023��7��7�� ����4:36:25
	 * <AUTHOR>
	 */
	DataSet<PartyBuildingRetire> getDataSetList(Page page, PartyBuildingRetire record);
}
