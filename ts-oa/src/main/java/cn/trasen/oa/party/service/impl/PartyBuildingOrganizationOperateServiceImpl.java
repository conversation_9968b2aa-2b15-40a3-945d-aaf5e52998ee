package cn.trasen.oa.party.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.party.dao.PartyBuildingOrganizationMapper;
import cn.trasen.oa.party.dao.PartyBuildingOrganizationOperateMapper;
import cn.trasen.oa.party.model.PartyBuildingOrganization;
import cn.trasen.oa.party.model.PartyBuildingOrganizationOperate;
import cn.trasen.oa.party.service.PartyBuildingOrganizationOperateService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PartyBuildingOrganizationOperateServiceImpl
 * @Description TODO
 * @date 2023��6��27�� ����2:06:18
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PartyBuildingOrganizationOperateServiceImpl implements PartyBuildingOrganizationOperateService {

	@Autowired
	private PartyBuildingOrganizationOperateMapper mapper;
	
	@Autowired
	private PartyBuildingOrganizationMapper partyBuildingOrganizationMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PartyBuildingOrganizationOperate record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		PartyBuildingOrganization partyBuildingOrganization = new PartyBuildingOrganization();
		partyBuildingOrganization.setId(record.getOrganizationId());
		if("1".equals(record.getOperateType())){//撤销
			partyBuildingOrganization.setOrganizationStatus(2);
		}
		if("2".equals(record.getOperateType())){//恢复
			partyBuildingOrganization.setOrganizationStatus(1);
		}
		
		partyBuildingOrganizationMapper.updateByPrimaryKeySelective(partyBuildingOrganization);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PartyBuildingOrganizationOperate record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PartyBuildingOrganizationOperate record = new PartyBuildingOrganizationOperate();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PartyBuildingOrganizationOperate selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PartyBuildingOrganizationOperate> getDataSetList(Page page, PartyBuildingOrganizationOperate record) {
		Example example = new Example(PartyBuildingOrganizationOperate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PartyBuildingOrganizationOperate> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
