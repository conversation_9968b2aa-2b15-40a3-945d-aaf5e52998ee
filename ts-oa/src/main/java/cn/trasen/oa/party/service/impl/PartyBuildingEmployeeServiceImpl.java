package cn.trasen.oa.party.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.party.dao.PartyBuildingEmployeeMapper;
import cn.trasen.oa.party.model.PartyBuildingEmployee;
import cn.trasen.oa.party.service.PartyBuildingEmployeeService;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PartyBuildingEmployeeServiceImpl implements PartyBuildingEmployeeService{

	@Autowired
	private PartyBuildingEmployeeMapper mapper;
	
	@Transactional(readOnly = false)
	@Override
	public Integer updateEmployee(PartyBuildingEmployee record) {
		// TODO Auto-generated method stub
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateEmployee(record);
	}

}
