package cn.trasen.oa.party.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.party.model.PartyBuildingTeam;
import cn.trasen.oa.party.service.PartyBuildingTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PartyBuildingTeamController
 * @Description TODO
 * @date 2023��6��30�� ����12:01:05
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "党建班子")
public class PartyBuildingTeamController {

	private transient static final Logger logger = LoggerFactory.getLogger(PartyBuildingTeamController.class);

	@Autowired
	private PartyBuildingTeamService partyBuildingTeamService;

	/**
	 * @Title savePartyBuildingTeam
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/partyBuildingTeam/save")
	public PlatformResult<String> savePartyBuildingTeam(@RequestBody PartyBuildingTeam record) {
		try {
			partyBuildingTeamService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePartyBuildingTeam
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/partyBuildingTeam/update")
	public PlatformResult<String> updatePartyBuildingTeam(@RequestBody PartyBuildingTeam record) {
		try {
			partyBuildingTeamService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPartyBuildingTeamById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PartyBuildingTeam>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/partyBuildingTeam/{id}")
	public PlatformResult<PartyBuildingTeam> selectPartyBuildingTeamById(@PathVariable String id) {
		try {
			PartyBuildingTeam record = partyBuildingTeamService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePartyBuildingTeamById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/partyBuildingTeam/delete/{id}")
	public PlatformResult<String> deletePartyBuildingTeamById(@PathVariable String id) {
		try {
			partyBuildingTeamService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPartyBuildingTeamList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PartyBuildingTeam>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/partyBuildingTeam/list")
	public DataSet<PartyBuildingTeam> selectPartyBuildingTeamList(Page page, PartyBuildingTeam record) {
		return partyBuildingTeamService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Title selectByOrganizationId
	 * @Description 查询党组织班子及成员信息
	 * @param page
	 * @param record
	 * @return DataSet<PartyBuildingTeam>
	 * @date 2023��6��30�� ����12:01:05
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询党组织班子及成员信息", notes = "查询党组织班子及成员信息")
	@GetMapping("/api/partyBuildingTeam/selectByOrganizationId")
	public PlatformResult<PartyBuildingTeam> selectByOrganizationId(PartyBuildingTeam record) {
		try {
			PartyBuildingTeam  team = partyBuildingTeamService.selectByOrganizationId(record);
			return PlatformResult.success(team);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
