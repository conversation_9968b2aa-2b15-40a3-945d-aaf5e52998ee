package cn.trasen.oa.party.model;

import io.swagger.annotations.*;

import java.util.Date;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_party_building_organization_operate")
@Setter
@Getter
public class PartyBuildingOrganizationOperate {
	
	@Id
	@ApiModelProperty(value = "主键")
    private String id;
	
	@Column(name = "organization_id")
    @ApiModelProperty(value = "党组织id")
	private String organizationId;

    /**
     * 操作类型  1撤销  2恢复
     */
    @Column(name = "operate_type")
    @ApiModelProperty(value = "操作类型  1撤销  2恢复")
    private String operateType;

    /**
     * 撤销/恢复日期
     */
    @Column(name = "operate_date")
    @ApiModelProperty(value = "撤销/恢复日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date operateDate;

    /**
     * 撤销/恢复文号
     */
    @Column(name = "operate_number")
    @ApiModelProperty(value = "撤销/恢复文号")
    private String operateNumber;

    /**
     * 撤销/恢复原因
     */
    @Column(name = "operate_reason")
    @ApiModelProperty(value = "撤销/恢复原因")
    private String operateReason;

    /**
     * 撤销/恢复附件
     */
    @Column(name = "operate_files")
    @ApiModelProperty(value = "撤销/恢复附件")
    private String operateFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}