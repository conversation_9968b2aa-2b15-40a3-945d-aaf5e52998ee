<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.daily.dao.LeaderDailyMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.daily.model.LeaderDaily">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="daily_begin_date" jdbcType="VARCHAR" property="dailyBeginDate" />
    <result column="daily_end_date" jdbcType="VARCHAR" property="dailyEndDate" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="files" jdbcType="VARCHAR" property="files" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="data_storage" jdbcType="LONGVARCHAR" property="dataStorage" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  
  <select id="selectLeaderDailyPageList" resultType="cn.trasen.oa.daily.model.LeaderDaily" parameterType="cn.trasen.oa.daily.model.LeaderDaily">
	  	
		  select a.*,case when ifnull(b.read_number,0)>0 then 1 else 0 end as readStatus from toa_leader_daily a left join toa_leader_daily_read b on a.id=b.daily_id and b.create_user=#{userCode}
		    where 1 =1  and a.is_deleted='N'
		 <!--  <if test="condition !=null and condition !=''">
		  	and (a.name like concat('%',#{condition},'%') or a.pym like concat('%',#{condition},'%') or a.identity_number like concat('%',#{condition},'%') ) 
		  </if> -->
		 	<if test="type !=null and type !='' and type ==0 ">
		  		and a.type = #{type} and  DATE_FORMAT(daily_Begin_Date, '%Y-%m')=#{dailyBeginDate} 
		 	 </if>
		 	 <if test="type !=null and type !='' and type ==1 ">
		  		and a.type = #{type} and  (DATE_FORMAT(daily_Begin_Date, '%Y-%m')=#{dailyBeginDate} or DATE_FORMAT(daily_end_date, '%Y-%m')=#{dailyBeginDate})
		 	 </if>
		 	 <if test="type !=null and type !='' and type ==2 ">
		  		and a.type = #{type} and DATE_FORMAT(daily_Begin_Date, '%Y')=#{dailyBeginDate} 
		 	 </if>
	  </select>
  
    <select id="getAllZbByData" resultType="MAP" parameterType="MAP">
  		select   zbcode,sum(PVALUE) as PVALUE  from  trasen_zbk.cxzb_all where pdate between #{begdate} AND #{enddate}  group by zbcode
  </select>
  
	 <select id="getAllSingleZbData" resultType="MAP" parameterType="MAP">
	    select hj_zsr,mz_zsr,zy_zsr,hj_tjsr,cy_zsr,case when mz_mjzrc=0 then 0 else ROUND(mz_zsr/mz_mjzrc,2) end as mz_jcfy,case when zy_cyrc=0 then 0 else ROUND(cy_zsr/zy_cyrc,2) end as zy_jcfy,
	    	  mz_mjzrc,mz_mzrc,mz_jzrc,mz_yyrc,case when mz_mzrc=0 then 0 else mz_yyrc/mz_mzrc end as mz_yyjzl, 
	    	  zy_zyrc,zy_ryrc,zy_cyrc,zy_ssrc,zy_lgrc,zy_bwrc,zy_bzrc,zy_swrc,zy_fmrc,zy_zyzts,case when zy_cyrc=0 then 0 else ROUND(zy_zyzts/zy_cyrc,2) end as zy_pjzyr,
	    	   zy_bzcws,zy_icu_bzcws,zy_kfcws,
	    	  case when zy_bzcws=0 then 0 else  zy_zyrc/zy_bzcws END AS bzcwsyl  from (
	  		select   IFNULL(sum(case when zbcode in ('0105','0205') then PVALUE else 0 end),0) as hj_zsr,
	  				 IFNULL(sum(case when zbcode='0105' then PVALUE else 0 end),0) as mz_zsr,
	  				 IFNULL(sum(case when zbcode='0205' then PVALUE else 0 end),0) as zy_zsr,
	  				 IFNULL(sum(case when zbcode='03051' then PVALUE else 0 end),0) as hj_tjsr,
	  				 IFNULL(sum(case when zbcode='02053' then PVALUE else 0 end),0) as cy_zsr,
	  				 
	  				 ROUND(IFNULL(sum(case when zbcode='0101' then PVALUE else 0 end),0)) as mz_mjzrc,
	  				 ROUND(IFNULL(sum(case when zbcode='0102' then PVALUE else 0 end),0)) as mz_mzrc,
	  				 ROUND(IFNULL(sum(case when zbcode='0103' then PVALUE else 0 end),0)) as mz_jzrc,
	  				 ROUND(IFNULL(sum(case when zbcode='0117' then PVALUE else 0 end),0)) as mz_yyrc,
	  				 
	  				 
	  				   ROUND(IFNULL(sum(case when zbcode='0201' then PVALUE else 0 end),0)) as zy_zyrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0202' then PVALUE else 0 end),0)) as zy_ryrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0203' then PVALUE else 0 end),0)) as zy_cyrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0215' then PVALUE else 0 end),0)) as zy_ssrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0251' then PVALUE else 0 end),0)) as zy_lgrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0204' then PVALUE else 0 end),0)) as zy_bwrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0228' then PVALUE else 0 end),0)) as zy_bzrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0232' then PVALUE else 0 end),0)) as zy_swrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0231' then PVALUE else 0 end),0)) as zy_fmrc,
	  				   ROUND(IFNULL(sum(case when zbcode='0216' then PVALUE else 0 end),0)) as zy_zyzts,
	  				   
	  				   ROUND((SELECT IFNULL(SUM(BED_NUM),0)  FROM trasen_zbk.jc_bas_org_department where 0=0 and IS_DELETE='N' and  ENABLED='Y')) as zy_bzcws,
	  				   ROUND((SELECT IFNULL(SUM(CASE WHEN (DEPT_NAME LIKE '重症%' OR  DEPT_NAME LIKE '%ICU%') THEN BED_NUM ELSE 0 END),0) FROM trasen_zbk.jc_bas_org_department where 0=0 and IS_DELETE='N' and  ENABLED='Y')) as zy_icu_bzcws,
	  				   ROUND(IFNULL(sum(case when zbcode='0217' then PVALUE else 0 end),0)) as zy_kfcws
	  				    from  trasen_zbk.cxzb_all  where pdate between #{begdate} AND #{enddate}
	  		  		 )z  
	  </select>
  
  
   <select id="getHospDeptZbData" resultType="MAP" parameterType="MAP">
   select IFNULL(dept,'合计') as dept,sum(zy_zyrc) zy_zyrc,sum(zy_ryrc) zy_ryrc, sum(zy_cyrc) zy_cyrc,sum(zy_bwrc) zy_bwrc,sum(zy_bzrc) zy_bzrc,sum(zy_ssrc) zy_ssrc,sum(zy_yjssrc) zy_yjssrc,sum(zy_ejssrc) zy_ejssrc,sum(zy_sanjssrc) zy_sanjssrc,
   		  sum(zy_sijssrc) zy_sijssrc,sum(cws) cws,case when 0>(sum(cws)-sum(zy_zyrc)) then 0 else (sum(cws)-sum(zy_zyrc)) end kcs,CASE WHEN sum(cws)=0 THEN 0 ELSE sum(zy_zyrc)/sum(cws) END AS bzcwsyl  from  (
	    select a.deptcode,a.dept,ROUND(IFNULL(zy_zyrc,0)/(datediff(#{enddate},#{begdate})+1),0) zy_zyrc,ROUND(IFNULL(zy_bwrc,0)/(datediff(#{enddate},#{begdate})+1),0) zy_bwrc,ROUND(IFNULL(zy_bzrc,0)/(datediff(#{enddate},#{begdate})+1),0) zy_bzrc, ROUND(IFNULL(zy_ryrc,0)) zy_ryrc,
			 ROUND(IFNULL(zy_cyrc,0)) zy_cyrc,ROUND(IFNULL(zy_ssrc,0)) zy_ssrc,ROUND(IFNULL(zy_yjssrc,0)) zy_yjssrc,ROUND(IFNULL(zy_ejssrc,0)) zy_ejssrc,ROUND(IFNULL(zy_sanjssrc,0)) zy_sanjssrc,ROUND(IFNULL(zy_sijssrc,0)) zy_sijssrc,ROUND(IFNULL(b.BED_NUM,0)) cws  from (
				select deptcode,dept,sum(case when zbcode='0201' then PVALUE else 0 end) as zy_zyrc,sum(case when zbcode='0202' then PVALUE else 0 end) as zy_ryrc,
					   sum(case when zbcode='0203' then PVALUE else 0 end) as zy_cyrc,sum(case when zbcode='0204' then PVALUE else 0 end) as zy_bwrc,
					   sum(case when zbcode='0228' then PVALUE else 0 end) as zy_bzrc,sum(case when zbcode='0215' then PVALUE else 0 end) as zy_ssrc,
					   sum(case when zbcode='02151' then PVALUE else 0 end) as zy_yjssrc,sum(case when zbcode='02152' then PVALUE else 0 end) as zy_ejssrc,
					   sum(case when zbcode='02153' then PVALUE else 0 end) as zy_sanjssrc,sum(case when zbcode='02154' then PVALUE else 0 end) as zy_sijssrc
					    from trasen_zbk.cxzb_all  WHERE   PDATE BETWEEN #{begdate} AND #{enddate}  AND ZBCODE in ('0201','0202','0203','0204','0228','0215','02151','02152','02153','02154')
						group by deptcode,dept
				)a
					left  join (SELECT DEPT_CODE,BED_NUM FROM trasen_zbk.jc_bas_org_department  WHERE  0=0 and IS_DELETE='N' and  ENABLED='Y' AND BED_NUM>0
					)b on a.deptcode=b.DEPT_CODE
	)z  group by dept WITH ROLLUP
				
  </select>
  
</mapper>