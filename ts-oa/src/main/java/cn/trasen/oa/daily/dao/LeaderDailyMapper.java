package cn.trasen.oa.daily.dao;

import java.util.List;
import java.util.Map;
import tk.mybatis.mapper.common.Mapper;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.daily.model.LeaderDaily;

public interface LeaderDailyMapper extends Mapper<LeaderDaily>{

	List<Map<String, Object>> getAllZbByData(Map<String, Object> map);
	
	Map<String, Object> getAllSingleZbData(Map<String, Object> map);
	
	List<Map<String, Object>> getHospDeptZbData(Map<String, Object> map);
	
	List<LeaderDaily> selectLeaderDailyPageList(Page page, LeaderDaily record);
	
}
