package cn.trasen.oa.boardroom.model;

import javax.persistence.Table;

import cn.trasen.homs.core.model.BaseBean;
import lombok.Data;

/**
 * 机器
 * <AUTHOR>
 * @createTime 2021/9/13 12:38
 * @description
 */
@Table(name = "toa_boardroom_machine")
@Data
public class BoardroomMachine extends BaseBean {

    /**
     * 会议室ID
     */
    String boardroomId;

    /**
     * 设备ID
     */
    String machineCode;
    /**
     * 1扫脸
     */
    Integer machineType;
}