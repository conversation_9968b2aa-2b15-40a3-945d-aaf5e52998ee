package cn.trasen.oa.boardroom.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/21 13:59
 */
@Setter
@Getter
public class BoardRoomSigninListRes {
    /**
     * 申请ID
     */
    @ApiModelProperty(value = "申请ID")
    private String applyId;

    /**
     * 申请时间表ID
     */
    @ApiModelProperty(value = "申请时间表ID")
    private String meetingTimeId;

    /**
     * 会议室名称
     */
    @ApiModelProperty(value = "会议室名称")
    private String boardroomName;


    /**
     * 签到人userCode
     */
    @ApiModelProperty(value = "签到人userCode")
    private String signinUsercode;

    /**
     * 签到人名称
     */
    @ApiModelProperty(value = "签到人名称")
    private String signinUsername;

    /**
     * 签到时间
     */
    @ApiModelProperty(value = "签到时间")
    private Date signinTime;

    /**
     * 0未签到 ， 1已签到,2已请假,3 迟到
     */
    @ApiModelProperty(value = "0未签到 ， 1已签到,2已请假,3 迟到")
    private String signinStatus;

    /**
     * 0未签到 ， 1已签到,2已请假,3 迟到
     */
    @ApiModelProperty(value = "0未签到 ， 1已签到,2已请假,3 迟到")
    private String signinStatusLable;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 签退时间
     */
    @ApiModelProperty(value = "签退时间")
    private Date signinOutTime;

    /**
     * 签退状态(1：已签退)
     */
    @ApiModelProperty(value = "签退状态(1：已签退)")
    private String signinOutStatus;


    /**
     * 签退状态(1：已签退)
     */
    @ApiModelProperty(value = "签退状态(1：已签退)")
    private String signinOutStatusLable;
    /**
     * 1 邀请，2未邀请
     */
    @ApiModelProperty(value = "1 邀请，2未邀请")
    private String invitee;


    /**
     * 签到人部门名称
     */
    @ApiModelProperty(value = "签到人部门名称")
    private String signinOrgName;

    /**
     * 签到人部门编码
     */
    @ApiModelProperty(value = "签到人部门编码")
    private String signinOrgCode;


    /**
     * 签到人电话
     */
    @ApiModelProperty(value = "签到人电话")
    private String signinPhoneNumber;


}