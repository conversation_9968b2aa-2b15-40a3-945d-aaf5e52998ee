package cn.trasen.oa.boardroom.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.DataBaseProvider;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingLeaveReq;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingSigninCountRes;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingTimeReq;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingTimeRes;
import cn.trasen.oa.boardroom.bean.BoardRoomSignInSaveReq;
import cn.trasen.oa.boardroom.bean.BoardRoomSigninListRes;
import cn.trasen.oa.boardroom.bean.BoardroomSignRes;
import cn.trasen.oa.boardroom.bean.BoardroomSigninCountRes;
import cn.trasen.oa.boardroom.bean.BoardroomSigninListReq;
import cn.trasen.oa.boardroom.bean.BoardroomSigninReq;
import cn.trasen.oa.boardroom.bo.BoardRoomApplyListInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomSigninListInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomSigninListOutBO;
import cn.trasen.oa.boardroom.bo.BoardroomApplyInBO;
import cn.trasen.oa.boardroom.bo.BoardroomMeetingTimeListInBO;
import cn.trasen.oa.boardroom.enums.SignOutStatusEnum;
import cn.trasen.oa.boardroom.enums.SigninStatusEnum;
import cn.trasen.oa.boardroom.mapper.BoardRoomSignInMapper;
import cn.trasen.oa.boardroom.model.BoardroomApply;
import cn.trasen.oa.boardroom.model.BoardroomMeetingTime;
import cn.trasen.oa.boardroom.model.BoardroomSignin;
import cn.trasen.oa.boardroom.service.BoardRoomApplyService;
import cn.trasen.oa.boardroom.service.BoardRoomMeetingTimeService;
import cn.trasen.oa.boardroom.service.BoardRoomSignInService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/10/15 16:33
 */
@Service
public class BoardRoomSignInServiceImpl implements BoardRoomSignInService {

    @Autowired
    BoardRoomSignInMapper boardRoomSignInMapper;

    @Autowired
    BoardRoomMeetingTimeService boardRoomMeetingTimeService;

    @Autowired
    HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    BoardRoomApplyService boardRoomApplyService;


    @Autowired
    InformationFeignService informationFeignService;
    @Autowired
    BoardRoomApplyServiceImpl boardRoomApplyServiceImpl;

    @Autowired
    AppConfigProperties appConfigProperties;

    /**
     * 获取签到基础数据
     *
     * @param boardroomSigninReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomSigninReq]
     * <AUTHOR>
     * @date 2021/10/21 10:33
     */
    public BoardroomSignin getBase(BoardroomSigninReq boardroomSigninReq) {
        Example example = new Example(BoardroomSignin.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isBlank(boardroomSigninReq.getId()) == false) {
            criteria.andEqualTo("id", boardroomSigninReq.getId());
        }
        if (StringUtils.isBlank(boardroomSigninReq.getMeetingId()) == false) {
            criteria.andEqualTo("meetingTimeId", boardroomSigninReq.getMeetingId());
        }
        if (StringUtils.isBlank(boardroomSigninReq.getSigninUsercode()) == false) {
            criteria.andEqualTo("signinUsercode", boardroomSigninReq.getSigninUsercode());
        }
        example.setOrderByClause(" id desc LIMIT 1");
        return boardRoomSignInMapper.selectOneByExample(example);
    }


    @Override
    /**
    * 获取基础数据列表
    * @param boardroomSigninListReq
    * @return java.util.List<cn.trasen.oa.boardroom.model.BoardroomSignin>
    * <AUTHOR>
    * @date 2021/10/28 10:14
    */
    public List<BoardroomSignin> getBaseList(BoardroomSigninListReq boardroomSigninListReq) {
        Example example = new Example(BoardroomSignin.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (CollectionUtils.isEmpty(boardroomSigninListReq.getMeetingIdList())==false) {
            criteria.andIn("meetingTimeId", boardroomSigninListReq.getMeetingIdList());
        }
        if (StringUtils.isBlank(boardroomSigninListReq.getMeetingId()) == false) {
            criteria.andEqualTo("meetingTimeId", boardroomSigninListReq.getMeetingId());
        }

        if (CollectionUtils.isEmpty(boardroomSigninListReq.getApplyIdList())==false) {
            criteria.andIn("applyId", boardroomSigninListReq.getApplyIdList());
        }

        if (StringUtils.isBlank(boardroomSigninListReq.getApplyId()) == false) {
            criteria.andEqualTo("applyId", boardroomSigninListReq.getApplyId());
        }
        if (StringUtils.isBlank(boardroomSigninListReq.getInvitee()) == false) {
            criteria.andEqualTo("invitee", boardroomSigninListReq.getInvitee());
        }
        if (StringUtils.isBlank(boardroomSigninListReq.getSigninStatus()) == false) {
            criteria.andEqualTo("signinStatus", boardroomSigninListReq.getSigninStatus());
        }


        List<BoardroomSignin> boardroomSigninList = boardRoomSignInMapper.selectByExample(example);

        return boardroomSigninList;
    }


    @Override
    /**
     * 获取数据
     * @param boardroomSigninListReq
     * @return java.util.List<cn.trasen.oa.boardroom.model.BoardroomSignin>
     * <AUTHOR>
     * @date 2021/10/28 10:14
     */
    public BoardroomSignRes get(BoardroomSigninReq boardroomSigninReq) {
        BoardroomSignin boardroomSignin = getBase(boardroomSigninReq);
        if (boardroomSignin == null) {
            return null;
        }
        BoardroomSignRes boardroomSigninResp = BeanUtil.copyProperties(boardroomSignin, BoardroomSignRes.class);
        List<BoardRoomSigninListRes> boardRoomSigninListResList = new ArrayList<>();
        BoardRoomSigninListRes boardRoomSigninListRes = BeanUtil.copyProperties(boardroomSigninResp, BoardRoomSigninListRes.class);
        boardRoomSigninListResList.add(boardRoomSigninListRes);
        initDataOld(boardRoomSigninListResList);

        return BeanUtil.copyProperties(boardRoomSigninListResList.get(0), BoardroomSignRes.class);
    }


    @Override
    /**
     * 获取列表
     * @param boardroomSigninListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomSigninListReq]
     * <AUTHOR>
     * @date 2021/10/21 14:48
     */
    public List<BoardRoomSigninListOutBO> list(BoardRoomSigninListInBO boardroomSigninListInBO) {
        Example example = new Example(BoardroomSignin.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isBlank(boardroomSigninListInBO.getMeetingId()) == false) {
            criteria.andEqualTo("meetingTimeId", boardroomSigninListInBO.getMeetingId());
        }

        List<BoardroomSignin> boardroomSigninList = boardRoomSignInMapper.selectByExample(example);

        List<BoardRoomSigninListOutBO> boardRoomSigninListResList = BeanUtil.copyToList(boardroomSigninList, BoardRoomSigninListOutBO.class);

        initData(boardRoomSigninListResList);

        return boardRoomSigninListResList;
    }

    @Override
    /**
     * 获取列表
     * @param boardroomSigninListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomSigninListReq]
     * <AUTHOR>
     * @date 2021/10/21 14:48
     */
    public List<BoardRoomSigninListRes> getList(BoardroomSigninListReq boardroomSigninListReq) {
        Example example = new Example(BoardroomSignin.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isBlank(boardroomSigninListReq.getMeetingId()) == false) {
            criteria.andEqualTo("meetingTimeId", boardroomSigninListReq.getMeetingId());
        }
        if (StringUtils.isBlank(boardroomSigninListReq.getApplyId()) == false) {
            criteria.andEqualTo("applyId", boardroomSigninListReq.getApplyId());
        }
        if (StringUtils.isBlank(boardroomSigninListReq.getInvitee()) == false) {
            criteria.andEqualTo("invitee", boardroomSigninListReq.getInvitee());
        }
//    	0未签到 ， 1已签到,2已请假,3 迟到
        if (!StringUtils.isEmpty(boardroomSigninListReq.getSigninStatus()) && "4".equals(boardroomSigninListReq.getSigninStatus())) {
	    	criteria.andEqualTo("signinOutStatus", "1");
        }else{
        	if(StringUtils.isNotBlank(boardroomSigninListReq.getSigninStatus())){
        		criteria.andEqualTo("signinStatus", boardroomSigninListReq.getSigninStatus());
        	}
        }
	
//        example.setOrderByClause("SIGNIN_TIME is null, SIGNIN_TIME ASC");
//    	case when status=1 then 0  when status=3 then 0 else status  end desc
//		example.setOrderByClause("SIGNIN_TIME is null, FIELD(signinStatus,'1','2','3','0'), SIGNIN_TIME ASC");
        if(!StringUtils.isEmpty(boardroomSigninListReq.getSigninStatus()) && "4".equals(boardroomSigninListReq.getSigninStatus())){
        	example.orderBy("signinOutTime").desc();
        }else{
        	if (DataBaseProvider.databaseId.equalsIgnoreCase("mysql")) {
        		example.setOrderByClause("SIGNIN_TIME is null, FIELD(SIGNIN_STATUS,'1','2','3','0'), SIGNIN_TIME ASC");
        	}
        	if (DataBaseProvider.databaseId.equalsIgnoreCase("kingbase")) {
        		example.setOrderByClause("SIGNIN_TIME is null, case SIGNIN_STATUS when '1' then '1' when '2' then '2' when '3' then '3' when '0' then '0' end, SIGNIN_TIME ASC");
        	}
        }
        List<BoardroomSignin> boardroomSigninList = boardRoomSignInMapper.selectByExample(example);

        List<BoardRoomSigninListRes> boardRoomSigninListResList = BeanUtil.copyToList(boardroomSigninList, BoardRoomSigninListRes.class);

        initDataOld(boardRoomSigninListResList);

        return boardRoomSigninListResList;
    }

    /**
     * 填充数据
     * @param boardRoomSigninListOutBOList
     * @return void
     * <AUTHOR>
     * @date 2021/12/22 9:26
     */
    private void initData(List<BoardRoomSigninListOutBO> boardRoomSigninListOutBOList)
    {
        List<String> employeeNos = new ArrayList<>();
        for (BoardRoomSigninListOutBO boardRoomSigninListRes : boardRoomSigninListOutBOList) {
            employeeNos.add(boardRoomSigninListRes.getSigninUsercode());
        }

        List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(employeeNos).getObject();
        for (BoardRoomSigninListOutBO boardRoomSigninListRes : boardRoomSigninListOutBOList) {
            for (EmployeeResp employeeResp : employeeRespList) {
                if(StringUtils.isBlank(boardRoomSigninListRes.getSigninUsercode()))
                {
                    break;
                }
                if (boardRoomSigninListRes.getSigninUsercode().equals(employeeResp.getEmployeeNo())) {
                    boardRoomSigninListRes.setSigninUsername(employeeResp.getEmployeeName());
                    boardRoomSigninListRes.setSigninOrgCode(employeeResp.getOrgCode());
                    boardRoomSigninListRes.setSigninOrgName(employeeResp.getOrgName());
                    boardRoomSigninListRes.setSigninPhoneNumber(employeeResp.getPhoneNumber());
                    break;
                }
            }
            boardRoomSigninListRes.setSigninStatusLable(SigninStatusEnum.getValByKey(boardRoomSigninListRes.getSigninStatus()));
            boardRoomSigninListRes.setSigninOutStatusLable(SignOutStatusEnum.getValByKey(boardRoomSigninListRes.getSigninOutStatus()));
        }
    }

    /**
    * 填充数据
    * @param boardRoomSigninListResList
    * @return void
    * <AUTHOR>
    * @date 2021/12/22 9:26
    */
    private void initDataOld(List<BoardRoomSigninListRes> boardRoomSigninListResList)
    {
        List<String> employeeNos = new ArrayList<>();
        for (BoardRoomSigninListRes boardRoomSigninListRes : boardRoomSigninListResList) {
            employeeNos.add(boardRoomSigninListRes.getSigninUsercode());
        }

        List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(employeeNos).getObject();
        for (BoardRoomSigninListRes boardRoomSigninListRes : boardRoomSigninListResList) {
            for (EmployeeResp employeeResp : employeeRespList) {
                if(StringUtils.isBlank(boardRoomSigninListRes.getSigninUsercode()))
                {
                    break;
                }
                if (boardRoomSigninListRes.getSigninUsercode().equals(employeeResp.getEmployeeNo())) {
                    boardRoomSigninListRes.setSigninUsername(employeeResp.getEmployeeName());
                    boardRoomSigninListRes.setSigninOrgCode(employeeResp.getOrgCode());
                    boardRoomSigninListRes.setSigninOrgName(employeeResp.getOrgName());
                    boardRoomSigninListRes.setSigninPhoneNumber(employeeResp.getPhoneNumber());
                    break;
                }
            }
            boardRoomSigninListRes.setSigninStatusLable(SigninStatusEnum.getValByKey(boardRoomSigninListRes.getSigninStatus()));
            boardRoomSigninListRes.setSigninOutStatusLable(SignOutStatusEnum.getValByKey(boardRoomSigninListRes.getSigninOutStatus()));
        }
    }

    @Override
    /**
     * 保存参会人员
     *
     * @param boardRoomSignInSaveReqList
     * @return [java.util.List<cn.trasen.oa.boardroom.bean.BoardRoomSignInSaveReq>]
     * <AUTHOR>
     * @date 2021/10/15 16:39
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(List<BoardRoomSignInSaveReq> boardRoomSignInSaveReqList) {
        if (CollectionUtils.isEmpty(boardRoomSignInSaveReqList)) {
            return;
        }
        Example example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("applyId", boardRoomSignInSaveReqList.get(0).getApplyId());
        boardRoomSignInMapper.deleteByExample(example);
        //批量写入需要优化
        for (BoardRoomSignInSaveReq boardRoomSignInSaveReq : boardRoomSignInSaveReqList) {
            BoardroomSignin boardroomSignin = BeanUtils.addInitBean(BoardroomSignin.class);
            BeanUtil.copyProperties(boardRoomSignInSaveReq, boardroomSignin, "id");
            boardroomSignin.setSigninStatus("0");
            boardroomSignin.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            boardRoomSignInMapper.insertSelective(boardroomSignin);
        }
    }


    @Override
    /**
     * 批量修改会议室ID
     * @param applyId
     * @param meetingTimeId
     * @return [java.lang.String, java.lang.String]
     * <AUTHOR>
     * @date 2021/10/21 17:05
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingId(String applyId, String meetingTimeId) {
        Example example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("applyId", applyId);
        BoardroomSignin boardroomSigninUpdate = new BoardroomSignin();
        BeanUtils.updateInitBean(boardroomSigninUpdate);
        boardroomSigninUpdate.setMeetingTimeId(meetingTimeId);
        boardRoomSignInMapper.updateByExampleSelective(boardroomSigninUpdate, example);

    }


    @Override
    /**
     * 签到
     *
     * @param meetingId invitee 1 邀请，0所有
     * @return [java.lang.String] 1签到成功 2 已经签到过 -1不在邀请范围内
     * <AUTHOR>
     * @date 2021/10/21 10:35
     */
    @Transactional(rollbackFor = Exception.class)
    public BoardroomSignRes signIn(String meetingId) {
        return  signIn(meetingId,0);
    }
    @Override
    /**
     * 签到
     *
     * @param meetingId invitee 1 邀请，0所有
     * @return [java.lang.String] 1签到成功 2 已经签到过 -1不在邀请范围内
     * <AUTHOR>
     * @date 2021/10/21 10:35
     */
    @Transactional(rollbackFor = Exception.class)
    public BoardroomSignRes signIn(String meetingId, Integer invitee) {
        BoardroomSignRes boardroomSigninResp=new BoardroomSignRes();
        BoardroomSigninReq boardroomSigninReq = new BoardroomSigninReq();
        boardroomSigninReq.setMeetingId(meetingId);
        boardroomSigninReq.setSigninUsercode(UserInfoHolder.getCurrentUserCode());
        BoardroomSignin boardroomSignin = getBase(boardroomSigninReq);
        if (boardroomSignin != null) {
            boardroomSigninResp=BeanUtil.copyProperties(boardroomSignin, BoardroomSignRes.class);
            if (
                    "0".equals(boardroomSignin.getSigninStatus())
                    ||
                            "2".equals(boardroomSignin.getSigninStatus())
            ) {
                BoardroomSignin boardroomSigninUpdate = new BoardroomSignin();
                BeanUtils.updateInitBean(boardroomSigninUpdate);
                boardroomSigninUpdate.setId(boardroomSignin.getId());
                boardroomSigninUpdate.setSigninStatus("1");
                boardroomSigninUpdate.setSigninTime(new Date());
                boardroomSigninUpdate.setRemarks("");
                boardRoomSignInMapper.updateByPrimaryKeySelective(boardroomSigninUpdate);
                boardroomSigninResp.setSignIng(1);
                boardroomSigninResp.setSigninStatus("1");
                boardroomSigninResp.setSigninTime(new Date());
            } else {
                boardroomSigninResp.setSignIng(2);
            }
            return  boardroomSigninResp;

        } else {

            if(invitee.equals(0)) {
                BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
                boardRoomMeetingTimeReq.setMeetingId(meetingId);
                BoardroomMeetingTime boardroomMeetingTime = boardRoomMeetingTimeService.getBase(boardRoomMeetingTimeReq);
                if (boardroomMeetingTime == null) {
                    throw new BusinessException("会议不存在！");
                }
                boardroomSignin = BeanUtils.addInitBean(BoardroomSignin.class);
                boardroomSignin.setSigninStatus("1");
                boardroomSignin.setSigninTime(new Date());
                boardroomSignin.setSigninUsercode(UserInfoHolder.getCurrentUserCode());
                boardroomSignin.setMeetingTimeId(meetingId);
                boardroomSignin.setApplyId(boardroomMeetingTime.getApplyId());
                boardroomSignin.setInvitee("2");
                boardroomSignin.setRemarks("临时参会");
                boardroomSignin.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                boardRoomSignInMapper.insertSelective(boardroomSignin);
                //return 1;
                boardroomSigninResp=BeanUtil.copyProperties(boardroomSignin, BoardroomSignRes.class);
                boardroomSigninResp.setSignIng(1);

                return  boardroomSigninResp;

            }
        }
        throw new BusinessException("不是受邀员工无法签到！");
    }


    @Override
    /**
     * 签退
     *
     * @param meetingId
     * @return [java.lang.String] 1签到成功 2 已经签到过
     * <AUTHOR>
     * @date 2021/10/21 10:26
     */
    @Transactional(rollbackFor = Exception.class)
    public BoardroomSignRes signOut(String meetingId) {
        return  signOut(meetingId,0);
    }

    @Override
    /**
     * 签退
     *
     * @param meetingId
     * @return [java.lang.String] 1签到成功 2 已经签到过
     * <AUTHOR>
     * @date 2021/10/21 10:26
     */
    @Transactional(rollbackFor = Exception.class)
    public BoardroomSignRes signOut(String meetingId, Integer invitee) {
        BoardroomSignRes boardroomSigninResp;

        BoardroomSigninReq boardroomSigninReq = new BoardroomSigninReq();
        boardroomSigninReq.setMeetingId(meetingId);
        boardroomSigninReq.setSigninUsercode(UserInfoHolder.getCurrentUserCode());
        BoardroomSignin boardroomSignin = getBase(boardroomSigninReq);
        if (boardroomSignin != null) {
            boardroomSigninResp = BeanUtil.copyProperties(boardroomSignin, BoardroomSignRes.class);
            if (StringUtils.isBlank(boardroomSignin.getSigninOutStatus())) {
                BoardroomSignin boardroomSigninUpdate = new BoardroomSignin();
                BeanUtils.updateInitBean(boardroomSigninUpdate);
                boardroomSigninUpdate.setId(boardroomSignin.getId());
                boardroomSigninUpdate.setSigninOutStatus("1");
                boardroomSigninUpdate.setSigninOutTime(new Date());
                boardRoomSignInMapper.updateByPrimaryKeySelective(boardroomSigninUpdate);
                boardroomSigninResp.setSignOutIng(1);
            } else {
                boardroomSigninResp.setSignOutIng(2);
            }
            return boardroomSigninResp;


        } else {
            if (invitee.equals(0)) {
                BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
                boardRoomMeetingTimeReq.setMeetingId(meetingId);
                BoardroomMeetingTime boardroomMeetingTime = boardRoomMeetingTimeService.getBase(boardRoomMeetingTimeReq);
                if (boardroomMeetingTime == null) {
                    throw new BusinessException("会议不存在！");
                }
                boardroomSignin = BeanUtils.addInitBean(BoardroomSignin.class);
                boardroomSignin.setSigninTime(new Date());
                boardroomSignin.setMeetingTimeId(meetingId);
                boardroomSignin.setApplyId(boardroomMeetingTime.getApplyId());
                boardroomSignin.setSigninOutStatus("1");
                boardroomSignin.setSigninOutTime(new Date());
                boardroomSignin.setRemarks("临时参会");
                boardroomSignin.setSigninUsercode(UserInfoHolder.getCurrentUserCode());
                boardroomSignin.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                boardRoomSignInMapper.insertSelective(boardroomSignin);
                boardroomSigninResp = BeanUtil.copyProperties(boardroomSignin, BoardroomSignRes.class);
                boardroomSigninResp.setSignOutIng(1);
                return boardroomSigninResp;
            }
            throw new BusinessException("不是受邀员工无法签退！");
        }
    }
    @Override
    /**
     * 请假
     * @param boardRoomMeetingLeaveReq
     * @return [cn.trasen.oa.boardroom.bean.BoardRoomMeetingLeaveReq]
     * <AUTHOR>
     * @date 2021/10/21 11:35
     */
    @Transactional(rollbackFor = Exception.class)
    public void leave(BoardRoomMeetingLeaveReq boardRoomMeetingLeaveReq) {
        BoardroomSigninReq boardroomSigninReq = new BoardroomSigninReq();
        boardroomSigninReq.setMeetingId(boardRoomMeetingLeaveReq.getMeetingId());
        boardroomSigninReq.setSigninUsercode(UserInfoHolder.getCurrentUserCode());
        BoardroomSignin boardroomSignin = getBase(boardroomSigninReq);
        if (boardroomSignin != null) {
            BoardroomSignin boardroomSigninUpdate = new BoardroomSignin();
            BeanUtils.updateInitBean(boardroomSigninUpdate);
            boardroomSigninUpdate.setId(boardroomSignin.getId());
            boardroomSigninUpdate.setSigninStatus("2");
            boardroomSigninUpdate.setSigninTime(new Date());
            boardroomSigninUpdate.setReasonleave(boardRoomMeetingLeaveReq.getRemark());
            boardroomSigninUpdate.setRemarks(boardRoomMeetingLeaveReq.getRemark());
            boardRoomSignInMapper.updateByPrimaryKeySelective(boardroomSigninUpdate);


            BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
            boardRoomMeetingTimeReq.setMeetingId(boardroomSignin.getMeetingTimeId());
            BoardRoomMeetingTimeRes boardRoomMeetingTimeRes = boardRoomMeetingTimeService.get(boardRoomMeetingTimeReq);

            if (boardRoomMeetingTimeRes.getApplyEmp().equals(UserInfoHolder.getCurrentUserCode()) == false) {
                NoticeReq notice =
                        NoticeReq.builder()
                                .content("主题：" + boardRoomMeetingTimeRes.getMotif() + "\n时间：" + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardRoomMeetingTimeRes.getEndTime(), "HH:mm") + "\n" + boardRoomMeetingLeaveReq.getRemark())
                                .noticeType("3")
                                .receiver(boardRoomMeetingTimeRes.getApplyEmp())  //接收人
                                .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                                .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                                .subject(UserInfoHolder.getCurrentUserName() + "无法出席会议，请您知悉")
                                .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                                .wxSendType("1")
                                .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                                .build();
                informationFeignService.sendNotice(notice);

            }
        }
    }





    @Override
    /**
     * 获取统计
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/21 16:21
     */
    public  List<BoardRoomMeetingSigninCountRes> getCount(List<String> meetingIdList) {

        if(CollectionUtils.isEmpty(meetingIdList))
        {
            return  new ArrayList<>();
        }

        BoardroomMeetingTimeListInBO boardroomMeetingTimeListInBO = new BoardroomMeetingTimeListInBO();
        boardroomMeetingTimeListInBO.setMeetingIdList(meetingIdList);
        List<BoardroomMeetingTime> boardroomMeetingTimeList = boardRoomMeetingTimeService.getListBase(boardroomMeetingTimeListInBO);
        List<String> applyIdList = new ArrayList<>();
        for (BoardroomMeetingTime b : boardroomMeetingTimeList) {
            applyIdList.add(b.getApplyId());
        }
        BoardRoomApplyListInBO boardRoomApplyListInBO = new BoardRoomApplyListInBO();
        boardRoomApplyListInBO.setApplyIdList(applyIdList);
        List<BoardroomApply> boardroomApplyList = boardRoomApplyService.getBaseList(boardRoomApplyListInBO);
        BoardRoomMeetingSigninCountRes boardRoomMeetingSigninCountRes;
        List<BoardRoomMeetingSigninCountRes> boardRoomMeetingSigninCountResList = new ArrayList<>();

        BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
        boardroomSigninListReq.setMeetingIdList(meetingIdList);
        List<BoardroomSignin> boardroomSigninList = getBaseList(boardroomSigninListReq);

        for (BoardroomMeetingTime b : boardroomMeetingTimeList) {
            boardRoomMeetingSigninCountRes = new BoardRoomMeetingSigninCountRes();
            boardRoomMeetingSigninCountRes.setMeetingId(b.getId());
            BoardRoomMeetingSigninCountRes.Count count = getCount(boardroomSigninList, b.getId());
            for (BoardroomApply boardroomApply : boardroomApplyList) {
                if (boardroomApply.getId().equals(b.getApplyId())) {
                    if (StringUtils.isBlank(boardroomApply.getControlNumber())) {
                        count.setPlanNum(0);
                    } else {
                        count.setPlanNum(Integer.valueOf(boardroomApply.getControlNumber()));

                    }
                }
            }
            boardRoomMeetingSigninCountRes.setCount(count);
            boardRoomMeetingSigninCountResList.add(boardRoomMeetingSigninCountRes);
        }
        return boardRoomMeetingSigninCountResList;
    }


//    @Override
//    /**
//     * 获取统计
//     *
//     * @param meetingId
//     * @return [java.lang.String]
//     * <AUTHOR>
//     * @date 2021/10/21 16:21
//     */
//    public  List<BoardRoomMeetingSigninCountRes> getCount(List<String> meetingIdList) {
//
//        if(CollectionUtils.isEmpty(meetingIdList))
//        {
//            return  new ArrayList<>();
//        }
//
//        BoardroomMeetingTimeListInBO boardroomMeetingTimeListInBO = new BoardroomMeetingTimeListInBO();
//        boardroomMeetingTimeListInBO.setMeetingIdList(meetingIdList);
//        List<BoardroomMeetingTime> boardroomMeetingTimeList = boardRoomMeetingTimeService.getListBase(boardroomMeetingTimeListInBO);
//        List<String> applyIdList = new ArrayList<>();
//        for (BoardroomMeetingTime b : boardroomMeetingTimeList) {
//            applyIdList.add(b.getApplyId());
//        }
//        BoardRoomApplyListInBO boardRoomApplyListInBO = new BoardRoomApplyListInBO();
//        boardRoomApplyListInBO.setApplyIdList(applyIdList);
//        List<BoardroomApply> boardroomApplyList = boardRoomApplyService.getBaseList(boardRoomApplyListInBO);
//        BoardRoomMeetingSigninCountRes boardRoomMeetingSigninCountRes;
//        List<BoardRoomMeetingSigninCountRes> boardRoomMeetingSigninCountResList = new ArrayList<>();
//
//        BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
//        boardroomSigninListReq.setMeetingIdList(meetingIdList);
//        List<BoardroomSignin> boardroomSigninList = getBaseList(boardroomSigninListReq);
//
//        for (BoardroomMeetingTime b : boardroomMeetingTimeList) {
//            boardRoomMeetingSigninCountRes = new BoardRoomMeetingSigninCountRes();
//            boardRoomMeetingSigninCountRes.setMeetingId(b.getId());
//            BoardRoomMeetingSigninCountRes.Count count = getCount(boardroomSigninList, b.getId());
//            for (BoardroomApply boardroomApply : boardroomApplyList) {
//                if (boardroomApply.getId().equals(b.getApplyId())) {
//                    if (StringUtils.isBlank(boardroomApply.getControlNumber())) {
//                        count.setPlanNum(0);
//                    } else {
//                        count.setPlanNum(Integer.valueOf(boardroomApply.getControlNumber()));
//
//                    }
//                }
//            }
//            boardRoomMeetingSigninCountRes.setCount(count);
//            boardRoomMeetingSigninCountResList.add(boardRoomMeetingSigninCountRes);
//        }
//        return boardRoomMeetingSigninCountResList;
//    }

    /**
    * 获取统计
    * @param boardroomSigninList
    * @param meetingId
    * @return cn.trasen.oa.boardroom.bean.BoardRoomMeetingSigninCountRes.Count
    * <AUTHOR>
    * @date 2022/1/5 16:26
    */
    private BoardRoomMeetingSigninCountRes.Count getCount(List<BoardroomSignin> boardroomSigninList,String meetingId) {
        BoardRoomMeetingSigninCountRes.Count count = new BoardRoomMeetingSigninCountRes.Count();


        /**
         * 未签到
         */
        Integer noSignInNum = 0;


        /**
         * 签到
         */
        Integer signInNum = 0;

        /**
         * 未邀请签到
         */
        Integer noInviteSignInNum = 0;

        /**
         * 请假
         */
        Integer leaveNum = 0;

        /**
         * 签退
         */
        Integer signOut = 0;
        for (BoardroomSignin boardroomSignin : boardroomSigninList) {
            if (boardroomSignin.getMeetingTimeId().equals(meetingId)) {
                if (boardroomSignin.getSigninOutStatus()!=null&&boardroomSignin.getSigninOutStatus().equals("1")) {
                    signOut++;
                }
                if (boardroomSignin.getSigninStatus()!=null&&boardroomSignin.getSigninStatus().equals("1")) {
                    signInNum++;
                }
                if (boardroomSignin.getSigninStatus()!=null&&boardroomSignin.getSigninStatus().equals("0")) {
                    noSignInNum++;
                }
                if (boardroomSignin.getSigninStatus()!=null&&boardroomSignin.getSigninStatus().equals("2")) {
                    leaveNum++;
                }
                if (boardroomSignin.getInvitee()!=null&&boardroomSignin.getSigninStatus()!=null&&
                        boardroomSignin.getInvitee().equals("2") && boardroomSignin.getSigninStatus().equals("1")) {
                    noInviteSignInNum++;
                }
            }
        }
        count.setLeaveNum(leaveNum);
        count.setNoSignInNum(noSignInNum);
        count.setSignOut(signOut);
        count.setSignInNum(signInNum);
        count.setNoInviteSignInNum(noInviteSignInNum);
        return count;
    }

    @Override
    /**
     * 获取统计
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/21 16:21
     */
    public BoardroomSigninCountRes getCount(String meetingId) {
        BoardroomSigninCountRes boardroomSigninCountRes = new BoardroomSigninCountRes();
        BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
        boardRoomMeetingTimeReq.setMeetingId(meetingId);
        BoardroomMeetingTime boardroomMeetingTime = boardRoomMeetingTimeService.getBase(boardRoomMeetingTimeReq);
        BoardroomApplyInBO boardroomApplyReq = new BoardroomApplyInBO();
        boardroomApplyReq.setId(boardroomMeetingTime.getApplyId());
        BoardroomApply boardroomApply = boardRoomApplyService.getBase(boardroomApplyReq);
        if (StringUtils.isBlank(boardroomApply.getControlNumber())) {
            boardroomSigninCountRes.setPlanNum(0);
        } else {
            boardroomSigninCountRes.setPlanNum(Integer.valueOf(boardroomApply.getControlNumber()));

        }

        Example example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("meetingTimeId", meetingId)
                .andEqualTo("signinOutStatus", "1");
        boardroomSigninCountRes.setSignOut(boardRoomSignInMapper.selectCountByExample(example));


        example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("meetingTimeId", meetingId)
                .andEqualTo("signinStatus", "1");
        boardroomSigninCountRes.setSignInNum(boardRoomSignInMapper.selectCountByExample(example));


        example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("meetingTimeId", meetingId)
                .andEqualTo("signinStatus", "0");
        boardroomSigninCountRes.setNoSignInNum(boardRoomSignInMapper.selectCountByExample(example));


        example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("meetingTimeId", meetingId)
                .andEqualTo("signinStatus", "2");
        boardroomSigninCountRes.setLeaveNum(boardRoomSignInMapper.selectCountByExample(example));


        example = new Example(BoardroomSignin.class);
        example.createCriteria().andEqualTo("meetingTimeId", meetingId)
                .andEqualTo("invitee", "2")
                .andEqualTo("signinStatus", "1");
        boardroomSigninCountRes.setNoInviteSignInNum(boardRoomSignInMapper.selectCountByExample(example));
        return boardroomSigninCountRes;
    }


    @Override
    /**
     * 导出
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/22 15:53
     */
    public byte[] export(String meetingId,String signinStatus) throws IOException {
        Resource resource = new ClassPathResource("template/boardRoomSignIn.xlsx");
        XSSFWorkbook wb = new XSSFWorkbook(resource.getInputStream());
        BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
        boardRoomMeetingTimeReq.setMeetingId(meetingId);
        BoardRoomMeetingTimeRes boardRoomMeetingTimeRes = boardRoomMeetingTimeService.get(boardRoomMeetingTimeReq);
        byte[] excelBy = null;
        ByteArrayOutputStream outputStream = null;

        try {
            XSSFSheet sheet = wb.getSheetAt(0);


            sheet.getRow(0).getCell(0).setCellValue(boardRoomMeetingTimeRes.getAppTypeLable()==null?"":"["+boardRoomMeetingTimeRes.getAppTypeLable()+"] "+boardRoomMeetingTimeRes.getMotif());

			if(boardRoomMeetingTimeRes.getMeetingStatus().equals(-1)) {
			    sheet.getRow(1).getCell(0).setCellValue(boardRoomMeetingTimeRes.getLocation() + "-" + boardRoomMeetingTimeRes.getFloor() + "-" + boardRoomMeetingTimeRes.getBoardroomName() + "    预约时段：" + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "HH:mm") + "-" + DateUtil.format(boardRoomMeetingTimeRes.getEndTime(), "HH:mm") + "    实际结束时间：" + DateUtil.format(boardRoomMeetingTimeRes.getMeetingEndTime(), "yyyy-MM-dd HH:mm"));
			}
			else
			{
			    sheet.getRow(1).getCell(0).setCellValue(boardRoomMeetingTimeRes.getLocation() + "-" + boardRoomMeetingTimeRes.getFloor() + "-" + boardRoomMeetingTimeRes.getBoardroomName() + "    预约时段：" + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardRoomMeetingTimeRes.getStartTime(), "HH:mm") + "-" + DateUtil.format(boardRoomMeetingTimeRes.getEndTime(), "HH:mm"));
			}
            sheet.getRow(2).getCell(0).setCellValue("应到" + boardRoomMeetingTimeRes.getBoardroomSigninCount().getPlanNum() + "人    已签到 " + boardRoomMeetingTimeRes.getBoardroomSigninCount().getSignInNum() + "人    临时参会签到 " + boardRoomMeetingTimeRes.getBoardroomSigninCount().getNoInviteSignInNum() + "    未签到 " + boardRoomMeetingTimeRes.getBoardroomSigninCount().getNoSignInNum() + "人    请假 " + boardRoomMeetingTimeRes.getBoardroomSigninCount().getLeaveNum() + "人    签退" + boardRoomMeetingTimeRes.getBoardroomSigninCount().getSignOut() + "人");
            BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
            boardroomSigninListReq.setMeetingId(boardRoomMeetingTimeRes.getMeetingId());
            boardroomSigninListReq.setSigninStatus(signinStatus);
            List<BoardRoomSigninListRes> boardRoomSigninListResList = getList(boardroomSigninListReq);
            int i = 0;
            for (BoardRoomSigninListRes boardRoomSigninListRes : boardRoomSigninListResList) {
                XSSFRow xssfRow = sheet.createRow(i + 4);//
                xssfRow.createCell(0).setCellValue(boardRoomSigninListRes.getSigninOrgName());
                xssfRow.createCell(1).setCellValue(boardRoomSigninListRes.getSigninUsercode());
                xssfRow.createCell(2).setCellValue(boardRoomSigninListRes.getSigninUsername());
                xssfRow.createCell(3).setCellValue(boardRoomSigninListRes.getSigninPhoneNumber());
                xssfRow.createCell(4).setCellValue(boardRoomSigninListRes.getSigninStatusLable());
                xssfRow.createCell(5).setCellValue(DateUtil.format(boardRoomSigninListRes.getSigninTime(),"yyyy-MM-dd HH:mm:ss"));
                xssfRow.createCell(6).setCellValue(DateUtil.format(boardRoomSigninListRes.getSigninOutTime(),"yyyy-MM-dd HH:mm:ss"));
                xssfRow.createCell(7).setCellValue(boardRoomSigninListRes.getRemarks());
                i++;
            }
            outputStream = new ByteArrayOutputStream();
            wb.write(outputStream);
            excelBy = outputStream.toByteArray();
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
        }
        return excelBy;
    }
}