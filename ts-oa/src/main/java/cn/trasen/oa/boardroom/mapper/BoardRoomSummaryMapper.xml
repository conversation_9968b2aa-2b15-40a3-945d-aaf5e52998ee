<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.trasen.oa.boardroom.mapper.BoardRoomSummaryMapper">
    <select id="getList"
            resultType="cn.trasen.oa.boardroom.bean.BoardRoomSummaryListRes">

        select DISTINCT b.* from (

        SELECT
        DISTINCT
        a1.`NAME` AS boardroomName,
        a3.MEETING_TIME_ID,
        a3.ACCESSORY_NAME,
        a3.ACCESSORY_SIZE,
        a3.ACCESSORY_URL,
        a3.UPDATE_DATE,
        a3.UPDATE_USER,
        a3.file_extension,
        a3.ACCESSORY_ID as fileId,
        a4.APPLY_EMP
        FROM
        toa_boardroom AS a1
        JOIN toa_boardroom_meeting_time AS a2 ON a1.id = a2.BOARDROOM_ID
        JOIN toa_boardroom_summary AS a3 ON a3.MEETING_TIME_ID = a2.id
        JOIN toa_boardroom_apply AS a4 ON a4.id = a3.APPLY_id
        JOIN toa_boardroom_signin AS a5 ON a2.ID = a5.MEETING_TIME_ID
        WHERE


        a3.IS_DELETED='N'  and a3.sso_org_code = #{req.ssoOrgCode}

      
        AND
        (
        a5.SIGNIN_USERCODE = #{req.empCode}
        or
        a4.APPLY_EMP= #{req.empCode}
        )
        <if test="req.meetingId != null and req.meetingId != ''">
            and a3.MEETING_TIME_ID = #{req.meetingId}
        </if>
        <if test="req.isVideo != null and req.isVideo != ''">
            and a1.IS_VIDEO = #{req.isVideo}
        </if>
        <if test="req.motif != null and req.motif != ''">
            and (a4.motif LIKE CONCAT('%',#{req.motif},'%'))
        </if>

        <if test="req.applyOrgCode != null and req.applyOrgCode != ''">
            and a4.APPLY_ORG = #{req.applyOrgCode}
        </if>

        <if test="req.meetingBeninTime != null and req.meetingEndTime != null  ">
            <![CDATA[
        and a2.START_TIME > #{req.meetingBeninTime} AND a2.START_TIME < #{req.meetingEndTime}
         ]]>
        </if>

        ) as b

        <if test="req.joinEmpCode != null and req.joinEmpCode != ''">
            join  toa_boardroom_signin AS b1 ON b.MEETING_TIME_ID = b1.MEETING_TIME_ID
            where
            b1.SIGNIN_USERCODE = #{req.joinEmpCode}
            or
            b.APPLY_EMP= #{req.joinEmpCode}
        </if>


        order by b.UPDATE_DATE desc


    </select>


    <select id="count"
            resultType="java.lang.Integer">
        select count(1) from (

        SELECT
        DISTINCT
        a3.id
        FROM
        toa_boardroom_meeting_time AS a2
        JOIN toa_boardroom_summary AS a3 ON a3.MEETING_TIME_ID = a2.id
        JOIN toa_boardroom_apply AS a4 ON a4.id = a3.APPLY_id
        JOIN toa_boardroom_signin AS a5 ON a3.MEETING_TIME_ID = a5.MEETING_TIME_ID
        WHERE


        a3.IS_DELETED='N'  and a2.sso_org_code = #{req.ssoOrgCode}


        AND
        (
        a5.SIGNIN_USERCODE = #{req.empCode}
        or
        a4.APPLY_EMP= #{req.empCode}
        )


        <if test="req.meetingBeninTime != null and req.meetingEndTime != null  ">
            <![CDATA[
        and a2.START_TIME > #{req.meetingBeninTime} AND a2.START_TIME < #{req.meetingEndTime}
         ]]>
        </if>

        ) as b


    </select>


</mapper>