package cn.trasen.oa.boardroom.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2021/8/13 8:33
 * @description
 */
@Data
public class SyncUserReq {

    @ApiModelProperty(value = "员工id")
    String employeeId;
    @ApiModelProperty(value = "员工工号")
    String employeeNo;
    @ApiModelProperty(value = "员工脸图片")
    String employeeFace;
    @ApiModelProperty(value = "员工姓名")
    String employeeName;
}