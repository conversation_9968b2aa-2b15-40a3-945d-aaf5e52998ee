package cn.trasen.oa.boardroom.service;

import cn.trasen.oa.boardroom.bean.BoardroomMachineReq;
import cn.trasen.oa.boardroom.model.BoardroomMachine;

/**
 * <AUTHOR>
 * @createTime 2021/9/13 12:41
 * @description
 */
public interface BoardroomMachineService {
    /**
     * @param boardroomMachineReq
     * @return cn.trasen.boardroom.model.BoardroomMachine
     * @description 获取基础数据
     * <AUTHOR>
     * @createTime 2021/9/13 12:45
     */
    BoardroomMachine getBase(BoardroomMachineReq boardroomMachineReq);
}
