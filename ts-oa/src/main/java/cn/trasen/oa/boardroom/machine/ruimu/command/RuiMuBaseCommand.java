package cn.trasen.oa.boardroom.machine.ruimu.command;

import cn.hutool.extra.spring.SpringUtil;
import cn.trasen.oa.boardroom.bean.BoardRoomElectronicScreenHandleReq;
import cn.trasen.oa.boardroom.bean.BoardRoomElectronicScreenRespon;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;

/**
 * <AUTHOR>
 * @date 2021/12/21 13:21
 */
public abstract class RuiMuBaseCommand {
    public static RuiMuBaseCommand build(String command) {
        RuiMuBaseCommand ruiMuBaseCommand = null;
        try {
            ruiMuBaseCommand = SpringUtil.getBean(command + "RuiMuCommand", RuiMuBaseCommand.class);

        } catch (NoSuchBeanDefinitionException noSuchBeanDefinitionException) {
            ruiMuBaseCommand = null;
        }
        return ruiMuBaseCommand;

    }

    public abstract BoardRoomElectronicScreenRespon handle(BoardRoomElectronicScreenHandleReq electronicScreenHandleReq);
}