package cn.trasen.oa.boardroom.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021/10/14 11:24
 * @description
 */
@Setter
@Getter
public class BoardroomDisableSaveReq {


    /**
     * 会议室ID
     */
    @NotBlank(message = "会议室ID不能为空")
    @ApiModelProperty(value = "会议室ID")
    private String boardroomId;


    /**
     * 禁用开始时间
     */
    @ApiModelProperty(value = "禁用开始时间")
    private Date disableBegintime;
    /**
     * 禁用结束时间
     */
    @ApiModelProperty(value = "禁用结束时间")
    private Date disableEndtime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String  remark;


    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String  contactsCode;


    /**
     * 禁用类型
     */
    @ApiModelProperty(value = "禁用类型 1临时2永久")
    private Integer  disableType;

    /**
     * 是否发送
     */
    @ApiModelProperty(value = "是否发送 1发送2不发送")
    private Integer  bookingSendInfo;

}
