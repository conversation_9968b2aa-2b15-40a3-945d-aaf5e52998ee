package cn.trasen.oa.boardroom.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/15 9:43
 */
@Setter
@Getter
public class BoardRoomSubscribeListRes {


    /**
     * 会议室ID
     */
    @ApiModelProperty(value = "会议室ID")
    private String roomId;

    /**
     * 资源图片
     */
    @ApiModelProperty(value = "资源图片")
    private String emphasis;


    /**
     * 会议室名称
     */
    @ApiModelProperty(value = "会议室名称")
    private String roomName;


    /**
     * 楼层
     */
    @ApiModelProperty(value = "楼层")
    private String floor;

    /**
     * 位置
     */
    @ApiModelProperty(value = "位置")
    private String location;
    /**
     * 容量
     */
    @ApiModelProperty(value = "容量")
    private String capacitance;


    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private Integer roomIsEnable;


    @ApiModelProperty(value = "1.取消订阅 2订阅;")
    private Integer subscribeStatus;

    @ApiModelProperty(value = "1临时2永久")
    private Integer disableType;

    @ApiModelProperty(value = "会议室备注")
    String roomRemark;
}
