package cn.trasen.oa.morality.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.BootComm.permission.DataPermissionColumnHolder;
import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.document.Attachment;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.oa.DocumentFeignClient;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.morality.dao.MoralityItemMapper;
import cn.trasen.oa.morality.dao.MoralityOptionsMapper;
import cn.trasen.oa.morality.dao.MoralityRemindMapper;
import cn.trasen.oa.morality.dao.MoralityResultFileMapper;
import cn.trasen.oa.morality.dao.MoralityResultMapper;
import cn.trasen.oa.morality.model.MoralityItem;
import cn.trasen.oa.morality.model.MoralityRemind;
import cn.trasen.oa.morality.model.MoralityResult;
import cn.trasen.oa.morality.model.MoralityResultFile;
import cn.trasen.oa.morality.service.MoralityResultService;
import cn.trasen.oa.morality.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MoralityResultServiceImpl
 * @Description TODO
 * @date 2021��8��5�� ����3:27:06
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MoralityResultServiceImpl implements MoralityResultService {

	@Autowired
	private MoralityResultMapper mapper;
	
	@Autowired
	private MoralityItemMapper moralityItemMapper;
	
	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	@Autowired
	private MoralityResultFileMapper moralityResultFileMapper;
	
	@Autowired
	private DocumentFeignClient documentFeignClient;
	
	@Autowired
	private InformationFeignService informationFeignClient;
	
	@Autowired
	private MoralityOptionsMapper moralityOptionsMapper;
	
	@Autowired
	private MoralityRemindMapper moralityRemindMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MoralityResult record) {
		if(StringUtils.isEmpty(record.getItemId())) {
			record.setItemId("610b999bb5a3ad3a28fd6e21");
		}
		/*MoralityItem  item = moralityItemMapper.selectByPrimaryKey(record.getItemId());
		Integer maxScore = item.getMaxScore();
		Map<String,Object> sumOptionsSocre = mapper.selectSumOptionsSocre(record);
		if(null != sumOptionsSocre) {
			BigDecimal bg = (BigDecimal) sumOptionsSocre.get("negative");
			bg = bg.add(new BigDecimal(record.getOptionsSocre()));
			BigDecimal maxbg = new BigDecimal(maxScore);
			if(bg.compareTo(maxbg) == 1){
			    Assert.isTrue(false, "已超出分数设置上限，无法继续新增！");
			}
		}*/
		
		record.setId(ApplicationUtils.GUID32());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setStatus("0");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setCreateDept(user.getDeptcode());
			record.setCreateDeptName(user.getDeptname());
		}
		//查询被考评人岗位  科室
		PlatformResult<EmployeeResp> result = hrmsEmployeeFeignService.getEmployeeDetailByCode(record.getUserCode());
		if(result.isSuccess()) {
			EmployeeResp employeeResp = result.getObject();
			record.setUserJob(employeeResp.getPersonalIdentity());
			record.setUserDept(employeeResp.getOrgCode());
			record.setUserDeptName(employeeResp.getOrgName());
		}
		
		if(StringUtils.isNotBlank(record.getFileName())) {
			PlatformResult<List<Attachment>> res = documentFeignClient.selectByIds(record.getFileName(),UserInfoHolder.getToken());
			if(res.isSuccess()) {
				List<Attachment> attachmentList = res.getObject();
				List<MoralityResultFile> accessoryList = new ArrayList<>();
				for (Attachment attachment : attachmentList) {
					MoralityResultFile moralityResultFile = new MoralityResultFile();
					moralityResultFile.setId(attachment.getId());
					moralityResultFile.setResultId(record.getId());
					moralityResultFile.setAccessoryName(attachment.getOriginalName());
					moralityResultFile.setAccessorySaveName(attachment.getFileName());
					moralityResultFile.setAccessoryType(attachment.getFileExtension());
					moralityResultFile.setIsImage(attachment.getIsImage());
					moralityResultFile.setFileSize(attachment.getFileSize());
					moralityResultFile.setType("0");
					accessoryList.add(moralityResultFile);
				}
				//新增附件信息数据
				moralityResultFileMapper.batchInsert(accessoryList);
			}
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MoralityResult record) {
		if(StringUtils.isEmpty(record.getItemId())) {
			record.setItemId("610b999bb5a3ad3a28fd6e21");
		}
		/*MoralityItem  item = moralityItemMapper.selectByPrimaryKey(record.getItemId());
		Integer maxScore = item.getMaxScore();
		Map<String,Object> sumOptionsSocre = mapper.selectSumOptionsSocre(record);
		if(null != sumOptionsSocre) {
			BigDecimal bg = (BigDecimal) sumOptionsSocre.get("negative");
			bg = bg.add(new BigDecimal(record.getOptionsSocre()));
			BigDecimal maxbg = new BigDecimal(maxScore);
			if(bg.compareTo(maxbg) == 1){
			    Assert.isTrue(false, "已超出分数设置上限，更新失败！");
			}
		}*/
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//查询被考评人岗位
		PlatformResult<EmployeeResp> result = hrmsEmployeeFeignService.getEmployeeDetailByCode(record.getUserCode());
		if(result.isSuccess()) {
			EmployeeResp employeeResp = result.getObject();
			record.setUserJob(employeeResp.getPersonalIdentity());
			record.setUserDept(employeeResp.getOrgCode());
			record.setUserDeptName(employeeResp.getOrgName());
		}
		
		moralityResultFileMapper.deleteByResultId(record.getId(),"0");
		if(StringUtils.isNotBlank(record.getFileName())) {
			PlatformResult<List<Attachment>> res = documentFeignClient.selectByIds(record.getFileName(),UserInfoHolder.getToken());
			List<Attachment> attachmentList = res.getObject();
			List<MoralityResultFile> accessoryList = new ArrayList<>();
			for (Attachment attachment : attachmentList) {
				MoralityResultFile moralityResultFile = new MoralityResultFile();
				moralityResultFile.setId(attachment.getId());
				moralityResultFile.setResultId(record.getId());
				moralityResultFile.setAccessoryName(attachment.getOriginalName());
				moralityResultFile.setAccessorySaveName(attachment.getFileName());
				moralityResultFile.setAccessoryType(attachment.getFileExtension());
				moralityResultFile.setIsImage(attachment.getIsImage());
				moralityResultFile.setFileSize(attachment.getFileSize());
				moralityResultFile.setType("0");
				accessoryList.add(moralityResultFile);
			}
			moralityResultFileMapper.batchInsert(accessoryList);
		}
		
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MoralityResult record = new MoralityResult();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MoralityResult selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MoralityResult> getDataSetList(Page page, MoralityResult record) {
		Example example = new Example(MoralityResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		//查询当前登录人是否是考评人
		List<MoralityItem> moralityItem = moralityItemMapper.selectMoralityItemByAdminUser(UserInfoHolder.getCurrentUserCode());
		if(StringUtils.isNotBlank(record.getUserCode())) {
			criteria.andEqualTo("userCode", record.getUserCode());
		}else {
			//普通用户通过数据权限查看
			StringBuffer permissionSb = new StringBuffer();
			permissionSb.append(" (create_dept in ").append(UserInfoHolder.getCurrentUserInfo().getOrgRang());
			permissionSb.append(" or create_user = '").append(UserInfoHolder.getCurrentUserCode()).append("'");
			if(null != moralityItem && moralityItem.size() > 0) {
				permissionSb.append(" or create_dept ='").append(UserInfoHolder.getCurrentUserInfo().getDeptcode()).append("'");
			}
			permissionSb.append(")");
			criteria.andCondition(permissionSb.toString());
		}
		
		if(StrUtil.isNotBlank(record.getResultType())) {
			if("0".equals(record.getResultType()) || "1".equals(record.getResultType())) {
				criteria.andEqualTo("resultType", record.getResultType());
				criteria.andNotEqualTo("status", "1");
			}else if("3".equals(record.getResultType())){
				List<String> type = new ArrayList<>();
				type.add("0");
				type.add("1");
				criteria.andIn("resultType",type);
				criteria.andNotEqualTo("status", "1");
			}else {
				criteria.andEqualTo("status", "1");
			}
		}else {
			criteria.andEqualTo("resultType", "0");
			criteria.andNotEqualTo("status", "1");
		}
		
		if(StrUtil.isNotBlank(record.getStatus())) {
			criteria.andEqualTo("status", record.getStatus());
		}
		if(StrUtil.isNotBlank(record.getUserName())) {
			criteria.andLike("userName", "%" + record.getUserName() + "%");
		}
		if(StrUtil.isNotBlank(record.getUserDeptName())) {
			criteria.andLike("userDeptName", "%" + record.getUserDeptName() + "%");
		}
		if(StrUtil.isNotBlank(record.getCreateDeptName())) {
			criteria.andLike("createDeptName", "%" + record.getCreateDeptName() + "%");
		}
		if(null != record.getValidStartTime() && null != record.getValidEndTime()) {
			StringBuffer sb = new StringBuffer();
			/*sb.append(" ( ( str_to_date('").append(DateUtil.formatDate(record.getValidStartTime())).append("', '%Y-%m-%d') BETWEEN valid_start_time and valid_end_time");
			sb.append(" ) or ( str_to_date('").append(DateUtil.formatDate(record.getValidEndTime())).append("', '%Y-%m-%d') BETWEEN valid_start_time and valid_end_time");
			sb.append(" ))");*/
			sb.append(" (( valid_start_time BETWEEN str_to_date('").append(DateUtil.formatDate(record.getValidStartTime())).append("', '%Y-%m-%d') and str_to_date('").append(DateUtil.formatDate(record.getValidEndTime())).append("', '%Y-%m-%d')");
			sb.append(" ) or (valid_end_time BETWEEN str_to_date('").append(DateUtil.formatDate(record.getValidStartTime())).append("', '%Y-%m-%d') and str_to_date('").append(DateUtil.formatDate(record.getValidEndTime())).append("', '%Y-%m-%d')");
			sb.append(" ))");
			criteria.andCondition(sb.toString());
		}
		if(StrUtil.isNotBlank(record.getCreateStartDate())) {
			criteria.andBetween("createDate", record.getCreateStartDate(), record.getCreateEndDate());
		}
		if("2".equals(record.getResultType())) {
			example.setOrderByClause("cancel_time desc");
		}else {
			example.setOrderByClause("create_date desc");
		}
		
		List<MoralityResult> records = mapper.selectByExampleAndRowBounds(example, page);
		for (MoralityResult moralityResult : records) {
			if(null != moralityItem && moralityItem.size() > 0) {
				moralityResult.setIsMoralityAdmin("1");
			}else {
				moralityResult.setIsMoralityAdmin("0");
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<Map<String, Object>> selectMoralityResultList(Page page, MoralityResult record) {
		
		//默认查询近一年的数据
		if(null == record.getValidEndTime() && null  == record.getValidStartTime()) {
			Map<String,String> yearTRange = DateUtils.getLatest12Month(new Date());
			record.setValidStartTime(DateUtil.parse(yearTRange.get("startDate")));
			record.setValidEndTime(DateUtil.parse(yearTRange.get("endDate")));
		}
		
		if(StringUtils.isEmpty(record.getUserJob())) {
			record.setUserJob("1");
		}
		
		DataPermissionColumnHolder.set("t8.orgCode,t8.empCode");
		List<Map<String,Object>> empList = mapper.selectByEmployeePermissionsPage(page,record);
		
		
		//根据分数排序
		//empList.stream().sorted(Comparator.comparing(MoralityResultServiceImpl::comparingByToalScore)).collect(Collectors.toList());
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), empList);
	}
	
	/*private static String comparingByToalScore(Map<String, Object> map){
        return (String) map.get("toalScore");
    }*/

	@Transactional(readOnly = false)
	@Override
	public void updateMoralityResult(MoralityResult record) {
		MoralityResult moralityResult = mapper.selectByPrimaryKey(record.getId());
		moralityResult.setCancelRemark(record.getCancelRemark());
		moralityResult.setStatus(record.getStatus());
		
		
		if("1".equals(record.getStatus())) {
			
			moralityResultFileMapper.deleteByResultId(record.getId(),"1");
			if(StringUtils.isNotBlank(record.getCancelFile())) {
				PlatformResult<List<Attachment>> res = documentFeignClient.selectByIds(record.getCancelFile(),UserInfoHolder.getToken());
				List<Attachment> attachmentList = res.getObject();
				List<MoralityResultFile> accessoryList = new ArrayList<>();
				for (Attachment attachment : attachmentList) {
					MoralityResultFile moralityResultFile = new MoralityResultFile();
					moralityResultFile.setId(attachment.getId());
					moralityResultFile.setResultId(record.getId());
					moralityResultFile.setAccessoryName(attachment.getOriginalName());
					moralityResultFile.setAccessorySaveName(attachment.getFileName());
					moralityResultFile.setAccessoryType(attachment.getFileExtension());
					moralityResultFile.setIsImage(attachment.getIsImage());
					moralityResultFile.setFileSize(attachment.getFileSize());
					moralityResultFile.setType("1");
					accessoryList.add(moralityResultFile);
				}
				moralityResultFileMapper.batchInsert(accessoryList);
			}
			
			moralityResult.setCancelFile(record.getCancelFile());
			moralityResult.setCancelTime(new Date());
			moralityResult.setCancelUser(UserInfoHolder.getCurrentUserCode());
			moralityResult.setCancelUserName(UserInfoHolder.getCurrentUserName());
			
			MoralityItem moralityItem = new MoralityItem();
			moralityItem.setId(moralityResult.getItemId());
			moralityItem = moralityItemMapper.selectOne(moralityItem);
			
			List<String> empCodeList = new ArrayList<>();
			if(StringUtils.isNotBlank(moralityItem.getAdminUser())) {
				String[] adminUserArr = moralityItem.getAdminUser().split(",");
				String[] deptUserNameArr = moralityItem.getDeptUserName().split(",");
				for (int i = 0; i < deptUserNameArr.length; i++) {
					String deptName = deptUserNameArr[i].split("-")[0];
					if(deptName.equals(moralityResult.getCreateDeptName())) {
						empCodeList.add(adminUserArr[i]);
					}
				}
			}
			
			empCodeList.remove(UserInfoHolder.getCurrentUserCode());
			
			if(empCodeList.size() > 0) {
				StringBuffer sb = new StringBuffer();
				sb.append(moralityResult.getUserDeptName()).append(moralityResult.getUserName());
				sb.append("的考评项“").append(moralityResult.getOptionsName()).append("”因");
				sb.append(moralityResult.getCancelRemark()).append("原因被").append(moralityResult.getCancelUserName());
				sb.append("撤销，请您知悉!");
				NoticeReq notice =
						NoticeReq.builder()
							.content(sb.toString())
							.noticeType("3")
							.receiver(StringUtils.join(empCodeList, ","))
							.sender(UserInfoHolder.getCurrentUserCode())
							.senderName(UserInfoHolder.getCurrentUserName())
							.subject("医德医风考评项撤销提醒")
							.wxSendType("2")
							.toUrl("/")
							.source("医德医风")
							.build();
					informationFeignClient.sendNotice(notice);
			}
		}else {
			moralityResult.setStatus(null);
			moralityResult.setCancelUser(null);
			moralityResult.setCancelUserName(null);
		}
		
		mapper.updateByPrimaryKey(moralityResult);
	}

	@Override
	public List<Map<String, String>> selectMoralityRemindHandle(String startDate,String endDate) {
		//默认查询近一年的数据
		if(StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate)) {
			Map<String,String> yearTRange = DateUtils.getLatest12Month(new Date());
			startDate = yearTRange.get("startDate");
			endDate = yearTRange.get("endDate");
		}
		List<Map<String,String>> deptList = moralityOptionsMapper.selectGroupDept();
		for (Map<String, String> dept : deptList) {
			List<Map<String,String>> dateMap = DateUtils.getMonths(startDate,endDate);
			StringBuffer sb = new StringBuffer();
			StringBuffer sb2 = new StringBuffer();
			for (Map<String,String> map : dateMap) {
				Map<String,String> params = new HashMap<>();
				params.put("monthStr", map.get("month"));
				params.put("createDept", dept.get("create_dept"));
				long ih = mapper.selectMoralityResultByCreateDate(params);
				
				MoralityRemind record = new MoralityRemind();
				record.setRemindDept(dept.get("create_dept"));
				record.setRemindMonth(map.get("month"));
				MoralityRemind moralityRemind = moralityRemindMapper.selectOne(record);
				
				if(ih > 0 || (null != moralityRemind && "1".equals(moralityRemind.getRemindConfirm()))) {
					sb.append(map.get("month")).append(",");
				}else {
					sb2.append(map.get("month")).append(",");
				}
			}
			dept.put("haveData", sb.toString());
			dept.put("noData", sb2.toString());
		}
		
		return deptList;
	}

	@Override
	@Transactional(readOnly = false)
	public void moralityRemindHandle(String createDeptName,String noData,String createDept,String noDataStr) {
		
		List<String> empCodeList = new ArrayList<>();
		
		//拿到月份  插入提醒数据 如果已经当月提醒过则更像提醒次数
		//2020-11,2020-12,2021-01,2021-02,2021-03,2021-04,2021-05,2021-06,2021-07,2021-08,2021-09,2021-10
		if(StringUtils.isNotBlank(noData)) {
			String[] noDataArr = noData.split(",");
			for (int i = 0; i < noDataArr.length; i++) {
				MoralityRemind record = new MoralityRemind();
				record.setRemindDept(createDept);
				record.setRemindMonth(noDataArr[i]);
				MoralityRemind selectMoralityRemind = moralityRemindMapper.selectOne(record);
				if(null != selectMoralityRemind) {
					selectMoralityRemind.setUpdateDate(new Date());
					selectMoralityRemind.setUpdateUser(UserInfoHolder.getCurrentUserCode());
					selectMoralityRemind.setUpdateUserName(UserInfoHolder.getCurrentUserName());
					selectMoralityRemind.setRemindNumber(selectMoralityRemind.getRemindNumber() + 1);
					moralityRemindMapper.updateByPrimaryKeySelective(selectMoralityRemind);
				}else {
					MoralityRemind moralityRemind = new MoralityRemind();
					moralityRemind.setId(IdGeneraterUtils.nextId());
					ThpsUser user = UserInfoHolder.getCurrentUserInfo();
					if (user != null) {
						moralityRemind.setCreateUser(user.getUsercode());
						moralityRemind.setCreateUserName(user.getUsername());
						moralityRemind.setUpdateUser(user.getUsercode());
						moralityRemind.setUpdateUserName(user.getUsername());
					}
					
					moralityRemind.setUpdateDate(new Date());
					moralityRemind.setRemindDept(createDept);
					moralityRemind.setRemindDeptName(createDeptName);
					moralityRemind.setRemindMonth(noDataArr[i]);
					moralityRemind.setCreateDate(new Date());
					moralityRemind.setIsDeleted("N");
					moralityRemind.setRemindConfirm("0");
					moralityRemind.setRemindNumber(1);
					moralityRemindMapper.insertSelective(moralityRemind);
				}
			}
		}
		
		List<MoralityItem> list = moralityItemMapper.selectAll();
		
		for (MoralityItem moralityItem : list) {
			if(StringUtils.isNotBlank(moralityItem.getDeptUserName())) {
				String[] adminUserArr = moralityItem.getAdminUser().split(",");
				String[] deptUserNameArr = moralityItem.getDeptUserName().split(",");
				
				for (int i = 0; i < deptUserNameArr.length; i++) {
					String deptName = deptUserNameArr[i].split("-")[0];
					if(deptName.equals(createDeptName)) {
						empCodeList.add(adminUserArr[i]);
					}
				}
			}
		}
		
		//去重
		empCodeList = empCodeList.stream().distinct().collect(Collectors.toList());
		
		if(empCodeList.size() > 0) {
			
			StringBuffer sb = new StringBuffer();
			sb.append("您好，").append(UserInfoHolder.getCurrentUserName());
			sb.append("提醒您确认").append(noDataStr).append("的医德医风考评!");
			NoticeReq notice =
					NoticeReq.builder()
						.content(sb.toString())
						.noticeType("3")
						.receiver(StringUtils.join(empCodeList, ","))
						.sender(UserInfoHolder.getCurrentUserCode())
						.senderName(UserInfoHolder.getCurrentUserName())
						.subject("医德医风评分提醒")
						.wxSendType("2")
						.source("医德医风")
						.toUrl("/morality/MoralityResult")
						.build();
				informationFeignClient.sendNotice(notice);
		}
	}
}
