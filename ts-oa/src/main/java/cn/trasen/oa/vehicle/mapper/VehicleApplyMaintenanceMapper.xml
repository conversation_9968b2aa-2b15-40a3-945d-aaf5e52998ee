<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.vehicle.dao.VehicleApplyMaintenanceMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.vehicle.model.VehicleApplyMaintenance">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="vehicle_id" jdbcType="VARCHAR" property="vehicleId" />
    <result column="maintenance_date" jdbcType="DATE" property="maintenanceDate" />
    <result column="maintenance_price" jdbcType="VARCHAR" property="maintenancePrice" />
    <result column="maintenance_remark" jdbcType="VARCHAR" property="maintenanceRemark" />
    <result column="maintenance_files" jdbcType="VARCHAR" property="maintenanceFiles" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <select id="selectByApplyId" parameterType="String" resultType="cn.trasen.oa.vehicle.model.VehicleApplyMaintenance">
  		select * from toa_vehicle_apply_maintenance
  		where is_deleted = 'N' and apply_id = #{applyId}
  </select>
</mapper>