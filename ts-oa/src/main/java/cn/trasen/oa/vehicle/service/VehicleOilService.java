package cn.trasen.oa.vehicle.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.vehicle.model.VehicleOil;

/**
 * @ClassName VehicleOilService
 * @Description TODO
 * @date 2023��5��4�� ����2:05:55
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleOilService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��5��4�� ����2:05:55
	 * <AUTHOR>
	 */
	Integer save(VehicleOil record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��5��4�� ����2:05:55
	 * <AUTHOR>
	 */
	Integer update(VehicleOil record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��5��4�� ����2:05:55
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return VehicleOil
	 * @date 2023��5��4�� ����2:05:55
	 * <AUTHOR>
	 */
	VehicleOil selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<VehicleOil>
	 * @date 2023��5��4�� ����2:05:55
	 * <AUTHOR>
	 */
	DataSet<VehicleOil> getDataSetList(Page page, VehicleOil record);

	/**
	 * 
	 * @MethodName: getVehicleOilList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<VehicleOil>
	 * @date 2023-05-09 07:00:46
	 */
	List<VehicleOil> getVehicleOilList(VehicleOil record);

	/**
	 * 
	 * @MethodName: getVehicleOilListStatistics
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<VehicleOil>
	 * @date 2023-05-11 11:24:04
	 */
	Map<String,Object> getVehicleOilListStatistics(VehicleOil record);

	/**
	 * 
	 * @MethodName: selectVehicleOilById
	 * @Description: TODO
	 * <AUTHOR>
	 * @param id
	 * @return VehicleOil
	 * @date 2023-05-13 01:57:55
	 */
	VehicleOil selectVehicleOilById(String id);
}
