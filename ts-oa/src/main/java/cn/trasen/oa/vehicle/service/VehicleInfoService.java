package cn.trasen.oa.vehicle.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.vehicle.model.VehicleInfo;

/**
 * @ClassName VehicleInfoService
 * @Description TODO
 * @date 2023��4��28�� ����10:43:52
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleInfoService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��4��28�� ����10:43:52
	 * <AUTHOR>
	 */
	Integer save(VehicleInfo record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��4��28�� ����10:43:52
	 * <AUTHOR>
	 */
	Integer update(VehicleInfo record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��4��28�� ����10:43:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return VehicleInfo
	 * @date 2023��4��28�� ����10:43:52
	 * <AUTHOR>
	 */
	VehicleInfo selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<VehicleInfo>
	 * @date 2023��4��28�� ����10:43:52
	 * <AUTHOR>
	 */
	DataSet<VehicleInfo> getDataSetList(Page page, VehicleInfo record);

	/**
	 * 
	 * @MethodName: selectAll
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return List<VehicleInfo>
	 * @date 2023-05-08 02:24:29
	 */
	List<VehicleInfo> selectAll(VehicleInfo record);

	/**
	 * 
	 * @MethodName: selectVehicleInfoById
	 * @Description: TODO
	 * <AUTHOR>
	 * @param id
	 * @return VehicleInfo
	 * @date 2023-05-13 02:06:44
	 */
	VehicleInfo selectVehicleInfoById(String id);

	/**
	 * 
	 * @MethodName: getVehicleInfoListStatistics
	 * @Description: TODO
	 * <AUTHOR>
	 * @return Map<String,Object>
	 * @date 2023-05-13 05:27:41
	 */
	Map<String,Object> getVehicleInfoListStatistics();

	/**
	 * 
	 * @MethodName: getVehicleConditionStatistics
	 * @Description: TODO
	 * <AUTHOR>
	 * @return Map<String,Object>
	 * @date 2023-05-20 11:00:50
	 */
	List<Map<String,Object>> getVehicleConditionStatistics();

}
