package cn.trasen.oa.vehicle.dao;

import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.vehicle.model.VehicleInfo;

public interface VehicleInfoMapper extends Mapper<VehicleInfo> {

	List<VehicleInfo> getDataSetList(VehicleInfo record, Page page);

	VehicleInfo getVehicleInfoStatistics(String id);

	List<VehicleInfo> selectAllVehicleInfoList(VehicleInfo record);

	Map<String, Object> getVehicleInfoListStatistics();

	List<Map<String,Object>> getVehicleConditionStatistics();

	void clearVehicleId(String vehicleId);
}