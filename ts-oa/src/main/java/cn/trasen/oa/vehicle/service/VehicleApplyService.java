package cn.trasen.oa.vehicle.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.vehicle.model.VehicleApply;

/**
 * @ClassName VehicleApplyService
 * @Description TODO
 * @date 2023��5��5�� ����7:04:06
 * <AUTHOR>
 * @version 1.0
 */
public interface VehicleApplyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��5��5�� ����7:04:06
	 * <AUTHOR>
	 */
	Integer save(VehicleApply record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��5��5�� ����7:04:06
	 * <AUTHOR>
	 */
	Integer update(VehicleApply record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��5��5�� ����7:04:06
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return VehicleApply
	 * @date 2023��5��5�� ����7:04:06
	 * <AUTHOR>
	 */
	VehicleApply selectById(String id,String viewData);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<VehicleApply>
	 * @date 2023��5��5�� ����7:04:06
	 * <AUTHOR>
	 */
	DataSet<VehicleApply> getDataSetList(Page page, VehicleApply record);

	/**
	 * 
	 * @MethodName: getisOccupy
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record
	 * @return VehicleApply
	 * @date 2023-05-06 05:22:10
	 */
	Boolean getisOccupy(VehicleApply record);

	/**
	 * 
	 * @MethodName: getApprovalList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param record
	 * @return DataSet<VehicleApply>
	 * @date 2023-05-08 02:59:10
	 */
	DataSet<VehicleApply> getApprovalList(Page page, VehicleApply record);

	/**
	 * 
	 * @MethodName: pass
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-08 03:52:19
	 */
	void pass(VehicleApply record);

	/**
	 * 
	 * @MethodName: reject
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-08 04:50:04
	 */
	void reject(VehicleApply record);

	/**
	 * 
	 * @MethodName: revoke
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-08 04:59:19
	 */
	void revoke(VehicleApply record);

	/**
	 * 
	 * @MethodName: dispatch
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 09:43:53
	 */
	void dispatch(VehicleApply record);

	/**
	 * 
	 * @MethodName: urgentDispatch
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 11:08:11
	 */
	void urgentDispatch(VehicleApply record);

	/**
	 * 
	 * @MethodName: getOutAndRetuenList
	 * @Description: TODO
	 * <AUTHOR>
	 * @param page
	 * @param record
	 * @return DataSet<VehicleApply>
	 * @date 2023-05-09 11:47:11
	 */
	DataSet<VehicleApply> getOutAndRetuenList(Page page, VehicleApply record);

	/**
	 * 
	 * @MethodName: outVehicle
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 03:09:26
	 */
	void outVehicle(VehicleApply record);

	/**
	 * 
	 * @MethodName: returnVehicle
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 03:13:36
	 */
	void returnVehicle(VehicleApply record);

	/**
	 * 
	 * @MethodName: supplementaryDispatch
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 07:38:33
	 */
	void supplementaryDispatch(VehicleApply record);

	/**
	 * 
	 * @MethodName: fastReturnVehicle
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-09 07:39:45
	 */
	void fastReturnVehicle(VehicleApply record);

	/**
	 * 
	 * @MethodName: dispatchRevoke
	 * @Description: TODO
	 * <AUTHOR>
	 * @param record void
	 * @date 2023-05-20 11:45:35
	 */
	void dispatchRevoke(VehicleApply record);
}
