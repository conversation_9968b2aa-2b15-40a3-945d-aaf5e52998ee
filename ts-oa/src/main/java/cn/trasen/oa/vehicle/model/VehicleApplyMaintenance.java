package cn.trasen.oa.vehicle.model;

import io.swagger.annotations.*;

import java.util.Date;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_vehicle_apply_maintenance")
@Setter
@Getter
public class VehicleApplyMaintenance {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 预约记录id
     */
    @Column(name = "apply_id")
    @ApiModelProperty(value = "预约记录id")
    private String applyId;

    /**
     * 车辆id
     */
    @Column(name = "vehicle_id")
    @ApiModelProperty(value = "车辆id")
    private String vehicleId;

    /**
     * 维修日期
     */
    @Column(name = "maintenance_date")
    @ApiModelProperty(value = "维修日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date maintenanceDate;

    /**
     * 费用
     */
    @Column(name = "maintenance_price")
    @ApiModelProperty(value = "费用")
    private String maintenancePrice;

    /**
     * 维修情况
     */
    @Column(name = "maintenance_remark")
    @ApiModelProperty(value = "维修情况")
    private String maintenanceRemark;

    /**
     * 附件
     */
    @Column(name = "maintenance_files")
    @ApiModelProperty(value = "附件")
    private String maintenanceFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}