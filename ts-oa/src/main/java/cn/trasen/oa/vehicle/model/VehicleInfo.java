package cn.trasen.oa.vehicle.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_vehicle_info")
@Setter
@Getter
public class VehicleInfo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;
    
    
    @Column(name = "picture")
    @ApiModelProperty(value = "车辆图片")
    private String picture;

    /**
     * 车辆名称
     */
    @Column(name = "vehicle_name")
    @ApiModelProperty(value = "车辆名称")
    private String vehicleName;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_no")
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    /**
     * 车颜色
     */
    @Column(name = "vehicle_color")
    @ApiModelProperty(value = "车颜色")
    private String vehicleColor;

    /**
     * 座位数
     */
    @ApiModelProperty(value = "座位数")
    private Integer seats;

    /**
     * 车辆类型
     */
    @Column(name = "vehicle_type")
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    /**
     * 所属机构
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "所属机构")
    private String orgId;

    /**
     * 所属机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "所属机构名称")
    private String orgName;

    /**
     * 车辆状态
     */
    @Column(name = "vehicle_status")
    @ApiModelProperty(value = "车辆状态  1在库  2预约  3在用  4借用 5报废")
    private Integer vehicleStatus;

    /**
     * 购买价格
     */
    @Column(name = "vehicle_price")
    @ApiModelProperty(value = "购买价格")
    private String vehiclePrice;

    /**
     * 购置日期
     */
    @Column(name = "buy_date")
    @ApiModelProperty(value = "购置日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date buyDate;

    /**
     * 发动机号码
     */
    @Column(name = "engine_no")
    @ApiModelProperty(value = "发动机号码")
    private String engineNo;

    /**
     * 初始里程数
     */
    @ApiModelProperty(value = "初始里程数")
    private BigDecimal mileage;
    
    @ApiModelProperty(value = "总里程数")
    @Column(name = "total_mileage")
    private BigDecimal totalMileage;

    /**
     * 车架号号码
     */
    @ApiModelProperty(value = "车架号号码")
    private String vin;
    
    
    @ApiModelProperty(value = "车辆管理员")
    @Column(name = "manager_code")
    private String managerCode;
    
    @ApiModelProperty(value = "车辆管理员名称")
    @Column(name = "manager_name")
    private String managerName;
    
    @ApiModelProperty(value = "预约是否需要审批  0不需要  1需要")
    private String examine;
    
    
    @ApiModelProperty(value = "绑定油卡id")
    @Column(name = "oil_card")
    private String oilCard;

    /**
     * 可预约开始时间
     */
    @Column(name = "appointment_start_time")
    @ApiModelProperty(value = "可预约开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date appointmentStartTime;

    /**
     * 可预约结束时间
     */
    @Column(name = "appointment_end_time")
    @ApiModelProperty(value = "可预约结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date appointmentEndTime;

    /**
     * 备注
     */
    @Column(name = "vehicle_remark")
    @ApiModelProperty(value = "备注")
    private String vehicleRemark;

    /**
     * 附件
     */
    @Column(name = "vehicle_files")
    @ApiModelProperty(value = "附件")
    private String vehicleFiles;

    /**
     * 借用时间
     */
    @Column(name = "borrow_date")
    @ApiModelProperty(value = "借用时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date borrowDate;

    /**
     * 预计归还时间
     */
    @Column(name = "return_date")
    @ApiModelProperty(value = "预计归还时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date returnDate;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "是否有油卡 有传Y  无传N（查询用）")
    private String isOilCard;
    
    @Transient
    @ApiModelProperty(value = "最小里程数（查询用）")
    private String mileageMin;
    
    @Transient
    @ApiModelProperty(value = "最大里程数（查询用）")
    private String mileageMax;
    
    @Transient
    @ApiModelProperty(value = "保险到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date insureDate;
    
    @Transient
    @ApiModelProperty(value = "年检到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date inspectNextDate;
    
    @Transient
    @ApiModelProperty(value = "出险次数")
    private Long dangerNumbers;
    
    @Transient
    @ApiModelProperty(value = "保养次数")
    private Long maintenanceNumbers;
    
    @Transient
    @ApiModelProperty(value = "加油次数")
    private Long refuelNumbers;
    
    @Transient
    @ApiModelProperty(value = "维修次数")
    private Long applyMaintenanceNumbers;
    
    @Transient
    @ApiModelProperty(value = "事故次数")
    private Long accidentNumbers;
    
    @Transient
    @ApiModelProperty(value = "违章次数")
    private Long violationNumbers;
    
    @Transient
    @ApiModelProperty(value = "出险总额")
    private Double totalDangerPrice;
    
    @Transient
    @ApiModelProperty(value = "保养总额")
    private Double totalMaintenancePrice;
    
    @Transient
    @ApiModelProperty(value = "加油总额")
    private Double totalRefuelPrice;
    
    @Transient
    @ApiModelProperty(value = "维修总额")
    private Double totalApplyMaintenancePrice;
    
    @Transient
    @ApiModelProperty(value = "油卡余额")
    private String oilPrice;
    
    @Transient
    @ApiModelProperty(value = "保险记录")
    private List<VehicleInfoInsure>  vehicleInfoInsureList;
    
    @Transient
    @ApiModelProperty(value = "年检记录")
    private List<VehicleInfoInspect>  vehicleInfoInspectList;
    
    @Transient
    @ApiModelProperty(value = "出险记录")
    private List<VehicleInfoDanger>  vehicleInfoDangerList;
    
    @Transient
    @ApiModelProperty(value = "保养记录")
    private List<VehicleInfoMaintenance>  vehicleInfoMaintenanceList;
    
    @Transient
    @ApiModelProperty(value = "加油记录")
    private List<VehicleApplyRefuel>  vehicleApplyRefuelList;
    
    @Transient
    @ApiModelProperty(value = "维修记录")
    private List<VehicleApplyMaintenance> vehicleApplyMaintenanceList;
    
    @Transient
    @ApiModelProperty(value = "车辆预约视图详情传Y")
    private String viewData;
    
    @Transient
    private String vehicleInfoId;
}
