package cn.trasen.oa.gov.service;


import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.gov.model.GovReceivefileseq;

/**
 * @Description: 收文流水号设置 Service层
 * @Date: 2020/5/26 14:59
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface GovReceivefileseqService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int insert(GovReceivefileseq entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int update(GovReceivefileseq entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int deleted(GovReceivefileseq entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<govSendDocnum></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    List<GovReceivefileseq> getDataList(Page page, GovReceivefileseq entity);

    /**
     * @Author: Lizhihuo
     * @Description: 获取流水号类别下拉值
     * @Date: 2020/5/27 10:52
     * @Param: 
     * @return: java.util.List<cn.trasen.gov.model.GovReceivefileseq>
     **/
    List<GovReceivefileseq> getSeqTypeList(Page page);

    /**
     * @Author: Lizhihuo
     * @Description: 判断流水号名称是否重复
     * @Date: 2020/5/27 16:35
     * @Param: 
     * @return: cn.trasen.gov.model.GovReceivefileseq
     **/
    GovReceivefileseq judgeSeqNameIsRepeated(GovReceivefileseq entity);

    /**
     * 
     * @Title: getReceiveFileNumber   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param processId
     * @param: @return      
     * @return: String  
     * @author: YueC
     * @date:   2020年9月7日 下午5:48:56    
     * @throws
     */
    Map<String,String> getReceiveFileNumber(String processId);

    /**
     * 
     * @Title: saveReceiveFileNumber   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param seqId
     * @param: @param number      
     * @return: void  
     * @author: YueC
     * @date:   2020年9月7日 下午6:12:49    
     * @throws
     */
	void saveReceiveFileNumber(String seqId, String number);
}
