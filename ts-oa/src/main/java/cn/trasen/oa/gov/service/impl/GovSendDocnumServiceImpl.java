package cn.trasen.oa.gov.service.impl;


import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.gov.dao.GovSendDocnumMapper;
import cn.trasen.oa.gov.dao.GovSendDocnumValueMapper;
import cn.trasen.oa.gov.model.GovSendDocnum;
import cn.trasen.oa.gov.model.GovSendDocnumValue;
import cn.trasen.oa.gov.service.GovSendDocnumService;
import tk.mybatis.mapper.entity.Example;

/**
 * @Description: 发文文号设置 Impl
 * @Date: 2020/4/22 18:01
 * @Author: Lizh
 * @Company: 湖南创星
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class GovSendDocnumServiceImpl implements GovSendDocnumService {

    @Autowired
    private GovSendDocnumMapper govSendDocnumMapper;
    
    @Autowired
    private GovSendDocnumValueMapper govSendDocnumValueMapper;

    /**
     * @Author: Lizhihuo
     * @Description: 新增
     * @Date: 2020/4/23 10:33
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int insert(GovSendDocnum entity) {
        entity.setId(String.valueOf(IdWork.id.nextId()));
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateDate(new Date());
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        entity.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        entity.setHospCode(UserInfoHolder.getCurrentUserInfo().getHospCode());
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return govSendDocnumMapper.insertSelective(entity);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改
     * @Date: 2020/4/23 10:33
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int update(GovSendDocnum entity) {
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        return govSendDocnumMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 删除
     * @Date: 2020/4/23 10:33
     * @Param:
     * @return: int
     **/
    @Override
    @Transactional(readOnly = false)
    public int deleted(GovSendDocnum entity) {
        GovSendDocnum govSendDocnum = govSendDocnumMapper.selectByPrimaryKey(entity.getId());
        govSendDocnum.setIsDeleted(Contants.IS_DELETED_TURE);
        return govSendDocnumMapper.updateByPrimaryKeySelective(govSendDocnum);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询列表
     * @Date: 2020/4/23 10:33
     * @Param:
     * @return: java.util.List<cn.trasen.gov.model.GovSendDocnum>
     **/
    @Override
    public List<GovSendDocnum> getDataList(Page page, GovSendDocnum entity) {
        Example example = new Example(GovSendDocnum.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (StringUtils.isNotBlank(entity.getNumName())) {//文号名称
            example.and().andLike("numName", "%" + entity.getNumName() + "%");
        }
        return govSendDocnumMapper.selectByExampleAndRowBounds(example, page);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 判断文号名称不能重复
     * @Date: 2020/5/27 16:27
     * @Param:
     * @return: cn.trasen.gov.model.GovSendDocnum
     **/
    @Override
    public GovSendDocnum judgeNumNameIsRepeated(GovSendDocnum entity) {
        Example example = new Example(GovSendDocnum.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        example.and().andEqualTo("id", entity.getId());
        example.and().andEqualTo("numName", entity.getNumName());
        return govSendDocnumMapper.selectOneByExample(example);
    }

	@Override
	public GovSendDocnum selectById(String docnumId) {
		return govSendDocnumMapper.selectByPrimaryKey(docnumId);
	}

	@Override
	public boolean validateFileNumber(String number, String docnumId) {
		Example example = new Example(GovSendDocnumValue.class);
        example.createCriteria().andEqualTo("value",Integer.valueOf(number));
        example.and().andEqualTo("docnumId",docnumId);
        if(govSendDocnumValueMapper.selectCountByExample(example) > 0) {
        	return true;
        }else {
        	return false;
        }
	}

	@Override
	public List<GovSendDocnumValue> fileNumberList(Page page,String docnumId) {
        return govSendDocnumValueMapper.fileNumberList(page,docnumId);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void insertFileNumber(GovSendDocnumValue govSendDocnumValue) {
		govSendDocnumValue.setId(String.valueOf(IdWork.id.nextId()));
		govSendDocnumValueMapper.insertSelective(govSendDocnumValue);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void updateFileNumber(GovSendDocnumValue govSendDocnumValue) {
		govSendDocnumValueMapper.updateByPrimaryKeySelective(govSendDocnumValue);
	}

	@Override
	@Transactional(readOnly = false)
	public void delFileNumber(String numberId) {
		govSendDocnumValueMapper.deleteByPrimaryKey(numberId);
	}
	

}
