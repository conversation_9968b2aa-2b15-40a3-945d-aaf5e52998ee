<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.gov.dao.GovSendDocnumMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.gov.model.GovSendDocnum">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NUM_NAME" jdbcType="VARCHAR" property="numName" />
    <result column="NUM_TYPE" jdbcType="VARCHAR" property="numType" />
    <result column="NUM_FORMAT" jdbcType="VARCHAR" property="numFormat" />
    <result column="NUM_MODE" jdbcType="VARCHAR" property="numMode" />
    <result column="INIT_VALUE" jdbcType="VARCHAR" property="initValue" />
    <result column="BIT_NUM" jdbcType="VARCHAR" property="bitNum" />
    <result column="KEY_VALUE" jdbcType="DECIMAL" property="keyValue" />
    <result column="NUM_IS_WORD" jdbcType="DECIMAL" property="numIsWord" />
    <result column="NUM_IS_YEAR" jdbcType="DECIMAL" property="numIsYear" />
    <result column="NUM_IS_SERIAL" jdbcType="DECIMAL" property="numIsSerial" />
    <result column="OLD_YEAR" jdbcType="TIMESTAMP" property="oldYear" />
    <result column="CANMODIFY_EMP_ID" jdbcType="VARCHAR" property="canmodifyEmpId" />
    <result column="CANMODIFY_EMP_NAME" jdbcType="VARCHAR" property="canmodifyEmpName" />
    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
    <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
  </resultMap>
</mapper>