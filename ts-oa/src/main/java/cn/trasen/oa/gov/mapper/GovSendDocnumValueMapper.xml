<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.gov.dao.GovSendDocnumValueMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.gov.model.GovSendDocnumValue">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="value" jdbcType="INTEGER" property="value" />
    <result column="docnum_id" jdbcType="VARCHAR" property="docnumId" />
  </resultMap>
  
  <select id="getDocnumMaxValue" parameterType="String" resultMap="BaseResultMap">
  		select * from toa_gov_send_docnum_value
		where docnum_id = #{docnumId} 
		order by value desc 
		limit 1
  </select>
  
  <select id="fileNumberList" resultType="cn.trasen.oa.gov.model.GovSendDocnumValue" parameterType="String">
  		select t1.*,t2.NUM_NAME as numName from toa_gov_send_docnum_value t1
		LEFT JOIN toa_gov_send_docnum t2 on t1.docnum_id = t2.id
		where docnum_id = #{docnumId} 
		order by value desc 
  </select>
  
</mapper>