package cn.trasen.oa.patrol.vo.outVo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 巡查项目子项保存VO
 * <AUTHOR>
 * @date: 2021/12/6 16:04
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Data
public class PatrolItemChildOutVo {

    @ApiModelProperty(value = "巡查项id")
    private String fkPatrolItemId;

    @ApiModelProperty(value = "巡查项子项描述")
    private String itemChildDescribe;

    @NotEmpty(message = "巡查项子项是否必填不能为空")
    @ApiModelProperty(value = "巡查项子项是否必填（N否，Y是）")
    private String itemChildRequired;

    @NotEmpty(message = "巡查项子项类型不能为空")
    @ApiModelProperty(value = "巡查项子项类型（DX单选,DGX多选,SRK输入框,TP图片,FJ附件）")
    private String itemChildType;

    @ApiModelProperty(value = "巡查项子项内容")
    private String itemChildContent;

    @ApiModelProperty(value = "附件业务id")
    private String fileBusinessId;


}
