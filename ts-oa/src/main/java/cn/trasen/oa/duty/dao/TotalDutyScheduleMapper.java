package cn.trasen.oa.duty.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.duty.model.ToaTotalDutyScheduleAo;
import cn.trasen.oa.duty.model.ToaTotalDutyScheduleEo;
import cn.trasen.oa.duty.model.ToaTotalDutyScheduleVo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

public interface TotalDutyScheduleMapper extends Mapper<ToaTotalDutyScheduleEo> {
	
	List<ToaTotalDutyScheduleVo> getPageList(Page page, ToaTotalDutyScheduleAo record);

    void updateData(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}