package cn.trasen.oa.evaluation.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_evaluation_result")
@Setter
@Getter
public class EvaluationResult {
    /**
     * 主键
     */
	@Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 题目id
     */
    @Column(name = "set_id")
    @ApiModelProperty(value = "题目id")
    private String setId;

    /**
     * 主表id
     */
    @Column(name = "master_id")
    @ApiModelProperty(value = "主表id")
    private String masterId;

    /**
     * 发布id
     */
    @Column(name = "release_id")
    @ApiModelProperty(value = "发布id")
    private String releaseId;

    /**
     * 被考评人
     */
    @Column(name = "result_user")
    @ApiModelProperty(value = "被考评人")
    private String resultUser;

    /**
     * 被考评人名称
     */
    @Column(name = "result_user_name")
    @ApiModelProperty(value = "被考评人名称")
    private String resultUserName;

    /**
     * 题目类型    1单选    2多选   3下拉框  4填空
     */
    @Column(name = "resultu_type")
    @ApiModelProperty(value = "题目类型    1单选    2多选   3下拉框  4填空")
    private Integer resultuType;

    /**
     * 考评值
     */
    @Column(name = "result_value")
    @ApiModelProperty(value = "考评值")
    private String resultValue;

    /**
     * 总分数
     */
    @ApiModelProperty(value = "总分数")
    private Float totalscore;

    /**
     * 平均分
     */
    @ApiModelProperty(value = "平均分")
    private Float avgscore;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    @ApiModelProperty(value = "题目类型")
    private String resultuTypeStr;//题目类型
}