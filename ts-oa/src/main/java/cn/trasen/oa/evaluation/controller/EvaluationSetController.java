package cn.trasen.oa.evaluation.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.evaluation.model.EvaluationMaster;
import cn.trasen.oa.evaluation.model.EvaluationReportduty;
import cn.trasen.oa.evaluation.model.EvaluationSet;
import cn.trasen.oa.evaluation.service.EvaluationMasterService;
import cn.trasen.oa.evaluation.service.EvaluationReportdutyService;
import cn.trasen.oa.evaluation.service.EvaluationSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName EvaluationSetController
 * @Description TODO
 * @date 2024��4��10�� ����5:04:28
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "EvaluationSetController")
public class EvaluationSetController {

	private transient static final Logger logger = LoggerFactory.getLogger(EvaluationSetController.class);

	@Autowired
	private EvaluationSetService evaluationSetService;
	
	@Autowired
	private EvaluationMasterService evaluationMasterService;
	
	@Autowired
	private EvaluationReportdutyService evaluationReportdutyService;

	/**
	 * @Title saveEvaluationSet
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��10�� ����5:04:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/EvaluationSet/save")
	public PlatformResult<String> saveEvaluationSet(@RequestBody EvaluationSet record) {
		try {
			evaluationSetService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateEvaluationSet
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��4��10�� ����5:04:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/EvaluationSet/update")
	public PlatformResult<String> updateEvaluationSet(@RequestBody EvaluationSet record) {
		try {
			evaluationSetService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectEvaluationSetById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<EvaluationSet>
	 * @date 2024��4��10�� ����5:04:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/EvaluationSet/{id}")
	public PlatformResult<EvaluationSet> selectEvaluationSetById(@PathVariable String id) {
		try {
			EvaluationSet record = evaluationSetService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteEvaluationSetById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��4��10�� ����5:04:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/EvaluationSet/delete/{id}")
	public PlatformResult<String> deleteEvaluationSetById(@PathVariable String id) {
		try {
			evaluationSetService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 删除deleteEvaluationSet
	 * @param ids
	 * @return
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/EvaluationSet/deleteByIds")
	public PlatformResult<String> deleteByIds(String ids) {
		try {
			String[] idArray = ids.split(",");
			if(idArray != null && idArray.length > 0){
				for (String id : idArray) {
					 evaluationSetService.deleteById(id);
				 }
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectEvaluationSetList
	 * @Description 获取列表数据
	 * @param page
	 * @param record
	 * @return DataSet<EvaluationSet>
	 * @date 2024��4��10�� ����5:04:28
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/EvaluationSet/list")
	public DataSet<EvaluationSet> selectEvaluationSetList(Page page, EvaluationSet record) {
		return evaluationSetService.getDataSetList(page, record);
	}
	
	
	/**
	 * 问卷预览
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@ApiOperation(value = "问卷预览", notes = "问卷预览")
	@GetMapping("/api/EvaluationSet/previewQuestionnaire")
	public PlatformResult<Map<String,Object>> previewQuestionnaire(EvaluationSet record) {
		try {
			EvaluationMaster evaluationMaster = evaluationMasterService.selectById(record.getMasterId());
			List<EvaluationSet> evaluationSetlist = evaluationSetService.selectByMasterid(record.getMasterId());
			List<EvaluationReportduty> evaluationReportdutyList = evaluationReportdutyService.selectByMasterid(record.getMasterId());
			Map<String , Object> resultMap = new HashMap<String, Object>();
			resultMap.put("evaluationMaster",evaluationMaster);
			resultMap.put("evaluationSetlist",evaluationSetlist);
			resultMap.put("evaluationReportdutyList",evaluationReportdutyList);
			return PlatformResult.success(resultMap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
