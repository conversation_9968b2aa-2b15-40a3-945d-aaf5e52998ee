package cn.trasen.oa.check.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_check_route_content")
@Setter
@Getter
public class ToaCheckRouteContent {
	@Id
    private String id;

    @Column(name = "route_detail_id")
    private String routeDetailId;

    @Column(name = "route_id")
    private String routeId;
    /**
     * 检查内容
     */
    @Column(name = "check_content")
    @ApiModelProperty(value = "检查内容")
    private String checkContent;
    
    @ApiModelProperty(value = "排序")
    private Integer seq;
    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}