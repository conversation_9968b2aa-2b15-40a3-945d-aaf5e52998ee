package cn.trasen.oa.check.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.check.model.ToaStartCheckDetail;

/**
 * @ClassName ToaStartCheckDetailService
 * @Description TODO
 * @date 2021��8��4�� ����11:21:46
 * <AUTHOR>
 * @version 1.0
 */
public interface ToaStartCheckDetailService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021��8��4�� ����11:21:46
	 * <AUTHOR>
	 */
	Integer save(ToaStartCheckDetail record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021��8��4�� ����11:21:46
	 * <AUTHOR>
	 */
	Integer update(ToaStartCheckDetail record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021��8��4�� ����11:21:46
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return ToaStartCheckDetail
	 * @date 2021��8��4�� ����11:21:46
	 * <AUTHOR>
	 */
	ToaStartCheckDetail selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<ToaStartCheckDetail>
	 * @date 2021��8��4�� ����11:21:46
	 * <AUTHOR>
	 */
	DataSet<ToaStartCheckDetail> getDataSetList(Page page, ToaStartCheckDetail record);
}
