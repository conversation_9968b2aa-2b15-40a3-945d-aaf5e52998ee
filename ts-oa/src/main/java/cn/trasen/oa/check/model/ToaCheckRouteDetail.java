package cn.trasen.oa.check.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "toa_check_route_detail")
@Setter
@Getter
public class ToaCheckRouteDetail {
	@Id
    private String id;

    @Column(name = "route_id")
    private String routeId;

    /**
     * 检查地点
     */
    @Column(name = "check_address")
    @ApiModelProperty(value = "检查地点")
    private String checkAddress;

    /**
     * 检查坐标
     */
    @Column(name = "check_coordinate")
    @ApiModelProperty(value = "检查坐标")
    private String checkCoordinate;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    
    @Transient
    private Integer count;
    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    /**
     * 安全检查地点里面的内容
     */
    @Transient
    @ApiModelProperty(value = "安全检查地点里面的内容")
    private List<ToaCheckRouteContent> checkRouteContents;
    
    @Transient
    @ApiModelProperty(value = "发起检查检查内容")
    private List<ToaStartCheckDetail> startCheckDetails;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
}