package cn.trasen.oa.check.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.check.model.StartCheckAddress;

/**
 * @ClassName StartCheckAddressService
 * @Description TODO
 * <AUTHOR>
 * @version 1.0
 */
public interface StartCheckAddressService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * <AUTHOR>
	 */
	Integer save(StartCheckAddress record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * <AUTHOR>
	 */
	Integer update(StartCheckAddress record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return StartCheckAddress
	 * <AUTHOR>
	 */
	StartCheckAddress selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<StartCheckAddress>
	 * <AUTHOR>
	 */
	DataSet<StartCheckAddress> getDataSetList(Page page, StartCheckAddress record);
}
