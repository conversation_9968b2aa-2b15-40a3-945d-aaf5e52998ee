package cn.trasen.oa.utils;




import org.springframework.util.Base64Utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;

/**
 * @version V1.0
 * @desc AES 加密工具类
 */
public class AESUtil {

    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";//默认的加密算法

    /**
     * AES 加密操作
     *
     * @param content  待加密内容
     * @param password 加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String password) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);// 创建密码器

            byte[] byteContent = content.getBytes("utf-8");

            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(password));// 初始化为加密模式的密码器

            byte[] result = cipher.doFinal(byteContent);// 加密

            return Base64Utils.encodeToString(result);//通过Base64转码返回
        } catch (Exception ex) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, ex);
        }

        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content
     * @param password
     * @return
     */
    public static String decrypt(String content, String password) {

        try {
            //实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);

            //使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(password));

            //执行操作
            byte[] result = cipher.doFinal(Base64Utils.decodeFromString(content));

            return new String(result, "utf-8");
        } catch (Exception ex) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, ex);
        }

        return null;
    }

    /**
     * 生成加密秘钥
     *
     * @return
     */
    /*private static SecretKeySpec getSecretKey(String password) {
        //返回生成指定算法密钥生成器的 KeyGenerator 对象
        KeyGenerator kg = null;
        try {
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            //AES 要求密钥长度为 128
            kg.init(128, new SecureRandom(password.getBytes()));
            //生成一个密钥
            SecretKey secretKey = kg.generateKey();
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);// 转换为AES专用密钥
        } catch (NoSuchAlgorithmException ex) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }*/
    
    
    private static SecretKeySpec getSecretKey(final String key) throws NoSuchAlgorithmException {

    	SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");

    	secureRandom.setSeed(key.getBytes());

    	KeyGenerator kg = null;

    	try{

    		kg = KeyGenerator.getInstance(KEY_ALGORITHM);

    	}catch(NoSuchAlgorithmException ex){

    		ex.getMessage();

    	}

    	kg.init(secureRandom); 

    	return new SecretKeySpec(kg.generateKey().getEncoded(),KEY_ALGORITHM);

    }
    
//    public static void main(String[] args) {
//    	/*String content = HttpUtil.get("http://10.200.10.23/ts-oa/oa/api/selectEmployeeList");
//    	JSONObject parseObject = JSON.parseObject(content);
//		String result = decrypt(parseObject.getString("object"),"MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALHhuiDXFcgNb6+D6Uq+7aI0oakz3avOjZx041OkBKqS4Sh/IUCXcA0VtbZRuLLxT3+5Dr8vHN/seL2XNMcsHRMCAwEAAQ==");
//		System.out.println(result);*/
//    	
//    	
//    	 double number = 4;
//         DecimalFormat df = new DecimalFormat("#.##");
//         String formattedNumber = df.format(number);
//         double roundedNumber = Double.parseDouble(formattedNumber);
//         
//         System.out.println("原始数值: " + number);
//         System.out.println("保留三位小数并四舍五入后的数值: " + roundedNumber);
//    	
//    	System.out.println(Math.round(roundedNumber));
//		
//	}
//    

}