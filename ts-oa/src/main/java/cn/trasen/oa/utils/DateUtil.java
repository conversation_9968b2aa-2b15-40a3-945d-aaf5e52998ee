//package cn.trasen.oa.utils;
//
//import java.text.DateFormat;
//import java.text.DecimalFormat;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.Calendar;
//import java.util.Date;
//
//import cn.trasen.homs.core.utils.StringUtil;
//
///**
// * 日期操作辅助类
// * 
// * <AUTHOR>
// * @version $Id: DateUtil.java, v 0.1 2014年3月28日 上午8:58:11 ShenHuaJie Exp $
// */
//public final class DateUtil {
//	private DateUtil() {
//	}
//
//	/** 日期格式 **/
//	public interface DATE_PATTERN {
//		String HHMMSS = "HHmmss";
//		String HH_MM = "HH:mm";
//		String HH_MM_SS = "HH:mm:ss";
//		String YYYYMMDD = "yyyyMMdd";
//		String YYYY_MM_DD = "yyyy-MM-dd";
//		String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
//		String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";
//		String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
//	}
//
//	/**
//	 * 格式化日期
//	 * 
//	 * @param date
//	 * @param
//	 * @return
//	 */
//	public static final String format(Object date) {
//		return format(date, DATE_PATTERN.YYYY_MM_DD);
//	}
//
//	/**
//	 * 格式化日期
//	 * 
//	 * @param date
//	 * @param pattern
//	 * @return
//	 */
//	public static final String format(Object date, String pattern) {
//		if (date == null) {
//			return null;
//		}
//		if (pattern == null) {
//			return format(date);
//		}
//		return new SimpleDateFormat(pattern).format(date);
//	}
//
//	/**
//	 * 获取日期
//	 * 
//	 * @return
//	 */
//	public static final String getDate() {
//		return format(new Date());
//	}
//
//	/**
//	 * 获取日期时间
//	 * 
//	 * @return
//	 */
//	public static final String getDateTime() {
//		return format(new Date(), DATE_PATTERN.YYYY_MM_DD_HH_MM_SS);
//	}
//
//	/**
//	 * 获取日期
//	 * 
//	 * @param pattern
//	 * @return
//	 */
//	public static final String getDateTime(String pattern) {
//		return format(new Date(), pattern);
//	}
//
//	/**
//	 * 日期计算
//	 * 
//	 * @param date
//	 * @param field
//	 * @param amount
//	 * @return
//	 */
//	public static final Date addDate(Date date, int field, int amount) {
//		if (date == null) {
//			return null;
//		}
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(date);
//		calendar.add(field, amount);
//		return calendar.getTime();
//	}
//
//	/**
//	 * 字符串转换为日期:不支持yyM[M]d[d]格式
//	 * 
//	 * @param date
//	 * @return
//	 */
//	public static final Date stringToDate(String date) {
//		if (StringUtil.isEmpty(date)) {
//			return null;
//		}
//		String separator = String.valueOf(date.charAt(4));
//		String pattern = "yyyyMMdd";
//		if (!separator.matches("\\d*")) {
//			pattern = "yyyy" + separator + "MM" + separator + "dd";
//			if (date.length() < 10) {
//				pattern = "yyyy" + separator + "M" + separator + "d";
//			}
//		} else if (date.length() < 8) {
//			pattern = "yyyyMd";
//		}
//		pattern += " HH:mm:ss.SSS";
//		pattern = pattern.substring(0, Math.min(pattern.length(), date.length()));
//		try {
//			return new SimpleDateFormat(pattern).parse(date);
//		} catch (ParseException e) {
//			return null;
//		}
//	}
//
//	/**
//	 * 间隔天数
//	 * 
//	 * @param startDate
//	 * @param endDate
//	 * @return
//	 */
//	public static final Integer getDayBetween(Date startDate, Date endDate) {
//		Calendar start = Calendar.getInstance();
//		start.setTime(startDate);
//		start.set(Calendar.HOUR_OF_DAY, 0);
//		start.set(Calendar.MINUTE, 0);
//		start.set(Calendar.SECOND, 0);
//		start.set(Calendar.MILLISECOND, 0);
//		Calendar end = Calendar.getInstance();
//		end.setTime(endDate);
//		end.set(Calendar.HOUR_OF_DAY, 0);
//		end.set(Calendar.MINUTE, 0);
//		end.set(Calendar.SECOND, 0);
//		end.set(Calendar.MILLISECOND, 0);
//
//		long n = end.getTimeInMillis() - start.getTimeInMillis();
//		return (int) (n / (60 * 60 * 24 * 1000l));
//	}
//
//	/**
//	 * 间隔月
//	 * 
//	 * @param startDate
//	 * @param endDate
//	 * @return
//	 */
//	public static final Integer getMonthBetween(Date startDate, Date endDate) {
//		if (startDate == null || endDate == null || !startDate.before(endDate)) {
//			return null;
//		}
//		Calendar start = Calendar.getInstance();
//		start.setTime(startDate);
//		Calendar end = Calendar.getInstance();
//		end.setTime(endDate);
//		int year1 = start.get(Calendar.YEAR);
//		int year2 = end.get(Calendar.YEAR);
//		int month1 = start.get(Calendar.MONTH);
//		int month2 = end.get(Calendar.MONTH);
//		int n = (year2 - year1) * 12;
//		n = n + month2 - month1;
//		return n;
//	}
//
//	/**
//	 * 间隔月，多一天就多算一个月
//	 * 
//	 * @param startDate
//	 * @param endDate
//	 * @return
//	 */
//	public static final Integer getMonthBetweenWithDay(Date startDate, Date endDate) {
//		if (startDate == null || endDate == null || !startDate.before(endDate)) {
//			return null;
//		}
//		Calendar start = Calendar.getInstance();
//		start.setTime(startDate);
//		Calendar end = Calendar.getInstance();
//		end.setTime(endDate);
//		int year1 = start.get(Calendar.YEAR);
//		int year2 = end.get(Calendar.YEAR);
//		int month1 = start.get(Calendar.MONTH);
//		int month2 = end.get(Calendar.MONTH);
//		int n = (year2 - year1) * 12;
//		n = n + month2 - month1;
//		int day1 = start.get(Calendar.DAY_OF_MONTH);
//		int day2 = end.get(Calendar.DAY_OF_MONTH);
//		if (day1 <= day2) {
//			n++;
//		}
//		return n;
//	}
//
//	/**
//	 * 获取当日所在周周一日期
//	 * 
//	 * <AUTHOR>
//	 * @param date
//	 * @return
//	 */
//	public static Date getMondayByToday(Date date) {
//		Calendar cal = Calendar.getInstance();
//		cal.setTime(date);
//		// 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
//		int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
//		if (1 == dayWeek) {
//			cal.add(Calendar.DAY_OF_MONTH, -1);
//		}
//		cal.setFirstDayOfWeek(Calendar.MONDAY);// 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
//		int day = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
//		cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
//		return cal.getTime();
//	}
//
//	/**
//	 * 比对时间差(返回结果单位秒)
//	 * 
//	 * @param date1
//	 * @param date2
//	 * @return
//	 */
//	public static long getTimeBetween(Date date1, Date date2) {
//		if (date1 == null || date2 == null) {
//			return 0;
//		}
//		return (date1.getTime() - date2.getTime()) / 1000;
//	}
//
//	/**
//	 * 根据描述计算时间（时：分）
//	 * 
//	 * @param seconds
//	 * @return
//	 */
//	public static String timePerSecond(int seconds) {
//		int hours = seconds / 60 / 60;
//		int mins = (seconds - hours * 3600) / 60;
//		String hour = String.valueOf(hours);
//		String min = String.valueOf(mins);
//		if (hour.length() == 1) {
//			hour = "0" + hour;
//		}
//		if (min.length() == 1) {
//			min = "0" + min;
//		}
//		return hour + ":" + min;
//	}
//
//	/**
//	 * 比对两个时间是否是同一天（年月日相等）
//	 * 
//	 * @param date1
//	 * @param date2
//	 * @return
//	 */
//	public static boolean equalsDate(Date date1, Date date2) {
//		if (date1 == null || date2 == null) {
//			return false;
//		}
//		if (format(date1).equals(format(date2))) {
//			return true;
//		}
//		return false;
//	}
//
//	/**
//	 * 获取时分秒
//	 * 
//	 * <AUTHOR> 2017年5月18日09:40:41
//	 * @param date1
//	 * @return
//	 */
//	public static String getTimeForDate(Date date1) {
//		if (date1 == null) {
//			return "";
//		}
//		DateFormat df3 = DateFormat.getTimeInstance();// 只显示出时分秒
//		return df3.format(date1);
//	}
//
//	/**
//	 * 获取季度
//	 * 
//	 * @param date
//	 * @return
//	 */
//	public static int getSeason(Date date) {
//
//		int season = 0;
//
//		Calendar c = Calendar.getInstance();
//		c.setTime(date);
//		int month = c.get(Calendar.MONTH);
//		switch (month) {
//		case Calendar.JANUARY:
//		case Calendar.FEBRUARY:
//		case Calendar.MARCH:
//			season = 1;
//			break;
//		case Calendar.APRIL:
//		case Calendar.MAY:
//		case Calendar.JUNE:
//			season = 2;
//			break;
//		case Calendar.JULY:
//		case Calendar.AUGUST:
//		case Calendar.SEPTEMBER:
//			season = 3;
//			break;
//		case Calendar.OCTOBER:
//		case Calendar.NOVEMBER:
//		case Calendar.DECEMBER:
//			season = 4;
//			break;
//		default:
//			break;
//		}
//		return season;
//	}
//
//	// 使用当前月份,得到上一个月的月份:月份的格式是:yyyy-MM
//	public static String getLastDate(String currentDate) {
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//		Date date = null;
//		try {
//			date = sdf.parse(currentDate + "-" + "01");
//		} catch (ParseException e) {
//			e.printStackTrace();
//		}
//
//		Calendar c = Calendar.getInstance();
//		c.setTime(date);
//		c.add(Calendar.MONTH, -1);
//		String lastDate = "";
//		int month = c.get(Calendar.MONTH) + 1;
//		if (month < 10) {
//			lastDate = c.get(Calendar.YEAR) + "-0" + month;
//		} else {
//			lastDate = c.get(Calendar.YEAR) + "-" + month;
//		}
//
//		return lastDate;
//	}
//
//	/**
//	 * 根据 年、月 获取对应的月份 的 天数
//	 */
//	public static int getDaysByYearMonth(int year, int month) {
//		Calendar a = Calendar.getInstance();
//		a.set(Calendar.YEAR, year);
//		a.set(Calendar.MONTH, month - 1);
//		a.set(Calendar.DATE, 1);
//		a.roll(Calendar.DATE, -1);
//		int maxDate = a.get(Calendar.DATE);
//		return maxDate;
//	}
//
//	/**
//	 * 判断时间是否在时间段内
//	 * 
//	 * @param nowTime
//	 * @param beginTime
//	 * @param endTime
//	 * @return
//	 */
//	public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
//		return nowTime.getTime() >= beginTime.getTime() && nowTime.getTime() <= endTime.getTime();
//	}
//
//	public static Date stringToDate2(String time) {
//		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");// 日期格式
//		Date date = null;
//		try {
//			date = format.parse(time);
//		} catch (ParseException e) {
//			e.printStackTrace();
//		}
//		return date;
//	}
//
//	/**
//	 * 获取指定日期所在月份开始的时间戳
//	 * 
//	 * @param date
//	 *            指定日期
//	 * @return
//	 */
//	public static Date getMonthBegin(Date date) {
//		Calendar c = Calendar.getInstance();
//		c.setTime(date);
//
//		// 设置为1号,当前日期既为本月第一天
//		c.set(Calendar.DAY_OF_MONTH, 1);
//		// 将小时至0
//		c.set(Calendar.HOUR_OF_DAY, 0);
//		// 将分钟至0
//		c.set(Calendar.MINUTE, 0);
//		// 将秒至0
//		c.set(Calendar.SECOND, 0);
//		// 将毫秒至0
//		c.set(Calendar.MILLISECOND, 0);
//		// 获取本月第一天的时间戳
//		return c.getTime();
//	}
//
//	/**
//	 * 获取指定日期所在月份结束的时间戳
//	 * 
//	 * @param date
//	 *            指定日期
//	 * @return
//	 */
//	public static Date getMonthEnd(Date date) {
//		Calendar c = Calendar.getInstance();
//		c.setTime(date);
//
//		// 设置为当月最后一天
//		c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
//		// 将小时至23
//		c.set(Calendar.HOUR_OF_DAY, 23);
//		// 将分钟至59
//		c.set(Calendar.MINUTE, 59);
//		// 将秒至59
//		c.set(Calendar.SECOND, 59);
//		// 将毫秒至999
//		c.set(Calendar.MILLISECOND, 999);
//		// 获取本月最后一天的时间戳
//		return c.getTime();
//	}
//
//	/**
//	 * 
//	 * @Title: getEmpWorkYear @Description: 根据入职时间计算工龄 @param: @param
//	 * empHiredate @param: @return @return: Integer @author: Yuec @date:
//	 * 2021年3月29日 下午4:12:10 @throws
//	 */
//	public static final Integer getEmpWorkYear(Date empHiredate) {
//		
//		// 计算工龄天数
//		long days = cn.hutool.core.date.DateUtil.betweenDay(empHiredate, new Date(), true);
//		int countLeapYears = countLeapYears(empHiredate,new Date());
//		Double y = Math.floor((days-countLeapYears) / 365);
//		DecimalFormat dft = new DecimalFormat("##");
//		return Integer.valueOf(dft.format(y));
//	}
//	
//	
//	// 计算指定日期范围内的闰年天数
//    private static int countLeapYears(Date startDate, Date endDate) {
//        int leapYears = 0;
//
//        for (int year = startDate.getYear(); year <= endDate.getYear(); year++) {
//            if (isLeapYear(year)) {
//                leapYears++;
//            }
//        }
//
//        return leapYears;
//    }
//
//    // 判断指定年份是否为闰年
//    private static boolean isLeapYear(int year) {
//        return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
//    }
//
//	public static String plusDay(int num, String newDate) throws ParseException {
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//		Date currdate = format.parse(newDate);
//		System.out.println("现在的日期是：" + currdate);
//		Calendar ca = Calendar.getInstance();
//		ca.add(Calendar.DATE, num);// num为增加的天数，可以改变的
//		currdate = ca.getTime();
//		String enddate = format.format(currdate);
//		System.out.println("增加天数以后的日期：" + enddate);
//		return enddate;
//	}
//
//	public static void main(String[] args) throws ParseException {
//		String startDate = "2021-12-28";
//
//		String endDate = "2021-12-31";
//
//		int diff = 3;
//
//		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
//
//		int day = getDayBetween(sf.parse(startDate), sf.parse(endDate));
//		
//		System.out.println(day);
//
//		System.out.println(day % diff == 0);
//		
//		System.out.println(plusDay(3, startDate));
//	}
//
//}
