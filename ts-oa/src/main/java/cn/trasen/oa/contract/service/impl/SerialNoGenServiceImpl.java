package cn.trasen.oa.contract.service.impl;

import cn.trasen.oa.contract.service.RedisService;
import cn.trasen.oa.contract.service.SerialNoGenService;
import cn.trasen.oa.contract.service.StepperService;
import cn.trasen.oa.contract.util.Comm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @projectName: apps
 * @package: cn.trasen.oa.contract.service.impl
 * @className: SerialNoGenServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 16:41
 * @version: 1.0
 */
@Service
public class SerialNoGenServiceImpl implements SerialNoGenService {

    @Autowired
    private StepperService stepperService;

    @Autowired
    private RedisService redisService;

    @Override
    public String genByDate(String key) {
        key = key + "-" + Comm.getCurDate("yyyyMMdd");
        long step = stepperService.step(key, 86400);
        String code = String.format("%s-%04d", key, step);
        return code;
    }


    @Override
    public String genCommonNo(String key, int length) {
        long step = stepperService.step(key, 0);
        String code = String.format("%s%0" + length + "d", key, step);
        return code;
    }

    @Override
    public String genAssetNo() {
        // 获取当前年份 只保留后两位
        String year = Comm.getCurDate("yy");
        String key = "ASSET_NO" + year;
        // 防止闰月？好像阳历根本不需要关心，但是这个数据很小，多存一段时间也无所谓
        long step = stepperService.step(key, 86400 * 400);
        // 5位数字
        String code = String.format("%s%06d", year, step);
        return code;
    }
}
