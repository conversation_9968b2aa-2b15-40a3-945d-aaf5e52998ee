package cn.trasen.oa.contract.service;

import cn.trasen.oa.contract.bean.PermissionResp;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.oa.contract.service
 * @className: PermissionService
 * @author: chenbin
 * @description: 某个用户对于当前业务有什么权限
 * @date: 2025/4/24 08:58
 * @version: 1.0
 */

public interface PermissionService {
    PermissionResp getPermission(String business);

    PermissionResp cgetPermission(String business);

    void appendInCondition(StringBuffer sql, String field, List<String> values);

    void appendEqualCondition(StringBuffer sql, String field, String value);

    void appendPermissionSql(String business, StringBuffer sql);

    void appendPermissionCondition(String business, Example.Criteria criteria);
}
