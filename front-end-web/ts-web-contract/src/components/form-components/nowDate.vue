<template>
  <div style="width: 50%">
    <ts-form-item :label="fieldDesc" :prop="fieldName" :rules="requiredValue">
      <ts-date-picker
        style="width: 100%"
        type="datetime"
        v-model="formData[fieldName]"
        :placeholder="fieldTip"
        :readonly="readonlyValue"
      >
      </ts-date-picker>
    </ts-form-item>
  </div>
</template>

<script>
import props from './props';
export default {
  mixins: [props],
  created() {
    if (this.renderStatus === 'add') {
      let nowTime = this.$dayjs().format('YYYY-MM-DD');
      this.$set(this.formData, this.fieldName, nowTime);
    }
  }
};
</script>
