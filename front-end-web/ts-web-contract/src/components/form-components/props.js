export default {
  props: {
    formData: {
      typeof: Object,
      default: () => {}
    },
    fieldName: {
      typeof: String,
      default: ''
    },
    fieldDesc: {
      typeof: String,
      default: ''
    },
    required: {
      typeof: String,
      default: '0'
    },
    readonly: {
      typeof: String,
      default: '0'
    },
    fieldDefault: {
      typeof: String,
      default: ''
    },
    fieldLength: {
      typeof: String,
      default: ''
    },
    fieldTip: {
      typeof: String,
      default: ''
    },
    renderStatus: {
      type: String,
      default: 'add'
    }
  },
  watch: {
    fieldDefault: {
      handler(val) {
        if (
          !this.formData[this.fieldName] &&
          val &&
          this.renderStatus === 'add'
        ) {
          this.$set(this.formData, this.fieldName, val);
        }
      },
      immediate: true
    }
  },
  computed: {
    requiredValue() {
      return { required: this.required === '1', message: '必填' };
    },
    readonlyValue() {
      return this.readonly === '1';
    }
  }
};
