export default {
  data() {
    return {
      fundTypeTreeData: [],
      searchForm: {},

      searchList: [
        {
          value: 'condition',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '项目名称/项目负责人'
          }
        },
        {
          label: '经费类型',
          value: 'fundType'
        }
      ],

      activeFundsType: '1',
      baseColumns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '项目名称',
          prop: 'itemName',
          minWidth: 440,
          formatter: (row, prop, cell) => {
            return (
              <span
                class="action-cell"
                onclick={this.handleOpenPreview.bind(this, row)}>
                {cell}
              </span>
            );
          }
        },
        {
          label: '项目负责人',
          prop: 'leaderUserName',
          minWidth: 100
        },
        {
          label: '科室',
          prop: 'deptName',
          minWidth: 150
        },
        {
          label: '状态',
          prop: 'status',
          formatter: (row, prop, cell) => {
            return ['待提交', '已提交', '已录入'][cell] || '';
          }
        },
        {
          label: '经费类型',
          prop: 'fundTypeName'
        },
        {
          label: '联系方式',
          prop: 'telephone',
          align: 'center',
          minWidth: 120
        },
        {
          label: '上报人',
          prop: 'createUserName'
        },
        {
          label: '上报时间',
          prop: 'createDate',
          minWidth: 160
        }
      ]
    };
  },
  computed: {
    columns() {
      let columns = [].concat(this.baseColumns);
      if (this.activeFundsType != 2) {
        columns.push({
          label: '操作',
          fixed: 'right',
          align: 'center',
          formatter: row => {
            if (row.status != 0) {
              return;
            }

            let action = [
              {
                label: '提交',
                event: this.handleSubmit.bind(this, row)
              },
              {
                label: '编辑',
                event: this.handleOpenEditModel.bind(this, row, 'edit')
              },
              {
                label: '删除',
                event: this.handleDeleteFund.bind(this, row, 'approval')
              }
            ];

            return (
              <BaseActionCell
                actions={action}
                on={{
                  'action-select': e => e()
                }}
              />
            );
          }
        });
      }
      return columns;
    }
  },
  watch: {
    activeFundsType() {
      this.refresh();
    }
  },
  methods: {
    refreshTable(page) {
      let { pageNo = 1, pageSize = 15 } = page || {},
        data = {
          pageNo,
          pageSize,
          ...this.searchForm,
          sord: 'desc',
          sidx: 'a.create_date'
        };

      this.ajax.getReportFundsTableDatas(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        res.rows.forEach((item, index) => {
          item.pageIndex = index + 1 + (pageNo - 1) * pageSize;
        });
        this.$refs.table.refresh(res);
      });
    },
    getFundsType() {
      this.ajax
        .getFundTypesTreeData()
        .then(res => {
          this.fundTypeTreeData = res;
        })
        .catch();
    }
  }
};
