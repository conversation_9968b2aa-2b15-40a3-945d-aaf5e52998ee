<template>
  <div class="creation-box">
    <ts-search-bar
      class="search-bar"
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    />
    <ul class="content">
      <li class="type-box" v-for="(item, index) in list" :key="index">
        <top-title :title="item.typeName" />
        <ul class="contract-box">
          <li v-for="children in item.definitionList || []" :key="children.id">
            <contract-type-item
              :info="children"
              @handleItemClick="handleOpenDialogCreationRender(children)"
            />
          </li>
        </ul>
      </li>
    </ul>

    <dialog-creation-render
      :key="destroyKey"
      v-model="openCreatuibRenderDialog"
      :title="creationRendertitle"
      :typeContractData="typeContractData"
      processTabsStatus="1"
      :renderStatus="renderStatus"
      @initDialogInfo="initDialogInfo"
    />
  </div>
</template>

<script>
import TopTitle from '@/components/TopTitle.vue';
import ContractTypeItem from './components/contract-type-item.vue';
import DialogCreationRender from './dialog/dialog-creation-render.vue';

import api from '@/api/ajax/api-creation.js';
export default {
  components: {
    TopTitle,
    ContractTypeItem,
    DialogCreationRender
  },
  data() {
    return {
      destroyKey: 0,
      list: [],
      searchList: [
        {
          label: '合同类型',
          value: 'definitionName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入合同类型'
          }
        }
      ],
      searchForm: {
        definitionName: ''
      },

      openCreatuibRenderDialog: false,
      creationRendertitle: '',
      renderStatus: '',
      typeContractData: {}
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    search() {
      this.getTableList();
    },
    async getTableList() {
      let data = {
        isAll: 'Y',
        definitionName: this.searchForm.definitionName || ''
      };
      const res = await api.getContractTypeList(data);
      if (res.success && res.statusCode === 200) {
        this.list = res.object || [];
      }
    },
    handleOpenDialogCreationRender(children) {
      children.definitionId = children.id;
      children.showWorkflowInfo = false;

      this.creationRendertitle = '合同流程创建-' + children.definitionName;
      this.typeContractData = children;
      this.renderStatus = 'add';
      this.openCreatuibRenderDialog = true;
    },
    initDialogInfo() {
      this.destroyKey++;
    }
  }
};
</script>

<style lang="scss" scoped>
.creation-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 0 0 8px;
  > * {
    box-sizing: border-box;
    &::-webkit-scrollbar {
      width: 6px;
    }
    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  ul {
    margin-bottom: 0px;
  }
  .search-bar {
    margin: 8px 0;
  }
  .content {
    width: 100%;
    height: calc(100% - 55px);
    overflow-y: auto;
    .type-box {
      margin-bottom: 8px;
      .contract-box {
        display: flex;
        flex-wrap: wrap;
        margin-top: 8px;
        > li {
          width: calc((100% - 32px) / 5);
          margin-right: 8px;
          border: 1px solid #eee;
          &:nth-child(5n) {
            margin-right: 0px;
          }
        }
      }
    }
  }
}
</style>
