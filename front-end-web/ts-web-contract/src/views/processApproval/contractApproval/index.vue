<template>
  <div class="contract-approval-box">
    <div class="head">
      <ts-tabs v-model="processTabsStatus">
        <ts-tab-pane label="待我办理" name="1"></ts-tab-pane>
        <ts-tab-pane label="我已办理" name="2"></ts-tab-pane>
        <ts-tab-pane label="由我发起" name="3"></ts-tab-pane>
        <ts-tab-pane label="合同草稿" name="4"></ts-tab-pane>
      </ts-tabs>
    </div>
    <div class="content">
      <contract-status-table
        v-if="processTabsStatus !== '0'"
        ref="ContractStatusTable"
        :processTabsStatus="processTabsStatus"
        @emitHandleShowDetails="emitHandleShowDetails"
      />
    </div>

    <dialog-creation-render
      :key="destroyKey"
      v-model="editStepCreatuibRenderDialog"
      :title="creationRendertitle"
      :typeContractData="typeContractData"
      :renderStatus="renderStatus"
      :processTabsStatus="processTabsStatus"
      @initDialogInfo="initDialogInfo"
      @refresh="tableRefresh"
    />
  </div>
</template>

<script>
import creationApi from '@/api/ajax/api-creation.js';
import { deepClone } from '@/unit/commonHandle.js';

import ContractStatusTable from './components/contract-status-table.vue';

import DialogCreationRender from '../../contractCreation/dialog/dialog-creation-render.vue';
export default {
  components: {
    DialogCreationRender,
    ContractStatusTable
  },
  data() {
    return {
      processTabsStatus: '1',

      editStepCreatuibRenderDialog: false,
      renderStatus: '',
      creationRendertitle: '',
      destroyKey: 0,
      typeContractData: {}
    };
  },
  watch: {
    processTabsStatus: {
      handler() {
        this.tableRefresh();
      },
      immediate: true
    }
  },
  methods: {
    async tableRefresh() {
      await this.$nextTick();
      this.$refs.ContractStatusTable.searchForm = {};
      this.$refs.ContractStatusTable.getTableData();
    },
    async emitHandleShowDetails(row) {
      // 回显数据
      const res = await creationApi.selectAllContractRecordById({ id: row.id });
      if (res.success && res.statusCode === 200) {
        this.renderStatus = 'edit';

        // 我已办理（无操作按钮） 由我发起 渲染遮罩仅查看
        if (this.processTabsStatus === '2' || this.processTabsStatus === '3') {
          this.renderStatus = 'show';
        }

        this.creationRendertitle = `合同流程创建-${row.contract_type}-[${row.currentStepName}]`;
        const {
          contractRecord,
          contractRecordPayment: paymentList, // 合同付款信息
          contractRecordPartners: recordPartnersList, // 相对方
          contractRecordGoods: goodsList // 合同标的物信息
        } = res.object;

        let basicsFieldForm = JSON.parse(contractRecord.field_json);

        // 回显相对方信息 签署方数 甲方公司名称
        let signNumbersOption = {
          1: '双方签署',
          2: '三方签署'
        };
        basicsFieldForm.signNumbers =
          signNumbersOption[recordPartnersList.length];
        basicsFieldForm.partya = recordPartnersList[0].partya;
        basicsFieldForm.filesId = contractRecord.files_id;
        basicsFieldForm.templateId = contractRecord.template_id;

        let data = {
          formData: {
            ...basicsFieldForm,
            paymentList,
            recordPartnersList,
            goodsList
          },
          definitionName: row.contract_type,
          workflowName: row.workflowName,
          definitionId: contractRecord.definition_id,
          formId: row.form_id,
          taskId: row.taskId,
          workflowId: row.workflow_id,
          wfInstanceId: row.wfInstanceId,
          currentStepNo: row.currentStepNo,
          wfDefinitionId: row.wf_definition_id,
          currentStepName: row.currentStepName,
          instanceStatus: row.instanceStatus,
          recordId: row.id,
          showWorkflowInfo: true,
          definition_id: row.definition_id
        };
        this.typeContractData = deepClone(data);
        this.editStepCreatuibRenderDialog = true;
      }
    },
    initDialogInfo() {
      this.destroyKey++;
    }
  }
};
</script>

<style lang="scss" scoped>
.contract-approval-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 0 0 8px;
  > * {
    box-sizing: border-box;
    &::-webkit-scrollbar {
      width: 6px;
    }
    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }

  .content {
    height: calc(100% - 44px);
  }
}
</style>
