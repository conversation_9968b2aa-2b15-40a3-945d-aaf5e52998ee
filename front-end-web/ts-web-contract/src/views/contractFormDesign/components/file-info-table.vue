<template>
  <div class="file-info-field-box">
    <base-table border stripe ref="table" :columns="columns" :hasPage="false">
      <template #action="{row}" class="action-operation">
        <div class="action-row">
          <el-dropdown
            v-if="row.defaultField !== '1'"
            trigger="hover"
            placement="bottom-end"
          >
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <el-dropdownMenu slot="dropdown" placement="bottom-end">
              <el-dropdownItem class="action-item">
                <span @click="handleEdit(row)">编辑</span>
              </el-dropdownItem>
              <el-dropdownItem class="action-item">
                <span @click="handleDelete(row)">删除</span>
              </el-dropdownItem>
              <el-dropdownItem class="action-item">
                <span @click="handleStopUsing(row)">{{
                  row.enabled === '1' ? '停用' : '启用'
                }}</span>
              </el-dropdownItem>
            </el-dropdownMenu>
          </el-dropdown>
        </div>
      </template>
    </base-table>
  </div>
</template>

<script>
import BaseTable from '@/components/base-table/index.vue';

import fileInfoTableMixin from '../mixins/file-info-table-mixin';
export default {
  mixins: [fileInfoTableMixin],
  components: {
    BaseTable
  },
  props: {
    fileInfoData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  async created() {
    await this.$nextTick();
    this.initRefreshTableData();
  },
  watch: {
    fileInfoData(val) {
      this.initRefreshTableData();
    }
  },
  methods: {
    initRefreshTableData() {
      this.$refs.table.refresh({
        rows: this.fileInfoData
      });
    },
    handleEdit(row) {
      this.$emit('editField', row);
    },
    handleDelete(row) {
      this.$emit('deleteField', row);
    },
    handleStopUsing(row) {
      this.$emit('stopUsingField', row);
    }
  }
};
</script>

<style lang="scss" scoped>
.file-info-field-box {
  padding: 0px 8px 0 0;
  overflow: hidden;
  .operation-span {
    color: $primary-blue;
    margin-right: 8px;
    cursor: pointer;
  }
}
</style>
