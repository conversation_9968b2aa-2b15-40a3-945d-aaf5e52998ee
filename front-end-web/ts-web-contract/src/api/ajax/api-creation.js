import { $api } from '@/api/ajax';

const getContractTypeList = function(data) {
  return $api({
    url: `/ts-oa/api/contractType/getContractTypeList`,
    method: 'get',
    data
  });
};

const selectContractFormFieldList = function(data) {
  return $api({
    url: `/ts-oa/api/contractFormField/selectContractFormFieldList`,
    method: 'get',
    data
  });
};

const selectContractTemplateList = function() {
  return $api({
    url: `/ts-oa/api/contractTemplate/selectContractTemplateList`,
    method: 'get'
  });
};

// 保存 提交接口
const contractRecordSave = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

// 获取工作流节点信息(最初提交)
const getNextWfStepListByWfDefId = function(data) {
  return $api({
    url: `/ts-workflow/workflow/task/getNextWfStepListByWfDefId`,
    method: 'post',
    data
  });
};

// 获取工作流节点信息（提交之后）
const getNextWfStepListByTaskId = function(data) {
  return $api({
    url: `/ts-workflow/workflow/task/getNextWfStepListByTaskId`,
    method: 'post',
    data
  });
};

const workflowTaskHisList = function(params) {
  return $api({
    url: `/ts-workflow/workflow/task/his/list`,
    method: 'get',
    params
  });
};

// 催办合同流程
const workflowWfInstPress = function(data) {
  return $api({
    url: `/ts-workflow/workflow/wfInst/press`,
    method: 'post',
    data
  });
};

// 撤回合同流程
const doUndoInstance = function(data) {
  return $api({
    url: `/ts-workflow/workflow/instance/doUndoInstance`,
    method: 'post',
    data
  });
};

// 打开合同版本
const selectContractRecordFileList = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecordFile/selectContractRecordFileList`,
    method: 'get',
    data
  });
};

// 是否拟稿
const selectIsHaveDraft = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/selectIsHaveDraft`,
    method: 'get',
    data
  });
};

// 回显 保存草稿接口
const selectAllContractRecordById = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/selectAllContractRecordById`,
    method: 'get',
    data
  });
};

// 起草 查看流程
const getWorkflowParams = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/getWorkflowParams`,
    method: 'get',
    data
  });
};

// 合同 流程退回 获取流程
const getHisTaskNodeList = function(data) {
  return $api({
    url: `/ts-workflow/workflow/taskHis/getHisTaskNodeList`,
    method: 'post',
    data
  });
};

// 合同 流程退回接口
const contractRecordReject = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/reject`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

// 删除草稿
const deleteContractRecordById = function(data) {
  return $api({
    url: `/ts-oa/api/contractRecord/delete/deleteContractRecordById`,
    method: 'post',
    data
  });
};

export default {
  getContractTypeList,
  selectContractFormFieldList,
  selectContractTemplateList,
  contractRecordSave,
  getNextWfStepListByWfDefId,
  workflowTaskHisList,
  workflowWfInstPress,
  doUndoInstance,
  getNextWfStepListByTaskId,
  selectIsHaveDraft,
  selectAllContractRecordById,
  getWorkflowParams,
  getHisTaskNodeList,
  selectContractRecordFileList,
  deleteContractRecordById,
  contractRecordReject
};
