{"name": "ts-web-contract", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint"}, "dependencies": {"@ckeditor/ckeditor5-basic-styles": "^27.0.0", "@ckeditor/ckeditor5-dev-utils": "^24.4.2", "@ckeditor/ckeditor5-dev-webpack-plugin": "^24.4.2", "@ckeditor/ckeditor5-editor-classic": "^27.0.0", "@ckeditor/ckeditor5-essentials": "^27.0.0", "@ckeditor/ckeditor5-image": "^27.0.0", "@ckeditor/ckeditor5-link": "^27.0.0", "@ckeditor/ckeditor5-paragraph": "^27.0.0", "@ckeditor/ckeditor5-theme-lark": "^27.0.0", "@ckeditor/ckeditor5-vue2": "^1.0.5", "@tinymce/tinymce-vue": "^3.0.1", "@trasen-oa/trasen-ui-web": "^1.1.1", "@trasen/trasen-axios": "^1.0.8", "@trasen/trasen-element-ui": "^1.1.0", "@trasen/trasen-logs": "^1.0.4", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "base64-js": "^1.5.1", "core-js": "^3.6.5", "dayjs": "^1.10.7", "echarts": "^5.1.2", "element-ui": "^2.15.6", "js-base64": "^3.7.2", "js-export-excel": "^1.1.4", "qs": "^6.10.1", "raven-js": "^3.27.2", "tinymce": "^5.8.2", "vue": "^2.6.11", "vue-fragment": "^1.5.2", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.13.10", "@babel/runtime-corejs3": "^7.13.10", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-e2e-cypress": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.0.3", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^2.3.3", "compression-webpack-plugin": "^1.1.12", "css-loader": "^5.0.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.14.1", "prettier": "^1.19.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "rules": {"no-console": "off", "no-unused-vars": "off", "no-debugger": "off", "vue/no-unused-components": "off"}}