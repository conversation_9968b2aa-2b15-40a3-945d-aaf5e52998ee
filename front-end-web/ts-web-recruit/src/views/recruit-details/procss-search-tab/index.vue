<template>
  <div id="ProgressSearchBox">
    <Authentication
      :planId="planId"
      type="searchPlanProgress"
      @getIsSignUpHandle="getIsSignUpHandle"
      v-if="progress === 'check'"
    />
    <div class="info-progress-box" v-else-if="progress === 'info'">
      <h3 class="plan-name">
        {{ planNoticeInfo.name }}
      </h3>
      <span class="plan-time">
        报名时间:
        <span>
          {{ planNoticeInfo.beginTime }}至{{ planNoticeInfo.endTime }}
        </span>
      </span>
      <div class="recruit-bottom">
        <div v-for="(item, index) in progressArr" :key="item">
          <span
            class="recruit-progress"
            :class="{ active: item === progressAcive }"
            @click="switchActiveHandle(item)"
            >{{ item }}</span
          >
          <img
            v-if="index !== progress.length"
            src="@/assets/icon-next-step.png"
          />
        </div>
      </div>
      <!--报名-->
      <div class="progress-content-box">
        <div v-if="progressAcive === '报名'">
          <div class="sign-up-tips">
            <div>
              当前报名：<span style="color: #d9001b">{{ signUpPostName }}</span>
            </div>
            <div class="button-box">
              <div v-if="isHaveInHand">
                <button v-if="childFormReadonly" @click="editSignUpHandle">
                  编辑
                </button>
                <button v-else @click="submitSignUpHandle">保存</button>
              </div>
              <button @click="printFormHandle">打印</button>
            </div>
          </div>
          <div class="form-relative-box">
            <component
              id="FormBox"
              ref="componentName"
              type="progress"
              :is="templateName"
              :planId="planId"
              :readonly="childFormReadonly"
              :signUpData="userSignUpProgressInfo.signUpInfo.signUpData"
            ></component>
          </div>
        </div>
        <!--资格审查-->
        <div class="examination-status-box" v-if="progressAcive === '资格审查'">
          <div
            class="examination-item"
            v-for="(item, index) in examinationList"
            :key="index"
          >
            <div class="examination-left">
              <div class="examination-name">{{ item.postName }}</div>
              <div class="examination-tips">
                <p class="font" :class="item.className">
                  {{ item.status | examinationStatusLabel }}
                </p>
                <div class="fail-style" v-if="item.status === 1">
                  <p>未通过原因 "{{ item.remarks }}"</p>
                  <p>如有疑问，可联系招聘工作人员了解</p>
                </div>
              </div>
            </div>

            <img class="examination-img" :src="item.imgUrl" />
          </div>
        </div>
        <!--笔试环节-->
        <div v-if="progressAcive === '笔试环节'">
          <WrittenInterviewStatus
            :basicInfo="basicInfo"
            :list="writtenList"
            type="笔试"
          />
        </div>
        <!--面试环节-->
        <div v-if="progressAcive === '面试环节'">
          <WrittenInterviewStatus
            :basicInfo="basicInfo"
            :list="interviewList"
            type="面试"
          />
        </div>
        <!--面试环节-->
        <div v-if="progressAcive === '实操考试'">
          <WrittenInterviewStatus
            :basicInfo="basicInfo"
            :list="semList"
            type="实操"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Authentication from '@/components/Authentication';
import lyszyy from '@/views/components/post-from/lyszyy';
import cscjk from '@/views/components/post-from/cscjk';

import WrittenInterviewStatus from '@/views/components/writtenInterviewStatus';

import { recruitByprocedureid } from '@/api/index.js';

import { readSignUpProgress } from '@/api/recruitDetails';
export default {
  components: {
    Authentication,
    lyszyy,
    cscjk,
    WrittenInterviewStatus,
  },
  props: {
    planNoticeInfo: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapState(['templateName']),
    isHaveInHand() {
      let arr = [3, 4, 5, 6, 7, 8];
      return !arr.includes(this.planNoticeInfo.stage);
    },
    signUpPostName() {
      return this.userSignUpProgressInfo.postList
        .map(item => item.postName)
        .join(',');
    },
  },
  data: () => ({
    planId: '',
    userSignUpProgressInfo: {},
    progress: 'check',
    childFormReadonly: true,
    progressArr: ['报名'],
    progressAcive: '报名',
    basicInfo: {},
    examinationList: null,
    writtenList: null,
    interviewList: null,
    semList: null,
  }),
  async created() {
    this.planId = this.$route.query.id;
    const procedureId = this.$route.query.procedureId;

    const res = await recruitByprocedureid(procedureId);

    if (!res.success) {
      this.$message.error(res.message || '获取流程失败!');
      return;
    }

    this.progressArr.push(...res.object.map(item => item.flowName));
  },
  filters: {
    examinationStatusLabel: status => {
      let str = '';
      if (status === 0 || status === 2) {
        str = '审查中，请耐心等候哦…';
      }

      if (status === 3) {
        str = '恭喜！您的报名信息已通过审查，请注意接收考试通知';
      }

      if (status === 1) {
        str = '很遗憾，您的报名信息未通过审查';
      }
      return str;
    },
  },
  watch: {
    userSignUpProgressInfo(val) {
      this.basicInfo = {
        name: val.signUpInfo.signUpData.name,
        identityCard: val.signUpInfo.signUpData.identityCard,
        gender: val.signUpInfo.signUpData.gender,
      };

      if (val.postList && val.postList.length > 0) {
        this.getExaminationListHandle(val);

        // 笔试 获取审查通过的人员
        let writtenTrueArr = val.postList.filter(item => item.examStage);
        this.getWrittenListHandle(writtenTrueArr);

        // 面试 获取笔试通过的人员
        let interviewTrueList = val.postList.filter(
          item => item.interviewStage
        );
        this.getInterviewListHandle(interviewTrueList);

        // 实操 获取笔试通过的人员
        let manipulateTrueList = val.postList.filter(item => item.manipulate);
        this.getManipulateTrueListHandle(manipulateTrueList);
      }
    },
  },
  methods: {
    getIsSignUpHandle(object) {
      this.userSignUpProgressInfo = object;
      this.progress = 'info';
    },
    async switchActiveHandle(item) {
      const info = this.userSignUpProgressInfo.signUpInfo.signUpData;
      let data = {
        planId: this.planId,
        name: info.name,
        identityCard: info.identityCard,
      };
      const res = await readSignUpProgress(data);
      if (res.success && res.statusCode === 200) {
        this.userSignUpProgressInfo = res.object;
        this.progressAcive = item;
      }
    },
    // 审查 设置不同的图标 添加不同的class
    getExaminationListHandle(val) {
      this.examinationList = val.postList.map(item => {
        let className = '';
        let imgUrl = '';
        const { status } = item.signUpStage;

        if (status === 0 || status === 2) {
          className = 'pending';
          imgUrl = require('@/assets/warit-view-status.png');
        }
        if (status === 1) {
          className = 'fail';
          imgUrl = require('@/assets/progress-fail.png');
        }
        if (status === 3) {
          className = 'pass';
          imgUrl = require('@/assets/progress-success.png');
        }
        return {
          imgUrl,
          className,
          ...item.signUpStage,
          postName: item.postName,
          postId: item.postId,
        };
      });
    },
    getWrittenListHandle(writtenTrueArr) {
      this.writtenList = writtenTrueArr.map(item => {
        // 时间格式化
        const { examBeginTime, examEndTime, examAddr, examContent } =
          item.examStage;
        let str = '';
        if (examBeginTime && examEndTime)
          str = examBeginTime + ' 至' + examEndTime.slice(10);

        item.examStage.time = str;
        item.examStage.addr = examAddr;
        item.examStage.content = examContent;

        // 根据状态设置对应的提示与文字颜色
        let tips = '';
        let className = '';
        let imgUrl = '';
        const { status, exemptionStatus } = item.examStage;
        if (status === 2 || exemptionStatus === 2) {
          tips = '恭喜您！通过笔试';
          className = 'pass';
          imgUrl = require('@/assets/progress-success.png');
        } else if (status === 1) {
          tips = '很遗憾，您未能通过笔试';
          className = 'fail';
          imgUrl = require('@/assets/progress-fail.png');
        } else {
          tips = '暂无笔试成绩信息';
          className = 'pending';
          imgUrl = require('@/assets/warit-view-status.png');
          item.examStage.status = 0;
        }
        return {
          tips,
          className,
          imgUrl,
          ...item.examStage,
          postId: item.postId,
          postName: item.postName,
        };
      });
    },
    getInterviewListHandle(interviewTrueList) {
      this.interviewList = interviewTrueList.map(item => {
        // 时间格式化
        const {
          interviewBeginTime,
          interviewEndTime,
          interviewAddr,
          interviewContent,
        } = item.interviewStage;
        let str = '';
        if (interviewBeginTime && interviewEndTime)
          str = interviewBeginTime + ' 至' + interviewEndTime.slice(10);

        item.interviewStage.time = str;
        item.interviewStage.addr = interviewAddr;
        item.interviewStage.content = interviewContent;

        // 根据状态设置对应的提示与文字颜色
        let tips = '';
        let className = '';
        let imgUrl = '';
        const { status, exemptionStatus } = item.interviewStage;
        if (status === 2 || exemptionStatus === 2) {
          tips = '恭喜您！通过面试';
          className = 'pass';
          imgUrl = require('@/assets/progress-success.png');
        } else if (status === 1) {
          tips = '很遗憾，您未能通过面试';
          className = 'fail';
          imgUrl = require('@/assets/progress-fail.png');
        } else {
          tips = '暂无面试成绩信息';
          className = 'pending';
          imgUrl = require('@/assets/warit-view-status.png');
          item.interviewStage.status = 0;
        }
        return {
          tips,
          className,
          imgUrl,
          ...item.interviewStage,
          postId: item.postId,
          postName: item.postName,
        };
      });
    },
    getManipulateTrueListHandle(manipulateTrueList) {
      this.semList = manipulateTrueList.map(item => {
        // 时间格式化
        const { examBeginTime, examEndTime, examAddr, examContent } =
          item.manipulate;
        let str = '';
        if (examBeginTime && examEndTime)
          str = examBeginTime + ' 至' + examEndTime.slice(10);

        item.manipulate.time = str;
        item.manipulate.addr = examAddr;
        item.manipulate.content = examContent;

        // 根据状态设置对应的提示与文字颜色
        let tips = '';
        let className = '';
        let imgUrl = '';
        const { status, exemptionStatus } = item.manipulate;
        if (status === 2 || exemptionStatus === 2) {
          tips = '恭喜您！通过实操考试';
          className = 'pass';
          imgUrl = require('@/assets/progress-success.png');
        } else if (status === 1) {
          tips = '很遗憾，您未能通过实操考试';
          className = 'fail';
          imgUrl = require('@/assets/progress-fail.png');
        } else {
          tips = '暂无实操考试成绩信息';
          className = 'pending';
          imgUrl = require('@/assets/warit-view-status.png');
          item.manipulate.status = 0;
        }
        return {
          tips,
          className,
          imgUrl,
          ...item.manipulate,
          postId: item.postId,
          postName: item.postName,
        };
      });
    },
    editSignUpHandle() {
      this.childFormReadonly = false;
    },
    async submitSignUpHandle() {
      const result = await this.$refs.componentName.formSubmitHandle(
        'ruleForm'
      );
      if (result) {
        this.childFormReadonly = true;
      }
    },
    printFormHandle() {
      this.$refs.componentName.print();
    },
  },
};
</script>

<style lang="scss" scoped>
#ProgressSearchBox {
  height: 100%;

  .plan-name {
    font-weight: 500;
    font-size: 16px;
  }

  .plan-time {
    font-size: 14px;
    margin: 8px 0;
    display: inline-block;
  }

  .info-progress-box {
    height: 100%;
  }

  .recruit-bottom {
    margin-bottom: 8px;
    display: flex;

    > div {
      display: flex;
      align-items: center;

      .recruit-progress {
        width: 106px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        background: #fafafa;
        border-radius: 4px;
        border: 1px solid #e7ebf0;
        cursor: pointer;
      }

      .recruit-progress.active {
        background: #6b95cb !important;
        color: #fff !important;
      }

      .recruit-progress.interview {
        background: #fff;
        border: 1px solid #6b95cb;
        color: #6b95cb;
      }

      img {
        width: 30px;
        height: 12px;
        margin: 0 16px;
      }
    }
  }

  .progress-content-box {
    width: 100%;
    height: calc(100% - 98px);
    position: relative;

    > div {
      width: 100%;
      height: 100%;
      overflow: auto;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

      .sign-up-tips {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .button-box {
          display: flex;

          button {
            cursor: pointer;
            border: none;
            background: #5260ff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 8px;
            margin-right: 8px;
          }
        }
      }

      .form-relative-box {
        position: relative;
        height: calc(100% - 30px);

        .post-from {
          padding: 8px 0 0;
        }
      }

      &.examination-status-box {
        display: flex;
        flex-direction: column;

        .examination-item {
          width: 544px;
          height: 125px;
          padding: 24px 25px 33px 25px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #cccccc;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .examination-left {
            display: flex;
            flex-direction: column;
            height: 100%;

            .examination-name {
              font-size: 18px;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              line-height: 26px;
            }

            .examination-tips {
              .font {
                font-size: 14px;
                font-weight: 500;
                line-height: 22px;
              }

              .pending {
                color: #5260ff;
                margin-top: 16px;
              }

              .pass {
                color: #c23a3a;
                margin-top: 16px;
              }

              .fail {
                color: #ec7b25;
              }
            }

            .fail-style {
              color: #666666;
            }
          }

          .examination-img {
            width: 120px;
            height: 120px;
            margin-top: 16px;
          }
        }
      }
    }
  }
}
</style>
