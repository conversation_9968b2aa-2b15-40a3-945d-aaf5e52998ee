<template>
  <div class="component-file">
    <span class="file-label">{{ label }}:</span>
    <div class="file-box" @click="handleClick">
      <img src="@/assets/upload.png" alt="" />
      <slot name="tips">添加附件</slot>
      <input
        type="file"
        ref="file"
        class="file-input"
        :accept="accept"
        :multiple="multiple"
        @change="handleFileChange"
      />
    </div>
    <slot name="PromptText"></slot>
  </div>
</template>

<script>
import { fileAttachmentOpenUpload } from '@/api/forFile';
import { isFunction } from '@/utils/utils';

export default {
  name: 'upload-file',
  props: {
    label: {
      type: String,
      default: () => '附件上传',
    },
    limit: {
      type: Number,
      default: 5,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: '',
    },
    onSuccess: Function,
  },
  data: () => ({
    fileList: [],
  }),
  methods: {
    handleClick() {
      this.$refs.file.click();
    },
    async handleFileChange(event) {
      const files = Array.from(event.target.files);

      if (files.length > this.limit) {
        this.$message({
          type: 'warning',
          center: true,
          message: `一次最多只能选择${this.limit}个文件`,
        });

        return;
      }

      if (!files.length) {
        return;
      }

      this.fileList = [];

      this.$emitter.emit('startLoading');

      for (let i = 0; i < files.length; i++) {
        const fileItem = files[i];
        try {
          let formData = new FormData();
          formData.append('file', fileItem);
          formData.append('moduleName', 'zp');
          const result = await fileAttachmentOpenUpload(formData);

          this.fileList.push(result.object);
        } catch (e) {
          this.$message({
            type: 'warning',
            center: true,
            message: e.message,
          });
          this.fileList.push('');
        }
      }

      this.$emitter.emit('endLoading');

      if (isFunction(this.onSuccess)) {
        this.onSuccess(this.fileList);
      }

      event.target.value = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.component-file {
  display: flex;
  align-items: center;

  .file-box {
    margin-left: 8px;
    width: 126px;
    height: 44px;
    box-sizing: border-box;
    background: #f4f4f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 11px;
    cursor: pointer;
    border-radius: 2px;

    .file-input {
      display: none;
    }
  }
}
</style>
