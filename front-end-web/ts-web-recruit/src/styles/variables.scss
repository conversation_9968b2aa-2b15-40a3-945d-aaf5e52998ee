// base color
// 蓝色
$primary-blue: #5260ff;
$list-hover-color: mix($primary-blue, #fff, 8%);
$primary-light-blue: #1677FF;
$primary-blue-active: mix($primary-blue, #000, 80%);
$light-blue: #3A71A8;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;
$success-color: #67C23A;
$error-color: #E24242;
$warning-color: #EC7B25;
$theme-border-color: #E7EBF0;
$theme-interval: 8px;
// 黑色
$primary-black: #333333;
$primary-gray: #666666;
$primary-light-black: #999999;
$primary-light-gray: #F4F4F4;
$toast-text-color: #999999;
$disabled-color: #CCCCCC;
$primary-bg: #f4f4f4;
$container-bg: #fafafa;
$container-border: #E7EBF0;
// 红色
$primary-red: #E24242;
// 白色
$primary-white: #FFFFFF;

//间距
$primary-spacing: 8px;
$medium-spacing: 16px;
$card-spacing: 24px;

//线
$split-line-color: #e4e4e4;

//圆角
$medium-radius: 4px;
$small-radius: 2px;

// 字体
$xl-title-size: 40px;
$lg-title-size: 32px;
$md-title-size: 18px;
$primary-title-size: 14px;
$md-text-size: 24px;
$seconde-title-text-size: 16px;
$primary-text-size: 14px;
$small-text-size: 12px;


// sidebar
$ts-menu-text: $primary-black;
$ts-menu-active-text: $primary-blue;
$ts-sub-menu-active-text: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$ts-menu-bg: $primary-white;
$ts-menu-hover: mix($primary-blue, #fff, 8%);
$ts-sub-menu-bg: #FFFFFF;
$ts-sub-menu-hover: mix($primary-blue, #fff, 8%);

$ts-side-bar-width: 160px;
$ts-side-bar-item-height: 40px;


// layout
$ts-layout-main-bg: #ffffff;

//步骤条
$ts-step-title-spacing: 12px;
$ts-step-icon-size: 32px;

// 进度条
$ts-progress-tip-color: #999999;
$ts-progress-bg: #f5f5f5;
$ts-progress-default-color: $primary-light-blue;
$ts-progress-warning-color: #e6a23c;
$ts-progress-exception-color: #FF3B30;
$ts-progress-tip-size: 14px;

// 标签页
$ts-tabs-item-width: 100px;
$ts-tabs-item-height: 32px;
$ts-tabs-item-card-bg: #E8ECF2;
$ts-tabs-split-color: #D4D8E8;

// 下载
$ts-upload-text-color: rgba(0, 0, 0, 0.65);


// table
$ts-table-header-bg: #D2DEF0;
$ts-table-header-hover-bg: #dee9f8;
$ts-table-sort-bg: #bdd2f3;
$ts-table-sort-seleced-bg: #6583AF;
$ts-table-text-color: rgba(51, 51, 51, 0.7);


// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  primaryBlue: $primary-blue;
  ts-menu-text: $ts-menu-text;
  ts-menu-active-text: $ts-menu-active-text;
  ts-sub-menu-active-text: $ts-sub-menu-active-text;
  ts-menu-bg: $ts-menu-bg;
  ts-menu-hover: $ts-menu-hover;
  ts-sub-menu-bg: $ts-sub-menu-bg;
  ts-sub-menu-hover: $ts-sub-menu-hover;
  ts-side-bar-width: $ts-side-bar-width;
  ts-side-bar-item-height: $ts-side-bar-item-height;
  ts-progress-default-color: $ts-progress-default-color;
  ts-progress-exception-color: $ts-progress-exception-color;
  ts-progress-warning-color: $ts-progress-warning-color;
  ts-primary-font-size: $primary-text-size;
  ts-primary-spacing: $primary-spacing;
}
