<template>
  <div class="otherS-states">
    <div class="search-box mb-8">
      <div class="ts-form-item">
        <!-- <span class="label">主题</span> -->
        <div class="value" style="width:150px">
          <el-input
            v-model="queryParam.motif"
            placeholder="请输入主题"
          ></el-input>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">会议室</span>
        <div class="value" style="width:140px">
          <el-select
            v-model="queryParam.boardroomId"
            placeholder="请选择"
            filterable
          >
            <el-option
              v-for="item in navList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">会议类型</span>
        <div class="value" style="width:140px">
          <el-select
            v-model="queryParam.appTypeId"
            placeholder="请选择"
            filterable
          >
            <el-option
              v-for="item in TypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.itemNameValue"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">会议时间</span>
        <div class="value">
          <base-date-range-picker
            v-model="datePicker"
            :placeholder="['开始日期', '结束日期']"
            type="datetimerange"
          ></base-date-range-picker>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">参与人/预约人</span>
        <div class="value" style="width:140px">
          <ts-emp-pagination-select
            v-model="queryParam.empList"
            placeholder="请选择人员"
            :multiple="true"
          >
          </ts-emp-pagination-select>
        </div>
      </div>
      <el-button @click="handleSearch" class="ts-button primary ml-8">
        搜索
      </el-button>
      <img
        @click="reset"
        class="icon_reset ml-8"
        src="@/assets/img/meeting/reset.svg"
      />
      <el-button
        @click="handleExport"
        class="ts-button primary ml-8 shallowButton"
      >
        导出
      </el-button>
    </div>
    <div class="otherS-states-list">
      <el-table :data="dataSource" border height="100%" style="width: 100%;">
        <el-table-column
          prop="motif"
          label="主题"
          :show-overflow-tooltip="true"
          min-width="350"
        >
          <template slot-scope="scope">
            <span
              class="showBlue"
              @click="handleCommand('details', scope.row)"
              >{{ scope.row.motif }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="地点"
          :show-overflow-tooltip="true"
          min-width="200"
        >
          <template slot-scope="scope">
            {{ getAddressName(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column
          label="会议时间"
          prop="motif"
          :show-overflow-tooltip="true"
          width="200"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.startTime).format('YYYY-MM-DD HH:mm') }}
            -
            {{ moment(scope.row.endTime).format('HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column
          label="会议类型"
          :show-overflow-tooltip="true"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.appTypeLable }}
          </template>
        </el-table-column>
        <el-table-column
          label="预约人"
          :show-overflow-tooltip="true"
          min-width="200"
        >
          <template slot-scope="scope">
            {{ applyEmployeeStr(scope.row.applyEmployee) }}
          </template>
        </el-table-column>
        <!-- 会议状态 -->
        <el-table-column
          prop="meetingStatusLable"
          label="会议状态"
          align="center"
          width="80"
        ></el-table-column>
        <!-- 已签到 -->
        <el-table-column
          prop="boardroomSigninCount.signInNum"
          label="已签到"
          align="center"
          width="65"
        >
          <template slot-scope="scope">
            <span class="showBlue" @click="handleViewSignIn(scope.row, 1)">{{
              scope.row.boardroomSigninCount.signInNum
            }}</span>
          </template>
        </el-table-column>
        <!-- 未签到 -->
        <el-table-column
          prop="boardroomSigninCount.noSignInNum"
          label="未签到"
          align="center"
          width="65"
        >
          <template slot-scope="scope">
            <span class="showBlue" @click="handleViewSignIn(scope.row, 0)">{{
              scope.row.boardroomSigninCount.noSignInNum
            }}</span>
          </template>
        </el-table-column>
        <!-- 请假 -->
        <el-table-column
          prop="boardroomSigninCount.leaveNum"
          label="请假"
          align="center"
          width="65"
        >
          <template slot-scope="scope">
            <span class="showBlue" @click="handleViewSignIn(scope.row, 2)">{{
              scope.row.boardroomSigninCount.leaveNum
            }}</span>
          </template>
        </el-table-column>
        <!-- 签退 -->
        <el-table-column
          prop="boardroomSigninCount.signOut"
          label="签退"
          align="center"
          width="65"
        >
          <template slot-scope="scope">
            <span class="showBlue" @click="handleViewSignIn(scope.row, 4)">{{
              scope.row.boardroomSigninCount.signOut
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="出席率" align="center" width="65">
          <template slot-scope="scope">
            {{ tendanceRate(scope.row) }}
          </template>
        </el-table-column>
        <!-- 会议纪要 -->
        <el-table-column label="会议纪要" align="center" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.summarySize ? '已上传' : '暂未上传' }}</span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column align="center" label="操作" width="300" fixed="right">
          <template slot-scope="scope">
            <!-- <el-popover
              ref="popover"
              placement="bottom-end"
              trigger="hover"
              :close-delay="0"
              popper-class="base-table-action-cell-popover"
              :popper-options="{
                boundariesElement: 'viewport',
                removeOnDestroy: true
              }"
            >
              <span slot="reference">
                <i class="more-action-icon el-icon-more"></i>
              </span>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus === 0 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('edit', scope.row)"
              >
                编辑
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus === 0 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('edit', scope.row)"
              >
                编辑
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  (scope.row.meetingStatus === 1 ||
                    scope.row.meetingStatus === 0) &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('qrCodeSignIn', scope.row)"
              >
                签到二维码
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus === 1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1) &&
                    scope.row.signOutType === 1
                "
                @click="handleCommand('qrCodeSignOut', scope.row)"
              >
                签退二维码
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus === -1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('exportSign', scope.row)"
              >
                导出签到记录
              </div>
              <el-upload
                v-if="
                  (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                    scope.row.currentEmpPower.indexOf('1') !== -1) &&
                    (scope.row.meetingStatus === 1 ||
                      scope.row.meetingStatus === 0 ||
                      scope.row.meetingStatus === -1)
                "
                class="hidden-action-item"
                :action="importExcelUrl(scope.row)"
                multiple
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
              >
                <a style="color: #0f40f5;">上传资料</a>
              </el-upload>
              <div
                class="hidden-action-item"
                v-if="
                  (scope.row.meetingStatus === 1 ||
                    scope.row.meetingStatus === -1) &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('minutesMeetingDetails', scope.row)"
              >
                会议纪要
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  (scope.row.meetingStatus === 0 ||
                    scope.row.meetingStatus === 1) &&
                    scope.row.currentEmpPower.indexOf('3') != -1
                "
                @click="handleCommand('leave', scope.row)"
              >
                请假
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus === 1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('finishMeeting', scope.row)"
              >
                结束会议
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.meetingStatus == 0 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('cancel', scope.row)"
              >
                取消会议
              </div>
              <div
                class="hidden-action-item"
                v-if="
                  scope.row.currentEmpPower.indexOf('2') !== -1 &&
                    scope.row.meetingStatus === 4
                "
                @click="handleCommand('reschedule', scope.row)"
              >
                重新预定
              </div>
              <div
                class="hidden-action-item"
                @click="handleCommand('print', scope.row)"
              >
                预览打印
              </div>
            </el-popover> -->
            <div class="all-Butoon">
              <div
                v-if="
                  scope.row.meetingStatus === 0 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('edit', scope.row)"
              >
                编辑
              </div>
              <div
                v-if="
                  (scope.row.meetingStatus === 1 ||
                    scope.row.meetingStatus === 0) &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('qrCodeSignIn', scope.row)"
              >
                签到二维码
              </div>
              <div
                v-if="
                  scope.row.meetingStatus === 1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1) &&
                    scope.row.signOutType === 1
                "
                @click="handleCommand('qrCodeSignOut', scope.row)"
              >
                签退二维码
              </div>
              <div
                v-if="
                  scope.row.meetingStatus === -1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('exportSign', scope.row)"
              >
                导出签到记录
              </div>
              <el-upload
                v-if="
                  (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                    scope.row.currentEmpPower.indexOf('1') !== -1) &&
                    (scope.row.meetingStatus === 1 ||
                      scope.row.meetingStatus === 0 ||
                      scope.row.meetingStatus === -1)
                "
                class="upload"
                :action="importExcelUrl(scope.row)"
                multiple
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
              >
                <a style="color: #0f40f5;">上传资料</a>
              </el-upload>
              <div
                v-if="
                  (scope.row.meetingStatus === 1 ||
                    scope.row.meetingStatus === -1) &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('minutesMeetingDetails', scope.row)"
              >
                会议纪要
              </div>
              <div
                v-if="
                  (scope.row.meetingStatus === 0 ||
                    scope.row.meetingStatus === 1) &&
                    scope.row.currentEmpPower.indexOf('3') != -1
                "
                @click="handleCommand('leave', scope.row)"
              >
                请假
              </div>
              <div
                v-if="
                  scope.row.meetingStatus === 1 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('finishMeeting', scope.row)"
              >
                结束会议
              </div>
              <div
                v-if="
                  scope.row.meetingStatus == 0 &&
                    (scope.row.currentEmpPower.indexOf('2') !== -1 ||
                      scope.row.currentEmpPower.indexOf('1') !== -1)
                "
                @click="handleCommand('cancel', scope.row)"
              >
                取消会议
              </div>
              <div
                v-if="
                  scope.row.currentEmpPower.indexOf('2') !== -1 &&
                    scope.row.meetingStatus === 4
                "
                @click="handleCommand('reschedule', scope.row)"
              >
                重新预定
              </div>
              <div @click="handleCommand('print', scope.row)">
                预览打印
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 预约详情页 -->
    <details-modal ref="detailsModal" @ok="loadData"></details-modal>
    <!-- 会议详情 -->
    <meeting-details-modal ref="meetingDetailsModal"></meeting-details-modal>
    <!-- 查看流程 -->
    <process-modal ref="processModal"></process-modal>
    <!-- 二维码 -->
    <qr-code-modal ref="qrCodeModal" />
    <!-- 会议纪要 -->
    <minutes-meeting-details
      ref="minutesMeetingDetails"
      @ok="minutesMeetingDetailsOk"
    ></minutes-meeting-details>
    <!-- 签到详情 -->
    <sign-in-list-modal ref="signInListModal"></sign-in-list-modal>
    <preview-print ref="previewPrint"></preview-print>
  </div>
</template>
<script>
import DetailsModal from '@/views/meetingManagement/usageSituation/details/index';
import ProcessModal from '@/views/meetingManagement/bookingApproval/processModal.vue';
import MeetingDetailsModal from '../details.vue';
import QrCodeModal from './QrCodeModal.vue';
import MinutesMeetingDetails from '@/views/meetingManagement/minutesMeeting/details.vue';
import SignInListModal from './signInListModal.vue';
import previewPrint from './previewPrint.vue';
import moment from 'moment';
import { commonUtils } from '@/utils/index.js';
export default {
  components: {
    DetailsModal,
    MeetingDetailsModal,
    ProcessModal,
    QrCodeModal,
    MinutesMeetingDetails,
    SignInListModal,
    previewPrint
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryParam: {
        motif: '',
        empList: [],
        startTime: '',
        endTime: '',
        meetingStatus: ''
      },
      datePicker: '',
      meetingStatus: [
        {
          value: '0',
          label: '未开始'
        },
        {
          value: 1,
          label: '进行中'
        },
        {
          value: -1,
          label: '已结束'
        },
        {
          value: 4,
          label: '取消'
        }
      ],
      navList: [],
      TypeList: []
    };
  },
  created() {
    this.getNavList();
    this.getTypeList();
  },
  methods: {
    moment,
    applyEmployeeStr: commonUtils.applyEmployeeStr,
    loadData() {
      this.$parent.loadData();
    },
    importExcelUrl(row) {
      return `${location.origin}/ts-basics-bottom/fileAttachment/upload?businessId=${row.accessoryId}&moduleName=meeting`;
    },
    tendanceRate(row) {
      let num =
        (row.boardroomSigninCount.signInNum /
          (row.boardroomSigninCount.signInNum +
            row.boardroomSigninCount.noSignInNum)) *
        100;
      return num.toFixed(2) + '%';
    },
    // 文件上传成功回调
    handleAvatarSuccess(response, file, fileList) {
      if (response.success) {
        this.$message({
          showClose: true,
          message: '上传成功',
          type: 'success'
        });
        this.loadData();
      } else {
        this.$message({
          showClose: true,
          message: response.message,
          type: 'error'
        });
      }
    },
    // 搜索
    handleSearch() {
      this.$parent.loadData(1);
    },
    // 搜索
    handleExport() {
      this.$parent.handleExport();
    },
    // 查看签到详情
    handleViewSignIn(row, status) {
      this.$refs.signInListModal.open(row, status);
    },
    // 重置
    reset() {
      this.datePicker = '';
      this.queryParam = {
        motif: '',
        empList: [],
        startTime: '',
        endTime: ''
      };
      this.$parent.loadData(1);
    },
    // 获取位置名称
    getAddressName(row) {
      return `${row.location ? row.location + '-' : ''}${
        row.floor ? row.floor + '层' : ''
      }${row.boardroomName ? '-' + row.boardroomName : ''}`;
    },
    async getNavList() {
      try {
        let boardRoomList = await this.ajax.getBoardRoomList();
        boardRoomList = boardRoomList.object || [];
        boardRoomList = boardRoomList.map(e => {
          return { id: e.id, label: e.name, isVideoLable: e.isVideoLable };
        });
        this.navList = this.navList.concat(boardRoomList);
      } catch (error) {}
    },
    async getTypeList() {
      const meetingRoomTypes = await this.ajax.getDictDatas({
        typeCode: 'MEETING_TYPE'
      });
      this.TypeList = meetingRoomTypes.object || [];
    },
    // 操作
    async handleCommand(command, row) {
      let modalData;
      switch (command) {
        // 详情
        case 'details':
          this.$refs.meetingDetailsModal.open(row);
          break;
        // 编辑
        case 'edit':
          try {
            const meetingRoomDetails = await this.ajax.getBoardRoom({
              id: row.boardroomId
            });
            const bookingDetails = await this.ajax.getBoardRoomApply({
              id: row.applyId
            });
            let dateTime = moment(row.startTime).format('YYYY-MM-DD');
            const timeLineData = await this.ajax.getBoardRoomApplyTimeDetail({
              applyId: row.applyId,
              boardroomId: row.boardroomId,
              startTime: dateTime + ' 00:00:00',
              endTime: dateTime + ' 23:59:59'
            });
            this.$refs.detailsModal.edit(
              meetingRoomDetails.object,
              bookingDetails.object,
              {
                timeLineData: timeLineData.object
              }
            );
          } catch (error) {}
          break;
        case 'bookingDetails':
          try {
            const boardRoomApply = await this.ajax.getBoardRoomApply({
              id: row.applyId
            });
            let data = boardRoomApply.object;
            data.motifType = data.motifType == '1';
            if (!data.motifType) {
              data.motif = data.motif
                .split('')
                .map(e => {
                  return '*';
                })
                .join('');
            }
            data.times = [data.startTime, data.endTime];
            const boardRoom = await this.ajax.getBoardRoom({
              id: row.boardroomId
            });
            this.$refs.processModal.open(row, boardRoom.object, data, false);
          } catch (error) {}
          break;
        case 'reschedule':
          try {
            const meetingRoomDetails = await this.ajax.getBoardRoom({
              id: row.boardroomId
            });
            let bookingDetails = await this.ajax.getBoardRoomApply({
              id: row.applyId
            });
            let business = await this.ajax.copyBusinessIdFiles({
              businessId: bookingDetails.object.businessId
            });
            bookingDetails.accessoryId =
              business.object && business.object.length > 0
                ? business.object[0].businessId
                : '';
            if (business.object && business.object.length > 0) {
              bookingDetails.accessoryId = business.object[0].businessId;
            } else {
              delete bookingDetails.accessoryId;
            }
            let dateTime = moment().format('YYYY-MM-DD');
            const timeLineData = await this.ajax.getBoardRoomApplyTimeDetail({
              applyId: row.applyId,
              boardroomId: row.boardroomId,
              startTime: dateTime + ' 00:00:00',
              endTime: dateTime + ' 23:59:59'
            });
            this.$refs.detailsModal.edit(
              meetingRoomDetails.object,
              bookingDetails.object,
              {
                isReschedule: command === 'reschedule',
                readOnly:
                  row.currentEmpPower && row.currentEmpPower.indexOf('2') !== -1
                    ? false
                    : true,
                timeLineData: timeLineData.object
              }
            );
          } catch (error) {
            throw error;
          }

          break;
        //   取消
        case 'cancel':
          modalData = {
            title: '取消会议',
            content: '确定取消这条会议室预约吗？',
            placeholder: '请输入取消原因',
            type: command,
            meetingId: row.meetingId
          };
          this.$parent.openModal(modalData);
          break;
        //   请假
        case 'leave':
          modalData = {
            title: '请假',
            placeholder: '请输入请假原因',
            type: command,
            meetingId: row.meetingId
          };
          this.$parent.openModal(modalData);
          break;
        //   签到二维码
        case 'qrCodeSignIn':
          let obj = {
            type: 'signIn',
            autoRefresh: row.autoRefreshSignInQrCodeType
          };
          this.$refs.qrCodeModal.open(
            row.signInQrCode,
            row.meetingId,
            obj,
            row.autoRefreshSignInQrCodeDate,
            '会议签到二维码',
            row.motif
          );
          break;
        //   签退二维码
        case 'qrCodeSignOut':
          let objs = {
            type: 'signOut',
            autoRefresh: row.autoRefreshSignInQrCodeType
          };
          this.$refs.qrCodeModal.open(
            row.signOutQrCode,
            row.meetingId,
            objs,
            row.autoRefreshSignInQrCodeDate,
            '会议签退二维码',
            row.motif
          );
          break;
        //   结束会议
        case 'finishMeeting':
          let modalData = {
            title: '结束会议',
            content:
              '您预约的会议要求签退，如果现在结束会议，则无法签退，是否结束会议？',
            type: 'finish',
            meetingId: row.meetingId,
            isInput: false
          };
          this.$parent.openModal(modalData);
          break;
        //   会议纪要
        case 'minutesMeetingDetails':
          this.$refs.minutesMeetingDetails.open(row);
          break;
        // 导出签到记录
        case 'exportSign':
          this.ajax
            .customDownloadFile(
              `/ts-oa/boardRoomSignIn/export?meetingId=${row.meetingId}&signinStatus=`,
              `${row.motif}签到表.xlsx`
            )
            .then(data => {});
          break;
        // 打印
        case 'print':
          this.$refs.previewPrint.open({ data: row });
          break;
        default:
          break;
      }
    },
    minutesMeetingDetailsOk() {
      this.$parent.loadData();
    }
  },

  watch: {
    datePicker: {
      handler(val) {
        if (!val) return;
        this.queryParam.startTime = val[0];
        this.queryParam.endTime = val[1];
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.otherS-states {
  height: 100%;
  .search-box {
    display: flex;
    align-items: center;
    .icon_reset {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
  .otherS-states-list {
    width: 100%;
    height: calc(100% - 38px);
    .rotate-90 {
      transform: rotate(90deg);
      color: #333333;
    }
    .bg-color {
      background-color: transparent !important;
    }
  }
}
.all-Butoon {
  display: flex;
  color: #0f40f5;
  div {
    padding: 0 3px;
    cursor: pointer;
  }
}
.more-action-icon {
  cursor: pointer;
  transform: rotate(90deg);

  &:hover {
    color: $primary-blue;
  }
}
.showBlue {
  color: #0f40f5;
  cursor: pointer;
}
// 设置滚动条的宽度
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 设置滚动条的背景色和圆角
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: rgba(153, 153, 153, 0.4);
  &:hover {
    background: rgba(153, 153, 153, 0.8);
  }
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  background: #fff;
}
</style>
<style lang="scss">
.base-table-action-cell-popover {
  margin-top: 2px !important;
  padding: 0 !important;
  min-width: unset;

  .popper__arrow {
    display: none;
  }

  .hidden-action-item {
    display: flex;
    align-items: center;
    padding-left: $primary-spacing;
    padding-right: 20px;
    height: 27px;
    cursor: pointer;
    color: #333;
    &:hover {
      background-color: mix(#fff, $primary-blue, 92%);
      color: $primary-blue;
    }
    a {
      color: #333 !important;
    }
  }

  .action-icon {
    height: 27px;
    width: 27px;
    line-height: 27px;
    text-align: center;
    display: inline-block;
    margin-right: 5px;

    i {
      font-size: 16px;
    }
  }
}
</style>
