<template>
  <el-dialog
    custom-class="booking-info-modal"
    title="预约信息"
    :visible.sync="visible"
    width="640px"
    :show-close="false"
  >
    <el-form :label-width="formLabelWidth">
      <el-form-item label="主题">
        <span class="value">{{ isHidden }}</span>
      </el-form-item>
      <el-form-item label="预约时段">
        <span class="value">{{
          moment(form.startTime).format('YYYY-MM-DD') +
            ' ' +
            moment(form.startTime).format('HH:mm') +
            '-' +
            moment(form.endTime).format('HH:mm')
        }}</span>
      </el-form-item>
      <el-form-item label="参与人数">
        <span class="value">{{ form.controlNumber }}人</span>
      </el-form-item>
      <el-form-item label="预约状态">
        <span class="value">{{ form.statusLable }}</span>
      </el-form-item>
      <el-form-item label="预约人">
        <span class="value">{{ applyEmployeeStr(form.applyEmployee) }}</span>
      </el-form-item>
      <el-form-item label="备注">
        <span class="value">{{ form.remark }}</span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <span
        @click="handleDetails"
        class="ts-button primary"
        v-if="form.applyEmployee.employeeNo === usercode"
      >
        查看详情
      </span>
      <span class="ts-button" @click="handleCancel"> 关 闭 </span>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment';
import { commonUtils } from '@/utils/index.js';
export default {
  data() {
    return {
      visible: false,
      formLabelWidth: '120px',
      form: {
        motif: '',
        startTime: '',
        endTime: '',
        controlNumber: '',
        status: '',
        statusLable: '',
        applyEmp: '',
        applyEmployee: {
          orgName: '',
          employeeNo: '',
          employeeName: '',
          phoneNumber: ''
        },
        motifType: false
      },
      usercode: ''
    };
  },
  methods: {
    moment,
    applyEmployeeStr: commonUtils.applyEmployeeStr,
    open(row) {
      this.usercode = this.$getCookiesInfo('usercode');
      this.form = Object.assign(this.form, row);
      this.form.startTime = moment(this.form.startTime).format(
        'YYYY-MM-DD HH:mm'
      );
      this.form.endTime = moment(this.form.endTime).format('YYYY-MM-DD HH:mm');
      this.form.motifType = this.form.motifType == '1';
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
      this.form = this.$options.data().form;
      this.usercode = '';
    },
    handleDetails() {
      let params = JSON.parse(JSON.stringify(this.form));
      params.startTime = moment(params.startTime).format('YYYY-MM-DD HH:mm:ss');
      params.endTime = moment(params.endTime).format('YYYY-MM-DD HH:mm:ss');
      this.$parent.chilrenOpenDetails(params);
      this.handleCancel();
    }
  },

  computed: {
    isHidden() {
      const isVisble =
        this.form.applyEmployee.employeeNo === this.usercode ||
        this.form.attendEmployeeList.some(e => {
          return e.usercode == this.usercode;
        });

      return this.form.motifType == '1' || isVisble
        ? this.form.motif
        : this.form.motif
            .split('')
            .map(e => {
              return '*';
            })
            .join('');
    }
  }
};
</script>
<style lang="scss" scoped>
.booking-info-modal {
  .dialog-footer {
    .primary {
      margin-left: 8px;
    }
  }
  .value {
    display: inline-block;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }
}
/deep/.booking-info-modal {
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
  .el-dialog__body {
    padding: 24px 16px 40px 16px;
  }
  .el-dialog__footer {
    border-top: 1px solid #e4e4e4;
  }
}
</style>
