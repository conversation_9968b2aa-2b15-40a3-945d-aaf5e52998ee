<template>
  <el-dialog
    custom-class="agree-modal"
    :append-to-body="true"
    title="流程审批"
    :visible.sync="dialogVisible"
    width="640px"
    top="0"
    @close="handleCancel"
  >
    <el-form
      :label-width="formLabelWidth"
      :model="form"
      ref="form"
      :rules="rules"
    >
      <!-- 办理节点 -->
      <el-form-item label="办理节点" prop="wfStepId">
        <el-select
          style="width:100%"
          v-model="form.wfStepId"
          placeholder="请选择办理节点"
        >
          <el-option
            v-for="(item, idx) in nextWfStepList"
            :key="idx"
            :label="item.wfStepName"
            :value="item.wfStepId"
          ></el-option>
        </el-select>
      </el-form-item>
      <div v-if="wfStepName !== '结束'">
        <!-- 办理人 -->
        <el-form-item label="办理人" prop="users">
          <el-select
            style="width:100%"
            multiple
            v-model="form.users"
            placeholder="请选择办理人"
          >
            <el-option
              v-for="(item, idx) in userList"
              :key="idx"
              :label="item.username"
              :value="item.usercode"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 紧急程度 -->
        <!-- <el-form-item label="紧急程度">
          <el-select
            style="width:100%"
            v-model="form.urgencyLevel"
            placeholder="请选择紧急程度"
          >
            <el-option
              v-for="(item, idx) in urgencyLevelList"
              :key="idx"
              :label="item.label"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <!-- 办理期限 -->
        <!-- <el-form-item label="办理期限">
          <el-date-picker
            style="width:100%"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="form.handleAllottedTime"
            placeholder="请选择办理期限"
            type="date"
          >
          </el-date-picker>
        </el-form-item> -->
        <!-- 办理提示 -->
        <!-- <el-form-item label="办理提示">
          <el-input
            style="width:100%"
            v-model="form.handleMarkedWords"
            placeholder="请输入办理提示"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
          >
          </el-input>
        </el-form-item> -->
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <span class="ts-button primary" @click="handleSave"> 保 存 </span>
      <span class="ts-button" @click="handleCancel"> 取 消 </span>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      wfStepName: '',
      dialogVisible: false,
      nextWfStepList: [],
      formLabelWidth: '82px',
      form: {
        taskId: '',
        wfStepId: '',
        urgencyLevel: '',
        handleAllottedTime: '',
        handleMarkedWords: '',
        // L_TaskRemark:'',
        users: [],
        names: []
      },
      approvalData: {},
      rules: {
        wfStepId: [
          {
            required: true,
            message: '请选择办理节点',
            trigger: 'blur'
          }
        ],
        users: [
          {
            required: true,
            message: '请选择办理人',
            trigger: 'blur'
          }
        ]
      },
      urgencyLevelList: [
        {
          id: '1',
          label: '一般'
        },
        {
          id: '2',
          label: '加急'
        },
        {
          id: '3',
          label: '急件'
        },
        {
          id: '4',
          label: '特急'
        }
      ]
    };
  },
  methods: {
    async open(row) {
      try {
        this.approvalData = row;
        const res = await this.ajax.getNextWfStepListByTaskId({
          taskId: row.taskId
        });
        this.form.taskId = row.taskId;
        this.nextWfStepList = res.object || [];
        this.nextWfStepList = this.nextWfStepList.map(e => {
          return { ...e, userList: e.userList || [] };
        });
        this.dialogVisible = true;
        this.form.wfStepId = this.nextWfStepList[0].wfStepId;
        this.wfStepName = this.nextWfStepList[0].wfStepName;
      } catch (error) {}
    },
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
      this.form = this.$options.data().form;
    },
    handleSave() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            if (this.wfStepName === '结束') {
              await this.ajax.passBoardRoomApply({
                businessId: this.approvalData.businessId
              });
            } else {
              let params = JSON.parse(JSON.stringify(this.form));
              params.users.forEach(e => {
                if (!params.names) params.names = [];
                params.names.push(
                  this.userList.find(i => {
                    return i.usercode == e;
                  }).username
                );
              });
              params.names = params.names.join(',');
              params.users = params.users.join(',');
              await this.ajax.completeTaskByWfStepId(params);
            }

            this.$emit('ok');
            this.handleCancel();
          } catch (error) {}
        } else {
          return false;
        }
      });
    }
  },
  computed: {
    userList() {
      const idx = this.nextWfStepList.findIndex(e => {
        return e.wfStepId == this.form.wfStepId;
      });
      return idx != -1 ? this.nextWfStepList[idx].userList : [];
    }
  },
  watch: {
    userList: {
      handler(val) {
        if (val.length > 0) {
          this.form.users = [val[0].usercode];
        }
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/.agree-modal {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
}
</style>
<style></style>
