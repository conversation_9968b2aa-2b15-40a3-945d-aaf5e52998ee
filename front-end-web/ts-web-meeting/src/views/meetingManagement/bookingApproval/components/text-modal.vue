<template>
  <el-dialog
    :title="conf.title"
    :visible.sync="visible"
    width="400px"
    :append-to-body="true"
    @close="cancelDialogForm"
  >
    <el-form :model="form" ref="remarkForm">
      <el-form-item
        prop="remark"
        :rules="[
          {
            required: conf.meta.required,
            message: '请输入' + conf.meta.label,
            trigger: 'blur'
          }
        ]"
        :label="conf.meta.label"
      >
        <el-input
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
          v-model="form.remark"
          :placeholder="'请输入' + conf.meta.label"
          autocomplete="off"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <button class="ts-button primary" @click="okDialogForm">
        确 定
      </button>
      <button class="ts-button" @click="cancelDialogForm">
        取 消
      </button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      form: { remark: '' },
      conf: {
        title: '',
        meta: {
          label: '',
          required: true
        },
        ok: text => {}
      }
    };
  },
  methods: {
    open(conf, text) {
      this.conf = Object.assign(this.conf, conf);

      this.form.remark = text;
      this.visible = true;
    },
    cancelDialogForm() {
      this.visible = false;
      this.$refs.remarkForm.resetFields();
    },
    okDialogForm() {
      this.$refs.remarkForm.validate(async valid => {
        if (valid) {
          try {
            this.conf.ok(this.form.remark);
            this.cancelDialogForm();
          } catch (error) {
          } finally {
          }
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style></style>
