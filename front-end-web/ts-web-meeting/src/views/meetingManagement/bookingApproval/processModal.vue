<template>
  <el-dialog
    custom-class="process-modal"
    :fullscreen="true"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @close="handleCancel"
  >
    <template #title>
      <div class="process-modal-header">
        <div class="avatar mr-8">流程<br />在办</div>
        <div class="process-modal-header-content">
          <div class="title">{{ approvalData.motif }}</div>
          <div class="desc">
            <span>流程编号：{{ approvalData.workflowNumber }}</span>
            <span>发起人：{{ approvalData.applyEmpName }}</span>
            <span>当前节点：{{ approvalData.stepName }}</span>
          </div>
        </div>
      </div>
    </template>
    <div class="process-modal-container">
      <el-tabs
        class="process-modal-tabs"
        tab-position="left"
        v-model="active"
        type="card"
      >
        <!-- 预约信息 -->
        <el-tab-pane label="预约信息" name="1">
          <!-- <bookingInfo
            :approvalData="approvalData"
            :boardRoom="boardRoom"
            :boardRoomApply="boardRoomApply"
            ref="bookingInfo"
          ></bookingInfo> -->
          <booking-detail ref="bookingDetail" />
        </el-tab-pane>
        <!-- 流程信息 -->
        <el-tab-pane label="流程信息" name="2">
          <process-info
            ref="processInfo"
            :approvalData="approvalData"
          ></process-info>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <span v-if="isEdit" class="ts-button" @click="handleDisagree">
          不同意
        </span>
        <span v-if="isEdit" class="ts-button primary" @click="agree">
          同 意
        </span>
        <span class="ts-button" @click="handleCancel"> 关 闭 </span>
      </div>
    </template>
    <agree-modal ref="agreeModal" @ok="handleOk"></agree-modal>
    <text-modal ref="textModal"></text-modal>
  </el-dialog>
</template>

<script>
// import BookingInfo from './components/bookingInfo.vue';
import ProcessInfo from './components/processInfo.vue';
import AgreeModal from './components/agreeModal.vue';
import TextModal from './components/text-modal.vue';
import bookingDetail from './components/bookingDetail.vue';
export default {
  components: {
    // BookingInfo,
    AgreeModal,
    ProcessInfo,
    TextModal,
    bookingDetail
  },
  data() {
    return {
      visible: false,
      active: '1',
      approvalData: {
        motif: '', // 主题
        stepName: '',
        workflowNumber: '', // 流程编号
        applyId: ''
      },
      boardRoom: {},
      boardRoomApply: {},
      isEdit: false,
      dialogFormVisible: false
    };
  },
  methods: {
    /**@desc row 审批详情; boardRoom 会议室详情;  boardRoomApply 会议室预定详情 */
    open(row, boardRoom, boardRoomApply, isEdit) {
      this.approvalData = Object.assign({}, this.approvalData, row);
      // 兼容，取预定详情里的wfInstanceId字段
      this.approvalData.wfInstanceId =
        this.approvalData.wfInstanceId || boardRoomApply.wfInstanceId;
      this.boardRoom = Object.assign(this.boardRoom, boardRoom);
      this.boardRoomApply = Object.assign(this.boardRoomApply, boardRoomApply);
      this.isEdit = isEdit;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.bookingDetail.edit(
          boardRoom,
          boardRoomApply,
          this.approvalData
        );
      });
    },

    handleOk() {
      this.handleCancel();
      this.$emit('ok');
    },

    handleCancel() {
      this.visible = false;
      this.active = '1';
      this.approvalData = {
        motif: '', // 主题
        stepName: '',
        workflowNumber: '', // 流程编号
        applyId: ''
      };
      this.boardRoom = {};
      this.boardRoomApply = {
        attendEmployeeList: []
      };
      this.isEdit = false;
    },
    // 不同意
    handleDisagree() {
      this.$refs.textModal.open({
        title: '审批意见',
        meta: {
          label: '不同意原因',
          required: true
        },
        ok: async text => {
          await this.ajax.failBoardRoomApply({
            remark: text,
            businessId: this.approvalData.businessId
          });
          this.handleCancel();
          this.$emit('ok');
        }
      });
    },
    // 同意
    agree() {
      // 兼容内控系统
      if (this.boardRoom.defaultProcessid === 'HYSSQ') {
        this.$refs.textModal.open({
          title: '审批意见',
          meta: {
            label: '备注',
            required: false
          },
          ok: async text => {
            try {
              await this.ajax.passBoardRoomApply({
                remark: text,
                businessId: this.approvalData.businessId
              });
              this.handleOk();
            } catch (error) {}
          }
        });
      } else {
        this.$refs.agreeModal.open(this.approvalData);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/.process-modal {
  & > .el-dialog__header {
    padding: 8px 16px 7px 16px !important;
  }
  & > .el-dialog__body {
    background-color: #f4f4f4;
    height: calc(100% - 100px);
    .process-modal-tabs.el-tabs.el-tabs--card.el-tabs--left {
      height: 100%;
      .el-tabs__header.is-left {
        margin-right: 18px;
        .el-tabs__nav {
          // height: 100%;
          .el-tabs__item {
            height: 36px;
            line-height: 32px;
            text-align: center;
            border: 2px solid #e8ecf2;
          }
          .el-tabs__item:hover {
            color: #5260ff;
          }
        }
      }
      .el-tabs__content {
        height: 100%;
        .el-tab-pane {
          height: 100%;
        }
      }
    }
  }
  & > .el-dialog__footer {
    padding: 5px 16px !important;
  }
}
.process-modal {
  .process-modal-header {
    display: flex;
    align-items: center;
    .avatar {
      width: 44px;
      height: 44px;
      background: #5260ff;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      line-height: 13px;
    }
    .process-modal-header-content {
      height: 44px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .title {
        font-size: 14px;
        color: #333333;
        line-height: 14px;
      }
      .desc {
        span {
          font-size: 12px;
          color: rgba(51, 51, 51, 0.7);
          line-height: 12px;
        }
        span + span {
          margin-left: 8px;
        }
      }
    }
  }
  .process-modal-container {
    height: 100%;
    width: 1405px;
    margin: 0 auto;
  }
}
</style>
