import axios from 'axios';
import store from '@/store';
import qs from 'qs';
import { Message } from '@trasen/trasen-element-ui/lib';
import Vue from 'vue';

const errorStatus = `【登录失败】用户系统正在升级，请稍等片刻`;

let options = {
  axios,
  store,
  qs,
  Message
};
let timeout = 0;
let headers = {};
let downLoadTimer = true;
timeout = options.timeout || 120 * 1000;
let maxtimer = 3000;
headers = Object.assign(
  {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'x-requested-with': 'XMLHttpRequest'
  },
  options.headers
);
const request = options.axios.create({ timeout, headers });
/**@desc 在请求前做的操作**/
request.interceptors.request.use(
  function(config) {
    let noCache = new Date().getTime();
    let data = config.data || {};
    config.headers['startTimer'] = `${noCache}`; //请求前记录时间
    config.headers['token'] = options.store.state.common.token;
    if (config.method === 'get' || config.method === 'GET') {
      let pms = '';
      Object.keys(data).forEach((v, i) => {
        if (pms === '') {
          pms = v + '=' + data[v];
        } else {
          pms += '&' + v + '=' + data[v];
        }
      });
      if (config.url.indexOf('?') === -1 && pms !== '') {
        config.url = config.url + '?' + pms;
      } else if (pms !== '') {
        config.url = config.url + '&' + pms;
      }
    } else if (
      config.headers['Content-Type'].indexOf(
        'application/x-www-form-urlencoded'
      ) > -1
    ) {
      config.data = options.qs.stringify(data);
    }

    // appVue.$root.$emit('changes');
    !config.isLoading &&
      Vue.prototype.$devopParentTypeFun &&
      Vue.prototype.$devopParentTypeFun({
        type: 'sendAjaxMessage',
        data: 'sendMessage'
      });
    return config;
  },
  function(error) {
    return Promise.reject(error);
  }
);
/**@desc 在请求后做的操作**/
request.interceptors.response.use(
  /**@desc 对响应数据做点什么**/
  response => {
    let endTimer = Date.parse(new Date());
    let endTotalTimer = endTimer - response.config.headers.startTimer;
    const data = response.data;
    if (Object.prototype.toString.call(data) === '[object Object]') {
      data.endTotalTimer = endTotalTimer;
    }
    if (data.statusCode == 21000) {
      options.store.dispatch('common/goToLogin');
    }
    !response.config.isLoading &&
      Vue.prototype.$devopParentTypeFun &&
      Vue.prototype.$devopParentTypeFun({
        type: 'sendAjaxMessage',
        data: 'getMessage'
      });
    if (data.success == false) {
      options.Message.error(data.message);
      return Promise.reject(data.message || 'error');
    } else {
      return data;
    }
  },
  /**@desc 对请求错误做些什么**/
  error => {
    !error.response.config.isLoading &&
      Vue.prototype.$devopParentTypeFun &&
      Vue.prototype.$devopParentTypeFun({
        type: 'sendAjaxMessage',
        data: 'getMessage'
      });
    let status = error.response.status;
    if (error.response) {
      if (error.response.status == 401) {
        options.store.dispatch('common/goToLogin');
      } else if (status == 500 || status == 502) {
        if (downLoadTimer) {
          downLoadTimer = false;
          options.Message.error(errorStatus);
          setTimeout(() => {
            downLoadTimer = true;
          }, maxtimer);
        }
      } else {
        try {
          if (downLoadTimer) {
            downLoadTimer = false;
            options.Message.error(error.response.data);
            setTimeout(() => {
              downLoadTimer = true;
            }, maxtimer);
          }
        } catch (error) {}
      }

      return Promise.reject(error.response.data);
    } else {
      Vue.prototype.$devopParentTypeFun &&
        Vue.prototype.$devopParentTypeFun({
          type: 'sendAjaxMessage',
          data: 'getMessage'
        });
      return error;
    }
  }
);
/**@desc 把请求对象**/
export const $api = request;
/**@desc get请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $get = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'get'];
  headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
  if (show == false) headers.messageErrorAlert = 1;
  let transformRequest = [data => options.qs.stringify(data)];
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers, transformRequest })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
/**@desc post  表单提交请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $post = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'post'];
  if (show == false) headers.messageErrorAlert = 1;
  let transformRequest = [data => qs.stringify(data)];
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers, transformRequest })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
/**@desc post json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $postJson = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'post'];
  if (show == false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/json';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
/**@desc put json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $put = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'put'];
  if (show == false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/json';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

/**@desc delete json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $delete = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'delete'];
  if (show == false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

/**@desc post json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $upload = function({
  url,
  data = {},
  onUploadProgress,
  show = false,
  requestMethod = 'post',
  headers = {}
}) {
  let [method] = [requestMethod];
  if (show == false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = headers['Content-Type'] || 'text/plain';

  return new Promise((resolve, reject) => {
    request({
      url,
      method,
      data,
      headers,
      onUploadProgress,
      responseType: 'arraybuffer'
    })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
