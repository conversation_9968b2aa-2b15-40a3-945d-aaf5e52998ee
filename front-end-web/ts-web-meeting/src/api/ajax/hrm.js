import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  // 获取科室树
  getTree() {
    return $api({
      url: `${service.tsBasics()}/organization/getTree`,
      method: 'post'
    });
  },

  getOrgGroupTree(params) {
    return $api({
      url: `${service.tsBasics()}/employee/orgGroup/getOrgGroupTree`,
      method: 'get',
      params: params
    });
  },
  getEmployeeList(params, data) {
    return $api({
      url: `${service.tsOa()}/employee/list`,
      method: 'post',
      isLoading: true,
      params: params,
      data: data
    });
  },
  getOrgGroupUser(params) {
    return $api({
      url: `${service.tsBasics()}/employee/orgGroup/getOrgGroupUser`,
      method: 'get',
      params: params
    });
  },
  getDictionaries(type) {
    return $api({
      url: `/ts-hrms/getDictionariesByType/${type}`,
      method: 'post'
    });
  }
};
