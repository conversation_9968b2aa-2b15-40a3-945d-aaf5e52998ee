import { $api } from '@/api/ajax';
import { service } from '@/api/config';

// 获取设备列表
function getDeviceList() {
  return $api({
    url: `${service.tsOa()}/boardRoomDevice/getList`,
    method: 'get'
  });
}
// 获取楼栋/位置
function getLocationList() {
  return $api({
    url: `${service.tsOa()}/boardRoom/getLocationList`,
    method: 'get'
  });
}

// 删除设备
function deleteDevice(data) {
  return $api({
    url: `${service.tsOa()}/boardRoomDevice/del`,
    method: 'post',
    data: data
  });
}

export default {
  getDeviceList,
  getLocationList,
  deleteDevice
};
