<template>
  <div class="table-container">
    <div class="form-table">
      <ts-vxe-table
        ref="formTable"
        :id="id"
        border
        :stripe="stripe"
        resizable
        show-overflow
        keep-source
        height="auto"
        :min-height="minHeight"
        :columns="columns"
        :column-key="true"
        :custom-config="customConfig"
        :data="rows"
        :row-config="{
          useKey: true,
          keyField: 'id',
          isHover: true,
          isCurrent: true,
          ...rowConfig
        }"
        :checkbox-config="{ trigger: 'row', ...checkboxConfig }"
        :column-config="{
          showOverflow: 'tooltip',
          headerShowOverflow: 'title',
          isCurrent: true,
          isHover: true,
          resizable: true
        }"
        :tooltip-config="{
          showAll: true,
          transfer: true,
          zIndex: 2009
        }"
        @sort-change="handleSortChange"
        @checkbox-change="handleSelectionChange"
        @checkbox-all="handleSelectionChange"
        v-bind="{ ...$attrs, ...$props }"
        v-on="$listeners"
      >
      </ts-vxe-table>
    </div>
    <div v-if="hasPage" class="pagination-content">
      <vxe-pager
        perfect
        size="mini"
        :pager-count="pagerCount"
        :page-sizes="pageSizes"
        :current-page="pageNo"
        :page-size="pageSize"
        :total="total"
        :align="pageAlign"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total'
        ]"
        @page-change="handlePageChange"
      ></vxe-pager>
    </div>
  </div>
</template>

<script>
import TsVxeTable from '../ts-vxe-table';
export default {
  name: 'ts-vxe-template-table',
  components: {
    TsVxeTable
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    minHeight: {
      type: [String, Number]
    },
    defaultPageSize: {
      type: Number,
      default: 100
    },
    pageAlign: {
      type: String,
      default: 'center'
    },
    pagerCount: {
      type: Number,
      default: 7
    },
    pageSizes: {
      type: Array,
      default: () => [100, 200, 500, 1000]
    },
    hasPage: {
      type: Boolean,
      default: () => true
    },
    rowConfig: {
      type: Object,
      default: () => {}
    },
    checkboxConfig: {
      type: Object,
      default: () => ({
        reserve: false
      })
    },
    defaultSort: {
      type: Object,
      default: () => ({
        sord: 'desc',
        sidx: 'create_time'
      })
    },
    stripe: {
      type: Boolean,
      default: () => false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rows: [],

      pageNo: 1,
      pageSize: 20,
      total: 0,

      sord: '',
      sidx: '',
      customConfig: { storage: true }
    };
  },
  watch: {
    defaultPageSize: {
      handler(val) {
        if (val !== undefined && val !== null && val !== 0) {
          this.pageSize = val;
        }
      },
      immediate: true
    },

    defaultSort: {
      handler(val = {}) {
        if (val && JSON.stringify(val) !== '{}') {
          let { sord, sidx } = val;
          this.sord = sord;
          this.sidx = sidx;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    tsVxeTableRef() {
      return this.$refs.formTable.$refs.tsVxeTable;
    },
    /**@desc 数据刷新 */
    refresh() {
      if (!this.checkboxConfig.reserve) {
        this.tsVxeTableRef().clearCheckboxRow();
        this.tsVxeTableRef().clearCheckboxReserve();

        this.handleSelectionChange({
          reserves: [],
          records: []
        });
      }

      this.tsVxeTableRef().clearCurrentRow();

      let rows,
        totalCount,
        argList = Array(...arguments),
        obj = argList.find(
          arg => Object.prototype.toString.call(arg) == '[object Object]'
        ),
        list = argList.find(arg => arg instanceof Array),
        total =
          argList.find(
            arg => typeof arg === 'number' || typeof arg === 'string'
          ) || 0;

      if (obj) {
        rows = obj.rows;
        totalCount = obj.totalCount;
      } else {
        rows = list;
        totalCount = Number(total);
      }

      this.rows = rows || [];
      this.total = totalCount || 0;
    },
    handlePageChange({ currentPage, pageSize }) {
      this.pageNo = currentPage;
      this.pageSize = pageSize;
      this.triggerRefresh();
    },
    handleSortChange({ order, column }) {
      this.sord = order;
      this.sidx = column.sortBy || column.field;
      this.triggerRefresh();
    },
    handleGetCurrentRecord() {
      return this.tsVxeTableRef().getCurrentRecord();
    },
    handleSelectionChange({ reserves, records }) {
      this.$emit('selection-change', [...records, ...reserves]);
    },
    triggerRefresh() {
      this.$emit('refresh', this.getTableData());
    },
    /**@desc 主动获取表格数据 */
    getTableData() {
      return {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        sord: this.sort,
        sidx: this.sidx
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  transform: scale(1);
  overflow: hidden;

  .form-table {
    width: 100%;
    flex: 1;
    overflow: hidden;

    ::v-deep {
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        color: $primary-blue !important;
        border-color: $primary-blue !important;
        background-color: $primary-blue !important;
      }

      .el-checkbox__inner:hover,
      .el-select-dropdown__item.selected {
        color: $primary-blue !important;
      }

      .action-cell {
        cursor: pointer;
        color: $primary-blue;

        &:active {
          color: $primary-blue-active;
        }
      }
    }
  }

  .pagination-content {
    height: 36px;

    .vxe-pager {
      background: #f6f6f6;
    }
  }
}
</style>
