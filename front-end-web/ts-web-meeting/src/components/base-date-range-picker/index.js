import {
  formatDate,
  parseDate,
  isDateObject
} from 'element-ui/src/utils/date-util';
import moment from 'moment';

const DEFAULT_FORMATS = {
  daterange: 'yyyy-MM-dd',
  monthrange: 'yyyy-MM',
  datetimerange: 'yyyy-MM-dd HH:mm',
  yearrange: 'yyyy'
};

const TRIGGER_TYPES = {
  daterange: 'date',
  monthrange: 'month',
  datetimerange: 'datetime',
  yearrange: 'year'
};

const DATE_FORMATTER = function(value, format) {
  return formatDate(value, format);
};
const DATE_PARSER = function(text, format) {
  return parseDate(text, format);
};
const RANGE_FORMATTER = function(value, format) {
  if (Array.isArray(value) && value.length === 2) {
    const start = value[0];
    const end = value[1];

    if (start || end) {
      return [DATE_FORMATTER(start, format), DATE_FORMATTER(end, format)];
    }
  }
  return '';
};
const RANGE_PARSER = function(array, format, separator) {
  if (!Array.isArray(array)) {
    array = array.split(separator);
  }
  if (array.length === 2) {
    const range1 = array[0];
    const range2 = array[1];

    return [DATE_PARSER(range1, format), DATE_PARSER(range2, format)];
  }
  return [];
};
const parseAsFormatAndType = (
  value,
  customFormat,
  type,
  rangeSeparator = '-'
) => {
  if (!value) return null;
  const format = customFormat || DEFAULT_FORMATS[type];
  return RANGE_PARSER(value, format, rangeSeparator);
};
const formatAsFormatAndType = (value, customFormat, type) => {
  if (!value) return null;
  const format = customFormat || DEFAULT_FORMATS[type];
  return RANGE_FORMATTER(value, format);
};

export default {
  computed: {
    pickerDisabled() {
      return this.disabled || (this.elForm || {}).disabled;
    },
    dataFormat() {
      return this.format || DEFAULT_FORMATS[this.type];
    },
    parsedValue() {
      if (!this.value) {
        this.userInput = null;
        return this.value;
      }
      const valueIsDateObject =
        Array.isArray(this.value) && this.value.every(isDateObject);
      if (valueIsDateObject) {
        return this.value;
      }
      if (this.valueFormat) {
        return (
          parseAsFormatAndType(
            this.value,
            this.valueFormat,
            this.type,
            this.rangeSeparator
          ) || this.value
        );
      }
      if (Array.isArray(this.value)) {
        let dataList = this.value.map(val => (val ? new Date(val) : ''));
        this.userInput = formatAsFormatAndType(
          [dataList[0], dataList[1]],
          this.format,
          this.type
        );
        return dataList;
      } else return new Date(this.value);
    },
    pickerType() {
      return TRIGGER_TYPES[this.type];
    },
    pickerOptions() {
      let option = {
        disabledDate: this.disabledDate
      };
      if (this.type != 'datetimerange')
        option.shortcuts = [
          {
            text: '清空',
            onClick(picker) {
              picker.$emit('pick', '');
            }
          },
          {
            text: '现在',
            onClick: this.dateClick
          }
        ];
      return option;
    },
    pickerOptions1() {
      let option = {
        disabledDate: this.disabledDate1
      };
      if (this.type != 'datetimerange')
        option.shortcuts = [
          {
            text: '清空',
            onClick: picker => {
              picker.$emit('pick', '');
            }
          },
          {
            text: '现在',
            onClick: this.dateClick1
          }
        ];
      return option;
    }
  },
  watch: {
    parsedValue: {
      handler: function(val) {
        const formattedValue = formatAsFormatAndType(
          this.parsedValue,
          this.format,
          this.type,
          this.rangeSeparator
        );
        if (Array.isArray(this.userInput)) {
          this.startValue =
            this.userInput[0] || (formattedValue && formattedValue[0]) || '';
          this.endValue =
            this.userInput[1] || (formattedValue && formattedValue[1]) || '';
        } else {
          this.startValue = '';
          this.endValue = '';
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      startValue: '',
      endValue: '',
      userInput: null
    };
  },
  methods: {
    dispatch(componentName, eventName, params) {
      var parent = this.$parent || this.$root;
      var name = parent.$options.componentName;

      while (parent && (!name || name !== componentName)) {
        parent = parent.$parent;

        if (parent) {
          name = parent.$options.componentName;
        }
      }
      if (parent) {
        parent.$emit.apply(parent, [eventName].concat(params));
      }
    },
    disabledDate(time) {
      if (this.userInput && this.userInput[1]) {
        if (moment(time).isAfter(moment(this.userInput[1]))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    disabledDate1(time) {
      if (this.userInput && this.userInput[0]) {
        if (moment(time).isBefore(moment(this.userInput[0]))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    dateClick(picker) {
      if (
        this.userInput &&
        this.userInput[1] &&
        new Date() > new Date(this.userInput[1])
      ) {
        picker.$emit('pick', new Date(this.userInput[1]));
        this.userInput[1] = this.$dayjs().format(this.dataFormat);
      } else {
        picker.$emit('pick', new Date());
      }
    },
    dateClick1(picker) {
      if (
        this.userInput &&
        this.userInput[0] &&
        new Date() < new Date(this.userInput[0])
      ) {
        picker.$emit('pick', new Date(this.userInput[0]));
        this.userInput[0] = this.$dayjs().format(this.dataFormat);
      } else {
        picker.$emit('pick', new Date());
      }
    },
    handleChange() {
      let start = this.startValue,
        end = this.endValue;
      if (
        this.startValue &&
        this.endValue &&
        moment(this.startValue).isAfter(moment(this.endValue))
      ) {
        this.startValue = end;
        this.endValue = start;
      }
      this.userInput = [this.startValue, this.endValue];
      this.$emit('input', [this.startValue, this.endValue]);
      this.$emit('change', [this.startValue, this.endValue]);
    },
    handleBlur() {
      if (this.validateEvent) {
        this.dispatch('ElFormItem', 'el.form.blur', [this.value]);
      }
      this.$emit('blur', this);
    }
  }
};
