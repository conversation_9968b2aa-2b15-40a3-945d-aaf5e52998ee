<template>
  <el-select
    :size="size"
    style="width:100%"
    v-model="childSelectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    filterable
    remote
    v-selectLoadMore="selectLoadMore"
    :loading="loading"
    :remote-method="remoteMethod"
    @visible-change="visibleChange"
  >
    <el-option
      v-for="item in goodsCategoryData"
      :key="item.empCode"
      :label="item.empName"
      :value="item.empCode"
    />
  </el-select>
</template>

<script>
import { commonUtils } from '@/utils/index.js';
export default {
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: () => false
    },
    placeholder: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'medium'
    }
  },
  data() {
    return {
      loading: false,
      childSelectedValue: this.value,
      queryParam: {
        userSel: true
      },
      goodsCategoryData: [],
      dataParam: {
        empName: undefined
      },
      ipagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  methods: {
    selectLoadMore() {
      this.ipagination.current = this.ipagination.current + 1;
      const pageCount = Math.ceil(
        this.ipagination.total / this.ipagination.pageSize
      );
      if (this.ipagination.current > pageCount) {
        return;
      }
      this.getGoodsList(); // 请求接口
    },
    remoteMethod(query) {
      this.dataParam.empName = query;
      this.ipagination.current = 1;
      this.goodsCategoryData = [];
      this.loading = true;
      setTimeout(() => {
        // 查询数据
        this.getGoodsList();
      }, 200);
    },
    async getGoodsList() {
      const dataParams = this.getDataParams();
      const resp = await this.ajax.getEmployeeList(this.queryParam, dataParams);
      this.goodsCategoryData = [...this.goodsCategoryData, ...resp.rows];
      this.ipagination.total = resp.totalCount;
      this.loading = false;
    },
    getDataParams() {
      // 获取查询条件
      var data = Object.assign(this.dataParam);

      data.pageNo = this.ipagination.current;
      data.pageSize = this.ipagination.pageSize;
      return commonUtils.filterObj(data);
    },

    visibleChange(visible) {
      if (visible) {
        this.getGoodsList();
      } else {
        this.goodsCategoryData = [];
        this.dataParam.empName = '';
        this.ipagination.current = 1;
      }
    }
  },
  created() {
    // this.getGoodsList();
  },
  watch: {
    value: {
      handler(newValue) {
        this.childSelectedValue = newValue;
      },
      deep: true,
      immediate: true
    },
    childSelectedValue(newValue) {
      if (!newValue) this.childSelectedValue = '';
      this.$emit('input', newValue);
    }
  }
};
</script>

<style>
.el-input--medium .el-input__inner {
  height: 30px;
}
</style>
