export const TSTableMixin = {
  data() {
    return {
      queryParam: {}
    };
  },
  methods: {
    // 排序
    sortChange({ column, prop, order }) {
      this.queryParam.sord =
        order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
      switch (prop) {
        case 'boardroomName':
          this.queryParam.sidx = 'a1.name';
          break;
        case 'destineDate':
          this.queryParam.sidx = 'a2.DESTINE_DATE';
          break;
        case 'startTime':
          this.queryParam.sidx = 'a2.START_TIME';
          break;
        case 'statusLable':
          this.queryParam.sidx = 'a2.STATUS';
          break;
        case 'applyOrgName':
          this.queryParam.sidx = 'a2.APPLY_ORG';
          break;
        case 'checkTime':
          this.queryParam.sidx = 'a4.FINISHED_DATE';
          break;
        default:
          break;
      }
      this.loadData();
    }
  }
};
