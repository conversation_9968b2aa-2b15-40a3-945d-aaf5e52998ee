/**@desc 路由组件混入获取菜单权限 */
function routerResolve(resolve) {
  let _this = this;
  return function(module) {
    let component = module.default;
    !component.mixins && (component.mixins = []);
    component.mixins.push({
      data() {
        return {
          menuLimits: [] // 路由按钮权限按钮
        };
      },
      mounted() {
        this.getMenuLimitList();
      },
      methods: {
        async getMenuLimitList() {
          this.menuLimits = await this.$getMenuSource();
        }
      }
    });
    return resolve.call(_this, module);
  };
}
export default [
  {
    path: '/attendance/summaryAndReport',
    component: resolve =>
      require([
        `@/views/attendance/summary-and-report/index.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '考勤汇总上报'
  },
  {
    path: '/attendance/checkReportRecords',
    component: resolve =>
      require([
        `@/views/attendance/summary-and-report/check-report-records.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '考勤汇总记录'
  },
  {
    path: '/attendance/attendanceSummary',
    component: resolve =>
      require([
        `@/views/attendance/attendance-summary/index.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '考勤汇总'
  },
  {
    path: '/attendance/annual-leave-setting',
    component: resolve =>
      require([
        `@/views/attendance/annual-leave-setting/index.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '年假设置'
  },
  {
    path: '/attendance/annual-leave-manage',
    component: resolve =>
      require([
        `@/views/attendance/annual-leave-manage/index.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '年假管理'
  },
  {
    path: '/attendance/leave-statistics',
    component: resolve =>
      require([`@/views/attendance/leave-statistics/index.vue`], routerResolve(
        resolve
      )),
    styleName: '',
    name: '假期统计'
  }
];
