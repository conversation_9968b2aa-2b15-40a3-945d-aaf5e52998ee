import { request } from '@/api/ajax';
export default {
  /**@desc 获取我的培训总计数据 */
  getMyTrainStatisticData() {
    return request({
      url: '/ts-hrms/train/record/myDetails',
      method: 'get'
    });
  },
  /**@desc 获取我的培训列表数据 */
  getMyTrainTableDataList(params) {
    return request({
      url: '/ts-hrms/train/record/getDataList',
      method: 'post',
      params
    });
  },
  /**@desc 我的培训tab数据 */
  getMyPlanTabData() {
    return request({
      url: '/ts-hrms/train/record/myParticipate',
      method: 'get'
    });
  },
  /**@desc 获取学分榜数据 */
  getTrainingAccumulatePointsData(params) {
    return request({
      url: '/ts-hrms/train/record/ranking',
      method: 'get',
      params
    });
  },
  /**@desc 我的培训 */
  getTrainingRecordFindListByEmpId(id, params) {
    return request({
      url: `/ts-hrms/train/record/findListByEmpId/${id}`,
      method: 'get',
      params
    });
  }
};
