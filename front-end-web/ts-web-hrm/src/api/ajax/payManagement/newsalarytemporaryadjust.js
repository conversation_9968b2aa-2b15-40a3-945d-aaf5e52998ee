import { request } from '@/api/ajax';

//查询组织树
const getOrganizationSelectZTreeList = function(params) {
  return request({
    url: '/ts-basics-bottom/organization/getTree3',
    method: 'post',
    params
  });
};

//查询薪酬调整列表
const getNewsalaryTemporaryAdjustList = function(params) {
  return request({
    url: '/ts-hrms/api/newsalaryTemporaryAdjust/list',
    method: 'get',
    params
  });
};

// 新增临时员工记录
const handleAddTemporaryAdjustTableData = function(data) {
  return request({
    url: `/ts-hrms/api/newsalaryTemporaryAdjust/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 编辑临时员工记录
const handleEditTemporaryAdjustTableData = function(data) {
  return request({
    url: `/ts-hrms/api/newsalaryTemporaryAdjust/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 删除
function handleDeleteTemporaryAdjustData(id) {
  return request({
    url: `/ts-hrms/api/newsalaryTemporaryAdjust/delete/` + id,
    method: 'post'
  });
}

// 导入
function importTemporaryAdjustData(data) {
  return request({
    url: '/ts-hrms/api/newsalaryTemporaryAdjust/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 操作记录
const getTemporaryAdjustOperateLoggerList = function(params) {
  return request({
    url: `/ts-hrms/api/newsalaryTemporaryAdjustChange/list`,
    method: 'get',
    params
  });
};

// 操作功能类型
const getTemporaryAdjustOpTypeDatas = function() {
  return request({
    url: `/ts-hrms/api/newsalaryTemporaryAdjustChange/getOpTypeDatas`,
    method: 'get'
  });
};

// 分页查询包括临时员工的所有员工
const pagesAllEmployeeByParam = function(data) {
  return request({
    url: `/ts-hrms/employee/pagesAllEmployeeByParam`,
    method: 'post',
    data
  });
};

export {
  getOrganizationSelectZTreeList,
  getNewsalaryTemporaryAdjustList,
  handleAddTemporaryAdjustTableData,
  handleEditTemporaryAdjustTableData,
  handleDeleteTemporaryAdjustData,
  importTemporaryAdjustData,
  getTemporaryAdjustOperateLoggerList,
  getTemporaryAdjustOpTypeDatas,
  pagesAllEmployeeByParam
};
export default {
  getOrganizationSelectZTreeList,
  getNewsalaryTemporaryAdjustList,
  handleAddTemporaryAdjustTableData,
  handleEditTemporaryAdjustTableData,
  handleDeleteTemporaryAdjustData,
  importTemporaryAdjustData,
  getTemporaryAdjustOperateLoggerList,
  getTemporaryAdjustOpTypeDatas,
  pagesAllEmployeeByParam
};
