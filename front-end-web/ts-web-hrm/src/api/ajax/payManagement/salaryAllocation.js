import { request } from '@/api/ajax';

// 列表
function newsalarySeniorityWageDispList(params) {
  return request({
    url: '/ts-hrms/api/newsalarySeniorityWageDisp/list',
    method: 'get',
    params
  });
}

// 新增
function newsalarySeniorityWageDispSave(data) {
  return request({
    url: '/ts-hrms/api/newsalarySeniorityWageDisp/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 编辑
function newsalarySeniorityWageDispSaveUpdate(data) {
  return request({
    url: '/ts-hrms/api/newsalarySeniorityWageDisp/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 启用
function newsalarySeniorityWageDispEnable(data) {
  return request({
    url: `/ts-hrms/api/newsalarySeniorityWageDisp/enable`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 禁用
function newsalarySeniorityWageDispDisEnable(data) {
  return request({
    url: `/ts-hrms/api/newsalarySeniorityWageDisp/disEnable`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 删除
function newsalarySeniorityWageDispDelete(id) {
  return request({
    url: `/ts-hrms/api/newsalarySeniorityWageDisp/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export default {
  newsalarySeniorityWageDispList,
  newsalarySeniorityWageDispSave,
  newsalarySeniorityWageDispSaveUpdate,
  newsalarySeniorityWageDispDelete,
  newsalarySeniorityWageDispEnable,
  newsalarySeniorityWageDispDisEnable
};
