import { request } from '@/api/ajax';

// 薪酬核算页面列表
function newSalaryOptionPayrollList(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/list',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 查询未加入薪酬组人员
function salaryBasicColumnGetUnoption(params) {
  return request({
    url: '/ts-hrms/api/salaryBasicColumn/getUnoption',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 核对人员接口
function newSalaryOptionPayrollCheckPersonnel(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/checkPersonnel',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬核算定薪调薪表头接口
function makePayTableTitle(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/makePayTableTitle',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬核算定薪调薪数据接口
function makePayTableData(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/makePayTableData',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬核算-核算表头
function calculateWagesTitle(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/calculateWagesTitle',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬核算-核算数据
function calculateWagesData(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/calculateWagesData',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

function checkPersonnelCount(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/checkPersonnelCount',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬方案核算 定薪调薪统计
function makePayTableDataStatis(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/makePayTableDataStatis',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 导入手工工资项
function newSalaryDetailImportImportSave(data) {
  return request({
    url: '/ts-hrms/api/newSalaryDetailImport/importSave',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

// 导入所有手工工资项
function newSalaryDetailImportImportAllSave(data) {
  return request({
    url: '/ts-hrms/api/newSalaryDetailImport/importAllSave',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
}

// 清除薪酬核算手工工资项导入数据
function cleanImportDataByOptionId(data) {
  return request({
    url: '/ts-hrms/api/newSalaryDetailImport/cleanImportDataByOptionId',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 开始计算
function startCalculation(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/startCalculation',
    method: 'get',
    headers: {
      'is-not-wait-result': 'true'
    },
    params
  });
}

// 获取方案状态
function getCalculationStatus(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/getCalculationStatus',
    method: 'get',
    params
  });
}

// 检查是否存在未处理薪酬提醒数据
function checkIsExistsUnprocessedRemindData(params) {
  return request({
    url:
      '/ts-hrms/api/newSalaryOptionPayroll/checkIsExistsUnprocessedRemindData',
    method: 'get',
    params
  });
}

// 完成核算接口
function newSalaryOptionPayrollComplete(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/complete',
    method: 'get',
    params
  });
}

// 重启核算接口
function newSalaryOptionPayrollReloadComplete(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/reloadComplete',
    method: 'get',
    params
  });
}

// 锁定工资条接口
function newSalaryOptionPayrollLockSalary(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/lockSalary',
    method: 'get',
    params
  });
}

// 发送工资条接口
function newSalaryOptionPayrollPaySlip(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/paySlip',
    method: 'get',
    params
  });
}

// 获取修改工资项
function newSalaryOptionPayrollGetSalaryChangesData(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/getSalaryChangesData',
    method: 'get',
    params
  });
}

// 保存修改工资项
function newSalaryOptionPayrollReloadStartCalculation(data) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/reloadStartCalculation',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 薪酬核算-批量锁定工资结果
function newSalaryOptionPayrollBatchLockSalary(data) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/batchLockSalary',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 薪酬核算-批量发放工资条
function newSalaryOptionPayrollBatchPayslip(data) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/batchPayslip',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 薪酬核算-人员确认添加人员列表
function newSalaryOptionPayrollGetCheperList(data) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/getCheperList',
    method: 'get',
    data
  });
}

// 薪酬核算-人员确认添加人员保存
function salaryOptionEmpPerSave(optionId, data) {
  return request({
    url: `/ts-hrms/api/salaryOptionEmp/perSave/${optionId}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 薪酬核算-移除人员
function salaryOptionEmpDeleteOption(optionId, data) {
  return request({
    url: `/ts-hrms/api/salaryOptionEmp/deleteOption/${optionId}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 薪酬数据确认 表头接口
function newSalaryOptionPayrollSalaryConfirmTitle(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/salaryConfirmTitle',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 薪酬数据确认 数据接口
function newSalaryOptionPayrollSalaryConfirmData(params) {
  return request({
    url: '/ts-hrms/api/newSalaryOptionPayroll/salaryConfirmData',
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    },
    params
  });
}

// 判断薪酬方案是否导出手工录入工资项
function newSalaryDetailImportCheckManualSalary(optionId, computeDate) {
  return request({
    url: `/ts-hrms/api/newSalaryDetailImport/checkManualSalary/${optionId}/${computeDate}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 薪酬计算-预览
function newSalaryOptionPayrollPreview(optionId, employeeId, computeDate) {
  return request({
    url: `/ts-hrms/api/newSalaryOptionPayroll/preview/${optionId}/${employeeId}/${computeDate}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取修改工资项
function queryUploadInfo(params) {
  return request({
    url: '/ts-hrms/api/queryUploadInfo',
    method: 'get',
    params
  });
}

export default {
  newSalaryOptionPayrollList,
  checkPersonnelCount,
  newSalaryOptionPayrollCheckPersonnel,
  makePayTableTitle,
  makePayTableData,
  calculateWagesTitle,
  salaryBasicColumnGetUnoption,
  makePayTableDataStatis,
  calculateWagesData,
  newSalaryDetailImportImportSave,
  newSalaryDetailImportImportAllSave,
  startCalculation,
  getCalculationStatus,
  newSalaryOptionPayrollComplete,
  newSalaryOptionPayrollReloadComplete,
  newSalaryOptionPayrollLockSalary,
  newSalaryOptionPayrollPaySlip,
  newSalaryOptionPayrollGetSalaryChangesData,
  newSalaryOptionPayrollReloadStartCalculation,
  newSalaryOptionPayrollBatchLockSalary,
  newSalaryOptionPayrollBatchPayslip,
  newSalaryOptionPayrollGetCheperList,
  salaryOptionEmpPerSave,
  salaryOptionEmpDeleteOption,
  newSalaryOptionPayrollSalaryConfirmTitle,
  newSalaryOptionPayrollSalaryConfirmData,
  newSalaryDetailImportCheckManualSalary,
  newSalaryOptionPayrollPreview,
  queryUploadInfo,
  checkIsExistsUnprocessedRemindData,
  cleanImportDataByOptionId
};
