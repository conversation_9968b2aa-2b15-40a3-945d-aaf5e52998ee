import { request } from '@/api/ajax';
import { service } from '@/api/config';

// 科室荣誉导入
const deptHonorWinnerImportHonorWinner = function(data) {
  return request({
    url: `${service.tsHrms()}/api/deptHonorWinner/importHonorWinner`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 科室荣誉列表查询
const deptHonorList = function(params) {
  return request({
    url: `${service.tsHrms()}/api/deptHonor/list`,
    method: 'get',
    params
  });
};

// 科室荣誉保存
const deptHonorSave = function(data) {
  return request({
    url: `${service.tsHrms()}/api/deptHonor/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 会诊安排保存
const deptHonorUpdate = function(data) {
  return request({
    url: `${service.tsHrms()}/api/deptHonor/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 科室荣誉详情
const deptHonorDetails = function(id) {
  return request({
    url: `${service.tsHrms()}/api/deptHonor/${id}`,
    method: 'get'
  });
};

// 科室荣誉删除
const deptHonorDelete = function(id) {
  return request({
    url: `${service.tsHrms()}/api/deptHonor/delete/${id}`,
    method: 'post'
  });
};

export default {
  deptHonorList,
  deptHonorSave,
  deptHonorUpdate,
  deptHonorDetails,
  deptHonorDelete,
  deptHonorWinnerImportHonorWinner
};
