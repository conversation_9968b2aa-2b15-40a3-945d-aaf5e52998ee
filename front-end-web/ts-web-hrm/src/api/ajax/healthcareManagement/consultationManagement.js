import { request } from '@/api/ajax';

// 会诊排班导入
const scduImport = function(data) {
  return request({
    url: '/ts-hrms/api/scdu/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 会诊排班查询
const getScduList = function(params) {
  return request({
    url: '/ts-hrms/api/scdu/list',
    method: 'get',
    params
  });
};

// 会诊排班表头获取
const getScduHeadler = function(data) {
  return request({
    url: '/ts-hrms/api/scdu/getHeadler',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 会诊排班保存
const saveDetls = function(data) {
  return request({
    url: '/ts-hrms/api/scduDetl/saveDetls',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 会诊安排列表
const getCsltAppyList = function(params) {
  return request({
    url: '/ts-hrms/api/csltAppy/list',
    method: 'get',
    params
  });
};

// 会诊安排保存
const csltAppyUpdate = function(data) {
  return request({
    url: `/ts-hrms/api/csltAppy/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 操作记录
const getcsltOprtList = function(params) {
  return request({
    url: '/ts-hrms/api/csltOprt/list',
    method: 'get',
    params
  });
};

// 会诊安排详情
const getCsltAppyDetail = function(id) {
  return request({
    url: `/ts-hrms/api/csltAppy/${id}`,
    method: 'get'
  });
};

// 会诊一览
const csltScduList = function(params) {
  return request({
    url: `/ts-hrms/api/scdu/selectCsltSchedule`,
    method: 'get',
    params
  });
};

// 会诊一览
const selectCsltScheduleNew = function(params) {
  return request({
    url: `/ts-hrms/api/scdu/selectCsltScheduleNew`,
    method: 'get',
    params
  });
};

// 复制会诊排班
const copyScduDetl = function(params) {
  return request({
    url: `/ts-hrms/api/scdu/copyScduDetl`,
    method: 'get',
    params
  });
};

// 清空会诊排班
const clearScduDetl = function(params) {
  return request({
    url: `/ts-hrms/api/scdu/clearScduDetl`,
    method: 'get',
    params
  });
};

// 清空会诊排班
const CsltScduDeptCount = function(params) {
  return request({
    url: `/ts-hrms/api/scdu/CsltScduDeptCount`,
    method: 'get',
    params
  });
};

// ---- 会诊看板
// 卡片
const scduDetlStatistics = function(params) {
  return request({
    url: `/ts-hrms/api/scduDetl/statistics`,
    method: 'get',
    params
  });
};

// 卡片
const scduDetlHzjgfxList = function(params) {
  return request({
    url: `/ts-hrms/api/scduDetl/hzjgfxList`,
    method: 'get',
    params
  });
};

const scduDetlZlpffxList = function(params) {
  return request({
    url: `/ts-hrms/api/scduDetl/zlpffxList`,
    method: 'get',
    headers: {
      'is-not-wait-result': 'true'
    },
    params
  });
};

// 个人的红黑榜
const personRedBlackRank = function(params) {
  return request({
    url: `/ts-hrms/api/scduDetl/personRedBlackRank`,
    method: 'get',
    headers: {
      'is-not-wait-result': 'true'
    },
    params
  });
};

// 科室的红黑榜
const deptRedBlackRank = function(params) {
  return request({
    url: `/ts-hrms/api/scduDetl/deptRedBlackRank`,
    method: 'get',
    headers: {
      'is-not-wait-result': 'true'
    },
    params
  });
};

// 会诊资质管理-全院大会诊
const getConsultQuaList = function(params) {
  return request({
    url: `/ts-hrms/api/consultQua/list`,
    method: 'get',
    params
  });
};

const consultQuaAuthorize = function(data) {
  return request({
    url: `/ts-hrms/api/consultQua/authorize`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 会诊特殊药物
const getConsultSpecialDrugList = function(params) {
  return request({
    url: `/ts-hrms/api/consultSpecialDrug/list`,
    method: 'get',
    params
  });
};

const importMedWorkplanPlayer = function(data) {
  return request({
    url: `/ts-hrms/api/consultSpecialDrug/importMedWorkplanPlayer`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

const consultSpecialDrugSave = function(data) {
  return request({
    url: `/ts-hrms/api/consultSpecialDrug/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

const consultSpecialDrugUpdate = function(data) {
  return request({
    url: `/ts-hrms/api/consultSpecialDrug/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

const consultSpecialDrugDelete = function(id) {
  return request({
    url: `/ts-hrms/api/consultSpecialDrug/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
};
const getConsultApplyList = function(params) {
  return request({
    url: `/ts-hrms/api/consultApply/list`,
    method: 'get',
    params
  });
};
// 全院大会诊申请单-详情列表
const getconsultApplyDetailsList = function(params) {
  return request({
    url: `/ts-hrms/api/consultApplyDetails/list`,
    method: 'get',
    params
  });
};

export default {
  scduImport,
  getScduList,
  getScduHeadler,
  saveDetls,
  getCsltAppyList,
  getCsltAppyDetail,
  csltAppyUpdate,
  getcsltOprtList,
  csltScduList,
  copyScduDetl,
  clearScduDetl,
  CsltScduDeptCount,
  scduDetlStatistics,
  scduDetlHzjgfxList,
  scduDetlZlpffxList,
  personRedBlackRank,
  deptRedBlackRank,
  selectCsltScheduleNew,
  getConsultQuaList,
  consultQuaAuthorize,
  getConsultSpecialDrugList,
  importMedWorkplanPlayer,
  consultSpecialDrugDelete,
  consultSpecialDrugSave,
  consultSpecialDrugUpdate,
  getConsultApplyList,
  getconsultApplyDetailsList
};
