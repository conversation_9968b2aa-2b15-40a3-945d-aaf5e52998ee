import { request } from '@/api/ajax';
import { service } from '@/api/config';

const importMedDeliveryDict = function(data) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryDict/importMedDeliveryDict`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      messageErrorAlert: 1
    },
    data
  });
};

const medDeliveryDictList = function(params) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryDict/list`,
    method: 'get',
    params
  });
};

const medDeliveryDictDelete = function(id) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryDict/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

const medDeliveryDictSave = function(data) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryDict/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      messageErrorAlert: 1
    },
    data
  });
};

const medDeliveryDictUpdate = function(data) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryDict/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      messageErrorAlert: 1
    },
    data
  });
};

const medDeliveryRecordList = function(params) {
  return request({
    url: `${service.tsHrms()}/api/medDeliveryRecord/list`,
    method: 'get',
    params
  });
};

export default {
  importMedDeliveryDict,
  medDeliveryDictList,
  medDeliveryDictDelete,
  medDeliveryDictSave,
  medDeliveryDictUpdate,
  medDeliveryRecordList
};
