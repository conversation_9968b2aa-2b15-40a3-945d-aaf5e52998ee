import { request } from '@/api/ajax';

// 意见箱列表
function suggestionBoxList(params) {
  return request({
    url: '/ts-hrms/api/suggestionBox/list',
    method: 'get',
    params
  });
}

// 编辑
function suggestionBoxUpdate(data) {
  return request({
    url: `/ts-hrms/api/suggestionBox/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 删除
function suggestionBoxDel(id) {
  return request({
    url: `/ts-hrms/api/suggestionBox/delete/${id}`,
    method: 'post'
  });
}
//获取医院页面设置信息
function getGlobalSetting() {
  return request({
    url: `/ts-basics-bottom/globalSetting/getAllGlobalSetting`,
    method: 'get'
  });
}
function verificationPwd(data) {
  return request({
    url: `/ts-hrms/api/suggestionBox/verificationPwd`,
    method: 'post',
    data
  });
}
export default {
  suggestionBoxList,
  suggestionBoxUpdate,
  suggestionBoxDel,
  getGlobalSetting,
  verificationPwd
};
