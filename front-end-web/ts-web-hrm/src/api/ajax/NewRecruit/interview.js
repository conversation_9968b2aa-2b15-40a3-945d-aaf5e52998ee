import { request } from '@/api/ajax';

// 面试管理 待面试表单
export const zpglInterviewMessageList = params => {
  return request({
    url: '/ts-hrms/api/zpglInterviewMessage/list',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    params
  });
};

// 获取面试信息
export const zpglInterviewMessageInfo = messageId => {
  return request({
    url: `/ts-hrms/api/zpglInterviewMessage/${messageId}`,
    method: 'get'
  });
};

// 设置面试信息 变更
export const zpglInterviewMessageUpdate = data => {
  return request({
    url: '/ts-hrms/api/zpglInterviewMessage/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 面试评价
export const zpglInterviewResultSave = data => {
  return request({
    url: '/ts-hrms/api/zpglInterviewResult/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 跟踪列表
export const getZpglTraceResultAllList = params => {
  return request({
    url: '/ts-hrms/api/zpglTraceResult/allList',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    params
  });
};

// 跟踪保存
export const zpglTraceResultSave = data => {
  return request({
    url: '/ts-hrms/api/zpglTraceResult/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 跟踪记录
export const getZpglTraceResultList = zpglempid => {
  return request({
    url: `/ts-hrms/api/zpglTraceResult/getListByName/${zpglempid} `,
    method: 'get'
  });
};

// 面试通过提交
export const zpglInterviewResultSubmit = zpglempid => {
  return request({
    url: `/ts-hrms/api/zpglInterviewResult/submit/${zpglempid}`,
    method: 'get'
  });
};

// 继续面试
export const zpglInterviewResultContinue = data => {
  return request({
    url: `/ts-hrms/api/zpglInterviewResult/continue`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 通过resultid来查询 面试评价信息
export const getZpglInterviewResult = resultId => {
  return request({
    url: `/ts-hrms/api/zpglInterviewResult/${resultId}`,
    method: 'get'
  });
};

// 面试失败 删除
export const zpglInterviewResultDelete = zpglempid => {
  return request({
    url: `/ts-hrms/api/zpglInterviewResult/delete/${zpglempid}`,
    method: 'post'
  });
};

export default {};
