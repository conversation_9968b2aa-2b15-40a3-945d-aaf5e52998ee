import { request } from '@/api/ajax';

// 获取过滤器
function personnelIncidentList(data) {
  return request({
    url: `/ts-hrms/personnelIncident/list`,
    method: 'post',
    data
  });
}

// 获取编制类型
function getDictType(dictName) {
  return request({
    url: `/ts-hrms/dict/combobox/${dictName}`,
    method: 'post'
  });
}

// 获取人事调动
function personnelChangeList(data) {
  return request({
    url: `/ts-hrms/personnelChange/list`,
    method: 'post',
    data
  });
}

// 获取异动类型
function loadchangeselect(data) {
  return request({
    url: `/ts-hrms/personnelTransaction/loadchangeselect`,
    method: 'post',
    data
  });
}

// 获取异动类型
function loadchangeList(data) {
  return request({
    url: `/ts-hrms/personnelTransaction/list`,
    method: 'post',
    data
  });
}

// 获取预警名称
function selectWarningList() {
  return request({
    url: `/ts-hrms/api/warningSetting/selectWarningList`,
    method: 'get'
  });
}

// 获取预警分类
function getGroupWarning() {
  return request({
    url: `/ts-hrms//api/warningSetting/selectGroupWarningSettingList`,
    method: 'get'
  });
}

// 获取预警列表
function warningRecord(data) {
  return request({
    url: `/ts-hrms/api/warningRecord/list`,
    method: 'get',
    data
  });
}

// 删除预警设置
function warningRecordDel(id) {
  return request({
    url: `/ts-hrms/api/warningSetting/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 操作 预警设置
function warningRecordDo(data, type) {
  return request({
    url: `/ts-hrms/api/warningSetting/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取进修规培-
function outRecord(data) {
  return request({
    url: `/ts-hrms/api/outRecord/list`,
    method: 'get',
    data
  });
}

// 获取进修规培-
function outRecordDo(data, type) {
  return request({
    url: `/ts-hrms/api/outRecord/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取进修规培-
function outRecordDel(id) {
  return request({
    url: `/ts-hrms/api/outRecord/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取延聘管理-人员列表
function getMyEmployeeList(params, data) {
  return request({
    url: '/ts-oa/employee/list',
    method: 'post',
    params: params,
    data: data
  });
}

// 获取延聘管理-人员列表
function getBasicMyEmployeeList(data) {
  return request({
    url: '/ts-basics-bottom/employee/getEmployeePageList',
    method: 'post',
    data: data
  });
}

// 获取同工同酬-人员选择列表
function getEmployeeListByEqualPay(data) {
  return request({
    url: '/ts-basics-bottom/employee/getEmployeeListByEqualPay',
    method: 'post',
    data: data
  });
}

// 获取延聘管理-人员新增更改
function personnelIncidentSave(data, type) {
  return request({
    url: `/ts-hrms/personnelIncident/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  });
}

// 获取延聘管理-审批
function incidentAudit(ids) {
  return request({
    url: `/ts-hrms/personnelIncident/incidentAudit/${ids}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取延聘管理-删除
function personnelIncidentDel(id) {
  return request({
    url: `/ts-hrms/personnelIncident/deletedById/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 停止返聘
function personnelIncidentStop(data) {
  return request({
    url: `/ts-hrms/personnelIncident/stop`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取延聘管理-人员新增更改
function personnelChangeSave(data, type) {
  return request({
    url: `/ts-hrms/personnelChange/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  });
}

// 退回调动
function personnelChangeCancel(id) {
  return request({
    url: `/ts-hrms/personnelChange/cancel/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 删除调动
function personnelChangeDel(id) {
  return request({
    url: `/ts-hrms/personnelChange/deletedById/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取延聘管理-审批
function personnelChangeIncidentAudit(ids) {
  return request({
    url: `/ts-hrms/personnelChange/incidentAudit/${ids}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 亡故
function personnelIncidentPassAway(ids) {
  return request({
    url: `/ts-hrms/personnelIncident/passAway/${ids}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 杏林人才
function oldTcmList(data) {
  return request({
    url: `/ts-hrms/api/oldTcm/list`,
    method: 'post',
    data
  });
}

// 杏林人才 新增 编辑
function oldTcmSave(data, type) {
  return request({
    url: `/ts-hrms/api/oldTcm/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  });
}

// 删除杏林人才
function oldTcmDel(id) {
  return request({
    url: `/ts-hrms/api/oldTcm/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 同工统筹
function equalPayList(data) {
  return request({
    url: `/ts-hrms/api/equalPay/list`,
    method: 'post',
    data
  });
}

// 同工统筹 新增 编辑
function equalPaySave(data, type) {
  return request({
    url: `/ts-hrms/api/equalPay/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: data
  });
}

// 获取职务列表
function positionList(data) {
  return request({
    url: `/ts-basics-bottom/position/getList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 转正管理
function beComeList(data) {
  return request({
    url: `/ts-hrms/api/beCome/list`,
    method: 'post',
    data
  });
}

// 转正管理
function beComeSave(data) {
  return request({
    url: `/ts-hrms/api/beCome/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 调动管理打印数据
function getTransferOrderData(data) {
  return request({
    url: `/ts-hrms/personnelChange/getTransferOrderData`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 人事流程-查询
function workflowById(wfInstid) {
  return request({
    url: `/ts-workflow/workflow/wfInst/info/businessId/${wfInstid}`,
    method: 'get'
  });
}

// 人事流程-查询
function workflowByInstId(wfInstid) {
  return request({
    url: `/ts-workflow/workflow/wfInst/info/${wfInstid}`,
    method: 'get'
  });
}

// 遗嘱管理-查询
function willManageList(data) {
  return request({
    url: `/ts-hrms/api/willManage/list`,
    method: 'get',
    data
  });
}

// 遗嘱管理-删除
function willManageDel(id) {
  return request({
    url: `/ts-hrms/api/willManage/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 遗嘱管理-操作
function willManageDo(data, type) {
  return request({
    url: `/ts-hrms/api/willManage/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 学历管理-查询
function educationInfoList(data) {
  return request({
    url: `/ts-hrms/educationInfo/getDataList`,
    method: 'post',
    data
  });
}

// 学历管理-根据ID 审批 取消审批 删除
function educationInfoDoById(id, type) {
  return request({
    url: `/ts-hrms/educationInfo/${type}/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 学历管理-操作  编辑 新增
function educationInfoDo(data, type) {
  return request({
    url: `/ts-hrms/educationInfo/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 职称聘任-查询
function jobtitleAppointList(data) {
  return request({
    url: `/ts-hrms/jobtitleAppoint/getDataList`,
    method: 'post',
    data
  });
}

// 职称聘任-根据ID 审批 取消审批 删除
function jobtitleAppointDoById(id, type) {
  return request({
    url: `/ts-hrms/jobtitleAppoint/${type}/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 职称聘任-操作  编辑 新增
function jobtitleAppointDo(data, type) {
  return request({
    url: `/ts-hrms/jobtitleAppoint/${type}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 职称聘任-职称类别
function getJobTitleCategoryList(data = {}) {
  return request({
    url: `/ts-basics-bottom/jobtitleBasic/getJobTitleCategoryList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 职称聘任-职称级别 职称名称
function getListByPid(data) {
  return request({
    url: `/ts-basics-bottom/jobtitleBasic/getListByPid`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 导入退休人员
function importRetirementData(data) {
  return request({
    url: '/ts-hrms/api/personnelIncident/retirement/import',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

export default {
  personnelIncidentList,
  getDictType,
  personnelChangeList,
  loadchangeselect,
  selectWarningList,
  getGroupWarning,
  warningRecord,
  outRecord,
  outRecordDo,
  outRecordDel,
  getMyEmployeeList,
  personnelIncidentSave,
  incidentAudit,
  personnelIncidentDel,
  personnelIncidentStop,
  getBasicMyEmployeeList,
  positionList,
  personnelChangeSave,
  personnelChangeCancel,
  personnelChangeDel,
  personnelChangeIncidentAudit,
  personnelIncidentPassAway,
  loadchangeList,
  oldTcmList,
  oldTcmSave,
  oldTcmDel,
  equalPayList,
  equalPaySave,
  warningRecordDel,
  warningRecordDo,
  beComeList,
  beComeSave,
  getTransferOrderData,
  workflowById,
  getEmployeeListByEqualPay,
  willManageList,
  willManageDel,
  willManageDo,
  educationInfoList,
  educationInfoDoById,
  educationInfoDo,
  jobtitleAppointList,
  jobtitleAppointDoById,
  jobtitleAppointDo,
  getJobTitleCategoryList,
  getListByPid,
  importRetirementData,
  workflowByInstId
};
