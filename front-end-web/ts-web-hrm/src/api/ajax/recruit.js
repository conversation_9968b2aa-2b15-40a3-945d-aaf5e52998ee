import { request } from '@/api/ajax';

// 招聘列表
function recruitPlanGetList(params) {
  return request({
    url: '/ts-hrms/recruitPlan/getList',
    method: 'post',
    params
  });
}

// 新增招聘
function recruitPlanAdd(data) {
  return request({
    url: '/ts-hrms/recruitPlan/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 修改招聘
function recruitPlanEdit(data) {
  return request({
    url: '/ts-hrms/recruitPlan/edit',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 发布招聘
function recruitRelease(id) {
  return request({
    url: '/ts-hrms/recruitPlan/release?id=' + id,
    method: 'post'
  });
}

// 取消发布
function cancelRelease(id) {
  return request({
    url: '/ts-hrms/recruitPlan/cancelRelease?id=' + id,
    method: 'post'
  });
}

// 结束招聘
function recruitPlanEnd(id) {
  return request({
    url: '/ts-hrms/recruitPlan/end?id=' + id,
    method: 'post'
  });
}

// 删除
function recruitPlanDel(params) {
  return request({
    url: '/ts-hrms/recruitPlan/del',
    method: 'post',
    params
  });
}

//招聘设置列表
function zpapiSettingList(params) {
  return request({
    url: '/ts-hrms/zpapi/jobDescription/list',
    method: 'get',
    params
  });
}

//招聘设置 详情
function zpapiSettingDetails(id) {
  return request({
    url: `/ts-hrms/zpapi/procedure/${id}`,
    method: 'get'
  });
}

//招聘设置 删除
function zpapiSettingDel(id) {
  return request({
    url: `/ts-hrms/zpapi/procedure/delete/${id}`,
    method: 'post'
  });
}

//招聘设置新增
function zpapiSettingSave(data) {
  return request({
    url: '/ts-hrms/zpapi/procedure/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

//招聘设置编辑
function zpapiSettingUpdate(data) {
  return request({
    url: '/ts-hrms/zpapi/procedure/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 招聘计划详情 岗位详情列表数据
function recruitPlanPostGetList(data) {
  return request({
    url: '/ts-hrms/recruitPlanPost/getList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取招聘设置 流程排序
function recruitByprocedureid(id) {
  return request({
    url: `/ts-hrms/zpapi/flow/byprocedureid/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取招聘计划 岗位下的报名列表
function recruitSignUpGetList(params) {
  return request({
    url: `/ts-hrms/recruitSignUp/getList`,
    method: 'post',
    params
  });
}

// 获取招聘计划 审查的岗位报名统计
function recruitSignUpSignUpCount(data) {
  return request({
    url: `/ts-hrms/recruitSignUp/signUpCount`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取学历字典
function recruitPlanGetEducationPage() {
  return request({
    url: `/ts-hrms/recruitPlan/getEducationPage`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取笔试环节 列表数据
function recruitSignUpExamGetList(data) {
  return request({
    url: `/ts-hrms/recruitSignUpExam/getList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取笔试环节 各阶段数据
function recruitSignUpExamSignUpExamCount(data) {
  return request({
    url: `/ts-hrms/recruitSignUpExam/signUpExamCount`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 免试
function recruitSignUpExamExemption(data) {
  return request({
    url: `/ts-hrms/recruitSignUpExam/exemption`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 笔试 导入准考证信息
function recruitSignUpExamSave(data) {
  return request({
    url: `/ts-hrms/recruitSignUpExam/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 笔试 导入成绩
function recruitSignUpExamImportScore(data) {
  return request({
    url: `/ts-hrms/recruitSignUpExam/importScore`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data;'
    },
    data
  });
}

// 获取面试环节 列表数据
function recruitSignUpInterviewList(params) {
  return request({
    url: `/ts-hrms/recruitSignUpInterview/getList`,
    method: 'post',
    params
  });
}

// 获取面试 各阶段数据 统计
function recruitSignUpInterviewCount(data) {
  return request({
    url: `/ts-hrms/recruitSignUpInterview/signUpInterviewCount`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 面试导入准考信息
function recruitSignUpInterviewSave(data) {
  return request({
    url: `/ts-hrms/recruitSignUpInterview/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 面试导入成绩
function recruitSignUpInterviewImportScore(data) {
  return request({
    url: `/ts-hrms/recruitSignUpInterview/importScore`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取实操 列表数据
function recruitSignUpManipulateGetList(data) {
  return request({
    url: `/ts-hrms/recruitSignUpManipulate/getList`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 获取实操 各阶段数据
function recruitSignUpManipulateSignUpExamCount(data) {
  return request({
    url: `/ts-hrms/recruitSignUpManipulate/signUpExamCount`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 实操 导入准考证信息
function recruitSignUpManipulateSave(data) {
  return request({
    url: `/ts-hrms/recruitSignUpManipulate/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 实操 导入成绩
function recruitSignUpManipulateImportScore(data) {
  return request({
    url: `/ts-hrms/recruitSignUpManipulate/importScore`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data;'
    },
    data
  });
}

// 获取模版名称
function getTemplateName() {
  return request({
    url: `/ts-hrms/recruitWebSite/get`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 初审
function firstAuditPass(data) {
  return request({
    url: `/ts-hrms/recruitSignUp/firstAuditPass`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 复审
function reviewAuditPass(data) {
  return request({
    url: `/ts-hrms/recruitSignUp/reviewAuditPass`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// 审查不通过
function recruitSignUpFail(data) {
  return request({
    url: `/ts-hrms/recruitSignUp/fail`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
}

// jk报名接口
export const userJKSignUpApi = function(data) {
  return request({
    method: 'POST',
    url: `/ts-hrms/recruit/newSignUp`,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 修改报名接口 jk
export const editSignUpJk = function(data) {
  return request({
    method: 'post',
    url: '/ts-hrms/recruit/newEditSignUp',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 修改人才库 jk
export const talentpoolUpdate = function(data) {
  return request({
    method: 'post',
    url: '/ts-hrms/talentpool/update',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 获取招聘审查权限
export const recruitPlanGetPower = function(data) {
  return request({
    method: 'post',
    url: '/ts-hrms/recruitPlan/getPower',
    data
  });
};

// 面试  免试
export const recruitSignUpInterviewExemption = function(data) {
  return request({
    url: `/ts-hrms/recruitSignUpInterview/exemption`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 实操 免试
export const recruitSignUpManipulateExemption = function(data) {
  return request({
    method: 'post',
    url: '/ts-hrms/recruitSignUpManipulate/exemption',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  });
};

// 获取笔试自定义环节
export const stepByFlowId = function(id) {
  return request({
    method: 'get',
    url: `/ts-hrms/zpapi/step/byFlowId/${id}`
  });
};

// 获取学历
export const getEducationList = function() {
  return request({
    method: 'post',
    url: `/ts-hrms/recruit/getEducationList`
  });
};

// 获取职称
export const getjobtitleList = function() {
  return request({
    method: 'post',
    url: `/ts-hrms/recruit/getjobtitleList`
  });
};

// 报名进度查询
export const readSignUpProgress = function(data) {
  return request({
    method: 'post',
    url: '/ts-hrms/recruit/readSignUpProgress',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

// 招聘报表 招聘数据
export const zpglReportDataList = function(params) {
  return request({
    method: 'get',
    url: '/ts-hrms/api/zpglReport/dataList',
    params
  });
};

// 招聘报表 招聘指标
export const zpglReportIndexList = function(params) {
  return request({
    method: 'get',
    url: '/ts-hrms/api/zpglReport/indexList',
    params
  });
};

export default {
  recruitPlanGetList,
  recruitPlanAdd,
  recruitPlanEdit,
  cancelRelease,
  recruitRelease,
  recruitPlanEnd,
  recruitPlanDel,
  zpapiSettingList,
  zpapiSettingSave,
  zpapiSettingUpdate,
  zpapiSettingDel,
  zpapiSettingDetails,
  recruitPlanPostGetList,
  recruitByprocedureid,
  recruitSignUpGetList,
  recruitSignUpSignUpCount,
  recruitPlanGetEducationPage,
  recruitSignUpExamGetList,
  recruitSignUpExamSignUpExamCount,
  recruitSignUpExamExemption,
  recruitSignUpExamSave,
  recruitSignUpExamImportScore,
  recruitSignUpInterviewList,
  recruitSignUpInterviewCount,
  recruitSignUpInterviewSave,
  recruitSignUpInterviewImportScore,
  recruitSignUpManipulateGetList,
  recruitSignUpManipulateSignUpExamCount,
  recruitSignUpManipulateSave,
  recruitSignUpManipulateImportScore,
  getTemplateName,
  firstAuditPass,
  reviewAuditPass,
  recruitSignUpFail,
  userJKSignUpApi,
  recruitPlanGetPower,
  recruitSignUpInterviewExemption,
  recruitSignUpManipulateExemption,
  talentpoolUpdate,
  stepByFlowId,
  getEducationList,
  getjobtitleList,
  readSignUpProgress,
  editSignUpJk,
  zpglReportDataList,
  zpglReportIndexList
};
