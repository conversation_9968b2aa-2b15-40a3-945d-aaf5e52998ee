<template>
  <div class="human-resource-planning">
    <!-- 左侧职称分类树 -->
    <div class="left-tree-panel">
      <new-base-search-tree
        ref="newBaseSearchTree"
        title="组织机构"
        :activeId="activeTreeNodeId"
        :apiFunction="getOrganizationTree"
        placeholder="请输入组织机构名称"
        @tree="handleTree"
        @nodeClick="handleNodeClick"
        @clearInput="handleResetSearchTree"
      />
    </div>

    <div class="right-content-panel">
      <ts-search-bar
        v-model="searchForm"
        :actions="actions"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        @reset="handleResetSearchForm"
        :resetData="{}"
      >
        <template slot="right">
          <ts-button type="primary" @click="handleImport">
            导入
          </ts-button>
          <ts-button type="primary" @click="handleExport">
            导出
          </ts-button>
        </template>
      </ts-search-bar>

      <div class="table-content">
        <vxe-table
          v-if="tableColumns.length > 0"
          width="100%"
          height="100%"
          ref="xTable"
          :data="tableData"
          border
          auto-resize
          :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
          keep-source
        >
          <vxe-column type="seq" width="55px" align="center" fixed="left" />
          <vxe-column
            field="OrgName"
            minWidth="140px"
            title="组织机构"
            align="center"
            fixed="left"
          />
          <vxe-column
            field="bedNum"
            width="70px"
            title="床位"
            align="center"
            :edit-render="{
              autofocus: '.el-input__inner',
              placeholder: '请输入'
            }"
          >
            <template #edit="{ row }">
              <ts-input
                class="input_65px"
                v-model="row.bedNum"
                placeholder="请输入"
                @input="
                  value => (row.bedNum = (value.match(/\d+/g) || [''])[0])
                "
                :maxlength="5"
                v-enter-next-input
              />
            </template>
          </vxe-column>

          <vxe-colgroup title="岗位定编人数" align="center">
            <vxe-column
              v-for="column in tableColumns"
              :key="column.field"
              :field="column.field"
              :title="column.title"
              width="80px"
              align="center"
              :edit-render="{
                autofocus: '.el-input__inner',
                placeholder: '请输入'
              }"
            >
              <template #edit="{ row }">
                <ts-input
                  class="input_65px"
                  v-model="row[column.field]"
                  placeholder="请输入"
                  @input="
                    value =>
                      (row[column.field] = (value.match(/\d+/g) || [''])[0])
                  "
                  :maxlength="5"
                  v-enter-next-input
                />
              </template>
            </vxe-column>
          </vxe-colgroup>

          <vxe-column width="100px" title="操作" align="center" fixed="right">
            <template #default="{ row }">
              <div v-if="row.edit" class="edit-btn-group">
                <ts-button type="text" @click="handleSubmit(row)">
                  保存
                </ts-button>
                <ts-button
                  class="cancel"
                  type="text"
                  @click="handleCancel(row)"
                >
                  取消
                </ts-button>
              </div>
              <ts-button v-else type="text" @click="handleEdit(row)">
                编辑
              </ts-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>

    <diaolog-import-data
      ref="diaologImportData"
      @refresh="handleRefreshTable"
    />
  </div>
</template>
<script>
import diaologImportData from './diaolog-import-data.vue';
import enterNextInput from '@/directive/enterNextInput.js';

export default {
  components: {
    diaologImportData
  },
  directives: {
    enterNextInput
  },
  data() {
    return {
      searchForm: {},
      actions: [],
      searchList: [
        {
          label: '组织机构名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入组织机构名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],

      tableData: [],
      tableColumns: [],

      activeTreeNodeId: '',
      firstId: ''
    };
  },
  methods: {
    handleTree(treeData) {
      if (!treeData.length > 0) return;
      // 页面第一次加载时，默认选中第一个节点
      if (!this.activeTreeNodeId) {
        this.firstId = treeData[0].id;
        this.activeTreeNodeId = treeData[0].id;
      }
      this.handleRefreshTable('init');
    },

    handleResetSearchTree() {
      this.activeTreeNodeId = this.firstId;
      this.handleRefreshTable('init');
    },

    handleResetSearchForm() {
      this.activeTreeNodeId = this.firstId;

      this.$refs.newBaseSearchTree.searchVal = '';
      this.$refs.newBaseSearchTree.clearInput();
      this.handleRefreshTable('init');
    },

    refresh() {
      // 页面refresh事件
      // 如果未选中节点，则不刷新表格 （在tree加载完执行）
      if (!this.activeTreeNodeId) return;

      this.$nextTick(() => {
        this.$refs.newBaseSearchTree.getTreeData();
      });
    },

    async getOrganizationTree(params = {}) {
      try {
        const res = await this.ajax.organizationZTreeList(params);
        return res;
      } catch (error) {
        console.error('获取职称分类树失败:', error);
        return { success: false, data: [] };
      }
    },

    handleNodeClick(treeNode) {
      if (treeNode.id === this.activeTreeNodeId) {
        this.handleRefreshTable();
      } else {
        this.activeTreeNodeId = treeNode.id;
        this.handleRefreshTable('init');
      }
    },

    search() {
      this.handleRefreshTable();
    },

    handleEdit(row) {
      if (this.tableData.some(item => item.edit)) {
        this.$newMessage('warning', '请先保存或关闭已打开编辑的行');
        return;
      }

      row.edit = true;
      const $table = this.$refs.xTable;
      $table.setEditRow(row);
    },

    handleCancel(row) {
      row.edit = false;
      const $table = this.$refs.xTable;
      $table.clearEdit().then(() => {
        $table.revertData(row);
      });
    },

    async handleSubmit(row) {
      row.edit = false;
      await this.$refs.xTable.clearEdit();
      let data = {
        bedNum: row.bedNum,
        orgId: row.OrgId,
        personalIdentityNumList: []
      };
      Object.keys(row).forEach(key => {
        if (key.includes('personalIdentity')) {
          data.personalIdentityNumList.push({
            personalIdentityCode: key,
            num: row[key]
          });
        }
      });

      let res = await this.ajax.organizationPlanOrganizationAllocationSaveNum(
        data
      );
      if (res.success == false) {
        this.$newMessage('error', res.message || '保存失败');
        return;
      }
      this.$newMessage('success', '保存成功');
      this.handleRefreshTable();
    },

    handleImport() {
      this.$refs.diaologImportData.open({
        data: {}
      });
    },

    handleExport() {
      let aDom = document.createElement('a');
      aDom.href = '/ts-basics-bottom/organizationAllocation/export';
      aDom.click();
    },

    async handleRefreshTable(type) {
      if (type == 'init') this.tableColumns = [];
      this.tableData = [];
      let searchForm = {
        parentId: this.activeTreeNodeId,
        ...this.searchForm
      };
      this.ajax
        .organizationPlanOrganizationAllocationGetList(searchForm)
        .then(res => {
          if (res.success == false) {
            this.$newMessage('error', res.message || '列表数据获取失败');
            return;
          }
          if (res.object?.head && Object.keys(res.object?.head).length > 0) {
            this.tableColumns = Object.entries(res.object?.head)
              .map(([key, value]) => {
                return {
                  field: key,
                  title: value
                };
              })
              .filter(item => item.field.includes('personalIdentity'));
          }
          this.tableData = (res.object?.body || []).map(item => {
            return {
              ...item,
              edit: false
            };
          });
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.human-resource-planning {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  overflow: hidden;

  .left-tree-panel {
    width: 216px;
    min-width: 216px;
    margin-right: 8px;
    height: 100%;
    border-right: 1px solid #e6e6e6;
    background-color: #fafafa;
  }

  .right-content-panel {
    border-radius: 4px;
    border: 1px solid#295cf9;
    padding: 8px;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .table-content {
      flex: 1;
      overflow: hidden;
    }

    ::v-deep {
      .input_65px {
        min-width: 65px;
        width: 65px;

        .el-input__inner {
          min-width: 65px;
          width: 65px;
        }
      }

      .vxe-cell--edit-icon {
        display: none;
      }

      .edit-btn-group {
        display: flex;
        justify-content: center;
        align-items: center;

        .ts-button {
          min-width: 30px !important;
          padding: 0 !important;
          height: 20px !important;

          &.cancel {
            color: rgb(223, 59, 59);
          }
        }
      }
    }
  }
}
</style>
