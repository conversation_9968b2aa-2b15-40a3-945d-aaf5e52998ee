<template>
  <div class="position-level-container">
    <div class="layout-wrapper">
      <!-- 左侧导航 -->
      <div class="left-sidebar">
        <div class="sidebar-title">
          <span>岗位类别</span>
          <vxe-icon
            class="add-icon"
            name="square-plus"
            status="primary"
            @click="handleAddCategory"
          />
        </div>
        <div class="category-list">
          <div
            v-for="category in categoryList"
            :key="category.postCategoryId"
            class="category-item"
            :class="{ active: activeCategory === category.postCategoryId }"
            @click="handleCategoryChange(category.postCategoryId)"
          >
            <div
              class="category-name"
              :class="{ 'is-enable': category.isEnable !== '1' }"
            >
              {{ category.postCategoryName }}
            </div>
            <div class="category-operate">
              <i
                class="el-icon-edit"
                @click.stop="handleEditCategory(category)"
              />
              <!-- <vxe-switch
                :value="category.isEnable"
                openValue="1"
                closeValue="0"
                openLabel="启用"
                closeLabel="禁用"
                @change="e => handleChangeStatus(category, e)"
              /> -->
              <img
                @click="handleChangeStatus(category)"
                class="category-operate-item"
                v-if="category.isEnable === '1'"
                src="@/assets/img/organization/systemGroupOpen.svg"
                alt="禁用"
              />
              <img
                @click="handleChangeStatus(category)"
                class="category-operate-item"
                v-else
                src="@/assets/img/organization/systemGroupDisable.svg"
                alt="启用"
              />
              <i
                class="el-icon-delete"
                @click.stop="handleDeleteCategory(category)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧主内容 -->
      <div class="right-content">
        <ts-search-bar
          v-model="searchForm"
          :actions="actions"
          :formList="searchList"
          :elementCol="14"
          @search="search"
          :resetData="{}"
        >
          <template slot="right">
            <ts-button
              class="shallowButton"
              type="primary"
              @click="handleAddLevel"
            >
              新增岗位等级
            </ts-button>
          </template>
        </ts-search-bar>

        <TsVxeTemplateTable
          id="table_position_level"
          class="form-table set-sort-position-level-table"
          ref="table"
          :defaultSort="{
            sidx: 'create_date',
            sord: 'desc'
          }"
          :columns="columns"
          @refresh="handleRefreshTable"
          disabledRowField="isEnable"
          disabledRowValue="0"
        />
      </div>

      <dialog-add-category ref="DialogAddCategory" @ok="refresh" />
      <dialog-add-category-level
        :categoryList="categoryList"
        ref="DialogAddCategoryLevel"
        @ok="refresh"
      />
    </div>
  </div>
</template>

<script>
import Sortable from 'sortablejs';
import { deepClone } from '@/unit/commonHandle.js';
import table from './table.js';
import { primaryBlue } from '@/styles/variables.scss';
import DialogAddCategory from './dialog-add-category.vue';
import DialogAddCategoryLevel from './dialog-add-category-level.vue';
export default {
  mixins: [table],
  name: 'PositionLevel',
  components: { DialogAddCategory, DialogAddCategoryLevel },
  data() {
    return {
      categoryList: [],
      activeCategory: '',
      sortableInstance: null
    };
  },
  methods: {
    async refresh() {
      if (this.sortableInstance) {
        this.sortableInstance.destroy();
        this.sortableInstance = null;
      }
      this.handleSetSort();
      await this.getCategoryList();
      this.search();
    },

    // 获取岗位类别
    async getCategoryList() {
      const res = await this.ajax.organizationPlanPostCategoryList();
      if (!res.success) {
        this.$newMessage('error', res.message || '【获取岗位类别】失败!');
        return;
      }
      this.categoryList = res.object;
      if (this.categoryList.length > 0 && !this.activeCategory) {
        this.activeCategory = this.categoryList[0].postCategoryId;
      }
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      this.tableData = [];
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          postCategory: this.activeCategory,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      this.ajax.organizationPlanPostList(searchForm).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '列表数据获取失败');
          return;
        }
        this.tableData = res.rows;
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },

    async handleChangeStatus(category) {
      // e.$event.stopPropagation();
      try {
        const { postCategoryId, postCategoryName, isEnable } = category;
        const isDisabling = isEnable === '1';
        const text = isDisabling ? '禁用' : '启用';
        const color = isDisabling ? 'red' : primaryBlue;
        const enable = isDisabling ? '0' : '1';

        if (isDisabling) {
          const verifyRes = await this.ajax.organizationPlanPostCategoryVerifyEnable(
            {
              postCategoryId
            }
          );

          if (!verifyRes.success) {
            this.$newMessage('error', verifyRes.message || '不能禁用');
            return;
          }
        }

        const confirmMessage = `是否【<span style="color: ${color}">${text}</span>】${postCategoryName}这条岗位类别数据？`;
        await this.$newConfirm(confirmMessage);

        const res = await this.ajax.organizationPlanPostCategoryEnable({
          dictTypeId: category.dictTypeId,
          postCategoryId,
          enable
        });

        if (res.success) {
          this.$newMessage('success', `${text}成功`);
          this.refresh();
        } else {
          this.$newMessage('error', res.message || `${text}失败`);
        }
      } catch (e) {
        console.error(e);
      }
    },

    handleAddCategory() {
      this.$refs.DialogAddCategory.open({
        type: 'add',
        data: {}
      });
    },

    handleEditCategory(data) {
      this.$refs.DialogAddCategory.open({ type: 'edit', data });
    },

    async handleDeleteCategory(category) {
      try {
        const verifyRes = await this.ajax.organizationPlanPostCategoryVerifyDel(
          {
            dictTypeId: category.dictTypeId,
            postCategoryId: category.postCategoryId
          }
        );
        if (!verifyRes.success) {
          this.$newMessage('error', verifyRes.message || '不能删除');
          return;
        }
        await this.$newConfirm(
          `【<span style="color: red">删除</span>】${category.postCategoryName}这条岗位类别数据？`
        );

        const res = await this.ajax.organizationPlanPostCategoryDel({
          dictTypeId: category.dictTypeId,
          postCategoryId: category.postCategoryId
        });
        if (!res.success) return this.$newMessage('error', '【删除】失败');
        if (category.postCategoryId === this.activeCategory) {
          this.activeCategory = '';
        }
        this.$newMessage('success', '【删除】成功');
        this.refresh();
      } catch (e) {
        console.error(e);
      }
    },

    // 左侧类别切换
    handleCategoryChange(categoryId) {
      if (categoryId === this.activeCategory) return;

      this.activeCategory = categoryId;
      this.search();
    },

    // 新增岗位等级
    handleAddLevel() {
      this.$refs.DialogAddCategoryLevel.open({
        type: 'add',
        data: {
          postCategory: this.activeCategory
        }
      });
    },

    // 编辑岗位等级
    handleEditLevel(data) {
      this.$refs.DialogAddCategoryLevel.open({ type: 'edit', data });
    },

    async handleChangeLevelStatus(row) {
      try {
        const { postId, postName, isEnable } = row;
        const isDisabling = isEnable === '1';
        const text = isDisabling ? '禁用' : '启用';
        const color = isDisabling ? 'red' : primaryBlue;
        const enable = isDisabling ? '0' : '1';

        if (isDisabling) {
          let VerifyAPI = this.ajax.organizationPlanPostVerifyEnable;
          const verifyRes = await VerifyAPI({ postId });
          if (!verifyRes.success) {
            this.$newMessage('error', verifyRes.message || '不能禁用');
            return;
          }
        }

        await this.$newConfirm(
          `是否【<span style="color: ${color}">${text}</span>】${postName}这条岗位等级数据1？`
        );

        const res = await this.ajax.organizationPlanPostEnable({
          dictTypeId: row.dictTypeId,
          postId,
          enable
        });

        if (res.success) {
          this.$newMessage('success', `${text}成功`);
          this.handleRefreshTable();
        } else {
          this.$newMessage('error', res.message || `${text}失败`);
        }
      } catch (e) {
        console.error(e);
      }
    },

    // 删除岗位等级
    async handleDeleteLevel(level) {
      try {
        const verifyRes = await this.ajax.organizationPlanPostVerifyDel({
          postId: level.postId
        });
        if (!verifyRes.success) {
          this.$newMessage('error', verifyRes.message || '不能删除');
          return;
        }

        await this.$newConfirm(
          `【<span style="color: red">删除</span>】${level.postName}这条岗位等级数据2？`
        );

        const res = await this.ajax.organizationPlanPostDel({
          dictTypeId: level.dictTypeId,
          postId: level.postId,
          enable: '0'
        });
        if (!res.success) return this.$newMessage('error', '【删除】失败');
        this.$newMessage('success', '【删除】成功');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    handleSetSort() {
      const _this = this;

      if (this.sortableInstance) {
        this.sortableInstance.destroy();
        this.sortableInstance = null;
      }

      const tbody = document.querySelector(
        '.set-sort-position-level-table tbody'
      );

      if (!tbody) return;

      this.sortableInstance = Sortable.create(tbody, {
        async onEnd({ newIndex, oldIndex }) {
          if (newIndex == oldIndex) return;

          let sortTable = deepClone(_this.tableData);

          const currRow = sortTable.splice(oldIndex, 1)[0];
          sortTable.splice(newIndex, 0, currRow);

          sortTable = sortTable.map((m, i) => {
            return {
              postId: m.postId,
              sortNo: String(i + 1)
            };
          });

          _this.loading = true;
          const res = await _this.ajax.organizationPlanPostUpdateSort(
            sortTable
          );
          _this.loading = false;
          if (res.success) {
            _this.$refs.table.refresh({
              rows: []
            });

            await _this.handleRefreshTable();
          } else {
            this.$message.error(res.message || '获取数据失败!');
          }
        }
      });
    }
  },

  beforeDestroy() {
    if (this.sortableInstance) {
      this.sortableInstance.destroy();
      this.sortableInstance = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.position-level-container {
  width: 100%;
  height: 100%;
  background: #fff;

  .layout-wrapper {
    background: #fff;
    display: flex;
    height: 100%;
    gap: 8px;
  }

  // 左侧导航样式
  .left-sidebar {
    width: 270px;
    background: white;
    border-radius: 4px;
    border: 1px solid $primary-blue;

    .sidebar-title {
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      span {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);

        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 16px;
          background: $primary-blue;
          transform: translateY(2px);
          margin-right: 4px;
          border-radius: 4px;
        }
      }

      .add-icon {
        font-size: 16px;
        cursor: pointer;
        color: $primary-blue;
      }
    }

    .category-list {
      padding-top: 8px;
      .category-item {
        display: flex;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: 1px solid #f0f0f0;
        padding: 0px 8px;

        .category-name {
          flex: 1;
          display: flex;
          align-items: center;
          padding: 6px 8px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.65);
          border-radius: 4px;

          &.is-enable {
            color: red !important;
          }
        }

        .category-operate {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .category-operate-item {
            cursor: pointer;
            margin-left: 0px;
            margin-right: 4px;
            width: 16px;
            height: 16px;
            transform: translateY(1px);
          }

          i {
            margin-right: 4px;
            cursor: pointer;
            font-size: 16px;
            &.el-icon-edit {
              margin-left: 4px;
              color: $primary-blue;
            }
            &.el-icon-delete {
              color: $error-color;
            }
          }
        }

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          .category-name {
            background: $primary-blue;
            font-weight: 500;
            color: #fff;
          }
        }
      }
    }
  }

  // 右侧内容区域
  .right-content {
    flex: 1;
    background: white;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
  }
}
</style>
