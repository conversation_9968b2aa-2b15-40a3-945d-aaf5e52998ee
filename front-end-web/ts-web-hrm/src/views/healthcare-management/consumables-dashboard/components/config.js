import XLSX from 'xlsx-js-style';

/**
 * @param dataList 表格数据内容  array
 * @param fileName 文件标题。必须以 .xlsx结尾
 */
export const downloadXlsx = (dataList, fileName, merges, cols, rows) => {
  const stringToBuff = str => {
    let buf = new ArrayBuffer(str.length);
    let view = new Uint8Array(buf);
    for (let i = 0; i !== str.length; ++i) {
      view[i] = str.charCodeAt(i) & 0xff;
    }
    return buf;
  };
  // 创建表格
  let workbook = XLSX.utils.book_new();
  let worksheet = XLSX.utils.json_to_sheet(dataList, { skipHeader: true });
  worksheet['!merges'] = merges;
  worksheet['!cols'] = cols;
  worksheet['!rows'] = rows;
  let length = merges[1].s.r + 1;
  for (const key in worksheet) {
    if (worksheet[key] instanceof Object) {
      worksheet[key].s = {
        alignment: {
          vertical: 'center',
          horizontal: 'center',
          wrapText: true
        },
        font: {
          name: '宋体',
          sz: 10,
          color: { rgb: '#FF000000' },
          bold: false
        },
        border: {
          top: { style: 'thin' },
          bottom: { style: 'thin' },
          left: { style: 'thin' },
          right: { style: 'thin' }
        }
      };
    }
  }
  addBorder(worksheet, cols, length);
  XLSX.utils.book_append_sheet(workbook, worksheet); //第三个参数是sheet名称
  // 创建二进制对象写入转换好的字节流
  let xlsxBlob = new Blob(
    [
      stringToBuff(
        XLSX.write(workbook, {
          bookType: 'xlsx',
          bookSST: false,
          type: 'binary'
        })
      )
    ],
    { type: '' }
  );

  const a = document.createElement('a');
  // 利用URL.createObjectURL()方法为a元素生成blob URL
  a.href = URL.createObjectURL(xlsxBlob); // 创建对象超链接
  a.download = fileName;
  a.click();
};

function addBorder(ws, cols, length) {
  let colsZ = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ];
  cols.forEach((e, index) => {
    let style = {
      t: 's',
      v: '',
      s: {
        border: {
          top: { style: 'thin' },
          bottom: { style: 'thin' },
          left: { style: 'thin' },
          right: { style: 'thin' }
        }
      }
    };
    ws[`${colsZ[index]}1`] = ws[`${colsZ[index]}1`] || style;
    ws[`${colsZ[index]}${length}`] = ws[`${colsZ[index]}${length}`] || style;
  });
}
