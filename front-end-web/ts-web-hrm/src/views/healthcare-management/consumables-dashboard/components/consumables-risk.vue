<template>
  <div class="drug-risk-warning flex-column borders">
    <colmun-head title="耗材风险(TOP1)" :background="background">
      <template slot="right">
        <ts-button class="shallowButton">下载</ts-button>
      </template>
    </colmun-head>
    <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
      <ul class="data-list">
        <li
          class="data-item"
          v-for="(item, index) in eventWarningList"
          :key="index"
        >
          <div class="data-item-left">
            <img
              class="data-item-img"
              src="@/assets/img/home-page/hrm-home-page-file.svg"
              alt=""
            />
            <span class="data-item-label">
              {{ item.key1 }}
            </span>
          </div>
          <p class="data-item-label-right">【{{ item.key2 }}】</p>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    async refresh() {}
  }
};
</script>

<style lang="scss" scoped>
.drug-risk-warning {
  flex: 1;
  margin-right: 4px;
  /deep/ {
    .data-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .data-item {
        padding: 0 8px;
        height: 36px;
        line-height: 36px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        &:nth-child(2n - 1) {
          background-color: #fcf2f1;
        }
        .data-item-left {
          .data-item-img {
            width: 20px;
            margin-right: 4px;
          }
          .data-item-label {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
          }
        }
        .data-item-label-right {
          display: flex;
          justify-content: space-around;
          margin: 0;
          line-height: 36px;
          align-items: center;
          .cellDot {
            text-align: center;
            height: 20px;
            width: 20px;
            line-height: 20px;
            font-size: 12px;
            background: $primary-blue;
            color: #fff;
            border-radius: 12px;
          }
          .cellValue {
            margin-left: 4px;
            line-height: 24px;
            margin-right: 4px;
          }
        }
        .data-item-val {
          font-weight: 700;
          color: #f96c17;
          margin-right: 4px;
          line-height: 36px;
        }
      }
    }
  }
}
</style>
