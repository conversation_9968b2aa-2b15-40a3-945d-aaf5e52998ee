import { exportTemplateWord, numberToChinese } from '@/unit/exportTemplate.js';
import axios from 'axios';
import config from './config';
export default {
  mixins: [config],
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'searchKey',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '搜索患者/住院号/门诊号/床号'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '患者类型',
          value: 'patientType',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [],
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'seq',
          align: 'center',
          width: 40
        },
        {
          label: '状态',
          prop: 'compensateStatus',
          width: 80,
          align: 'center',
          render: (h, { row }) => {
            let statusObj = {
              0: {
                label: '待交班',
                class: 'yellow'
              },
              1: {
                label: '待接班',
                class: 'red'
              },
              2: {
                label: '已接班',
                class: ''
              }
            };
            let compensateStatusObj = {
              0: '',
              1: '(补)'
            };
            let cellVal = `${statusObj[row.recordStatus].label}${
              compensateStatusObj[row.compensateStatus]
            }`;
            return h(
              'span',
              { class: `${statusObj[row.recordStatus].class}` },
              cellVal
            );
          }
        },
        {
          label: '患者类型',
          prop: 'patientType',
          minWidth: 120,
          align: 'center',
          render: (h, { row }) => {
            let index = this.hasTypeColor(row);
            let list = index.map(e => {
              return h(
                'p',
                {
                  class: 'nameItem',
                  style: `background: ${this.typeColor[e].color}`
                },
                this.typeColor[e].name
              );
            });
            return h('div', { class: 'flex-cell' }, [...list]);
          }
        },
        {
          label: '住院号',
          prop: 'patnNo',
          showOverflow: 'tooltip',
          width: 100,
          align: 'center'
        },
        {
          label: '床号',
          showOverflow: 'tooltip',
          prop: 'bedNo',
          width: 100,
          align: 'center'
        },
        {
          label: '患者姓名',
          prop: 'patnName',
          showOverflow: 'tooltip',
          minWidth: 100,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetails(row) }
              },
              row.patnName
            );
          }
        },
        {
          label: '性别',
          prop: 'patnSex',
          showOverflow: 'tooltip',
          width: 60,
          align: 'center',
          render: (h, { row }) => {
            let sexName =
              row.patnSex == 1 ? '男' : row.patnSex == '2' ? '女' : '未知';
            return h('span', {}, sexName);
          }
        },
        {
          label: '年龄',
          prop: 'patnAge',
          showOverflow: 'tooltip',
          width: 60,
          align: 'center'
          // render: (h, { row }) => {
          //   let age = commonUtils.calculateAge(row.birthday);
          //   return h('span', {}, age);
          // },
        },
        {
          label: '身份证号',
          prop: 'idCard',
          width: 180,
          align: 'center'
        },
        {
          label: '联系方式',
          prop: 'phoneNumber',
          showOverflow: 'tooltip',
          width: 120,
          align: 'center'
        },
        {
          label: '入院时间',
          prop: 'inDate',
          showOverflow: 'tooltip',
          width: 180,
          align: 'center',
          render: (h, { row }) => {
            return h('span', {}, row.inDate.replace('T', ' '));
          }
        },
        {
          label: '交班医师',
          prop: 'handoverDoctorName',
          width: 100,
          align: 'center'
        },
        {
          label: '归属院区',
          prop: 'areaText',
          width: 100,
          align: 'center'
        },
        {
          label: '归属科室',
          prop: 'handoverOrgName',
          width: 120,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 60,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '查看',
                event: this.handleDetails
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      imageSize: {
        Logo: [341, 341]
      }
    };
  },
  computed: {
    globalSetting() {
      return this.$getParentStoreInfo('globalSetting');
    }
  },
  methods: {
    async search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleDetails(data) {
      this.$refs.darwerHandover.open({
        data,
        type: 'details',
        title: '查看',
        MedShiftRecord: { id: this.recordId }
      });
    },
    handleGetSeacrhQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          id: this.recordId,
          status: 1,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageNo, pageSize };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let API = this.ajax.getMedShiftRecordToDoList;
      let res = await API(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    // 导出交接班文档
    async handleExport() {
      let res = await this.ajax.getMedShiftRecordDetails(this.recordId);
      if (!res.object) {
        this.$newMessage('error', '获取数据异常');
        return;
      }
      let res1 = await this.downloadImageToBase64(
        `/ts-basics-bottom/fileAttachment/downloadFile/${this.globalSetting.hospitalLogo}`
      );
      let { templateData, fileName } = await this.formateData(res.object || {});
      templateData.Logo = res1;
      exportTemplateWord(
        '/templateWord/templateHanvoer.docx',
        templateData,
        fileName,
        this.imageSize
      );
    },
    async formateData(data) {
      let patientList = [
        {
          label: '新（转）入病人重点事项交班',
          groupKey: 'isNewPatient,isInTransfer',
          list: []
        },
        { label: '危重病人诊疗要点交班', groupKey: 'isBw,isBz', list: [] },
        { label: '手术病人诊疗要点交班', groupKey: 'isOperation', list: [] },
        { label: '死亡病人救治总结交班', groupKey: 'isDeath', list: [] },
        { label: '分娩病人病情变化交班', groupKey: 'isBirth', list: [] },
        { label: '特殊病人管理重点交班', groupKey: 'isSpecial', list: [] },
        { label: '转出病人交班', groupKey: 'isOutTransfer', list: [] }
      ];
      data.details.forEach(patient => {
        let dataKeys = Object.keys(patient).filter(key => patient[key] == '1');
        let typeName = [];
        this.pientList.forEach(e => {
          if (patient[e.key] == '1') {
            typeName.push(e.label);
          }
        });
        patientList.forEach(e => {
          let keys = e.groupKey.split(',');
          let has = false;
          keys.forEach(k => {
            if (dataKeys.indexOf(k) > -1) has = true;
          });
          if (has) {
            let tableRow = this.staticTableRows.filter(
              i => e.groupKey == i.groupKey
            );
            let patientObj = {
              index: e.list.length + 1,
              bed: patient.bedNo || '暂无床号',
              name: patient.patnName,
              sex: patient.patnSex == 1 ? '男' : '女',
              age: patient.patnAge,
              typeName: typeName.join('，'),
              point: []
            };
            tableRow.forEach(row => {
              patientObj.point.push({
                label: row.IndicatorName,
                value: patient[row.IndicatorKey] || ''
              });
            });
            e.list.push(patientObj);
          }
        });
      });
      let templateData = {
        webTitle: this.globalSetting.webTitle ?? 'XXX医院',
        handoverDate: data.medShiftRecord.handoverDate.split(' ')[0],
        handoverOrgName: data.medShiftRecord.handoverOrgName,
        areaText: data.areaText,
        dutyHours:
          data.medShiftRecord.scheduleStartDate +
          '至' +
          data.medShiftRecord.scheduleEndDate,
        isBu: data.medShiftRecord.compensateStatus == '1' ? '是' : '否',
        yy: data.medShiftRecord.originalNum,
        xy: data.medShiftRecord.nowNum,
        bw: data.bw,
        bz: data.bz,
        ss: data.ss,
        xr: data.xrzr,
        cy: data.cy,
        zc: data.zc,
        sw: data.sw,
        fm: data.fm,
        handoverUserName: data.medShiftRecord.handoverDoctorName,
        handoverUserDate: data.medShiftRecord.handoverDate,
        takeoverDoctorName: data.medShiftRecord.takeoverDoctorName || '',
        takeoverDate: data.medShiftRecord.takeoverDate || '',
        patientList
      };
      let fileName = `${templateData.webTitle}医师交接班-${templateData.handoverOrgName}-‘${templateData.dutyHours}’-${templateData.handoverUserName}`;
      return { templateData, fileName };
    },
    async downloadImageToBase64(url) {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const base64 = btoa(
        new Uint8Array(response.data).reduce(
          (data, byte) => data + String.fromCharCode(byte),
          ''
        )
      );
      return `data:image/png;base64,${base64}`;
    }
  }
};
