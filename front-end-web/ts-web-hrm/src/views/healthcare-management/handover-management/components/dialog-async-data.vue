<template>
  <vxe-modal
    width="700"
    v-model="visible"
    showFooter
    className="dialog-async-data"
    :before-hide-method="close"
  >
    <template #title>
      <span>
        同步
        <el-popover
          placement="right"
          width="400"
          trigger="hover"
          popper-class="popverContent"
        >
          <i class="vxe-icon-question-circle popverIcon" slot="reference"></i>
          <p>同步说明：</p>
          <p>1、正常交接班同步：选择的同步时间，2小时内算正常交班。</p>
          <p>2、补交交接班同步：选择的同步时间，超过2小时算补交交班。</p>
        </el-popover>
      </span>
    </template>
    <template #default>
      <div class="content">
        <ts-form ref="ruleForm" :model="form" labelWidth="110px">
          <ts-form-item
            label="同步类型"
            prop="scheduleType"
            :rules="rules.required"
          >
            <vxe-radio-group v-model="form.scheduleType" @change="typeChage">
              <vxe-radio
                v-for="(item, index) in shiftsTimeList"
                :key="index"
                :label="item.itemNameValue"
                :content="item.itemName"
              ></vxe-radio>
            </vxe-radio-group>
          </ts-form-item>
          <ts-form-item
            label="同步时间"
            prop="scheduleStartDate"
            :rules="rules.required"
          >
            <el-date-picker
              class="date-picker"
              style="width: 150px;"
              v-model="form.scheduleStartDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="请选择同步开始时间"
              @change="typeChage"
            ></el-date-picker>
            {{ timeList[0] || '' }}&nbsp;&nbsp;至&nbsp;&nbsp;{{
              form.scheduleEndDate
            }}&nbsp;{{ timeList[1] || '' }}
          </ts-form-item>
          <ts-form-item label="设置">
            <ts-input
              v-model="COMPENSATE_TIME"
              disabled
              style="width: 80px;"
            />&nbsp;&nbsp;时后，算补齐数据
          </ts-form-item>
        </ts-form>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">提 交</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import moment from 'moment';

export default {
  props: {
    COMPENSATE_TIME: {
      type: String,
      default: () => ''
    },
    shiftsTimeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {
        scheduleType: ''
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    timeList() {
      if (this.form.scheduleType) {
        return [
          this.form.scheduleType.split('-')[0],
          this.form.scheduleType.split('-')[1]
        ];
      }
      return [];
    },
    userInfo() {
      return this.$getParentStoreInfo('userInfo');
    }
  },
  methods: {
    async open() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    typeChage() {
      if (this.timeList.length && this.form.scheduleStartDate) {
        let days = moment().format('YYYY-MM-DD');
        let start = this.timeList[0];
        let end = this.timeList[1];
        if (
          start == end ||
          !moment(`${days} ${start}`).isBefore(moment(`${days} ${end}`))
        ) {
          let date = moment(this.form.scheduleStartDate)
            .add(1, 'days')
            .format('YYYY-MM-DD');
          this.$set(this.form, 'scheduleEndDate', date);
        }
        if (moment(`${days} ${start}`).isBefore(moment(`${days} ${end}`))) {
          this.$set(this.form, 'scheduleEndDate', this.form.scheduleStartDate);
        }
      }
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        let shiftCord = await this.ajax.getMedShiftRecord({
          currentOrgId: '1',
          scheduleStartDate: `${this.form.scheduleStartDate} ${this.timeList[0]}`,
          scheduleEndDate: `${this.form.scheduleEndDate} ${this.timeList[1]}`
        });
        if (
          shiftCord.object &&
          this.userInfo.employeeName != shiftCord.object.createUserName
        ) {
          await this.$newConfirm(
            `已被【${shiftCord.object.createUserName}】同步，是否确定将患者数据再次同步更新?`
          );
        }
        let shiftCords = await this.ajax.getMedShiftRecord();
        if (shiftCords.object) {
          await this.$newConfirm(
            `【确定】清除上一次已同步，未发起交班的指标信息?`
          );
        }
        this.submitLoading = true;
        let data = {
          scheduleStartDate: `${this.form.scheduleStartDate} ${this.timeList[0]}`,
          scheduleEndDate: `${this.form.scheduleEndDate} ${this.timeList[1]}`
        };
        let res = await this.ajax.syncMedShiftRecord(data);
        if (res.success) {
          this.submitLoading = false;
          this.$newMessage('success', `【同步】成功`);
          this.$emit('refresh');
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【同步】失败!`);
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {
        scheduleType: ''
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-async-data {
  ::v-deep {
    .content {
      height: 160px;
      display: flex;
      flex-direction: column;
      position: relative;
    }
  }
}
</style>
