<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @beforeClick="clickItemTree"
      />
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
        <template slot="skey1">
          <div style="display: flex; line-height: 30px;">
            <el-date-picker
              style="flex: 1;"
              class="date-picker"
              v-model="searchForm.shiftDateStart"
              value-format="yyyy-MM-dd 00:00:00"
              @change="changeTimeSearch('shiftDateStart', 'shiftDateEnd')"
              :picker-options="shiftDateStartOption"
              placeholder="开始日期"
            >
            </el-date-picker>
            &nbsp;-&nbsp;
            <el-date-picker
              style="flex: 1;"
              class="date-picker"
              v-model="searchForm.shiftDateEnd"
              value-format="yyyy-MM-dd 23:59:59"
              placeholder="结束日期"
              @change="changeTimeSearch('shiftDateStart', 'shiftDateEnd')"
              :picker-options="shiftDateEndOption"
            >
            </el-date-picker>
          </div>
        </template>
      </new-ts-search-bar>
      <TsVxeTemplateTable
        id="table_authorized_project"
        class="form-table"
        ref="table"
        :defaultSort="{
          sidx: 'create_date',
          sord: 'desc'
        }"
        :columns="columns"
        :stripe="false"
        pageAlign="center"
        @refresh="handleRefreshTable"
      />
    </div>
    <darwer-handover-matters
      ref="darwerHandoverMatters"
      @refresh="handleRefreshTable"
    />
    <base-import
      ref="baseImport"
      @refresh="search"
      :ImportConfiguration="ImportConfiguration"
    />
  </div>
</template>

<script>
import table from './mixins/table';
import darwerHandoverMatters from './components/darwer-handover-matters.vue';
export default {
  mixins: [table],
  components: { darwerHandoverMatters },
  data() {
    return {
      treeNode: {},
      apiFunction: this.ajax.getOrganizationGetTree2,
      ImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/quaAuthCfg/downloadTmpl',
        importTempalteName: '项目导入模板',
        importApi: 'quaAuthCfgImport'
      }
    };
  },
  methods: {
    // 树 item点击
    clickItemTree(node) {
      this.treeNode = node;
      this.search();
    },
    async refresh() {
      await this.search();
    },
    async search() {
      this.$refs.table.pageNo = 1;
      await this.handleRefreshTable();
    },
    changeTimeSearch(key1, key2) {
      if (this.searchForm[key1] && this.searchForm[key2]) {
        this.search();
      }
    },
    reset() {
      return {};
    },
    handleGetSeacrhQueryParam() {
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          index: 2,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageNo, pageSize };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let num = 0;
      searchForm.shiftDateStart && num++;
      searchForm.shiftDateEnd && num++;
      if (num == 1) {
        this.$newMessage('warning', '请选择【交接班时间】的开始时间和结束时间');
        return;
      }
      let res = await this.ajax.getShiftWorkList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
</style>
