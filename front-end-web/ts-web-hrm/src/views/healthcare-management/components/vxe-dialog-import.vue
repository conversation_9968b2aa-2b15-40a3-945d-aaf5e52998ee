<template>
  <vxe-modal :title="title" width="650" v-model="visible" showFooter>
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="0">
          <ts-row>
            <ts-col
              :span="12"
              v-for="(item, index) in templatelist"
              :key="index"
            >
              <ts-form-item class="down-box" label="">
                <div class="importContent">
                  <div class="importTip">
                    <strong>{{ item.label }}</strong>
                    <p>{{ item.tips }}</p>
                  </div>
                  <span class="down-template" @click="handleDownTemplate">
                    下载模版
                  </span>
                  <ts-upload
                    v-if="visible"
                    class="base-uplaod"
                    ref="tsUpload"
                    action=""
                    :limit="1"
                    :fileList.sync="fileList"
                    :show-file-list="false"
                    accept=".xlsx"
                    :http-request="
                      params => handleUploadFile(params, item.type)
                    "
                    :on-exceed="masterFileMax"
                  >
                    <ts-button type="primary">导入</ts-button>
                  </ts-upload>
                </div>
              </ts-form-item>
            </ts-col>
          </ts-row>
        </ts-form>
        <div class="errorRes" v-if="resList.length">
          <colmun-head title="导入提示" />
          <div class="contenRes">
            <strong>{{ resMesage }}</strong>
            <el-scrollbar
              style="height: calc(100% - 50px);"
              wrap-style="overflow-x: hidden;"
            >
              <p v-for="(item, index) in resList" :key="index">
                {{ item.data }}
              </p>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
export default {
  props: {
    ImportConfiguration: {
      type: Object,
      default: () => {
        return {
          importTempalteApi: '',
          importTempalteName: '导入模板',
          importApi: ''
        };
      }
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      fileList: [],
      resList: [],
      resMesage: '',
      title: '',
      templateList: [
        {
          label: '全量导入',
          type: '0',
          tips: '提示:全量导入会删除所有的旧数据。',
          isShow: true
        },
        {
          label: '增量导入',
          type: '1',
          tips: '提示：增量导入不会删除旧数据，是去重新增数据。',
          isShow: true
        }
      ]
    };
  },
  computed: {
    templatelist() {
      return this.templateList.filter(e => e.isShow);
    }
  },
  methods: {
    open({ title = '', increment = true, quantity = true }) {
      let {
        importTempalteApi,
        importTempalteName = '',
        importApi
      } = this.ImportConfiguration;
      this.importTempalteApi = importTempalteApi;
      this.importTempalteName = importTempalteName;
      this.importApi = importApi;
      this.templateList[0].show = quantity;
      this.templateList[1].show = increment;
      this.title = title || '导入';
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    handleUploadFile(params, type) {
      let data = new FormData();
      data.append('file', params.file);
      data.append('type', type);
      this.ajax[this.importApi](data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.$message.success({
            customClass: 'new-el-message',
            message: '【导入】成功',
            showClose: true
          });
          this.close();
          this.$emit('refresh');
        } else {
          params.file = null;
          this.fileList = [];
          this.resList = res.object || [];
          this.resMesage = res.message;
        }
      });
    },

    masterFileMax() {
      this.$message.warning({
        customClass: 'new-el-message',
        message: '请最多上传1个文件。',
        showClose: true
      });
    },

    handleDownTemplate() {
      let xhr = new XMLHttpRequest();
      let _this = this;
      xhr.open('get', this.importTempalteApi, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send();
      xhr.onload = function() {
        if (this.status === 200) {
          let url = window.URL.createObjectURL(new Blob([this.response]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `${_this.importTempalteName}.xlsx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      };
    },

    close() {
      this.form = null;
      this.fileList = [];
      this.resList = [];
      this.resMesage = '';
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .content {
    height: 100% !important;

    .down-box {
      position: relative;
      display: flex;
      justify-content: center;
      .importContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 200px;
        .importTip {
          text-align: center;
          border: 1px solid $primary-blue;
          border-radius: 5px;
          padding: 5px;
          strong {
            font-weight: bold;
            color: $primary-blue;
          }
          p {
            color: #bbb;
            margin: 0;
          }
        }
        .down-template {
          margin: 10px 0;
          cursor: pointer;
        }
      }
    }
    .errorRes {
      .contenRes {
        padding: 5px 10px;
        height: 200px;
        overflow: hidden;
        p {
          margin: 0;
        }
      }
    }
  }
}
</style>
