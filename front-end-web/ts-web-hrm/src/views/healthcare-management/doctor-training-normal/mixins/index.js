export default {
  data() {
    return {
      cardMap: [
        {
          title: '进修记录',
          API: this.ajax.outRecord,
          column: [
            {
              width: '35%',
              key: '',
              ellipsis: true,
              align: 'left',
              render: row => {
                return `${row.startTime}至${row.endTime}`;
              }
            },
            {
              width: '35%',
              key: 'outAddress',
              ellipsis: true,
              align: 'center'
            },
            {
              width: '20%',
              key: 'applyUserName',
              render: row => {
                return `${row.outType}${row.outDays}天`;
              }
            }
          ],
          params: {
            outType: '进修',
            sidx: 'create_date',
            sord: 'desc'
          },
          itemClick: this.handleOutItemClick
        },
        {
          title: '资质认证',
          API: this.ajax.getMyQualifications,
          params: {
            sidx: 'create_date',
            sord: 'desc'
          },
          column: [
            {
              width: '70%',
              key: 'dateTime',
              ellipsis: true,
              align: 'left'
            },
            {
              width: '30%',
              key: 'itemNmae',
              ellipsis: true,
              align: 'center'
            }
          ]
        },
        {
          title: '教育与培训',
          API: null
        },
        {
          title: '不良事件',
          API: null
        },
        {
          title: '定期考核',
          API: null
        },
        {
          title: '科研论文',
          API: null
        }
      ],
      activeIndex: 0,
      tabList: ['进修', '规培', '学习', '下乡']
    };
  },
  computed: {
    userInfo() {
      return this.$getParentStoreInfo('userInfo');
    }
  },
  created() {
    console.log(this.userInfo);
    this.cardMap[0].params.employeeId = this.userInfo.employeeId;
  },
  methods: {
    handleOutItemClick(item) {
      this.$refs.DialogAddOrEdit.open(item, true);
    },
    changeActive(index, i) {
      if (this.activeIndex == index) return;
      this.activeIndex = index;
      let _this = this.$refs[`cardList${i}`][0];
      _this.list = [];
      _this.pageNo = 1;
      let quertMap = ['进修', '规培', '学习与会议与公务外出', '下乡'];
      _this.card.params.outType = quertMap[index];
      _this.$nextTick(() => {
        if (!_this.wrapDom) {
          _this.wrapDom = _this.$refs.scroll.$el.querySelector(
            '.options-scrollbar'
          );
        }
        _this.wrapDom.resetInfinityScrolling();
      });
    }
  }
};
