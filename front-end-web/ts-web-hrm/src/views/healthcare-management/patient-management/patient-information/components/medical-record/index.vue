<template>
  <div class="template-two-zone">
    <div class="left">
      <searchList
        ref="searchList"
        :menuData="menuData"
        :defaultOpeneds="defaultOpeneds"
        @nodeClick="nodeClick"
      />
    </div>
    <div class="viewerbox-right">
      <PdfExpress
        :pdfUrl="selectedPdfSrc"
        :readOnly="true"
        :useWaterflow="true"
        :patientInfo="patientInfo"
      ></PdfExpress>
    </div>
  </div>
</template>

<script>
import searchList from './searchList.vue';
import PdfExpress from '@/components/PdfExpress/index.vue';
export default {
  components: { searchList, PdfExpress },
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      treeCode: '',
      menuData: [],
      defaultOpeneds: [],
      selectedPdfSrc: ''
    };
  },
  methods: {
    async nodeClick(node) {
      let params = {
        patnId: this.patientInfo.patnId,
        emrId: node.EMR_ID,
        hospCode: this.patientInfo.hospCode,
        templateFiletype: node.TEMPLATE_FILETYPE
      };
      let res = await this.ajax.getQueryInpEmrFileInfoPdfStream(params);
      this.selectedPdfSrc = res[0].BASESTR;
    },
    async refresh() {
      await this.getQueryInpEmrFileInfo();
    },
    // 去重
    uniqueByProp(arr, prop) {
      const map = new Map();
      return arr.reduce((acc, item) => {
        if (!map.has(item[prop])) {
          map.set(item[prop], true);
          acc.push(item);
        }
        return acc;
      }, []);
    },
    async getQueryInpEmrFileInfo() {
      let res = await this.ajax.getQueryInpEmrFileInfo({
        hospCode: this.patientInfo.hospCode,
        patnId: this.patientInfo.patnId
      });
      let parentList = this.uniqueByProp(res, 'MR_TYPE');
      this.defaultOpeneds = parentList;
      this.menuData = parentList.map(e => {
        let children = res
          .filter(i => i.MR_TYPE == e.MR_TYPE)
          .map(e => {
            return {
              ...e,
              MR_TITLEDATE: e.MR_TITLEDATE.replace('T', ' ')
            };
          });
        return {
          MR_TYPE: e.MR_TYPE,
          MR_TITLE: e.MR_TITLE,
          children,
          num: children.length
        };
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
.template-two-zone {
  padding: 0px;
  flex: 1;
  height: 100%;
  overflow: hidden;
  .viewerbox-right {
    flex: 1;
    height: 100%;
    overflow: hidden;
    position: relative;
  }
}
</style>
