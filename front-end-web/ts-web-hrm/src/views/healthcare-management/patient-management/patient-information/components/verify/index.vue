<template>
  <div class="verify flex-column">
    <TsVxeTemplateTable
      id="table_checklist"
      class="form-table"
      ref="table"
      :columns="columns"
      :hasPage="false"
      :span-method="mergeRowMethod"
      @refresh="handleRefreshTable"
    />
    <dialog-record-table ref="dialogRecordTable" />
    <dialog-pdf ref="dialogPdf" :patientInfo="patientInfo" />
  </div>
</template>

<script>
import columns from './columns';
import dialogRecordTable from './dialog-record-table.vue';
import dialogPdf from '../../../components/dialog-pdf.vue';
export default {
  components: { dialogRecordTable, dialogPdf },
  mixins: [columns],
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {};
  },
  methods: {
    async refresh() {
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let API = this.ajax.getTestRptItemRequestNew;
      let res = await API(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      this.$refs.table.refresh({ rows: res });
    }
  }
};
</script>

<style lang="scss" scoped>
.verify {
  flex: 1;
  /deep/ {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      .primary-span {
        color: $primary-blue;
        cursor: pointer;
      }
    }
  }
}
</style>
