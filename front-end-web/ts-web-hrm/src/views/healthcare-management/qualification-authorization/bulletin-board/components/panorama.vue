<template>
  <div class="panorama flex-column">
    <card-head
      title="资质授权全景"
      :img="require('@/assets/img/work-bench/reminder.png')"
    />
    <div class="qualification">
      <div class="top-left">
        <div class="top-head">
          <img src="@/assets/img/doctor-qua.png" />
          <span class="value">{{ doctorList.length }}项</span>
          <span>医生权限</span>
        </div>
        <div class="qualification-content">
          <div
            class="qualification-item flex6"
            v-for="item in doctor"
            :key="item.key"
          >
            <p>{{ item.label }}</p>
            <p class="item-content">
              <span class="value">{{ item.count }}</span>
              <span>{{ item.parcent }}%</span>
            </p>
          </div>
        </div>
      </div>
      <div class="top-right">
        <div class="top-head">
          <img src="@/assets/img/yaopin.png" />
          <span class="value">{{ pharmacistList.length }}项</span>
          <span>药师权限</span>
        </div>
        <div class="qualification-content flex-column">
          <div
            class="qualification-item flex1"
            v-for="item in pharmacist"
            :key="item.key"
          >
            <p>{{ item.label }}</p>
            <p class="item-content">
              <span class="value">{{ item.count }}</span>
              <span>{{ item.parcent }}%</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="situation-dynamics">
      <div class="situation">
        <!-- 手术级别授权情况 -->
        <div class="situation-first">
          <div class="small-card-head">手术级别授权情况</div>
          <div class="situation-first-box">
            <div class="first-box-item">
              <div class="cardIcon first"></div>
              <div class="cardTitle">
                <p class="ellipsis">一级</p>
                <p class="ellipsis">{{ authSituation.one_lv }}</p>
              </div>
            </div>
            <div class="first-box-item">
              <div class="cardIcon second"></div>
              <div class="cardTitle">
                <p class="ellipsis">二级</p>
                <p class="ellipsis">{{ authSituation.two_lv }}</p>
              </div>
            </div>
            <div class="first-box-item">
              <div class="cardIcon third"></div>
              <div class="cardTitle">
                <p class="ellipsis">三级</p>
                <p class="ellipsis">{{ authSituation.three_lv }}</p>
              </div>
            </div>
            <div class="first-box-item">
              <div class="cardIcon fourth"></div>
              <div class="cardTitle">
                <p class="ellipsis">四级</p>
                <p class="ellipsis">{{ authSituation.four_lv }}</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 抗肿瘤药物处方权授权情况 -->
        <div class="situation-second">
          <div class="small-card-head">抗肿瘤药物处方权授权情况</div>
          <div class="situation-second-box">
            <div class="second-box-item flex-column">
              <span>普通级</span>
              <span class="value">{{ authSituation.kzlywcfq_ptj }}</span>
              <img src="@/assets/img/ptj.png" />
            </div>
            <div class="second-box-item flex-column">
              <span>限制级</span>
              <span class="value">{{ authSituation.kzlywcfq_xzj }}</span>
              <img src="@/assets/img/xzj.png" />
            </div>
          </div>
        </div>
        <!-- 麻精要批处方权授权情况 -->
        <div class="situation-third">
          <div class="small-card-head">麻精药品处方权授权情况</div>
          <div class="situation-third-box">
            <div class="telnItem">
              <p class="label">麻醉</p>
              <div class="progress-container" :title="`${mzparcent}%`">
                <div
                  class="progress-bar"
                  :style="{ width: `${mzparcent}%` }"
                ></div>
              </div>
              <p class="value">{{ mzCount }}</p>
            </div>
            <div class="telnItem">
              <p class="label">精一</p>
              <div class="progress-container" :title="`${j1Parcent}%`">
                <div
                  class="progress-bar"
                  :style="{ width: `${j1Parcent}%` }"
                ></div>
              </div>
              <p class="value">{{ authSituation.jsypcfq_j1 }}</p>
            </div>
            <div class="telnItem">
              <p class="label">精二</p>
              <div class="progress-container" :title="`${j2Parcent}%`">
                <div
                  class="progress-bar"
                  :style="{ width: `${j2Parcent}%` }"
                ></div>
              </div>
              <p class="value">{{ authSituation.jsypcfq_j2 }}</p>
            </div>
          </div>
        </div>
        <!-- 抗菌药物处方权授权情况 -->
        <div class="situation-fourth">
          <div class="small-card-head">抗菌药物处方权授权情况</div>
          <div class="situation-fourth-box">
            <div class="colItem">
              <p class="label ellipsis">限制级</p>
              <p class="value">{{ authSituation.kjywcfq_xzj }}</p>
            </div>
            <div class="colItem">
              <p class="label ellipsis">非限制级</p>
              <p class="value">{{ authSituation.kjywcfq_fxzj }}</p>
            </div>
            <div class="colItem">
              <p class="label ellipsis">特殊级</p>
              <p class="value">{{ authSituation.kjywcfq_txj }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 授权动态 -->
      <div class="dynamics flex-column">
        <colmun-head title="授权动态" background="#fff">
          <template slot="right">
            <ts-input
              v-model="searchVal"
              placeholder="请输入医生姓名，授权名称进行检索"
            />
          </template>
        </colmun-head>
        <TsVxeTemplateTable
          id="table_panorama"
          class="form-table"
          ref="table"
          :columns="columns"
          :defaultSort="{
            sidx: 'create_date',
            sord: 'desc'
          }"
          @refresh="handleRefreshTable"
        />
      </div>
    </div>
  </div>
</template>

<script>
import cardHead from './card-head.vue';
import panorama from './panorama';
export default {
  components: { cardHead },
  mixins: [panorama],
  data() {
    return {
      searchVal: '',
      debounceTimer: null,
      debounceConfig: {
        delay: 500,
        immediate: false
      }
    };
  },
  watch: {
    searchVal(newVal) {
      this.debouncedSearch(newVal);
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
  },
  methods: {
    async refresh() {
      this.$nextTick(async () => {
        await this.getQualificationCode();
        await this.getEmpNumByOrgAttributes();
        await this.handleRefreshStatistics();
        await this.getAuthSituation();
        await this.handleRefreshTable();
      });
    },
    debouncedSearch: function(value) {
      const config = this.debounceConfig;
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      if (config.immediate && !this.debounceTimer) {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable(value);
        this.debounceTimer = setTimeout(() => {
          this.debounceTimer = null;
        }, config.delay);
        return;
      }
      this.debounceTimer = setTimeout(() => {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable(value);
        this.debounceTimer = null;
      }, config.delay);
    },
    async handleRefreshTable(value = '') {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          pageNo,
          pageSize,
          optTitle: value,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      this.ajax.getListKanban(searchForm).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.panorama {
  background: #fff;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
  /deep/ {
    p {
      margin: 0;
    }
    .qualification {
      display: flex;
      gap: 8px;
      border-bottom: 2px dashed #eee;
      .top-head {
        display: flex;
        align-items: center;
        padding: 8px 8px;
        border-radius: 8px;
        background: #eaeffe;
        img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        span {
          line-height: 20px;
          margin-right: 8px;
        }
        .value {
          color: $primary-blue;
          font-size: 15px;
          font-weight: bold;
        }
      }
      .top-left {
        flex: 6;
      }
      .top-right {
        flex: 1;
      }
      .qualification-content {
        margin: 8px 0px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        .qualification-item {
          &.flex1 {
            flex: 1;
          }
          &.flex6 {
            width: calc(16.7% - 8px);
          }
          background: #e9e9e9;
          border-radius: 6px;
          padding: 4px 6px;
          p {
            line-height: 25px;
          }
          .item-content {
            display: flex;
            justify-content: space-between;
            .value {
              color: $primary-blue;
              font-weight: bold;
            }
          }
        }
      }
    }
    .situation-dynamics {
      display: flex;
      .situation {
        flex: 1;
        border-right: 2px dashed #eee;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        padding: 8px 0;
        .situation-first,
        .situation-second,
        .situation-third,
        .situation-fourth {
          width: calc(50% - 4px);
          padding: 8px 0;
          display: flex;
          flex-direction: column;
        }
        .small-card-head {
          color: #000;
          line-height: 25px;
        }
        .situation-first-box {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-top: 8px;
          .first-box-item {
            width: calc(50% - 10px);
            display: flex;
            margin-bottom: 8px;
            .cardIcon {
              width: 40px;
              height: 40px;
              border: 10px solid;
              border-radius: 20px;
              &.first {
                border-color: #ffe9cc;
                background: #ffbe66;
              }
              &.second {
                border-color: #ffddcc;
                background: #ff9a66;
              }
              &.third {
                border-color: #f9d4d4;
                background: #ee7d7d;
              }
              &.fourth {
                border-color: #d4f7f1;
                background: #7de6d4;
              }
            }
            .cardTitle {
              p {
                margin: 0;
                margin-left: 8px;
                line-height: 20px;
                font-size: 14px;
                color: #333333;
                &:last-child {
                  font-weight: bold;
                  color: #121f3f;
                }
              }
            }
          }
        }
        .situation-second-box {
          border: 2px dashed #eee;
          display: flex;
          padding: 8px;
          flex: 1;
          margin: 8px;
          .second-box-item {
            width: 50%;
            justify-content: center;
            align-items: center;
            .value {
              font-weight: bold;
            }
            span {
              line-height: 30px;
            }
            img {
              width: 20px;
              height: 20px;
            }
          }
        }
        .situation-third-box {
          margin-top: 8px;
          .telnItem {
            display: flex;
            height: 18px;
            align-items: center;
            .label {
              margin-right: 4px;
              width: 30px;
            }
            .value {
              padding-left: 4px;
              padding-right: 8px;
              width: 50px;
              font-weight: bold;
            }
            .progress-container {
              // width: 65%;
              flex: 1;
              height: 12px;
              background-color: #e5efff;
              // box-shadow: 0px 6px 6px 0px rgba(26, 117, 234, 0.15);
              border-radius: 2px;
              overflow: hidden;
              .progress-bar {
                height: 100%;
                background: #186df5;
                transition: width 0.3s ease;
                border-radius: 2px;
              }
            }
            &:not(:last-child) {
              margin-bottom: 20px;
            }
          }
        }
        .situation-fourth-box {
          margin-top: 8px;
          .colItem {
            display: flex;
            justify-content: space-between;
            height: 30px;
            .label {
              margin-right: 4px;
              line-height: 25px;
              width: 60%;
              position: relative;
              padding-left: 25px;
              &::before {
                content: '●';
                font-size: 25px;
                color: $primary-blue;
                position: absolute;
                top: -1px;
                left: 5px;
              }
            }
            .value {
              padding-left: 4px;
              padding-right: 8px;
              font-weight: bold;
            }
            &:not(:last-child) {
              margin-bottom: 8px;
            }
          }
        }
      }
      .dynamics {
        flex: 1.5;
        padding: 6px;
        .top-title {
          .lefts {
            display: none;
          }
        }
      }
    }
  }
}
</style>
