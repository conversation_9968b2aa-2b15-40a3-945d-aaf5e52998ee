export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'searchKey',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索编码/项目名称'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '高风险项目分类',
          value: 'riskCfgType',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入高风险项目分类'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '是否上传附件',
          value: 'isFile',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '是',
              value: 1,
              element: 'ts-option'
            },
            {
              label: '否',
              value: 0,
              element: 'ts-option'
            }
          ],
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'seq',
          align: 'center',
          width: 40
        },
        {
          label: '状态',
          prop: 'status',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            return h('vxe-switch', {
              attrs: {
                value: row['status'],
                'open-value': '1',
                'close-value': '0',
                'open-label': '启用',
                'close-label': '禁用'
              },
              on: {
                change: () => {
                  this.handleOperateEdit(row, 'status');
                }
              }
            });
          }
        },
        {
          label: '高风险项目分类',
          prop: 'riskCfgType',
          width: 200,
          align: 'center'
        },
        {
          label: '项目编码',
          prop: 'riskCfgCode',
          width: 150,
          align: 'center'
        },
        {
          label: '项目名称',
          prop: 'riskCfgName',
          minWidth: 300,
          align: 'center'
        },
        {
          label: '是否需要附件',
          prop: 'isFile',
          width: 120,
          align: 'center',
          render: (h, { row }) => {
            return h('vxe-switch', {
              attrs: {
                value: row['isFile'],
                'open-value': '1',
                'close-value': '0',
                'open-label': '是',
                'close-label': '否'
              },
              on: {
                change: () => {
                  this.handleOperateEdit(row, 'isFile');
                }
              }
            });
          }
        },
        {
          label: '操作者',
          prop: 'updateUserName',
          width: 100,
          align: 'center'
        },
        {
          label: '操作时间',
          prop: 'updateDate',
          width: 160,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '修改',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'actionDel'
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  methods: {
    // 获取所需的全部字典数据
    // async getAllDictItemList() {
    //   let res = await this.ajax.getDictItemByTypeCode({
    //     typeCode: 'auth_lv_hosp'
    //   });
    //   this.searchList[1].childNodeList = res.object.map(e => {
    //     return {
    //       label: e.itemName,
    //       value: e.itemCode,
    //       element: 'ts-option'
    //     };
    //   });
    // },
    // 变更开始字段
    async handleOperateEdit(row, key) {
      let changeVal = row[key] === '1' ? '0' : '1';
      let data = Object.assign(row, {
        [key]: changeVal
      });
      const res = await this.ajax.medHighRiskCfgUpdate(data);
      if (res.success && res.statusCode === 200) {
        row[key] = changeVal;
        this.$newMessage('success', '【修改】成功！');
      } else {
        this.$newMessage('error', '【修改】失败！');
      }
    }
  }
};
