<template>
  <div class="wait-approval">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="reset"
    >
      <template slot="skey1">
        <input-ztree
          v-model="searchForm.skey1"
          ref="tree"
          placeholder="请选择邀请科室"
          :treeData="deptTreeData"
          :defaultExpandedKeys="defaultExpandedKeys"
          style="width: 100%"
          @ok="handleOk"
          nodeKey="code"
        />
      </template>
      <template slot="right">
        <ts-button type="primary" @click="handleExport" class="shallowButton">
          导出
        </ts-button>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_already_arranged"
      class="form-table"
      ref="table"
      :stripe="false"
      :defaultSort="{
        sidx: 'create_date',
        sord: 'desc'
      }"
      :columns="columns"
      :cell-style="cellStyle"
      @refresh="handleRefreshTable"
    />
    <darwer-authoried ref="darwerAuthoried" @refresh="search" />
  </div>
</template>

<script>
import darwerAuthoried from '../components/darwer-authoried.vue';
import waitApproval from '../mixins/wait-approval';
import {
  resetSearchFormSelect,
  supplementSearchSelect
} from '@/unit/commonHandle.js';
export default {
  components: { darwerAuthoried },
  mixins: [waitApproval],
  deta() {
    return {
      treeCode: ''
    };
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.field == 'actions') {
        return {
          backgroundColor: '#eceef3'
        };
      }
    },
    async refresh(treeCode = '') {
      await this.getAllDictItemList();
      await this.getTree();
      this.searchForm = {
        ...resetSearchFormSelect(this.searchList)
      };
      supplementSearchSelect(this.searchList);
      await this.search(treeCode);
    },
    search(treeCode = '') {
      this.$refs.table.pageNo = 1;
      if (treeCode) {
        this.treeCode = treeCode;
      }
      this.handleRefreshTable();
    },
    handleApproval(data) {
      this.$refs.darwerAuthoried.open({
        title: '审批',
        data,
        type: 'approval'
      });
    },
    reset() {
      this.$refs.tree.fullPath = '';
      return {
        ...resetSearchFormSelect(this.searchList)
      };
    },
    handleExport() {
      let aDom = document.createElement('a');
      let { searchForm } = this.handleGetSeacrhQueryParam();
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/quaAuthMgt/export?${conditionList.join('&')}`;
      aDom.click();
    },
    handleGetSeacrhQueryParam() {
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          auditStatus: '0',
          quaAuthType: this.treeCode,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageNo, pageSize };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let res = await this.ajax.getQuaAuthMgtList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wait-approval {
  flex: 1;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  .form-table {
    margin-top: 2px;
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    .primary-span {
      color: #295cf9;
      cursor: pointer;
      text-align: left;
    }
  }
}
</style>
