export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'employeeNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索身份证号/姓名'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '院区',
          value: 'hospArea',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [],
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '科室',
          value: 'skey1'
        },
        {
          label: '技术职称',
          value: 'jobtitle',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索技术职称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 40
        },
        {
          label: '状态',
          prop: 'auditStatusName',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            let color = '';
            if (row.auditStatusName.indexOf('待审批') > -1) {
              color = '#03B998';
            }
            if (row.auditStatusName.indexOf('审批中') > -1) {
              color = '#DC851F';
            }
            if (row.auditStatusName.indexOf('审批办结') > -1) {
              color = '#333';
            }
            return h('span', { style: `color: ${color}` }, row.auditStatusName);
          }
        },
        {
          label: '资质授权分类',
          prop: 'quaAuthTypeName',
          width: 100,
          align: 'center'
        },
        {
          label: '院区',
          prop: 'hospAreaName',
          minWidth: 300,
          align: 'center'
        },
        {
          label: '科室',
          prop: 'orgName',
          minWidth: 120,
          align: 'center'
        },
        {
          label: '身份证号',
          prop: 'identityNumber',
          minWidth: 200,
          align: 'center'
        },
        {
          label: '医师姓名',
          prop: 'employeeName',
          width: 120,
          align: 'center'
        },
        {
          label: '技术职称',
          prop: 'jobtitle',
          width: 120,
          align: 'center'
        },
        {
          label: '申请日期',
          prop: 'createDate',
          width: 160,
          align: 'center'
        },
        {
          label: '申请人',
          prop: 'createUserName',
          width: 100,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 80,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '审批',
                event: this.handleApproval
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  methods: {
    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'hosp_area'
      });
      this.searchList[1].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    },
    async getTree() {
      const tree = await this.ajax.getOrganizationGetTree2();
      this.deptTreeData = tree.object || [];
      let ids = this.deptTreeData.map(e => e.code);
      this.defaultExpandedKeys = ids;
    },
    // 部门树确定回调
    handleOk(list) {
      let id = [];
      list.forEach(item => {
        id.push(item.code);
      });
      this.searchForm.orgId = id.join(',');
      this.search();
    }
  }
};
