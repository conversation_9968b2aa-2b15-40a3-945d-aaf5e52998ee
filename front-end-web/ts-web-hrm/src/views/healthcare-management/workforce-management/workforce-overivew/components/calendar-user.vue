<template>
  <div>
    <vxe-modal
      :title="title"
      v-model="visible"
      width="80%"
      height="510px"
      showFooter
      className="calendar-user"
      :before-hide-method="close"
    >
      <template #default>
        <div class="content">
          <div class="top">
            <div class="top-left">
              <el-date-picker
                v-model="currentMonth"
                value-format="yyyy-MM"
                format="yyyy-MM"
                type="month"
                :clearable="false"
                placeholder="选择月"
                style="width: 140px;"
              >
              </el-date-picker>
              <p class="tips">提示：可单击查看班次的具体时间。</p>
            </div>
            <div class="top-right">
              <ts-button @click="handleMonth(1)" class="default">
                上月
              </ts-button>
              <ts-button @click="handleMonth(2)" class="shallowButton">
                今天
              </ts-button>
              <ts-button @click="handleMonth(3)" class="default">
                下月
              </ts-button>
            </div>
          </div>
          <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
            <base-calendar
              v-model="currentMonth"
              :showHeaderToolbar="false"
              :showLunar="true"
              :first-day-of-week="1"
            >
              <template slot="dateCellHeader" slot-scope="{ data }">
                <div
                  class="day-text"
                  @click="
                    event =>
                      handleTime(dateEventList[data.day], data.day, event)
                  "
                >
                  <label>{{ data.text }}</label>
                  <span v-if="holiday && holiday[data.day]">
                    {{ holiday[data.day] }}
                  </span>
                  <span v-else>{{ data.lunar }}</span>
                  <span class="work-day" v-if="workdayList.includes(data.day)">
                    班
                  </span>
                  <span
                    class="rest-day"
                    v-else-if="restDayList.includes(data.day)"
                  >
                    休
                  </span>
                </div>
              </template>
              <template slot="dateCellEvent" slot-scope="{ data }">
                <div
                  class="day-event"
                  ref="dayEvent"
                  @click="
                    event =>
                      handleTime(dateEventList[data.day], data.day, event)
                  "
                >
                  <span
                    class="eventItem"
                    v-for="item in dateEventList[data.day]?.show || []"
                    :key="item.id"
                    :style="{
                      backgroundColor: `${item.classesColor}1e`,
                      border: `1px solid ${item.classesColor}`,
                      color: `#333`
                    }"
                  >
                    {{ item.classesName }}
                  </span>
                  <span
                    v-if="dateEventList[data.day]?.isHide"
                    class="eventItem default"
                    >...</span
                  >
                </div>
              </template>
            </base-calendar>
          </el-scrollbar>
        </div>
      </template>
      <template #footer>
        <span slot="footer" class="dialog-footer">
          <ts-button class="shallowButton" @click="close">关 闭</ts-button>
        </span>
      </template>
    </vxe-modal>
    <dialog-user-work ref="dialogUserWork" />
  </div>
</template>

<script>
import BaseCalendar from '@/components/base-calendar/index.vue';
import dialogUserWork from './dialog-user-work.vue';
import moment from 'moment';
export default {
  components: { BaseCalendar, dialogUserWork },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      restDayList: [],
      holiday: {},
      workdayList: [],
      currentMonth: moment().format('YYYY-MM'),
      dateEventList: {},
      data: {}
    };
  },
  watch: {
    currentMonth: {
      handler(val) {
        this.handleGetShedule();
      }
    }
  },
  methods: {
    handleMonth(index) {
      let month = null;
      if (index == 1) {
        month = moment(this.currentMonth)
          .subtract(1, 'months')
          .format('YYYY-MM');
      }
      if (index == 2) {
        month = moment().format('YYYY-MM');
      }
      if (index == 3) {
        month = moment(this.currentMonth)
          .add(1, 'months')
          .format('YYYY-MM');
      }
      this.currentMonth = month;
    },
    async open({ data = {}, title, currentMonth = '' }) {
      this.title = title + `${data.employeeName}`;
      this.data = data;
      if (currentMonth) {
        this.currentMonth = moment(currentMonth).format('YYYY-MM');
      }
      this.visible = true;
      this.$nextTick(() => {
        this.handleGetShedule();
      });
    },
    handleTime(data, title, event) {
      event.stopPropagation();
      this.$refs.dialogUserWork.close();
      this.$refs.dialogUserWork.open({
        data: data.all,
        title,
        x: event.clientX,
        y: event.clientY
      });
    },
    handleGetShedule() {
      let data = {
        scheduleDateStr: moment(this.currentMonth).format('YYYY-MM')
      };
      this.$refs.dialogUserWork?.close();
      this.ajax.getHolidayList(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.workdayList = [];
          this.restDayList = [];
          this.holiday = {};
          res.object.forEach(item => {
            if (item.remark) this.workdayList.push(...item.remark.split('|'));
            if (item.holiday) this.holiday[item.holiday] = item.name;
            if (item.vacation)
              this.restDayList.push(...item.vacation.split('|'));
          });
        } else {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
      });
      this.ajax
        .getPersonMedScheduleRecord({
          employeeId: this.data.employeeId,
          startDate: moment(this.currentMonth)
            .startOf('months')
            .format('YYYY-MM-DD'),
          endDate: moment(this.currentMonth)
            .endOf('months')
            .format('YYYY-MM-DD')
        })
        .then(res => {
          if (res.success) {
            if (res.object && res.object.length) {
              let dateList = [
                ...new Set(res.object.map(item => item.scheduleDate))
              ];
              let obj = {};
              dateList.forEach(i => {
                let list = res.object.filter(e => e.scheduleDate == i);
                list = list.map(ej => {
                  let dayLabel = '';
                  if (ej.days == 0.5) {
                    ej.startDateValue == 1
                      ? (dayLabel = '上午')
                      : ej.startDateValue == 2
                      ? (dayLabel = '下午')
                      : '';
                  }
                  let classesName = ej.id
                    ? ej.classesName
                    : `${ej.classesName}-${ej.days == 1 ? '全天' : dayLabel}`;
                  return {
                    ...ej,
                    classesName
                  };
                });
                this.$set(obj, i, { all: [...list], show: [], isHide: false });
              });
              Object.keys(obj).forEach(key => {
                let indexs = obj[key].all.length;
                if (indexs) {
                  let width = this.$refs.dayEvent.offsetWidth - 25;
                  for (let i = 0; i < obj[key].all.length; i++) {
                    let itemWidth =
                      obj[key].all[i].classesName.length * 12 + 10;
                    width -= itemWidth;
                    if (width <= 0) {
                      indexs = i || 1;
                      break;
                    }
                  }
                  obj[key].show = obj[key].all.slice(0, indexs);
                  if (indexs != obj[key].all.length) {
                    obj[key].isHide = true;
                  }
                }
              });
              this.dateEventList = obj;
            }
          } else {
            this.$message.error(res.message || '数据获取失败');
            return;
          }
        });
    },
    close() {
      this.visible = false;
      this.$refs.dialogUserWork.close();
      this.restDayList = [];
      this.holiday = {};
      this.workdayList = [];
      this.currentMonth = moment().format('YYYY-MM');
      this.dateEventList = {};
      this.data = {};
    }
  }
};
</script>

<style lang="scss" scoped>
.calendar-user {
  ::v-deep {
    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      .default {
        color: #7f7f7f !important;
        background-color: #eff0f2 !important;
        border: none !important;
      }
      .top {
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        border: 1px solid #eee;
        border-radius: 3px;
        padding: 4px;
        .top-left {
          display: flex;
          .tips {
            margin: 0;
            color: #f59a23;
            line-height: 30px;
            margin-left: 8px;
          }
        }
      }
      .work-day {
        width: 18px;
        height: 18px;
        background: rgba(#0099e7, 0.1);
        color: #0099e7;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
      }
      .rest-day {
        width: 18px;
        height: 18px;
        background: rgba(#418f1f, 0.1);
        color: #418f1f;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
      }
      .default {
        color: #7f7f7f !important;
        background-color: #eff0f2 !important;
        border: none !important;
      }
      .day-text {
        display: flex;
        align-items: center;
        span {
          margin: 0 2px;
          font-weight: bold;
        }
      }
      .el-calendar .el-calendar__body {
        padding: 0;
      }
      .el-calendar .el-calendar-table thead {
        background-color: #fafafa;
      }
      .el-calendar .el-calendar-table thead th {
        text-align: center;
        padding: 8px;
        font-weight: bold;
      }
      .el-calendar-table td.is-selected {
        background-color: unset !important;
      }
      .el-calendar .el-calendar-day {
        height: 60px !important;
        padding: 4px !important;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .day-event {
        display: flex;
        align-items: center;
        height: 25px;
        .eventItem {
          padding: 2px 4px;
          margin-right: 2px;
          max-width: calc(100% - 30px);
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          &.default {
            background: #eee;
            padding: 2px 5px;
            color: #333;
          }
        }
      }
    }
  }
}
</style>
