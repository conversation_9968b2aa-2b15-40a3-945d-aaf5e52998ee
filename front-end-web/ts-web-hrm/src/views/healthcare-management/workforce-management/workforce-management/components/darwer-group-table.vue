<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-group-table"
    direction="rtl"
    size="70%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <div class="table-form">
        <div class="flex-end mtb8">
          <ts-button type="primary" @click="handleAdd">
            新增
          </ts-button>
        </div>
        <TsVxeTemplateTable
          id="table_darwer-add-group"
          class="form-table"
          ref="table"
          :defaultSort="{
            sidx: 'group_sort',
            sord: 'asc'
          }"
          :columns="columns"
          :dropRow="true"
          @refresh="handleRefreshTable"
          @rowDropEnd="rowDropEnd"
        />
      </div>
      <div class="drawer-footer">
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
    <dialog-add-group ref="dialogAddGroup" @refresh="handleRefreshTable" />
  </el-drawer>
</template>
<script>
import dialogAddGroup from './dialog-add-group.vue';
export default {
  components: { dialogAddGroup },
  data() {
    return {
      visible: false,
      title: '排班分组',
      searchForm: {},
      searchList: [],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '分组名称',
          prop: 'groupName',
          width: 140,
          align: 'center'
        },
        {
          label: '选择人数',
          prop: '',
          width: 140,
          align: 'center',
          render: (h, { row }) => {
            let length = row.groupEmpName.split(',').length;
            return h('span', {}, length);
          }
        },
        {
          label: '考勤组成员',
          prop: 'groupEmpName',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '修改',
                event: this.handleEdit
              }
            ];
            actionList.push({
              label: '删除',
              event: this.handleDelete,
              className: 'actionDel'
            });
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  methods: {
    async open() {
      this.visible = true;
      this.$nextTick(() => {
        this.search();
      });
    },
    async search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleAdd() {
      this.$refs.dialogAddGroup.open({
        title: '新增',
        type: 'add'
      });
    },
    handleEdit(data) {
      this.$refs.dialogAddGroup.open({
        title: '修改',
        type: 'edit',
        data
      });
    },
    async handleDelete(item, index) {
      try {
        await this.$newConfirm(
          `【<span style="color: red">删除</span>】该条数据？`
        );
        this.ajax.scheduleGroupDelete(item.id).then(res => {
          if (!res.success) {
            this.$newMessage('error', res.message || '【删除】失败');
            return;
          }
          this.$newMessage('success', '【删除】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async rowDropEnd(newIndex, oldIndex, ref) {
      let data = ref.getTableData().tableData;
      if (oldIndex !== newIndex) {
        const targetRow = data.splice(oldIndex, 1)[0];
        data.splice(newIndex, 0, targetRow);
        let parmas = data.map((i, index) => {
          return {
            id: i.id,
            groupSort: index + 1
          };
        });
        let res = await this.ajax.scheduleGroupupdateSeq(parmas);
        if (res.success) {
          this.$newMessage('success', '【排序】成功');
        } else {
          this.$newMessage('error', '【排序】失败');
        }
      }
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageSize, pageNo };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let res = await this.ajax.getScheduleGroupList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    close() {
      this.visible = false;
      this.$emit('refresh');
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .darwer-group-table {
    .table-form {
      width: 100%;
      height: calc(100% - 45px);
      display: flex;
      flex-direction: column;
      .form-table {
        margin-top: 8px;
      }
    }
  }
}
</style>
