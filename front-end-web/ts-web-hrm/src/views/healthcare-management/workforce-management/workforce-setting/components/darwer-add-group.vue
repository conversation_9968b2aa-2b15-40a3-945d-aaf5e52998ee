<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-add-group"
    direction="rtl"
    size="80%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="130px">
            <!-- 基本信息 -->
            <div class="form-card-box">
              <colmun-head title="基本信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="24">
                  <ts-form-item prop="composeName" :rules="rules.required">
                    <template #label>
                      周期班次名称
                      <el-popover
                        placement="left"
                        width="400"
                        trigger="hover"
                        popper-class="popverContent"
                      >
                        <i
                          class="vxe-icon-question-circle popverIcon"
                          slot="reference"
                        ></i>
                        <p>说明：</p>
                        <p>
                          1）已有班次按顺序组合在一起，一个班次对应1天的排班，一次性排好多天的班次。
                        </p>
                      </el-popover>
                    </template>
                    <ts-input
                      v-model="form.composeName"
                      placeholder="请输入周期班次名称"
                      :disabled="isDetail"
                      maxlength="20"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="备注" prop="remark">
                    <ts-input
                      v-model="form.remark"
                      placeholder="请输入备注"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="排班组合设置" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="24">
                  <div class="box-content">
                    <div class="float-content flex-column">
                      <colmun-head title="已有班次" />
                      <div class="search-top">
                        <ts-input
                          v-model="searchKeyword"
                          clearable
                          style="width: 30%;"
                          placeholder="搜索班次"
                        />
                        <ts-select
                          v-model="searchTypeId"
                          @change="handleRefreshTable"
                          style="margin-left: 10px;"
                          filterable
                          clearable
                        >
                          <ts-option
                            v-for="item in list"
                            :key="item.id"
                            :label="item.typeName"
                            :value="item.id"
                          ></ts-option>
                        </ts-select>
                      </div>
                      <PurityVxeTable
                        id="table_darwer-add-group"
                        class="form-table"
                        ref="table"
                        :defaultSort="{
                          sidx: 'create_date',
                          sord: 'desc'
                        }"
                        :checkboxConfig="{
                          reserve: true
                        }"
                        :columns="columns"
                        @refresh="handleRefreshTable"
                        @cell-click="handleSelectionChange"
                      />
                    </div>
                    <div class="float-content-right flex-column">
                      <colmun-head
                        :title="`已选组合班次(${scheduList.length})`"
                      />
                      <el-scrollbar
                        style="flex: 1;width: 100%; margin-top: 8px;"
                        wrap-style="overflow-x:hidden;"
                      >
                        <div class="boxList">
                          <div
                            class="box"
                            v-for="(item, i) in scheduList"
                            :key="item.id"
                            :style="{
                              backgroundColor: `${item.classesColor}32`,
                              border: `1px solid ${item.classesColor}`,
                              color: '#333'
                            }"
                          >
                            {{ item.classesName }}
                            <i class="el-icon-close" @click="delItem(i)"></i>
                          </div>
                        </div>
                      </el-scrollbar>
                    </div>
                  </div>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="适用范围" :showPopover="true">
                <template #popverContent>
                  <p>
                    组合班次的适用范围：取的是班次使用范围中都存在的科室、人员
                  </p>
                </template>
              </colmun-head>
              <ts-form-item label="科室范围">
                <p class="tipsName">{{ deptNames }}</p>
              </ts-form-item>
              <ts-form-item label="人员范围">
                <p class="tipsName">{{ userNames }}</p>
              </ts-form-item>
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button type="primary" v-if="!isDetail" @click="submit"
          >提交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      isDetail: false,
      title: '',
      type: '',
      form: {
        classesIds: []
      },
      rules: {
        required: { required: true, message: '必填' }
      },
      workList: [],
      scheduList: [],
      searchKeyword: '',
      debounceTimer: null,
      debounceConfig: {
        delay: 500,
        immediate: false
      },
      columns: [
        // {
        //   type: 'checkbox',
        //   align: 'center',
        //   width: 50
        // },
        {
          label: '班次类别',
          align: 'center',
          width: 90,
          showOverflow: 'tooltip',
          prop: 'typeName'
        },
        {
          label: '班次名称',
          align: 'center',
          width: 110,
          showOverflow: 'tooltip',
          prop: 'classesName'
        },
        {
          label: '班次属性',
          align: 'center',
          prop: 'classAttributes',
          width: 80,
          render: (h, { row }) => {
            let classAttributes =
              row.classAttributes == '1'
                ? '全院'
                : row.classAttributes == '2'
                ? '科室'
                : '';
            return h('span', {}, classAttributes);
          }
        },
        {
          label: '使用范围',
          align: 'center',
          prop: 'classesUseNames',
          headerRender: h => {
            return (
              <span>
                使用范围
                <el-popover
                  placement="left"
                  width="400"
                  trigger="hover"
                  popper-class="popverContent">
                  <i
                    class="vxe-icon-question-circle-fill popverIcon"
                    slot="reference"></i>
                  <p>说明：</p>
                  <p>
                    1）已有班次按顺序组合在一起，一个班次对应1天的排班，一次性排好多天的班次。
                  </p>
                </el-popover>
              </span>
            );
          }
        },
        {
          label: '班次时间',
          align: 'center',
          prop: 'classesWorktime',
          width: 90,
          render: (h, { row }) => {
            let list = [];
            row.classesWorktime.split(',').forEach(e => {
              list.push(h('p', {}, e));
            });
            return h('div', { class: 'timeList' }, [...list]);
          }
        }
      ],
      searchTypeId: '',
      list: [],
      formatList: {
        deptList: [],
        userList: []
      },
      deptNames: '',
      userNames: ''
    };
  },
  created() {
    this.initialData = JSON.parse(JSON.stringify(this.$data));
  },
  watch: {
    searchKeyword(newVal) {
      this.debouncedSearch(newVal);
    }
  },
  computed: {
    isAdmin() {
      let sysRoleCode = this.$getParentStoreInfo().sysRoleCode;
      return (
        sysRoleCode.indexOf('YWGLY') > -1 ||
        sysRoleCode.indexOf('SYS_ROLE_SUPERVISE') > -1 ||
        sysRoleCode.indexOf('SCHEDULER_SETTING_MANAGER') > -1
      );
    }
  },
  methods: {
    async open({ data = null, title, type }) {
      if (data) {
        await this.getDetail(data);
      }
      await this.getScheduleTypeList();
      this.title = title;
      this.type = type;
      if (type == 'detail') {
        this.isDetail = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
        if (data) {
          this.$refs.table
            .tsVxeTableRef()
            .setCheckboxRow(this.scheduList, true);
        }
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
      });
    },
    async getScheduleTypeList() {
      let res = await this.ajax.getScheduleTypeList();
      this.$emit('success', res.object || []);
      this.list = res.object || [];
    },
    async getDetail(data) {
      this.form = deepClone(data) || {};
      // let colorList = this.form.composeColours.split('|');
      // let composeContent = this.form.composeContent.split('|');
      let composeContentId = this.form.composeContentId.split('|');
      // let composeTime = this.form.composeTime.split('|');
      let searchForm = {
        pageNo: 1,
        pageSize: composeContentId.length,
        classIds: composeContentId.join(',')
      };
      let res = await this.ajax.getScheduleClassesList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let list = composeContentId.map(e => {
        let obj = res.rows.find(i => i.id == e);
        return { ...obj };
      });
      this.scheduList = list || [];
      this.formatSchedu(this.scheduList);
      // colorList.forEach((i, index) => {
      //   this.scheduList.push({
      //     id: composeContentId[index],
      //     classesColor: colorList[index],
      //     classesName: composeContent[index],
      //     classesWorktime: composeTime[index]
      //   });
      // });
    },
    handleSelectionChange({ row }) {
      this.scheduList.push({ ...row });
      this.formatSchedu(this.scheduList);
    },
    // 快速判断二维数组中的一维数组的并集
    getIntersectionOfObjectProps(arr, prop) {
      if (arr.length === 0) return [];
      return arr[0].filter(e =>
        arr.every(subArray => subArray.some(obj => obj[prop] === e[prop]))
      );
    },
    // 格式化范围的数据
    formatSchedu(scheduList) {
      let optionDept = [],
        optionEmp = [],
        data = scheduList.filter(e => e.classAttributes == '2');
      data.forEach(item => {
        let nameList = item.classesUseNames?.split(',') || [];
        console.log(nameList);
        let deptList = [],
          empList = [];
        if (item.classesUseOrg) {
          deptList = item.classesUseOrg.split(',').map((e, index) => {
            return {
              id: e,
              type: 'dept',
              name: nameList[index]
            };
          });
        }
        if (item.classesUseUser) {
          let length = deptList.length || 0;
          empList = item.classesUseUser.split(',').map((e, index) => {
            return {
              id: e,
              type: 'emp',
              name: nameList[index + length]
            };
          });
        }
        optionDept.push(deptList);
        optionEmp.push(empList);
      });
      this.formatList.deptList = this.getIntersectionOfObjectProps(
        optionDept,
        'id'
      );
      this.formatList.userList = this.getIntersectionOfObjectProps(
        optionEmp,
        'id'
      );
      if (data.length == 0 && scheduList.length) {
        this.userNames = this.deptNames = '全院';
      } else {
        this.userNames = this.formatList.userList.map(e => e.name).join(',');
        this.deptNames = this.formatList.deptList.map(e => e.name).join(',');
      }
    },
    delItem(index) {
      this.$refs.table
        .tsVxeTableRef()
        .setCheckboxRow({ id: this.scheduList[index].id }, false);
      this.scheduList.splice(index, 1);
      this.formatSchedu(this.scheduList);
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          pageNo,
          pageSize,
          classesName: this.searchKeyword,
          typeId: this.searchTypeId,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      // classAttributes: this.isAdmin ? '1' : '',
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      let res = await this.ajax.getScheduleClassesList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    debouncedSearch: function(value) {
      const config = this.debounceConfig;
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      if (config.immediate && !this.debounceTimer) {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
        this.debounceTimer = setTimeout(() => {
          this.debounceTimer = null;
        }, config.delay);
        return;
      }
      this.debounceTimer = setTimeout(() => {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
        this.debounceTimer = null;
      }, config.delay);
    },
    // 设置选中
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        let Api = this.ajax.medScheduleComposeClassesSave;
        let submitData = {
          composeContent: this.scheduList.map(e => e.classesName).join('|'),
          composeContentId: this.scheduList.map(e => e.id).join('|'),
          composeName: data.composeName,
          isHospital: this.isAdmin ? 'Y' : 'N',
          remark: data.remark
        };
        if (data.id) {
          submitData.id = data.id;
          Api = this.ajax.medScheduleComposeClassesUpdate;
        }
        if (this.scheduList.length == 0) {
          this.$newMessage('error', '请选择班次');
          return;
        }
        if (this.deptNames == '全院') {
          submitData.classesUseNames = '全院';
          submitData.classesUseOrg = '-1';
          submitData.classesUseUser = '-1';
        } else {
          submitData.classesUseNames = [
            ...this.formatList.deptList,
            ...this.formatList.userList
          ]
            .map(e => e.name)
            .join(',');
          submitData.classesUseOrg = this.formatList.deptList
            .map(e => e.id)
            .join(',');
          submitData.classesUseUser = this.formatList.userList
            .map(e => e.id)
            .join(',');
        }
        const res = await Api(submitData);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      // this.visible = false;
      // this.type = undefined;
      // this.form = {
      //   classesIds: []
      // };
      // this.isDetail = false;
      // this.workList = [];
      // this.scheduList = [];
      // this.$refs.table.tsVxeTableRef().clearCheckboxRow();
      // this.formatList = {
      //   deptList: [],
      //   userList: []
      // };
      // this.deptNames = '';
      // this.userNames = '';
      Object.assign(this.$data, JSON.parse(JSON.stringify(this.initialData)));
      this.$refs.table.tsVxeTableRef().clearCheckboxRow();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-popover__reference-wrapper {
    line-height: 36px;
  }
  .popverIcon {
    font-size: 18px;
    margin-left: 4px;
  }
  .darwer-add-group {
    .el-form-item__label {
      display: flex;
      justify-content: flex-end;
    }
    .timeList {
      p {
        margin: 0;
        line-height: 20px;
        text-align: center;
      }
    }
    .textarea {
      .el-textarea__inner {
        min-height: 120px !important;
        max-height: 120px !important;
      }
    }
    .tipsName {
      margin: 0;
      line-height: 36px;
    }
    .box-content {
      display: flex;
      padding: 0 4px 4px 4px;
    }
    .float-content,
    .float-content-right {
      height: 400px;
      margin-top: 4px;
      margin-right: 4px;
      padding: 8px;
      border: 1px solid #eee;
      border-radius: 8px;
      .search-top {
        margin-bottom: 4px;
        margin-top: 8px;
      }
      .boxList {
        display: flex;
        flex-wrap: wrap;
        .box {
          padding: 4px 12px;
          border-radius: 2px;
          margin-right: 2px;
          margin-bottom: 4px;
          position: relative;
          cursor: pointer;
          .el-icon-close {
            color: red;
          }
          &.active {
            &::before {
              content: '';
              position: absolute;
              right: 0px;
              bottom: 0px;
              height: 0;
              width: 0;
              border: 10px solid $primary-blue;
              border-left-color: transparent;
              border-top-color: transparent;
            }
            &::after {
              content: '✓';
              position: absolute;
              right: 0;
              bottom: 0px;
              color: #fff;
              font-size: 15px;
              font-weight: bold;
              line-height: 14px;
            }
          }
        }
      }
    }
    .float-content {
      width: 66%;
    }
    .float-content-right {
      width: 33%;
    }
  }
}
</style>
