import { checkSearchFormDate } from '@/unit/commonHandle.js';
import moment from 'moment';
export default {
  data() {
    return {
      searchForm: {
        wheelDate: [
          moment()
            .add(-1, 'month')
            .startOf('month')
            .format('YYYY-MM-DD'),
          moment()
            .add(-1, 'month')
            .endOf('month')
            .format('YYYY-MM-DD')
        ]
      },
      searchList: [
        {
          label: '',
          value: 'keywords',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '搜索姓名'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '院区',
          value: 'hospCode',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [],
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '科室',
          value: 'dept'
        },
        {
          label: '排班日期',
          value: 'wheelDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { wheelDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = wheelDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          }
        }
      ],
      staticCol: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 40,
          fixed: 'left'
        },
        {
          label: '归属院区',
          prop: 'hospName',
          fixed: 'left',
          width: 95,
          align: 'center'
        },
        {
          label: '归属科室',
          prop: 'deptName',
          fixed: 'left',
          width: 95,
          align: 'center',
          sortable: true,
          sortBy: 't1.org_id'
        },
        {
          label: '姓名',
          prop: '',
          fixed: 'left',
          width: 95,
          align: 'center',
          render: (h, { row }) => {
            let spanList = [h('span', {}, row.employeeName)];
            let attr = row.orgAttributes;
            let color = {
              研: 'blue',
              进: 'green',
              规: 'yellow'
            };
            if (
              attr &&
              (attr.startsWith('研') ||
                attr.startsWith('进') ||
                attr.startsWith('规'))
            ) {
              spanList.push(
                h(
                  'span',
                  { class: `Attributes ${color[attr.charAt(0)]}` },
                  row.orgAttributes.slice(0, 1)
                )
              );
            }
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleCalender(row) }
              },
              [...spanList]
            );
          }
        },
        {
          label: '应出勤(天)',
          prop: 'scheduledAttendance',
          width: 120,
          fixed: 'left',
          align: 'center',
          headerRender: h => {
            return (
              <span class="font-bold">
                应出勤(天)
                <el-popover
                  placement="bottom"
                  width="400"
                  trigger="hover"
                  popper-class="popverContent">
                  <i
                    class="vxe-icon-question-circle-fill popverIcon"
                    slot="reference"></i>
                  <p>应出勤(天)说明：</p>
                  <p>应出勤(天=)每月天数-周末-法定节假日</p>
                </el-popover>
              </span>
            );
          }
        },
        {
          label: '实际出勤(天)',
          prop: 'actualAttendance',
          width: 120,
          fixed: 'left',
          align: 'center',
          headerRender: h => {
            return (
              <span class="font-bold">
                实际出勤(天)
                <el-popover
                  placement="bottom"
                  width="400"
                  trigger="hover"
                  popper-class="popverContent">
                  <i
                    class="vxe-icon-question-circle-fill popverIcon"
                    slot="reference"></i>
                  <p>实际出勤(天)说明：</p>
                  <p>
                    实际出勤(天)=已排班的班次（不算休息班次及OA数据）-请假-下乡-进修-规培-外出学习+销假
                  </p>
                </el-popover>
              </span>
            );
          }
        }
      ],
      columns: [],
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  methods: {
    async getTree() {
      const tree = await this.ajax.organizationZTree3List();
      this.deptTreeData = tree.object || [];
      this.defaultExpandedKeys = [this.deptTreeData[0].id];
    },
    async getAllDictItemList() {
      await this.getTree();
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'hosp_area'
      });
      this.searchList[1].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    },
    // 科室树点击回调
    handleSelectOk(list) {
      let id = [];
      list.forEach(item => {
        id.push(item.code);
      });
      this.searchForm.deptIds = id.join(',');
      this.search();
    },
    async handleRefreshTableCol() {
      let col = [...this.staticCol];
      let { wheelDate = [] } = this.searchForm,
        [startDate = '', endDate = ''] = wheelDate,
        searchForm = {
          ...this.searchForm,
          startDate,
          endDate,
          type: 3
        };
      delete searchForm.wheelDate;
      let res = await this.ajax.getScheduleTableTitleList(searchForm);
      if (!res.success) {
        this.$newMessage('error', res.message || '获取表头失败，请稍后重试');
        return false;
      }
      col = [...col, ...res.object];
      this.columns = col;
    },
    handleClass() {
      this.$refs.drawerCustomizeClass.open();
    },
    handleCalender(data) {
      let currentMonth = this.searchForm.wheelDate[0] || '';
      this.$refs.calendarUser.open({ title: '排班总览-', data, currentMonth });
    },
    handleExport() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm } = this.getQueryParam();
      let aDom = document.createElement('a');
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/scheduleStatistics/export?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { wheelDate = [] } = this.searchForm,
        [startDate = '', endDate = ''] = wheelDate,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDate,
          endDate,
          type: 3,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      delete searchForm.wheelDate;
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageSize, pageNo };
    }
  }
};
