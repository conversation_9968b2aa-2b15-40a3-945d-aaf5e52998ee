<template>
  <div class="consultation flex-column">
    <colmun-head title="会诊" :background="background">
      <template slot="right">
        <div class="slotRight">
          <ts-button class="default">上年</ts-button>
          <ts-button class="shallowButton">本年</ts-button>
          <base-date-range-picker v-model="date" style="margin-left: 12px;" />
        </div>
      </template>
    </colmun-head>
    <div class="flexBox">
      <div
        class="flexItem flex-column flex5"
        v-for="(item, index) in qualityControlConsultation"
        :key="index"
      >
        <div class="topContent">
          <p>{{ item.name }}</p>
          <p class="primaryColor">{{ item.value }}</p>
        </div>
        <div class="bottomContent">
          <p>
            <span>{{ item.label1 + ':' + item.value1 }}</span>
            <span class="endSpan">
              <span class="cellDot">院</span>
              <span>{{ item.key1 }}</span>
            </span>
          </p>
          <p>
            <span>{{ item.label2 + ':' + item.value2 }}</span>
            <span class="endSpan">
              <span class="cellDot">科</span>
              <span>{{ item.key2 }}</span>
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import staticData from '../../../mixins/staticData';
export default {
  mixins: [staticData],
  data() {
    return {
      date: []
    };
  },
  methods: {
    async refresh() {}
  }
};
</script>

<style lang="scss" scoped>
.consultation {
  margin-top: 4px;
  border: 1px solid #eee;
  border-radius: 4px;
  /deep/ {
    .slotRight {
      height: 32px;
      display: flex;
      align-items: center;
      .el-input__inner {
        background: #efeff4;
      }
    }
    p {
      margin: 0;
      line-height: 30px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &.primaryColor {
        color: #f59a23;
        font-size: 18px;
      }
      span {
        line-height: 30px;
      }
      .cellDot {
        padding: 2px 4px;
        font-size: 12px;
        line-height: 30px;
        background: $primary-blue;
        color: #fff;
        border-radius: 12px;
        margin-right: 8px;
      }
    }
    .flexBox {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      padding: 4px 4px 4px 0;
      .flexItem {
        margin-left: 4px;
        margin-top: 4px;
        border-radius: 4px;
        padding: 8px;
        border: 1px solid #eee;
        justify-content: space-between;
        &.flex5 {
          width: calc(20% - 4px);
        }
        &.flexRow {
          display: flex;
          align-items: center;
          .itemLeft {
            text-align: right;
          }
        }
        .topContent {
          text-align: center;
        }
        .bottomContent {
          p {
            display: flex;
            justify-content: space-between;
            .endSpan {
              width: 30%;
            }
          }
        }
      }
    }
  }
}
</style>
