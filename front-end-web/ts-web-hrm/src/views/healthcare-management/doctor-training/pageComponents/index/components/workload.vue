<template>
  <div class="workload flex-column">
    <colmun-head
      :title="`工作量(${indexWorkLoadData.length})`"
      :background="background"
    >
      <template slot="right">
        <div class="slotRight">
          <ts-button class="shallowButton">本周</ts-button>
          <base-date-range-picker v-model="date" style="margin-left: 4px;" />
        </div>
      </template>
    </colmun-head>
    <TsVxeTemplateTable
      id="table_workload"
      class="form-table"
      ref="table"
      :hasPage="false"
      :columns="columns"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import staticData from '../../../mixins/staticData';
export default {
  mixins: [staticData],
  data() {
    return {
      date: [],
      columns: [
        {
          label: '任务名称',
          prop: 'key1',
          align: 'center',
          minWidth: 100
        },
        {
          label: '工作量',
          prop: 'key2',
          align: 'center',
          minWidth: 100,
          render: (h, { row }) => {
            return h(
              'div',
              { class: 'parcentCell', style: `width: ${row.key2}%` },
              row.key2
            );
          }
        },
        {
          label: '排名',
          prop: 'key3',
          align: 'center',
          minWidth: 150,
          render: (h, { row }) => {
            return h('p', { class: 'topCell' }, [
              h('span', {}, [
                h('span', { class: 'cellDot' }, '院'),
                h('span', { class: 'cellValue' }, row.key3)
              ]),
              h('span', {}, [
                h('span', { class: 'cellDot' }, '科'),
                h('span', { class: 'cellValue' }, row.key4)
              ])
            ]);
          }
        }
      ],
      tableData: []
    };
  },
  watch: {
    date: {
      handler(val) {
        if (val && val.length == 2 && val[0] && val[1]) {
          this.handleRefreshTable();
        }
      },
      immediate: true
    }
  },
  created() {
    this.refresh();
  },
  methods: {
    async refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    async handleRefreshTable() {
      // let searchForm = {
      //   startDate: this.date[0],
      //   endDate: this.date[1]
      // };
      // let res = await this.ajax.getScheduleClassesList(searchForm);
      this.$refs.table.refresh({ rows: this.indexWorkLoadData });
    }
  }
};
</script>

<style lang="scss" scoped>
.workload {
  flex: 1;
  width: 100%;
  height: 100%;
  margin-right: 4px;
  /deep/ {
    .slotRight {
      height: 32px;
      display: flex;
      align-items: center;
      .el-input__inner {
        background: #efeff4;
      }
    }
    .form-table {
      margin-top: 2px;
    }
    .parcentCell {
      background: #f8e5b0;
      color: #eec985;
    }
    .topCell {
      display: flex;
      justify-content: space-around;
      margin: 0;
      .cellDot {
        padding: 2px 4px;
        font-size: 12px;
        line-height: 24px;
        background: $primary-blue;
        color: #fff;
        border-radius: 12px;
      }
      .cellValue {
        margin-left: 4px;
      }
    }
  }
}
</style>
