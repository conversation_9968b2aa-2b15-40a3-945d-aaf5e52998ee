<template>
  <div class="doctor-menus">
    <el-scrollbar
      ref="topscroll"
      style="width: 100%; height: 100%"
      wrap-class="scroll-wrap"
    >
      <el-menu
        default-active="1"
        class="el-menu-doctor"
        @open="handleOpen"
        @close="handleClose"
      >
        <div v-for="item in menuData" :key="item.index">
          <div v-if="item.children?.length">
            <el-submenu :index="item.index">
              <template slot="title">
                <i :class="item.icon"></i>
                <span>{{ item.name }}</span>
              </template>
              <el-menu-item
                v-for="menu in item.children"
                :key="menu.index"
                :index="menu.index"
                @click="menuClick(menu, item)"
              >
                <i :class="menu.icon || 'oaicon oa-icon-erjicaidan'"></i>
                <span slot="title">{{ menu.name }}</span>
              </el-menu-item>
            </el-submenu>
          </div>
          <div v-else>
            <el-menu-item
              :key="item.index"
              :index="item.index"
              @click="menuClick(item)"
            >
              <i :class="item.icon || 'oaicon oa-icon-erjicaidan'"></i>
              <span slot="title">{{ item.name }}</span>
            </el-menu-item>
          </div>
        </div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import staticData from '../mixins/staticData';
export default {
  mixins: [staticData],
  data() {
    return {};
  },
  methods: {
    async refresh() {},
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    menuClick(menu, item = null) {
      this.$emit('toComponents', menu, item);
    }
  }
};
</script>

<style lang="scss" scoped>
.doctor-menus {
  flex: 1;
  height: 100%;
  width: 100%;
  border: 1px solid #eee;
  border-radius: 0 0 4px 4px;
  /deep/ {
    $menu-doctor-color: #333;
    $theme-doctor-hover: #e8ebfa;
    $menu-doctor-isActive-color: #e8ebfa;
    .scroll-wrap {
      overflow-x: auto;
      overflow-y: hidden;
      background-color: #fff;
    }
    .nav-item-icon {
      font-size: 18px;
      margin-right: 8px;
    }
    .el-menu-doctor {
      width: 100%;
      i {
        color: $primary-blue !important;
      }
      .el-menu-item {
        height: 30px;
        line-height: 30px;
        color: $menu-doctor-color;
        width: 100%;
        min-width: 100%;
        border-bottom: 1px solid #eee;
        &:hover {
          background-color: $theme-doctor-hover;
          color: $menu-doctor-color;
        }

        &.is-active {
          background-color: $menu-doctor-isActive-color;
        }
      }
      .el-submenu {
        width: 100%;
        .el-submenu__title {
          height: 30px;
          line-height: 30px;
          color: $menu-doctor-color;
          border-bottom: 1px solid #eee;
          &:hover {
            background-color: $theme-doctor-hover;
            color: $menu-doctor-color;
          }
        }
        &.is-active {
          & > .el-submenu__title {
            background-color: $menu-doctor-isActive-color;
            color: $menu-doctor-color;
          }
        }
      }
    }
  }
}
</style>
