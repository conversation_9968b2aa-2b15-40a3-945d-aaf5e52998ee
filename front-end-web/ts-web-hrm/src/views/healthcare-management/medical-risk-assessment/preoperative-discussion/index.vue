<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @nodeCheck="clickItemTree"
      >
      </new-base-search-tree>
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
      </new-ts-search-bar>
      <div class="flex-end mtb8">
        <ts-button class="shallowButton" @click="handleExport">
          导出
        </ts-button>
      </div>
      <TsVxeTemplateTable
        id="table_preoperative-discussion"
        class="form-table"
        ref="table"
        :defaultSort="{
          sidx: 'a.operation_date',
          sord: 'desc'
        }"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
    <darwer-discussion ref="darwerDiscussion" />
  </div>
</template>

<script>
import table from './mixins/table';
import darwerDiscussion from './components/darwer-discussion.vue';
import {
  checkSearchFormDate,
  resetSearchFormSelect,
  supplementSearchSelect
} from '@/unit/commonHandle.js';
export default {
  mixins: [table],
  components: { darwerDiscussion },
  data() {
    return {
      treeCode: '',
      apiFunction: this.ajax.getDeptTreeList
    };
  },
  methods: {
    clickItemTree(node) {
      this.treeCode = node.map(e => e.code).join(',');
      this.search();
    },
    async refresh() {
      await this.getAllDictItemList();
      await this.search();
      this.searchForm = {
        ...resetSearchFormSelect(this.searchList)
      };
      supplementSearchSelect(this.searchList);
    },
    reset() {
      this.$refs.searchTree.treeClass.checkAllNodes(false);
      this.treeCode = '';
      return {
        ...resetSearchFormSelect(this.searchList)
      };
    },
    async search() {
      this.$refs.table.pageNo = 1;
      await this.handleRefreshTable();
    },
    handleExport() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm } = this.getQueryParam();
      let aDom = document.createElement('a');
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/medRiskPatientOperation/exportPageListAll?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    },
    async handleRefreshTable() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let res = await this.ajax.medRiskPatientOperation(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
</style>
