<template>
  <vxe-modal
    width="800"
    :title="title"
    v-model="visible"
    showFooter
    className="dialog-add"
    :before-hide-method="close"
  >
    <template #default>
      <ts-form ref="form" :model="form" labelWidth="110px">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              prop="employeeNo"
              label="资质人员"
              :rules="rules.required"
            >
              <base-select
                v-model="form.employeeNo"
                :inputText.sync="form.employeeName"
                :loadMethod="handleGetPersonList"
                label="employeeName"
                value="employeeNo"
                searchInputName="employeeName"
                :clearable="false"
                placeholder="请选择"
                :disabled="isDetails"
                style="width: 100%;"
                @select="handlePersonSelect"
              >
                <template slot-scope="options">
                  <p
                    style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                    :title="
                      `${options.data['employeeNo']}--${options.data['orgName']}--${options.data['employeeName']}`
                    "
                  >
                    {{ options.data['employeeNo'] }}--{{
                      options.data['orgName']
                    }}--{{ options.data['employeeName'] }}
                  </p>
                </template>
              </base-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              prop="issueDate"
              label="发证日期"
              :rules="rules.required"
            >
              <el-date-picker
                style="width: 100%"
                v-model="form.issueDate"
                :disabled="isDetails"
                value-format="yyyy-MM-dd"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              prop="certificateNumber"
              label="证书编号"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.certificateNumber"
                :disabled="isDetails"
                maxlength="50"
                placeholder="请输入证书编号"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-form-item prop="files" label="附件" :rules="rules.required">
          <base-upload :onlyRead="isDetails" v-model="form.files" />
        </ts-form-item>
      </ts-form>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" v-if="!isDetails" @click="submit"
          >提 交</ts-button
        >
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      isDetails: false,
      title: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async getData(id) {
      let res = await this.ajax.RadiateQualificationCertificateDetail(id);
      this.form = res.object || {};
    },
    async open({ data = {}, title, type }) {
      if (type != 'add') {
        await this.getData(data.id);
      } else {
        this.form = {};
      }
      if (type == 'detail') {
        this.isDetails = true;
      }
      this.title = title;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },
    async handleGetPersonList(data) {
      let res = await this.ajax.getBasicMyEmployeeList({
        employeeStatusList: [],
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    handlePersonSelect(item) {
      this.$set(this.form, 'employeeNo', item.employeeNo);
      this.$set(this.form, 'employeeName', item.employeeName);
      this.$set(this.form, 'orgId', item.orgId);
      this.$set(this.form, 'orgName', item.orgName);
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.form);
        this.submitLoading = true;
        let API = this.ajax.RadiateQualificationCertificateSave;
        let label = '新增';
        if (formData.id) {
          API = this.ajax.RadiateQualificationCertificateUpdate;
          label = '修改';
        }
        const res = await API(formData);
        if (res.success) {
          this.submitLoading = false;
          this.$newMessage('success', `【${label}】成功`);
          this.$emit('refresh');
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【${label}】失败!`);
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.isDetails = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
