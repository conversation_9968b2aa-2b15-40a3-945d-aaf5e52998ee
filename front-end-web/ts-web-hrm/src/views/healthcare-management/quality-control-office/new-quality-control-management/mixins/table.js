import { checkSearchFormDate } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      searchForm: {},
      localSearchList: [
        {
          label: '',
          value: 'applyName',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索身份证/姓名'
          },
          event: {
            change: e => {
              this.search();
            }
          },
          index: '1,2,3'
        },
        {
          label: '状态',
          value: 'expireStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '未过期',
              element: 'ts-option',
              value: 0
            },
            {
              label: '已过期',
              element: 'ts-option',
              value: 1
            }
          ],
          event: {
            change: e => {
              this.search();
            }
          },
          index: '1'
        },
        {
          label: '院区',
          value: 'applyArea',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [],
          event: {
            change: e => {
              this.search();
            }
          },
          index: '1,2,3'
        },
        {
          label: '考核结果',
          value: 'applyResult',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '合格',
              element: 'ts-option',
              value: 1
            },
            {
              label: '不合格',
              element: 'ts-option',
              value: 2
            }
          ],
          event: {
            change: e => {
              this.search();
            }
          },
          index: '2'
        },
        {
          label: '是否超期任职',
          value: 'isOverdue',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '否',
              element: 'ts-option',
              value: 0
            },
            {
              label: '是',
              element: 'ts-option',
              value: 1
            }
          ],
          event: {
            change: e => {
              this.search();
            }
          },
          index: '1'
        },
        {
          label: '技术职称',
          value: 'applyTechnical',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索技术职称'
          },
          event: {
            change: e => {
              this.search();
            }
          },
          index: '1,2,3'
        },
        {
          label: '任期日期',
          value: 'wheelDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { wheelDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = wheelDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          },
          index: '1'
        },
        {
          label: '考核日期',
          value: 'doDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { doDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = doDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          },
          index: '2'
        },
        {
          label: '中止日期',
          value: 'doDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { doDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = doDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          },
          index: '3'
        }
      ],
      localColumns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          index: '1,2,3'
        },
        {
          label: '状态',
          prop: 'expireStatus',
          width: 80,
          sortable: true,
          sortBy: 'expire_status',
          align: 'center',
          render: (h, { row }) => {
            if (row.expireStatus == null) return;
            let statusObj = {
              0: {
                label: '未过期',
                color: ''
              },
              1: {
                label: '已过期',
                color: 'color: red'
              }
            };
            return h(
              'span',
              { style: statusObj[row.expireStatus].color },
              statusObj[row.expireStatus].label
            );
          },
          index: '1'
        },
        {
          label: '考核结果',
          prop: 'applyResult',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            let resObject = {
              0: {
                label: '考核中',
                color: ''
              },
              1: {
                label: '合格',
                color: ''
              },
              2: {
                label: '不合格',
                color: 'color: red'
              }
            };
            return h(
              'span',
              { style: `${resObject[row.applyResult].color}` },
              resObject[row.applyResult].label
            );
          },
          index: '2'
        },
        {
          label: '质控科室',
          prop: 'orgName',
          width: 120,
          sortable: true,
          sortBy: 'apply_org_id',
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '姓名',
          prop: 'applyName',
          minWidth: 100,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetail(row) }
              },
              row.applyName
            );
          },
          index: '1,2,3'
        },
        {
          label: '技术职称',
          prop: 'applyTechnical',
          sortable: true,
          sortBy: 'apply_technical',
          width: 100,
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '质控率',
          prop: 'qualityRate',
          sortable: true,
          sortBy: 'quality_rate',
          width: 100,
          align: 'center',
          index: '1'
        },
        {
          label: '是否超期任职',
          prop: 'isOverdue',
          width: 100,
          align: 'center',
          index: '1',
          render: (h, { row }) => {
            if (row.isOverdue) {
              return h('span', {}, row.isOverdue == '1' ? '是' : '否');
            }
          }
        },
        {
          label: '任期开始日期',
          prop: 'applyStartDate',
          sortable: true,
          sortBy: 'apply_start_date',
          width: 120,
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '任期结束日期',
          prop: 'applyEndDate',
          sortable: true,
          sortBy: 'apply_end_date',
          width: 120,
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '应任期时长',
          prop: 'applyTermTime',
          width: 80,
          align: 'center',
          index: '1,2,3',
          render: (h, { row }) => {
            if (row.applyTermTime) {
              return h('span', {}, `${row.applyTermTime}个月`);
            }
          }
        },
        {
          label: '考核日期',
          prop: 'optDate',
          width: 120,
          align: 'center',
          index: '2'
        },
        {
          label: '中止日期',
          prop: 'optDate',
          width: 120,
          align: 'center',
          index: '3'
        },
        {
          label: '已担任时长',
          prop: 'applyRealTime',
          width: 120,
          sortable: true,
          sortBy: 'apply_real_time',
          align: 'center',
          index: '2,3',
          render: (h, { row }) => {
            return h('span', {}, `${row.applyRealTime}个月`);
          }
        },
        {
          label: '延长任期结束日期',
          prop: 'punishEndDate',
          width: 160,
          sortable: true,
          sortBy: 'punish_end_date',
          align: 'center',
          index: '1'
        },
        {
          label: '禁用申请期限(月)',
          prop: 'disableTime',
          width: 140,
          align: 'center',
          index: '3'
        },
        {
          label: '处罚次数',
          prop: 'punishNumbers',
          width: 80,
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '处罚结果',
          prop: 'punishResultText',
          width: 120,
          align: 'center',
          index: '1,2,3'
        },
        {
          label: '院区',
          prop: 'applyAreaText',
          align: 'center',
          width: 120,
          index: '1,2,3'
        },
        {
          label: '身份证',
          prop: 'applyIdcard',
          align: 'center',
          width: 180,
          index: '1,2,3'
        },
        {
          label: '联系方式',
          prop: 'applyPhone',
          align: 'center',
          width: 120,
          index: '1,2,3'
        },
        {
          label: '申请时间',
          prop: 'applyDate',
          align: 'center',
          width: 120,
          index: '1,2,3'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 150,
          headerSlots: 'action',
          fixed: 'right',
          index: '1,2,3',
          render: (h, { row }) => {
            let actionList = [];
            if (this.activeTab == 1) {
              actionList = [
                {
                  label: '修改',
                  event: this.handleEdit
                },
                {
                  label: '考核',
                  event: this.handleExamine
                },
                {
                  label: '中止',
                  event: this.handleStop,
                  className: 'actionDel'
                },
                {
                  label: '删除',
                  event: this.handleDelete,
                  className: 'actionDel'
                }
              ];
            }
            if (this.activeTab == 2 || this.activeTab == 3) {
              actionList = [
                {
                  label: '修改',
                  event: this.handleEdit
                },
                {
                  label: '查看',
                  event: this.handleDetails
                },
                {
                  label: '删除',
                  event: this.handleDelete,
                  className: 'actionDel'
                }
              ];
            }
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      deptList: []
    };
  },
  computed: {
    columns() {
      return this.localColumns.filter(
        e => e.index.indexOf(this.activeTab) > -1
      );
    },
    searchList() {
      return this.localSearchList.filter(
        e => e.index.indexOf(this.activeTab) > -1
      );
    }
  },
  methods: {
    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'hosp_area'
      });
      this.searchList[2].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    },
    handleAdd() {
      this.$refs.darwerQualityControlAdd.open({
        title: '新增',
        type: 'add'
      });
    },
    handleDetail(data) {
      this.$refs.darwerQualityControlAdd.open({
        data,
        title: '详情',
        type: 'details'
      });
    },
    handleEdit(data) {
      this.$refs.darwerQualityControlAdd.open({
        data,
        title: '编辑',
        type: 'edit'
      });
    },
    handleDetails(data) {
      this.$refs.darwerDetails.open({
        data,
        title: '详情',
        indexTab: this.activeTab - 1
      });
    },
    async handleStop(data) {
      this.$refs.dialogDiscontinue.open({ data });
    },
    async handleExamine(data) {
      this.$refs.dialogExamine.open({ data });
    },
    async handleDelete(row) {
      try {
        await this.$newConfirm(
          `是否【<span style="color: red">删除</span>】
            <span style="color: #295cf9">${row.applyName}</span>
            质控专员资质`
        );
        this.ajax.qualityApplyDelete(row.id).then(res => {
          if (!res.success) {
            this.$newMessage('error', '【删除】失败');
            return;
          }
          this.$newMessage('success', '【删除】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleGetDeptList() {
      let API = this.ajax.getNoDataApplyDept;
      let res = await API({});
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      this.deptList = res.object || [];
    },
    handleExport() {
      if (!checkSearchFormDate(this)) return;
      let aDom = document.createElement('a');
      let { searchForm } = this.getQueryParam();
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/qualityApply/export?${conditionList.join('&')}`;
      aDom.click();
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { wheelDate = [], doDate = [] } = this.searchForm,
        [startDate = '', endDate = ''] = wheelDate,
        [optStartDate = '', optEndDate = ''] = doDate,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDate,
          endDate,
          optStartDate,
          optEndDate,
          orgId: this.treeCode,
          applyStatus: Number(this.activeTab) - 1,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      delete searchForm.wheelDate;
      delete searchForm.doDate;
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageSize, pageNo };
    }
  }
};
