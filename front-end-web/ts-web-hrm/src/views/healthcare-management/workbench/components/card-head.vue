<template>
  <div class="item-head-border">
    <div class="top-title">
      <p class="title">
        <img v-if="img" :src="img" />
        {{ title }}
      </p>
      <el-popover
        placement="right"
        width="400"
        trigger="hover"
        popper-class="popverContent"
      >
        <vex-icon
          class="vxe-icon-question-circle popverIcon"
          slot="reference"
          v-if="showPopover"
        />
        <slot name="popverContent"></slot>
      </el-popover>
      <div class="operate-container">
        <slot name="operate"></slot>
      </div>
      <div class="operate-container-button">
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    showPopover: {
      type: Boolean,
      default: false
    },
    img: {
      type: String,
      default: ''
    }
  }
};
</script>
<style lang="scss" scoped>
.item-head-border {
  display: flex;
  align-items: center;
  height: 20px;
  width: 100%;
  margin-bottom: 4px;
  .popverIcon {
    font-size: 18px;
    line-height: 25px;
    margin-left: 5px;
  }
  .top-title {
    width: 100%;
    padding: 0 0 0 20px;
    display: flex;
    justify-content: space-between;
    .title {
      color: #333;
      font-size: 15px;
      line-height: 20px;
      font-weight: 550;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin: 0;
      img {
        width: 18px;
        height: 18px;
        margin-right: 10px;
      }
    }
    .operate-container {
      display: inline-block;
      flex: 1;
    }
  }
}
</style>
