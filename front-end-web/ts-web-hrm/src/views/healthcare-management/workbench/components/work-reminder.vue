<template>
  <div class="work-reminder flex-column">
    <card-head
      title="工作提醒"
      :img="require('@/assets/img/work-bench/reminder.png')"
      :showPopover="true"
    >
      <template #popverContent>
        <p v-for="(item, index) in workPopver" :key="index">{{ item }}</p>
      </template>
    </card-head>
    <div class="card">
      <div class="card-left flex-column">
        <div class="cardModule">
          <div
            v-for="item in cardList"
            :key="item.index"
            class="cardItem"
            :class="activeIndex == item.index ? 'active' : ''"
            @click="choseItem(item.index)"
          >
            <div class="itemLeft">
              <img v-if="activeIndex != item.index" :src="item.img" />
              <img v-else :src="item.activeImg" />
            </div>
            <div class="itemRight flex-column">
              <p class="ellipsis">{{ item.name }}</p>
              <p class="bold ellipsis">{{ numObject[item.key] }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="card-right flex-column" v-loading="loading">
        <el-scrollbar
          style="flex: 1;"
          ref="scroll"
          v-infinity-scroll="{
            loadMethod: getFlowList,
            selector: '.options-scrollbar',
            hasFinished: false,
            hasLoading: true
          }"
          wrap-class="options-scrollbar"
          wrap-style="overflow-x: hidden;"
        >
          <div
            class="eventItem"
            v-for="(item, index) in eventList"
            :key="index"
            @click="eventItemClick(item)"
          >
            <div class="eventTitle ellipsis">
              {{ item[titleKey[activeIndex]] }}
            </div>
            <div class="eventTime">
              <span>{{ item.createDate }}</span>
              <span>></span>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import cardHead from './card-head.vue';
import indexConfig from './indexConfig';
import infinityScroll from '@/util/infinityScroll';
export default {
  components: { cardHead },
  mixins: [infinityScroll, indexConfig],
  data() {
    return {
      activeIndex: 1,
      eventList: [],
      workPopver: [
        '工作提醒指标说明：',
        '1、待办事项：医务系统需要审批的事项。',
        '2、待阅消息：发布在医务管理系统中的待阅消息。',
        '3、风险提醒：不良事件、危急值事件。',
        '4、通知公告：发布在医务管理系统中的通知公告。'
      ],
      cardList: [
        {
          index: 1,
          name: '待办事项',
          img: require('@/assets/img/work-bench/wait.png'),
          activeImg: require('@/assets/img/work-bench/wait-active.png'),
          key: 'toDoNumbers',
          API: this.ajax.getMyHandleWorkflowList
        },
        {
          index: 2,
          name: '待阅事项',
          img: require('@/assets/img/work-bench/read.png'),
          activeImg: require('@/assets/img/work-bench/read-active.png'),
          key: 'toReadNumbers',
          API: this.ajax.getInformationList
        },
        {
          index: 3,
          name: '风险事项',
          img: require('@/assets/img/work-bench/risk.png'),
          activeImg: require('@/assets/img/work-bench/risk-active.png'),
          key: 'riskNumbers',
          API: this.ajax.getRiskList
        },
        {
          index: 4,
          name: '通知公告',
          img: require('@/assets/img/work-bench/notice.png'),
          activeImg: require('@/assets/img/work-bench/notice-active.png'),
          key: 'noticeNumbers',
          API: this.ajax.getInformationList
        }
      ],
      numObject: {},
      titleKey: {
        1: 'workflowTitle',
        2: 'informationTitle',
        3: '',
        4: 'informationTitle'
      },
      wrapDom: null,
      loading: false,
      pageNo: 1
    };
  },
  watch: {
    activeIndex: {
      handler(val) {
        this.wrapDom = null;
        this.$nextTick(() => {
          if (!this.wrapDom) {
            this.wrapDom = this.$refs.scroll.$el.querySelector(
              '.options-scrollbar'
            );
          }
          this.pageNo = 1;
          this.wrapDom.resetInfinityScrolling();
        });
      },
      immediate: true
    }
  },
  methods: {
    async refresh() {
      this.$nextTick(() => {
        this.getWorkRemind();
      });
    },
    // 获取工作提醒数量
    async getWorkRemind() {
      let res = await this.ajax.getWorkRemind({});
      this.numObject = res.object || {};
    },
    choseItem(index) {
      if (this.activeIndex != index) this.activeIndex = index;
    },
    eventItemClick(item) {
      if (this.activeIndex == '1') {
        this.handleDeal(item);
      }
      if (this.activeIndex == '2' || this.activeIndex == '4') {
        this.handleInformationDetails(item.id);
      }
    },
    async getFlowList(cb) {
      let param = {};
      if (this.activeIndex == 1) {
        param = {
          handleStatus: 1,
          sidx: 'inst.create_date',
          sord: 'desc'
        };
      }
      if (this.activeIndex == 2) {
        param = {
          informationStatus: 1,
          index: 5,
          sidx: 'releaseDate',
          sord: 'desc',
          medType: 2
        };
      }
      if (this.activeIndex == 4) {
        param = {
          informationStatus: 1,
          index: 5,
          sidx: 'releaseDate',
          sord: 'desc',
          medType: 1
        };
      }
      let params = {
        ...param,
        pageNo: 1,
        pageSize: 20
      };
      params.pageNo = this.pageNo;
      this.loading = true;
      let res = await this.cardList[this.activeIndex - 1].API(params);
      this.loading = false;
      let dataList = res.rows || res.object || [];
      cb(!(dataList && dataList.length));
      if (this.pageNo == 1) this.eventList = [];
      if (dataList && dataList.length) {
        this.eventList.push(...dataList);
      }
      this.pageNo++;
    }
  }
};
</script>

<style lang="scss" scoped>
.work-reminder {
  width: calc(60% - 8px);
  margin-bottom: 8px;
  /deep/ {
    .card {
      flex: 1;
      padding: 8px;
      background: #fff;
      border-radius: 8px;
      display: flex;
      .card-left {
        width: 20%;
        max-width: 360px;
        min-width: 240px;
        .cardModule {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          .cardItem {
            width: calc(50% - 8px);
            padding: 2px 2px 2px 8px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #e5efff 100%, #f0f2ff 100%);
            cursor: pointer;
            &.active {
              background-image: linear-gradient(to right, #36d1b6, #3494e6);
              box-shadow: 0 2px 4px rgba(24, 109, 245, 0.3);
              p {
                color: #fff;
              }
              .itemLeft {
                background: #ffffff2c;
              }
            }
            .itemLeft {
              width: 36px;
              height: 36px;
              background: #186df532;
              border-radius: 18px;
              position: relative;
              img {
                width: 18px;
                height: 18px;
                position: absolute;
                top: 9px;
                left: 9px;
              }
            }
            .itemRight {
              margin-left: 3px;
              flex: 1;
              width: calc(100% - 44px);
              p {
                font-size: 14px;
                line-height: 18px;
                &.bold {
                  font-weight: bold;
                }
              }
            }
          }
          &:not(:last-child) {
            margin-bottom: 8px;
          }
        }
      }
      .card-right {
        flex: 1;
        margin-left: 8px;
        width: 200px;
        height: 90px;
        .eventItem {
          height: 26px;
          padding: 6px;
          border-left: 2px solid #186df5;
          background: rgba(32, 32, 32, 0.03);
          display: flex;
          justify-content: space-between;
          cursor: pointer;
          .eventTitle {
            line-height: 14px;
            font-size: 12px;
            color: #202020;
          }
          .eventTime {
            line-height: 14px;
            width: 138px;
            span {
              font-size: 12px;
              &:not(:last-child) {
                margin-right: 8px;
              }
              &:last-child {
                font-weight: bold;
              }
            }
          }
          &:not(:last-child) {
            margin-bottom: 6px;
          }
        }
      }
    }
  }
}
</style>
