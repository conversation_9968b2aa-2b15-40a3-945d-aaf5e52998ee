<template>
  <div class="talent-structure-container">
    <div class="content">
      <div class="content-item contract-contaiern">
        <!-- <div class="title">优秀个人<span class="noBold">(前10)</span></div> -->
        <div class="title">会诊质量优秀个人排名</div>
        <div
          class="contract-echarts-module"
          ref="pie1"
          v-loading="loading1"
        ></div>
      </div>

      <div class="content-item contract-contaiern">
        <!-- <div class="title">
          个人<span class="redBold">黑榜</span>
          <span class="noBold">(前10)</span>
        </div> -->
        <div class="title">会诊质量个人警示榜</div>
        <div
          class="contract-echarts-module"
          ref="pie2"
          v-loading="loading2"
        ></div>
      </div>

      <div class="content-item contract-contaiern">
        <!-- <div class="title">先进红榜<span class="noBold">(前10)</span></div> -->
        <div class="title">会诊质量优秀科室排名</div>
        <div
          class="contract-echarts-module"
          ref="pie3"
          v-loading="loading3"
        ></div>
      </div>

      <div class="content-item contract-contaiern">
        <!-- <div class="title">
          科室<span class="redBold">黑榜</span>
          <span class="noBold">(前10)</span>
        </div> -->
        <div class="title">会诊质量科室警示榜</div>
        <div
          class="contract-echarts-module"
          ref="pie4"
          v-loading="loading4"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pie1: undefined,
      pie2: undefined,
      pie3: undefined,
      pie4: undefined,
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false
    };
  },
  methods: {
    async render(date) {
      // 调用接口查询
      let param = {
        startDate: date[0],
        endDate: date[1],
        pageSize: 10
      };
      this.loading1 = true;
      this.loading2 = true;
      this.loading3 = true;
      this.loading4 = true;
      this.ajax.personRedBlackRank({ ...param, sord: 'desc' }).then(res => {
        this.renderPersonTop10Echarts(res.object || []);
      });
      this.ajax.personRedBlackRank({ ...param, sord: 'asc' }).then(res => {
        this.renderPersonBottom10Echarts(res.object || []);
      });
      this.ajax.deptRedBlackRank({ ...param, sord: 'desc' }).then(res => {
        this.renderDeptTop10Echarts(res.object || []);
      });
      this.ajax.deptRedBlackRank({ ...param, sord: 'asc' }).then(res => {
        this.renderDeptBottom10Echarts(res.object || []);
      });
    },
    // 个人及时会诊红榜(前10)
    renderPersonTop10Echarts(data) {
      data.reverse();
      let yData = data.map(e => e.fns_employee_name);
      let serData = data.map(e => e.total_score);
      let options = {
        legend: null,
        grid: {
          left: '3%',
          right: '8%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          data: yData
        },
        series: [
          {
            name: '',
            type: 'bar',
            label: {
              show: true,
              position: 'right',
              formatter: params => params.value
            },
            emphasis: {
              focus: 'series'
            },
            itemStyle: {
              color: '#33918C' // 柱体颜色设置
            },
            data: serData
          }
        ]
      };
      let _self = this;
      if (!_self.pie1) {
        _self.pie1 = _self.$echarts.init(_self.$refs.pie1);
      }
      _self.pie1.clear();
      _self.pie1.setOption(options);
      this.loading1 = false;
    },
    // 个人及时会诊黑榜(前10)
    renderPersonBottom10Echarts(data) {
      let yData = data.map(e => e.fns_employee_name);
      let serData = data.map(e => e.total_score);
      let options = {
        legend: null,
        grid: {
          left: '3%',
          right: '8%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          data: yData
        },
        series: [
          {
            name: 'Direct',
            type: 'bar',
            label: {
              show: true,
              position: 'right',
              formatter: params => params.value
            },
            emphasis: {
              focus: 'series'
            },
            itemStyle: {
              color: '#D16B39' // 柱体颜色设置
            },
            data: serData
          }
        ]
      };
      let _self = this;
      if (!_self.pie2) {
        _self.pie2 = _self.$echarts.init(_self.$refs.pie2);
      }
      _self.pie2.clear();
      _self.pie2.setOption(options);
      this.loading2 = false;
    },
    // 科室及时会诊红榜(前10)
    renderDeptTop10Echarts(data) {
      let date = data.map(e => {
        return {
          value: e.total_score,
          name: e.cslt_org_name
        };
      });
      let options = {
        tooltip: {
          trigger: 'item',
          formatter: function(parms) {
            return `${parms.data.name} ${parms.data.value}`;
          }
        },
        series: [
          {
            top: '5%',
            sort: 'descending',
            type: 'funnel',
            minSize: '2%',
            left: '40%',
            right: '2%',
            gap: 2,
            height: '90%',
            label: {
              position: 'left',
              formatter: function(parms) {
                return `${parms.data.name}`;
              }
            },
            labelLine: {
              lineStyle: {
                width: 2
              }
            },
            itemStyle: {
              // 为每个部分单独设置颜色
              color: function(params) {
                // 根据数据的不同返回不同的颜色
                const colors = [
                  '#387F84',
                  '#D16F3C',
                  '#03998E',
                  '#FFA636',
                  '#FF8E4F'
                ]; // 示例颜色数组
                return colors[params.dataIndex % colors.length]; // 使用数据索引来选择颜色
              }
            },
            data: date
          },
          {
            top: '5%',
            sort: 'descending',
            type: 'funnel',
            minSize: '2%',
            left: '40%',
            right: '2%',
            gap: 2,
            height: '90%',
            label: {
              position: 'inside',
              formatter: function(parms) {
                return `${parms.data.value}`;
              },
              color: '#fff'
            },
            itemStyle: {
              // 为每个部分单独设置颜色
              color: function(params) {
                // 根据数据的不同返回不同的颜色
                const colors = [
                  '#387F84',
                  '#D16F3C',
                  '#03998E',
                  '#FFA636',
                  '#FF8E4F'
                ]; // 示例颜色数组
                return colors[params.dataIndex % colors.length]; // 使用数据索引来选择颜色
              }
            },
            data: date
          }
        ]
      };

      let _self = this;
      if (!_self.pie3) {
        _self.pie3 = _self.$echarts.init(_self.$refs.pie3);
      }
      _self.pie3.clear();
      _self.pie3.setOption(options);
      this.loading3 = false;
    },
    // 科室及时会诊黑榜(前10)
    renderDeptBottom10Echarts(data) {
      let date = data.map(e => {
        return {
          value: e.total_score,
          name: e.cslt_org_name
        };
      });
      let options = {
        tooltip: {
          trigger: 'item',
          formatter: function(parms) {
            return `${parms.data.name} ${parms.data.value}`;
          }
        },
        series: [
          {
            top: '5%',
            sort: 'ascending',
            type: 'funnel',
            minSize: '2%',
            left: '40%',
            right: '2%',
            gap: 2,
            height: '90%',
            label: {
              position: 'left',
              formatter: function(parms) {
                return `${parms.data.name}`;
              }
            },
            labelLine: {
              lineStyle: {
                width: 2
              }
            },
            itemStyle: {
              // 为每个部分单独设置颜色
              color: function(params) {
                // 根据数据的不同返回不同的颜色
                const colors = [
                  '#387F84',
                  '#D16F3C',
                  '#03998E',
                  '#FFA636',
                  '#FF8E4F'
                ]; // 示例颜色数组
                return colors[params.dataIndex % colors.length]; // 使用数据索引来选择颜色
              }
            },
            data: date
          },
          {
            top: '5%',
            sort: 'ascending',
            type: 'funnel',
            minSize: '2%',
            left: '40%',
            right: '2%',
            gap: 2,
            height: '90%',
            label: {
              position: 'inside',
              formatter: function(parms) {
                return `${parms.data.value}`;
              },
              color: '#fff'
            },
            itemStyle: {
              // 为每个部分单独设置颜色
              color: function(params) {
                // 根据数据的不同返回不同的颜色
                const colors = [
                  '#387F84',
                  '#D16F3C',
                  '#03998E',
                  '#FFA636',
                  '#FF8E4F'
                ]; // 示例颜色数组
                return colors[params.dataIndex % colors.length]; // 使用数据索引来选择颜色
              }
            },
            data: date
          }
        ]
      };

      let _self = this;
      if (!_self.pie4) {
        _self.pie4 = _self.$echarts.init(_self.$refs.pie4);
      }
      _self.pie4.clear();
      _self.pie4.setOption(options);
      this.loading4 = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.talent-structure-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .content {
    flex: 1;
    display: flex;
    padding: 8px;
    overflow: hidden;
    box-sizing: border-box;

    .content-item {
      flex: 1;
      margin-right: 10px;
      overflow: hidden;
      &:last-child {
        margin-right: 0;
      }

      .title {
        font-size: 13px;
        color: #333333;
        padding-left: 8px;
        line-height: 28px;
        font-weight: bold;
        border: 1px solid #eee;
        .redBold {
          font-weight: bold;
          color: red;
        }
        .noBold {
          font-weight: 400;
        }
      }

      &.contract-contaiern {
        display: flex;
        flex-direction: column;

        .contract-echarts-module {
          flex: 1;
          overflow: hidden;
          border: 1px solid #eee;
          border-top: 0px;
        }
      }
    }
  }
}
</style>
