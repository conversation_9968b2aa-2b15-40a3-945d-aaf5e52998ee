<template>
  <div class="to-be-arranged">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="actOrgId">
        <input-ztree
          v-model="searchForm.actOrgId"
          ref="tree"
          placeholder="请选择邀请科室"
          :treeData="deptTreeData"
          :defaultExpandedKeys="defaultExpandedKeys"
          style="width: 100%"
          @ok="handleOk"
          key="code"
        />
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_to_be_arranged"
      class="form-table"
      ref="table"
      :stripe="false"
      :defaultSort="{
        sidx: 'create_date',
        sord: 'desc'
      }"
      :columns="columns"
      pageAlign="center"
      :cell-style="cellStyle"
      @refresh="handleRefreshTable"
    />
    <darwer-arranged ref="darwerArranged" @refresh="search()" />
  </div>
</template>

<script>
import toBeArrangedTable from '../mixins/to-be-arranged-table';
import darwerArranged from './darwer-arranged.vue';
export default {
  mixins: [toBeArrangedTable],
  components: { darwerArranged },
  deta() {
    return {
      treeCode: ''
    };
  },
  methods: {
    async refresh() {
      await this.getAllDictItemList();
      await this.getTree();
      this.handleRefreshTable();
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.field == 'actions') {
        return {
          backgroundColor: '#eceef3'
        };
      }
    },
    search(treeCode = '') {
      this.$refs.table.pageNo = 1;
      if (treeCode) {
        this.treeCode = treeCode;
      }
      this.handleRefreshTable();
    },
    handleArrange(data) {
      this.$refs.darwerArranged.open({
        title: '安排',
        type: 'edit',
        data
      });
    },
    handleGetSeacrhQueryParam() {
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          actStatus: '0',
          csltType: this.treeCode,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageNo, pageSize };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let res = await this.ajax.getCsltAppyList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.to-be-arranged {
  flex: 1;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  .form-table {
    margin-top: 20px;
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    .primary-span {
      color: #295cf9;
      cursor: pointer;
      text-align: left;
    }
  }
}
</style>
