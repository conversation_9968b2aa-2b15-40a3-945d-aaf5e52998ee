export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'actEmployeeNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索工号/会诊医生'
          }
        },
        {
          label: '会诊类型',
          value: 'csltMold',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: []
        },
        {
          label: '会诊科室',
          value: 'actOrgId'
        },
        {
          label: '创建日期',
          value: 'createDate',
          element: 'ts-date-picker',
          elementProp: {
            clearable: true,
            placeholder: '请选择创建日期',
            valueFormate: 'yyyy-MM-DD',
            formate: 'yyyy-MM-DD'
          }
        },
        {
          label: '申请日期',
          value: 'csltTime',
          element: 'ts-date-picker',
          elementProp: {
            clearable: true,
            placeholder: '请选择创建日期',
            valueFormate: 'yyyy-MM-DD',
            formate: 'yyyy-MM-DD'
          }
        },
        {
          label: '计划会诊日期',
          value: 'planCsltTime',
          element: 'ts-date-picker',
          elementProp: {
            clearable: true,
            placeholder: '请选择创建日期',
            valueFormate: 'yyyy-MM-DD',
            formate: 'yyyy-MM-DD'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 40
        },
        {
          label: '状态',
          prop: 'csltStatus',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            return h('span', {}, row.csltStatusName);
          }
        },
        // {
        //   label: '倒计时(h)',
        //   prop: 'remainingHour',
        //   width: 100,
        //   align: 'center',
        //   render: (h, { row }) => {
        //     if (Number(row.remainingHour) < 0) {
        //       return h('span', { style: 'color: red;' }, row.remainingHour);
        //     } else {
        //       return h('span', {}, row.remainingHour);
        //     }
        //   }
        // },
        {
          label: '工号',
          prop: 'actEmployeeNo',
          width: 100,
          align: 'center'
        },
        {
          label: '会诊医生',
          prop: 'actEmployeeName',
          width: 100,
          align: 'center'
        },
        {
          label: '技术职称',
          prop: 'actJobtitle',
          width: 100,
          align: 'center'
        },
        {
          label: '会诊类别',
          prop: 'csltTypeName',
          width: 100,
          align: 'center'
        },
        {
          label: '会诊类型',
          prop: 'csltMoldName',
          width: 100,
          align: 'center'
        },
        {
          label: '院区',
          prop: 'hospAreaName',
          width: 100,
          align: 'center'
        },
        {
          label: '被邀科室',
          prop: 'csltOrgName',
          width: 100,
          align: 'center'
        },
        {
          label: '会诊级别',
          prop: 'csltLvName',
          width: 100,
          align: 'center'
        },
        {
          label: '申请科室',
          prop: 'appyOrgName',
          width: 100,
          align: 'center'
        },
        {
          label: '申请人',
          prop: 'appyEmpName',
          width: 100,
          align: 'center'
        },
        {
          label: '申请时间',
          prop: 'appyTime',
          width: 100,
          align: 'center'
        },
        {
          label: '安排时间',
          prop: 'actTime',
          width: 140,
          align: 'center'
        },
        {
          label: '申请会诊日期',
          prop: 'csltTime',
          width: 100,
          align: 'center'
        },
        {
          label: '计划会诊时间',
          prop: 'planCsltTime',
          width: 210,
          align: 'center'
        },
        {
          label: '操作者',
          prop: 'updateUserName',
          width: 100,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '查看',
                event: this.handleArrange
              }
            ];
            if (['1', '2', '6'].indexOf(row.csltStatus) > -1) {
              actionList.push({
                label: '撤销',
                event: this.handleBack
              });
            }
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  methods: {
    async getTree() {
      const tree = await this.ajax.getOrganizationGetTree2();
      this.deptTreeData = tree.object || [];
      this.defaultExpandedKeys = [this.deptTreeData[0].id];
    },
    // 部门树确定回调
    handleOk(list) {
      let id = [];
      list.forEach(item => {
        id.push(item.code);
      });
      this.searchForm.actOrgId = id.join(',');
    },

    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'cslt_mold'
      });
      this.searchList[0].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    }
  }
};
