<template>
  <div class="delivery-inspection-setting">
    <el-tabs v-model="activeTab" class="new-tabs-container">
      <el-tab-pane name="1" label="无物价项目"></el-tab-pane>
      <el-tab-pane name="2" label="有物价项目"></el-tab-pane>
    </el-tabs>
    <div class="template-two-zone">
      <div class="right">
        <new-ts-search-bar
          v-model="searchForm"
          :formList="searchList"
          :elementCol="14"
          @search="search"
          :resetData="reset"
        >
          <template slot="right">
            <ts-button type="primary" @click="handleAdd" v-if="activeTab == '1'"
              >新增</ts-button
            >
            <ts-button
              class="shallowButton"
              @click="handleImport"
              v-if="activeTab == '1'"
              >导入</ts-button
            >
            <ts-button class="shallowButton" @click="handleExport"
              >导出</ts-button
            >
          </template>
        </new-ts-search-bar>
        <TsVxeTemplateTable
          id="table_delivery-inspection-setting"
          class="form-table"
          ref="table"
          :defaultSort="{
            sidx: 'create_date',
            sord: 'desc'
          }"
          :columns="columns"
          @refresh="handleRefreshTable"
        />
      </div>
    </div>
    <!-- 公用导入 -->
    <base-import
      ref="baseImport"
      :ImportConfiguration="ImportConfiguration"
      @refresh="handleRefreshTable"
    />
    <drawer-delivery-inspection-add
      ref="drawerDeliveryInspectionAdd"
      :sampleTypeList="sampleTypeList"
      :testOrgList="testOrgList"
      @refresh="search"
    />
  </div>
</template>
<script>
import table from './mixins/table';
import drawerDeliveryInspectionAdd from './components/drawer-delivery-inspection-add.vue';
export default {
  mixins: [table],
  components: { drawerDeliveryInspectionAdd },
  data() {
    return {
      activeTab: '1',
      ImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/medDeliveryDict/downloadTemplate',
        importTempalteName: '外送检验项目配置导入',
        importApi: 'importMedDeliveryDict'
      }
    };
  },
  watch: {
    activeTab: {
      handler() {
        this.$nextTick(() => {
          this.search();
        });
      }
    }
  },
  methods: {
    async refresh() {
      await this.getAllDictItemList();
      await this.search();
    },
    reset() {
      return {};
    },
    handleImport() {
      this.$refs.baseImport.open({
        title: '导入外送检验项目',
        increment: true,
        quantity: false
      });
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let API = this.ajax.medDeliveryDictList;
      let res = await API(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/template.scss';
.template-two-zone {
  flex: 1;
}
.delivery-inspection-setting {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    .primary-span {
      color: $primary-blue;
      cursor: pointer;
    }
  }
}
</style>
