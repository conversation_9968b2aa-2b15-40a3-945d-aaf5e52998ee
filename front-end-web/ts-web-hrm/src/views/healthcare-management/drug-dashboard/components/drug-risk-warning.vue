<template>
  <div class="drug-risk-warning flex-column borders">
    <colmun-head title="药品风险预警" background="#efeff4" />
    <div class="warningModule">
      <div class="content flex-column borders">
        <item-head title="科室风险(TOP1)" />
        <el-scrollbar
          style="width:100%; height: 100%;"
          wrap-style="overflow-x: hidden;"
        >
          <ul class="data-list">
            <li
              class="data-item"
              v-for="(item, index) in resultDeptTop1"
              :key="index"
            >
              <div class="data-item-left">
                <img
                  class="data-item-img"
                  src="@/assets/img/home-page/hrm-home-page-file.svg"
                  alt=""
                />
                <span class="data-item-label">
                  {{ item.title }}
                </span>
              </div>
              <p class="data-item-label-right">【{{ item.dept }}】</p>
            </li>
          </ul>
        </el-scrollbar>
      </div>
      <div class="content flex-column borders">
        <item-head title="个人风险(TOP1)" />
        <el-scrollbar
          style="width:100%; height: 100%;"
          wrap-style="overflow-x: hidden;"
        >
          <ul class="data-list">
            <li
              class="data-item"
              v-for="(item, index) in resultDocTop1"
              :key="index"
            >
              <div class="data-item-left">
                <img
                  class="data-item-img"
                  src="@/assets/img/home-page/hrm-home-page-file.svg"
                  alt=""
                />
                <span class="data-item-label">
                  {{ item.title }}
                </span>
              </div>
              <p class="data-item-label-right">【{{ item.doc }}】</p>
            </li>
          </ul>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import ItemHead from './item-head.vue';
export default {
  components: { ItemHead },
  data() {
    return {
      date: [],
      deptList: [
        {
          titleStart: '高额异常住院费用示警',
          titleEnd: '次',
          key: 'ZY_GEYCFYSJ',
          valKey: 'F_VAL'
        },
        // {
        //   titleStart: '药占比',
        //   titleEnd: '%',
        //   key: 'YP_ZSR_ZB',
        //   valKey: 'F_VAL'
        // },
        {
          titleStart: '门诊药占比',
          titleEnd: '%',
          key: 'MZ_YP_ZSR_ZB',
          valKey: 'F_VAL'
        },
        {
          titleStart: '住院药占比',
          titleEnd: '%',
          key: 'ZY_YP_ZSR_ZB',
          valKey: 'F_VAL'
        },
        {
          titleStart: '门诊大处方数',
          titleEnd: '例',
          key: 'MZ_DCFS',
          valKey: 'F_VAL'
        },
        {
          titleStart: '超药品说明书适应症病历',
          titleEnd: '例',
          key: 'CYPSMS_SYZBL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '集中带量药品采购率',
          titleEnd: '%',
          key: 'DLYP_CGL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '集中带量药品使用率',
          titleEnd: '%',
          key: 'DLYP_SYL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '处方合格率',
          titleEnd: '%',
          key: 'CF_HGL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '抗肿瘤药物消耗金额',
          titleEnd: '元',
          key: 'KZLYWXH',
          valKey: 'F_VAL'
        },
        {
          titleStart: '长期处方',
          titleEnd: '例',
          key: 'MZ_CYPCF',
          valKey: 'F_VAL'
        },
        {
          titleStart: '抗菌药物使用强度',
          titleEnd: ' DDDs',
          key: 'KJYW_SYQD',
          valKey: 'F_VAL'
        },
        {
          titleStart: '超适应症用药',
          titleEnd: '例',
          key: 'CSYZYY',
          valKey: 'F_VAL'
        }
      ],
      resultDeptTop1: [],
      docList: [
        {
          titleStart: '高额异常住院费用示警',
          titleEnd: '次',
          key: 'ZY_GEYCFYSJ',
          valKey: 'F_VAL'
        },
        {
          titleStart: '药占比',
          titleEnd: '%',
          key: 'YP_ZSR_ZB',
          valKey: 'F_VAL'
        },
        {
          titleStart: '门诊药占比',
          titleEnd: '%',
          key: 'MZ_YP_ZSR_ZB',
          valKey: 'F_VAL'
        },
        {
          titleStart: '住院药占比',
          titleEnd: '%',
          key: 'ZY_YP_ZSR_ZB',
          valKey: 'F_VAL'
        },
        {
          titleStart: '门诊大处方数',
          titleEnd: '例',
          key: 'MZ_DCFS',
          valKey: 'F_VAL'
        },
        {
          titleStart: '超药品说明书适应症病历',
          titleEnd: '例',
          key: 'CYPSMS_SYZBL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '处方合格率',
          titleEnd: '%',
          key: 'CF_HGL',
          valKey: 'F_VAL'
        },
        {
          titleStart: '长期处方',
          titleEnd: '例',
          key: 'MZ_CYPCF',
          valKey: 'F_VAL'
        },
        {
          titleStart: '超适应症用药',
          titleEnd: '例',
          key: 'CSYZYY',
          valKey: 'F_VAL'
        }
      ],
      resultDocTop1: []
    };
  },
  methods: {
    async refresh(date) {
      this.date = date;
      this.$nextTick(() => {
        this.getKsTopOne();
        this.getYsTopOne();
      });
    },
    async getKsTopOne() {
      let res = await this.ajax.getKsTopOne({
        busiDateBegin: this.date[0],
        busiDateEnd: this.date[1]
      });
      let list = this.deptList.map(e => {
        let obj = res.object.find(i => i.KEY == e.key);
        if (obj) {
          if (e.titleEnd == '%') {
            obj[e.valKey] = (obj[e.valKey] * 100).toFixed(2);
          }
          if (obj[e.valKey]) {
            obj[e.valKey] = Number(obj[e.valKey]).toLocaleString('en-US');
          }
          return {
            title: `${e.titleStart}${obj[e.valKey] || '--'}${e.titleEnd}`,
            dept: obj.KSMC || ''
          };
        }
      });
      this.resultDeptTop1 = list;
    },
    async getYsTopOne() {
      let res = await this.ajax.getYsTopOne({
        busiDateBegin: this.date[0],
        busiDateEnd: this.date[1]
      });
      let list = this.docList.map(e => {
        let obj = res.object.find(i => i.KEY == e.key);
        if (obj) {
          if (e.titleEnd == '%') {
            obj[e.valKey] = (obj[e.valKey] * 100).toFixed(2);
          }
          if (obj[e.valKey]) {
            obj[e.valKey] = Number(obj[e.valKey]).toLocaleString('en-US');
          }
          return {
            title: `${e.titleStart}${obj[e.valKey] || '--'}${e.titleEnd}`,
            doc: obj.YSMC || ''
          };
        }
      });
      this.resultDocTop1 = list;
    }
  }
};
</script>

<style lang="scss" scoped>
.drug-risk-warning {
  flex: 2;
  /deep/ {
    .warningModule {
      flex: 1;
      display: flex;
      padding: 4px 4px;
      .content {
        flex: 1;
        height: 200px;
        &:not(:last-child) {
          margin-right: 4px;
        }
      }
      .data-list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .data-item {
          padding: 0 8px;
          height: 36px;
          line-height: 36px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #fff;
          &:nth-child(2n - 1) {
            background-color: #fcf2f1;
          }
          .data-item-left {
            .data-item-img {
              width: 20px;
              margin-right: 4px;
            }
            .data-item-label {
              font-weight: 400;
              font-size: 13px;
              color: #333333;
            }
          }
          .data-item-label-right {
            display: flex;
            justify-content: space-around;
            margin: 0;
            line-height: 36px;
            align-items: center;
            .cellDot {
              text-align: center;
              height: 20px;
              width: 20px;
              line-height: 20px;
              font-size: 12px;
              background: $primary-blue;
              color: #fff;
              border-radius: 12px;
            }
            .cellValue {
              margin-left: 4px;
              line-height: 24px;
              margin-right: 4px;
            }
          }
          .data-item-val {
            font-weight: 700;
            color: #f96c17;
            margin-right: 4px;
            line-height: 36px;
          }
        }
      }
    }
  }
}
</style>
