<template>
  <div class="item-head-border" :style="{ background }">
    <div class="top-title">
      <p class="title">
        <span class="lefts"></span>
        {{ title }}
      </p>
      <el-popover
        placement="right"
        width="400"
        trigger="hover"
        popper-class="popverContent"
      >
        <i
          class="el-icon-warning-outline popverIcon"
          slot="reference"
          v-if="showPopover"
        ></i>
        <slot name="popverContent"></slot>
      </el-popover>
      <div class="operate-container">
        <slot name="operate"></slot>
      </div>
      <div class="operate-container-button">
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    showPopover: {
      type: Boolean,
      default: false
    },
    background: {
      type: String,
      default: '#fff'
    }
  }
};
</script>
<style lang="scss" scoped>
.item-head-border {
  display: flex;
  align-items: center;
  height: 32px;
  width: 100%;
  border-bottom: 1px solid #eee;
  border-radius: 8px;
  .popverIcon {
    font-size: 18px;
    margin-left: 5px;
    margin-top: 2px;
  }
  .top-title {
    width: 100%;
    padding: 0 8px 0 30px;
    display: flex;
    justify-content: space-between;
    .title {
      color: #333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin: 0;
    }
    .operate-container {
      display: inline-block;
      flex: 1;
    }
  }
}
</style>
