<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @nodeCheck="clickItemTree"
      >
      </new-base-search-tree>
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
      </new-ts-search-bar>
      <TsVxeTemplateTable
        id="table_qualification-task-management"
        class="form-table"
        ref="table"
        :defaultSort="{
          sidx: 'create_date',
          sord: 'desc'
        }"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
    <dialog-quality-details
      ref="dialogQualityDetails"
      @refresh="handleRefreshTable"
    />
    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    />
    <dialog-upload ref="dialogUpload" />
  </div>
</template>

<script>
import table from './mixins/table';
import dialogQualityDetails from '../components/dialog-quality-details.vue';
import dialogUpload from './components/dialog-upload.vue';
import {
  checkSearchFormDate,
  resetSearchFormSelect,
  supplementSearchSelect
} from '@/unit/commonHandle.js';
export default {
  components: { dialogQualityDetails, dialogUpload },
  mixins: [table],
  data() {
    return {
      treeCode: '',
      apiFunction: this.ajax.getDeptTreeList,
      data: [],
      previewFile: '',
      previewFileList: []
    };
  },
  methods: {
    // 树 item点击
    clickItemTree(node) {
      this.treeCode = node.map(e => e.code).join(',');
      this.search();
    },
    async refresh() {
      await this.getAllDictItemList();
      this.searchForm = {
        ...resetSearchFormSelect(this.searchList)
      };
      supplementSearchSelect(this.searchList);
      await this.search();
    },
    async search() {
      this.pageNo = 1;
      await this.handleRefreshTable();
    },
    reset() {
      return {
        ...resetSearchFormSelect(this.searchList),
        orgId: this.treeCode
      };
    },
    async handleRefreshTable() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let res = await this.ajax.getRoundsTaskPageList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
</style>
