<template>
  <div class="problem-analysis flex-column">
    <colmun-head title="查房问题分析" background="#fff">
      <template slot="right">
        <div class="activeTab">
          <div
            class="item left"
            :class="activeTab == 1 ? 'active' : ''"
            @click="selectType(1)"
          >
            临床
          </div>
          <div
            class="item right"
            :class="activeTab == 2 ? 'active' : ''"
            @click="selectType(2)"
          >
            医技
          </div>
        </div>
      </template>
    </colmun-head>
    <div class="echartsModule">
      <div class="echartsItem flex-column">
        <colmun-head title="高管专家组" background="#fff" class="noBorder">
          <!-- <template slot="right">
            <ts-button class="shallowButton" @click="handleExport"
              >下载</ts-button
            >
          </template> -->
        </colmun-head>
        <div class="echarts" ref="echarts1"></div>
      </div>
      <div class="echartsItem flex-column">
        <colmun-head title="医政组" background="#fff" class="noBorder">
          <!-- <template slot="right">
            <ts-button class="shallowButton" @click="handleExport"
              >下载</ts-button
            >
          </template> -->
        </colmun-head>
        <div class="echarts" ref="echarts2"></div>
      </div>
    </div>
  </div>
</template>

<script>
import problemAnalysis from './problem-analysis';
export default {
  mixins: [problemAnalysis],
  data() {
    return {
      echarts1: null,
      echarts2: null,
      date: [],
      activeTab: 1
    };
  },
  methods: {
    async refresh(date) {
      this.date = date;
      this.renderEchart();
    },
    async renderEchart() {
      let res = await this.ajax.getGroupProblemChart({
        groupName: '高管',
        isClinical: this.activeTab,
        roundsTimeBegin: this.date[0],
        roundsTimeEnd: this.date[1]
      });
      let res1 = await this.ajax.getGroupProblemChart({
        groupName: '医政',
        isClinical: this.activeTab,
        roundsTimeBegin: this.date[0],
        roundsTimeEnd: this.date[1]
      });
      this.$nextTick(() => {
        this.renderEcharts(res.object, 'echarts1', '高管专家组');
        this.renderEcharts(res1.object, 'echarts2', '医政组');
      });
    },
    selectType(index) {
      if (this.activeTab == index) return;
      this.activeTab = index;
      this.renderEchart();
    },
    handleExport() {
      console.log('导出');
    }
  }
};
</script>

<style lang="scss" scoped>
.problem-analysis {
  flex: 1;
  height: 100%;
  padding: 4px;
  /deep/ {
    .activeTab {
      display: flex;
      width: 120px;
      border-radius: 4px;
      .item {
        flex: 1;
        height: 30px;
        line-height: 30px;
        justify-content: center;
        align-items: center;
        background-color: #eee;
        text-align: center;
        cursor: pointer;
        &.active {
          background: #d2dcfc;
          color: $primary-blue;
        }
        &.left {
          border-radius: 4px 0 0 4px;
        }
        &.right {
          border-radius: 0 4px 4px 0;
        }
      }
    }
    .noBorder {
      .lefts {
        display: none;
      }
    }
    .echartsModule {
      display: flex;
      width: 100%;
      gap: 6px;
      margin-top: 8px;
      .echartsItem {
        flex: 1;
        height: 100%;
        .echarts {
          height: 430px;
        }
      }
    }
  }
}
</style>
