<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-add-rounds"
    direction="rtl"
    size="90%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <!-- 基本信息 -->
            <div class="form-card-box">
              <colmun-head title="基本信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item
                    prop="groupName"
                    label="查房小组名称"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.groupName"
                      placeholder="请输入查房小组名称"
                      :disabled="type != 'add'"
                      maxlength="20"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="版本号">
                    <ts-input v-model="form.versionInfo" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    prop="sort"
                    label="排班排序"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.sort"
                      maxlength="5"
                      :disabled="isDetail"
                      placeholder="请输入排班排序"
                      @input="validateNumbersNegative($event, 'form', 'sort')"
                      @blur="event => inputBlur(event, 'form', 'sort')"
                    />
                  </ts-form-item>
                </ts-col>
                <!-- <ts-col :span="16">
                  <ts-form-item
                    prop="groupMemberName"
                    label="查房人员"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.groupMemberName"
                      placeholder="请选择查房人员"
                      readonly
                      @focus="handleOpenSelPerson('groupMemberCode')"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    prop="schedulingCode"
                    label="排班负责人"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.schedulingCode"
                      clearable
                      multiple
                      placeholder="请先选择查房人再选择排班负责人"
                      style="width: 100%;"
                    >
                      <ts-option
                        v-for="(item, index) in codeList"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col> -->
                <ts-col :span="8">
                  <ts-form-item prop="type" label="关联得分统计">
                    <vxe-switch
                      v-model="form.type"
                      openValue="1"
                      openLabel="启用"
                      closeValue="0"
                      closeLabel="禁用"
                      :disabled="type != 'add'"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8" v-if="form.type == '1'">
                  <ts-form-item
                    prop="replyPerson"
                    label="整改权限"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.replyPerson"
                      :disabled="type != 'add'"
                      style="width: 100%;"
                      placeholder="请选择整改权限"
                    >
                      <ts-option label="科室主任" value="1"></ts-option>
                      <ts-option label="护士长" value="2"></ts-option>
                      <ts-option label="科室主任,护士长" value="3"></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    prop="isClinical"
                    label="临床/医技"
                    :rules="rules.required"
                  >
                    <vxe-radio-group
                      v-model="form.isClinical"
                      :disabled="type != 'add'"
                    >
                      <vxe-radio label="1" content="临床"></vxe-radio>
                      <vxe-radio label="2" content="医技"></vxe-radio>
                    </vxe-radio-group>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="备注" prop="remarks">
                    <ts-input
                      v-model="form.remarks"
                      placeholder="请输入备注"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      style="margin-top: 4px;"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="查房资质" />
              <ts-row>
                <ts-col :span="15">
                  <ts-form-item
                    labelWidth="120px"
                    prop="groupMemberName1"
                    label="天心阁查房人员"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.groupMemberName1"
                      placeholder="请选择天心阁查房人员"
                      readonly
                      @focus="handleOpenSelPerson('groupMemberCode', '1')"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    labelWidth="150px"
                    prop="schedulingCode1"
                    label="天心阁排班负责人"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.schedulingCode1"
                      clearable
                      multiple
                      placeholder="请先选择查房人再选择排班负责人"
                      style="width: 100%;"
                    >
                      <ts-option
                        v-for="(item, index) in codeList1"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="15">
                  <ts-form-item
                    labelWidth="120px"
                    prop="groupMemberName2"
                    label="马王堆查房人员"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.groupMemberName2"
                      placeholder="请选择马王堆查房人员"
                      readonly
                      @focus="handleOpenSelPerson('groupMemberCode', '2')"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    labelWidth="150px"
                    prop="schedulingCode2"
                    label="马王堆排班负责人"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.schedulingCode2"
                      clearable
                      multiple
                      placeholder="请先选择查房人再选择排班负责人"
                      style="width: 100%;"
                    >
                      <ts-option
                        v-for="(item, index) in codeList2"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="15">
                  <ts-form-item
                    labelWidth="120px"
                    prop="groupMemberName3"
                    label="岳麓山查房人员"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.groupMemberName3"
                      placeholder="请选择岳麓山查房人员"
                      readonly
                      @focus="handleOpenSelPerson('groupMemberCode', '3')"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    labelWidth="150px"
                    prop="schedulingCode3"
                    label="岳麓山排班负责人"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.schedulingCode3"
                      clearable
                      multiple
                      placeholder="请先选择查房人再选择排班负责人"
                      style="width: 100%;"
                    >
                      <ts-option
                        v-for="(item, index) in codeList3"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="评分细则">
                <template slot="right" v-if="!isEdit">
                  <ts-upload
                    class="base-uplaod"
                    ref="tsUpload"
                    action=""
                    :limit="1"
                    :fileList.sync="fileList"
                    :show-file-list="false"
                    accept=".xlsx"
                    :http-request="handleUploadFile"
                    :on-exceed="masterFileMax"
                  >
                    <ts-button class="shallowButton">导入</ts-button>
                  </ts-upload>
                </template>
              </colmun-head>
              <form-vxe-table
                ref="formVxeTable"
                class="form-table mrgT8 pdlr8"
                :data="form.roundsGroupRulesList"
                :columns="columns"
                :span-method="mergeRowMethod"
              />
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button type="primary" v-if="type != 'detail'" @click="submit"
          >提交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk1" />
  </el-drawer>
</template>

<script>
import {
  deepClone,
  inputTowDecimalPlaces,
  inputNumbers
} from '@/unit/commonHandle.js';
import darwerAddRounds from '../mixins/darwer-add-rounds';
import formVxeTable from '../../components/form-vxe-table.vue';
import moment from 'moment';
export default {
  mixins: [darwerAddRounds],
  components: { formVxeTable },
  data() {
    return {
      visible: false,
      isDetail: false,
      isEdit: false,
      title: '',
      type: '',
      form: {
        roundsGroupRulesList: [],
        type: '1'
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    inputBlur(event, formName, setKey) {
      let value = parseFloat(event.target.value);
      this.$set(this[formName], setKey, isNaN(value) ? '' : value);
    },
    validateTowDecimalPlacesNegative(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },
    validateNumbersNegative(value, obj, attr) {
      let newVal = inputNumbers(value);
      this.$set(this[obj], attr, newVal);
    },
    async getDetail(data) {
      let res = await this.ajax.getRoundsGroupDetails(data.id);
      this.form = res.object || {};
      for (let i = 1; i <= 3; i++) {
        let obj = this.form.roundsGroupMemberList.find(
          e => e.hospArea == this[`code${i}`]
        );
        let codeList = [];
        obj.groupMemberCode.split(',').forEach((e, index) => {
          codeList.push({
            code: e,
            name: obj.groupMemberName.split(',')[index]
          });
        });
        this[`codeList${i}`] = codeList;
        this.form[`groupMemberCode${i}`] = obj.groupMemberCode;
        this.form[`groupMemberName${i}`] = obj.groupMemberName;
        this.$set(
          this.form,
          `schedulingCode${i}`,
          obj.schedulingCode?.split(',') || []
        );
        this.form[`id${i}`] = obj.id;
      }
    },
    async open({ data = null, title, type }) {
      await this.getHosp();
      if (data) {
        await this.getDetail(data);
      } else {
        this.form.versionInfo = 'V' + moment().format('YYYYMMDD');
      }
      this.title = title;
      this.type = type;
      if (type == 'detail') {
        this.isDetail = true;
        this.isEdit = true;
      }
      if (type == 'edit') {
        this.isEdit = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    async checkOnlyEnableGroup() {
      let param = {
        pageSize: 1,
        pageNo: 1,
        groupNameParam: this.form.groupName,
        isEnable: 1,
        isClinical: this.form.isClinical
      };
      let res = await this.ajax.getRoundsGroupPageList(param);
      if (res.rows.length && res.rows[0].id == this.form.id) {
        return false;
      }
      return res.rows.length ? true : false;
    },
    async submit() {
      if (this.form.roundsGroupRulesList.length == 0 && this.form.type == '1') {
        this.$newMessage('warning', '请导入评分细则');
        return false;
      }
      try {
        await this.$refs.ruleForm.validate();
        let data = deepClone(this.form);
        let Api = this.ajax.roundsGroupSave;
        data = this.formatData(data);
        if (data.id) {
          Api = this.ajax.roundsGroupUpdate;
        } else {
          let boolean = await this.checkOnlyEnableGroup();
          if (boolean) {
            await this.$newConfirm(
              `是否确定启用，确定后，查房任务评分细则更新为当前版本号，取消后，查房任务评分细则延用之前版本号`
            );
            data.isEnable = 1;
          }
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (e) {
        if (e == 'cancel') {
          const data = deepClone(this.form);
          data.isEnable = 0;
          let Api = this.ajax.roundsGroupSave;
          if (data.id) {
            Api = this.ajax.roundsGroupUpdate;
          }
          const res = await Api(data);
          if (res.success && res.statusCode === 200) {
            this.$newMessage('success', '【操作】成功!');
            this.$emit('refresh');
            this.close();
          } else {
            this.$newMessage('error', res.message || '【操作】失败!');
          }
        }
        console.log(e);
      }
    },
    close() {
      this.visible = false;
      this.type = undefined;
      this.form = {
        roundsGroupRulesList: [],
        type: '1'
      };
      this.isDetail = false;
      this.isEdit = false;
      this.fileList = [];
      this.codeList1 = [];
      this.codeList2 = [];
      this.codeList3 = [];
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    padding-bottom: 8px;
  }
  .textarea {
    .el-textarea__inner {
      min-height: 80px !important;
      max-height: 80px !important;
    }
  }
}
</style>
