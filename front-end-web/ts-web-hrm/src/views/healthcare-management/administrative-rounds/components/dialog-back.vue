<template>
  <div>
    <vxe-modal
      width="60%"
      :title="title"
      v-model="visible"
      showFooter
      className="dialog-rating-details"
      :before-hide-method="close"
    >
      <template #default>
        <div class="content">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <ts-form-item label="打回原因" prop="returnReason">
              <ts-input
                v-model="form.returnReason"
                placeholder="请输入打回原因"
                type="textarea"
                class="textarea"
                maxlength="500"
                style="margin-top: 4px;"
                :disabled="isDetail"
                show-word-limit
              />
            </ts-form-item>
          </ts-form>
        </div>
      </template>
      <template #footer>
        <span slot="footer" class="dialog-footer">
          <ts-button type="primary" v-if="!isDetail" @click="submit"
            >提 交</ts-button
          >
          <ts-button class="shallowButton" @click="close">关 闭</ts-button>
        </span>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import dialogRatingDetails from './dialog-rating-details';
import configTable from './configTable';
export default {
  mixins: [dialogRatingDetails, configTable],
  data() {
    return {
      visible: false,
      title: '',
      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      isDetail: false
    };
  },
  methods: {
    async open({ data = {}, type, title }) {
      this.form = data;
      this.type = type;
      this.title = title;
      if (type == 'details') {
        this.isDetail = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        this.form.status = 0;
        this.submitLoading = true;
        let res = await this.ajax.roundsTaskGroupUpdate(this.form);
        if (res.success) {
          this.submitLoading = false;
          this.$newMessage('success', `【打回】成功`);
          this.$emit('refresh');
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【打回】失败!`);
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.isDetail = false;
      this.title = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-rating-details {
  ::v-deep {
    .content {
      height: 180px;
      display: flex;
      flex-direction: column;
      position: relative;
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
          &[disabled='disabled'] {
            color: #777;
          }
        }
      }
    }
  }
}
</style>
