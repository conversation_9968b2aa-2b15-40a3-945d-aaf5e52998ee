import Decimal from 'decimal.js';
import moment from 'moment';
export default {
  data() {
    return {
      approveColumns: {
        // 查房任务填写扣分事项的表单字段设置
        deductColumns: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'roundsProject',
            label: '查房项目',
            width: 120,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'scoreValue',
            label: '分值',
            width: 50,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'scoringRubric',
            label: '评分细则',
            minWidth: 280,
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              let list = [h('p', { style: 'margin: 0' }, row.scoringRubric)];
              if (row.checked) {
                list.push(
                  h('i', {
                    class: 'el-icon-check',
                    style: 'font-size: 24px;color: red'
                  })
                );
              }
              return h('div', { style: 'display: flex' }, [...list]);
            }
          },
          {
            prop: 'source',
            label: '来源',
            width: 180,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 280,
            editabled: true,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            editabled: true,
            required: true,
            slotName: 'inputNumber',
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'score',
            label: '得分',
            width: 80,
            headerAlign: 'center',
            align: 'center'
          }
        ],
        // 弹框的扣分详情表单
        dialogColumns: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'roundsProject',
            label: '查房项目',
            width: 120,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'scoreValue',
            label: '分值',
            width: 50,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'scoringRubric',
            label: '评分细则',
            minWidth: 280,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'source',
            label: '来源',
            width: 180,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 280,
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'score',
            label: '得分',
            width: 80,
            headerAlign: 'center',
            align: 'center'
          }
        ],
        // 查房任务秘书审查事项的表单字段设置 -数据维度与细则不一致
        examineColumns: [
          {
            prop: 'status',
            label: '状态',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              if (row.status == 0) {
                return h('span', { style: 'color: red' }, '打回');
              }
              return h('span', {}, '');
            }
          },
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              let sumScore = new Decimal(row.hjScore)
                .plus(new Decimal(row.hjDeductPoints))
                .toNumber();
              return h(
                'div',
                { style: 'display:flex; flex-direction: column' },
                [
                  h('span', {}, row.groupName),
                  h('span', {}, `总分：${sumScore}`),
                  h('span', { style: 'color: red' }, `得分：${row.hjScore}`),
                  h(
                    'span',
                    {
                      style: 'color: #295cf9;cursor: pointer;',
                      on: {
                        click: () => this.handleDialogDetails(row)
                      }
                    },
                    '查看评分详情'
                  )
                ]
              );
            }
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 220,
            editabled: true,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'score',
            label: '得分',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              return h('span', { style: 'color: red' }, row.score);
            }
          },
          {
            label: '操作',
            align: 'center',
            prop: 'actions',
            width: 80,
            headerSlots: 'action',
            fixed: 'right',
            render: (h, { row }) => {
              let actionList = [];
              if (row.status != 0) {
                actionList.push({
                  label: '打回',
                  event: this.handleBack,
                  className: 'actionDel'
                });
              } else {
                actionList.push({
                  label: '查看',
                  event: this.handleBackDetails
                });
              }
              return h('BaseActionCell', {
                on: { 'action-select': event => event(row) },
                attrs: { actions: actionList }
              });
            }
          }
        ],
        // 查房任务科室主任，护士长填写整改表单设置
        correctionColumns: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              let sumScore = new Decimal(row.hjScore)
                .plus(new Decimal(row.hjDeductPoints))
                .toNumber();
              return h(
                'div',
                { style: 'display:flex; flex-direction: column' },
                [
                  h('span', {}, row.groupName),
                  h('span', {}, `总分：${sumScore}`),
                  h('span', { style: 'color: red' }, `得分：${row.hjScore}`),
                  h(
                    'span',
                    {
                      style: 'color: #295cf9;cursor: pointer;',
                      on: {
                        click: () => this.handleDialogDetails(row)
                      }
                    },
                    '查看评分详情'
                  )
                ]
              );
            }
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 220,
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              return h('span', { style: 'color: red' }, row.deductPoints);
            }
          },
          {
            prop: 'causeAnalysis',
            label: '原因分析',
            minWidth: 220,
            editabled: true,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.causeAnalysis);
            }
          },
          {
            prop: 'rectificationMeasures',
            label: '整改措施',
            minWidth: 220,
            editabled: true,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationMeasures
              );
            }
          },
          {
            prop: 'rectificationFiles',
            label: '整改附件',
            minWidth: 220,
            headerAlign: 'center',
            editabled: true,
            required: true,
            onlyRead: false,
            slotName: 'files',
            align: 'left'
          }
        ],
        // 复检表单设置
        correctionColumnsEdit: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              let sumScore = new Decimal(row.hjScore)
                .plus(new Decimal(row.hjDeductPoints))
                .toNumber();
              return h(
                'div',
                { style: 'display:flex; flex-direction: column' },
                [
                  h('span', {}, row.groupName),
                  h('span', {}, `总分：${sumScore}`),
                  h('span', { style: 'color: red' }, `得分：${row.hjScore}`),
                  h(
                    'span',
                    {
                      style: 'color: #295cf9;cursor: pointer;',
                      on: {
                        click: () => this.handleDialogDetails(row)
                      }
                    },
                    '查看评分详情'
                  )
                ]
              );
            }
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 220,
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              return h('span', { style: 'color: red' }, row.deductPoints);
            }
          },
          {
            prop: 'causeAnalysis',
            label: '原因分析',
            minWidth: 220,
            editabled: false,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.causeAnalysis);
            }
          },
          {
            prop: 'rectificationMeasures',
            label: '整改措施',
            minWidth: 220,
            editabled: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationMeasures
              );
            }
          },
          {
            prop: 'rectificationFiles',
            label: '整改附件',
            minWidth: 220,
            headerAlign: 'center',
            editabled: false,
            onlyRead: true,
            slotName: 'files',
            align: 'left'
          },
          {
            prop: 'rectificationResults',
            label: '整改结果',
            minWidth: 220,
            editabled: true,
            required: true,
            slotName: 'inputSelect',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationResults
              );
            }
          },
          {
            prop: 'remarks',
            label: '效果评价',
            minWidth: 220,
            editabled: true,
            required: true,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.remarks);
            }
          },
          {
            prop: 'evaluationFiles',
            label: '评估附件',
            minWidth: 220,
            editabled: true,
            required: false,
            onlyRead: false,
            slotName: 'files',
            headerAlign: 'center',
            align: 'left'
          }
        ],
        // 查房任务普通人员查看的详情
        ordinaryColumns: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              let sumScore = new Decimal(row.hjScore)
                .plus(new Decimal(row.hjDeductPoints))
                .toNumber();
              return h(
                'div',
                { style: 'display:flex; flex-direction: column' },
                [
                  h('span', {}, row.groupName),
                  h('span', {}, `总分：${sumScore}`),
                  h('span', { style: 'color: red' }, `得分：${row.hjScore}`),
                  h(
                    'span',
                    {
                      style: 'color: #295cf9;cursor: pointer;',
                      on: {
                        click: () => this.handleDialogDetails(row)
                      }
                    },
                    '查看评分详情'
                  )
                ]
              );
            }
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 220,
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              return h('span', { style: 'color: red' }, row.deductPoints);
            }
          },
          {
            prop: 'causeAnalysis',
            label: '原因分析',
            minWidth: 220,
            editabled: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.causeAnalysis);
            }
          },
          {
            prop: 'rectificationMeasures',
            label: '整改措施',
            minWidth: 220,
            editabled: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationMeasures
              );
            }
          },
          {
            prop: 'rectificationFiles',
            label: '整改附件',
            minWidth: 220,
            headerAlign: 'center',
            editabled: false,
            onlyRead: true,
            slotName: 'files',
            align: 'left'
          },
          {
            prop: 'rectificationResults',
            label: '整改结果',
            minWidth: 220,
            editabled: false,
            slotName: 'inputSelect',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationResults
              );
            }
          },
          {
            prop: 'remarks',
            label: '效果评价',
            minWidth: 220,
            editabled: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.remarks);
            }
          },
          {
            prop: 'evaluationFiles',
            label: '评估附件',
            minWidth: 220,
            editabled: false,
            onlyRead: true,
            slotName: 'files',
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'evaluator',
            label: '评估人',
            width: 80,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'evaluationTime',
            label: '评估时间',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              if (row.evaluationTime)
                return h(
                  'span',
                  {},
                  moment(row.evaluationTime).format('YYYY-MM-DD')
                );
            }
          }
        ],
        // 最大权限
        masterColumns: [
          {
            prop: 'groupName',
            label: '查房小组名称',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              let sumScore = new Decimal(row.hjScore)
                .plus(new Decimal(row.hjDeductPoints))
                .toNumber();
              return h(
                'div',
                { style: 'display:flex; flex-direction: column' },
                [
                  h('span', {}, row.groupName),
                  h('span', {}, `总分：${sumScore}`),
                  h('span', { style: 'color: red' }, `得分：${row.hjScore}`),
                  h(
                    'span',
                    {
                      style: 'color: #295cf9;cursor: pointer;',
                      on: {
                        click: () => this.handleDialogDetails(row)
                      }
                    },
                    '查看评分详情'
                  )
                ]
              );
            }
          },
          {
            prop: 'roundsContent',
            label: '查房内容',
            width: 200,
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'existingProblems',
            label: '存在问题',
            minWidth: 220,
            editabled: true,
            required: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.existingProblems);
            }
          },
          {
            prop: 'deductPoints',
            label: '扣分',
            width: 80,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              return h('span', { style: 'color: red' }, row.deductPoints);
            }
          },
          {
            prop: 'causeAnalysis',
            label: '原因分析',
            minWidth: 220,
            editabled: true,
            required: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.causeAnalysis);
            }
          },
          {
            prop: 'rectificationMeasures',
            label: '整改措施',
            minWidth: 220,
            editabled: true,
            required: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationMeasures
              );
            }
          },
          {
            prop: 'rectificationFiles',
            label: '整改附件',
            minWidth: 220,
            headerAlign: 'center',
            editabled: true,
            required: false,
            onlyRead: false,
            slotName: 'files',
            align: 'left'
          },
          {
            prop: 'rectificationResults',
            label: '整改结果',
            minWidth: 220,
            editabled: true,
            required: false,
            slotName: 'inputSelect',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h(
                'pre',
                { class: 'preContent' },
                row.rectificationResults
              );
            }
          },
          {
            prop: 'remarks',
            label: '效果评价',
            minWidth: 220,
            editabled: true,
            required: false,
            slotName: 'inputTextArea',
            headerAlign: 'center',
            align: 'left',
            render: (h, { row }) => {
              return h('pre', { class: 'preContent' }, row.remarks);
            }
          },
          {
            prop: 'evaluationFiles',
            label: '评估附件',
            minWidth: 220,
            editabled: true,
            required: false,
            onlyRead: false,
            slotName: 'files',
            headerAlign: 'center',
            align: 'left'
          },
          {
            prop: 'evaluator',
            label: '评估人',
            width: 80,
            headerAlign: 'center',
            align: 'center'
          },
          {
            prop: 'evaluationTime',
            label: '评估时间',
            width: 120,
            headerAlign: 'center',
            align: 'center',
            render: (h, { row }) => {
              if (row.evaluationTime)
                return h(
                  'span',
                  {},
                  moment(row.evaluationTime).format('YYYY-MM-DD')
                );
            }
          }
        ]
      },
      // 需要进行合并单元格的字段
      spanFields: [
        'groupName',
        'roundsProject',
        'roundsContent',
        'scoreValue',
        'source',
        'existingProblems',
        'deductPoints',
        'score',
        'causeAnalysis',
        'rectificationMeasures',
        'rectificationFiles',
        'rectificationResults',
        'rectificationScore',
        'remarks',
        'evaluationFiles',
        'evaluator',
        'evaluationTime'
      ],
      // 需要使用其他单元格合并信息的字段
      copyFields: [
        'scoreValue',
        'source',
        'existingProblems',
        'deductPoints',
        'score',
        'causeAnalysis',
        'rectificationMeasures',
        'rectificationFiles',
        'rectificationResults',
        'rectificationScore',
        'remarks',
        'evaluationFiles',
        'evaluator',
        'evaluationTime'
      ],
      // 标准合并单元格的字段
      defaultSpanKey: 'roundsContent',
      filesValid: ({ cellValue, row }) => {
        if (row.deductPoints > 0 && !cellValue) {
          // this.$newMessage(
          //   'warning',
          //   `请上传【${row.roundsContent}】的整改附件`
          // );
          return new Error('请上传整改附件');
        }
      },
      inputValid: ({ cellValue, row }) => {
        if (row.rectificationResults == '部分整改' && !cellValue) {
          return new Error('请填写效果评价');
        }
      }
    };
  },
  computed: {
    columnsApprove() {
      if (this.type == '') {
        return [];
      }
      return this.approveColumns[this.role[this.type].approveColumns];
    },
    validRulesApprove() {
      let requiredList = this.columnsApprove.filter(
        e => e.hasOwnProperty('editabled') && e.editabled && e.required
      );
      let rules = {};
      requiredList.forEach(item => {
        if (item.slotName == 'files') {
          rules[item.prop] = [{ validator: this.filesValid }];
        } else if (item.prop == 'remarks' && item.editabled) {
          rules[item.prop] = [{ validator: this.inputValid }];
        } else {
          rules[item.prop] = [
            { required: true, message: `请填写${item.label}内容` }
          ];
        }
      });
      return rules;
    },
    editConfig() {
      return { trigger: 'click', mode: 'cell' };
    }
  },
  methods: {
    handleDialogDetails(data) {
      let datas = this.form.roundsTaskGroupList.find(
        e => e.groupName == data.groupName
      );
      datas.roundsTaskGroupRulesList.forEach(item => {
        item.groupName = data.groupName;
      });
      if (this.type == 'masterRole') {
        this.$refs.dialogRatingDetails.open({
          data: datas.roundsTaskGroupRulesList,
          columns: 'deductColumns',
          title: '编辑'
        });
      } else {
        this.$refs.dialogRatingDetails.open({
          data: datas.roundsTaskGroupRulesList
        });
      }
    },
    handleBack(data) {
      this.$refs.dialogBack.open({ data, type: 'edit', title: '打回' });
    },
    handleBackDetails(data) {
      this.$refs.dialogBack.open({ data, type: 'details', title: '打回详情' });
    }
  }
};
