<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @nodeCheck="clickItemTree"
      >
      </new-base-search-tree>
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      />
      <div class="flex-end mtb8">
        <ts-button type="primary" v-if="ssoRoleCode" @click="handleAdd">
          新增
        </ts-button>
        <ts-button class="shallowButton" @click="handleExport">
          导出
        </ts-button>
      </div>
      <div class="card-container">
        <el-scrollbar
          style="flex: 1;height: 100%;"
          wrap-style="overflow-x: hidden;"
        >
          <div class="cardBox">
            <div class="card" v-for="(item, index) in data" :key="index">
              <div class="title-card">
                <p class="time">{{ formatDate(item.roundsTime) }}</p>
                <p class="dept">
                  {{ item.roundsOrgName }}
                  ({{ item.isClinical == '1' ? '临床' : '医技' }})
                </p>
              </div>
              <div class="content-card">
                <div class="content-left">
                  <p>
                    <span>周次：{{ item.weekNumber }}</span>
                    <span style="margin-left: 8px;"
                      >院区：{{ item.hospAreaText }}</span
                    >
                  </p>
                  <p class="address">科室地址：{{ item.roundsOrgAddress }}</p>
                  <p class="user" :title="item.schedulingGroupUserName">
                    查房人员：
                    {{ item.schedulingGroupUserName }}
                  </p>
                </div>
                <div class="content-right">
                  <span class="status" :class="getschedulingStatusClass(item)">
                    {{ getschedulingStatus(item) }}
                  </span>
                </div>
              </div>
              <div class="card-bottom">
                <div class="switch" v-if="canEdit(item)">
                  <span class="switch-title">是否查房</span>
                  <vxe-switch
                    v-model="item.status"
                    openValue="0"
                    openLabel="是"
                    closeValue="1"
                    closeLabel="否"
                    @change="updateStatus(item)"
                  />
                </div>
                <div class="button">
                  <ts-button
                    v-if="!canEdit(item)"
                    class="shallowButton"
                    @click="handleDetails(item)"
                    >查看</ts-button
                  >
                  <ts-button
                    v-if="canEdit(item) || canEditUser(item)"
                    class="edit"
                    @click="handleEdit(item)"
                    >修改</ts-button
                  >
                  <ts-button
                    v-if="canEdit(item)"
                    class="del"
                    @click="handleDelete(item)"
                    >删除</ts-button
                  >
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="pagination-content">
        <vxe-pager
          perfect
          size="mini"
          :pager-count="pagerCount"
          :page-sizes="pageSizes"
          :current-page="pageNo"
          :page-size="pageSize"
          :total="total"
          align="center"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total'
          ]"
          @page-change="handlePageChange"
        ></vxe-pager>
      </div>
    </div>
    <darwer-add-work ref="darwerAddWork" @refresh="search" />
  </div>
</template>

<script>
import table from './mixins/table';
import darwerAddWork from './components/darwer-add-work.vue';
import {
  checkSearchFormDate,
  resetSearchFormSelect,
  supplementSearchSelect
} from '@/unit/commonHandle.js';
export default {
  components: { darwerAddWork },
  mixins: [table],
  data() {
    return {
      treeCode: '',
      apiFunction: this.ajax.getDeptTreeList,
      data: []
    };
  },
  methods: {
    // 树 item点击
    clickItemTree(node) {
      this.treeCode = node.map(e => e.code).join(',');
      this.search();
    },
    async refresh() {
      await this.getAllDictItemList();
      await this.search();
      this.searchForm = {
        ...resetSearchFormSelect(this.searchList)
      };
      supplementSearchSelect(this.searchList);
    },
    async search() {
      this.pageNo = 1;
      await this.handleRefreshTable();
    },
    reset() {
      return {
        ...resetSearchFormSelect(this.searchList),
        orgId: this.treeCode
      };
    },
    handleExport() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm } = this.getQueryParam();
      let aDom = document.createElement('a');
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/roundsScheduling/exportRoundsScheduling?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    },
    async handleRefreshTable() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm } = this.getQueryParam();
      let res = await this.ajax.getRoundsSchedulingPageList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      this.data = res.rows;
      this.total = res.totalCount;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
::v-deep {
  .right {
    .card-container {
      flex: 1;
      padding: 8px;
      overflow: hidden;
      transform: scale(1);
      .cardBox {
        flex: 1;
        overflow: hidden;
        transform: scale(1);
        display: flex;
        flex-wrap: wrap;
        .card {
          min-width: 400px;
          max-width: 400px;
          flex: 1;
          margin-right: 8px;
          margin-bottom: 8px;
          .title-card {
            padding: 8px;
            border-radius: 10px 10px 0 0;
            line-height: 40px;
            height: 40px;
            background: #ebeefd;
            display: flex;
            p {
              margin: 0;
              font-size: 16px;
              font-weight: bold;
            }
            .time {
              color: #333;
            }
            .dept {
              flex: 1;
              margin-left: 8px;
              color: $primary-blue;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
            }
          }
          .content-card {
            padding: 8px 12px;
            background: #f1f4fd;
            height: 120px;
            display: flex;
            justify-content: space-between;
            p {
              margin: 0;
              line-height: 30px;
              font-size: 14px;
              color: #666;
            }
            span {
              line-height: 30px;
            }
            .content-left {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              width: 80%;
            }
            .content-right {
              width: 20%;
              display: flex;
              justify-content: flex-end;
              .status {
                &.wait {
                  color: $primary-blue;
                }
                &.do {
                  color: #5caa90;
                }
                &.cancel {
                  color: $error-color;
                }
              }
            }
            .address {
              line-height: 30px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
            }
            .user {
              line-height: 20px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
            }
          }
          .card-bottom {
            border-radius: 0 0 10px 10px;
            background: #f5f7fa;
            height: 40px;
            padding: 5px 8px;
            display: flex;
            justify-content: space-between;
            .switch {
              flex: 1;
              .switch-title {
                line-height: 30px;
              }
            }
            .button {
              flex: 1;
              display: flex;
              justify-content: flex-end;
            }
          }
        }
      }
    }
  }
  .pagination-content {
    height: 36px;
    .vxe-pager {
      background: #f6f6f6;
    }
  }
}
</style>
