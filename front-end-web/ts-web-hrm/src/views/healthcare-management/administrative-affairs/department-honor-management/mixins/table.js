export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '荣誉名称',
          value: 'honorName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入荣誉名称'
          }
        },
        {
          label: '证书编号',
          value: 'certificateNo',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入证书编号'
          }
        },
        {
          label: '颁发日期',
          value: 'date',
          element: 'base-date-range-picker'
        }
      ],

      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '颁发日期',
          prop: 'issueDate',
          width: 110,
          align: 'center'
        },
        {
          label: '证书编号',
          prop: 'certificateNo',
          width: 120,
          align: 'center'
        },
        {
          label: '荣誉名称',
          prop: 'honorName',
          minWidth: 150,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetails(row) }
              },
              row.honorName
            );
          }
        },
        {
          label: '所属科室',
          prop: 'orgName',
          minWidth: 150,
          align: 'center'
        },
        {
          label: '颁发机构',
          prop: 'issueOrg',
          width: 150,
          align: 'center'
        },
        {
          label: '展示状态',
          prop: 'isPub',
          width: 80,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'div',
              {
                style: row.isPub === '0' ? { color: 'red' } : {}
              },
              row.isPub === '0' ? '隐藏' : '公开'
            );
          }
        },
        {
          label: '操作者',
          prop: 'updateUserName',
          width: 100,
          align: 'center'
        },
        {
          label: '操作时间',
          prop: 'updateDate',
          width: 145,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          showOverflow: 'ellipsis',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '修改',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'actionDel'
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  }
};
