<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        placeholder="输入组织机构进行搜索"
        :apiFunction="apiFunction"
        title="科室分类"
        @tree="getTreeSuccess"
        @beforeClick="clickItemTree"
      />
    </div>

    <div class="right">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
        <template slot="right">
          <ts-button type="primary" @click="handleSalaryAdd">
            新增
          </ts-button>
        </template>
      </ts-search-bar>

      <TsVxeTemplateTable
        id="table_medical-special-projects"
        class="form-table"
        ref="table"
        :defaultSort="{
          sidx: 'create_date',
          sord: 'desc'
        }"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>

    <dialog-add ref="dialogAdd" @refresh="handleRefreshTable" />
  </div>
</template>

<script>
import table from './mixins/table';
import dialogAdd from './components/dialog-add.vue';
export default {
  mixins: [table],
  components: { dialogAdd },
  data() {
    return {
      apiFunction: this.ajax.getOrganizationGetTree2,
      footerData: [],
      treeCode: '',
      idList: []
    };
  },
  methods: {
    async refresh() {
      this.handleRefreshTable();
    },

    // 树加载成功
    getTreeSuccess(data, id, name) {},

    // 树 item点击
    clickItemTree(node) {
      this.searchForm.resperOrg = node.code;
      this.search();
    },

    reset() {
      this.$refs.searchTree.$refs.tsTree.cancelAllChecked();
      return {
        resperOrg: ''
      };
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleSalaryAdd() {
      this.$refs.dialogAdd.open({ data: {}, title: '新增', type: 'add' });
    },

    handleEdit(data) {
      this.$refs.dialogAdd.open({ data, title: '修改', type: 'edit' });
    },

    handleDetails(data) {
      this.$refs.dialogAdd.open({ data, title: '详情', type: 'detail' });
    },

    async handleDelete(row) {
      try {
        await this.$newConfirm(
          `【<span style="color: red">删除</span>】${row.itemCode}项目？`
        );
        this.ajax.spedItemDelete(row.id).then(res => {
          if (!res.success) {
            this.$newMessage('error', '【删除】失败！');
            return;
          }
          this.$newMessage('success', '【删除】成功！');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },

    handleGetSeacrhQueryParam() {
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { date = [] } = this.searchForm,
        [startDateSearch = '', endDateSearch = ''] = date,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDateSearch,
          endDateSearch,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      delete searchForm.date;

      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageNo, pageSize };
    },

    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let res = await this.ajax.getspedItemList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/template.scss';
</style>
