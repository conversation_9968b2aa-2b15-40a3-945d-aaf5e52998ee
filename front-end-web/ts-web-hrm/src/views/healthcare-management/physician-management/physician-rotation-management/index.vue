<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @tree="getTreeData"
        @nodeCheck="clickItemTree"
      >
      </new-base-search-tree>
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
        <template slot="right">
          <ts-button v-if="sysRoleCode" type="primary" @click="handleAdd">
            新增
          </ts-button>
        </template>
      </new-ts-search-bar>
      <TsVxeTemplateTable
        id="table_physician-rotation-management"
        class="form-table"
        ref="table"
        :defaultSort="{
          sidx: 'create_date',
          sord: 'desc'
        }"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
    <darwer-physician-rotation-add
      ref="darwerPhysicianRotationAdd"
      @refresh="search"
    />
  </div>
</template>

<script>
import table from './mixins/table';
import darwerPhysicianRotationAdd from './components/darwer-physician-rotation-add.vue';
export default {
  mixins: [table],
  components: { darwerPhysicianRotationAdd },
  data() {
    return {
      treeNode: '',
      apiFunction: this.ajax.getDeptTreeList,
      treeData: []
    };
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (to.path.indexOf('physician/rotation-management') > -1) {
          let { employeeName } = this.$route.query;
          this.$set(this.searchForm, 'docName', employeeName);
          this.$nextTick(() => {
            this.search();
          });
        }
      },
      immediate: true // 如果需要在组件创建时立即触发，设置为true
    }
  },
  methods: {
    // 树 item点击
    clickItemTree(node) {
      this.treeCode = node.map(e => e.code).join(',');
      this.search();
    },
    async refresh() {
      await this.search();
    },
    getTreeData(data) {
      this.treeData = data;
    },
    handleAdd() {
      this.$refs.darwerPhysicianRotationAdd.open({
        title: '新增',
        type: 'handAdd',
        treeData: this.treeData
      });
    },
    handleDetail(data) {
      this.$refs.darwerPhysicianRotationAdd.open({
        title: '详情',
        type: 'detail',
        treeData: this.treeData,
        data
      });
    },
    handleEditAdmin(data) {
      this.$refs.darwerPhysicianRotationAdd.open({
        title: '编辑',
        type: 'editAdmin',
        treeData: this.treeData,
        data
      });
    },
    handleEditMedical(data) {
      this.$refs.darwerPhysicianRotationAdd.open({
        title: '审核',
        type: 'editMedical',
        treeData: this.treeData,
        data
      });
    },
    handleEditDepartment(data) {
      this.$refs.darwerPhysicianRotationAdd.open({
        title: '考核',
        type: 'editDepartment',
        treeData: this.treeData,
        data
      });
    },
    async search() {
      this.$refs.table.pageNo = 1;
      await this.handleRefreshTable();
    },
    reset() {
      return {
        orgId: this.treeNode
      };
    },
    async handleDelete(row) {
      try {
        await this.$newConfirm(
          `【<span style="color: red">删除</span>】
            <span style="color: #295cf9">${row.docName}</span>
            轮科信息？`
        );
        this.ajax.wheelScienceDelete(row.id).then(res => {
          if (!res.success) {
            this.$newMessage('error', '【删除】失败');
            return;
          }
          this.$newMessage('success', '【删除】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let num = 0;
      searchForm.startDate && num++;
      searchForm.endDate && num++;
      if (num == 1) {
        this.$newMessage('warning', '请选择【轮科日期】的开始时间和结束时间');
        return;
      }
      let res = await this.ajax.wheelScienceList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
</style>
