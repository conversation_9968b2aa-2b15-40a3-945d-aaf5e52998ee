export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '',
          value: 'itemCode',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索工号/姓名/手机号码'
          }
        },
        {
          label: '异动类型',
          value: 'causess',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '编制类型',
          value: 'establishmentType',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '所属科室',
          prop: 'oldOrgName',
          align: 'center',
          width: 150
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          sortable: true,
          width: 130,
          sortProp: 'employee_no'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          width: 130,
          sortable: true,
          align: 'center',
          sortProp: 'employee_name'
        },
        {
          label: '身份证号',
          prop: 'identityNumber',
          align: 'center',
          sortable: true,
          width: 190,
          sortProp: 'identity_number'
        },
        {
          label: '人员状态',
          prop: 'establishmentType',
          align: 'center',
          sortable: true,
          width: 190
        },
        {
          label: '编制类型',
          prop: 'establishmentType',
          align: 'center',
          sortable: true,
          width: 190
        },
        {
          label: '异动原因',
          prop: 'cause',
          align: 'center',
          sortable: true,
          width: 100
        },
        {
          label: '操作人',
          prop: 'operatorUserName',
          align: 'center',
          width: 100
        },
        {
          label: '操作时间',
          prop: 'createDate',
          align: 'center',
          width: 100,
          sortProp: 'approval_status'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '查看详情',
                event: this.handleEdit
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  mounted() {
    this.getDictType();
  },
  methods: {
    getDictType() {
      this.ajax.getDictType('establishment_type').then(res => {
        if (res.success) {
          this.searchList[2].childNodeList = res.object.map(item => {
            return {
              label: item.dictName,
              value: item.dictValue,
              element: 'ts-option'
            };
          });
        } else {
          this.$message.error(res.message || '编制类型获取失败');
        }
      });
      this.ajax
        .loadchangeselect({
          pageNo: 1,
          pageSize: 15,
          condition: '',
          select: 1
        })
        .then(res => {
          this.searchList[1].childNodeList = res.rows.map(item => {
            return {
              label: item.itemName,
              value: item.itemCode,
              element: 'ts-option'
            };
          });
        });
    }
  }
};
