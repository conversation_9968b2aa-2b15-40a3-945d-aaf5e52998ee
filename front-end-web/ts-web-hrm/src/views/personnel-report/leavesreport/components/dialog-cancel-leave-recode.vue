<template>
  <ts-dialog
    custom-class="dialog-cancel-leave-recode"
    title="销假记录"
    width="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="tabs-content">
        <div style="margin-bottom: 15px;">
          <h4 style="background-color:#efefef;">
            <strong> 请假人信息：</strong>
          </h4>
          <ts-row>
            <ts-col :span="12">
              <strong> 姓名：</strong> {{ data.employeeName }}
            </ts-col>
            <ts-col :span="12">
              <strong>工号：</strong> {{ data.employeeCode }}
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-col :span="12">
              <strong>请假类型： </strong>{{ data.leaveType }}
            </ts-col>
            <ts-col :span="12">
              <strong>请假天数： </strong>{{ data.sumDays }}
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-col>
              <strong>请假日期：</strong> {{ data.startDateText }} -
              {{ data.endDateText }}
            </ts-col>
          </ts-row>
        </div>

        <div>
          <h4 style="background-color:#efefef;">
            <strong> 销假明细：</strong>
          </h4>
        </div>
        <el-table
          ref="table"
          class="form-table"
          :data="dataSource"
          style="width: 100%"
          height="50%"
          border
          stripe
          :show-overflow-tooltip="true"
        >
          <el-table-column
            :show-overflow-tooltip="true"
            width="36"
            align="center"
            type="index"
            prop="index"
          ></el-table-column>
          <el-table-column
            label="工号"
            width="130"
            :show-overflow-tooltip="true"
            align="center"
            prop="employeeCode"
          ></el-table-column>
          <el-table-column
            label="姓名"
            width="120"
            :show-overflow-tooltip="true"
            align="center"
            prop="employeeName"
          >
          </el-table-column>
          <el-table-column
            label="销假天数"
            width="90"
            :show-overflow-tooltip="true"
            align="center"
            prop="days"
          >
          </el-table-column>
          <el-table-column
            label="销假开始时间"
            :show-overflow-tooltip="true"
            align="center"
            prop="startDateText"
          >
          </el-table-column>
          <el-table-column
            label="销假结束时间"
            :show-overflow-tooltip="true"
            align="center"
            prop="endDateText"
          >
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <span
                class="operation-span"
                @click="handle2CancelProcess(scope.row.workflowId)"
              >
                查看
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      title: '销假记录',
      visible: false,
      dataSource: [],
      data: {}
    };
  },
  created() {},
  methods: {
    open({ data }) {
      this.data = data;
      this.visible = true;
      this.dataSource = data.cancelLeaveReportList.map(e => {
        return {
          ...e,
          startDateText: data.startDateText,
          endDateText: data.endDateText
        };
      });
    },

    //查询核销流程
    async handle2CancelProcess(workflowId) {
      let res = await this.ajax.workflowByInstId(workflowId);
      if (res.success && res.object) {
        let {
          workflowNo,
          businessId,
          wfInstanceId: workflowInstId
        } = res.object;
        let url = encodeURI(
          `/view-new/processView/see/index.html?workflowNo=${workflowNo}&businessId=${businessId}&wfInstanceId=${workflowInstId}&currentStepNo=end&role=deal&type=see`
        );
        let a = document.createElement('a');
        document.body.appendChild(a);
        a.href = url;
        a.target = '_blank';
        a.click();
        document.body.removeChild(a);
      }
    },
    //关闭
    close() {
      this.form = null;
      this.fileList = [];
      this.type = undefined;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-cancel-leave-recode {
    height: 70%;
    .ts-button {
      &.is-disabled {
        color: rgb(204, 204, 204) !important;
        border-color: rgb(231, 235, 240) !important;
        background-color: rgb(250, 250, 250) !important;
        &:hover {
          cursor: not-allowed;
        }
      }
    }
    .el-dialog__header {
      height: 42px !important;
      padding: 10px 20px !important;
    }
    .title-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        transform: translateY(14px);
        > p {
          font-weight: 800;
          margin: 0;
        }
      }
    }

    .el-dialog__footer {
      width: calc(100% - 45px) !important;

      .dialog-footer {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }

    .el-dialog__body {
      width: calc(100% - 45px) !important;
      height: calc(100% - 132px) !important;
      padding: 8px !important;
      overflow: auto;
      .content {
        height: 90%;
        position: relative;
        .tabs-content {
          position: absolute;
          left: 0;
          top: 0px;
          width: 100%;
          height: calc(100% - 0px);
          .tabs-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            .form-table {
              flex: 1;
              overflow: hidden;
              transform: scale(1);
              .vxe-footer--column {
                color: rgb(255, 121, 0);
              }

              .el-table__body-wrapper {
                margin-top: 31px !important;
              }
            }
          }
        }
      }
    }
    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
</style>
