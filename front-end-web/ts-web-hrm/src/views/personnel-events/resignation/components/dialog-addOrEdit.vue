<template>
  <vxe-modal
    width="800"
    :title="title"
    v-model="visible"
    showFooter
    :before-hide-method="close"
    className="dialog-edit-form"
  >
    <template #default>
      <div class="content">
        <el-scrollbar style="height: 100%;" wrap-style="overflow-x: hidden;">
          <ts-form ref="form" :model="form" labelWidth="100px">
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  label="姓名"
                  :rules="rules.required"
                  prop="employeeId"
                >
                  <base-select
                    v-if="api == 'save'"
                    v-model="form.employeeId"
                    :inputText.sync="form.employeeName"
                    :loadMethod="handleGetPersonList"
                    label="employeeName"
                    value="employeeId"
                    searchInputName="employeeName"
                    :clearable="false"
                    placeholder="请选择"
                    @select="handlePersonSelect"
                    style="width: 100%;"
                  >
                    <template slot-scope="options">
                      <p
                        style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                        :title="
                          `${options.data['employeeNo']}--${options.data['orgName']}--${options.data['employeeName']}`
                        "
                      >
                        {{ options.data['employeeNo'] }}--{{
                          options.data['orgName']
                        }}--{{ options.data['employeeName'] }}
                      </p>
                    </template>
                  </base-select>
                  <ts-input v-else v-model="form.employeeName" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item prop="employeeNo" label="工号">
                  <ts-input
                    v-model="form.employeeNo"
                    placeholder="请输入工号"
                    disabled
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item prop="employeeOrgName" label="部门">
                  <ts-input
                    v-model="form.employeeOrgName"
                    placeholder="请选择部门"
                    disabled
                  />
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item
                  prop="personalIdentity"
                  label="岗位"
                  :rules="rules.required"
                >
                  <ts-select
                    v-model="form.personalIdentity"
                    style="width: 100%;"
                    :disabled="disabled"
                  >
                    <ts-option
                      v-for="(item, index) in personalIdentity"
                      :key="index"
                      :label="item.dictName"
                      :value="item.dictValue"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  prop="incidentType"
                  label="离职类型"
                  :rules="rules.required"
                >
                  <ts-select
                    v-model="form.incidentType"
                    style="width: 100%;"
                    :disabled="disabled"
                  >
                    <ts-option
                      v-for="(item, index) in transferTypeList"
                      :key="index"
                      :label="item.dictName"
                      :value="item.dictValue"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item
                  prop="incidentTime"
                  label="离职日期"
                  :rules="rules.required"
                >
                  <el-date-picker
                    :disabled="disabled"
                    style="width: 100%"
                    v-model="form.incidentTime"
                    valueFormat="yyyy-MM-dd"
                    placeholder="请选择离职日期"
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  prop="cause"
                  label="离职原因"
                  :rules="rules.required"
                >
                  <ts-select
                    v-model="form.cause"
                    style="width: 100%;"
                    :disabled="disabled"
                  >
                    <ts-option
                      v-for="(item, index) in dimissionType"
                      :key="index"
                      :label="item.dictName"
                      :value="item.dictName"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item prop="relatedUserName" label="交接人">
                  <ts-input
                    v-model="form.relatedUserName"
                    placeholder="请输入交接人"
                    :disabled="disabled"
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-form-item prop="remark" label="备注">
              <ts-input
                :disabled="disabled"
                v-model="form.remark"
                :rules="rules.required"
                placeholder="请输入备注"
                type="textarea"
                class="textarea"
                maxlength="200"
                show-word-limit
              />
            </ts-form-item>
            <ts-form-item prop="fileId" label="附件">
              <base-upload
                v-model="form.fileId"
                :onlyRead="disabled"
                style="margin-top: 4px;"
              />
            </ts-form-item>
          </ts-form>
        </el-scrollbar>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button
          type="primary"
          :loading="submitLoading"
          @click="submit"
          v-if="!disabled"
          >提 交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    transferTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      title: '新增',
      disabled: false,
      personalIdentity: [],
      dimissionType: [],
      api: ''
    };
  },
  mounted() {
    this.positionList();
  },
  methods: {
    open(data = null, type = 'edit') {
      if (data) {
        this.form = deepClone(data);
        this.api = 'update';
        this.title = '编辑';
      } else {
        this.api = 'save';
        this.title = '新增';
      }
      this.disabled = type == 'edit' ? false : true;
      if (this.disabled) {
        this.title = '查看';
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    // 职务
    async positionList() {
      let res1 = await this.ajax.getDictType('personal_identity');
      this.personalIdentity = res1.object || [];
      let res2 = await this.ajax.getDictType('DIMISSION_TYPE');
      this.dimissionType = res2.object || [];
    },
    async handleGetPersonList(data) {
      let employeeStatus = ['1', '6', '12', '99', '89', '9'];
      let res = await this.ajax.getBasicMyEmployeeList({
        employeeStatusList: employeeStatus,
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$newMessage('error', res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    handlePersonSelect(item) {
      this.$set(this.form, 'employeeNo', item.employeeNo);
      this.$set(this.form, 'employeeOrgId', item.orgId);
      this.$set(this.form, 'employeeOrgName', item.orgName);
      this.$set(this.form, 'personalIdentity', item.personalIdentity);
      this.$set(this.form, 'personalIdentityName', item.personalIdentityName);
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        data.incidentCategory = '1';
        this.submitLoading = true;
        const res = await this.ajax.personnelIncidentSave(data, this.api);

        if (res.success && res.statusCode === 200) {
          this.submitLoading = false;
          this.$newMessage('success', '【操作】成功!');
          this.$emit('ok');
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.diabled = false;
      this.form = {};
      this.api = '';
      this.$refs.form.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-edit-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
    .content {
      height: 400px;
    }
  }
}
</style>
