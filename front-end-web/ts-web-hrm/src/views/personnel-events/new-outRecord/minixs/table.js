export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '申请人',
          value: 'applyUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入申请人姓名'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '申请人部门',
          value: 'applyDeptName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入申请人部门'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '外出类型',
          value: 'outType',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '全部'
          },
          childNodeList: [],
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '外出地点',
          value: 'outAddress',
          element: 'ts-input',
          elementProp: {
            placeholder: '外出地点'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '外出时间',
          value: 'completeDateList',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { completeDateList = [] } = this.searchForm,
                [startDate = '', endDate = ''] = completeDateList;
              if (startDate && endDate) {
                this.search();
              }
            }
          }
        },
        {
          label: '状态',
          value: 'status',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '全部'
          },
          childNodeList: [
            {
              element: 'ts-option',
              label: '正常',
              value: '1'
            },
            {
              element: 'ts-option',
              label: '取消',
              value: '2'
            }
          ],
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          type: 'seq',
          width: 40,
          align: 'center'
        },
        {
          label: '申请人',
          prop: 'applyUserName',
          width: 130,
          align: 'center',
          render: (h, { row }) => {
            if (row.workId) {
              let imag = require('../../../../assets/img/from_process.svg');
              return h('div', { class: 'workName' }, [
                h('div', {
                  class: 'process',
                  style: `background-image: url(${imag});`,
                  on: { click: () => this.handleProcess(row) }
                }),
                h(
                  'div',
                  {
                    class: 'empName',
                    on: { click: () => this.handleDetails(row) }
                  },
                  row.applyUserName
                )
              ]);
            } else {
              return h('div', { class: 'workName' }, [
                h(
                  'div',
                  {
                    class: 'empName',
                    on: { click: () => this.handleDetails(row) }
                  },
                  row.applyUserName
                )
              ]);
            }
          }
        },
        {
          label: '身份证号',
          prop: 'identityNumber',
          align: 'center',
          sortable: true,
          width: 190,
          sortBy: 'identity_number'
        },
        {
          label: '申请人部门',
          prop: 'applyDeptName',
          align: 'center',
          sortable: true,
          width: 150,
          sortBy: 'apply_dept_name'
        },
        {
          label: '岗位',
          prop: 'jobs',
          align: 'center',
          sortable: true,
          width: 150
        },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          sortable: true,
          width: 150,
          render: (h, { row }) => {
            let color = row.status == '1' ? '#333' : 'red';
            let label = row.status == '1' ? '正常' : '已取消';
            return h('span', { style: `color: ${color}` }, label);
          }
        },
        {
          label: '外出类型',
          prop: 'outType',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'out_type'
        },
        {
          label: '省内或省外',
          prop: 'provinceType',
          align: 'center',
          sortable: false,
          width: 100,
          sortBy: 'province_type'
        },
        {
          label: '外出开始时间',
          prop: 'startTime',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'start_time'
        },
        {
          label: '外出结束时间',
          prop: 'endTime',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'end_time'
        },
        {
          label: '外出天数',
          prop: 'outDays',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'out_days'
        },
        {
          label: '外出地点',
          prop: 'outAddress',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'out_address'
        },
        {
          label: '外出事由',
          prop: 'outRemark',
          align: 'center',
          width: 190,
          sortable: true,
          sortBy: 'out_remark'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 140,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit,
                show: this.hasOperateBtn.includes('edit')
              },
              {
                label: row.status == 1 ? '取消' : '恢复',
                event: this.handleCancel,
                show: this.hasOperateBtn.includes('cancel')
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'actionDel',
                show: this.hasOperateBtn.includes('delete')
              }
            ];
            arr = arr.filter(e => e.show);
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: arr }
            });
          }
        }
      ],

      outType: []
    };
  },
  computed: {
    hasOperateBtn() {
      return this.menuLimits.map(m => m.resourceId);
    }
  },
  methods: {
    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'GO_OUT_TYPE'
      });
      this.searchList[2].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemNameValue,
          element: 'ts-option'
        };
      });
    }
  }
};
