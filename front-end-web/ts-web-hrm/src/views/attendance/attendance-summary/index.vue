<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTab">
      <ts-tab-pane label="日报汇总" name="0"></ts-tab-pane>
      <ts-tab-pane label="月报汇总" name="1"></ts-tab-pane>
    </ts-tabs>
    <clock-day-table ref="table0" v-if="activeTab == 0" />
    <clock-month-table ref="table1" v-if="activeTab == 1" />
  </div>
</template>

<script>
// 人才库标签
import clockDayTable from './components/clockDayTable';
// 人才库类型
import clockMonthTable from './components/clockMonthTable';
export default {
  components: {
    clockDayTable,
    clockMonthTable
  },
  data() {
    return {
      activeTab: 0
    };
  },
  watch: {
    activeTab(val) {
      this.$nextTick(() => {
        this.$refs['table' + val]?.handleRefreshTable();
      });
    }
  },
  methods: {
    formateUnit(val, unit) {
      return val && val != 0 ? val + unit : val;
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
