<template>
  <vxe-modal
    width="800"
    :title="(editType == 'add' ? '新增' : '编辑') + '考勤记录'"
    v-model="visible"
    showFooter
    className="dialog-add-class"
  >
    <template #default>
      <ts-form ref="form" :model="editData">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="考勤科室"
              prop="reportDeptName"
              :rules="requiredRow"
            >
              <ts-ztree-select
                ref="treeSelect"
                defaultExpandAll
                :inpText.sync="editData.reportDeptName"
                :inpVal.sync="editData.reportDeptCode"
                :data="treeData"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item label="姓名" prop="name" :rules="requiredRow">
              <base-select
                v-model="editData.userCode"
                :inputText.sync="editData.name"
                :loadMethod="handleGetPersonList"
                label="employeeName"
                value="employeeNo"
                searchInputName="employeeName"
                :clearable="false"
                placeholder="请选择"
                style="width: 100%;"
                @select="handlePersonSelect"
              >
                <template slot-scope="options">
                  <p
                    style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                    :title="
                      `${options.data['employeeNo']}--${options.data['employeeName']}--${options.data['orgName']}`
                    "
                  >
                    {{ options.data['employeeNo'] }}--{{
                      options.data['employeeName']
                    }}--{{ options.data['orgName'] }}
                  </p>
                </template>
              </base-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item label="工号" prop="userCode" :rules="requiredRow">
              <ts-input
                v-model="editData.userCode"
                placeholder="选择姓名自动填充工号"
                disabled
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              label="考勤月份"
              prop="reportDate"
              :rules="requiredRow"
            >
              <ts-month-picker
                v-model="editData.reportDate"
                placeholder="请选择月份"
                clearable
                class="month-picker"
              ></ts-month-picker>
            </ts-form-item>
          </ts-col>
          <ts-col v-for="item of numberList" :key="item.prop" :span="8">
            <ts-form-item :label="item.label" :prop="item.prop">
              <ts-input
                v-model="editData[item.prop]"
                clearable
                @input="handleInputNumber($event, item.prop)"
                @blur="handleInputBlur(item.prop)"
              ></ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-form-item
          v-for="item of textList"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
        >
          <ts-input
            v-model="editData[item.prop]"
            type="textarea"
            rows="4"
            resize="none"
          ></ts-input>
        </ts-form-item>
      </ts-form>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="handleConfirm">确定</ts-button>
        <ts-button class="shallowButton" @click="handleCancelModel"
          >取消</ts-button
        >
      </span>
    </template>
  </vxe-modal>
</template>

<script>
export default {
  data() {
    return {
      editType: 'add',
      visible: false,
      loading: false,

      numberList: [],
      textList: [],
      treeData: [],
      editData: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  methods: {
    open({ columns = [], treeData = [], editType = 'add', editData = {} }) {
      let numberList = columns.filter(
        item => item.prop != 'ATTENDANCE_BZ' && item.prop.includes('ATTENDANCE')
      );
      this.numberList = numberList;
      this.textList = columns.filter(item => item.prop == 'ATTENDANCE_BZ');
      this.treeData = treeData;
      this.editType = editType;
      this.editData = editData;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.treeSelect && this.$refs.treeSelect.$forceUpdate();
      });
    },
    async handleGetPersonList(data) {
      let res = await this.ajax.getBasicMyEmployeeList({
        employeeStatusList: [],
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    handlePersonSelect(item) {
      this.$set(this.editData, 'employeeNo', item.employeeNo);
      this.$set(this.editData, 'employeeName', item.employeeName);
      if (!this.editData.reportDeptName) {
        this.$set(this.editData, 'reportDeptCode', item.orgId);
        this.$set(this.editData, 'reportDeptName', item.orgName);
        this.$refs.treeSelect.$forceUpdate();
      }
    },
    handleInputNumber(val, key) {
      let newVal = val.match(/[0-9]+(.[0-9]{1,2}|.)?/g) || [];
      this.$set(this.editData, key, newVal[0]);
    },
    handleInputBlur(key) {
      let val = this.editData[key],
        newVal = parseFloat(val);
      this.$set(this.editData, key, isNaN(newVal) ? '' : newVal);
    },
    handleCancelModel() {
      this.visible = false;
      this.keyList = [];
    },
    async handleConfirm() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate || this.loading) {
        return;
      }
      this.loading = true;
      this.editData.reportDate = this.$moment(this.editData.reportDate).format(
        'YYYY-MM'
      );
      this.ajax[
        this.editType == 'add'
          ? 'handleAddReportStatisticTableData'
          : 'handleEditReportStatisticTableData'
      ](this.editData).then(res => {
        this.loading = false;
        if (!res.success) {
          this.$message.error(
            res.message || (this.editType == 'add' ? '新增' : '编辑') + '失败'
          );
          return;
        }
        this.$message.success('操作成功');
        this.$emit('success');
        this.handleCancelModel();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.month-picker {
  width: 100%;
}
</style>
