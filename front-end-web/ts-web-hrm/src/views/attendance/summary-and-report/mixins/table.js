import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      searchForm: {
        reportDate: this.$moment().format('YYYY-MM'),
        name: ''
      },
      searchList: [
        {
          label: '月份',
          value: 'reportDate',
          element: 'TsMonthPicker',
          elementProp: {
            placeholder: '请选择月份'
          },
          event: {
            change: async e => {
              this.searchForm.reportDate = this.$moment(e._d).format('YYYY-MM');
              this.treeParams.reportDate = this.$moment(e._d).format('YYYY-MM');
              await this.$refs['searchTree'].getTreeData();
            }
          }
        },
        {
          label: '姓名',
          value: 'name',
          element: 'TsInput',
          elementProp: {
            placeholder: '请输入名称'
          }
        }
      ],
      columns: []
    };
  },
  computed: {
    pendingApproval() {
      if (this.reportDept.reportDeptCode && this.deptAttendanceStatus == 1) {
        return true;
      }
      return false;
    },
    actions() {
      let searchActions = [];
      if (this.reportDept.reportDeptCode && !this.attendanceManage) {
        switch (this.deptAttendanceStatus) {
          case '1':
            searchActions = [
              {
                label: '撤销',
                click: this.handleRevokeAttendanceRecord
              }
            ];
            break;
          case '2':
            searchActions = [];
            break;
          default:
            searchActions = [
              {
                label: '上报',
                prop: { type: 'primary' },
                click: this.handleReportAttendanceRecord
              },
              {
                label: '导入',
                click: this.handleImportAttendanceRecord
              }
            ];
            break;
        }
      }
      if (this.attendanceManage) {
        searchActions.push(
          {
            label: '新增',
            click: this.handleOpenAddGatherItemModal
          },
          {
            label: '打印',
            click: this.handlePrintData
          },
          {
            label: '科室上报情况',
            click: this.openDeptReportSituation
          },
          {
            label: '导出',
            prop: { type: 'primary' },
            click: this.handleExportAttendanceRecord
          }
        );
      }
      return searchActions;
    }
  },
  methods: {
    //检索
    async search() {
      this.$refs.table.pageNo = 1;
      await this.handleRefreshTable();
    },
    async getTableHeadCols() {
      let params = {
        attendanceManage: this.attendanceManage ? 'Y' : 'N'
      };
      let res = await this.ajax.getAttendanceSummaryTableHeadCols(params);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let columns = res.object.headCols.filter(item => {
        return !item.hidden;
      });
      this.columns = columns.map(item => {
        return {
          ...item,
          prop: item.name
        };
      });
      this.columns.unshift({
        label: '序号',
        prop: 'index',
        align: 'center'
        // fixed: 'left'
      });
      // this.columns[1].fixed = 'left';
      // this.columns[2].fixed = 'left';
    },
    async handleRefreshTableColumns() {
      let columns = this.columns.filter(item => {
        return item.label != '操作';
      });
      if (!this.attendanceManage) {
        columns.push({
          label: '操作',
          fixed: 'right',
          align: 'center'
        });
      } else {
        let actionList = [
          {
            label: '编辑',
            event: this.handleOpenAddGatherItemModal.bind(this, 'edit')
          },
          {
            label: '删除',
            event: this.handleDeleteTableData
          }
        ];
        columns.unshift({
          label: '操作',
          prop: '',
          align: 'center',
          formatter: (row, prop, cell) => {
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': event => event(row) }}
              />
            );
          }
        });
      }
      this.columns = deepClone(columns);
      await this.$nextTick(() => {
        this.$refs.table.computedTableWidth();
      });
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        formData = {
          ...this.searchForm,
          ...this.reportDept,
          pageNo,
          pageSize,
          attendanceManage: this.attendanceManage ? 'Y' : 'N'
        };
      let res = await this.ajax.getAttendanceSummaryReportList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        let daleteStatus = [0, 3, 4]; // ststus 0未上报 1已上报 2审批通过 3审批不通过 4已撤销
        let canBeDelete = daleteStatus.includes(item.status);
        return {
          index,
          canBeDelete,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
