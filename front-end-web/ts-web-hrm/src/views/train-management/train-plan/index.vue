<template>
  <div class="train-plan-management">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
      :resetData="{}"
    >
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <ts-dialog
      :title="actionDialogTitle"
      :visible.sync="actionDialogVisible"
      :fullscreen="['add', 'edit'].includes(eventType)"
      type="large"
      class="dialog-container"
    >
      <div
        class="dialog-content"
        :class="{
          'upload-review-content': eventType == 'uploadReviewFile'
        }"
      >
        <trainTemplateBoard
          v-if="['add', 'edit'].includes(eventType) && actionDialogVisible"
          @use-template="handleUseTemplate"
          @save-template="handleGetTemplateData"
        />
        <el-scrollbar
          ref="scroll"
          style="flex: 1; display: flex;"
          wrap-style="overflow-x: hidden;"
          class="content-scroll"
        >
          <!-- 新增、编辑 -->
          <edit-plan-tab
            v-if="['add', 'edit'].includes(eventType)"
            ref="editBoard"
          ></edit-plan-tab>
          <!-- 查看详情 -->
          <preview-plan-tab
            v-if="eventType == 'preview' && actionDialogVisible"
            ref="editBoard"
          ></preview-plan-tab>
          <!-- 取消培训 -->
          <quite-plan-tab
            v-if="['quite', 'superQuite'].includes(eventType)"
            ref="editBoard"
          ></quite-plan-tab>
          <template v-if="eventType == 'uploadReviewFile'">
            <base-upload
              ref="upload"
              v-model="editData.huifangFile"
              class="upload-table-container"
            >
              <template v-slot:uploadFiles="{ fileList }">
                <file-upload-table :data="fileList" />
              </template>
            </base-upload>
          </template>
        </el-scrollbar>
      </div>
      <template slot="footer">
        <ts-button
          v-if="eventType != 'preview'"
          type="primary"
          @click="handleActionConfirm"
          >确认</ts-button
        >
        <ts-button @click="handleCancelActionModal">
          {{ eventType == 'preview' ? '关闭' : '取消' }}
        </ts-button>
      </template>
    </ts-dialog>

    <score-board ref="scoreBoard" />
    <assign-point-modal ref="assignPointModal" />
  </div>
</template>

<script>
import editPlanTab from './components/edit-plan-tab.vue';
import previewPlanTab from '../components/preview-content.vue';
import quitePlanTab from './components/quite-plan-tab.vue';
import fileUploadTable from './components/file-upload-table.vue';
import scoreBoard from '../components/score-board.vue';
import assignPointModal from './components/assign-point-modal.vue';
import trainTemplateBoard from './components/train-template-board.vue'; // 培训模板

import table from './mixins/table';

export default {
  mixins: [table],
  components: {
    editPlanTab,
    previewPlanTab,
    quitePlanTab,
    fileUploadTable,
    scoreBoard,
    assignPointModal,
    trainTemplateBoard
  },
  data() {
    return {
      dialogDetails: false,

      eventType: 'add',
      actionDialogVisible: false,
      actionDialogTitle: '新增',

      editData: {}
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    //检索
    async refresh() {
      this.$refs.table.pageNo = 1;
      await this.getPageSource();
      this.getTrainingType();
      this.$refs.table.triggerRefresh();
    },
    getSearchData() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        [startDate, endDate] = this.searchForm.trainingTimeList || [],
        [searchStartCreateDate, searchEndCreateDate] =
          this.searchForm.createDateList || [],
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 'create_date',
          sord: 'desc'
        },
        assignData = {
          startDate,
          endDate,
          searchStartCreateDate,
          searchEndCreateDate
        };
      Object.keys(assignData).map(key => {
        assignData[key] && (searchForm[key] = assignData[key]);
      });
      delete searchForm.createDateList;
      delete searchForm.trainingTimeList;
      return searchForm;
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = this.getSearchData();

      let res = await this.ajax.getTrainPlanTableData(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.status = 1;
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    },
    handleDetails(row = {}) {
      this.ajax.getTrainPlanDetailData(row.trainPlanId).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '计划详情获取失败');
          return;
        }
        this.eventType = 'preview';
        this.actionDialogTitle = '查看详情';
        this.actionDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.editBoard &&
            this.$refs.editBoard.init({
              data: res.object,
              trainType: this.trainType
            });
        });
      });
    },
    openAddOrEditModal(
      row = {
        applicantName: this.$store.state.common.userInfo.employeeName,
        applicantCode: this.$store.state.common.userInfo.employeeNo,
        applicantDeptName: this.$store.state.common.userInfo.orgName,
        applicantDeptCode: this.$store.state.common.userInfo.orgCode
      },
      eventType = 'add'
    ) {
      this.eventType = eventType;
      this.actionDialogVisible = true;
      this.actionDialogTitle = eventType == 'edit' ? '编辑' : '新增';
      this.$nextTick(() => {
        this.$refs.editBoard &&
          this.$refs.editBoard.init({
            trainType: this.trainType,
            row
          });
      });
    },
    handleExportTrainPlan() {
      let data = this.getSearchData(),
        search = Object.keys(data)
          .map(key => {
            return `${key}=${data[key]}`;
          })
          .join('&'),
        aDom = document.createElement('a');
      aDom.href = '/ts-hrms/train/plan/export?' + search;
      aDom.click();
    },
    async handleDelete(row) {
      let confirm = await this.$confirm('确定删除该数据吗？', '提示', {
        type: 'warning'
      }).catch(res => res);
      if (confirm == 'cancel') {
        return;
      }
      this.ajax.handleDeleteTrainPlanData(row.trainPlanId).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.refresh();
      });
    },
    /**@desc 打开取消弹窗 */
    openQuiteModal(row) {
      this.actionDialogVisible = true;
      this.actionDialogTitle = '取消计划';
      this.$nextTick(() => {
        this.$refs.editBoard && this.$refs.editBoard.init(row);
      });
    },
    async handleActionConfirm() {
      let actionData = false;
      switch (this.eventType) {
        case 'add':
        case 'edit':
          actionData = await this.$refs.editBoard.validate();
          if (actionData) {
            let {
                isBeforeTime,
                trainTopic,
                trainPlaceId,
                trainStartTime,
                trainEndTime,
                trainEmployeeNames,
                trainEmployeeIds,
                trainNumbers
              } = actionData,
              attendEmployeeNoList = trainEmployeeIds.split(','),
              attendEmployeeList = trainEmployeeNames
                .split(',')
                .map((username, index) => ({
                  username,
                  usercode: attendEmployeeNoList[index]
                })),
              reservationData = {
                appTypeId: '1',
                attendEmployeeNoList,
                attendEmployeeList,
                motif: trainTopic,
                boardroomId: trainPlaceId,
                controlNumber: trainNumbers,
                startTime: trainStartTime + ':00',
                endTime: trainEndTime + ':59',
                autoRefreshSignInQrCodeType: 2,
                motifType: 1,
                repeatRate: 0,
                sendRemindAdvanceMinute: 15,
                sendRemindType: 1,
                signOutType: 2,
                times: [trainStartTime + ':00', trainEndTime + ':59']
              };

            if (!isBeforeTime) {
              let reservationRes = await this.ajax.handleReservationMeetingRoom(
                reservationData
              );
              if (!reservationRes.success) {
                this.$message.error(reservationRes.message || '会议室预约失败');
                return;
              }
              actionData.applyId = reservationRes.object;
            }
            actionData.planState = 1;

            this.ajax[
              this.eventType == 'add'
                ? 'handleAddTrainPlanData'
                : 'handleEditTrainPlanData'
            ](actionData).then(res => {
              if (!res.success) {
                this.$message.error(res.message || '新增失败');
                return;
              }
              this.$message.success('新增成功');
              this.handleCancelActionModal();
              this.refresh();
            });
          }
          break;
        case 'quite':
        case 'superQuite':
          actionData = await this.$refs.editBoard.validate();
          if (!actionData) {
            break;
          }
          actionData.planState = 4;
          actionData.applyId &&
            this.ajax
              .handleQuiteBoardRoom({
                applyId: actionData.applyId
              })
              .then(res => {
                if (!res.success) {
                  this.$message.error(res.message || '会议取消失败');
                  return;
                }
              });
          this.ajax.handleEditTrainPlanData(actionData).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '操作失败');
              return;
            }
            this.$message.success('取消成功');
            this.handleCancelActionModal();
            this.refresh();
          });
          break;
        case 'uploadReviewFile':
          if (!this.editData.huifangFile) {
            this.$message.warning('请至少上传一个回放附件');
            break;
          }
          this.ajax.handleEditTrainPlanData(this.editData).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '操作失败');
              return;
            }
            this.$message.success('取消成功');
            this.handleCancelActionModal();
            this.refresh();
          });
          break;
      }
    },
    handleCancelActionModal() {
      this.actionDialogVisible = false;
    },
    openUploadReviewFileModal(row = {}) {
      this.editData = { ...row };
      this.actionDialogTitle = '上传会议纪要';
      this.actionDialogVisible = true;
    },
    showScoreBoard() {
      this.$refs.scoreBoard.open();
    },
    /**@desc 打开赋予学分弹窗 */
    openAssignPointsModal(row) {
      this.$refs.assignPointModal.open(row);
    },
    /**@desc 使用模板 */
    handleUseTemplate(template) {
      this.$refs.editBoard && this.$refs.editBoard.useTemplate(template);
    },
    /**@desc 获取当前填写的培训数据 */
    async handleGetTemplateData(callback) {
      if (!this.$refs.editBoard) {
        return;
      }
      let templateData = await this.$refs.editBoard.validateByTemplate();
      if (!templateData) {
        return;
      }
      callback(templateData);
    }
  }
};
</script>

<style lang="scss" scoped>
.train-plan-management {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  /deep/ .dialog-container {
    .el-dialog {
      width: 1080px !important;

      .el-dialog__body {
        width: 1080px !important;
      }
    }

    .is-fullscreen {
      width: 100% !important;
    }
  }

  /deep/ .trasen-search-content .left-search-content {
    .ts-range-picker {
      width: 230px;
    }
    .ts-input {
      width: 160px;
    }
  }
  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .label {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .success {
      background: #d4ffd4;
      color: #000;
    }

    .pengding {
      background: #eaeaea;
      color: #000;
    }

    .error {
      background: #ffd4d4;
      color: #000;
    }

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
.dialog-content {
  max-height: calc(100vh - 140px);
  display: flex;
  overflow: hidden;
  display: flex;
  /deep/.content-scroll > .el-scrollbar__wrap {
    margin-bottom: 0 !important;
    flex: 1;
    height: unset;
  }
  &.upload-review-content {
    height: 500px;
    /deep/ .el-scrollbar__wrap {
      display: flex;
      overflow: hidden;
      width: 100%;
      .el-scrollbar__view {
        flex: 1;
        display: flex;
        overflow: hidden;
      }
      .upload-table-container {
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
}
/deep/ .score-show-btn .fa-list {
  margin-right: $primary-spacing;
  margin-top: 2px;
  color: $primary-blue;
}
</style>
