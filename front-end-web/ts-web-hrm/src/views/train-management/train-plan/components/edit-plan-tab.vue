<template>
  <div class="edit-plan-container">
    <ts-form ref="form" :model="editData">
      <ts-form-item label="培训主题" prop="trainTopic" :rules="requiredRow">
        <ts-input
          v-model="editData.trainTopic"
          placeholder="请输入培训主题"
          clearable
          maxlength="50"
        ></ts-input>
      </ts-form-item>
      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            label="培训讲师"
            prop="trainLecturer"
            :rules="requiredRow"
          >
            <ts-input
              v-model="editData.trainLecturer"
              maxlength="10"
              clearable
              placeholder="请输入培训讲师"
            ></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="培训类别" prop="trainType" :rules="requiredRow">
            <ts-select
              v-model="editData.trainType"
              clearable
              class="full-picker"
            >
              <ts-option
                v-for="item of trainingTypeList"
                :key="item.value"
                v-bind="item"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item
            label="培训开始时间"
            prop="trainStartTime"
            :rules="requiredRow"
          >
            <ts-date-picker
              v-model="editData.trainStartTime"
              valueFormat="YYYY-MM-DD HH:mm"
              format="YYYY-MM-DD HH:mm"
              allowClear
              showTime
              class="full-picker"
            ></ts-date-picker>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item
            label="培训结束时间"
            prop="trainEndTime"
            :rules="requiredRow"
          >
            <ts-date-picker
              v-model="editData.trainEndTime"
              valueFormat="YYYY-MM-DD HH:mm"
              format="YYYY-MM-DD HH:mm"
              allowClear
              showTime
              class="full-picker"
            ></ts-date-picker>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="预算(元)">
            <ts-input
              v-model="editData.budget"
              @input="handleInputNumber"
              @blur="handleNumberInputBlur('budget')"
            ></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item
            label="申请人"
            prop="applicantName"
            style="position: relative;"
            :rules="requiredRow"
          >
            <ts-input
              v-model="editData.applicantName"
              placeholder="请选择申请人"
              disabled
              class="user-select-input"
            ></ts-input>
            <img
              class="person-icon"
              src="@/assets/img/defUserPhoto.png"
              @click="handleOpenUserSelect('applicant', true)"
            />
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item
            label="申请科室"
            prop="applicantDeptName"
            :rules="requiredRow"
          >
            <ts-input v-model="editData.applicantDeptName" disabled></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="学时" prop="creditHour" :rules="requiredRow">
            <ts-input
              v-model="editData.creditHour"
              @input="handleInputOnePointNumber($event, 'creditHour')"
              @blur="handleNumberInputBlur('creditHour')"
            ></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item
            label="课程类型"
            prop="placeCourseType"
            :rules="requiredRow"
          >
            <ts-select
              v-model="editData.placeCourseType"
              clearable
              class="full-picker"
            >
              <ts-option
                v-for="item of placeCourseTypeList"
                :key="item.value"
                v-bind="item"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="培训层级" prop="placeLevel" :rules="requiredRow">
            <ts-select
              v-model="editData.placeLevel"
              clearable
              @change="handleChangePlaceLevel"
              class="full-picker"
            >
              <ts-option
                v-for="item of placeLevelList"
                :key="item.value"
                v-bind="item"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="学分" prop="credit" :rules="requiredRow">
            <ts-input
              disabled
              v-model="editData.credit"
              @input="handleInputOnePointNumber($event, 'credit', 10)"
              @blur="handleNumberInputBlur('credit')"
            ></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label-width="30px" label=" ">
            <ts-checkbox
              v-model="editData.repeatCompute"
              true-label="1"
              false-label="0"
            >
              参加过相同培训主题培训，学分不重复计算
            </ts-checkbox>
          </ts-form-item>
        </ts-col>
        <!-- <ts-col :span="12">
          <ts-form-item label=" ">
            <ts-checkbox v-model="editData.isNeedSp">是否审批</ts-checkbox>
          </ts-form-item>
        </ts-col> -->
      </ts-row>
      <ts-form-item label="培训地点" prop="trainPlace" :rules="requiredRow">
        <base-select
          v-model="editData.trainPlaceId"
          :inputText.sync="editData.trainPlace"
          :data="meetingRoomList"
          label="roomName"
          value="roomId"
        ></base-select>
      </ts-form-item>
      <ts-form-item label="培训内容" prop="trainContent" :rules="requiredRow">
        <ts-input
          v-model="editData.trainContent"
          rows="4"
          resize="none"
          show-word-limit
          type="textarea"
          maxlength="500"
        ></ts-input>
      </ts-form-item>
      <ts-form-item
        label="培训人员"
        prop="trainEmployeeName"
        :rules="requiredRow"
        style="position: relative;"
      >
        <ts-input
          v-model="editData.trainEmployeeName"
          rows="4"
          resize="none"
          type="textarea"
          disabled
          class="user-select-input"
        ></ts-input>
        <img
          class="person-icon"
          src="@/assets/img/defUserPhoto.png"
          @click="handleOpenUserSelect('trainEmployee')"
        />
      </ts-form-item>
      <ts-form-item label="备注" prop="remark">
        <ts-input
          v-model="editData.remark"
          rows="4"
          resize="none"
          show-word-limit
          type="textarea"
          maxlength="200"
        ></ts-input>
      </ts-form-item>

      <ts-form-item label="通知">
        <ts-switch
          v-model="editData.sendDing"
          active-value="1"
          inactive-value="0"
        />
      </ts-form-item>

      <ts-form-item label="附件">
        <base-upload v-model="editData.trainFile"> </base-upload>
      </ts-form-item>
    </ts-form>

    <ts-user-dept-select
      ref="userSelect"
      @ok="handleSaveUserSelect"
    ></ts-user-dept-select>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      activeTab: '1',
      editData: {},
      trainingTypeList: [],
      placeLevelList: [
        { label: '院级', value: '院级', val: 5 },
        { label: '部级', value: '部级', val: 3 },
        { label: '科级', value: '科级', val: 0 }
      ],
      placeCourseTypeList: [
        { label: '必修课', value: '必修课' },
        { label: '选修课', value: '选修课' }
      ],
      meetingRoomList: [],
      requiredRow: { required: true, message: '必填' }
    };
  },
  watch: {
    'editData.trainStartTime': {
      handler(val) {
        if (!this.editData.trainPlanId) {
          let trainEndTime = this.$dayjs(val)
            .add(1, 'hour')
            .format('YYYY-MM-DD HH:mm');
          this.$set(this.editData, 'trainEndTime', trainEndTime);
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * @desc 初始化培训计划
     * @param {object} props 初始化配置
     * @param {object[]} props.trainType 培训类型-数据字典
     * @param {object} props.row 编辑的数据
     */
    init({ trainType = [], row = {} }) {
      this.activeTab = '1';
      this.editData = deepClone(row);
      this.editData.trainEmployeeName = this.editData.trainEmployeeNames;
      this.editData.trainEmployeeCode = this.editData.trainEmployeeIds;
      this.trainingTypeList = trainType;

      if (!this.editData.trainPlanId) {
        let trainStartTime = this.$dayjs()
          .add(10, 'minute')
          .format('YYYY-MM-DD HH:mm');
        this.$set(this.editData, 'trainStartTime', trainStartTime);
      }

      this.getMeetingRoomData();
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
        this.$forceUpdate();
      });
    },
    handleChangePlaceLevel(e) {
      const find = this.placeLevelList.find(item => item.value === e) || {};
      this.$set(this.editData, 'credit', find?.val);
    },
    /**@desc 获取会议室数据  */
    getMeetingRoomData() {
      this.meetingRoomList = [];
      this.ajax.getMeetingRoomDataList({ isVideo: '' }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '培训地点数据获取失败');
          return;
        }
        this.meetingRoomList = res.object;
      });
    },
    /**@desc 使用模板 */
    async useTemplate(template) {
      if (
        Object.keys(this.editData).length &&
        this.editData.applicantCode !=
          this.$store.state.common.userInfo.employeeNo
      ) {
        let confirm = await this.$confirm(
          '已存在数据，使用模板将会覆盖原有内容，是否确认使用模板？',
          '提示',
          {
            type: 'warning'
          }
        ).catch(res => res);
        if (confirm != 'confirm') {
          return;
        }
      }
      this.editData = Object.assign({}, this.editData, template);
    },
    /**
     * @desc 模板保存-表单校验
     * @returns {object | boolean} data
     */
    async validateByTemplate() {
      let res = this.$refs.form.validate().catch(res => res);
      if (!res) {
        return false;
      }
      let templateData = deepClone(this.editData);
      delete templateData.remark;
      delete templateData.trainFile;
      return templateData;
    },
    /**@desc 输入两位小数 */
    handleInputNumber(value) {
      var numberReg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/,
        matchList = value.match(numberReg) || [],
        newVal = matchList[0] || '';
      this.$set(this.editData, 'budget', newVal);
    },
    handleInputOnePointNumber(value, key, max) {
      var numberReg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1}|\.))?/,
        matchList = value.match(numberReg) || [],
        newVal = matchList[0] || '';
      max && newVal && newVal > max && (newVal = max);
      this.$set(this.editData, key, newVal);
    },
    handleNumberInputBlur(key) {
      let val = parseFloat(this.editData[key]);
      if (isNaN(val)) {
        return;
      }
      this.$set(this.editData, key, val);
    },
    handleOpenUserSelect(key, isRadio = false) {
      let names = this.editData[key + 'Name']?.split(',') || [],
        ids = this.editData[key + 'Code']?.split(',') || [],
        empList = [];
      names.map((name, index) => {
        empList.push({
          empCode: ids[index],
          empName: name
        });
      });
      this.$refs.userSelect.open(key, {
        empList,
        appendToBody: true,
        showCheckbox: false,
        isRadio
      });
    },
    handleSaveUserSelect(res) {
      Object.keys(res).map(key => {
        let empList = res[key].empList || [];
        this.$set(
          this.editData,
          key + 'Name',
          empList.map(item => item.empName).join(',')
        );
        this.$set(
          this.editData,
          key + 'Code',
          empList.map(item => item.empCode).join(',')
        );
        if (key == 'applicant') {
          let user = empList[0] || {};
          this.$set(this.editData, 'applicantDeptName', user.empDeptName);
          this.$set(this.editData, 'applicantDeptCode', user.empDeptId);
        }
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      });
    },
    async validate(fn) {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (fn && Object.prototype.toString.call(fn) == '[object Function]') {
        fn(validate, this.editData);
        return;
      }
      if (!validate) {
        return false;
      }
      let saveData = deepClone(this.editData),
        { trainStartTime, trainEndTime } = saveData;

      if (this.$dayjs(trainEndTime).isBefore(this.$dayjs(trainStartTime))) {
        this.$message.error('会议结束时间不能早于会议开始时间!');
        return false;
      }

      let isBeforeTime = false;
      if (
        this.$dayjs(trainStartTime).isBefore(this.$dayjs()) &&
        this.$dayjs(trainEndTime).isBefore(this.$dayjs())
      ) {
        isBeforeTime = true;
      }

      if (!isBeforeTime) {
        let roomValidate = await this.ajax.handleCheckMeetingRoomIsOccupy({
          startTime: trainStartTime + ':00',
          endTime: trainEndTime + ':59',
          boardroomId: saveData.trainPlaceId
        });
        if (!roomValidate.success) {
          this.$message.error(roomValidate.message || '会议室暂不可使用');
          return false;
        }
      }

      Object.assign(saveData, {
        isBeforeTime,
        trainStartTime,
        trainEndTime,
        trainEmployeeIds: saveData.trainEmployeeCode,
        trainEmployeeNames: saveData.trainEmployeeName,
        trainNumbers: saveData.trainEmployeeCode.split(',').length
      });
      delete saveData.trainEmployeeName;
      delete saveData.trainEmployeeCode;

      return saveData;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .person-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 999;
}
.user-select-container {
  height: 464px;
  .search-input {
    width: 180px;
    margin-right: $primary-spacing;
  }
  .user-container {
    margin-top: $primary-spacing;
    overflow: hidden;
    .dept-tree {
      height: 100%;
      width: 220px;
      margin-right: $primary-spacing;
      flex-shrink: 0;
    }
  }
  .checked-user-container {
    border: 1px solid $split-line-color;
    border-radius: 2px;
  }
}

/deep/ {
  .ts-form-item {
    .user-select-input {
      input,
      textarea {
        cursor: pointer !important;
        background-color: #fff !important;
        color: $primary-black !important;
      }
    }
    &.is-error .user-select-input {
      input,
      textarea {
        border-color: #f56c6c;
      }
    }
    &:not(.is-error) .user-select-input {
      input,
      textarea {
        &:hover {
          border-color: rgb(192, 196, 204) !important;
        }
      }
    }
  }
}
.full-picker {
  width: 100% !important;
  /deep/ .ant-calendar-time-picker-select {
    width: 50% !important;
  }
  /deep/ .ant-calendar-time-picker-select:last-child {
    display: none;
  }
}
</style>
