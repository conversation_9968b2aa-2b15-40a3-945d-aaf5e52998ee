<template>
  <div class="componet-select-dept flex-column">
    <div class="input-container flex-space">
      <ts-input
        placeholder="输入关键字进行过滤"
        @input="searchInput"
        class="search-input"
        v-model="searchVal"
      >
        <template slot="append">
          <i
            @click="handleClickInputCircle"
            class="el-icon-circle-close input-circle"
          />
        </template>
      </ts-input>
    </div>
    <el-scrollbar
      class="flex-column tree-scrollbar"
      style="flex: 1;"
      wrap-style="overflow-x: hidden;"
    >
      <ul class="group-container">
        <li
          class="group-item flex"
          v-for="item in groupData"
          :key="item.id"
          v-show="item.show"
        >
          <ts-checkbox
            true-label="1"
            false-label="0"
            v-model="item.check"
            @change="$event => handleChangeCheckbox($event, item)"
          />
          <div class="flex info-item" @click.stop="handleClickGroupItem(item)">
            <img class="group-img" src="@/assets/img/other/group.svg" alt="" />
            <p class="group-name">
              {{ item.groupName }}
            </p>
          </div>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  model: {
    prop: 'selectGroupIds',
    event: 'change'
  },
  props: {
    selectGroupIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchVal: '',
      searchInput: null,
      groupData: []
    };
  },
  computed: {},
  methods: {
    async init(groupData) {
      this.searchVal = '';
      this.searchInput = this.debounce(this.input, 300);

      groupData.forEach(f => {
        f.check = false;
        f.show = true;
      });
      this.groupData = groupData;
    },

    handleClickInputCircle() {
      this.searchVal = '';

      this.groupData.forEach(f => {
        f.show = true;
      });
    },

    handleClickGroupItem(e) {
      e.check = e.check == '0' ? '1' : '0';
      this.handleModelValue();
    },

    handleChangeCheckbox(e, item) {
      item.check = e;
      this.handleModelValue();
    },

    handleModelValue() {
      let val = this.groupData
        .filter(f => f.check === '1')
        .map(m => m.id)
        .join(',');

      this.$emit('change', val);
    },

    input: function(val) {
      if (!val) {
        this.groupData.forEach(f => {
          f.show = true;
        });
      } else {
        this.groupData.forEach(f => {
          if (f.groupName.indexOf(val) === -1) {
            f.show = false;
          }
        });
      }
    },

    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.componet-select-dept {
  height: 100%;
  .input-container {
    margin-bottom: 8px;

    ::v-deep {
      .el-input-group__append {
        padding: 0px !important;

        .input-circle {
          cursor: pointer;
          padding: 0px 8px !important;
        }
      }
    }

    .search-input {
      width: 145px;
      min-width: 145px;
      input {
        width: 145px;
        min-width: 145px;
      }
    }
  }

  .group-container {
    display: flex;
    flex-direction: column;
    .group-item {
      border-bottom: 1px solid #eee;
      margin-bottom: 4px;
      color: #333;
      padding: 4px;
      cursor: pointer;

      .info-item {
        flex: 1;
        .group-img {
          margin: 0 8px;
          width: 16px;
          height: 16px;
        }

        .group-name {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin: 0;
          padding: 0;
        }
      }

      &:hover {
        background-color: #eee;
      }
    }
  }
}
</style>
