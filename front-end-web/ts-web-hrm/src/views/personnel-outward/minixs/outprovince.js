export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '',
          value: 'applyUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名'
          }
        },
        {
          label: '科室',
          value: 'applyDeptName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入科室'
          }
        },
        {
          label: '外出时间',
          value: 'completeDateList',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '科室',
          prop: 'orgName',
          align: 'center',
          renderHeader: (h, { column, $index }) => {
            return (
              <span style="font-weight:bold">
                科室
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <i class="el-icon-info"></i>
                  <div slot="content">
                    1）临床：每个科室3人次(限高级职称或中层干部及科室负责人)
                    <br />
                    2）行政后勤：每个科室1人次(限中层干部及科室负责人)
                    <br />
                    3）医技：每个科室2人次(限高级职称或中层干部及科室负责人)
                    <br />
                    4）护理：全院5次(限高级职称)
                  </div>
                </el-tooltip>
              </span>
            );
          }
        },
        {
          label: '科室外出次数',
          prop: 'num',
          align: 'center',
          formatter: row => {
            return (row.num || 1) + '次';
          }
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center'
        },
        {
          label: '外出地点',
          prop: 'outAddress',
          align: 'center'
        },
        {
          label: '外出开始时间',
          prop: 'startTime',
          align: 'center'
        },
        {
          label: '外出结束时间',
          prop: 'endTime',
          align: 'center'
        },
        {
          label: '外出天数',
          prop: 'outDays',
          align: 'center'
        }
      ]
    };
  }
};
