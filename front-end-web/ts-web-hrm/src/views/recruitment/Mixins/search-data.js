import { getDictionaries } from '@/api/ajax/NewRecruit/dictionaries.js';
import { getTitle } from '@/api/ajax/NewRecruit/dictionaries.js';

export default {
  created() {
    this.handleGetSex();
    this.handleGetEducationType();
    this.handleGetTitle();
    this.handleDictItemEmployeeStatus();
  },
  data() {
    return {
      educationType: [],
      sexList: [],
      titleList: []
    };
  },
  methods: {
    // 获取性别字典
    async handleGetSex() {
      await getDictionaries('SEX_TYPE', this, 'sexList');
      let find = this.searchList.find(item => item.value === 'gender');
      if (!find) return;

      find.childNodeList = this.sexList.map(item => {
        return {
          ...item,
          label: item.itemName,
          value: item.itemNameValue,
          element: 'ts-option'
        };
      });
    },
    // 获取学历
    async handleGetEducationType() {
      await getDictionaries('education_type', this, 'educationType');
      let find = this.searchList.find(item => item.value === 'xueli');
      if (!find) return;

      find.childNodeList = this.educationType.map(item => {
        return {
          ...item,
          label: item.itemName,
          value: item.itemNameValue,
          element: 'ts-option'
        };
      });
    },
    // 获取职称字典
    async handleGetTitle() {
      const res = await getTitle();
      if (res.success == false) {
        this.$message.error(res.message || '获取一二级科室树失败');
        return;
      }
      let find = this.searchList.find(
        item => item.value === 'zhichengmingcheng'
      );
      if (!find) return;

      find.childNodeList = res.object.map(item => {
        return {
          ...item,
          label: item.jobtitleName,
          value: item.jobtitleId,
          element: 'ts-option'
        };
      });

      this.titleList = res.object;
    },
    // 获取人员类别
    async handleDictItemEmployeeStatus() {
      let res = await this.ajax.dictItemList({
        dicTypeId: 'ORG_ATTRIBUTES',
        pageNo: 1,
        pageSize: 999
      });
      if (res.success == false) {
        this.$message.error(res.message || '字典数据获取失败');
        return;
      }

      if (res.rows && res.rows.length) {
        let options = res.rows.map(m => {
          return {
            label: m.itemName,
            value: m.itemNameValue,
            element: 'ts-option'
          };
        });

        let index = this.searchList.findIndex(
          f => f.value == 'personnelCategoryList'
        );
        this.searchList[index].childNodeList = options;
      }
    }
  }
};
