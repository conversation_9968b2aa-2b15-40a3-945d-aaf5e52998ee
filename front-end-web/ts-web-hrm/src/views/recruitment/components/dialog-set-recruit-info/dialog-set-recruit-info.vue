<template>
  <ts-dialog
    class="dialog-set-recruit-info"
    title="简历筛选及面试安排"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="100px">
        <ts-row>
          <ts-col :span="24">
            <ts-form-item
              label="筛选结果"
              prop="conform"
              :rules="rules.required"
            >
              <ts-radio-group v-model="form.conform">
                <ts-radio label="1">通过</ts-radio>
                <ts-radio label="2">不通过</ts-radio>
              </ts-radio-group>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-form-item
            label="应聘岗位"
            prop="interviewJob"
            :rules="rules.required"
          >
            <ts-ztree-select
              ref="treeSelect"
              defaultExpandAll
              :inpText.sync="form.allPath"
              :inpVal.sync="form.interviewJob"
              :data="treeData"
              @change="handleChangeTree"
              @before-change="handleTreeBeforeChange"
            />
          </ts-form-item>
        </ts-row>

        <div class="item-tips">
          <i class="sign"></i>
          <div class="tips-text">面试安排</div>
        </div>

        <ts-row>
          <ts-form-item label="笔试考核人">
            <ts-input v-model="form.writtenName" readonly>
              <template v-slot:suffix>
                <img
                  class="person-icon"
                  src="@/assets/img/defUserPhoto.png"
                  @click="handleOpenSelectPerson('writtenEmpNo')"
                />
              </template>
            </ts-input>
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="笔试时间">
            <ts-date-picker
              style="width: 100%"
              v-model="form.writtenDate"
              :disabledDate="disabledDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="实操考核人">
            <ts-input v-model="form.operationName" readonly>
              <template v-slot:suffix>
                <img
                  class="person-icon"
                  src="@/assets/img/defUserPhoto.png"
                  @click="handleOpenSelectPerson('operationEmpNo')"
                />
              </template>
            </ts-input>
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="实操时间">
            <ts-date-picker
              style="width: 100%"
              v-model="form.operationDate"
              :disabledDate="disabledDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="面试考核人">
            <ts-input v-model="form.interviewName" readonly>
              <template v-slot:suffix>
                <img
                  class="person-icon"
                  src="@/assets/img/defUserPhoto.png"
                  @click="handleOpenSelectPerson('interviewEmpNo')"
                />
              </template>
            </ts-input>
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="面试时间">
            <ts-date-picker
              style="width: 100%"
              v-model="form.interviewDate"
              :disabledDate="disabledDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="备注">
            <ts-input
              v-model="form.remark"
              type="textarea"
              class="textarea"
              maxlength="200"
              show-word-limit
            />
          </ts-form-item>
        </ts-row>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <ts-user-dept-select ref="SelectPerson" @ok="handleOk" />
  </ts-dialog>
</template>

<script>
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';

import { zpglInterviewMessageBatchSave } from '@/api/ajax/NewRecruit/resume.js';
import { zpglInterviewMessageUpdate } from '@/api/ajax/NewRecruit/interview';

export default {
  props: {
    treeData: {
      type: Array
    }
  },
  data() {
    return {
      type: '',
      title: '',
      visible: false,
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      },
      rules: {
        required: { required: true, message: '必填' }
      },
      form: {}
    };
  },
  methods: {
    open({ data, type }) {
      this.form = data;
      this.type = type;

      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
        this.$refs.treeSelect.$forceUpdate();
      });
      this.visible = true;
    },
    handleTreeBeforeChange(e, treeObj, fn) {
      if (e.children && e.children.length > 0) {
        fn(false);
        return false;
      }
    },

    handleChangeTree(e) {
      const paths = e.getPath();
      const names = paths.map(item => item.name) || [];
      let allPath = names.join('>');
      this.form.allPath = allPath;

      // 二级科室id
      const { id: interviewDept, name: interviewDepttext } = e.getParentNode();
      // 三级岗位id
      const { id: interviewJob, name: interviewJobtext } = e;

      this.form.interviewDept = interviewDept;
      this.form.interviewDepttext = interviewDepttext;
      this.form.interviewJob = interviewJob;
      this.form.interviewJobtext = interviewJobtext;
    },

    handleOpenSelectPerson(key) {
      const keyObject = {
        writtenEmpNo: 'writtenName',
        operationEmpNo: 'operationName',
        interviewEmpNo: 'interviewName'
      };
      const valStr = this.form[key];
      const nameStr = this.form[keyObject[key]];
      let empList = [];
      if (valStr && nameStr) {
        let valArr = valStr.split(',');
        let nameArr = nameStr.split(',');

        empList = valArr.map((item, index) => {
          return {
            empCode: item,
            empName: nameArr[index]
          };
        });
      }
      this.$refs.SelectPerson.open(key, {
        appendToBody: true,
        showCheckbox: false,
        title: '选择',
        deptList: [],
        empList,
        isRadio: false
      });
    },
    handleOk(result, key) {
      const keyObject = {
        writtenEmpNo: 'writtenName',
        operationEmpNo: 'operationName',
        interviewEmpNo: 'interviewName'
      };
      const { empList = [] } = result[key];

      const selectCodeArr = empList.map(item => item.empCode);
      const selectNameArr = empList.map(item => item.empName);

      this.form[key] = selectCodeArr.join(',');
      this.form[keyObject[key]] = selectNameArr.join(',');
      this.$forceUpdate();
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);

        let API = null;
        if (this.type === 'set') {
          data.zpglempidList = this.form.zpglempid.split(',') || [];
          API = zpglInterviewMessageBatchSave;
          delete data.zpglempid;
        } else {
          API = zpglInterviewMessageUpdate;
        }
        data.msgInterviewPath = this.form.allPath;

        const res = await API(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('successCallBack');
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-set-recruit-info {
  .content {
    .item-tips {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      .tips-text {
        font-size: 14px;
        font-weight: 500;
      }
      .sign {
        width: 6px;
        height: 18px;
        display: inline-block;
        background: #5260ff;
        margin-right: 8px;
        border-radius: 2px;
      }
    }
    ::v-deep {
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
