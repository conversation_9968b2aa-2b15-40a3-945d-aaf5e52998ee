<template>
  <div class="talen-pool-box">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    />
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :columns="columns"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectionChange"
    />

    <dialog-add-talen-pool
      v-model="dialogAddTalenPool"
      :eachData="eachData"
      :talenPoolList="talenPoolList"
      :formTemplate="formTemplate"
      :successCallBack="handleRefreshTable"
    />

    <component
      :is="seeFormDetailesTemplateName"
      v-model="dialogSeeFormDetailes"
      :eachData="eachData"
    />

    <dialog-setting-talen-pool
      v-model="dialogSettingTalenPool"
      :talenPoolList="talenPoolList"
      :eachData="eachData"
      :selectList="selectList"
      @refresh="handleRefreshTable"
    />

    <dialog-exam-details v-model="dialogExamDetails" :eachData="eachExamData" />
  </div>
</template>

<script>
import DialogAddTalenPool from './components/dialog-add-talen-pool';
import DialogSeeFormDetailsCscjk from '@/views/recruitment/recruitment-plan/components/dialog-see-form-details-cscjk.vue';
import DialogSettingTalenPool from './components/dialog-setting-talen-pool.vue';
import DialogExamDetails from './components/dialog-exam-details.vue';

import table from './mixins/table';

export default {
  components: {
    DialogAddTalenPool,
    DialogExamDetails,
    DialogSeeFormDetailsCscjk,
    DialogSettingTalenPool
  },
  mixins: [table],
  data() {
    return {
      talenPoolList: [],
      selectList: [],

      dialogAddTalenPool: false,
      eachData: {},

      formTemplate: '',
      templateName: '',

      seeFormDetailesTemplateName: '',
      dialogSeeFormDetailes: false,

      eachExamData: {},
      dialogExamDetails: false,

      dialogSettingTalenPool: false
    };
  },
  async mounted() {
    this.handleRefreshTable();
    this.handleGetTalenPoolList();

    const res = await this.ajax.getTemplateName();
    if (res.success && res.statusCode === 200) {
      this.formTemplate = `${res.object.template}-form`;
      this.templateName = res.object.template;
    }
  },
  methods: {
    // 查看详情
    handleDetails(row) {
      this.seeFormDetailesTemplateName = `dialog-see-form-details-${this.templateName}`;

      this.eachData = JSON.parse(row.otherData);
      this.dialogSeeFormDetailes = true;
    },
    // 查看详情
    async handleExamDetails(row) {
      return;
      const { name, identityCard } = row;
      // 后期添加如果没有 planid 则无详情
      const data = {
        name,
        identityCard
      };
      const res = await this.ajax.readSignUpProgress(data);

      if (!res.success) {
        this.$message.error(res.message || '获取考试详情失败!');
        return;
      }

      const exam = res.object.postList.map(item => {
        if (item.examStage) {
          let status =
            item.examStage.exemptionStatus === 2 ? 2 : item.examStage.status;
          return {
            score: item.examStage.score || '-',
            score2: item.examStage.score2 || '-',
            status,
            exemptionStatus: item.examStage.exemptionStatus
          };
        }
      });
      const interview = res.object.postList.map(item => {
        if (item.interviewStage) {
          let status =
            item.interviewStage.exemptionStatus === 2
              ? 2
              : item.interviewStage.status;
          return {
            score: item.interviewStage.score || '-',
            status,
            exemptionStatus: item.interviewStage.exemptionStatus
          };
        }
      });
      const sem = res.object.postList.map(item => {
        if (item.manipulate) {
          let status =
            item.manipulate.exemptionStatus === 2 ? 2 : item.manipulate.status;
          return {
            score: item.manipulate.score || '-',
            status,
            exemptionStatus: item.manipulate.exemptionStatus
          };
        }
      });
      this.eachExamData = {
        name,
        identityCard,
        sem,
        interview,
        exam
      };
      this.dialogExamDetails = true;
    },
    async handleAdd() {
      this.eachData = {};
      this.dialogAddTalenPool = true;
    },
    handleEdit(row) {
      this.eachData = JSON.parse(row.otherData);
      this.eachData.id = row.id;
      this.dialogAddTalenPool = true;
    },
    handleSelectionChange(e) {
      this.selectList = e;
    },
    handleSetting() {
      if (this.selectList.length === 0) {
        this.$message.warning('请至少选择一条数据操作!');
        return;
      }

      this.dialogSettingTalenPool = true;
    },
    async handleEmploymentInformation(row) {
      try {
        await this.$confirm(
          '确定之后人员会录入到员工档案,请及时去人员档案完善信息',
          '提示',
          {
            type: 'warning'
          }
        );
        const res = await this.ajax.talentpoolAddEmployee(row.id);
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }
        this.$message.success('操作成功!');
        this.handleRefreshTable();
      } catch (error) {
        console.error(error);
      }
    },
    handleExport() {
      let data = {
        ...this.searchForm
      };
      let xhr = new XMLHttpRequest();
      let url = '/ts-hrms/talentpool/export';
      xhr.open('post', url, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(JSON.stringify(data));
      xhr.onload = function() {
        if (this.status === 200) {
          let url = window.URL.createObjectURL(new Blob([this.response]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', '人才库导出名单.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      };
    },
    async handleGetTalenPoolList() {
      const res = await this.ajax.getTalenPoolList();
      if (!res.success) {
        this.$message.error(res.message || '获取人才类型字典失败!');
        return;
      }
      this.talenPoolList = res.object.map(item => {
        return {
          ...item,
          label: item.itemName,
          value: item.itemNameValue,
          element: 'ts-option'
        };
      });

      this.searchList.filter(
        item => item.value === 'rclx'
      )[0].childNodeList = this.talenPoolList;
    },
    async handleDel(row) {
      try {
        const { id } = row;

        await this.$confirm('确定要删除该数据吗？', '提示', {
          type: 'warning'
        });

        const res = await this.ajax.talentpoolDelete(id);

        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.handleRefreshTable();
        this.$message.success(res.message || '删除成功');
      } catch (e) {
        console.error(e, '?e');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.talen-pool-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .turn-span {
      margin-right: 8px;
      color: #fff;
      background: $primary-blue;
      padding: 2px 4px;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
</style>
