export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '类型名称',
          value: 'talentPoolName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入类型'
          }
        }
      ],
      columns: [
        {
          prop: 'index',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '全路径',
          prop: 'fullpath',
          align: 'center'
        },
        {
          label: '类型名称',
          prop: 'talentPoolName',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'statusValue',
          align: 'center',
          width: 60,
          formatter: row => {
            return (
              <span class={row.status === 'N' ? 'error-span' : ''}>
                {row.statusValue}
              </span>
            );
          }
        },
        {
          label: '创建人',
          prop: 'createUser',
          width: 80,
          align: 'center'
        },
        {
          label: '创建时间',
          width: 160,
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '备注',
          prop: 'remark',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'left',
          headerSlots: 'action',
          formatter: row => {
            let changeStatusLabel = row.status === 'Y' ? '禁用' : '启用';
            let actionList = [
              {
                label: '编辑',
                event: this.handleOpenEditMoal
              },
              // {
              //   label: '删除',
              //   event: this.handleDelete
              // },
              {
                label: changeStatusLabel,
                event: this.handleChangeStatus
              }
            ];

            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': event => event(row) }}
              />
            );
          }
        }
      ]
    };
  },
  methods: {}
};
