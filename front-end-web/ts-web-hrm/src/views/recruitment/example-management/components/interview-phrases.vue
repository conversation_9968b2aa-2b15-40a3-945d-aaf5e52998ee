<template>
  <div class="interview-phrases-container">
    <el-scrollbar
      v-loading="loading"
      style="flex: 1;"
      wrap-style="overflow-x: hidden;"
    >
      <div
        v-for="(item, index) of phrasesList"
        :key="index"
        class="phrases-item"
      >
        {{ item }}
        <i @click="handleDelWords(item)" class="el-icon-delete"></i>
      </div>
      <ts-button type="primary" @click="handleOpenAddModal">新增</ts-button>
    </el-scrollbar>
    <ts-dialog title="添加常用语" :visible.sync="addModalVisible">
      <ts-form ref="form" :model="form" labelWidth="80px">
        <ts-form-item
          label="标签名称"
          prop="content"
          :rules="{ required: true, message: '必填' }"
        >
          <ts-input
            v-model="form.content"
            clearable
            placeholder="请输入标签名称"
          ></ts-input>
        </ts-form-item>
      </ts-form>
      <template slot="footer">
        <ts-button type="primary" @click="handleSavePreses">确定</ts-button>
        <ts-button @click="addModalVisible = false">取消</ts-button>
      </template>
    </ts-dialog>
  </div>
</template>

<script>
import {
  setWordsList,
  getWordsList,
  wordCancelLabel
} from '@/api/ajax/NewRecruit/template.js';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      phrasesList: [],

      addModalVisible: false,
      loading: false,
      form: {}
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    async refresh() {
      this.loading = true;
      let res = await getWordsList();
      if (res.success == false) {
        this.$message.error(res.message || '数据获取失败');
        return;
      }
      this.phrasesList = res.rows;
      this.loading = false;
    },
    handleOpenAddModal() {
      this.form = {};
      this.addModalVisible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async handleSavePreses() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = deepClone(this.form);
      const res = await setWordsList(data);
      if (res.success && res.statusCode === 200) {
        this.$message.success('操作成功!');
        this.refresh();
        this.form = {};
        this.addModalVisible = false;
      } else {
        this.$message.error(res.message || '操作失败!');
      }
    },
    async handleDelWords(item) {
      const res = await wordCancelLabel({ content: item });
      if (res.success) {
        this.$message.success('操作成功!');
        this.refresh();
        this.form = {};
        this.addModalVisible = false;
      } else {
        this.$message.error(res.message || '操作失败!');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.interview-phrases-container {
  flex: 1;
  display: flex;
  overflow: hidden;

  .phrases-item {
    display: inline-flex;
    align-items: center;
    margin-right: $primary-spacing;
    margin-bottom: $primary-spacing;
    padding: 4px $primary-spacing;
    border: 1px solid $container-border;
    border-radius: $medium-radius;
    .el-icon-delete {
      margin-left: $primary-spacing;
      cursor: pointer;
    }
  }
}
.tips-item {
  margin-left: 80px;
  font-size: 10px;
}
</style>
