<template>
  <div class="pengding-status">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    />

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :propPageSize="40"
      :pageSizes="[20, 40, 60, 80, 100, 200]"
      v-loading="loading"
      :columns="
        columns(true, interviewColumns, {
          label: '面试岗位',
          prop: 'resInterviewPath'
        })
      "
      @refresh="handleRefreshTable"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-interview-evaluation
      ref="DialogInterviewEvaluation"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-add-talent-pool
      ref="DialogAddTalentPool"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogAddTalentPool from '@/views/recruitment/components/dialog-add-talent-pool/dialog-add-talent-pool.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogInterviewEvaluation from '@/views/recruitment/interview-management/component/dialog-interview-evaluation.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';

export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    DialogAdd,
    DialogInterviewEvaluation,
    DialogPersonDetails,
    DialogTrack,
    DialogAddTalentPool
  },
  props: {
    treeData: {
      type: Array
    }
  },
  data() {
    return {
      dialogAddTalentPool: false,
      addTalentPoolDto: {}
    };
  },
  methods: {
    search() {
      this.searchForm.searchStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.searchEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      delete this.searchForm.date;
      this.$parent.search(4, 'pengding');
    },
    handleRefreshTable() {
      this.$parent.handleRefreshTable(4, 'pengding');
    }
  }
};
</script>

<style lang="scss" scoped>
.pengding-status {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
}
</style>
