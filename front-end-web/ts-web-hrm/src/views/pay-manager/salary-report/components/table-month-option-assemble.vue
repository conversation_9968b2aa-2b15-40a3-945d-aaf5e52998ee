<template>
  <div class="table-month-salary-summary">
    <div class="trasen-container">
      <ul class="search-type-tabs">
        <li
          :class="{
            'search-item': 'true',
            active: item.value == searchForm.reportId
          }"
          v-for="(item, index) in optionsArr"
          @click="handleClickSearchTabs(item)"
          :key="index"
        >
          {{ item.label }}
        </li>
      </ul>

      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :resetData="resetData"
        :elementCol="14"
        @search="search"
      >
        <template slot="right">
          <ts-button type="primary" @click="handleExport">
            导 出
          </ts-button>
        </template>
      </ts-search-bar>

      <!-- <base-table
        ref="table"
        class="form-table"
        border
        stripe
        :columns="columns"
        @refresh="handleRefreshTable"
      /> -->
      <TsVxeTemplateTable
        id="table-month-option-assemble"
        class="form-table"
        ref="table"
        :hasPage="false"
        :columns="columns"
        :span-method="colspanMethod"
        @refresh="handleRefreshTable"
      />
    </div>
  </div>
</template>

<script>
import searchList from '../mixins/searchList';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  components: {},
  mixins: [searchList],
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      operationkeys: ['reportId'],
      searchList: [
        {
          label: '算薪周期',
          value: 'payrollDate',
          element: 'ts-month-picker',
          elementProp: {
            placeholder: '请选择月份',
            valueFormat: 'YYYY-MM',
            allowClear: false
          },
          event: {
            change: e => {
              this.$set(this.searchForm, 'payrollDate', e);
              this.search();
            }
          }
        }
      ],
      optionsArr: [],
      tableData: [],
      columns: [],

      personInformation: [],
      salaryItem: []
    };
  },
  methods: {
    colspanMethod({ _rowIndex, _columnIndex }) {
      if (_rowIndex === this.tableData.length - 1) {
        if (_columnIndex === 0) {
          return { rowspan: 0, colspan: 0 };
        } else if (_columnIndex === 1) {
          return { rowspan: 1, colspan: 4 };
        } else if (_columnIndex === 2 || _columnIndex === 3) {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },

    handleClickSearchTabs(item) {
      this.$refs.table.pageNo = 1;
      this.searchForm.reportId = item.value;
      this.resetData.reportId = item.value;
    },

    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      await this.handleGetReportsList();

      this.handleGetTableHead();
      this.handleGetTableData();
    },
    //获取薪酬方案配置报表
    async handleGetReportsList() {
      const res = await this.ajax.reportsList({
        sidx: 'create_date',
        sord: 'desc',
        reportsType: '2',
        pageNo: 1,
        pageSize: 999
      });
      let data = deepClone(res.rows || []);
      this.optionsArr = data.map(m => {
        return {
          label: m.reportName,
          value: m.id
        };
      });

      if (this.optionsArr && this.optionsArr.length) {
        if (!this.searchForm.reportId) {
          this.$set(this.searchForm, 'reportId', this.optionsArr[0].value);
        }
      }
    },

    //导出
    handleExport() {
      let resultForm = deepClone(this.handleGetSearchFromData());
      let pageNo = 1,
        pageSize = 9999999,
        formData = {
          pageNo,
          pageSize,
          payrollDate: resultForm.payrollDate,
          reportId: resultForm.reportId
        };

      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.id = 'down-file-iframe';

      const form = document.createElement('form');
      form.method = 'get';
      form.target = iframe.id;
      form.action =
        '/ts-hrms/api/newsalaryReportsStatistics/salaryOptionExport';

      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        } else {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = formData[key];
          form.appendChild(input);
        }
      });

      iframe.appendChild(form);
      document.body.appendChild(iframe);

      form.submit();
      document.body.removeChild(iframe);

      this.exportLoading = true;
      setTimeout(() => {
        this.exportLoading = false;
      }, 1500);
    },
    //获取表头
    async handleGetTableHead() {
      let res = await this.ajax.salaryOptionTotalTitle(
        this.searchForm.reportId
      );
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      this.columns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        }
      ];
      if (res.object.length) {
        this.columns.push(...res.object);
      }
    },

    //查询列表数据
    async handleGetTableData() {
      let resultForm = deepClone(this.handleGetSearchFromData());
      resultForm.startDate = resultForm.payrollDate;
      resultForm.endDate = resultForm.payrollDate;
      let pageNo = 1,
        pageSize = 9999999,
        searchForm = {
          pageNo,
          pageSize,
          ...resultForm
        };

      let res = await this.ajax.salaryOptionStatisticsData(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.tableData = deepClone(rows);
      this.$refs.table.refresh({
        ...res,
        rows
      });

      let pageDom = document.querySelectorAll('.table-month-salary-summary');
      if (pageDom && pageDom.length) {
        let paginationDom = pageDom[0].querySelectorAll('.el-pagination');
        if (paginationDom[0]) paginationDom[0].style = 'visibility: hidden;';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.table-month-salary-summary {
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
  .filterate {
    height: 45px;
  }
  .trasen-container {
    padding-top: 0px !important;
    position: relative;
    overflow: hidden;

    flex: 1;
    display: flex;
    flex-direction: column;

    .search-type-tabs {
      display: flex;
      margin-bottom: 8px;
      .search-item {
        cursor: pointer;
        margin-right: 8px;
        border-radius: 4px;
        border: 2px solid #e1e1e1;
        padding: 8px 12px;
        font-weight: 800;
        line-height: 32px;
        height: 32px;
        &.active {
          color: $primary-blue;
          border: 2px solid $primary-blue;
        }
      }
    }

    .footer_total {
      position: absolute;
      left: 8px;
      bottom: 8px;
      z-index: 9999;
    }

    ::v-deep {
      .form-table {
        flex: 1;
        overflow: hidden;
        transform: scale(1);
        .details-span {
          color: $primary-blue;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
