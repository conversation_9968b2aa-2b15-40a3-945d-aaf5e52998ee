<template>
  <ts-dialog
    custom-class="dialog-export-field"
    append-to-body
    :visible.sync="visible"
    title="选择导出字段"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="0px">
        <ts-row class="border-box">
          <span class="item-title">人事信息:</span>
          <div class="item-box">
            <ts-form-item class="day-checkbox" label="">
              <ts-checkbox-group v-model="form.personInformationVal">
                <ts-checkbox
                  v-for="item in personInformation"
                  :key="item.id"
                  :label="item.id"
                >
                  {{ item.label }}
                </ts-checkbox>
              </ts-checkbox-group>
            </ts-form-item>
          </div>
        </ts-row>

        <ts-row class="border-box">
          <span class="item-title">工资项:</span>
          <div class="item-box">
            <ts-form-item class="day-checkbox" label="">
              <ts-checkbox-group v-model="form.salaryItemVal">
                <ts-checkbox
                  v-for="item in salaryItem"
                  :key="item.id"
                  :label="item.id"
                >
                  {{ item.label }}
                </ts-checkbox>
              </ts-checkbox-group>
            </ts-form-item>
          </div>
        </ts-row>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <div>
        <ts-button @click="handleOperateSetChecked">
          {{ isAll ? '取消全选' : '全 选' }}
        </ts-button>
      </div>
      <div>
        <!-- <ts-button @click="handleSaveTemplate">
          {{ type == 'add' ? '保存为导出模板' : '编辑' }}
        </ts-button> -->
        <ts-button type="primary" @click="submit">导 出</ts-button>
        <ts-button @click="close">关 闭</ts-button>
      </div>
    </span>

    <dialog-add-export-template
      ref="DialogAddExportTemplate"
      @success="successClose"
    />
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import DialogAddExportTemplate from './dialog-add-export-template.vue';
export default {
  components: {
    DialogAddExportTemplate
  },
  props: {
    filterExportId: {
      type: String
    },
    personInformation: {
      type: Array,
      default: () => []
    },
    salaryItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,

      type: '',
      details: null,
      form: {}
    };
  },
  computed: {
    isAll() {
      return (
        this.form.personInformationVal?.length ==
          this.personInformation?.length &&
        this.form.salaryItemVal?.length == this.salaryItem?.length
      );
    }
  },
  methods: {
    async open({ type, data }) {
      this.details = deepClone(data);
      this.type = type;

      this.$set(this, 'form', {
        personInformationVal: [],
        salaryItemVal: []
      });

      if (this.type == 'edit') {
        let personInformationIds = this.personInformation.map(m => m.id);
        let salaryItemIds = this.salaryItem.map(m => m.id);
        let ids = (this.details.conditionMap || '').split(',');
        ids.forEach(f => {
          if (personInformationIds.includes(f)) {
            this.form.personInformationVal.push(f);
          }
          if (salaryItemIds.includes(f)) {
            this.form.salaryItemVal.push(f);
          }
        });
      }

      this.visible = true;

      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },

    // 全选 反选
    handleOperateSetChecked() {
      if (this.isAll) {
        this.form.personInformationVal = [];
        this.form.salaryItemVal = [];
      } else {
        this.form.personInformationVal = this.personInformation.map(m => m.id);
        this.form.salaryItemVal = this.salaryItem.map(m => m.id);
      }
    },

    // 保存导出模版/编辑
    handleSaveTemplate() {
      let selectIds = [
        ...this.form.personInformationVal,
        ...this.form.salaryItemVal
      ];
      if (selectIds.length === 0) {
        this.$message.warning('请选择导出项!');
        return;
      }

      let data = {
        details: this.type == 'add' ? {} : this.details,
        type: this.type,
        filterExportId: this.filterExportId,
        selectIds
      };
      this.$refs.DialogAddExportTemplate.open(data);
    },

    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        let { salaryItemVal, personInformationVal } = deepClone(this.form);

        let exportJsonArrs = [...salaryItemVal, ...personInformationVal];
        if (exportJsonArrs.length === 0) {
          this.$message.warning('请选择导出项!');
          return;
        }

        let exportJson = exportJsonArrs.join(',');

        this.submitLoading = true;
        await this.$parent.handleExport(exportJson);
        this.submitLoading = false;
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.form.personInformationVal = [];
      this.form.salaryItemVal = [];
      this.visible = false;
    },

    successClose() {
      this.$emit('refreshExportFilter');
      this.close();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-export-field {
    width: 900px !important;
    height: 750px;
    .el-dialog__body {
      height: calc(100% - 88px);
      overflow: auto;
      .border-box {
        padding: 16px;
        margin-bottom: 16px;
        border: 1px solid #ccc;
        position: relative;
        .item-title {
          position: absolute;
          left: 12px;
          top: -10px;
          background: #fff;
          padding: 0 8px;
        }
      }
    }

    .dialog-footer {
      display: flex;
      justify-content: space-between;
    }
  }

  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }

  .day-checkbox {
    .el-checkbox {
      width: 120px !important;
      margin-bottom: 4px;
    }
  }

  .item-box {
    margin-bottom: 8px;
  }
}
</style>
