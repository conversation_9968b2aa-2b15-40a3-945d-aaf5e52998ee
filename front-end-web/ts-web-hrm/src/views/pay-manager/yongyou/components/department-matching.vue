<!-- 科室匹配 -->
<template>
  <div class="trasen-container flex-column">
    <div class="matching">
      <base-table
        ref="tableLeft"
        class="form-table-left"
        border
        :columns="columnsLeft"
        @refresh="handleRefreshTableLeft"
        highlight-current-row
        @current-change="handleSelectionChangeLeft"
        :hideSelectAll="true"
      />
      <div class="btns">
        <div style="margin:auto;text-align:center;">
          <ts-button
            type="primary"
            class="el_Btns"
            style="margin-left:10px"
            @click="cupi"
            :loading="loading"
          >
            粗匹
          </ts-button>
          <ts-button
            type="primary"
            class="el_Btns"
            :loading="loading"
            @click="createPi"
          >
            建立匹配
          </ts-button>
          <ts-button
            type="primary"
            class="el_Btns"
            :loading="loading"
            @click="cancelPi"
          >
            取消匹配
          </ts-button>
        </div>
      </div>
      <base-table
        ref="tableRight"
        class="form-table-right"
        border
        :hasPage="false"
        :columns="columnsRight"
        @refresh="handleRefreshTableRight"
        highlight-current-row
        @current-change="handleSelectionChangeRight"
      />
    </div>
    <base-table
      ref="tableBottom"
      class="form-table-bottom"
      border
      :columns="columnsBottom"
      @refresh="handleRefreshTableBottom"
      highlight-current-row
      @current-change="handleSelectionChangeBottom"
    />
  </div>
</template>

<script>
import departmentTable from '../mixins/departmentTable';
export default {
  mixins: [departmentTable],
  data() {
    return {
      loading: false,
      selectLeft: null,
      selectRight: null,
      selectBottom: null
    };
  },
  methods: {
    refresh() {
      this.handleRefreshTableLeft();
      this.handleRefreshTableRight();
      this.handleRefreshTableBottom();
    },
    handleSelectionChangeLeft(e) {
      this.selectLeft = e;
    },
    handleSelectionChangeRight(e) {
      this.selectRight = e;
    },
    handleSelectionChangeBottom(e) {
      this.selectBottom = e;
    },
    async cupi() {
      this.loading = true;
      let res = await this.ajax.roughMatch();
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      } else {
        this.$message.success(res.message || '粗匹成功');
      }
    },
    async createPi() {
      if (!this.selectLeft) {
        this.$message.warning('请选择人事科室');
        return;
      }
      if (!this.selectRight) {
        this.$message.warning('请选择财务科室');
        return;
      }
      let param = {
        hrOrgCode: this.selectLeft.hrOrgCode,
        hrOrgName: this.selectLeft.hrOrgName,
        financeOrgCode: this.selectRight.orgCode,
        financeOrgName: this.selectRight.orgName
      };
      this.loading = true;
      let res = await this.ajax.createMatch(param);
      this.loading = false;
      this.selectLeft = null;
      this.selectRight = null;
      if (res.success == false) {
        this.$message.error(res.message || '匹配失败');
        return;
      } else {
        this.$message.success('匹配成功');
        this.$refs.tableLeft.pageNo = 1;
        this.$refs.tableBottom.pageNo = 1;
        this.handleRefreshTableLeft();
        this.handleRefreshTableBottom();
      }
    },
    async cancelPi() {
      if (!this.selectBottom) {
        this.$message.warning('请选择匹配记录');
        return;
      }
      let param = {
        id: this.selectBottom.id
      };
      this.loading = true;
      let res = await this.ajax.cancelMatch(param);
      this.loading = false;
      this.selectBottom = null;
      if (res.success == false) {
        this.$message.error(res.message || '取消匹配失败');
        return;
      } else {
        this.$message.success('取消匹配成功');
        this.$refs.tableLeft.pageNo = 1;
        this.$refs.tableBottom.pageNo = 1;
        this.handleRefreshTableLeft();
        this.handleRefreshTableBottom();
      }
    },
    async handleRefreshTableLeft() {
      let pageNo = this.$refs.tableLeft.pageNo,
        pageSize = this.$refs.tableLeft.pageSize,
        searchForm = {
          pageNo,
          pageSize
        };
      let res = await this.ajax.findHrOrgPageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.tableLeft.refresh({
        ...res,
        rows
      });
    },
    async handleRefreshTableRight() {
      let pageNo = this.$refs.tableRight.pageNo,
        pageSize = this.$refs.tableRight.pageSize,
        searchForm = {
          pageNo,
          pageSize
        };
      let res = await this.ajax.findFinanceOrgList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.object.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.tableRight.refresh({
        ...res,
        rows
      });
    },
    async handleRefreshTableBottom() {
      let pageNo = this.$refs.tableBottom.pageNo,
        pageSize = this.$refs.tableBottom.pageSize,
        searchForm = {
          pageNo,
          pageSize
        };
      let res = await this.ajax.findDtoPageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.tableBottom.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  height: calc(100% - 44px) !important;
  .matching {
    display: flex;
    height: 50%;
    /deep/ .form-table-left + .form-table-right {
      width: 40%;
      padding: 10px;
    }
    /deep/ .form-table-right {
      max-height: 100%;
      padding-bottom: 10px;
    }
    .btns {
      width: 20%;
      display: flex;
      flex-direction: column;
      .el_Btns {
        width: 60%;
        margin-bottom: 10px;
        background: #5260ff;
        border-color: #5260ff;
      }
    }
  }
}
</style>
