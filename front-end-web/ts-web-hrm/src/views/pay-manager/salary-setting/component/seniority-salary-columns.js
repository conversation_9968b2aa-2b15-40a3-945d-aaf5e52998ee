export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      actions: [],
      searchList: [
        {
          label: '工龄',
          value: 'years',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 60
        },
        {
          label: '工龄',
          prop: 'years',
          align: 'center'
        },
        {
          label: '工龄工资',
          prop: 'salary',
          align: 'center'
        },
        {
          label: '创建人',
          prop: 'createUserName',
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'isEnable',
          width: 70,
          align: 'center',
          render: (h, { row }) => {
            let status = row.isEnable == '2' ? 'red' : '';
            return h('div', { class: status }, row.isEnableLabel);
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 130,
          headerSlots: 'action',
          render: (h, { row }) => {
            let changeStatusLabel = row.isEnable == '1' ? '禁用' : '启用';
            let operateType = row.isEnable == '1' ? 'disEnable' : 'enable';

            let actionList = [
              {
                label: changeStatusLabel,
                event: () => {
                  this.handleSelectionOpetate('single', operateType, row.id);
                }
              },
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '详情',
                event: this.handleRowDetails
              }
            ];

            // row.isEnable == '2' &&
            //   actionList.push({
            //     label: '删除',
            //     event: this.handleDelete,
            //     className: 'red'
            //   });

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  }
};
