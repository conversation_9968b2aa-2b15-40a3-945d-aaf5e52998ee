<template>
  <div class="table-container" id="PostWagesTable">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="right">
        <ts-button
          type="primary"
          @click="handleSavePostWages()"
          v-if="this.hasOperateBtn.includes('batchAdd')"
        >
          岗位工资录入
        </ts-button>
        <ts-button
          type="primary"
          @click="handleImport()"
          v-if="this.hasOperateBtn.includes('import')"
        >
          导入
        </ts-button>
        <ts-button
          v-if="!this.isAll"
          type="primary"
          @click="handleShowAll('true')"
        >
          展开全部
        </ts-button>
        <ts-button
          v-if="this.isAll"
          type="primary"
          @click="handleShowAll('false')"
        >
          收起全部
        </ts-button>
        <!-- <ts-button
          type="primary"
          @click="handleSelectionOpetate('selection', '1')"
        >
          启用
        </ts-button>
        <ts-button
          type="primary"
          @click="handleSelectionOpetate('selection', '2')"
        >
          禁用
        </ts-button> -->
      </template>
    </ts-search-bar>

    <TsVxeTemplateTable
      id="table_post-wages-"
      class="form-table"
      ref="table"
      :columns="columns1"
      @selection-change="handleSelectionChange"
      @refresh="handleRefreshTable"
    />
    <dialog-setting-detail ref="DialogSettingDetail" />

    <post-wages-edit-dialog
      ref="postWagesEditDialog"
      @refresh="handleRefreshTable"
    />

    <!-- 公用导入 -->
    <base-import
      ref="basePostWagesImport"
      :ImportConfiguration="ImportConfiguration"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import { deepClone, inputTowDecimalPlaces } from '@/unit/commonHandle.js';
import postWagesColumns from './post-wages-columns';
import dialogSettingDetail from '@/views/pay-manager/components/dialog-setting-detail.vue';
import baseImport from '@/components/base-import/index.vue';
import postWagesEditDialog from './post-wages-edit-dialog.vue';
export default {
  props: {
    menuLimits: {
      type: Array,
      default: () => []
    }
  },
  mixins: [postWagesColumns],
  components: {
    dialogSettingDetail,
    postWagesEditDialog,
    baseImport
  },
  data() {
    return {
      multipleSelection: [],
      isAll: false,
      ImportConfiguration: {
        importTempalteApi: '/ts-basics-bottom/postWage/downloadTemplate',
        importTempalteName: '岗位工资导入模板',
        importApi: 'importPostWagesData'
      }
    };
  },
  created() {
    this.getHrmsSalaryPolicyStandardData();
    this.handleGetPostCategoryList();
  },
  computed: {
    hasOperateBtn() {
      return this.menuLimits.map(m => m.resourceId);
    }
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    async handleSelectionOpetate(operateType, enable, postWageId = '') {
      if (!this.multipleSelection.length && operateType == 'selection') {
        this.$message.warning('请选择要操作的数据!');
        return false;
      }

      let dic = {
          '1': '启用',
          '2': '禁用'
        },
        title = dic[enable];

      try {
        await this.$confirm(`您正在做${title}操作，是否确定？`, '提示', {
          type: 'warning'
        });

        let ids = [];
        if (operateType == 'selection') {
          ids = this.multipleSelection.map(item => item.postWageId);
        } else {
          ids.push(postWageId);
        }

        const res = await this.ajax.postWageBatchEnable(enable, ids);
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }
        this.$message.success('操作成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e, '?e');
      }
    },

    async handleGetPostCategoryList() {
      let res = await this.ajax.postCategoryGetList();

      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      if (res.object.length) {
        let rows = res.object.filter(f => f.isEnable == '1');
        this.searchList.find(
          f => f.value == 'postCategory'
        ).childNodeList = rows.map(f => {
          return {
            ...f,
            value: f.postCategoryId,
            label: f.postCategoryName,
            element: 'ts-option'
          };
        });
      }
    },

    handleKeyupBlurTargetInput(e) {
      if (e.target) e.target.blur();
    },

    handleTargetContainerClick(event, row, className, editKey) {
      row[editKey] = true;

      const clickedElement = event.target;
      const parentElement = clickedElement.closest(className);
      this.$nextTick(() => {
        if (
          !parentElement.hasChildNodes() ||
          !parentElement.childNodes.length
        ) {
          return false;
        }

        var _inputDivNode = parentElement.childNodes[0];
        if (_inputDivNode.tagName != 'DIV') return false;

        let _inputNode = _inputDivNode.firstElementChild;
        if (_inputNode.tagName === 'INPUT') _inputNode.focus();
      });
    },

    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      obj[attr] = newVal;
    },

    async handleBlurTargetInput(row, key, localKey, editKey) {
      let value = parseFloat(row[key]);
      row[key] = isNaN(value) ? '' : value;

      if (row[key] === '') {
        this.$message.error('修改失败,数据不能为空!');
        row[editKey] = false;
        row[key] = row[localKey];
        return false;
      }

      try {
        await this.$confirm('确定要修改该条数据吗？', '提示', {
          type: 'warning'
        });
        let data = deepClone(row);
        const res = await this.ajax.payPostWageUpdate(data);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
          row[editKey] = false;
          row[key] = row[localKey];
          return false;
        }
      } catch (error) {
        if (error === 'cancel') {
          row[editKey] = false;
          row[key] = row[localKey];
        }
      }
    },

    handleRowDetails(data) {
      let openData = {
        data,
        title: '详情',
        detailLabelKey: [
          {
            label: '岗位类别',
            key: 'postCategoryName'
          },
          {
            label: '岗位等级',
            key: 'postName'
          },
          {
            label: '岗位工资',
            key: 'postWage'
          },
          {
            label: '状态',
            key: 'isEnableLabel'
          }
        ],
        tableDataLabelKey: [
          {
            label: '岗位类别',
            key: 'postCategoryName'
          },
          {
            label: '岗位等级',
            key: 'postName'
          },
          {
            label: '岗位工资',
            key: 'postWage'
          },
          {
            label: '状态',
            key: 'isEnable'
          }
        ],
        param: {
          tableName: 'comm_post_wage',
          idKey: 'postWageId'
        }
      };
      this.$refs.DialogSettingDetail.open(openData);
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      let res = await this.ajax.payPostWageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          postWageIsEdit: false,
          performanceWageIsEdit: false,
          awardWageIsEdit: false,
          localPostWage: item.postWage,
          localPerformanceWage: item.performanceWage,
          localAwardWage: item.awardWage,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    //岗位工资录入
    handleSavePostWages() {
      this.$refs.postWagesEditDialog.open({
        title: '岗位工资录入',
        policyStandardData: this.policyStandardData
      });
    },
    //导入
    handleImport() {
      this.$refs.basePostWagesImport.open({
        title: '岗位工资导入',
        quantity: false
      });
    },
    //是否显示全部政策标准
    handleShowAll(isAll) {
      if (isAll == 'true') {
        this.isAll = true;
      } else {
        this.isAll = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .red {
        color: red;
      }

      .details-span {
        color: $primary-blue;
        cursor: pointer;
      }

      .operation-span {
        color: $primary-blue;
        margin-right: 8px;
        cursor: pointer;
      }
    }
  }
}
</style>
