<template>
  <ts-dialog
    custom-class="dialog-setting-relationship"
    width="820px"
    append-to-body
    :visible.sync="visible"
    :title="title"
    @close="close"
  >
    <div class="content flex">
      <left-select-ztree v-model="leftCheck" ref="leftSelectZtree" />
      <right-select-ztree v-model="rightChecks" ref="rightSelectZtree" />
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import LeftSelectZtree from './left-select-ztree.vue';
import rightSelectZtree from './right-select-ztree.vue';
export default {
  components: {
    LeftSelectZtree,
    rightSelectZtree
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      leftCheck: {},
      rightChecks: [],

      treeData: []
    };
  },
  watch: {
    leftCheck: {
      handler({ timekeeperId, timekeeperName }) {
        if (!timekeeperId || !timekeeperName) {
          return false;
        }
        this.$refs.rightSelectZtree.handleSearchRelationship(timekeeperId);
      },
      deep: true
    }
  },
  methods: {
    async open({ data = {}, type, title }) {
      this.title = title;
      this.type = type;

      await this.getTreeData();
      this.visible = true;

      this.$nextTick(() => {
        this.$refs.leftSelectZtree.init(this.treeData);
        this.$refs.rightSelectZtree.init(this.treeData);
      });
    },

    async getTreeData() {
      let res = await this.ajax.getTreeOrgAndEmp();
      if (!res.success) {
        this.$message.error('获取上报人员失败!');
        return;
      }

      if (res.object) {
        let data = JSON.parse(res.object);
        let formatterTree = this.handleFormatterJson(data);
        this.treeData = [formatterTree];
      }
    },

    handleFormatterJson(object) {
      formatter(object);
      function formatter(obj) {
        obj.pid = obj.parentId;
        obj.nocheck = false;
        if (obj.type === 1) {
          if (obj.children && obj.children.length) {
            obj.children.forEach(item => {
              formatter(item);
            });
          }
        } else {
          delete obj.children;
        }
      }
      return object;
    },

    async submit() {
      let { timekeeperId, timekeeperName } = this.leftCheck;
      if (!timekeeperId || !timekeeperName) {
        this.$message.warning('请先选择左侧科室下的人员再进行操作!');
        return false;
      }
      if (!this.rightChecks.length) {
        this.$message.warning('请选择被上报人员!');
        return false;
      }
      let list = [];
      for (let i = 0; i < this.rightChecks.length; i++) {
        let { employeeId, employeeName } = this.rightChecks[i];
        list.push({
          uploadType: '1',
          timekeeperId,
          employeeId,
          employeeName
        });
      }

      const res = await this.ajax.timekeeperSave(list);
      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refresh');
    },

    close() {
      this.title = '';
      this.type = '';
      this.leftCheck = {};
      this.rightChecks = [];

      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-setting-relationship {
    height: 620px;

    .el-dialog__body {
      height: calc(100% - 88px);

      .content {
        height: 100%;

        .left-tree-container,
        .right-tree-container {
          flex: 1;
          border: 1px solid #eee;
          border-radius: 4px;
          padding: 8px;
          .tree-scrollbar {
            margin: 8px 0;
          }
        }

        .left-tree-container {
          margin-right: 8px;
        }

        .textarea {
          .el-textarea__inner {
            min-height: 110px !important;
            max-height: 200px !important;
          }
        }
      }
    }
  }
}
</style>
