<template>
  <vxe-modal
    width="800"
    :title="title"
    v-model="visible"
    showFooter
    :before-hide-method="close"
    className="dialog-add-promotion"
  >
    <template #default>
      <div class="content">
        <ts-form
          class="ts-form-container"
          ref="form"
          :model="form"
          labelWidth="155px"
        >
          <div class="box-item-container">
            <div class="head flex-space">
              <span class="head-title">
                晋升说明
              </span>
            </div>
            <div class="item-content">
              <ts-row>
                <el-col :span="12">
                  <ts-form-item
                    label="晋升人员"
                    prop="employeeNo"
                    :rules="rules.required"
                  >
                    <base-select
                      style="width: 100%;"
                      v-model="form.employeeNo"
                      :inputText.sync="form.employeeName"
                      :data="personList"
                      label="name"
                      value="employeeNo"
                      searchInputName="employeeName"
                      :disabled="type === 'edit' || disabled"
                      @select="handleSelectPerson"
                    />
                  </ts-form-item>
                </el-col>
                <el-col :span="12">
                  <ts-form-item
                    label="生效日期"
                    prop="effectiveDate"
                    :rules="rules.required"
                  >
                    <ts-date-picker
                      style="width: 100%"
                      v-model="form.effectiveDate"
                      :disabledDate="disabledDate"
                      valueFormat="YYYY-MM-DD"
                      :disabled="type === 'edit' || disabled"
                      placeholder="请选择"
                    />
                  </ts-form-item>
                </el-col>
              </ts-row>
              <ts-row>
                <el-col :span="12">
                  <ts-form-item
                    label="任职时间"
                    prop="jobDeionTypeTime"
                    :rules="rules.required"
                  >
                    <ts-month-picker
                      style="width: 100%"
                      value-format="yyyy-MM"
                      placeholder="请选择月份"
                      :disabled="disabled"
                      v-model="form.jobDeionTypeTime"
                    />
                  </ts-form-item>
                </el-col>
                <el-col :span="12">
                  <ts-form-item
                    label="晋升原因"
                    prop="reason"
                    :rules="rules.required"
                  >
                    <ts-input
                      style="width: 100%"
                      v-model="form.reason"
                      :disabled="disabled"
                      placeholder="请输入"
                    />
                  </ts-form-item>
                </el-col>
              </ts-row>
              <ts-form-item label="备注">
                <ts-input
                  v-model="form.remark"
                  class="textarea"
                  type="textarea"
                  resize="none"
                  :disabled="disabled"
                  maxlength="300"
                  show-word-limit
                />
              </ts-form-item>
            </div>
          </div>
          <div class="box-item-container require-form">
            <div class="head">
              <span class="head-title">
                晋升项目
              </span>
            </div>
            <div class="item-content">
              <div class="flex-col-center title-head-table">
                <div class="title">薪酬项目</div>
                <div class="left">
                  调整前
                </div>
                <div class="right">调整后</div>
              </div>
              <el-scrollbar
                class="flex-column"
                style="flex: 1;"
                ref="scrollbar"
                wrap-style="overflow-x: hidden;"
              >
                <ts-form-item
                  class="histroy-form-item"
                  label="岗位类别"
                  prop="newPlgw"
                  :rules="rules.required"
                >
                  <template v-slot:error>
                    <div class="change-error">
                      必填
                    </div>
                  </template>
                  <ts-col class="last-time-container" :span="12">
                    <ts-input
                      style="width: 155px"
                      v-model="form.oldPlgwText"
                      disabled
                    />
                  </ts-col>
                  <ts-col :span="12" class="now-time-container">
                    <!-- 岗位类别 -->
                    <ts-select
                      style="width: 155px"
                      @change="handleChangePostCategory"
                      v-model="form.newPlgw"
                      :disabled="disabled"
                      clearable
                    >
                      <ts-option
                        v-for="item in postCategoryList"
                        :key="item.postCategoryId"
                        :label="item.postCategoryName"
                        :value="item.postCategoryId"
                      />
                    </ts-select>
                  </ts-col>
                </ts-form-item>
                <ts-form-item
                  class="histroy-form-item"
                  label="岗位等级"
                  prop="newGwdj"
                  :rules="rules.required"
                >
                  <template v-slot:error>
                    <div class="change-error">
                      必填
                    </div>
                  </template>
                  <ts-col class="last-time-container" :span="12">
                    <ts-input
                      style="width: 155px"
                      v-model="form.oldGwdjText"
                      disabled
                    />
                  </ts-col>
                  <ts-col :span="12" class="now-time-container">
                    <!-- 岗位等级 -->
                    <ts-select
                      style="width: 155px"
                      v-model="form.newGwdj"
                      :disabled="disabled"
                      clearable
                    >
                      <ts-option
                        v-for="item of postLevelList"
                        :key="item.postId"
                        :label="item.label"
                        :value="item.postId"
                      />
                    </ts-select>
                  </ts-col>
                </ts-form-item>
                <ts-form-item
                  class="histroy-form-item"
                  label="薪级类别"
                  prop="newSalaryLevelType"
                  :rules="rules.required"
                >
                  <template v-slot:error>
                    <div class="change-error">
                      必填
                    </div>
                  </template>
                  <ts-col class="last-time-container" :span="12">
                    <ts-input
                      style="width: 155px"
                      v-model="form.oldSalaryLevelTypeText"
                      disabled
                    />
                  </ts-col>
                  <ts-col :span="12" class="now-time-container">
                    <!-- 薪级类别 -->
                    <ts-select
                      style="width: 155px"
                      v-model="form.newSalaryLevelType"
                      :disabled="disabled"
                      @change="handleChangeSalaryCategory"
                      clearable
                    >
                      <ts-option
                        v-for="item in salaryCategoryList"
                        :key="item.dictValue"
                        :label="item.dictName"
                        :value="item.dictValue"
                      />
                    </ts-select>
                  </ts-col>
                </ts-form-item>
                <ts-form-item
                  class="histroy-form-item"
                  label="薪级等级"
                  prop="newSalaryLevelId"
                  :rules="rules.required"
                >
                  <template v-slot:error>
                    <div class="change-error">
                      必填
                    </div>
                  </template>
                  <ts-col class="last-time-container" :span="12">
                    <ts-input
                      style="width: 155px"
                      v-model="form.oldSalaryLevelIdText"
                      disabled
                    />
                  </ts-col>
                  <ts-col :span="12" class="now-time-container">
                    <ts-select
                      style="width: 155px"
                      v-model="form.newSalaryLevelId"
                      :disabled="disabled"
                      clearable
                    >
                      <ts-option
                        v-for="item of salaryLevelList"
                        :key="item.salaryLevelId"
                        :label="item.salaryLevelName"
                        :value="item.salaryLevelId"
                      />
                    </ts-select>
                  </ts-col>
                </ts-form-item>
              </el-scrollbar>
            </div>
          </div>
        </ts-form>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button
          type="primary"
          v-if="!disabled"
          :loading="submitLoading"
          @click="submit"
        >
          提 交
        </ts-button>
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  components: {},
  data() {
    return {
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      },
      submitLoading: false,
      visible: false,
      title: '',
      type: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      salaryCategoryList: [],
      postCategoryList: [],
      postLevelList: [],
      salaryLevelList: [],
      personList: [],
      disabled: false
    };
  },
  methods: {
    async open({ data, title, type }) {
      this.title = title;
      this.type = type;

      await this.handleGetPostCategoryList();
      await this.handleGetSalaryCategoryList();
      await this.getSelectPersonList();

      this.$set(this, 'form', {
        jobDeionTypeTime: this.$dayjs().format('YYYY-MM-DD') || ''
      });
      if (this.type === 'edit' || this.type === 'detail') {
        data.newPlgw && this.handleChangePostCategory(data.newPlgw);
        data.newSalaryLevelType &&
          this.handleChangeSalaryCategory(data.newSalaryLevelType);
        this.$set(this, 'form', deepClone(data));
      }
      if (this.type == 'detail') {
        this.disabled = true;
      }
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    async getSelectPersonList(data) {
      let res = await this.ajax.employeeGetMyEmployeeList();
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }

      let list = deepClone(res.object || []);
      this.personList = list.map(m => {
        let orgName = m.orgName ? `-${m.orgName}` : '';
        return {
          ...m,
          name: m.employeeName + orgName
        };
      });
    },

    async handleSelectPerson({
      employeeId = '',
      employeeNo = '',
      employeeName = ''
    }) {
      this.$set(this.form, 'employeeId', employeeId || '');
      this.$set(this.form, 'employeeNo', employeeNo || '');
      this.$set(this.form, 'employeeName', employeeName || '');

      let formEmployeeId = this.form.employeeId;

      if (!formEmployeeId) {
        this.$message.warning('查询员工id为空，请联系管理员!');
        return false;
      }

      let res = await this.ajax.advancementIncidentGetDataByEmployeeId(
        formEmployeeId
      );
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }

      let field = [
        'oldGwdj',
        'oldGwdjText',
        'oldPlgw',
        'oldPlgwText',
        'oldSalaryLevelId',
        'oldSalaryLevelIdText',
        'oldSalaryLevelType',
        'oldSalaryLevelTypeText'
      ];

      let result = res.object || {};
      for (const key in result) {
        if (field.includes(key)) {
          this.$set(this.form, key, result[key] || '');
        }
      }

      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let submitData = deepClone(this.form);
        let API = null;
        switch (this.type) {
          case 'add':
            API = this.ajax.advancementIncidentSave;
            break;
          case 'edit':
            API = this.ajax.advancementIncidentUpdate;
            break;
        }

        this.submitLoading = true;
        const res = await API(submitData);
        this.submitLoading = false;
        if (!res.success) {
          this.$message.error(res.message || '操作失败！');
          return;
        }
        this.$message.success('操作成功!');
        this.close();
        this.$emit('refresh');
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    // 获取岗位类别
    async handleGetPostCategoryList() {
      let res = await this.ajax.postCategoryGetList();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      if (res.object.length) {
        this.postCategoryList = res.object.filter(f => f.isEnable == '1');
      }
    },

    // 变更岗位类别 获取岗位等级
    handleChangePostCategory(val) {
      this.$set(this.form, 'newGwdj', '');
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });

      // 获取对应岗位类别的 岗位等级下拉
      this.handleGetPostLevel(val);
    },

    // 获取岗位等级
    async handleGetPostLevel(postCategory) {
      const res = await this.ajax.payPostGetList({
        postCategory
      });

      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object.length) {
        this.postLevelList = res.object.map(m => {
          return {
            postId: m.postId,
            label: m.postName
          };
        });
      }
    },

    // 获取薪级类别
    async handleGetSalaryCategoryList() {
      let res = await this.ajax.comboboxSalaryLevelCategory();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      if (res.object.length) {
        this.salaryCategoryList = res.object || [];
      }
    },

    // 变更薪级类别
    handleChangeSalaryCategory(val) {
      // 清除 薪级等级
      this.$set(this.form, 'newSalaryLevelId', '');
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });

      // 获取对应薪级类别的 薪级等级下拉
      this.handleGetSalaryLevel(val);
    },

    // 获取薪级等级
    async handleGetSalaryLevel(salaryLevelCategory) {
      const res = await this.ajax.getSalaryLeveCombobox({
        salaryLevelCategory
      });

      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object.length) {
        this.salaryLevelList = res.object || [];
      }
    },

    close() {
      this.visible = false;
      this.disabled = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-promotion {
  /deep/ .vxe-modal--content {
    height: 500px;
    padding: 8px !important;
    padding-bottom: 0px !important;
    overflow: auto;

    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .textarea {
        .el-textarea__inner {
          min-height: 65px !important;
          max-height: 65px !important;
        }
      }

      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      input[type='number'] {
        -moz-appearance: textfield;
      }

      .box-item-container {
        width: 100%;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        margin-bottom: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        .head {
          height: 32px;
          line-height: 32px;
          padding: 0 8px;
          border-bottom: 1px solid #ebeef5;

          .head-title {
            font-weight: 800;
            &::before {
              content: '';
              display: inline-block;
              width: 4px;
              height: 16px;
              background: $primary-blue;
              border-radius: 2px;
              vertical-align: text-top;
              margin-right: 4px;
            }
          }
        }

        &.close {
          .item-content {
            height: 0;
            padding: 0;
            opacity: 0;
          }
        }

        &.require-form {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          .item-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 0;
          }
        }

        .item-content {
          padding: 8px;
          padding-bottom: 0px;
          margin: 0px;
          transition: all 0.4s;

          li {
            margin-bottom: 8px;
            width: 25%;
          }
        }
      }

      .title-head-table {
        width: 100%;
        height: 30px;
        line-height: 30px;
        background-color: #7980f8;
        border-radius: 4px;
        color: #fff;
        .title {
          width: 155px;
          text-align: center;
          line-height: 30px;
          border-right: 1px solid #fff;
        }
        .left {
          flex: 1;
          text-align: center;
          line-height: 30px;
          border-right: 1px solid #fff;
        }
        .right {
          flex: 1;
          text-align: center;
          line-height: 30px;
        }
      }

      .ts-form-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .row-styles-input {
          margin: 0 4px 0 8px;
        }

        .percentage-input {
          width: 86px;
          min-width: 86px !important;

          .el-input {
            min-width: 86px !important;
          }
        }

        .histroy-form-item {
          border-bottom: 1px solid #ebeef5;
          margin-bottom: 0px !important;
          .el-form-item__label {
            transform: translateY(4px);
          }

          &.radio-item {
            .el-form-item__label {
              transform: translateY(0px);
            }
          }

          .el-form-item__content {
            display: flex;
            align-items: center;
            position: relative;

            .last-time-container {
              display: flex;
              align-items: center;
              overflow: hidden;
              border-left: 1px solid #ebeef5;
              padding: 6px 8px;

              .parent-last-container {
                width: 100%;
              }
            }

            .now-time-container {
              display: flex;
              align-items: center;
              overflow: hidden;
              border-left: 1px solid #ebeef5;
              padding-left: 12px;
              padding: 6px 8px;
            }
          }
        }

        .change-error {
          position: absolute;
          z-index: 10;
          padding: 2px 8px;
          left: 50%;
          top: calc(100% - 4px);
          color: rgb(245, 108, 108);
          background-color: rgb(251, 227, 227);
          border-radius: 4px;
          display: flex;
          align-items: center;
          height: 24px;
          width: 45px;
          transform: translateX(8px);
        }

        .error-tips {
          position: absolute;
          z-index: 10;
          padding: 2px 8px;
          top: calc(100% - 4px);
          left: 8px;
          color: rgb(245, 108, 108);
          background-color: rgb(251, 227, 227);
          border-radius: 4px;
          width: 45px;
          height: 24px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
