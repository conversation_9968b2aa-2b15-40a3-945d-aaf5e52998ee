<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTab">
      <ts-tab-pane label="岗位等级字典" name="1"></ts-tab-pane>
      <ts-tab-pane label="薪级字典" name="2"></ts-tab-pane>
      <ts-tab-pane label="政策标准" name="3"></ts-tab-pane>
    </ts-tabs>

    <PostSetting ref="PostSetting" v-if="activeTab == '1'" />
    <SalaryScale ref="SalaryScale" v-if="activeTab == '2'" />
    <policyStandard ref="policyStandard" v-if="activeTab == '3'" />
  </div>
</template>

<script>
import PostSetting from './components/post-setting.vue';
import SalaryScale from './components/salary-scale.vue';
import policyStandard from './components/policy-standard.vue';
export default {
  components: {
    PostSetting,
    SalaryScale,
    policyStandard
  },
  data() {
    return {
      activeTab: '1'
    };
  },
  watch: {
    activeTab() {
      this.refresh();
    }
  },
  methods: {
    refresh() {
      let dic = {
        1: 'PostSetting',
        2: 'SalaryScale',
        3: 'policyStandard'
      };

      this.$nextTick(() => {
        this.$refs[dic[this.activeTab]].handleRefreshTable();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
