<template>
  <ts-dialog
    class="dialog-add-post"
    :title="title"
    width="800px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="100px">
        <ts-row>
          <ts-form-item
            label="岗位类别"
            prop="postCategory"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="form.postCategory"
              :disabled="type === 'edit'"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of postCategoryList"
                :key="item.postCategoryId"
                :label="item.postCategoryName"
                :value="item.postCategoryId"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-row>

        <ts-form-item label="岗位等级:" prop="postName" :rules="rules.required">
          <ts-input v-model="form.postName" placeholder="请输入岗位等级" />
        </ts-form-item>

        <ts-form-item label="状态" prop="isEnable" :rules="rules.required">
          <ts-switch
            v-model="form.isEnable"
            inactive-value="0"
            active-value="1"
          ></ts-switch>
        </ts-form-item>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.remark"
            placeholder="请输入"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
          />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    postCategoryList: {
      type: Array
    }
  },
  data() {
    return {
      visible: false,

      title: '',
      type: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async open({ data = {}, title, type }) {
      this.form = deepClone(data);
      this.title = title;
      this.type = type;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },

    async submit() {
      try {
        await this.$refs.ruleForm.validate();

        const data = deepClone(this.form);
        let Api = this.ajax.payPostAdd;
        if (this.type == 'edit') {
          Api = this.ajax.payPostUpdate;
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.type = undefined;
      this.form = {};
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-add-post {
    height: 470px;

    .el-dialog__body {
      height: calc(100% - 88px);
      overflow: auto;
      padding-right: 64px !important;
    }
  }

  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
