<template>
  <div class="summary-and-report-box">
    <div class="left-tree-content">
      <base-search-tree
        class="attendance-tree"
        ref="searchTree"
        placeholder="输入科室名进行搜索"
        :apiFunction="apiFunction"
        :params="treeParams"
        @tree="getTreeSuccess"
        @beforeClick="clickItemTree"
      />
    </div>
    <div class="right-table-content">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :actions="actions"
        @search="search"
        :resetData="handleReset"
      >
        <template slot="employeeStatuses">
          <ts-select
            style="width: 100%"
            v-model="searchForm.employeeStatuses"
            clearable
            multiple
            placeholder="请选择"
          >
            <ts-option
              v-for="item of employeeStatusData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>
        <template slot="establishmentTypes">
          <ts-select
            style="width: 100%"
            v-model="searchForm.establishmentTypes"
            clearable
            placeholder="请选择"
          >
            <ts-option
              v-for="item of establishmentTypeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>

        <template slot="right">
          <ts-button type="primary" @click="handleAdjust">
            一键确认
          </ts-button>
        </template>
      </ts-search-bar>
      <TsVxeTemplateTable
        id="table_newsalary_options_changes_"
        class="form-table"
        ref="table"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>

    <dialog-options-adjust
      ref="DialogOptionsAdjust"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import { getOrganizationSelectZTreeList } from '@/api/ajax/personManage/empTemporary.js';
import table from './tab-newsalary-options-changes.js';
import DialogOptionsAdjust from './dialog-options-adjust.vue';
import moment from 'moment';
export default {
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  mixins: [table],
  components: {
    DialogOptionsAdjust
  },
  data() {
    return {
      org: {
        orgId: '',
        orgName: ''
      },
      treeParams: {},
      apiFunction: getOrganizationSelectZTreeList,
      establishmentTypeData: [],
      employeeStatusData: [],
      dialogOrganizationTreeData: []
    };
  },
  created() {
    this.getEstablishmentTypeData();
    this.getEmployeeStatusData();
  },
  methods: {
    //组织树加载成功
    async getTreeSuccess(data) {
      this.activeId = data[0].id;
      this.dialogOrganizationTreeData = data;
      this.org.orgId = data[0].id;
      this.org.orgName = data[0].name;
      await this.handleRefreshTable();
    },
    //组织树点击
    async clickItemTree(node) {
      this.$refs.table.pageNo = 1;
      this.org.orgId = node.id;
      this.org.orgName = node.name;
      await this.handleRefreshTable();
    },
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let employeeStatuses = null;
      if (this.searchForm.employeeStatuses) {
        employeeStatuses = this.searchForm.employeeStatuses.join(',');
      }
      let formData = {
        ...this.searchForm,
        ...this.org,
        employeeStatuses,
        pageNo,
        pageSize
      };
      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        }
      });
      let res = await this.ajax.getNewsalaryOptionsRemindRecordList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    //重置
    handleReset() {
      if (this.dialogOrganizationTreeData) {
        this.org.orgId = this.dialogOrganizationTreeData[0].id;
        this.org.orgName = this.dialogOrganizationTreeData[0].name;
      }
      return {
        computeDate: this.$moment().format('YYYY-MM')
      };
    },
    //一键确认
    async handleAdjust() {
      try {
        await this.$confirm(
          '方案调整后为空的数据不支持一键确认，不为空的数据当月生效，是否一键确认？',
          '提示',
          {
            type: 'warning'
          }
        );
        let employeeStatuses = null;
        if (this.searchForm.employeeStatuses) {
          employeeStatuses = this.searchForm.employeeStatuses.join(',');
        }
        let formData = {
          ...this.searchForm,
          ...this.org,
          employeeStatuses
        };
        Object.keys(formData).map(key => {
          if (
            formData[key] == null ||
            formData[key] == undefined ||
            formData[key] == ''
          ) {
            delete formData[key];
          }
        });
        const res = await this.ajax.handleBatchAdjustOptions(formData);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    //单条数据处理
    handleConfirm(row) {
      let effectiveDate = moment()
        .startOf('day')
        .format('YYYY-MM-DD');

      this.$refs.DialogOptionsAdjust.open({
        data: {
          ...row,
          effectiveDate
        },
        isdisabled: false
      });
    },
    handleShow(row) {
      this.$refs.DialogOptionsAdjust.open({
        data: {
          ...row
        },
        isdisabled: true
      });
    },
    /**@desc 获取编制类型 */
    getEstablishmentTypeData() {
      this.ajax.getDataByDataLibrary('establishment_type').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '编制类型获取失败');
          return;
        }
        this.establishmentTypeData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    /**@desc 获取员工状态 */
    getEmployeeStatusData() {
      this.ajax.getDataByDataLibrary('employee_status').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '员工状态获取失败');
          return;
        }
        this.employeeStatusData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.summary-and-report-box {
  height: calc(100% - 8px);
  margin-bottom: 8px;
  display: flex;

  & > div {
    height: 100%;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
    border: 1px solid #e4e4e4;
  }

  .left-tree-content {
    width: 220px;
    border-radius: 8px;
    background: #fff;
    height: 100%;
    margin-right: 8px;
    padding: 8px;
    /deep/ .attendance-tree {
      .button.attendance-icon_ico_docu,
      .button.attendance-icon_ico_open,
      .button.attendance-icon_ico_close {
        position: relative;
        width: 0;
        &::before {
          font-family: oaicon;
          content: '\e8c3';
          color: $success-color;
          width: 16px;
          height: 16px;
          position: absolute;
          left: -16px;
        }
      }
      .not-leaf-node {
        > .button.attendance-icon_ico_docu,
        > .button.attendance-icon_ico_open,
        > .button.attendance-icon_ico_close {
          &::before {
            position: static;
          }
        }
      }
    }
  }
  .right-table-content {
    border-radius: 8px;
    background: #fff;
    height: 100%;
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }
  .form-table {
    overflow: hidden;
  }
  .action-span {
    color: $primary-blue;
    cursor: pointer;
    user-select: none;

    & + .action-span {
      margin-left: $primary-spacing;
    }

    &:hover {
      opacity: 0.8;
    }

    &:focus,
    &:active {
      color: $primary-blue-active;
    }
  }
}
</style>
