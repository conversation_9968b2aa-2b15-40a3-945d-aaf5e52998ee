export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '薪级类别',
          value: 'salaryLevelCategory',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: []
        },
        {
          label: '薪级名称',
          value: 'salaryLevelName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入薪级名称'
          }
        },
        {
          label: '状态',
          value: 'isEnable',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              element: 'ts-option',
              label: '全部',
              value: ''
            },
            {
              element: 'ts-option',
              label: '启用',
              value: '1'
            },
            {
              element: 'ts-option',
              label: '禁用',
              value: '2'
            }
          ]
        }
      ],

      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '薪级类别',
          prop: 'salaryLevelCategoryName',
          align: 'center'
        },
        {
          label: '薪级名称',
          prop: 'salaryLevelName',
          align: 'center'
        },
        {
          label: '薪级等级',
          prop: 'grade',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'isEnable',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            const statusText = row.isEnable == '1' ? '启用' : '禁用';
            const statusStyle = row.isEnable != '1' ? { color: 'red' } : {};

            return h('div', { style: statusStyle }, statusText);
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          headerSlots: 'action',
          render: (h, { row }) => {
            let changeStatusLabel = row.isEnable == '1' ? '禁用' : '启用';
            let actionList = [
              {
                label: '编辑',
                event: this.handleOpenEditMoal
              },
              {
                label: changeStatusLabel,
                event: () => {
                  this.handleChangeStatus(row, changeStatusLabel);
                }
              },
              {
                label: '详情',
                event: this.handleDetail
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  }
};
