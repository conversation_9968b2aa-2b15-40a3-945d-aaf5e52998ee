<template>
  <ts-dialog
    custom-class="dialog-import-file"
    :title="导入"
    width="450px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-row>
          <ts-col :span="24">
            <ts-form-item class="down-box" :label="label" prop="file">
              <ts-upload
                v-if="visible"
                class="base-uplaod"
                ref="tsUpload"
                action=""
                :limit="1"
                :fileList.sync="fileList"
                :show-file-list="false"
                accept=".xlsx"
                :http-request="handleUploadFile"
                :on-exceed="masterFileMax"
              >
                <ts-button>上传文件</ts-button>
              </ts-upload>

              <span class="down-template" @click="handleDownTemplate">
                下载模版
              </span>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      fileList: [],

      type: undefined
    };
  },
  computed: {},
  methods: {
    open({ type }) {
      this.type = type;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },
    //上传文件
    handleUploadFile(params) {
      let data = new FormData();

      data.append('file', params.file);
      let API = this.ajax.importTemporaryAdjustData;

      API(data).then(res => {
        if (res.success && res.statusCode === 200) {
          let mesage = '操作成功';
          if (res.object) {
            mesage +=
              ';成功：' +
              res.object.successNum +
              '条，失败：' +
              res.object.errorNum +
              '条';
          }
          this.$message.success(mesage);
          this.close();
          this.$emit('refresh');
        } else {
          this.fileList = [];
          this.$message.error(res.message || '操作失败');
        }
      });
    },

    masterFileMax() {
      this.$message.warning(`请最多上传1个文件。`);
    },

    //导入模板下载
    handleDownTemplate() {
      let aDom = document.createElement('a');
      aDom.href =
        '/ts-hrms/api/newsalaryTemporaryAdjust/downloadImportTemplate';
      aDom.click();
    },

    close() {
      this.form = null;
      this.fileList = [];
      this.type = undefined;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-import-file {
    .el-dialog__body {
      height: 100px !important;

      .content {
        height: 100% !important;

        .down-box {
          position: relative;

          .down-template {
            position: absolute;
            left: 90px;
            top: 6px;
            color: $primary-blue;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
