import { getOrganizationSelectZTreeList } from '@/api/ajax/personManage/empTemporary.js';
export default {
  data() {
    return {
      org: {
        orgId: '',
        orgName: ''
      },
      activeId: '',
      apiFunction: getOrganizationSelectZTreeList,

      deptAttendanceStatus: -1,

      dialogImporAttendanceRecord: false,
      dialogOrganizationTreeData: [],
      personalIdentityData: [],
      adjusItemsData: []
    };
  },
  async created() {
    this.getAdjusItemsData();
    this.getPersonalIdentityData();
  },
  methods: {
    //组织树加载成功
    async getTreeSuccess(data) {
      this.activeId = data[0].id;
      this.dialogOrganizationTreeData = data;
      this.org.orgId = data[0].id;
      this.org.orgName = data[0].name;
      await this.handleRefreshTable();
    },
    //组织树点击
    async clickItemTree(node) {
      this.$refs.table.pageNo = 1;
      this.org.orgId = node.id;
      this.org.orgName = node.name;
      await this.handleRefreshTable();
    },
    //重置
    handleReset() {
      this.org = {};
      return {
        optionCycle: this.$moment().format('YYYY-MM')
      };
    },
    /**@desc 获取岗位 */
    getPersonalIdentityData() {
      this.ajax.getDataByDataLibrary('personal_identity').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '岗位获取失败');
          return;
        }
        this.personalIdentityData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    /**@desc 获取项目类型 */
    getAdjusItemsData() {
      this.ajax.getDataByDataLibrary('tmp_adjust_item').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '项目类型获取失败');
          return;
        }
        this.adjusItemsData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    }
  }
};
