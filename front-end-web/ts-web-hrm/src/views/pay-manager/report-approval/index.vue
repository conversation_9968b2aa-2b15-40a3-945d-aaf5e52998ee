<template>
  <div class="report-approval">
    <div class="report-approval-box">
      <ts-tabs v-model="activeTab">
        <ts-tab-pane label="待审批" name="1"></ts-tab-pane>
        <ts-tab-pane label="已审批" name="2"></ts-tab-pane>
      </ts-tabs>

      <div class="content-list">
        <Unapproved ref="unapproved" v-if="activeTab === '1'" />
        <Approved ref="approved" v-if="activeTab === '2'" />
      </div>
    </div>
  </div>
</template>

<script>
import Unapproved from './components/salary-report-unapproved.vue';
import Approved from './components/salary-report-approved.vue';
export default {
  components: {
    Unapproved,
    Approved
  },
  data() {
    return {
      activeTab: '1',
      refName: {
        1: 'unapproved',
        2: 'approved'
      }
    };
  },
  watch: {
    activeTab: {
      handler() {
        this.refresh();
      }
    }
  },
  methods: {
    refresh() {
      let refTable = this.refName[this.activeTab];
      this.$nextTick(() => {
        this.$refs[refTable].handleRefreshTable();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.report-approval {
  height: 100%;
  padding-bottom: 8px;
  background-color: rgb(239, 239, 244);
  .report-approval-box {
    padding: 8px;
    height: 100%;
    background: #fff;
    .content-list {
      height: calc(100% - 44px);
    }
  }
}
</style>
