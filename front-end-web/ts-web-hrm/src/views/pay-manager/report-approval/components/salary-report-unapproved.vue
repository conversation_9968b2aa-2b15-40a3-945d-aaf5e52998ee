<template>
  <div class="table-container" v-loading="exportLoaing">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :resetData="resetData"
      :elementCol="14"
      @search="search"
    >
      <template slot="right">
        <ts-button @click="handleExport">
          导出
        </ts-button>
        <ts-button @click="handleBatchReject">
          批量驳回
        </ts-button>
        <ts-button type="primary" @click="handleBatchApproval">
          批量审批
        </ts-button>
      </template>
    </ts-search-bar>

    <!-- <base-table
      ref="table"
      class="form-table"
      border
      stripe
      :hasPage="false"
      :columns="columns"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectChange"
    /> -->
    <TsVxeTemplateTable
      id="table_salary_report_unapproved"
      ref="table"
      :hasPage="false"
      :columns="columns"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectChange"
    />

    <dialog-edit-item
      ref="DialogEditItem"
      :localColumns="localColumns"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import moment from 'moment';
import DialogEditItem from '@/views/pay-manager/dept-report/components/dialog-edit-item.vue';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  components: {
    DialogEditItem
  },
  data() {
    return {
      selectList: [],
      searchForm: {
        payrollDate: moment()
          .subtract(1, 'months')
          .format('YYYY-MM')
      },
      resetData: {
        payrollDate: moment()
          .subtract(1, 'months')
          .format('YYYY-MM')
      },
      columns: [],
      localColumns: [],
      exportLoaing: false,
      searchList: [
        {
          label: '上报月份',
          value: 'payrollDate',
          element: 'ts-month-picker',
          elementProp: {
            placeholder: '请选择月份',
            valueFormat: 'YYYY-MM',
            allowClear: false
          },
          event: {
            change: e => {
              this.$set(this.searchForm, 'payrollDate', e);
              this.search();
            }
          }
        },
        {
          label: '科室',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入科室'
          }
        },
        {
          label: '姓名',
          value: 'employee_name',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入姓名'
          }
        },
        {
          label: '工号',
          value: 'employee_no',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入工号'
          }
        }
      ]
    };
  },
  methods: {
    handleSelectChange(e) {
      this.selectList = e;
    },

    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    // 修改
    handleEdit(data) {
      this.$refs.DialogEditItem.open({
        data: deepClone(data),
        role: 'admin'
      });
    },

    // 审批
    async handleApproval(row) {
      try {
        await this.$confirm(
          `确定审批 <span style="color: #FC8B2A">${row.employee_name}</span> 这条数据?`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );

        this.handleFunctionApproval([row.id]);
      } catch (e) {
        console.error(e);
      }
    },

    // 批量审批
    async handleBatchApproval() {
      if (this.selectList.length == 0) {
        this.$message.warning('请选择要操作的数据');
        return false;
      }

      let names = this.selectList.map(m => m.employee_name).join(',');
      try {
        await this.$confirm(
          `确定审批 <span style="color: #FC8B2A">${names}</span> 这些数据?`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );

        let ids = this.selectList.map(m => m.id);
        this.handleFunctionApproval(ids);
      } catch (e) {
        console.error(e);
      }
    },

    // 审批方法
    async handleFunctionApproval(ids) {
      try {
        const res = await this.ajax.newSalaryDetailUploadToExamine(ids);
        if (res.success && res.statusCode === 200) {
          this.$message.success(`操作成功! ${res.object || ''}`);
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {}
    },

    // 单个驳回
    async handleReject(row) {
      try {
        await this.$confirm(
          `确定驳回 <span style="color: #FC8B2A">${row.employee_name}</span> 这条数据?`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );

        this.handleFunctionReject([row.id]);
      } catch (e) {
        console.error(e);
      }
    },

    // 批量驳回
    async handleBatchReject() {
      if (this.selectList.length == 0) {
        this.$message.warning('请选择要操作的数据');
        return false;
      }

      let names = this.selectList.map(m => m.employee_name).join(',');
      try {
        await this.$confirm(
          `确定驳回 <span style="color: #FC8B2A">${names}</span> 这些数据?`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );

        let ids = this.selectList.map(m => m.id);
        this.handleFunctionReject(ids);
      } catch (e) {
        console.error(e);
      }
    },

    // 驳回操作
    async handleFunctionReject(ids) {
      try {
        const res = await this.ajax.rejectDetailUploadUpdate(ids);
        if (res.success && res.statusCode === 200) {
          this.$message.success(`操作成功! ${res.object || ''}`);
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {}
    },

    async handleRefreshTable() {
      await this.handleGetTableHead();
      await this.handleGetTableData();
    },

    async handleGetTableHead() {
      this.columns = [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          type: 'index',
          align: 'center',
          fixed: 'left',
          width: 70
        }
      ];
      let res = await this.ajax.newSalaryDetailUploadSalaryCountTitle({
        payrollDate: this.searchForm.payrollDate
      });
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        let autoWidth = false;
        if (res.object.length < 15) autoWidth = true;
        res.object.forEach((item, index) => {
          if (index < 4) {
            item.fixed = 'left';
          } else {
            if (autoWidth) delete item.width;
          }
          item.showOverflowTooltip = false;
        });
        this.localColumns = deepClone(res.object);
        this.columns.push(...res.object);
        this.columns.push({
          label: '操作',
          align: 'center',
          width: 130,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '修改',
                event: this.handleEdit
              },
              {
                label: '驳回',
                event: this.handleReject
              },
              {
                label: '审批',
                event: this.handleApproval
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        });
      }
    },

    async handleGetTableData() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          sidx: 'du.create_date',
          sord: 'desc',
          isExamine: '1',
          pageNo,
          pageSize
        };
      let res = await this.ajax.selectApprovalList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    /**@desc 校验输入两位小数 */
    inputTowDecimalPlacesNegative(value, obj, attr) {
      let newVal = inputTowDecimalPlacesNegative(value);
      obj[attr] = newVal;
    },

    inputBlur(event, obj, attr) {
      let value = parseFloat(event.target.value);
      this.$set(obj, attr, isNaN(value) ? '' : value);
    },

    handleExport() {
      try {
        this.exportLoaing = true;
        let pageNo = this.$refs.table.pageNo;
        let pageSize = this.$refs.table.pageSize;
        let data = {
            ...this.searchForm,
            sidx: 'du.create_date',
            sord: 'desc',
            isExamine: '1',
            pageNo,
            pageSize
          },
          queryList = [],
          aDom = document.createElement('a');

        Object.keys(data).map(key => {
          queryList.push(key + '=' + data[key]);
        });
        aDom.href = '/ts-hrms/api/uploadDataExport?' + queryList.join('&');
        aDom.click();
      } catch (error) {
      } finally {
        setTimeout(() => {
          this.exportLoaing = false;
        }, 1000);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
