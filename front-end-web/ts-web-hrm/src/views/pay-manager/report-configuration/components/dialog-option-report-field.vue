<template>
  <el-drawer
    custom-class="dialog-option-report-field"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
    size="35%"
  >
    <template slot="title">
      <span class="dialog-title">
        {{ title }}
      </span>
    </template>
    <div class="content">
      <div class="detail">
        <ts-form ref="form" :model="form" labelWidth="80px">
          <div
            style="width: 100%;background-color:#efefef;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">基本信息</span>
          </div>
          <ts-row style="flex-direction: column;">
            <ts-col :span="12">
              <ts-row style="flex-direction: column;">
                <ts-col :span="24">
                  <ts-form-item
                    label="字段名称"
                    prop="colName"
                    :rules="rules.required"
                  >
                    <ts-input v-model="form.colName" placeholder="请输入" />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item
                    label="加减项"
                    prop="countType"
                    :rules="rules.required"
                  >
                    <ts-select
                      style="width: 100%"
                      v-model="form.countType"
                      clearable
                      placeholder="请选择"
                    >
                      <ts-option label="加项" value="1"></ts-option>
                      <ts-option label="减项" value="2"></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="备注" prop="remark" :rules="rules.remark">
                <ts-input
                  v-model="form.remark"
                  type="textarea"
                  class="textarea"
                  maxlength="500"
                  placeholder="请输入"
                  show-word-limit
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <div
            style="width: 100%;background-color:#efefef;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">映射信息</span>
          </div>
          <div ref="mapDivScrollbar" :style="{ height: height + 'px' }">
            <!-- 字段映射 -->
            <el-scrollbar
              style="height: 100%;"
              wrap-style="overflow-x: hidden;"
            >
              <div
                class="option-map-item"
                v-for="(f, i) in form.reportMapVoList"
                :key="f.id"
              >
                <ts-row style="flex-direction: row;">
                  <ts-col :span="20">
                    <ts-form-item
                      label="映射方案"
                      :prop="`reportMapVoList.${i}.optionId`"
                      :rules="rules.required"
                    >
                      <ts-select
                        v-model="f.optionId"
                        @change="handleChangeOptions($event, f, 'change')"
                        clearable
                        filterable
                        style="width: 100%"
                      >
                        <ts-option
                          v-for="item in optionsList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          :disabled="ids.includes(item.value)"
                        />
                      </ts-select>
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="4">
                    <img
                      v-show="i == 0"
                      @click="handleOperate('add', i)"
                      class="image add"
                      src="@/assets/img/add.svg"
                      alt=""
                    />
                    <img
                      v-show="i != 0"
                      @click="handleOperate('ridurre', i)"
                      class="image ridurre"
                      src="@/assets/img/ridurre.svg"
                      alt=""
                    />
                  </ts-col>
                </ts-row>
                <ts-row>
                  <ts-col :span="16">
                    <ts-form-item
                      label="计算规则"
                      :prop="`reportMapVoList.${i}.itemRule`"
                      :rules="rules.required"
                    >
                      <ts-radio-group
                        v-model="f.itemRule"
                        clearable
                        style="width: 100%"
                      >
                        <ts-radio label="1">关联薪酬项目</ts-radio>
                        <ts-radio label="2">自定义公式</ts-radio>
                      </ts-radio-group>
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="8">
                    <ts-button
                      type="primary"
                      v-if="f.itemRule == '2' && f.fieldList"
                      @click="handleOpenCustomFormula(f)"
                      >编辑公式</ts-button
                    >
                  </ts-col>
                </ts-row>
                <ts-row>
                  <ts-form-item
                    label=""
                    :prop="`reportMapVoList.${i}.itemId`"
                    :rules="rules.required"
                    v-if="f.itemRule == '1'"
                  >
                    <ts-select
                      v-model="f.itemId"
                      clearable
                      filterable
                      style="width: 100%"
                      @change="handleChangeField($event, f)"
                      placeholder="请选择薪酬项目"
                    >
                      <ts-option
                        v-for="item in f.fieldList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </ts-select>
                  </ts-form-item>

                  <ts-form-item
                    label=""
                    :prop="`reportMapVoList.${i}.countFormulaText`"
                    :rules="rules.required"
                    v-if="f.itemRule == '2'"
                  >
                    <ts-input
                      v-model="f.countFormulaText"
                      placeholder="点击编辑公式"
                      disabled
                    />
                  </ts-form-item>
                </ts-row>
              </div>
            </el-scrollbar>
          </div>

          <dialog-custom-formula
            :basicSalaryArr="basicSalaryArr"
            :salaryGroupItemArr="salaryGroupItemArr"
            ref="DialogCustomFormula"
            @submit="handleCustomFormulaSubmit"
          />
        </ts-form>
        <template>
          <div style="position: fixed;bottom: 20px;right: 20px;z-index: 99999;">
            <ts-button type="primary" @click="submit">保存</ts-button>
            <ts-button @click="close">关闭</ts-button>
          </div>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import DialogCustomFormula from '@/views/pay-manager/components/dialog-custom-formula.vue';
export default {
  components: {
    DialogCustomFormula
  },
  data() {
    return {
      itemForm: {},
      visible: false,
      title: '',
      type: '',
      height: 300,
      optionsList: [],
      //薪酬计算数据
      basicSalaryArr: [],
      salaryGroupItemArr: [],

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    ids() {
      return (this.form.reportMapVoList || []).map(f => f.optionId);
    }
  },
  updated() {
    this.height =
      window.innerHeight -
      this.$refs.mapDivScrollbar?.getBoundingClientRect().top -
      50;
  },
  methods: {
    async open({ title, type, data = {} }) {
      this.title = title;
      this.type = type;
      await this.handleGetProgrammeList();
      // 获取薪酬档案字段
      await this.handleGetSalaryBasicColumnGetBasicSalary();

      if (type == 'add') {
        this.$set(this, 'form', {
          reportId: data.reportId,
          reportMapVoList: [
            {
              optionId: '',
              itemId: '',
              itemName: '',
              itemRule: '1',
              fieldList: []
            }
          ]
        });
      } else {
        let echoData = deepClone(data);
        if (echoData.reportMapVoList && echoData.reportMapVoList.length) {
          for (let i = 0; i < echoData.reportMapVoList.length; i++) {
            const item = echoData.reportMapVoList[i];
            await this.handleChangeOptions(item.optionId, item, 'echo');
          }
        }
        this.$set(this, 'form', echoData);
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    async handleChangeOptions(optionId, item, type) {
      if (type === 'change') {
        item.itemId = '';
        item.itemName = '';
        item.fieldList = [];
        this.$nextTick(() => {
          this.$refs.form?.clearValidate();
        });
      }

      if (optionId) {
        let options = await this.handleGetCalculationGroupList(optionId);
        if (options) {
          item.fieldList = deepClone(options);
          this.salaryGroupItemArr = item.fieldList || [];
          this.$forceUpdate();
        }
      }
    },
    async handleChangeField(fieldId, item) {
      if (fieldId) {
        let { fieldList = [] } = item;
        let find = fieldList.find(f => f.value === fieldId);
        if (find) item.itemName = find.label;
      } else {
        item.itemId = '';
        item.itemName = '';
        this.$nextTick(() => {
          this.$refs.form?.clearValidate();
        });
      }
    },

    async handleGetProgrammeList() {
      const res = await this.ajax.newSalaryOptionAllList();
      if (!res.success) {
        this.$message.error(res.message || '获取方案失败!');
        return;
      }

      let data = res.object || [];
      this.optionsList = data.map(m => {
        return {
          label: m.optionName,
          value: m.id
        };
      });
    },

    async handleGetCalculationGroupList(optionId) {
      const res = await this.ajax.newSalaryOptionItem(optionId);
      if (!res.success) {
        this.$message.error(res.message || '获取方案字段失败!');
        return false;
      }
      let data = res.object || [];
      return data.map(m => {
        return {
          label: m.itemName,
          value: m.id,
          libraryType: m.libraryType
        };
      });
    },

    handleOperate(type, index) {
      switch (type) {
        case 'add':
          this.form.reportMapVoList.push({
            itemId: '',
            itemName: '',
            optionId: '',
            itemRule: '1',
            fieldList: []
          });
          break;
        case 'ridurre':
          this.form.reportMapVoList.splice(index, 1);
          break;
      }
    },

    async submit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let API = null;
      if (this.type === 'add') {
        API = this.ajax.reportTotalSave;
      } else {
        API = this.ajax.reportTotalUpdate;
        this.form.id = this.form.colId;
      }
      const data = Object.assign({}, this.form);
      if (data.reportMapVoList && data.reportMapVoList.length) {
        data.reportMapVoList.forEach(f => delete f.fieldList);
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refresh');
    },

    close() {
      this.title = '';
      this.type = '';
      this.optionsList = [];
      this.form = {};
      this.visible = false;
    },
    //编辑计算公式
    handleOpenCustomFormula(item) {
      this.itemForm = item;
      this.salaryGroupItemArr = item.fieldList || [];
      let {
        valueList,
        stringNames
      } = this.$refs.DialogCustomFormula.formateData(item.countFormula);
      this.valueList = valueList;
      item.countFormulaText = stringNames;
      this.$refs.DialogCustomFormula.open(this.valueList);
    },
    //保存编辑公式
    handleCustomFormulaSubmit(data) {
      this.itemForm.countFormula = data.countFormula;
      this.itemForm.countFormulaText = data.countFormulaText;
      this.$forceUpdate();
    },

    async handleGetSalaryBasicColumnGetBasicSalary() {
      const res = await this.ajax.salaryBasicColumnGetBasicSalary();

      if (!res.success) {
        this.$message.error(res.message || '获取分组失败!');
        return;
      }

      this.basicSalaryArr = (res.object || []).map(m => {
        return {
          label: m.basicItemName,
          value: m.id
        };
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.month-picker {
  width: 100%;
}
::v-deep {
  .dialog-option-report-field {
    // margin-top: 45px;
    height: 100%;
    overflow: hidden;

    .el-drawer__header {
      .dialog-title {
        // color: #4148ff;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
      }
      .el-drawer__close-btn {
        position: relative;
        top: -9px;
      }
    }
    .el-drawer__body {
      padding: 0 10px;
      overflow: hidden;
      .content {
        .detail {
          height: calc(100% - 50px);
          .el-form .el-form-item {
            margin: 0;
          }
        }
        .image {
          width: 20px;
          height: 20px;
          margin-left: 8px;
          margin-top: 10px;
          cursor: pointer;
        }
        .textarea {
          .el-textarea__inner {
            min-height: 80px !important;
            max-height: 200px !important;
          }
        }
        .ts-button {
          &.is-disabled {
            color: rgb(204, 204, 204) !important;
            border-color: rgb(231, 235, 240) !important;
            background-color: rgb(250, 250, 250) !important;
            &:hover {
              cursor: not-allowed;
            }
          }
        }
        .fontWeight {
          font-size: 16px;
          font-weight: 600;
        }
        .top {
          width: 4px;
          background-color: #4148ff;
          height: 21px;
          top: -3px;
        }
        .form_item {
          line-height: 36px;
        }
        .option-map-item {
          padding: 10px;
          margin-bottom: 10px;
          border: 1px solid #eeeef4;
        }
      }
    }
  }
}
</style>
