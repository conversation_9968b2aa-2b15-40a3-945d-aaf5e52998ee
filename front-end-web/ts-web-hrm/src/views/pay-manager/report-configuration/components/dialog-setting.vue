<template>
  <ts-dialog
    custom-class="dialog-report-scheme-field"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content" v-loading="loading">
      <div class="left">
        <div class="flex-space">
          <div></div>
          <ts-button type="primary" @click="handleAddFields">
            添加字段
          </ts-button>
        </div>

        <base-table
          ref="table"
          class="form-table set-salary-profile-fields-table"
          border
          stripe
          :hasPage="false"
          :columns="columns"
        />
      </div>

      <el-scrollbar wrap-style="width:450px;overflow-x: hidden;">
        <div class="right">
          <h3>未使用汇总字段</h3>

          <div class="no-use-item-container">
            <div
              class="no-use-item"
              v-for="(name, index) in noUseList"
              :key="index"
            >
              {{ name }}
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <dialog-report-field
      ref="DialogReportField"
      @refresh="handleRefreshTable"
    />
  </ts-dialog>
</template>

<script>
import Sortable from 'sortablejs';
import { deepClone } from '@/unit/commonHandle.js';
import DialogReportField from './dialog-report-field';
export default {
  components: {
    DialogReportField
  },
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    }
  },
  data() {
    return {
      visible: false,
      title: '',
      reportId: '',

      loading: false,
      tableData: [],
      noUseList: [],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '名称',
          prop: 'colName',
          width: 150,
          align: 'center'
        },
        {
          label: '映射',
          prop: 'reportMapVoList',
          align: 'center',
          formatter: row => {
            return row.reportMapVoList.map(m => {
              return (
                <div>
                  {m.optionName} - {m.itemName}
                </div>
              );
            });
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 140,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleEditFields
              },
              {
                label: '删除',
                className: 'red',
                event: this.handleDelete
              }
            ];
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': event => event(row) }}
              />
            );
          }
        }
      ]
    };
  },
  methods: {
    async open({ reportId, title }) {
      if (!reportId) {
        this.$message.error('获取数据失败!');
        return false;
      }

      this.title = title;
      this.reportId = reportId;

      this.visible = true;
      const _this = this;
      this.$nextTick(async () => {
        await _this.handleRefreshTable();
        _this.handleSetSort();
      });
    },
    handleSetSort() {
      const _this = this;

      const tbody = document.querySelector(
        '.set-salary-profile-fields-table .el-table__body-wrapper tbody'
      );
      Sortable.create(tbody, {
        async onEnd({ newIndex, oldIndex }) {
          if (newIndex == oldIndex) return;

          let sortTable = deepClone(_this.tableData);

          const currRow = sortTable.splice(oldIndex, 1)[0];
          sortTable.splice(newIndex, 0, currRow);

          sortTable = sortTable.map((m, i) => {
            return {
              id: m.id,
              sortNo: i
            };
          });

          _this.loading = true;
          const res = await _this.ajax.reportMapSortNo(sortTable);
          _this.loading = false;
          if (res.success) {
            _this.$refs.table.refresh({
              rows: []
            });

            await _this.handleRefreshTable();
          } else {
            this.$message.error(res.message || '获取数据失败!');
          }
        }
      });
    },

    handleAddFields() {
      this.$refs.DialogReportField.open({
        title: '新增字段',
        type: 'add',
        data: {
          reportId: this.reportId
        }
      });
    },

    handleEditFields(row) {
      this.$refs.DialogReportField.open({
        title: '编辑字段',
        type: 'edit',
        data: row
      });
    },

    async handleDelete(row) {
      try {
        await this.$confirm('是否删除该条数据?', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.reportTotalDelete(row.id);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async getReportMapQueryItem() {
      this.noUseList = [];
      let res = await this.ajax.reportMapQueryItem(this.reportId);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      this.noUseList = res.object || [];
    },

    async handleRefreshTable() {
      this.tableData = [];
      let res = await this.ajax.reportTotalList({
        reportId: this.reportId
      });
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = (res?.object || []).map((item, i) => {
        return {
          index: i + 1,
          ...item
        };
      });

      this.tableData = deepClone(rows);
      this.$refs.table.refresh({
        rows
      });
      this.getReportMapQueryItem();
    },

    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-report-scheme-field {
    .el-dialog__body {
      width: calc(100% - 80px);
      height: calc(100% - 80px);
      overflow: auto;

      .content {
        height: 100%;
        display: flex;
        overflow: hidden;
        .left {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          margin-right: 8px;

          .form-table {
            flex: 1;
            overflow: hidden;
            transform: scale(1);
            margin: 8px 0;
            .red {
              color: red;
            }
          }
        }

        .right {
          width: 100%;
          background-color: #eee;
          border: 1px solid #eee;
          border-radius: 4px;
          padding: 8px;

          .no-use-item-container {
            display: flex;
            flex-wrap: wrap;
            overflow-x: hidden;
            .no-use-item {
              margin-right: 8px;
              margin-bottom: 8px;
              border-radius: 4px;
              border: 1px solid #4959f9;
              color: #6870bc;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }
}
</style>
