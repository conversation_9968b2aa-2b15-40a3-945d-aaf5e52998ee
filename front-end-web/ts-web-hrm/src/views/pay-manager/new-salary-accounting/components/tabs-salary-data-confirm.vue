<template>
  <div class="tabs-container tabs-salary-data-confirm">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="orgId">
        <ts-ztree-select
          placeholder="请选择科室"
          ref="treeSelect"
          :inpText.sync="searchForm.name"
          :inpVal.sync="searchForm.code"
          :data="treeData"
          defaultExpandAll
          @change="handleChangeTree"
        />
      </template>
      <template slot="right">
        <ts-button class="more-text-btn" type="primary" @click="handleExport">
          核实算薪人员
        </ts-button>
        <ts-button class="more-text-btn" type="primary" @click="handleImprot">
          手工工资项导入
        </ts-button>
        <ts-button class="more-text-btn" @click="handleCleanData">
          清除导入工资项
        </ts-button>
      </template>
    </ts-search-bar>

    <!-- <base-table
      ref="table"
      class="form-table"
      border
      stripe
      :columns="columns"
      @refresh="handleRefreshTable"
    /> -->
    <TsVxeTemplateTable
      class="form-table"
      ref="table"
      id="salary_data_confirm"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-import-file ref="DialogImportFile" @refresh="handleRefreshTable" />
  </div>
</template>

<script>
import DialogImportFile from './dialog-import-file.vue';

export default {
  components: {
    DialogImportFile
  },
  props: {
    details: {
      type: Object,
      default: () => {}
    },
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入工号/姓名'
          }
        },
        {
          label: '部门',
          value: 'orgId'
        }
      ],
      columns: []
    };
  },

  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleChangeTree(e) {
      const { id } = e;
      this.searchForm.orgId = id;
    },

    handleImprot() {
      this.$refs.DialogImportFile.open({
        optionId: this.details.optionId,
        sendDate: this.details.computeDate
      });
    },
    //清空手工工资条数据
    async handleCleanData() {
      try {
        await this.$confirm(
          `请确认是否清除当前月份核算方案的手工工资项数据，清除后将无法找回已导入的手工项数据`,
          '确认清除吗？',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );

        const res = await this.ajax.cleanImportDataByOptionId({
          optionId: this.details.optionId,
          sendDate: this.details.computeDate
        });
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }
        this.$message.success('操作成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e, '?e');
      }
    },
    handleExport() {
      let optionId = this.details.optionId;
      let computeDate = this.details.computeDate;
      let aDom = document.createElement('a');
      aDom.href = `/ts-hrms/api/newSalaryOptionPayroll/calculateWagesDataExport?optionId=${optionId}&computeDate=${computeDate}`;
      aDom.click();
    },

    async handleRefreshTable() {
      this.handleGetDeptReportUnApprovalPerson();

      await this.handleGetTableHead();
      await this.handleGetTableData();
    },

    async handleGetTableHead() {
      this.columns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          fixed: 'left',
          width: 70
        }
      ];
      let res = await this.ajax.newSalaryOptionPayrollSalaryConfirmTitle({
        optionId: this.details.optionId,
        computeDate: this.details.computeDate
      });
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        this.columns.push(...res.object);
      }
    },

    async handleGetTableData() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          optionId: this.details.optionId,
          computeDate: this.details.computeDate,
          pageNo,
          pageSize
        };

      let res = await this.ajax.newSalaryOptionPayrollSalaryConfirmData(
        searchForm
      );
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = (res.rows || []).map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    handleGetDeptReportUnApprovalPerson() {
      this.ajax
        .queryUploadInfo({
          optionId: this.details.optionId,
          payrollDate: this.details.computeDate
        })
        .then(res => {
          if (res.success == false) {
            this.$message.error(res.message || '获取科室上报人员信息失败');
            return;
          }
          if (res.object && res.object.length) {
            let names = res.object
              .map(m => m.employeeNo + '-' + m.employeeName)
              .join('，');
            this.$message.warning(
              `科室薪资项上报中存在未审批通过的人：${names}`
            );
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-salary-data-confirm {
  ::v-deep {
    .more-text-btn {
      width: 140px;
      > span {
        max-width: 140px !important;
      }
    }
  }
}
</style>
