<template>
  <ts-dialog
    custom-class="dialog-edit-salary-item"
    :title="title"
    width="90%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
        <div class="item-tips small" style="margin-bottom: 8px;">基础信息</div>
        <descriptions
          class="person-info-descriptions"
          column="4"
          :columns="descriptionColumns"
          :tableData="baseInfoData"
        />
        <ts-form ref="form" :model="form" labelWidth="200px">
          <div
            class="form-item"
            v-for="(item, label) in this.form"
            :key="label"
          >
            <div class="item-tips small">{{ label }}</div>
            <div class="item-content">
              <div
                v-for="(formItem, index) in item"
                :key="formItem.itemId"
                class="form-item-container"
              >
                <ts-form-item
                  :label="formItem.itemName"
                  :prop="`${label}.${index}.salary`"
                  :rules="rules.required"
                >
                  <ts-input
                    class="right-aligned-input"
                    style="width: 100%"
                    v-model="formItem.salary"
                    :disabled="
                      formItem.itemRule != '1' ||
                        profileFields.includes(formItem.itemId)
                    "
                    placeholder="请输入"
                    align="right"
                    @input="
                      inputTowDecimalPlacesNegative($event, formItem, 'salary')
                    "
                  />
                </ts-form-item>
              </div>
            </div>
          </div>
        </ts-form>
      </el-scrollbar>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import {
  deepClone,
  inputTowDecimalPlacesNegative
} from '@/unit/commonHandle.js';
import Descriptions from '@/views/pay-manager/components/descriptions.vue';

export default {
  components: {
    Descriptions
  },
  data() {
    return {
      visible: false,
      title: '',
      descriptionColumns: [],
      profileFields: [],
      baseInfoData: {},

      form: {},
      params: undefined,
      rules: {
        required: { required: true, message: '必填' }
      },
      submitLoading: false
    };
  },
  methods: {
    async open({ title, params }) {
      this.title = title;
      this.params = deepClone(params);

      await this.handleGetSalaryProfileFields();
      this.handleGetSalaryChangesData();
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },

    async handleGetSalaryProfileFields() {
      this.profileFields = [];
      let res = await this.ajax.salaryBasicColumnListAll();
      if (res.success == false) {
        this.$message.error(res.message || '定薪列表数据获取失败');
        return;
      }
      this.profileFields = (res.object?.data || []).map(m => m.empField);
    },

    async handleGetSalaryChangesData() {
      let res = await this.ajax.newSalaryOptionPayrollGetSalaryChangesData(
        this.params
      );

      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      // 处理基础信息
      let dir = {
        姓名: 'name',
        工号: 'usercode',
        部门: 'deptName',
        岗位: 'postName'
      };

      let baseInfoData = Object.create({});
      let descriptionColumns = [];

      if (res.object?.baseInfo) {
        for (const label in res.object.baseInfo) {
          const value = res.object.baseInfo[label];

          if (Object.keys(dir).includes(label)) {
            baseInfoData[dir[label]] = value;

            descriptionColumns.push({
              label,
              prop: dir[label]
            });
          }
        }
      }
      this.baseInfoData = baseInfoData;
      this.descriptionColumns = descriptionColumns;

      // 处理表单项目
      this.form = deepClone(res.object.item || {});
      this.$forceUpdate();
    },

    /**@desc 校验输入两位小数 */
    inputTowDecimalPlacesNegative(value, obj, attr) {
      let newVal = inputTowDecimalPlacesNegative(value);
      obj[attr] = newVal;
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        let updateSalaryList = Object.keys(data)
          .reduce((prev, cur) => {
            prev.push(data[cur]);
            return prev;
          }, [])
          .flat();
        data.employeeNo = this.baseInfoData.usercode;

        this.submitLoading = true;
        const res = await this.ajax.newSalaryOptionPayrollReloadStartCalculation(
          {
            updateSalaryList,
            ...this.params
          }
        );
        this.submitLoading = false;
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }

        this.$message.success('操作成功!');
        this.close();
        this.$emit('refresh');
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-edit-salary-item {
    .el-dialog__body {
      .item-content {
        display: flex;
        flex-wrap: wrap;
        .form-item-container {
          width: 25%;
        }
      }

      // .el-form-item__label {
      //   line-height: 18px;
      //   transform: translatey(4px);
      // }

      .ts-input {
        width: 90px !important;
        min-width: 90px !important;
        input {
          width: 90px !important;
        }
      }

      .content {
        height: 560px;
        display: flex;
      }

      .item-tips {
        font-size: 16px;
        font-weight: 700;
        margin-right: 8px;
        display: flex;
        align-items: center;
        &.small {
          font-size: 14px;
          font-weight: 400;
        }

        &::before {
          content: '';
          display: inline-block;
          color: rgb(82, 96, 255);
          height: 16px;
          width: 6px;
          border-radius: 4px;
          background-color: rgb(82, 96, 255);
          margin-right: 8px;
        }
      }

      .person-info-descriptions {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
