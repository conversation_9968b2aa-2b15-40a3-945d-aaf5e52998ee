<template>
  <ts-dialog
    custom-class="dialog-import-all-salary-item-file"
    title="导入"
    width="450px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="140px">
        <ts-row>
          <ts-col :span="24">
            <ts-form-item
              class="down-box"
              label="手工工资项全部导入"
              prop="file"
            >
              <ts-upload
                v-if="visible"
                class="base-uplaod"
                ref="tsUpload"
                action=""
                :limit="1"
                :fileList.sync="fileList"
                :show-file-list="false"
                accept=".xlsx"
                :http-request="handleUploadFile"
                :on-exceed="masterFileMax"
              >
                <ts-button>上传文件</ts-button>
              </ts-upload>

              <span class="down-template" @click="handleDownTemplate">
                下载模版
              </span>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      fileList: [],

      sendDate: undefined
    };
  },
  methods: {
    open({ sendDate }) {
      this.sendDate = sendDate;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    handleUploadFile(params) {
      let data = new FormData();
      data.append('file', params.file);
      data.append('sendDate', this.sendDate);

      this.ajax.newSalaryDetailImportImportAllSave(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.$message.success(res.object + '操作成功');
          this.close();
          this.$emit('refresh');
        } else {
          this.$message.error(res.message || '上传失败');
          this.fileList = [];
        }
      });
    },

    masterFileMax() {
      this.$message.warning(`请最多上传1个文件。`);
    },

    handleDownTemplate() {
      let aDom = document.createElement('a');
      aDom.href = '/ts-hrms/api/newSalaryDetailImport/exportTemplate';
      aDom.click();
    },

    close() {
      this.form = null;
      this.fileList = [];
      this.sendDate = undefined;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-import-all-salary-item-file {
    .el-dialog__body {
      height: 100px !important;

      .content {
        height: 100% !important;

        .down-box {
          position: relative;

          .down-template {
            position: absolute;
            left: 90px;
            top: 6px;
            color: $primary-blue;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
