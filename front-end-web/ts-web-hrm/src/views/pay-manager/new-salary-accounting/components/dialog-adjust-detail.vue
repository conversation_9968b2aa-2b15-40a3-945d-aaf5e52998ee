<template>
  <ts-dialog
    custom-class="dialog-adjust-detail"
    title="薪酬调整明细"
    width="80%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="tabs-container">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :resetData="resetAdjustData"
        @search="search"
      >
        <template slot="tmpItem">
          <ts-select
            style="width: 100%"
            v-model="searchForm.tmpItem"
            clearable
            placeholder="请选择"
          >
            <ts-option
              v-for="item of adjusItemsData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>
      </ts-search-bar>

      <TsVxeTemplateTable
        class="form-table"
        ref="table"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
  </ts-dialog>
</template>

<script>
import table from './dialog-adjust-detail-columns';

export default {
  mixins: [table],
  props: {},
  data() {
    return {
      visible: false,
      optionCycle: '',
      employeeNo: '',
      adjusItemsData: []
    };
  },
  methods: {
    open({ searchForm }) {
      this.searchForm = searchForm;
      this.employeeNo = this.searchForm.employeeNo;
      this.optionCycle = this.searchForm.optionCycle;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.getAdjusItemsData();
        this.handleRefreshTable();
      });
      this.visible = true;
    },
    //重置
    resetAdjustData() {
      return {
        employeeNo: this.employeeNo,
        optionCycle: this.optionCycle
      };
    },
    close() {
      this.form = null;
      this.fileList = [];

      this.optionId = undefined;
      this.sendDate = undefined;

      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-adjust-detail {
    .el-dialog__body {
      .item-content {
        display: flex;
        flex-wrap: wrap;
        .form-item-container {
          width: 25%;
        }
      }

      // .el-form-item__label {
      //   line-height: 18px;
      //   transform: translatey(4px);
      // }

      .ts-input {
        width: 120px !important;
        min-width: 120px !important;
        input {
          width: 120px !important;
        }
      }

      .tabs-container {
        height: 60%;
        // display: flex;
      }

      .item-tips {
        font-size: 16px;
        font-weight: 700;
        margin-right: 8px;
        display: flex;
        align-items: center;
        &.small {
          font-size: 14px;
          font-weight: 400;
        }

        &::before {
          content: '';
          display: inline-block;
          color: rgb(82, 96, 255);
          height: 16px;
          width: 6px;
          border-radius: 4px;
          background-color: rgb(82, 96, 255);
          margin-right: 8px;
        }
      }

      .person-info-descriptions {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
