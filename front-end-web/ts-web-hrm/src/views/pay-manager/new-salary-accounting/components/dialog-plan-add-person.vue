<template>
  <ts-dialog
    custom-class="dialog-plan-add-person"
    width="1260px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template v-slot:title>
      <div class="title-container">
        <div class="title-open-model">添加人员</div>
        <div class="title-tips">
          提示：保存后，选中的人员将会变更到本薪酬方案！
        </div>
      </div>
    </template>
    <div class="content">
      <div class="left">
        <ts-search-bar
          v-model="searchForm"
          :formList="searchList"
          :elementCol="14"
          :columns="2"
          @search="search"
          :resetData="{}"
        >
          <template slot="orgId">
            <ts-ztree-select
              placeholder="请选择科室"
              ref="treeSelect"
              :inpText.sync="searchForm.name"
              :inpVal.sync="searchForm.code"
              :data="treeData"
              defaultExpandAll
              @change="handleChangeTree"
            />
          </template>
        </ts-search-bar>

        <base-table
          ref="table"
          class="form-table"
          border
          stripe
          :columns="columns"
          @refresh="handleRefreshTable"
          @selection-change="handleSelectionChange"
          :row-key="rowKey"
        />
      </div>
      <div class="right">
        <div class="right-title">
          <span class="chooseNum">
            已选人员 (
            <span style="color: #333;font-weight: 600;"
              >{{ selectList.length }}
            </span>
            )
          </span>
          <span class="clear" @click="clearAll">清空</span>
        </div>
        <ul class="right-content">
          <li
            class="select-item-person"
            v-for="(item, index) in selectList"
            :key="index"
          >
            <div class="person-info">
              {{ item.employeeNo }} {{ item.employeeName }}
            </div>
            <span class="person-operate" @click="deleteItem(item)">
              <i class="el-icon-circle-close"></i>
            </span>
          </li>
        </ul>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      optionId: undefined,

      treeData: [],
      selectList: [],
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名'
          }
        },
        {
          label: '科室',
          value: 'orgId'
        },
        {
          label: '员工状态',
          value: 'employeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择',
            multiple: true
          },
          childNodeList: []
        }
      ],

      columns: [
        {
          type: 'selection',
          align: 'center',
          width: 50,
          reserveSelection: true,
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          fixed: 'left'
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          width: 100,
          fixed: 'left'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          label: '部门',
          prop: 'orgName',
          align: 'center',
          fixed: 'left'
        },
        {
          label: '岗位',
          prop: 'personalIdentityText',
          align: 'center'
        },
        {
          label: '编制类型',
          prop: 'establishmentTypeText',
          align: 'center'
        },
        {
          label: '员工状态',
          prop: 'employeeStatusText',
          align: 'center'
        },
        {
          label: '入职日期',
          prop: 'entryDate',
          align: 'center',
          width: 120
        },
        {
          label: '转正日期',
          prop: 'positiveTime',
          align: 'center',
          width: 120
        },
        {
          label: '离/退休日期',
          prop: 'retirementTime',
          align: 'center',
          width: 120
        },
        {
          label: '薪酬方案',
          prop: 'optionName',
          align: 'center',
          width: 150
        }
      ]
    };
  },
  methods: {
    async open({ data }) {
      this.optionId = data.optionId;
      this.visible = true;

      this.$nextTick(() => {
        this.init();
      });
    },

    init() {
      // 科室结构
      this.getDeptTreeList();
      this.handleDictItemEmployeeStatus();
      this.search();
    },

    resetData() {
      return {};
    },

    handleSelectionChange(e) {
      this.selectList = e;
    },

    rowKey(row) {
      return row.employeeId;
    },

    deleteItem(item) {
      let dataSource = this.$refs.table.rows;
      let rows = dataSource.filter(i => {
        return i.employeeId == item.employeeId;
      });
      let row = rows.length ? rows[0] : item;
      this.$refs.table.$refs.table.toggleRowSelection(row, false);
    },

    clearAll() {
      this.selectList = [];
      this.$refs.table.clearSelection();
    },

    getDeptTreeList() {
      this.ajax.organizationZTreeList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取科室信息失败');
        }
        this.treeData = res.object;
      });
    },

    // 获取人员状态
    async handleDictItemEmployeeStatus() {
      let res = await this.ajax.dictItemEmployeeStatus();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let personStatusOptions = (res.rows || []).map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });

      let index = this.searchList.findIndex(f => f.value == 'employeeStatus');
      this.searchList[index].childNodeList = personStatusOptions;
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          optionId: this.optionId,
          pageNo,
          pageSize
        };
      if (searchForm.employeeStatus) {
        searchForm.employeeStatus = searchForm.employeeStatus.join(',');
      } else {
        delete searchForm.employeeStatus;
      }

      let res = await this.ajax.newSalaryOptionPayrollGetCheperList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return;
      }

      let rows = (res.rows || []).map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    handleChangeTree(e) {
      const { id } = e;
      this.searchForm.orgId = id;
    },

    async submit() {
      try {
        if (this.selectList && this.selectList.length === 0) {
          this.$message.warning('请选择人员!');
          return false;
        }
        let ids = this.selectList.map(m => m.employeeId);
        let Api = this.ajax.salaryOptionEmpPerSave;
        this.submitLoading = true;
        const res = await Api(this.optionId, ids);
        this.submitLoading = false;
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return false;
        }
        this.$message.success('操作成功!');
        this.$emit('refresh');
        this.close();
      } catch (error) {
        console.log(error, 'error');
        return false;
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-plan-add-person {
    height: 680px;

    .title-container {
      display: flex;
      align-items: center;
      .title-open-model {
        font-weight: 800;
        font-size: 20px;
        color: $primary-blue;
      }

      .title-tips {
        font-size: 16px;
        margin-left: 8px;
        color: red;
      }
    }

    .el-dialog__body {
      height: calc(100% - 88px);
      overflow: auto;

      .content {
        height: 100%;
        display: flex;
        .left {
          width: 75%;
          height: 100%;
          display: flex;
          flex-direction: column;
          .form-table {
            flex: 1;
            overflow: hidden;
            transform: scale(1);
          }
        }
        .right {
          margin-left: 10px;
          width: 25%;
          height: 100%;
          display: flex;
          flex-direction: column;
          .right-title {
            background: #eee;
            display: flex;
            justify-content: space-between;
            padding: 5px 10px;
            border-radius: 5px;
            .clear {
              cursor: pointer;
              color: #5260ff;
            }
          }
          .right-content {
            flex: 1;
            border: 1px solid #ccc;
            margin-top: 10px;
            border-radius: 8px;
            padding: 5px 10px;
            overflow-y: auto;
            .select-item-person {
              display: flex;
              justify-content: space-between;
              text-align: left;
              .person-operate {
                color: #5260ff;
                cursor: pointer;
                color: #666666;
                font-size: 20px;
              }
              .person-info {
                flex: 1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
