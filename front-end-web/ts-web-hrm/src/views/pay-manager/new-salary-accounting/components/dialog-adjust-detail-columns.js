import moment from 'moment';
export default {
  data() {
    return {
      searchForm: {},
      resetData: {
        optionCycle: this.optionCycle,
        employeeNo: this.employeeNo
      },
      searchList: [
        {
          label: '算薪周期',
          value: 'optionCycle',
          element: 'ts-month-picker',
          elementProp: {
            placeholder: '请选择月份',
            valueFormat: 'YYYY-MM',
            allowClear: false,
            disabled: true
          },
          event: {
            change: e => {
              this.$set(this.searchForm, 'optionCycle', e);
              this.search();
            }
          }
        },
        {
          label: '姓名/工号',
          value: 'employeeNo',
          element: 'TsInput',
          elementProp: {
            placeholder: '请输入姓名/工号',
            disabled: true
          }
        },
        {
          label: '项目类型',
          value: 'tmpItem'
        }
      ],

      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center'
        },
        {
          label: '所属组织',
          prop: 'orgName',
          width: 160,
          align: 'center'
        },
        {
          label: '工号',
          prop: 'employeeNo',
          width: 130,
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          width: 120,
          align: 'center'
        },
        {
          label: '性别',
          prop: 'gender',
          width: 60,
          align: 'center'
        },
        {
          label: '岗位名称',
          prop: 'positionName',
          width: 130,
          align: 'center'
        },
        {
          label: '项目类型',
          prop: 'tmpItemName',
          width: 130,
          align: 'center'
        },
        {
          label: '金额',
          prop: 'salaryItemAmount',
          width: 80,
          align: 'center'
        },
        {
          label: '计算类型',
          prop: 'countType',
          align: 'center',
          width: 80,
          render: (h, { row }) => {
            let text = row.countType == '1' ? '加项' : '减项';
            let styles = row.countType == '2' ? { color: 'red' } : {};
            return h('div', { style: styles }, text);
          }
        },
        {
          label: '是否已锁定',
          prop: 'isUse',
          align: 'center',
          width: 90,
          render: (h, { row }) => {
            if (row.isUse === '1') {
              return h('div', {}, '已锁定');
            } else if (row.countType === '2') {
              return h('div', {}, '未锁定');
            } else {
              return h('div', {}, '未锁定');
            }
          }
        },
        {
          label: '备注',
          prop: 'remark',
          width: 120,
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center',
          width: 160
        },
        {
          label: '更新时间',
          prop: 'updateDate',
          align: 'center',
          width: 160
        }
      ]
    };
  },
  methods: {
    //检索
    async search() {
      this.$refs.table.pageNo = 1;
      await this.handleRefreshTable();
    },
    async handleSelectChange(e) {
      this.selectList = e;
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let formData = {
        ...this.searchForm,
        pageNo,
        pageSize
      };
      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        }
      });
      let res = await this.ajax.getNewsalaryTemporaryAdjustList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    /**@desc 获取项目类型 */
    getAdjusItemsData() {
      this.ajax.getDataByDataLibrary('tmp_adjust_item').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '项目类型获取失败');
          return;
        }
        this.adjusItemsData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    }
  }
};
