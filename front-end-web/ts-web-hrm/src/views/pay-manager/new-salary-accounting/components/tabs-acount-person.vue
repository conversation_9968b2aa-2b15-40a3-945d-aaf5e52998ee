<template>
  <div class="tabs-container tabs-acount-person">
    <ul class="search-type-tabs">
      <li
        :class="{
          'search-item': 'true',
          active: item.active == searchForm.menu
        }"
        v-for="(item, index) in typeSearchBar"
        @click="handleClickSearchTabs(item)"
        :key="index"
      >
        {{ item.label }}（{{ item.value }}）
      </li>
    </ul>

    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :resetData="resetData"
      @search="search"
    >
      <template slot="orgId">
        <ts-ztree-select
          placeholder="请选择科室"
          ref="treeSelect"
          :inpText.sync="searchForm.name"
          :inpVal.sync="searchForm.code"
          :data="treeData"
          defaultExpandAll
          @change="handleChangeTree"
        />
      </template>

      <template slot="employeeStatus">
        <ts-select
          style="width: 100%"
          v-model="searchForm.employeeStatus"
          clearable
          multiple
          placeholder="请选择"
        >
          <ts-option
            v-for="item of personStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></ts-option>
        </ts-select>
      </template>

      <template slot="right">
        <!-- <ts-button type="primary" @click="handleExport">导出</ts-button> -->
        <ts-button type="primary" @click="handlePlanAddPerson">
          添加人员
        </ts-button>
        <ts-button type="primary" @click="handleRemovePerson('selection')">
          移除人员
        </ts-button>
      </template>
    </ts-search-bar>

    <!-- <base-table
      ref="table"
      class="form-table"
      border
      stripe
      :columns="columns"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    /> -->
    <TsVxeTemplateTable
      class="form-table"
      ref="table"
      id="account_person"
      :columns="columns"
      @selection-change="handleSelectionChange"
      @refresh="handleRefreshTable"
    />

    <dialog-plan-add-person
      ref="DialogPlanAddPerson"
      @refresh="handleRefreshTable"
    />

    <dialog-salary-adjustment
      ref="DialogSalaryAdjustment"
      @refresh="handleRefreshTable"
    />

    <dialog-salary-profile-details ref="DialogSalaryProfileDetails" />
  </div>
</template>

<script>
import DialogPlanAddPerson from './dialog-plan-add-person.vue';
import DialogSalaryAdjustment from '@/views/pay-manager/salary-profile/components/dialog-salary-adjustment.vue';
import DialogSalaryProfileDetails from '@/views/pay-manager/salary-profile/components/dialog-salary-profile-details.vue';
export default {
  components: {
    DialogPlanAddPerson,
    DialogSalaryAdjustment,
    DialogSalaryProfileDetails
  },
  props: {
    details: {
      type: Object,
      default: () => {}
    },
    treeData: {
      type: Array,
      default: () => []
    },
    personStatusOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      multipleSelection: [],
      searchForm: {
        menu: '1'
      },
      resetData: {
        menu: '1'
      },
      searchList: [
        {
          label: '',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入工号/姓名'
          }
        },
        {
          label: '员工状态',
          value: 'employeeStatus'
        },
        {
          label: '部门',
          value: 'orgId'
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          fixed: 'left'
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          width: 100,
          fixed: 'left'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center',
          width: 80,
          fixed: 'left'
        },
        {
          label: '部门',
          prop: 'orgName',
          align: 'center'
        },
        {
          label: '岗位',
          prop: 'personalIdentityText',
          align: 'center'
        },
        {
          label: '编制类型',
          prop: 'establishmentTypeText',
          align: 'center'
        },
        {
          label: '员工状态',
          prop: 'employeeStatusText',
          align: 'center'
        },
        {
          label: '入职日期',
          prop: 'entryDate',
          align: 'center',
          width: 120
        },
        {
          label: '转正日期',
          prop: 'positiveTime',
          align: 'center',
          width: 120
        },
        {
          label: '离/退休日期',
          prop: 'retirementTime',
          align: 'center',
          width: 120
        },
        {
          label: '薪酬方案',
          prop: 'optionName',
          align: 'center',
          width: 200
        },
        {
          label: '操作',
          align: 'left',
          width: 120,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '移除',
                event: () => this.handleRemovePerson('single', row.employeeId)
              }
            ];

            this.searchForm.menu == '4' &&
              actionList.push({
                label: '详情',
                event: this.handleDetails
              });

            this.searchForm.menu == '2' &&
              actionList.push({
                label: '定薪',
                event: this.handleSettingSalary
              });

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],

      typeSearchBar: [
        { active: '1', label: '全部算薪人员', key: 'qbsx', value: 0 },
        { active: '2', label: '在职未定薪人员', key: 'zzwdx', value: 0 },
        { active: '3', label: '离/退休人员', key: 'lxt', value: 0 },
        { active: '4', label: '调薪/定薪人员', key: 'tdx', value: 0 }
      ]
    };
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    handleChangeTree(e) {
      const { id } = e;
      this.searchForm.orgId = id;
    },

    handleClickSearchTabs(item) {
      this.$refs.table.pageNo = 1;
      this.searchForm.menu = item.active;
      this.handleRefreshTable();
    },

    handleExport() {
      this.loading = true;
      let data = {
        optionId: this.details.optionId,
        ...this.searchForm
      };

      let queryData = Object.keys(data)
        .map(key => key + '=' + data[key])
        .join('&');
      let a = document.createElement('a');
      a.href = '/ts-hrms/api/newSalaryOptionPayroll/hsryExport?' + queryData;
      a.click();
      this.loading = false;
    },

    async handleGetCheckPersonnelCount() {
      const res = await this.ajax.checkPersonnelCount({
        optionId: this.details.optionId
      });

      if (!res.success) {
        this.$message.error(res.message || '获取方案人员统计情况失败!');
        return;
      }

      for (const key in res.object) {
        const val = res.object[key];
        this.typeSearchBar.find(f => f.key === key).value = val;
      }
    },

    async handleRemovePerson(operateType, employeeId) {
      if (!this.multipleSelection.length && operateType == 'selection') {
        this.$message.warning('请选择要操作的数据!');
        return false;
      }

      try {
        await this.$confirm(
          `人员移除薪酬方案后，将不会核算薪酬，是否确定移除？`,
          '是否移除?',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        let ids = [];
        if (operateType == 'selection') {
          ids = this.multipleSelection.map(item => item.employeeId);
        } else {
          ids.push(employeeId);
        }

        const res = await this.ajax.salaryOptionEmpDeleteOption(
          this.details.optionId,
          ids
        );
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }
        this.$message.success('操作成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e, '?e');
      }
    },

    handlePlanAddPerson() {
      this.$refs.DialogPlanAddPerson.open({
        data: {
          optionId: this.details.optionId
        }
      });
    },

    handleDetails(row) {
      row.employee_id = row.employeeId;
      this.$refs.DialogSalaryProfileDetails.open({
        title: '薪酬档案',
        data: row
      });
    },

    // 定薪
    handleSettingSalary(row) {
      let data = {
        ...row,
        employee_id: row.employeeId
      };
      this.$refs.DialogSalaryAdjustment.open({
        data,
        title: '定薪',
        type: 'add'
      });
    },

    async handleRefreshTable() {
      try {
        await this.handleGetCheckPersonnelCount();
      } catch (error) {
        throw new Error(error);
      }

      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          optionId: this.details.optionId
        };

      if (searchForm.employeeStatus) {
        searchForm.employeeStatus = searchForm.employeeStatus.join(',');
      } else {
        delete searchForm.employeeStatus;
      }
      let res = await this.ajax.newSalaryOptionPayrollCheckPersonnel(
        searchForm
      );
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = (res.rows || []).map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-acount-person {
  .search-type-tabs {
    display: flex;
    margin-bottom: 8px;
    .search-item {
      cursor: pointer;
      margin-right: 8px;
      border-radius: 4px;
      border: 2px solid #e1e1e1;
      padding: 8px 12px;
      font-weight: 800;
      line-height: 32px;
      height: 32px;
      &.active {
        color: $primary-blue;
        border: 2px solid $primary-blue;
      }
    }
  }
}
</style>
