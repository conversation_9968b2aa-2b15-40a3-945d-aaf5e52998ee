<template>
  <vxe-modal
    :title="title"
    width="400"
    v-model="visible"
    showFooter
    @close="close"
  >
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="100px">
          <ts-form-item
            prop="filterName"
            label="筛选器名称"
            :rules="rules.required"
          >
            <ts-input v-model="form.filterName" />
          </ts-form-item>
        </ts-form>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <ts-button type="primary" :loading="submitLoading" @click="submit"
          >提 交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      title: '保存筛选器'
    };
  },
  methods: {
    async open({ data = {} }) {
      this.form = deepClone(data);
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        let API = this.ajax.personalFilterSave;
        if (data.personalFilterId) {
          data.id = data.personalFilterId;
          API = this.ajax.personalFilterUpdate;
        }
        const res = await API(data);
        if (res.success) {
          this.$message.success('操作成功');
          this.close();
          this.$emit('refresh', data);
        } else {
          this.$message.error(res.message || '操作失败');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .content {
    height: 50px;
  }
}
</style>
