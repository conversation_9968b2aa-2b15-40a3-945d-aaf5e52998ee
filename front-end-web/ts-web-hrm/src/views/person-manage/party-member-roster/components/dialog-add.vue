<template>
  <vxe-modal
    :title="title"
    width="800"
    v-model="visible"
    showFooter
    @close="close"
  >
    <template #default>
      <div class="content">
        <el-scrollbar style="height: 100%;" wrap-style="overflow-x: hidden;">
          <ts-form ref="form" :model="form" labelWidth="100px">
            <ts-row>
              <ts-col :span="12">
                <ts-form-item label="姓名" :rules="rules.required" prop="empId">
                  <base-select
                    v-model="form.empId"
                    :inputText.sync="form.empName"
                    :loadMethod="handleGetPersonList"
                    label="empName"
                    value="empCode"
                    searchInputName="empName"
                    :clearable="false"
                    placeholder="请选择"
                    style="width: 100%;"
                    @select="handleOk"
                    :disabled="isDetail"
                  />
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item prop="empCode" label="工号">
                  <ts-input
                    v-model="form.empCode"
                    placeholder="请输入工号"
                    disabled
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item prop="deptName" label="科室">
                  <ts-input v-model="form.deptName" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item prop="idcard" label="身份证">
                  <ts-input v-model="form.idcard" disabled />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item prop="postName" label="岗位名称">
                  <ts-input v-model="form.postNameText" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item prop="retireDate" label="退休时间">
                  <ts-input v-model="form.retireDate" disabled />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  prop="politicalStatus"
                  label="政治面貌"
                  :rules="rules.required"
                >
                  <ts-select
                    v-model="form.politicalStatus"
                    :disabled="isDetail"
                    style="width: 100%;"
                  >
                    <ts-option
                      v-for="(item, index) in politicalList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-col>
              <ts-col :span="12">
                <ts-form-item
                  prop="partyDate"
                  label="入党时间"
                  :rules="rules.required"
                >
                  <el-date-picker
                    style="width: 100%"
                    :disabled="isDetail"
                    v-model="form.partyDate"
                    valueFormat="yyyy-MM-dd"
                    placeholder="请选择"
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-form-item prop="filesId" label="附件">
              <base-upload v-model="form.filesId" :onlyRead="isDetail" />
            </ts-form-item>
          </ts-form>
        </el-scrollbar>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <ts-button
          type="primary"
          v-if="!isDetail"
          :loading="submitLoading"
          @click="submit"
          >提 交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    dictObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      politicalList: [],
      isDetail: false,
      rules: {
        required: { required: true, message: '必填' }
      },
      title: '新增'
    };
  },
  methods: {
    async open({ title = '', data = {}, type }) {
      this.title = title;
      await this.getDict();
      this.form = deepClone(data);
      this.type = type;
      if (type == 'details') {
        this.isDetail = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async getDict() {
      let res = await this.ajax.getDictType('political_status');
      this.politicalList = res.object.map(e => {
        return {
          label: e.dictName,
          value: e.dictValue
        };
      });
    },
    async handleGetPersonList(data) {
      let res = await this.ajax.getMyEmployeeList(
        { empStatus: 1 },
        {
          ...data
        }
      );
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    async handleOk(item) {
      let res = await this.ajax.selectEmployeeById(item.id);
      if (res.success) {
        this.$set(this.form, 'empId', res.object.employee_id);
        this.$set(this.form, 'politicalStatus', res.object.political_status);
        this.$set(this.form, 'partyDate', res.object.party_date);
        this.$set(this.form, 'retireDate', res.object.retire_date);
        this.$set(this.form, 'empCode', res.object.employee_no);
        this.$set(this.form, 'empName', res.object.employee_name);
        this.$set(this.form, 'deptName', res.object.name);
        this.$set(this.form, 'idcard', res.object.identity_number);
        this.$set(this.form, 'postName', res.object.personal_identity);
        this.$set(this.form, 'postNameText', res.object.postNameText || '');
        this.$set(
          this.form,
          'politicalStatusText',
          res.object.politicalStatusText || ''
        );
      }
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        let API = this.ajax.partyMembersSave;
        if (this.type == 'edit') {
          API = this.ajax.partyMembersUpdate;
        }
        const res = await API(data);
        if (res.success) {
          this.$message.success('操作成功');
          this.close();
          this.$emit('refresh');
        } else {
          this.$message.error(res.message || '操作失败');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.isDetail = false;
      this.type = undefined;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .mini-select .el-input {
    min-width: 90px;
  }
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
  .content {
    height: 240px;
  }
}
</style>
