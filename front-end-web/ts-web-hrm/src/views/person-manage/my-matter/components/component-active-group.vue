<template>
  <div class="component-active-group flex-column">
    <div class="operate-container">
      <ts-input
        placeholder="请输入分组名称进行搜索"
        @input="searchInput"
        class="search-input"
        v-model="searchVal"
      >
        <template slot="append">
          <i
            @click="handleClickInputCircle"
            class="el-icon-circle-close input-circle"
          />
        </template>
      </ts-input>
    </div>
    <ul class="group-container">
      <template v-if="Array.isArray(groupData) && groupData.length">
        <li
          :class="{
            'group-item': true,
            'dragger-item': !!group.id,
            active: activeId == group.id
          }"
          v-for="group in groupData"
          :key="group.id"
          v-show="group.show"
        >
          <div class="info-item" @click.stop="handleClickGroupItem(group)">
            <img
              class="group-img"
              src="@/assets/img/person-archive/archive.svg"
              alt=""
            />
            <p class="group-name">
              {{ group.groupName }}
            </p>
          </div>
        </li>
      </template>
      <template v-else>
        暂无数据
      </template>
    </ul>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  model: {
    prop: 'activeId',
    event: 'change'
  },
  props: {
    activeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      groupData: [],
      searchInput: null,
      searchVal: ''
    };
  },
  methods: {
    async init() {
      this.groupData = [];
      this.$emit('change', '');
      this.searchInput = this.debounce(this.input, 300);

      let res = await this.ajax.customEmployeeGroupGetAuthorityList();
      if (!res.success) {
        this.$message.error(res.message || '获取档案分组失败,请联系管理员!');
        return;
      }
      let groupData = deepClone(res.object || []);
      if (Array.isArray(groupData) && groupData.length) {
        groupData.unshift({
          id: '',
          groupName: '全部'
        });
        groupData.forEach(f => {
          f.show = true;
        });

        this.groupData = groupData;
      }
    },

    input() {
      const searchKey = this.searchVal.trim();

      if (searchKey) {
        this.groupData.forEach(f => {
          f.show = f.groupName.includes(searchKey);
        });
      } else {
        this.groupData.forEach(f => (f.show = true));
      }
    },

    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    },

    handleClickInputCircle() {
      this.searchVal = '';
      this.input();
    },

    handleClickGroupItem(group) {
      if (group.id === this.activeId) return;
      this.$emit('change', group.id);
    }
  }
};
</script>

<style lang="scss" scoped>
.component-active-group {
  height: 100%;

  .operate-container {
    margin-bottom: 8px;
  }

  .group-container {
    display: flex;
    flex-direction: column;
    .group-item {
      border-bottom: 1px solid #eee;
      color: #333;
      height: 34px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .info-item {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        .group-img {
          margin-right: 8px;
          width: 16px;
          height: 16px;
        }

        .group-name {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin: 0;
          padding: 0;
        }
      }

      &.active {
        background-color: #6d7afa !important;
        color: #fff !important;
      }
      &:hover {
        background-color: #eee;
      }
    }
  }
}
</style>
