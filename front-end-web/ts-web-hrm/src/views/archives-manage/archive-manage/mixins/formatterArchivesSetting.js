import { fieldTypeComponentName } from '@/views/archives-manage/config/dictionary.js';
import cloneDeep from 'lodash-es/cloneDeep';

export default {
  methods: {
    async handleInitCustomEmployeeSetting(personalEditSetting) {
      const promises = personalEditSetting.flatMap(group => {
        if (!Array.isArray(group.fields) || !group.fields.length) return [];

        return group.fields.map(async field => {
          await this.handleSetDataSourceOptionValue(field);

          Object.assign(field, {
            prop: field.fieldName,
            pcComponent: fieldTypeComponentName[field.fieldType],
            disabled: field.isOnly == 1,
            required: field.isMust == 1,
            staticRequired: field.isMust == 1,
            placeholder: field.promptText || undefined,
            formatterType: field.dataFormat || undefined,
            maxlength: field.fieldLength
          });
        });
      });

      await Promise.all(promises);
    },

    async handleSetDataSourceOptionValue(field) {
      if (['radio', 'checkbox', 'select'].includes(field.fieldType)) {
        if (['concurrent_position', 'position_id'].includes(field.fieldName)) {
          const { rows = [] } = await this.ajax.archiveReportPositionList();
          field.formItemOptions = rows.map(m => ({
            label: m.positionName,
            value: m.positionId
          }));
        }
        // else if ('plgw' === field.fieldName) {
        // let res = await this.ajax.archiveReportPostCategoryGetList();
        // if (!res.success) {
        //   this.$message.error(res.message || '岗位类别码表获取失败');
        //   return;
        // }
        // let data = cloneDeep(res.object || []);
        // field.formItemOptions = data.map(m => {
        //   return {
        //     ...m,
        //     value: m.postCategoryId,
        //     label: m.postCategoryName
        //   };
        // });
        // }
        else if ('gwdj' === field.fieldName) {
          let postCategory = this.form?.[field.groupId]?.[0]?.plgw || '';
          let res = await this.ajax.archiveReportPostGetShowList({
            postCategory
          });
          if (!res.success) {
            this.$message.error(res.message || '岗位等级码表获取失败');
            return;
          }
          let data = cloneDeep(res.object || []);
          field.formItemOptions = data.map(m => {
            return {
              ...m,
              value: m.postId,
              label: m.postName
            };
          });
        } else if ('salary_level_type' === field.fieldName) {
          let res = await this.ajax.archiveReportSalaryLevelCategory();
          if (!res.success) {
            this.$message.error(res.message || '薪级类别码表获取失败');
            return;
          }
          let data = cloneDeep(res.object || []);
          field.formItemOptions = data.map(m => {
            return {
              ...m,
              value: m.dictValue,
              label: m.dictName
            };
          });
        } else if ('salary_level_id' === field.fieldName) {
          let salaryLevelCategory =
            this.form?.[field.groupId]?.[0]?.salary_level_type || '';
          let res = await this.ajax.archiveReportSalaryLevelCombobox({
            salaryLevelCategory
          });
          if (!res.success) {
            this.$message.error(res.message || '薪级等级码表获取失败');
            return;
          }
          let data = cloneDeep(res.object || []);
          field.formItemOptions = data.map(m => {
            return {
              ...m,
              value: m.salaryLevelId,
              label: m.salaryLevelName
            };
          });
        } else if (field.dataSource == '1' && field.optionValue) {
          field.formItemOptions = field.optionValue
            .split('|')
            .map(option =>
              option.includes(':')
                ? option.split(':').map(item => item.trim())
                : []
            )
            .filter(([value, label]) => value && label)
            .map(([value, label]) => ({ value, label }));
        } else if (field.dataSource == '4' && field.dictSource) {
          let res = await this.ajax.getDataByDataLibrary(field.dictSource);
          if (!res.success) {
            this.$message.error(res.message || '码表获取失败');
            return;
          }
          let data = cloneDeep(res.object || []);
          field.formItemOptions = data.map(m => {
            return {
              ...m,
              label: m.itemName,
              value: m.itemNameValue
            };
          });
          if (field.fieldName === 'employee_status' && !this.isNormalInstance) {
            field.formItemOptions = field.formItemOptions.filter(
              f => f.itemNameValue == '89'
            );

            if (this.renderType === 'ADMIN' && this.type === 'add') {
              this.$set(this.form[field.groupId][0], 'employee_status', '89');
            }
          }
        }
      } else if (field.fieldType === 'jobType_1') {
        let res = await this.ajax.jobtitleBasicGetJobtitleTree();
        if (!res.success) {
          this.$message.error(res.message || '码表获取失败');
          return;
        }
        let data = cloneDeep(res.object || []);
        field.formItemOptions = data;
      } else if (field.fieldType === 'deptChose') {
        let res = await this.ajax.getDeptTreeList();
        if (!res.success) {
          this.$message.error(res.message || '获取科室信息失败');
        }
        field.formItemOptions = res.object;
      }
    }
  }
};
