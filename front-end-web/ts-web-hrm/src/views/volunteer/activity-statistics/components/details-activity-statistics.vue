<template>
  <ts-dialog
    custom-class="dialog-volunteer-person-info"
    :visible.sync="visible"
    title="查看详情"
    type="large"
  >
    <div class="content">
      <div class="status-container">
        <ul class="status-list">
          <li
            v-for="item of sectionList"
            :key="item.name"
            :class="{
              active: activeTab == item.name
            }"
            @click="handlePreviewDetail(item)"
            class="status-item"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
      <div class="detail-container flex">
        <el-scrollbar
          ref="scroll"
          style="flex: 1;"
          wrap-style="height: calc(100% + 17px);"
        >
          <activity-info ref="activityInfo" :detail="detail" />
          <person-list ref="personList" :activityId="activityId" />
          <group-list ref="groupDetails" :list="groupList" />
        </el-scrollbar>
      </div>
    </div>
    <template slot="footer">
      <ts-button @click="close">取消</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import ActivityInfo from './activity-info.vue';
import PersonList from './person-list.vue';
import GroupList from './group-list';

export default {
  components: {
    ActivityInfo,
    PersonList,
    GroupList
  },
  data() {
    return {
      visible: false,
      activeTab: 'activityInfo',
      activityId: '',

      sectionList: [
        {
          label: '活动信息',
          name: 'activityInfo'
        },
        {
          label: '参与人员',
          name: 'personList'
        },
        {
          label: '参与团体',
          name: 'groupDetails'
        }
      ],
      detail: {},
      groupList: []
    };
  },
  methods: {
    async open({ data = {} }) {
      let { id: activityId } = data;
      this.activityId = activityId;
      await this.getVoluntariesActivityId();
      await this.getVoluntariesActivityRecordTeam();
      this.visible = true;

      this.$nextTick(() => {
        this.$refs.personList.handleTableRefresh();
        let wrap = this.$refs.scroll.wrap;
        wrap.scrollTop = 0;
      });
    },

    getVoluntariesActivityId() {
      this.ajax.voluntariesActivityId(this.activityId).then(res => {
        if (res.success && res.statusCode === 200) {
          this.detail = res.object;

          let ydm = res.object.activityStartTime.slice(0, 10),
            start = res.object.activityStartTime.slice(11, 16),
            end = res.object.activityEndTime.slice(11, 16);
          this.detail.startAndEndTime = `${ydm} ${start}-${end}`;
          this.detail.activityNeedSwcLabel =
            res.object.activityNeedSwc == '1' ? '有' : '没有';
        } else {
          this.$message.error(res.message || '获取信息失败!');
        }
      });
    },

    getVoluntariesActivityRecordTeam() {
      this.ajax
        .voluntariesActivityRecordTeam({ activityId: this.activityId })
        .then(res => {
          this.groupList = res;
        });
    },

    handlePreviewDetail({ name }) {
      name && (this.activeTab = name);
      let dom = this.$refs[name];
      dom = dom && dom.$el;

      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;
      if (!scrollDom.wrap.scrollHeight) {
        return;
      }
      scrollDom.wrap.scrollTo({ top: domScrollTop, behavior: 'smooth' });
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-volunteer-person-info {
  .el-dialog__body {
    background-color: transparent !important;
    display: flex;
    overflow: hidden;
    height: calc(100vh - 188px);
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;

    .status-container {
      flex-shrink: 0;
      margin-right: 16px;
      .status-list {
        background-color: $ts-table-header-hover-bg;
        cursor: pointer;
        padding: 2px;
        .status-item {
          line-height: 30px;
          padding: 0 24px;
          &.active {
            background-color: #fff;
            color: $primary-blue;
          }
        }
      }
    }
    .detail-container {
      display: flex;
      flex: 1;
      background-color: #fff;
      padding: 16px 24px;
      overflow: hidden;

      .el-scrollbar__view > div + div {
        margin-top: 24px;
      }
    }
  }
}
</style>
