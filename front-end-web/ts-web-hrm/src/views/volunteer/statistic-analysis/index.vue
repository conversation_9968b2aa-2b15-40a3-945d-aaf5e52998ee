<template>
  <div class="statistic-analysis">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      @search="refresh"
      :resetData="resetData"
    >
      <template slot="but1">
        <!-- primary -->
        <ts-button type="" @click="handleChangeDate('year')">
          本年
        </ts-button>
      </template>
      <template slot="but2">
        <ts-button type="" @click="handleChangeDate('month')">
          本月
        </ts-button>
      </template>
    </ts-search-bar>

    <ul class="statistic-container">
      <li
        class="statistic-item"
        v-for="item in statisticItemsArr"
        :key="item.id"
        :style="`background: ${item.background};`"
      >
        <span class="value" :style="`color: ${item.color}`">
          {{ item.value }}
        </span>
        {{ item.unit }}
        <p>{{ item.label }}</p>
      </li>
    </ul>

    <div class="container">
      <div>
        <span class="tabs-tips">
          院内志愿者活动次数(次) TOP 10
        </span>
        <div ref="content1" />
      </div>

      <div>
        <span class="tabs-tips">
          院外志愿者活动次数(次) TOP 10
        </span>
        <div ref="content2" />
      </div>

      <div>
        <span class="tabs-tips">
          院内志愿者活动时长(小时) TOP 10
        </span>
        <div ref="content3" />
      </div>

      <div>
        <span class="tabs-tips">
          院外志愿者活动时长(小时) TOP 10
        </span>
        <div ref="content4" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,

      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '活动时间',
          value: 'dateList',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        },
        {
          label: '',
          value: 'but1'
        },
        {
          label: '',
          value: 'but2'
        }
      ],

      statisticItemsArr: [
        {
          value: '',
          unit: '次',
          label: '活动次数',
          background: '#F0FAFE',
          color: '#5363F6'
        },
        {
          value: '',
          unit: '小时',
          label: '活动时长',
          color: '#5363F6',
          background: '#F0FAFE'
        },
        {
          value: '',
          unit: '人',
          label: '院内志愿者人数',
          color: '#BD4D9A',
          background: 'rgba(226, 163, 255, 0.1)'
        },
        {
          value: '',
          unit: '人',
          label: '院外志愿者人数',
          color: '#BD4D9A',
          background: 'rgba(226, 163, 255, 0.1)'
        },
        {
          value: '',
          unit: '个',
          label: '团体志愿者数',
          color: '#BD4D9A',
          background: 'rgba(226, 163, 255, 0.1)'
        },
        {
          value: '',
          unit: '人',
          label: '女性志愿者人数',
          color: '#BE5348',
          background: 'rgba(255, 239, 237, 1)'
        },
        {
          value: '',
          unit: '个',
          label: '男性志愿者人数',
          color: '#BE5348',
          background: 'rgba(255, 239, 237, 1)'
        },
        {
          value: '',
          unit: '人',
          label: '35岁以下人数',
          color: '#70B14D',
          background: '#F0FEF6'
        },
        {
          value: '',
          unit: '人',
          label: '35岁到60岁人数',
          color: '#70B14D',
          background: '#F0FEF6'
        },
        {
          value: '',
          unit: '人',
          label: '60岁以上人数',
          color: '#70B14D',
          background: '#F0FEF6'
        }
      ],
      content1: null,
      content2: null,
      content3: null,
      content4: null,
      data: {}
    };
  },
  created() {
    const nowYear = this.$dayjs().startOf('year'),
      start = nowYear.format('YYYY-MM-DD');
    let now = this.$dayjs(new Date()).format('YYYY-MM-DD');

    this.searchForm.dateList = [start, now];
    this.$set(this.resetData, 'dateList', [start, now]);
  },
  methods: {
    handleChangeDate(type) {
      let start,
        end,
        now = this.$dayjs();
      if (type === 'year') {
        start = now.startOf('year').format('YYYY-MM-DD');
        end = now.endOf('year').format('YYYY-MM-DD');
      } else if (type === 'month') {
        start = now.startOf('month').format('YYYY-MM-DD');
        end = now.endOf('month').format('YYYY-MM-DD');
      }
      this.$set(this.searchForm, 'dateList', [start, end]);
      this.$forceUpdate();
      this.refresh();
    },
    refresh() {
      let yearList = this.searchForm.dateList;
      this.content1 = null;
      this.content2 = null;
      this.content3 = null;
      this.content4 = null;
      let params = {};

      if (yearList && yearList[0] && yearList[1]) {
        params.startDate = yearList && (yearList[0] || '');
        params.endDate = yearList && (yearList[1] || '');
      } else {
        params.startDate =
          this.searchForm.dateList && (this.searchForm.dateList[0] || '');
        params.endDate =
          this.searchForm.dateList && (this.searchForm.dateList[1] || '');
      }

      this.ajax.voluntariesStatisticsTabulation(params).then(res => {
        if (res.success) {
          this.data = res.object;

          let yncsLabel = this.data.yncs.map(item => item.key);
          let yncsValue = this.data.yncs.map(item => item.val);
          this.initEcharts1(yncsLabel, yncsValue);

          let ywcsLabel = this.data.ywcs.map(item => item.key);
          let ywcsValue = this.data.ywcs.map(item => item.val);
          this.initEcharts2(ywcsLabel, ywcsValue);

          let ynscLabel = this.data.ynsc.map(item => item.key);
          let ynscValue = this.data.ynsc.map(item => item.val);
          this.initEcharts3(ynscLabel, ynscValue);

          let ywscLabel = this.data.ywsc.map(item => item.key);
          let ywscValue = this.data.ywsc.map(item => item.val);
          this.initEcharts4(ywscLabel, ywscValue);
        } else {
          this.$message.error(res.message || '获取统计失败');
        }
      });

      this.ajax.voluntariesStatisticsGetCategory(params).then(res => {
        if (res.success) {
          Object.values(res.object).forEach((value, index) => {
            this.statisticItemsArr[index].value = value;
          });
        } else {
          this.$message.error(res.message || '获取统计失败');
        }
      });
    },

    initEcharts1(xAxis, yAxis) {
      if (!this.content1) {
        this.content1 = this.$echarts.init(this.$refs.content1);
      }

      let options = {
        grid: {
          top: '10%',
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: yAxis,
            barWidth: '20px',
            type: 'bar'
          }
        ]
      };

      this.content1.clear();
      this.content1.resize();
      this.content1.setOption(options);
    },

    initEcharts2(xAxis, yAxis) {
      if (!this.content2) {
        this.content2 = this.$echarts.init(this.$refs.content2);
      }

      let options = {
        grid: {
          top: '10%',
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: yAxis,
            barWidth: '20px',
            type: 'bar'
          }
        ]
      };

      this.content2.clear();
      this.content2.resize();
      this.content2.setOption(options);
    },

    initEcharts3(xAxis, yAxis) {
      if (!this.content3) {
        this.content3 = this.$echarts.init(this.$refs.content3);
      }

      let options = {
        grid: {
          top: '10%',
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: yAxis,
            barWidth: '20px',
            type: 'bar'
          }
        ]
      };

      this.content3.clear();
      this.content3.resize();
      this.content3.setOption(options);
    },

    initEcharts4(xAxis, yAxis) {
      if (!this.content4) {
        this.content4 = this.$echarts.init(this.$refs.content4);
      }

      let options = {
        grid: {
          top: '10%',
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: yAxis,
            barWidth: '20px',
            type: 'bar'
          }
        ]
      };

      this.content4.clear();
      this.content4.resize();
      this.content4.setOption(options);
    }
  }
};
</script>

<style lang="scss" scoped>
.statistic-analysis {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px;
  display: flex;
  flex-direction: column;
  .statistic-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 0;
    .statistic-item {
      width: calc(20% - 8px);
      height: 80px;
      margin-top: 8px;
      border: 1px solid #e1e1e1;
      padding-top: 8px;
      padding-left: 16px;
      border-radius: 6px;
      color: #333;
      .value {
        font-size: 24px;
        font-weight: 700;
      }
      p {
        margin: 0;
      }
    }
  }
  .container {
    width: 100%;
    height: calc(100% - 176px - 8px - 39px);
    margin-top: 8px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    > div {
      width: calc(50% - 4px);
      margin-bottom: 8px;
      border: 1px solid #eee;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      > div {
        position: absolute;
        left: 8px;
        top: 24px;
        width: calc(100% - 16px);
        height: calc(100% - 32px);
      }
      .tabs-tips {
        position: absolute;
        left: 8px;
        top: 8px;
        z-index: 99;
        &::before {
          content: '1';
          width: 14px;
          height: 20px;
          background: $primary-blue;
          color: $primary-blue;
          border-radius: 4px;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
