<template>
  <ts-dialog
    custom-class="dialog-activity"
    :visible.sync="visible"
    :title="isEdit ? '编辑' : '新增'"
    type="large"
  >
    <ts-form ref="form" :model="editData">
      <ts-form-item prop="masterName" :rules="rules.required" label="活动名称">
        <ts-input placeholder="请输入活动名称" v-model="editData.masterName" />
      </ts-form-item>

      <ts-form-item label="活动介绍">
        <ts-input
          v-model="editData.masterRemark"
          type="textarea"
          class="textarea"
          maxlength="100"
          placeholder="请输入活动介绍"
          show-word-limit
        />
      </ts-form-item>
    </ts-form>
    <template slot="footer">
      <ts-button type="primary" @click="submit">确定</ts-button>
      <ts-button @click="close">取消</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      isEdit: false,
      editData: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open({ isEdit = false, data }) {
      this.isEdit = isEdit;
      this.editData = deepClone(data);

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.editData);
        let API = null;

        if (this.isEdit) {
          API = this.ajax.voluntariesActivityMasterUpdate;
        } else {
          API = this.ajax.voluntariesActivityMasterSave;
        }
        const res = await API(formData);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('refreshActivity');
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-activity {
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
