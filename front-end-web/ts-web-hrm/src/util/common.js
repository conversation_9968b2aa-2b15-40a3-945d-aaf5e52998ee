import { Base64 } from 'js-base64';
import moment from 'moment';
export const commonUtils = {
  /**
   * 检查数组长度是否大于一
   * @param obj
   * @returns {*}
   */
  arrayLength: function(text) {
    if (
      Object.prototype.toString.call(text) === '[object Array]' &&
      text.length > 0
    ) {
      return true;
    } else {
      return false;
    }
  },
  /**
   * 过滤对象中为空的属性
   * @param obj
   * @returns {*}
   */
  filterObj: function(obj) {
    if (!(typeof obj == 'object')) {
      return;
    }

    for (let key in obj) {
      if (
        obj.hasOwnProperty(key) &&
        (obj[key] == null || obj[key] == undefined || obj[key] === '')
      ) {
        delete obj[key];
      }
    }
    return obj;
  },
  /**@desc 获取唯一的ID */
  guid: function() {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return (
      S4() +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      S4() +
      S4()
    );
  },
  filterSize: function(size) {
    if (!size) return '';
    return size < 1024
      ? size + ' B'
      : size < pow1024(2)
      ? (size / 1024).toFixed(2) + ' KB'
      : size < pow1024(3)
      ? (size / pow1024(2)).toFixed(2) + ' MB'
      : size < pow1024(4)
      ? (size / pow1024(3)).toFixed(2) + ' GB'
      : (size / pow1024(4)).toFixed(2) + ' TB';
  },
  applyEmployeeStr: function(employee) {
    if (!employee) return '';
    return `${employee.orgName ? employee.orgName + '-' : ''}${
      employee.employeeName
    }${employee.phoneNumber ? '(' + employee.phoneNumber + ')' : ''}`;
  },
  // 防抖
  debounce: function(func, wait, immediate) {
    let timeout, args, context, timestamp, result;
    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp;
      // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last);
      } else {
        timeout = null;
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args);
          if (!timeout) context = args = null;
        }
      }
    };
    return function(...args) {
      context = this;
      timestamp = +new Date();
      const callNow = immediate && !timeout;
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait);
      if (callNow) {
        result = func.apply(context, args);
        context = args = null;
      }
      return result;
    };
  },
  isDoc(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ofd|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  /**
   * 线上预览文件
   * @param String path 文件路径
   * @param String filename 文件名字
   */
  viewerDocBase(path, filename) {
    var url = '';
    if (path.indexOf('http') >= 0) {
      url = path + '?fullfilename=' + filename;
    } else {
      url = 'http://127.0.0.1:8777' + path + '?fullfilename=' + filename;
    }

    let a = document.createElement('a');
    a.target = '_blank';
    a.href =
      location.origin +
      '/ts-preview/onlinePreview?url=' +
      encodeURIComponent(Base64.encode(url));
    a.click();
  },

  getBrithDayFromIdCard(idCard) {
    // 提取出生日日期
    var year = idCard.substring(6, 10);
    var month = idCard.substring(10, 12);
    var day = idCard.substring(12, 14);

    return year + '-' + month + '-' + day;
  },

  getSexFromIdCard(idCard) {
    const genderDigit = parseInt(idCard.charAt(idCard.length - 2));
    return genderDigit % 2 === 0 ? '女' : '男';
  },
  orgCodeHidden(_this, code) {
    if (_this.$store.state.common.hospitalCode == code) {
      return true;
    } else {
      return false;
    }
  },
  deduplicateArray(arr, iteratee) {
    const seen = new Set();
    return arr.filter(item => {
      // 支持函数/字符串/数组多种参数形式
      const key =
        typeof iteratee === 'function'
          ? iteratee(item)
          : Array.isArray(iteratee)
          ? iteratee.map(p => item[p])
          : item[iteratee];
      return seen.has(key) ? false : seen.add(key);
    });
  },
  calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);

    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // 如果当前月份小于出生月份，或月份相同但日期未到，则年龄减1
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }

    return age;
  },
  getDateDiffInYearsAndMonths(startDateStr, endDateStr) {
    const startDate = moment(startDateStr);
    const endDate = moment(endDateStr);
    if (!startDate.isValid() || !endDate.isValid()) {
      throw new Error('无效的日期格式');
    }

    // 确保 endDate >= startDate
    if (endDate.isBefore(startDate)) {
      throw new Error('结束日期不能早于开始日期');
    }

    let years = endDate.diff(startDate, 'years');
    let months;

    // 减去整年后，再计算剩余的月份
    const tempDate = startDate.clone().add(years, 'years');
    months = endDate.diff(tempDate, 'months');

    // 如果 months 是负数，说明多减了一年，需要调整
    if (months < 0) {
      years -= 1;
      months += 12;
    }

    return { years, months };
  }
};
// 求次幂
function pow1024(num) {
  return Math.pow(1024, num);
}
