<!-- pdf预览可添加批注组件 -->
<!-- 
方法：
callback - 保存成功的回调函数,无返回值
getdata  - 批注信息回调
 -->
<template>
  <div class="PdfAnnotor">
    <template v-if="mode === 'custom'">
      <div class="cropperContainer" id="cropperContainer">
        <div class="cropperWrapOuter" ref="cropperWrapOuter">
          <PDFViewer
            :pdfUrl="pdfUrl"
            :currentPage.sync="currentPage"
            :totalPages.sync="totalPages"
            :scale.sync="curScale"
            :useWaterflow="useWaterflow"
            @rendeCallback="handleRendeCallback"
            ref="pdfViewer"
          />
        </div>

        <div class="thumbnailbox" v-show="!readOnly">
          <div class="thumbnail-tit">缩略图</div>
          <div class="thumbnail-body" ref="scrollContainer">
            <div
              class="thumbnail-item"
              v-for="(item, index) in thumbnail"
              :key="index"
              :ref="'scrollItem_' + index"
              @click="thumbnailClick(item, index)"
            >
              <div
                class="thmbnail-img"
                :class="{ active: curIndex - 1 === index }"
              >
                <img :src="item.url" :alt="item.name" />
              </div>
              <p>{{ item.name }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="toolbar">
        <div class="toolbar-left">
          <!-- <div class="file-name">
            {{
              this.thumbnail.length > 0
                ? this.thumbnail[this.curIndex - 1].name
                : '--'
            }}
          </div> -->
        </div>
        <div class="toolbar-center">
          <div class="pager">
            <el-input-number
              v-model="currentPage"
              :min="1"
              :max="totalPages"
              @change="goPage"
            ></el-input-number>
            <span class="">/</span>
            <span>{{ totalPages }}</span>
          </div>
          <div class="zoombox">
            <div class="zoomout" @click="zoomImage(-0.2)">-</div>
            <el-input-number
              v-model="curScale"
              :step="10"
              @change="scalePdf()"
            ></el-input-number>
            <div class="zoomin" @click="zoomImage(0.2)">+</div>
          </div>
          <div class="translatebox">
            <div class="reset el-icon-refresh-left" @click="resetPdf"></div>
          </div>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" class="prev" @click="prevPage"
            >上一页</el-button
          >
          <el-button type="primary" class="next" @click="nextPage"
            >下一页</el-button
          >
          <el-button
            type="primary"
            class="prev"
            @click="prevFile"
            v-show="!readOnly"
            >上一个文件</el-button
          >
          <el-button
            type="primary"
            class="next"
            @click="nextFile"
            v-show="!readOnly"
            >下一个文件</el-button
          >
        </div>
      </div>
    </template>
    <template v-else-if="mode === 'default'">
      <iframe
        :src="viewerurl + pdfUrl"
        id="pdfViewer"
        style="width: 100%; height: 100%;"
        frameborder="0"
        scrolling="no"
      ></iframe>
    </template>
  </div>
</template>

<script>
import PDFViewer from './components/PDFViewer.vue';
import { debounce } from '@/unit/commonHandle';

export default {
  name: 'PdfExpress',
  components: {
    PDFViewer
  },
  props: {
    pdfUrl: { type: [String], default: '' }, //PDF地址
    thumbnail: { type: [Array], default: () => [] }, //缩略图集合
    currentIndex: { type: [Number], default: 1 }, //缩略图定位到第几张图
    useWaterflow: { type: [Boolean], default: true }, //是否开启水印功能
    readOnly: { type: [Boolean], default: true }, //是否只读模式
    role: { type: [String], default: '' }, //打开模式

    medRecordInfo: { type: [Object], default: () => {} }
  },
  data() {
    return {
      mode: 'custom', //可选值 default - pdf-viewer 和 custom - 自定义预览
      viewerurl: '/ts-web-paperless/pdfjs/web/viewer.html?file=',
      currentPage: 1, //当前PDF第几页
      totalPages: 0,
      // annotationData: [], // 每页的批注
      annoContWidth: 0,
      annoContHeight: 0,
      annoContOffset: {
        offsetTop: 0,
        offsetLeft: 0
      },

      curScale: 100,

      isShowAddTempDlg: false,
      edittype: 'add',
      annotDatas: [],

      curIndex: 1 //当前缩略图第几个文件
    };
  },
  watch: {
    currentIndex: {
      handler(val) {
        this.curIndex = val;
        if (this.thumbnail.length > 0) {
          this.scrollToItem();
        }
      },
      immediate: true
    },

    $route(to, from) {
      if (to.path.indexOf('PdfExpress') < 0) {
        this.removeListeners();
      } else if (to.path.indexOf('PdfExpress') > -1) {
        this.addListenners();
      }
    }
  },
  created() {},
  mounted() {
    // this.highlighter = new Highlighter({
    //   wrapTag: 'mark',
    //   style: { background: 'yellow' }
    // });
    // console.log(this.highlighter);
    // const pdfTextContainer = this.$refs.pdfTextContainer; // 指向 PDF 文本容器
    // this.highlighter.run(pdfTextContainer);
    // console.log(this.highlighter);
    // this.highlighter.fromStore(0, 20, '批注', 'sdsd');
    this.addListenners();
  },
  beforeDestroy() {
    this.removeListeners();
  },
  methods: {
    handleRendeCallback(res) {
      this.annoContWidth = res.viewport.width;
      this.annoContHeight = res.viewport.height;
      this.annoContOffset = res.pdfoffset;
    },

    handleScroll(e) {
      if (e.ctrlKey) {
        // 取消浏览器默认的放大缩小网页行为
        e.preventDefault();
        if (e.deltaY > 0) {
          // 缩小
          this.zoomImage(-0.3);
        } else {
          // 放大
          this.zoomImage(0.3);
        }
      }
    },

    handleKeydown(e) {
      if (e.target && e.target.nodeName !== 'INPUT') {
        if (e.keyCode === 38) {
          //上
          this.nextPage();
        } else if (e.keyCode === 40) {
          //下
          this.prevPage();
        }
      }
    },

    // test() {
    //   // 1. 实例化
    //   const highlighter = new Highlighter({
    //     wrapTag: 'span',
    //     style: {
    //       className: 'highlight-mengshou-wrap'
    //     }
    //   });

    //   // // 2. 从后端获取高亮信息，还原至网页
    //   // getRemoteData().then(s =>
    //   //   highlighter.fromStore(s.startMeta, s.endMeta, s.id, s.text)
    //   // );

    //   // // 3. 监听高亮笔记创建事件，并将信息存至后端
    //   // highlighter.on(Highlighter.event.CREATE, ({ sources }) => save(sources));

    //   // 4. 开启自动划词高亮
    //   highlighter.run();
    // },
    // saveAnnotations() {
    //   const annotations = {
    //     pages: this.annotationData // 按页面存储
    //   };
    //   const jsonData = JSON.stringify(annotations);
    //   // 下载到本地或发送到服务器
    //   const blob = new Blob([jsonData], { type: 'application/json' });
    //   const url = URL.createObjectURL(blob);
    //   const link = document.createElement('a');
    //   link.href = url;
    //   link.download = 'annotations.json';
    //   link.click();
    //   URL.revokeObjectURL(url);
    // },
    // loadAnnotations(jsonData) {
    //   const data = JSON.parse(jsonData);
    //   this.annotationData = data.pages || [];
    //   this.renderAnnotations(); // 调用渲染逻辑
    // },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.$nextTick(() => {
          this.$refs.pdfViewer.renderPage(false, true);
        });
        // this.renderAnnotations();
      }
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.$nextTick(() => {
          this.$refs.pdfViewer.renderPage(false, true);
        });
        // this.renderAnnotations();
      }
    },

    goPage(val) {
      this.currentPage = Number(val);
      this.$nextTick(() => {
        this.$refs.pdfViewer.renderPage(false, true);
      });
    },

    scalePdf() {
      this.$nextTick(() => {
        this.$refs.pdfViewer.renderPage();
      });
    },

    zoomImage: debounce(
      function(xishu) {
        this.curScale = this.curScale + xishu * 100;
        this.$nextTick(() => {
          this.$refs.pdfViewer.renderPage();
        });
      },
      50,
      true
    ),

    //重置（缩放系数）
    resetPdf() {
      this.$nextTick(() => {
        this.$refs.pdfViewer.renderPage(true, true);
      });
    },

    //点击缩略图
    thumbnailClick(image, index) {
      this.$nextTick(() => {
        this.curIndex = index + 1;
        this.$emit('update:currentIndex', index + 1);
        this.scrollToItem();
        this.$emit('GetImageOrigin', {
          type: 'right',
          data: {
            id: image.id
          }
        });
      });
    },

    prevFile() {
      if (this.curIndex > 1) {
        this.curIndex--;
        const image = this.thumbnail[this.curIndex - 1];
        this.$emit('update:currentIndex', this.curIndex);
        this.$emit('GetImageOrigin', {
          type: 'right',
          data: {
            id: image.id
          }
        });
      } else {
        //上一个目录文件
        this.$emit('GetPrevMenu', this.medRecordInfo);
      }
    },

    nextFile() {
      const _List = this.thumbnail;
      if (this.curIndex < _List.length) {
        this.curIndex++;
        const image = this.thumbnail[this.curIndex - 1];
        this.$emit('update:currentIndex', this.curIndex);
        this.$emit('GetImageOrigin', {
          type: 'right',
          data: {
            id: image.id
          }
        });
      } else {
        //下一个目录文件
        this.$emit('GetNextMenu', this.medRecordInfo);
      }
    },

    //滚动到指定缩略图位置
    scrollToItem() {
      this.$nextTick(() => {
        const index = this.curIndex - 1;
        const currentItem = this.$refs['scrollItem_' + index][0];
        if (currentItem) {
          const container = this.$refs.scrollContainer;
          container.scrollTop =
            currentItem.offsetTop - container.offsetTop - 10;
        }
      });
    },

    //添加页面监听器
    addListenners() {
      if (this.mode === 'custom') {
        this.$refs.cropperWrapOuter.addEventListener(
          'mousewheel',
          this.handleScroll,
          {
            passive: false
          }
        );

        this.$refs.cropperWrapOuter.addEventListener(
          'keydown',
          this.handleKeydown,
          {
            passive: false
          }
        );
      }

      if (this.mode === 'default') {
        const iframe = document.getElementById('pdfViewer');
      }
    },

    //移除监听器
    removeListeners() {
      if (this.mode === 'custom') {
        this.$refs.cropperWrapOuter.removeEventListener(
          'mousewheel',
          this.handleScroll
        );
        this.$refs.cropperWrapOuter.removeEventListener(
          'keydown',
          this.handleKeydown
        );
      }
    },

    showAnnotsDlg(type) {
      this.edittype = type;
      this.isShowAddTempDlg = true;
    },

    handleAnnotData(data) {
      this.annotDatas = data;
    },

    callback() {
      this.$emit('callback');
    }

    // updateAnnotations(newAnnotations) {
    //   this.$set(this.annotationData, this.currentPage - 1, newAnnotations);
    // }
  }
};
</script>

<style lang="scss" scoped>
.PdfAnnotor {
  width: 100%;
  height: 100%;
  min-height: 30vh;
  display: flex;
  flex-direction: column;
}

.cropperContainer {
  flex: 1;
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #dbdfe6;

  .cropperWrapOuter {
    position: relative;
    flex: 1;
    width: 85%;
    height: 100%;
    padding: 8px;
  }

  .thumbnailbox {
    flex-shrink: 0;
    width: 15%;
    height: 100%;
    margin-left: auto;
    border-radius: 2px;
    overflow: hidden;
    .thumbnail-tit {
      height: 40px;
      line-height: 40px;
      color: #fff;
      background-color: $primary-blue;
      text-align: center;
      font-size: 16px;
    }

    .thumbnail-body {
      width: 100%;
      height: calc(100% - 40px);
      overflow-y: auto;
      padding: 8px;
      border: 1px solid rgba(41, 92, 249, 0.4);
      .thumbnail-item {
        margin-bottom: 5px;
        .thmbnail-img {
          background-color: #ebeef3;
          padding: 6px;
          border-radius: 2px;
          &.active {
            border: 2px solid #ff0000;
          }
          img {
            display: block;
            width: 100%;
            height: auto;
            cursor: pointer;
          }
        }
        p {
          text-align: center;
          line-height: 16px;
          padding: 6px;
          color: #404040;
          font-size: 13px;
          cursor: pointer;
          word-break: break-word;
        }
      }
    }
  }
}

.toolbar {
  height: 40px;
  background: #5c5c5c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  color: #fff;

  .toolbar-center,
  .pager,
  .zoombox {
    display: flex;
    align-items: center;

    ::v-deep {
      .el-input-number {
        width: auto;
        min-width: none;
        min-width: auto;

        .el-input {
          width: auto;
          min-width: none;
          min-width: auto;
        }

        .el-input-number__increase,
        .el-input-number__decrease {
          display: none !important;
        }

        .el-input__inner {
          padding: 0 5px;
          height: 24px;
          line-height: 24px;
          width: auto;
          min-width: none;
          min-width: auto;
          background-color: #404040;
          color: #fff;
          border: 0;
        }
      }
    }
  }

  .pager,
  .zoombox {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 0;
      width: 1px;
      height: 11px;
      margin-top: -5.5px;
      background-color: #404040;
    }
  }

  .pager {
    padding-right: 12px;

    > span {
      margin-left: 5px;
      font-size: 13px;
    }

    ::v-deep {
      .el-input__inner {
        font-size: 13px;
        width: 48px !important;
        text-align: center;
      }
    }
  }

  .zoombox {
    padding: 0 5px;

    .zoomout,
    .zoomin {
      font-size: 20px;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      line-height: 24px;
      cursor: pointer;
      user-select: none !important;
    }

    ::v-deep {
      .el-input-number {
        margin: 0 4px;

        .el-input__inner {
          font-size: 13px;
          width: 48px !important;
          text-align: center;
        }
      }
    }
  }

  .translatebox {
    padding-left: 10px;
    display: flex;
    align-items: center;

    .reset {
      display: block;
      cursor: pointer;
      width: 20px;
      height: 20px;
      padding: 0 5px;
      line-height: 22px;
      text-align: center;
    }
  }
}

.toolbar-left {
  width: 32%;
}

.toolbar-right {
  width: 32%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  ::v-deep {
    .el-button {
      padding-left: 6px;
      padding-right: 6px;
      span {
        font-size: 12px;
      }
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
    .next {
      background-color: #f4a622;
    }
  }
}

.annotToolbox {
  position: absolute;
  bottom: 140px;
  left: 8px;
  width: 32px;
  height: 90px;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .addannot,
  .viewannot {
    width: 32px;
    height: 32px;
    background-color: #a1a1a1;
    border-radius: 6px;
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px auto;
    cursor: pointer;
    .annotnums {
      position: absolute;
      right: -7px;
      top: -7px;
      display: block;
      min-width: 14px;
      height: 14px;
      background-color: #ff0000;
      border-radius: 14px;
      line-height: 14px;
      text-align: center;
      font-size: 10px;
      padding: 0 5px;
      color: #fff;
    }
  }
}
</style>
