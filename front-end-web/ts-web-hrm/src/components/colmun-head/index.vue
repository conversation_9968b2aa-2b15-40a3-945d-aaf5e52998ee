<template>
  <div class="item-head" :style="{ background }">
    <div class="top-title">
      <p class="title">
        <span class="lefts"></span>
        <slot name="title"></slot>
        {{ title }}
      </p>
      <el-popover
        placement="right"
        width="400"
        trigger="hover"
        popper-class="popverContent"
      >
        <i
          class="vxe-icon-question-circle popverIcon"
          slot="reference"
          v-if="showPopover"
        ></i>
        <slot name="popverContent"></slot>
      </el-popover>
      <div class="operate-container">
        <slot name="operate"></slot>
      </div>
      <div class="operate-container-button">
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    showPopover: {
      type: Boolean,
      default: false
    },
    background: {
      type: String,
      default: '#e8ebfa'
    }
  }
};
</script>
<style lang="scss">
.el-popper.popverContent {
  p {
    margin: 0;
    color: #fff;
  }
  background: #12257c;
}
</style>
<style lang="scss" scoped>
/deep/ .el-popover__reference-wrapper {
  line-height: 30px;
}
.item-head {
  display: flex;
  align-items: center;
  height: 32px;
  width: 100%;
  .popverIcon {
    font-size: 18px;
    margin-left: 4px;
  }
  .top-title {
    width: 100%;
    padding: 0 8px;
    display: flex;
    justify-content: space-between;
    .title {
      color: #333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin: 0;
      .lefts::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 14px;
        background-color: $primary-blue;
        margin-right: 4px;
        margin-top: 6px;
        border-radius: 2px;
      }
    }
    .operate-container {
      display: inline-block;
      flex: 1;
    }
  }
}
</style>
