<template>
  <div class="flex flex-col-center">
    <el-date-picker
      class="date-picker"
      popper-class="form-data-bar-popper"
      v-model="dateList[0]"
      value-format="yyyy-MM-dd"
      :picker-options="pickerOptions"
      :placeholder="placeholder[0]"
      @change="changeDate"
    >
    </el-date-picker>
    <span style="padding: 0 8px;">{{ rangeSeparator }}</span>
    <el-date-picker
      class="date-picker"
      popper-class="form-data-bar-popper"
      v-model="dateList[1]"
      value-format="yyyy-MM-dd"
      :placeholder="placeholder[1]"
      :picker-options="pickerOptions1"
      @change="changeDate"
    >
    </el-date-picker>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  model: {
    prop: 'dateValue',
    event: 'change'
  },
  props: {
    dateValue: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: Array,
      default: () => ['开始时间', '结束时间']
    },
    rangeSeparator: {
      type: String,
      default: () => '至'
    }
  },
  watch: {
    dateValue(newValue) {
      this.dateList = newValue;
    }
  },
  data() {
    return {
      dateList: this.dateValue,
      pickerOptions: {
        disabledDate: this.disabledDate,
        shortcuts: [
          {
            text: '清空',
            onClick(picker) {
              picker.$emit('pick', '');
            }
          },
          {
            text: '现在',
            onClick: this.dateClick
          }
        ]
      },
      pickerOptions1: {
        disabledDate: this.disabledDate1,
        shortcuts: [
          {
            text: '清空',
            onClick: picker => {
              picker.$emit('pick', '');
            }
          },
          {
            text: '现在',
            onClick: this.dateClick1
          }
        ]
      }
    };
  },
  methods: {
    disabledDate(time) {
      if (this.dateList.length && this.dateList[1]) {
        if (moment(time).isAfter(moment(this.dateList[1]))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    disabledDate1(time) {
      if (this.dateList.length && this.dateList[0]) {
        if (moment(time).isBefore(moment(this.dateList[0]))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    changeDate() {
      this.$emit('change', this.dateList);
    },
    dateClick(picker) {
      if (this.dateList[1] && new Date() > new Date(this.dateList[1])) {
        picker.$emit('pick', new Date(this.dateList[1]));
        this.dateList[1] = this.$dayjs().format('YYYY-MM-DD');
      } else {
        picker.$emit('pick', new Date());
      }
      this.changeDate();
    },
    dateClick1(picker) {
      if (this.dateList[0] && new Date() < new Date(this.dateList[0])) {
        picker.$emit('pick', new Date(this.dateList[0]));
        this.dateList[0] = this.$dayjs().format('YYYY-MM-DD');
      } else {
        picker.$emit('pick', new Date());
      }
      this.changeDate();
    }
  }
};
</script>

<style scoped lang="scss">
.date-picker.el-date-editor {
  display: flex;
  align-items: center;
  background: transparent;
  width: 140px !important;
}
</style>

<style lang="scss">
.form-data-bar-popper {
  &.has-sidebar {
    width: 322px !important;
    .el-picker-panel__content {
      margin-bottom: 40px;
    }
  }
  .el-picker-panel__sidebar {
    width: 100%;
    height: 40px;
    line-height: 40px;
    top: unset;
    text-align: right;
    padding: 0 15px;
  }
  .el-picker-panel__shortcut {
    display: inline-block;
    width: auto;
    padding: 0 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-left: 8px;
  }
  .el-picker-panel__sidebar + .el-picker-panel__body {
    margin: 0;
  }
}
</style>
