<template>
  <div
    :id="containerId"
    class="qiankun-child"
    :class="[isFullScreen ? 'isLargeScreen' : '']"
  >
    <keep-alive>
      <router-view v-if="routerBool" />
    </keep-alive>

    <data-message-component
      v-if="openOrderDialog"
      v-model="openOrderDialog"
      :messageInfo="orderDetail"
    >
    </data-message-component>
  </div>
</template>
<script>
import api from '@/api/ajax/workOrderHomePage';
import data from '@/views/workSheet/workOrderHomePage/Minix/data';

import DataMessageComponent from '@/views/workSheet/workOrderHomePage/components/DataMessageComponent';

export default {
  mixins: [data],
  components: {
    DataMessageComponent
  },
  data() {
    return {
      containerId: process.env.VUE_APP_CONTAINER,
      cacheRoute: {}, //缓存路由对象,
      routerBool: true, //刷新页面
      openOrderDialog: false,
      orderDetail: null
    };
  },
  async created() {
    this.setStoreFullScreen();
    window.addEventListener('resize', () => {
      this.setStoreFullScreen();
    });
    window.addEventListener(
      'qiankunOpenWorkInfoHandle',
      this.getWorkInfoMessageHandle,
      false
    );

    let { worksheetSwitch = 0 } = this.$getParentStoreInfo('globalSetting');
    if (worksheetSwitch) {
      try {
        //Vuex添加fkUserId
        const res = await api.loginPersonInfo();
        if (!res.success) {
          throw res.message;
        }
        this.$store.commit('common/setData', {
          label: 'userMessage',
          value: res.object
        });
      } catch (e) {
        console.log(e, 'e getLoginPersonInfo');
      }
    }
  },
  methods: {
    async getWorkInfoMessageHandle({ detail }) {
      const detailRes = await api.workSheetInfo(detail.workNumber);
      if (detailRes.success) {
        this.openOrderDialog = true;
        this.orderDetail = detailRes.object;
      }
    },
    setStoreFullScreen() {
      this.$set(
        this.$store.state.common,
        'isFullScreen',
        window.screen.height === window.innerHeight
      );
    },
    /**@desc 接收父应用发过来的页面缓存事件**/
    curdRouter(_obj) {
      let obj = _obj.detail;
      if (obj.type == 'deleteRouter') {
        this.deleteRouter(obj);
      } else if (obj.type == 'updateRouter') {
        this.updateRouter(obj);
      }
    },
    /**@desc 删除单个页面缓存**/
    deleteRouter(obj) {
      let key = `/${obj.alink.split(process.env.VUE_APP_BASE_URL)[1]}`;
      if (this.cacheRoute[key]) {
        this.cacheRoute[key].instances.default.$destroy();
        delete this.cacheRoute[key];
      }
    },
    /**@desc 刷新单个页面**/
    /**@desc 何锴 2022/02/10 修改新框架页签重新加载点击不刷新，以及多次点击无效问题 -Start */
    async updateRouter(obj) {
      let key = `/${obj.alink.split(process.env.VUE_APP_BASE_URL)[1]}`;

      if (this.cacheRoute[key]) {
        this.cacheRoute[key].instances.default.$destroy();
        // delete this.cacheRoute[key];
        if (key == this.$route.fullPath) {
          this.routerBool = false;
          await this.$nextTick();
          this.routerBool = true;

          await this.$nextTick();
          let vm = this.cacheRoute[key].instances.default;
          vm.refresh && typeof vm.refresh == 'function' && vm.refresh();
          /**@desc 何锴 2022/02/10 修改新框架页签重新加载点击不刷新，以及多次点击无效问题 -End */
        }
      }
    },
    /**@desc 获取缓存路由**/
    setRouterCache(to, form) {
      if (form.matched.length != 0) {
        let instancesObj = form.matched[0];
        this.cacheRoute[form.path] = instancesObj;
      }
      if (to.matched.length != 0) {
        let instancesObj = to.matched[0];
        this.cacheRoute[to.path] = instancesObj;
      }
    },
    /**@desc 新项目接收websocket消息**/
    webSocketMessage(msg) {
      console.log(msg, `新项目接收websocket`);
    }
  },

  mounted() {
    /**@desc 接收页签自定义事件**/
    window.addEventListener('updateDataQianKun', this.curdRouter, false);
    window.addEventListener('webSocketMessage', this.webSocketMessage, false);
  },
  watch: {
    $route(to, form) {
      this.setRouterCache(to, form);
    }
  },
  beforeDestroy() {
    window.removeEventListener('updateDataQianKun', this.curdRouter);
    window.removeEventListener('webSocketMessage', this.webSocketMessage);
  },
  name: 'App'
};
</script>
<style scoped lang="scss">
.qiankun-child {
  margin-top: 8px;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  // background: #fff;
  border-radius: 4px;
  overflow: hidden;
  &.isLargeScreen {
    width: 100%;
    height: 100%;
    margin-top: 0px;
    border-radius: 0px;
  }
}
/deep/ {
  .ts-container {
    padding: 8px !important;
    height: 100%;
    background-color: #fff;
  }
}

.qiankun-child-container {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
@import './assets/css/element-variables.scss';
@import './assets/css/common.scss';
</style>

<style lang="scss">
.action-item.el-dropdown-menu__item:not(.is-disabled):hover,
.action-item.el-dropdown-menu__item:focus {
  background: none;
  color: inherit;
}
.action-item.el-dropdown-menu__item {
  padding-right: 20px;
  height: 27px;
  line-height: 27px;
  color: #333;
  white-space: nowrap;
  .fa-trash-o {
    font-size: 17px;
  }
  div {
    line-height: 27px;
  }
  &:hover {
    background-color: rgba(82, 96, 255, 0.08) !important;
  }
}
// .worksheet-table-action {
//   padding: 0;
//   margin-top: 0 !important;
//   .popper__arrow {
//     display: none;
//   }
//   .action-item {
//     padding: 0 20px 0 5px;
//     height: 27px;
//     line-height: 27px;
//     color: #333;

//     div {
//       line-height: 27px;
//     }
//     &:hover {
//       background-color: rgba(82, 96, 255, 0.08) !important;
//     }
//   }
// }
</style>
