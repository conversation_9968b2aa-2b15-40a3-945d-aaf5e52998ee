.flex{
    display: flex;
}
.flex-center{
    align-items: center;
    justify-content: center;
}
.flex-row-end{
    display: flex;
    justify-content: flex-end;
}
.flex-column{
    flex-direction: column;
}
.flex-grow{
    flex: 1;
}
.flex-row-between{
    justify-content: space-between;
}
.flex-col-end{
    align-items: flex-end;
}
.flex-col-center{
    align-items: center;
}
.flex-row-evenly{
    justify-content: space-evenly;
}
.flex-wrap{
    flex-wrap: wrap;
}
.red-font{
    font-size: 48px;
    font-family: Impact;
    color: #F65656;
    line-height: 58px;
}

.trasen-btn{
    border-radius: 2px;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
}

.trasen-perpul,
.trasen-perpul:focus{
    background-color: #5260ff;
    border: 1px #5260ff solid;
    border-radius: 2px;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    margin-left: 8px;
}

.trasen-perpul:hover{
    background-color: #5260ff;
    color: #fff;
    border: 1px #5260ff solid;
    opacity: 0.8;
}

.action-row{
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
}
.more-action-icon{
    height: 30px;
    width: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover i{
      color: #5260FF;
    }
}

.trasen-search-reset{
    display:inline-block;
    background-color: #FFF;
    border:none;
    line-height: 30px;
    width: 30px;
    text-align: center;
    padding: 0;
    height: 30px;
    font-size: 14px;
    margin-left: 8px;
    cursor: pointer;
}
.trasen-search-reset:hover{
    color: #5260ff;
}