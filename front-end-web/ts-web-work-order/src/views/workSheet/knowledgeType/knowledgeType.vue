<template>
  <div style="height: 100%;">
    <div class="flex contents">
      <div class="left">
        <div class="flex flex-col-center">
          <el-input
            v-model="categoryName"
            placeholder="输入关键字进行搜索"
            @keyup.native="handleRefreshKnowledgeTree"
          ></el-input>
          <!-- <el-button
            type="primary"
            class="trasen-btn"
            @click="handleRefreshKnowledgeTree()"
            >搜索</el-button
          > -->
        </div>

        <el-scrollbar
          ref="treeScroll"
          class="tree-scroll"
          wrapClass="tree-scroll-wrap"
        >
          <el-tree
            :data="knowledgeTypeTreeData"
            v-if="renderTree"
            default-expand-all
            ref="knowledgeTree"
            node-key="id"
            :props="{ label: computTreeName }"
            empty-text="暂无数据"
            :highlight-current="true"
            :expand-on-click-node="false"
            :filter-node-method="filterKnowledgeTree"
            @node-click="handleTypeTreeClick"
            @node-expand="handleTypeTreeChange"
            @node-collapse="handleTypeTreeChange"
            style="min-width: 234px;"
          >
            <div class="custom-tree-node" slot-scope="{ node }">
              <div class="flex-center">
                <i
                  :class="
                    node.level == 1
                      ? 'first-tree-icon'
                      : !node.isLeaf && (node.childNodes || []).length > 0
                      ? 'has-child-tree-icon'
                      : 'leaf-tree-icon'
                  "
                ></i
                >{{ node.label }}
              </div>
            </div>
          </el-tree>
        </el-scrollbar>
      </div>

      <div class="right flex-grow flex flex-column">
        <div class="search-bar flex flex-row-between">
          <div class="flex">
            <el-input
              v-model="tableSearchData"
              placeholder="请输入分类名称进行搜索"
            ></el-input>
            <el-button class="trasen-btn trasen-perpul" @click="refresh"
              >搜索</el-button
            >
            <div
              class="trasen-search-reset"
              @click="
                () => {
                  tableSearchData = '';
                  refresh();
                }
              "
            >
              <i class="el-icon-refresh-right"></i>
            </div>
          </div>
          <div class="flex">
            <el-button class="trasen-btn trasen-perpul" @click="openEditModal()"
              >新增</el-button
            >
            <el-button
              class="trasen-btn"
              type="danger"
              @click="handleDeleteList()"
              >删除</el-button
            >
          </div>
        </div>
        <div class="flex-grow">
          <CellTable v-bind="tableOptions" ref="table">
            <template slot="actionRow" slot-scope="scope">
              <ActionCell
                :renderFunc="renderAction"
                :scope="scope"
              ></ActionCell>
            </template>
          </CellTable>
        </div>
      </div>
    </div>

    <el-dialog
      :title="editModalTitle"
      :visible.sync="showEditModal"
      @close="closeEditModal"
      :close-on-click-modal="false"
      width="640px"
    >
      <el-form
        :model="editData"
        ref="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="全路径">
          <el-input :disabled="true" v-model="editData.fullPath"></el-input>
        </el-form-item>
        <el-form-item label="上级分类">
          <InputTree
            v-model="editData.parentId"
            :treeData="knowledgeTypeTreeData"
            v-if="showEditModal"
            @change="handleParentChange"
            textName="fullPath"
          ></InputTree>
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="editData.categoryName"
            @input="handleCateGoryNameChange"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="editData.knowledgeStatus"
            @change="handleChange"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div class="flex flex-center">
          <el-button class="trasen-perpul" @click="handleAddKnowledgeType"
            >保存</el-button
          >
          <el-button class="trasen-btn" @click="closeEditModal">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import cellTable from '../comment/components/cellTabel.vue'; //表格
import actionCell from '../comment/components/actionCell.vue'; //表格的操作栏
import inputTree from '@/components/input-tree/inputTree.vue'; //输入搜索选择下拉树

export default {
  components: {
    CellTable: cellTable,
    ActionCell: actionCell,
    InputTree: inputTree
  },
  data() {
    return {
      API: {
        getKnowledgeTypeTreeData:
          '/ts-worksheet/knowledgeType/getKnowledgeTypeTreeAllList', //获取知识库类型树
        getKnowledgeTypeList:
          '/ts-worksheet/knowledgeType/getKnowledgeTypePageList', //获取知识库类型分页信息
        deleteKnowledgeType: '/ts-worksheet/knowledgeType/deleteKnowledgeType', //删除知识库类型
        saveKnowledgeType: '/ts-worksheet/knowledgeType/save', //保存知识库类型
        useKnowledgeType: '/ts-worksheet/knowledgeType/status' //修改知识库类型启用停用状态
      },

      renderTree: true, //加载树， 用来刷新重新渲染
      knowledgeTypeTreeData: [], //知识类型树 数据
      currentKey: {}, //当前选中的current
      categoryName: '', //知识类型分类名称 用来做模糊搜索
      currentFullPath: '', //当前点击的对象，用来给新增

      editModalTitle: '新增', //新增、编辑弹框标题
      editData: {}, //新增、编辑弹框 数据
      showEditModal: false, //新增、编辑弹框控制属性
      editRules: {
        categoryName: [{ required: true, message: '分类名称不能为空' }]
      },

      tableSearchData: null, //表格搜索输入框字段
      tableOptions: {
        showIndex: true,
        showSelection: true,
        columns: [
          {
            label: '分类名称',
            align: 'left',
            prop: 'categoryName'
          },
          {
            label: '全路径',
            align: 'left',
            prop: 'fullPath'
          },
          {
            label: '状态',
            align: 'left',
            width: 80,
            prop: 'knowledgeStatus',
            formatter: function(row, column, val) {
              return <span>{['已禁用', '启用中'][val]}</span>;
            }
          }
        ],
        refresh: data => {
          return this.refreshTable(data);
        }
      }
    };
  },
  methods: {
    //触发表格刷新方法
    refresh() {
      this.refreshTree();
      this.$refs.table.refreshTable();
    },
    //刷新树
    refreshTree() {
      this.$api({
        url: this.API.getKnowledgeTypeTreeData,
        method: 'get'
      }).then(res => {
        if (!res.success) {
          return;
        }
        //待完成确认
        this.renderTree = false;
        this.knowledgeTypeTreeData = res.object || [];
        this.renderTree = true;
        this.$nextTick(() => {
          this.$refs.knowledgeTree.setCurrentKey(this.currentKey.id || null);
        });
      });
    },
    //打开新增/编辑弹框
    openEditModal(row) {
      this.editData = row || {
        fullPath: ''
      };
      this.editModalTitle = row ? '编辑' : '新增';
      if (!row) {
        this.editData.knowledgeStatus = true;
        this.editData.fullPath = this.currentKey.fullPath;
        this.editData.parentId = this.currentKey.id;
        this.editData.parentName = this.currentKey.fullPath;
      }
      this.showEditModal = true;
    },
    //新增、编辑框关闭
    closeEditModal() {
      this.showEditModal = false;
      this.editData = {};
    },

    //表格刷新方法
    refreshTable(data) {
      let searchData = {
        ...data,
        categoryName: this.tableSearchData || null
      };
      if (
        this.currentKey.id &&
        this.currentKey.ids &&
        this.currentKey.ids.length
      ) {
        searchData.pkKnowledgeTypeIds = this.currentKey.ids.join(',');
      }

      return this.$api({
        url: this.API.getKnowledgeTypeList,
        method: 'POST',
        data: searchData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '出错啦');
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //------------------ 事件处理 --------------------------

    //左侧知识类型树搜索
    handleRefreshKnowledgeTree(event) {
      if (event && event.keyCode != 13) {
        return;
      }

      this.$refs.knowledgeTree.filter();
    },
    //处理类型树点击
    handleTypeTreeClick(data, node, some) {
      if (this.currentKey.id == data.id) {
        this.$refs.knowledgeTree.setCurrentKey(null);
        this.currentKey = {};
        // if(this.categoryName){
        //     let newCurrent = [];
        //     this.getAllTypeId(this.knowledgeTypeTreeData, newCurrent);
        //     this.currentKey = newCurrent.join(',');
        // }
      } else {
        this.currentKey.id = data.id;
        this.currentKey.fullPath = data.fullPath;
        this.currentKey.ids = [data.id];
        if (data.children && data.children.length) {
          this.getAllTypeId(data.children, this.currentKey.ids);
        }
      }
      this.refresh();
      // this.refresh();
    },
    //刷新知识库树
    handleRefreshTree() {
      if (!this.categoryName) {
        this.currentKey = null;
        this.$api({
          url: this.API.getKnowledgeTypeTreeData,
          method: 'get'
        }).then(res => {
          if (!res.success) {
            this.knowledgeTypeTreeData = [];
            this.$message.error(res.message || '知识库类型树加载失败');
            return;
          }

          this.knowledgeTypeTreeData = res.object || [];
          this.currentKey = null;
          this.refresh();
        });
      } else {
        this.$api({
          url: this.API.getKnowledgeTypeTreeData,
          method: 'get',
          data: { categoryName: this.categoryName }
        }).then(res => {
          if (!res.success) {
            this.knowledgeTypeTreeData = [];
            this.$message.error(res.message || '知识库类型树加载失败');
            return;
          }
          this.knowledgeTypeTreeData = res.object || [];
          let newCurrent = [];
          this.getAllTypeId(this.knowledgeTypeTreeData, newCurrent);
          this.currentKey = newCurrent.join(',');
          this.refresh();
        });
      }
    },
    //处理新增事件
    handleAddKnowledgeType() {
      this.$refs.editForm.validate(res => {
        if (!res) return;
        this.editData.knowledgeStatus
          ? (this.editData.knowledgeStatus = 1)
          : (this.editData.knowledgeStatus = 0);
        this.$api({
          url: this.API.saveKnowledgeType,
          method: 'post',
          data: JSON.stringify(this.editData),
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          this.closeEditModal();
          if (!res.success) {
            this.$message.error(res.message || '出错啦');
            return;
          }

          this.$message.success(this.editModalTitle + '成功');
          this.refresh();
          this.refreshTree();
        });
      });
    },
    //处理批量删除事件
    handleDeleteList() {
      let list = this.$refs.table.getSelection() || [];
      if (!list.length) {
        this.$message.warning('尚未选择删除行');
        return;
      }

      let idList = [];
      list.forEach(item => {
        idList.push(item.pkKnowledgeTypeId);
      });
      this.handleDelete(idList.join(','));
    },
    //处理删除事件
    handleDelete(ids) {
      this.$confirm('删除后无法恢复，确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api({
            url: this.API.deleteKnowledgeType,
            method: 'POST',
            data: { pkKnowledgeTypeIds: ids },
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
          }).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '出错啦');
              return;
            }
            this.$message.success('删除成功');
            this.refresh();
            this.refreshTree();
          });
        })
        .catch(() => {});
    },
    //处理状态改变事件
    handleChangeStatus(row) {
      this.$api({
        url: this.API.useKnowledgeType,
        method: 'post',
        data: JSON.stringify({
          knowledgeStatus: row.knowledgeStatus ? 0 : 1,
          pkKnowledgeTypeId: row.pkKnowledgeTypeId
        }),
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '出错啦');
          return;
        }

        this.refreshTree();
        this.refresh();
        this.$message.success('状态修改成功');
      });
    },
    //处理分类名称改变，计算全路径
    handleCateGoryNameChange(val) {
      let fullPath = '';
      if (val) {
        fullPath =
          (this.editData.parentName ? this.editData.parentName + '-' : '') +
          val;
      } else {
        fullPath = this.editData.parentName;
      }
      this.$set(this.editData, 'fullPath', fullPath);
    },
    //处理上级分类改变，计算全路径
    handleParentChange(val) {
      let name = val && val.fullPath;
      this.editData.parentName = name;
      let fullPath = '';
      if (name) {
        fullPath =
          name +
          (this.editData.categoryName ? '-' + this.editData.categoryName : '');
      } else {
        fullPath = this.editData.categoryName ? this.editData.categoryName : '';
      }
      this.$set(this.editData, 'fullPath', fullPath);
      // this.$forceUpdate();
    },
    //处理树改变，导致的el-scrollbar未刷新的情况
    handleTypeTreeChange() {
      setTimeout(() => {
        this.$refs.treeScroll.update();
      }, 350);
    },
    handleChange() {
      this.$forceUpdate();
    },

    //----------------------- 渲染方法 ------------------------
    renderAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row));
      row.knowledgeStatus
        ? (row.knowledgeStatus = true)
        : (row.knowledgeStatus = false);
      row.parentId = row.pid;
      let fullPathList = row.fullPath.split('-');
      fullPathList.pop();
      row.parentName = fullPathList.join('-');

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleChangeStatus(row)}>
                  <i class="fa fa-pencil-square-o"></i>
                  {row.knowledgeStatus == 1 ? '禁用' : '启用'}
                </div>
              </ElDropdownItem>
              <ElDropdownItem class="action-item">
                <div onClick={() => this.openEditModal(row)}>
                  <i class="fa fa-pencil-square-o"></i>编辑
                </div>
              </ElDropdownItem>
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleDelete(row)}>
                  <i class="fa fa-trash-o"></i>删除
                </div>
              </ElDropdownItem>
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },

    //本地过滤知识类型树
    filterKnowledgeTree(val, node) {
      if (!this.categoryName) {
        return true;
      }
      if (node.name.indexOf(this.categoryName) >= 0) {
        return true;
      }
      return false;
    },
    //冲新渲染树的label
    computTreeName(data, node) {
      return `${data.name} (${data.count})`;
    },
    //获取所有子ID
    getAllTypeId(list, resList) {
      list.forEach(item => {
        resList.push(item.id);
        if (item.children) {
          this.getAllTypeId(item.children, resList);
        }
      });
    }
  },
  watch: {
    categoryName(val) {
      this.$refs.knowledgeTree.filter();
    }
  }
};
</script>

<style lang="scss" scoped>
@import url('../comment/css/workSheetCommont.scss');
.contents {
  height: 100%;
  padding-bottom: 8px;
}
.left {
  padding: 0 8px;
  padding-top: 10px;
  background-color: #fff;
  border-radius: 4px;
  margin-right: 8px;
  width: 220px;
  > div:first-child {
    margin-bottom: 8px;
  }
  .el-button {
    margin-left: 15px;
  }
}
.right {
  padding: 8px;
  border-radius: 4px;
  background-color: #fff;
}
.search-bar {
  margin-bottom: 8px;
}
.first-tree-icon {
  background: url(../../../assets/img/other/ztree_all.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.has-child-tree-icon {
  background: url(../../../assets/img/other/ztree_folder.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.leaf-tree-icon {
  background: url(../../../assets/img/other/ztree_file.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.tree-scroll {
  width: 100%;
  height: calc(100% - 40px);
}
/deep/.tree-scroll-wrap {
  height: calc(100% + 17px);
}
/deep/.el-tree {
  display: inline-block;
}
</style>
