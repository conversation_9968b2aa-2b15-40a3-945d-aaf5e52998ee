import Vue from 'vue';
import Router from 'vue-router';
import VueRouter from 'vue-router';
Vue.use(Router);
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
let threeRoutes = [];
let routes = [];
const modulesFiles = require.context('./modules', true, /\.js$/);
modulesFiles.keys().forEach(key => {
  threeRoutes = threeRoutes.concat(modulesFiles(key).default);
});
let home = [];

routes = [].concat(home, threeRoutes);
export default routes; //导出全部路由
export const _threeRoutes = threeRoutes; //导出树型结构路由

/**@desc **/
export const getRouter = function(props) {
  const { container } = props;
  let base = '';
  if (window.__POWERED_BY_QIANKUN__) {
    window.qiankunContainer = container; //微应用的当前容器的根节点  框架里面要用到 别修改
    base = props.data.activeRule || '/';
  } else {
    window.qiankunContainer = document; //主应用
    base = document.querySelector('base').href || '/';
    base = base.replace(/^https?:\/\/[^\/]+/, '');
  }
  const router = new VueRouter({
    base,
    mode: 'history',
    routes
  });
  return router;
};
