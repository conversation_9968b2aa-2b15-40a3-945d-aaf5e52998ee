const pageFirst = 10;

export default {
  namespaced: true,
  state: () => ({
    pageSizeArr: [pageFirst, 20, 30, 40], //选择分页
    pageSize: pageFirst, //分页显示多少条数据
    cacheRoute: [], //缓存页面
    token: '',
    userMessage: '',
    isFullScreen: false, //浏览器是否为全屏
    userInfo: {
      token: ''
    }, //用户基本消息
    hospitalCode: '',
    systemCustomCode: {}, //客户定制化参数
    personalSortData: {} //人员排序参数
  }),
  actions: {},
  mutations: {
    /**@desc 更新数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    setData(state, { label, value }) {
      state[label] = value;
    }
  }
};
