import styleOptions from '@/assets/css/var.scss';
export default {
  data() {
    return {
      systemName: '',
      logoUrl: '',
      orgCode: '',
      showSwitchTab: false,
      showInstitutionSelect: false,

      showAnonymousBox: false,
      showForgetPwdText: false,
      showRememberPwd: false,
      showVerifyCode: true,
      verifyCodeType: 1,
      countdown: 60,
      loginTimeCounterText: '获取验证码',
      originalContent: 'Copyright2020©湖南创星科技股份有限公司版权所有',

      institutionList: [],
      institution: {}
    };
  },
  computed: {
    boxHeight() {
      let boxH = 334,
        switchTabH = 74,
        institutionH = 52,
        verifyCodeH = 60,
        operateH = 44;
      if (this.showSwitchTab) {
        boxH += switchTabH;
      }
      if (this.showInstitutionSelect) {
        boxH += institutionH;
      }
      if (this.showVerifyCode) {
        boxH += verifyCodeH;
      }
      if (this.showLoginOperate) {
        boxH += operateH;
      }
      if (this.orgCode == 'yysdsrmyy') {
        boxH += 24;
      }
      return boxH + 'px';
    },
    showLoginOperate() {
      return (
        this.showRememberPwd || this.showAnonymousBox || this.showForgetPwdText
      );
    }
  },
  async created() {
    await this.getBackground();
  },
  methods: {
    getBackground() {
      this.ajax.getSafeGlobalSetting().then(res => {
        if (res.success == false) {
          return;
        }
        let object = res.object || {};
        this.showSwitchTab = object.smscode == 1;
        this.showForgetPwdText = object.forgetPwd == 1;
        this.showVerifyCode = object.verifyCode == 1;
        this.verifyCodeType = object.verifyCodeType ? object.verifyCodeType : 1;

        this.remember = String(object.rememberPwd);
        this.showRememberPwd = object.rememberPwd == 1;
        this.showAnonymousBox = object.anonymousBox == 1;

        this.showInstitutionSelect = object.platformLoginType == 2;
        this.orgCode = object.orgCode || '';

        this.systemName = object.loginTitle || '综合协同办公平台';
        this.logoUrl =
          '/ts-basics-bottom/fileAttachment/readFile/' + object.loginTopLogo;
        object.originalContent
          ? (this.originalContent = object.originalContent)
          : null;

        this.$nextTick(() => {
          if (object.loginPageBackground) {
            this.$refs.loginBody.style.backgroundImage = object.loginPageBackground
              ? 'url(/ts-basics-bottom/fileAttachment/readFile/' +
                object.loginPageBackground +
                ')'
              : 'url(../../assets/img/login/img_beijing_c_s.png)';
          } else {
            this.$refs.loginBody.classList.add('default');
          }
          if (object.recordNumber) {
            var h = '';
            if (object.recordLinkUrl) {
              h =
                '<a class="alink" style="color:#999;font-size:12px;" href="' +
                object.recordLinkUrl +
                '" target="_blank">' +
                object.recordNumber +
                '</a>';
            } else {
              h =
                '<a class="alink" style="color:#999;font-size:12px;" href="javascript:;" >' +
                object.recordNumber +
                '</a>';
            }
            let node = document.querySelector('.recordNumber');
            if (node) {
              node.innerHTML = h;
              node.classList.remove('none');

              node.querySelector('.alink').onmouseover = function() {
                node.querySelector('.alink').style.color =
                  styleOptions['theme-color'];
              };

              node.querySelector('.alink').onmouseout = function() {
                node.querySelector('.alink').style.color = '#999';
              };
            }
          }
        });
      });
    }
  }
};
