<template>
  <div ref="loginBody" id="loginBody" class="login__flex" name="loginContent">
    <div class="login__box">
      <div class="logo">
        <img :src="logoUrl" />
      </div>
      <div class="box" :style="{ height: boxHeight }">
        <div class="wrapper">
          <div class="steps">
            <div
              class="step"
              :class="tabType == 1 ? 'login-view-account' : 'login-view-sms'"
              v-if="loginType == 1"
            >
              <div class="title">{{ systemName }}</div>
              <p class="login-switch-tab" v-if="showSwitchTab">
                <span
                  class="login-tab-item account-login-tab-item"
                  @click="changeTab(1)"
                >
                  账号登录
                </span>
                <span
                  class="login-tab-item sms-login-tab-item"
                  @click="changeTab(2)"
                  >短信登录</span
                >
              </p>
              <div id="loginForm">
                <div
                  class="tab1"
                  v-if="tabType == 1"
                  key="tab1"
                  style="position: relative;"
                >
                  <div class="input__box">
                    <div class="icon">
                      <img
                        src="@/assets/img/login/user.png"
                        width="23"
                        height="23"
                      />
                    </div>
                    <div class="input">
                      <input
                        ref="accountUserCodeInput"
                        type="text"
                        name="usercode"
                        autocomplete="off"
                        @keypress.enter="handleLoginInputEnter"
                        placeholder="请输入账号"
                      />
                    </div>
                  </div>
                  <div class="input__box">
                    <div class="icon">
                      <img
                        src="@/assets/img/login/pwd.png"
                        width="23"
                        height="23"
                      />
                    </div>
                    <div class="input">
                      <input
                        ref="accountPasswordInput"
                        type="password"
                        name="password"
                        autocomplete="off"
                        @keypress.enter="handleLoginInputEnter"
                        placeholder="请输入密码"
                      />
                      <i
                        class="changeStatus oaicon"
                        :class="
                          passwordShowEye ? 'oa-icon-eye-fill' : 'oa-icon-eye'
                        "
                        @click="
                          hanleShowPassWordEvent(
                            'accountPasswordInput',
                            'passwordShowEye'
                          )
                        "
                      ></i>
                    </div>
                  </div>
                  <div class="input__box" v-if="showInstitutionSelect">
                    <div class="icon">
                      <img
                        src="@/assets/img/login/org.png"
                        width="23"
                        height="23"
                      />
                    </div>
                    <div class="input">
                      <el-select
                        style="width: 100%"
                        v-model="institution.orgCode"
                        clearable
                        popper-class="institution-select"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item of institutionList"
                          :key="item.orgCode"
                          :label="item.orgName"
                          :value="item.orgCode"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="verify-content" v-if="showVerifyCode">
                    <template v-if="verifyCodeType == 2">
                      <div class="input">
                        <input
                          ref="accountVerifyCodeInput"
                          type="text"
                          name="verify"
                          autocomplete="off"
                          placeholder="请输入验证码"
                          @keypress.enter="handleLoginInputEnter"
                        />
                      </div>
                      <div style="width: 114px; text-align: center;">
                        <button
                          class="login-time-counter"
                          :disabled="countdown > 0 && countdown < 60"
                          @click="handleTimeCounter"
                        >
                          {{ loginTimeCounterText }}
                        </button>
                      </div>
                    </template>
                    <template v-else>
                      <div class="input">
                        <input
                          ref="accountVerifyCodeInput"
                          type="text"
                          name="verify"
                          autocomplete="off"
                          placeholder="请输入验证码"
                          @keypress.enter="handleLoginInputEnter"
                        />
                      </div>
                      <img
                        style="cursor: pointer;"
                        @click="handleSwitchVerify"
                        :src="
                          '/ts-basics-bottom/verify/captcha?imageKey=' +
                            verifyImgKey
                        "
                        alt=""
                      />
                    </template>
                  </div>
                  <div class="login-operate" v-if="showLoginOperate">
                    <div class="memory-box" v-if="showRememberPwd">
                      <el-checkbox
                        v-model="remember"
                        true-label="1"
                        false-label="0"
                      >
                        记住密码
                      </el-checkbox>
                    </div>

                    <div class="fr forget">
                      <a
                        v-if="showAnonymousBox"
                        @click="handleShowAnonyMousBox"
                      >
                        意见反馈
                      </a>
                      <a
                        v-if="showForgetPwdText"
                        @click="
                          loginType = 2;
                          fogetForm = {};
                          fogetToken = '';
                        "
                      >
                        忘记密码
                      </a>
                    </div>
                  </div>
                </div>
                <div class="tab2" v-if="tabType == 2" key="tab2">
                  <div class="input__box">
                    <div class="icon">
                      <img
                        src="@/assets/img/login/user.png"
                        width="23"
                        height="23"
                      />
                    </div>
                    <div class="input">
                      <input
                        ref="smsUserCodeInput"
                        v-model="smsLoginForm.empPhone"
                        type="text"
                        autocomplete="off"
                        @keypress.enter="handleLoginInputEnter"
                        placeholder="请输入手机号码"
                      />
                    </div>
                  </div>
                  <div class="input__box">
                    <div class="icon">
                      <img
                        src="@/assets/img/login/sms.png"
                        width="23"
                        height="23"
                      />
                    </div>
                    <div class="input">
                      <input
                        ref="smsVerifyCodeInput"
                        v-model="smsLoginForm.verifyCode"
                        type="text"
                        autocomplete="off"
                        placeholder="请输入短信验证码"
                        style="width: 60%"
                        @keypress.enter="handleLoginInputEnter"
                      />

                      <button
                        ref="getSMSCode"
                        @click="getSmsLoginFormVerifyCode"
                        type="button"
                        class="valdiateCodeBtn"
                      >
                        获取短信验证码
                      </button>
                    </div>
                  </div>
                </div>
                <div class="loginbtn__box">
                  <input
                    name="remarkid"
                    type="hidden"
                    th:value="${returnURL}"
                  />
                  <input
                    id="userLoginSubmit"
                    type="submit"
                    value="登 录"
                    class="loginbtn"
                    @click="handleLogin"
                  />
                </div>

                <div class="tips-img-container" v-if="orgCode == 'yysdsrmyy'">
                  <a
                    href="https://ipw.cn/ipv6webcheck/?site=ipw.cn"
                    title="本站支持IPv6访问"
                    target="_blank"
                  >
                    <img
                      class="tips-img"
                      alt="本站支持IPv6访问"
                      src="https://static.ipw.cn/icon/ipv6-s1.svg"
                    />
                  </a>
                </div>
                <div class="support" v-else>
                  支持的浏览器:
                  <a href="https://down.360safe.com/cse/360cse_13.0.2250.0.exe">
                    <img src="@/assets/img/login/360.png" alt="" />
                    <span>360极速</span>
                  </a>
                  <a
                    href="https://link.zhihu.com/?target=https%3A//dl.google.com/tag/s/appguid%253D%257B8A69D345-D564-463C-AFF1-A69D9E530F96%257D%2526iid%253D%257B77CCCA8B-F59C-E570-E701-E2575690F29C%257D%2526lang%253Dzh-CN%2526browser%253D3%2526usagestats%253D0%2526appname%253DGoogle%252520Chrome%2526needsadmin%253Dprefers%2526ap%253Dx64-stable-statsdef_1%2526installdataindex%253Dempty/chrome/install/ChromeStandaloneSetup64.exe"
                  >
                    <img src="@/assets/img/login/chrome.png" alt="" />
                    <span>谷歌</span>
                  </a>
                </div>
              </div>
            </div>
            <div class="step forgetFormBox" v-else-if="loginType == 2">
              <div class="title">{{ systemName }}</div>
              <input type="hidden" name="repToken" id="repToken" />
              <div style="margin-top: 70px;">
                <h5 style="font-size: 16px; font-weight: 600;">找回密码</h5>
                <div class="input__box" style="margin-top: 0">
                  <div class="input inputall">
                    <input
                      ref="fogetAccountInput"
                      v-model="fogetForm.userAccount"
                      type="text"
                      placeholder="请输入账号"
                      style="width: 60%"
                      autocomplete="off"
                    />
                    <button
                      ref="getFogetFormValidate"
                      @click="getFogetFormVerification"
                      type="button"
                      class="valdiateCodeBtn"
                    >
                      获取验证码
                    </button>
                  </div>
                </div>
                <div class="input__box" style="margin-top: 0">
                  <div class="input inputall">
                    <input
                      ref="fogetVerifyCodeInput"
                      v-model="fogetForm.verifyCode"
                      type="text"
                      placeholder="请输入验证码"
                      autocomplete="off"
                    />
                  </div>
                </div>
              </div>
              <div class="loginbtn__box">
                <input
                  type="button"
                  value="下一步"
                  class="loginbtn"
                  style="height: 40px; line-height: 40px; margin-top: 30px"
                  @click="verifyFogetForm"
                />
              </div>
              <div class="login-operate" style="text-align: right">
                <a
                  class="forget returnLogin"
                  @click="
                    fogetForm = {};
                    loginType = 1;
                    fogetToken = '';
                  "
                  >返回登陆</a
                >
              </div>
            </div>
            <div class="step resetFormBox" v-else>
              <div class="title">{{ systemName }}</div>
              <input type="hidden" name="resetToken" id="resetToken" />
              <div style="margin-top: 70px;">
                <h5 style="font-size: 16px; font-weight: 600;">重置密码</h5>
                <div class="input__box">
                  <div class="input inputall">
                    <input
                      ref="newPasswordInput"
                      v-model="resetForm.password"
                      type="password"
                      placeholder="请输入新密码"
                      autocomplete="off"
                    />
                    <i
                      class="changeStatus oaicon"
                      :class="
                        resetShowEyes ? 'oa-icon-eye-fill' : 'oa-icon-eye'
                      "
                      @click="
                        hanleShowPassWordEvent(
                          'newPasswordInput',
                          'resetShowEyes'
                        )
                      "
                    ></i>
                  </div>
                </div>
                <div class="input__box">
                  <div class="input inputall">
                    <input
                      ref="confirmPasswordInput"
                      v-model="resetForm.confirmPassword"
                      type="password"
                      placeholder="确认新密码"
                      autocomplete="off"
                    />
                    <i
                      class="changeStatus oaicon"
                      :class="
                        resetShowEyes ? 'oa-icon-eye-fill' : 'oa-icon-eye'
                      "
                      @click="
                        hanleShowPassWordEvent(
                          'confirmPasswordInput',
                          'resetRepeatEyes'
                        )
                      "
                    ></i>
                  </div>
                </div>
              </div>
              <div class="loginbtn__box">
                <input
                  @click="handleResetPassword"
                  type="button"
                  value="提交"
                  class="loginbtn"
                  style="height: 40px; line-height: 40px; margin-top: 30px"
                />
              </div>
              <div class="login-operate" style="text-align: right">
                <a
                  class="forget returnLogin"
                  @click="
                    fogetForm = {};
                    resetForm = {};
                    loginType = 1;
                    resetToken = '';
                  "
                  >返回登陆</a
                >
              </div>
            </div>
            <div class="step resetSuccess"></div>
          </div>
        </div>
        <div class="browerSupport"></div>
        <div class="coopyright">
          <p class="none recordNumber"></p>
          <p>
            <a
              href="http://www.trasen.cn/"
              class="alink"
              target="_blank"
              id="original"
              >{{ originalContent }}</a
            >
          </p>
        </div>
      </div>
    </div>
    <Anonymous ref="Anonymous" />
  </div>
</template>
<script>
// import '@trasen-oa/trasen-ui-web/lib/tsElement.css';
import getSetData from './getSetData.js';
import loginVerify from './drawVerify.js';
import Anonymous from './components/anonymous';
import common from '@/unit/common.js';
import { Encrypt } from '@/utils/encrypt.js';
const CodeArray = 'abcdefghijklmnopqrstuvwxyz';

export default {
  components: {
    Anonymous
  },
  mixins: [getSetData, loginVerify],
  data() {
    return {
      tabType: 1, //登录方式 1 账号登录 2 短信登录
      loginType: 1, //登录状态   1 登录  2 校验本人  3重置密码
      passwordShowEye: false, //密码框是否有 show 这个class

      smsLoginForm: {}, //短信登录表单数据
      smsLoginToken: '', //短信登录的token

      fogetForm: {}, //忘记密码表单数据
      fogetToken: '', //忘记密码的token

      remember: '0',

      resetForm: {}, //重置密码表单数据
      resetToken: '', //重置密码的token
      resetShowEyes: false, //是否显示蓝色眼睛 新密码
      resetRepeatEyes: false, //是否显示蓝色眼睛 确认新密码
      verifySwitch: true,
      verifyImgKey: ''
    };
  },
  mounted() {
    // 删除锁屏标识
    sessionStorage.removeItem('lock');
    this.remember = this.$cookies.get('remember') || '1';
    if (this.remember === '1') {
      this.$refs.accountUserCodeInput.value =
        this.$cookies.get('rememberUsercode') || '';
      this.$refs.accountPasswordInput.value =
        this.$cookies.get('rememberPassword') || '';
    }

    this.$refs.accountUserCodeInput.addEventListener(
      'blur',
      this.handleBlurAccount,
      false
    );

    if (this.remember == '1') {
      this.handleBlurAccount();
    }

    !this.verifyImgKey && this.handleSwitchVerify();
  },
  watch: {
    loginType(val) {
      if (val === 1) {
        // this.$nextTick(this.drawLoginVerify);
        this.handleSwitchVerify();
      }
    }
  },
  methods: {
    async handleBlurAccount() {
      this.institutionList = [];
      let usercode = this.$refs.accountUserCodeInput.value;

      let res = await this.ajax.getMultipleOrg(usercode);
      if (res.success == false) {
        this.$message.error(res.message || '获取机构失败!');
        return;
      }

      if (res.object && res.object.length) {
        this.institutionList = res.object;
        this.$set(this.institution, 'orgCode', res.object[0].orgCode);
        this.$set(this.institution, 'orgName', res.object[0].orgName);
      }
    },
    changeTab(tab) {
      if (tab == 1) {
        this.tabType = 1;
        this.$nextTick(() => {
          if (
            this.$refs.accountUserCodeInput.value &&
            this.$refs.accountPasswordInput.value
          ) {
            this.$refs.accountVerifyCodeInput.focus();
          } else {
            this.$refs.accountUserCodeInput.focus();
          }
        });
      } else {
        this.tabType = 2;
        this.smsLoginForm = {};
        this.smsLoginToken = '';
        this.$nextTick(() => {
          this.$refs.smsUserCodeInput.focus();
        });
      }
    },
    //显示/隐藏意见反馈弹框
    handleShowAnonyMousBox() {
      this.$refs.Anonymous.handleToggleShow();
    },
    handleSwitchVerify() {
      let verifyImgKey = '';
      for (let i = 0; i < 4; i++) {
        const isNum = Math.round(Math.random()),
          isUpper = Math.round(Math.random());
        if (isNum) {
          verifyImgKey += String(Math.ceil(Math.random() * 10));
        } else {
          let code = CodeArray[Math.floor(Math.random() * 26)];
          verifyImgKey += isUpper ? code.toUpperCase() : code;
        }
      }

      this.verifyImgKey = verifyImgKey;
    },
    handleLogin() {
      if (this.tabType == 1) {
        this.handleAccountLoginVerify();
      } else {
        this.handleSMSLogin();
      }
    },
    //选中下一个input框 如果是密码框则登录
    handleLoginInputEnter(e) {
      let inputList = document.querySelectorAll(
        '[name="loginContent"] #loginForm .input input'
      );

      inputList = [...inputList];

      if (this.loginType == 1 && this.showInstitutionSelect) {
        const index = inputList.findIndex(
          input => input.className == 'el-input__inner'
        );
        inputList.splice(index, 1);
      }

      for (let i = 0; i < inputList.length; i++) {
        if (e.target != inputList[i]) {
          continue;
        }
        if (i == inputList.length - 1) {
          inputList[0].focus();
        } else {
          inputList[i + 1].focus();
        }
      }
      if (e.target.name == 'verify') {
        this.handleAccountLoginVerify();
      }
      if (e.target.name == 'verifyCode') {
        this.handleSMSLogin();
      }
    },
    //处理显示密码点击事件
    hanleShowPassWordEvent(name, shouldName) {
      let passWordInput = this.$refs[name];

      this[shouldName] = !this[shouldName];

      passWordInput.type == 'password'
        ? (passWordInput.type = 'text')
        : (passWordInput.type = 'password');
    },

    //短信验证登录
    handleTimeCounter() {
      let rules = {
        usercode: [{ required: true, message: '请输入账号' }],
        password: [{ required: true, message: '请输入密码' }]
      };
      if (!this.valdiateAccountLogin(rules)) {
        return;
      }
      let loginData = {
        usercode: this.$refs.accountUserCodeInput.value,
        password: this.$refs.accountPasswordInput.value
      };
      if (
        this.remember !== '1' ||
        this.$refs.accountPasswordInput.value !==
          this.$cookies.get('rememberPassword')
      ) {
        loginData.password = Encrypt(loginData.password.trim()).toString();
      }
      this.$api({
        url: '/ts-basics-bottom/user/login/sendLoginVerifyCode',
        method: 'post',
        data: JSON.stringify(loginData),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async res => {
        if (res.success) {
          const timeCounterInterval = setInterval(() => {
            this.countdown--;
            if (this.countdown == 0) {
              clearInterval(timeCounterInterval);
              this.countdown = 60;
              this.loginTimeCounterText = '获取验证码';
            } else {
              this.loginTimeCounterText = `${this.countdown}秒后重新获取`;
            }
          }, 1000);
          this.$message.success(res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //账号登录校验
    async handleAccountLoginVerify() {
      if (this.showInstitutionSelect) {
        if (!this.institution || !this.institution.orgCode) {
          this.$message.error('请选择机构');
          return;
        }
      }
      let rules = {
        usercode: [{ required: true, message: '请输入账号' }],
        password: [{ required: true, message: '请输入密码' }],
        verify: [{ required: true, message: '请输入验证码' }]
      };
      if (!this.showVerifyCode) {
        delete rules.verify;
      }
      if (!this.valdiateAccountLogin(rules)) {
        return;
      }

      let verifyDom = document.querySelector(
        `[name="loginContent"] #loginForm .input input[name="verify"]`
      );

      let loginData = {
        usercode: this.$refs.accountUserCodeInput.value,
        password: this.$refs.accountPasswordInput.value
      };
      loginData.fromType = 'OA';
      loginData.verifyCode = verifyDom?.value;
      loginData.remarkid = location.origin + '#/index?encrypt=0';
      if (
        this.remember !== '1' ||
        this.$refs.accountPasswordInput.value !==
          this.$cookies.get('rememberPassword')
      ) {
        loginData.password = Encrypt(loginData.password.trim()).toString();
      }
      if (this.showInstitutionSelect) {
        loginData = Object.assign({}, loginData, this.institution);
      }
      if (this.showVerifyCode && this.verifyCodeType == 2) {
        this.$api({
          url: '/ts-basics-bottom/user/login/loginVerifyCode',
          method: 'post',
          data: {
            usercode: loginData.usercode,
            captcha: loginData.verifyCode
          }
        }).then(async res => {
          if (res.success) {
            this.handleAccountLogin(loginData);
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.handleAccountLogin(loginData);
      }
    },
    //账号登录
    handleAccountLogin(loginData) {
      this.$api({
        url: '/ts-basics-bottom/user/login',
        method: 'post',
        data: JSON.stringify(loginData),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async res => {
        if (res.success == false) {
          this.$message.error(res.message);
          // this.drawLoginVerify();
          this.handleSwitchVerify();
          document.querySelector(
            '[name="loginContent"] #loginForm .input input[name="verify"]'
          ).value = '';
          return;
        }

        this.setLoginCookie(res.object);
        this.$store.state.common.token = res.object.token;

        if (this.showRememberPwd && this.remember === '1') {
          // 记住密码 存储标识与密码
          this.$cookies.set('rememberUsercode', loginData.usercode);
          this.$cookies.set('rememberPassword', loginData.password);
        }
        let orgName = '';
        this.institutionList.forEach(item => {
          if (item.orgCode == this.institution.orgCode) {
            orgName = item.orgName;
          }
        });
        localStorage.setItem(
          'values',
          JSON.stringify({
            orgCode: this.institution.orgCode,
            orgName: orgName
          })
        );
        localStorage.setItem('password', loginData.password);
        this.recordLoginLog({
          userCode: res.object.usercode,
          userName: res.object.username,
          source: 'PC'
        });
        await this.checkLowerPassword();
        await this.getHomeDatas(res.object.token);
      });
    },

    setLoginCookie(loginData) {
      this.$cookies.set('THPMSCookie', loginData.token);
      this.$cookies.set('token', loginData.token);
      this.$cookies.set('noticeType', '1');
      this.$cookies.set('sso_user_code', loginData.id);
      this.$cookies.set('sso_domain_user_code', loginData.usercode);
      this.$cookies.set('sso_sysRoleCode', loginData.sysRoleCode);
    },

    /**@desc 日志接口，检查是否为弱密码 */
    recordLoginLog(sysAccessLog) {
      this.$api({
        url: '/ts-information/api/sysAccessLog/save',
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(sysAccessLog),
        method: 'POST'
      });
    },

    /**@desc 检查是不是弱密码 */
    async checkLowerPassword() {
      let lowerRes = await this.ajax.getAllGlobalSetting();
      if (lowerRes.success) {
        let refP = this.$refs.accountPasswordInput.value;
        let object = lowerRes.object;

        // 弱密码校验开启 且为弱密码则提示修改 isLowerPassWord = 1
        if (object?.remindPassword == 1) {
          let check = true;
          let level = common.pwdCheckStrong(refP, object?.passwordLength);
          let rules = [];

          if (object?.passwordRule) {
            rules = object.passwordRule.split(',');
          }

          if (level.level < rules.length || level.level == -1) {
            check = false;
          }

          for (var i = 0; i < rules.length; i++) {
            if (!level.checkType[rules[i]]) {
              check = false;
            }
          }
          if (!check) {
            let tips = '当前密码强度较低，为保护账号安全，请尽快更新密码';
            this.$message.warning(tips);
            this.$cookies.set('isLowerPassWord', '1');
          } else {
            this.$cookies.set('isLowerPassWord', '0');
          }
          this.$store.state.common.globalSetting = object;
        }

        // 校验是否与默认密码相同开启 且相同则提示修改 isModifyDefaultPwd = 1
        if (object?.modifyDefaultPwd == 1) {
          if (this.loginPswIsEqualPsdPreset(refP, object?.passwordPreset)) {
            let tips = '登陆密码与系统初始密码相同，请您重置密码';
            this.$message.warning(tips);
            this.$cookies.set('isModifyDefaultPwd', '1');
          } else {
            this.$cookies.set('isModifyDefaultPwd', '0');
          }
          this.$store.state.common.globalSetting = object;
        }
      }
    },

    loginPswIsEqualPsdPreset(refP, passwordPreset) {
      return (
        refP === passwordPreset ||
        refP === Encrypt(passwordPreset.trim()).toString()
      );
    },

    /**@desc 获取首页信息 */
    async getHomeDatas(token) {
      await this.$api({
        url: '/ts-basics-bottom/rolePage/getRolePage',
        method: 'POST'
      }).then(async homeRes => {
        let homeUrl =
          homeRes.success == false
            ? '/index'
            : (homeRes.object && homeRes.object.pageUrl) || '/index';

        let sysRoleCode = this.$cookies.get('sso_sysRoleCode') || '';
        if (sysRoleCode && sysRoleCode?.includes('EXTERNAL_PERSONNEL')) {
          let res = await this.ajax.getMenuList();
          if (res.success) {
            let menu = res.object[0];
            if (menu.menus.length === 1) {
              homeUrl = this.recursionFindLink(menu.menus[0]);
            }
          }
        }

        sessionStorage.setItem('homeUrl', homeUrl);
        //由于调了这个接口后THPMSCookie被修改，重新设置一下
        this.$cookies.set('THPMSCookie', token);
        this.$router.push(homeUrl);
        this.$root.$emit('login');
      });
    },

    recursionFindLink(menuItem) {
      if (menuItem.alink === '#' && menuItem.menus.length) {
        return this.recursionFindLink(menuItem.menus[0]);
      } else {
        return menuItem.alink;
      }
    },

    //校验账号登录
    valdiateAccountLogin(rules) {
      let allValidate = true;

      Object.keys(rules).some(key => {
        let validate = true;

        let dom = document.querySelector(
          `[name="loginContent"] #loginForm .input input[name="${key}"]`
        );
        rules[key].some(rule => {
          if (rule.validator) {
            rule.validator(dom.value, dom, cb => {
              let valiRes = cb ? false : true;
              validate = validate && valiRes;
            });
          } else {
            if (rule.required) {
              validate = validate && (dom.value ? true : false);
            } else {
              validate = validate && true;
            }
          }

          if (!validate) {
            this.$message.error(rule.message);
            dom.focus();
          }
          return !validate;
        });

        allValidate = allValidate && validate;
        return !validate;
      });
      return allValidate;
    },
    getSmsLoginFormVerifyCode() {
      var reg = /1[^0,2]\d{9}/;
      if (!this.smsLoginForm.empPhone) {
        this.$message.error('请输入手机号码');
        this.$refs.smsUserCodeInput.focus();
        return;
      } else if (!reg.test(this.smsLoginForm.empPhone)) {
        this.$message.error('请输入正确的手机号码');
        this.$refs.smsUserCodeInput.focus();
        return;
      }
      this.$api({
        url: '/ts-information/messageLogin/sendVerifyCode',
        method: 'post',
        data: {
          empPhone: this.smsLoginForm.empPhone
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误，验证码发送失败');
          return;
        }
        this.$refs.smsVerifyCodeInput.focus();
        this.$refs.getSMSCode.disabled = true;
        this.smsLoginToken = res.object;
        if (res.message) {
          this.$message.success(res.message);
        }
        this.times('getSMSCode', '获取短信验证码');
      });
    },
    //校验短信登录表单数据进行短信登录
    async handleSMSLogin() {
      if (!this.smsLoginForm.empPhone) {
        this.$message.error('请输入手机号码');
        this.$refs.smsUserCodeInput.focus();
        return;
      }
      var reg = /1[^0,2]\d{9}/;
      if (!reg.test(this.smsLoginForm.empPhone)) {
        this.$message.error('请输入正确的手机号码');
        this.$refs.smsUserCodeInput.focus();
        return;
      }

      if (!this.smsLoginForm.verifyCode) {
        this.$message.error('请输入验证码');
        this.$refs.smsVerifyCodeInput.focus();
        return;
      }
      let formData = {
        ...this.smsLoginForm,
        token: this.smsLoginToken
      };
      this.$api({
        url: '/ts-information/messageLogin/SMSlogin',
        method: 'post',
        data: formData
      }).then(async res => {
        if (res.success == false) {
          this.$message.error(res.message || '验证码错误');
          this.smsLoginForm.verifyCode = '';
          this.$refs.smsVerifyCodeInput.focus();
          return;
        }
        this.setLoginCookie(res.object);
        this.$store.state.common.token = res.object.token;

        await this.getHomeDatas(res.object.token);
      });
    },
    //校验忘记密码表单数据
    verifyFogetForm() {
      if (!this.fogetForm.userAccount) {
        this.$message.error('请输入账号');
        this.$refs.fogetAccountInput.focus();
        return;
      }
      if (!this.fogetForm.verifyCode) {
        this.$message.error('请输入验证码');
        this.$refs.fogetVerifyCodeInput.focus();
        return;
      }

      let formData = {
        ...this.fogetForm,
        token: this.fogetToken
      };

      this.$api({
        url: '/ts-information/messageInternal/verifyCode',
        method: 'post',
        data: formData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '验证码错误');
          this.fogetForm.verifyCode = '';
          this.$refs.fogetVerifyCodeInput.focus();
          return;
        }
        this.loginType = 3;
        this.resetToken = res.object;
      });
    },
    //获取忘记密码验证码
    getFogetFormVerification() {
      if (!this.fogetForm.userAccount) {
        this.$message.error('请输入账号');
        this.$refs.fogetAccountInput.focus();
        return;
      }

      let formData = {
        userAccount: this.fogetForm.userAccount
      };

      this.$api({
        url: '/ts-oa/employee/valdiateUser',
        method: 'post',
        data: formData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误');
          return;
        }
        this.fogetToken = res.object;
        //发送验证码
        this.sendFogetFormVerifyCode(res.object);
      });
    },
    //发送忘记密码验证码
    sendFogetFormVerifyCode(token) {
      this.$api({
        url: '/ts-information/messageInternal/sendVerifyCode',
        method: 'post',
        data: {
          token,
          userAccount: this.fogetForm.userAccount
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误，验证码发送失败');
          return;
        }
        this.$refs.getFogetFormValidate.disabled = true;
        this.fogetToken = res.object;
        if (res.message) {
          this.$message.success(res.message);
        }
        this.times('getFogetFormValidate', '获取验证码');
      });
    },
    //倒计时
    times(ref, text) {
      let time = 120;
      this.$refs[ref].innerHTML = time + '秒后重新获取';
      let timer = setInterval(() => {
        time--;
        this.$refs[ref].innerHTML = time + '秒后重新获取';
        if (timer && time == 0) {
          this.$refs[ref].innerHTML = text;
          this.$refs[ref].disabled = false;
          clearInterval(timer);
        }
      }, 1000);
    },
    //重置密码
    handleResetPassword() {
      if (!this.resetForm.password) {
        this.$message.error('新密码不能为空');
        return;
      }
      if (this.resetForm.password.length < 6) {
        this.$message.error('密码长度不能小于6位');
        return;
      }
      if (this.resetForm.password != this.resetForm.confirmPassword) {
        this.$message.error('两次输入密码不一致');
        return;
      }

      let formData = {
        userAccount: this.fogetForm.userAccount,
        newPassword: Encrypt(this.resetForm.password.trim()).toString(),
        token: this.resetToken
      };

      this.$api({
        url: '/ts-oa/employee/resetPassword',
        method: 'post',
        data: formData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误');
          return;
        }
        this.$message.success('密码修改成功，请重新登录');
        this.loginType = 1;
        this.fogetForm = {};
        this.resetForm = {};
        this.resetToken = '';
      });
    }
  },
  destroy() {
    this.$refs.accountUserCodeInput.removeEventListener(
      'input',
      this.handleBlurAccount,
      false
    );
  }
};
</script>

<style lang="scss">
.institution-select {
  .el-scrollbar {
    padding-bottom: 20px !important;
  }

  .el-select-dropdown__item {
    padding: 8px !important;
    display: flex !important;
    align-items: center !important;
  }
}
</style>

<style lang="scss" scoped>
@import './login.scss';
.login__box {
  .tips-img-container {
    padding-top: 8px;
    font-size: 12px;
    color: #333;
    line-height: 17px;
    text-align: center;
    .tips-img {
      display: inline-block;
      vertical-align: middle;
    }
  }
  ::v-deep {
    .institution-select {
      .el-input__suffix-inner {
        cursor: pointer;
      }
    }
    .el-input__inner {
      border: 0;
      padding: 0;
      font-size: 14px;
      color: #333;
      background-color: transparent;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $theme-color;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: $theme-color;
      border-color: $theme-color;
    }
    .el-checkbox__inner:hover {
      border-color: $theme-color;
    }
    .el-input__icon {
      font-size: 16px;
      color: #999;
    }
  }
}
</style>
