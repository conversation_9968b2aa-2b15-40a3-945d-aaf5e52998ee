<!-- 待办 -->
<template>
  <div class="process">
    <div class="navHead">
      <el-tabs type="border-card" v-model="activeName" @tab-click="handleTab">
        <el-tab-pane
          v-for="(tab, index) in headTabList"
          :key="index"
          :name="tab.name"
        >
          <div slot="label" v-if="tab.isValue && tab.num">
            <el-badge
              :value="tab.num"
              class="item"
              :max="99"
              v-if="tab.isValue"
            >
              <div class="tab">
                <i :class="tab.icon"></i><span>{{ tab.title }}</span>
              </div>
            </el-badge>
          </div>
          <div slot="label" v-else class="tab">
            <i :class="tab.icon"></i><span>{{ tab.title }}</span>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="content" v-loading="loading" v-if="activeName < 6">
      <el-scrollbar
        style="width: 100%;height: 100%;"
        ref="scroll"
        v-infinity-scroll="{
          loadMethod: getFlowList,
          selector: '.options-scrollbar',
          hasFinished: false,
          hasLoading: true
        }"
        wrap-class="options-scrollbar"
        wrap-style="overflow-x: hidden;"
      >
        <div
          class="item"
          v-for="(item, index) in workFlowList"
          :key="index"
          @click="handleDeal(item)"
        >
          <div class="firstTitle">
            <span style="color:red" v-if="item.isPress">[ 催办 ]</span>
            <div
              class="urgencyLevel"
              :class="'level' + item.urgencyLevel"
              v-if="item.urgencyLevel && item.urgencyLevel != 1"
            >
              {{ urgencyLevelFont[item.urgencyLevel] }}
            </div>
            <p class="type-name-style one-line text">
              {{ item.workflowTitle }}
            </p>
          </div>
          <div class="secondTitle">
            <p class="dept">
              <span class="wf-stepName">{{ item.currentStepName }}</span>
              <span class="show-time padding-left-8">{{ showTime(item) }}</span>
              <span class="term" v-if="item.handleAllottedTime"
                >办理期限: {{ showFormatDate(item) }}</span
              >
            </p>
          </div>
        </div>
      </el-scrollbar>
      <no-data v-if="workFlowList.length == 0" />
    </div>
    <div class="content" v-loading="loading" v-else>
      <el-scrollbar
        style="width: 100%;height: 100%;"
        ref="scroll"
        v-infinity-scroll="{
          loadMethod: getFlowList,
          selector: '.options-scrollbar',
          hasFinished: false,
          hasLoading: true
        }"
        wrap-class="options-scrollbar"
        wrap-style="overflow-x: hidden;"
      >
        <div
          class="item"
          v-for="(item, index) in workFlowList"
          :key="index"
          @click="handleFlowStart(item)"
        >
          <i
            :class="item.icon"
            :style="`color: ${item.color};margin:3px 4px 0 0;`"
          ></i>
          {{ item.workflowName }}
        </div>
      </el-scrollbar>
      <no-data v-if="workFlowList.length == 0" />
    </div>
    <div class="bottom" @click="toProcess">查看更多</div>
  </div>
</template>

<script>
import moment from 'moment';
import indexJs from './indexConfig.js';
import noData from '../noData.vue';
import infinityScroll from '@/utils/infinityScroll';
import { deepClone } from '@/utils/deepClone';

export default {
  components: { noData },
  mixins: [indexJs, infinityScroll],
  data() {
    return {
      timer: null,
      activeName: '1',
      workFlowList: [],
      loading: false,
      headTabList: [
        {
          icon: 'oaicon oa-icon-daiban1',
          title: '待办',
          isValue: 1,
          num: 0,
          api: 'getMyHandleWorkflowList',
          name: '1',
          param: {
            handleStatus: 1,
            sidx: 'inst.create_date',
            sord: 'desc'
          }
        },
        {
          icon: 'oaicon oa-icon-jingban',
          title: '经办',
          isValue: 1,
          api: 'getMyHandleWorkflowList',
          name: '2',
          num: 0,
          param: {
            handleStatus: 2,
            pageIndex: 'Y',
            sidx: 'inst.update_date',
            sord: 'desc'
          }
        },
        {
          icon: 'oaicon oa-icon-zaiban',
          title: '在办',
          isValue: 1,
          api: 'getMyLaunchWorkflowList',
          name: '3',
          num: 0,
          param: {
            handleStatus: '',
            status: 1,
            sidx: 'inst.status asc,inst.create_date',
            sord: 'desc'
          }
        },
        {
          icon: 'oaicon oa-icon-chaosong',
          title: '抄送',
          isValue: 1,
          num: 0,
          api: 'getCopyToMyWorkflowList',
          name: '5',
          param: {
            readStatus: 0,
            sidx: 'inst.create_date',
            sord: 'desc'
          }
        },
        {
          icon: 'oaicon oa-icon-banliwanjieshenhe',
          title: '办结',
          isValue: 0,
          api: 'getMyLaunchWorkflowList',
          name: '4',
          param: {
            handleStatus: '',
            status: 2,
            sidx: 'inst.status asc,inst.create_date',
            sord: 'desc'
          }
        },
        {
          icon: 'oaicon oa-icon-ziliaoku',
          title: '我的收藏',
          isValue: 0,
          api: 'getCollectTree',
          name: '6',
          param: {}
        },
        {
          icon: 'oaicon oa-icon-changyongliucheng',
          title: '常用流程',
          isValue: 0,
          api: 'getCommonlyUsed',
          name: '7',
          param: {}
        }
      ],
      tabObject: {},
      urgencyLevelFont: {
        1: '一般',
        2: '加急',
        3: '急件',
        4: '特急'
      },
      wrapDom: null,
      pageNo: 1
    };
  },
  created() {
    this.$root.$on('handleRefreshIndexProcess', this.handleTab);
    this.timer = setInterval(() => {
      //监听子页面关闭事件,轮询时间1000毫秒
      let openWin = this.indexWinSon.filter(item => {
        return !item.closed;
      });
      if (openWin && openWin.length < this.indexWinSon.length) {
        this.indexWinSon = deepClone(openWin);
        this.handleTab();
      }
    }, 1000);
  },
  destroyed() {
    this.timer && clearInterval(this.timer);
  },
  mounted() {
    this.getHomePageWfCount();
    this.tabObject = this.headTabList.find(e => e.name == this.activeName);
  },
  methods: {
    // 角标数据获取
    async getHomePageWfCount() {
      let res = await this.ajax.getHomePageWfCount();
      this.headTabList[0].num = res.object.toDoNum; //待办
      this.headTabList[1].num = res.object.haveDoNum; //经办
      this.headTabList[2].num = res.object.byMeStartNum; //在办
      this.headTabList[3].num = res.object.copyToMeNum; //抄送
    },
    async getFlowList(cb) {
      let params = {
        ...this.tabObject.param,
        pageNo: 1,
        pageSize: 20
      };
      params.pageNo = this.pageNo;
      this.loading = true;
      let res = await this.ajax[this.tabObject.api](params);
      this.loading = false;
      let dataList = res.rows || res.object || [];
      cb(!(dataList && dataList.length));
      if (this.pageNo == 1) this.workFlowList = [];

      if (dataList && dataList.length) {
        this.workFlowList.push(...dataList);
      }
      this.pageNo++;
    },
    showTime(item) {
      if (moment(item.createDate).format('YYYY') == moment().format('YYYY')) {
        return moment(item.createDate).format('MM-DD HH:mm');
      } else {
        return moment(item.createDate).format('YYYY-MM-DD HH:mm');
      }
    },
    showFormatDate(item) {
      let allottedTimeYear = item.handleAllottedTime.slice(0, 4);
      let newYear = moment().format('YYYY');
      let showFormatDate =
        newYear == allottedTimeYear
          ? moment(item.handleAllottedTime).format('MM-DD')
          : moment(item.handleAllottedTime).format('YYYY-MM-DD HH:mm');
      return showFormatDate;
    },
    handleTab() {
      this.getHomePageWfCount();
      this.tabObject = this.headTabList.find(e => e.name == this.activeName);
      this.pageNo = 1;
      this.workFlowList = [];
      this.wrapDom = null;
      this.$nextTick(() => {
        if (!this.wrapDom) {
          this.wrapDom = this.$refs.scroll.$el.querySelector(
            '.options-scrollbar'
          );
        }
        this.wrapDom.resetInfinityScrolling();
      });
    },
    // 查看更多
    toProcess() {
      let data = null;
      let datas = null;
      let trig = '';
      // 待办
      if (this.activeName == '1') {
        this.$router.push('/process/index');
        data = 0;
        datas = {};
        trig = 'changeStatus';
      }
      // 经办
      if (this.activeName == '2') {
        this.$router.push('/process/index');
        data = 1;
        datas = { status: 1 };
        trig = 'changeStatus';
      }
      // 在办
      if (this.activeName == '3') {
        this.$router.push('/process/index');
        data = 2;
        datas = { status: 1 };
        trig = 'changeStatus';
      }
      // 办结
      if (this.activeName == '4') {
        this.$router.push('/process/index');
        data = 2;
        datas = { status: 2 };
        trig = 'changeStatus';
      }
      // 抄送
      if (this.activeName == '5') {
        this.$router.push('/process/index');
        data = 3;
        datas = {};
        trig = 'changeStatus';
      }
      // 我的收藏
      if (this.activeName == '6') {
        this.$router.push('/process/start');
        data = '10';
        trig = 'jumpChangeTabs';
      }
      // 常用流程
      if (this.activeName == '7') {
        this.$router.push('/process/start');
        data = '0';
        trig = 'jumpChangeTabs';
      }
      this.$root.$emit('sendMessageToOldFrame', {
        detail: {
          type: 'operateEvent',
          data: {
            triggerName: trig,
            eventName: 'toDealList',
            data,
            datas
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.process {
  height: 100%;
  position: relative;
  background: #fff;
  .navHead {
    /deep/ .el-tabs {
      box-shadow: none;
      border: none;
      border-bottom: 1px solid #dcdfe6;
      .el-tabs__header {
        margin: 0;
        .el-tabs__nav-wrap {
          .el-tabs__nav-prev,
          .el-tabs__nav-next {
            top: 12px;
            i {
              font-size: 20px;
            }
          }
        }
        .el-tabs__nav-scroll {
          background: #fff;
          .el-tabs__nav {
            height: 55px !important;
          }
          .el-tabs__item {
            border-radius: 0;
            border: none;
            background: #fff;
            height: 55px !important;
            i {
              color: var(--theme-color);
              font-size: 24px;
            }
            &.is-active {
              border-bottom: 1px solid;
            }
            .el-badge__content.is-fixed {
              top: 14px;
            }
          }
        }
      }
      .el-tabs__content {
        display: none;
      }
    }
    .tab {
      display: flex;
      flex-direction: column;
    }
  }
  .content {
    height: calc(100% - 87px);
    position: relative;
    .item {
      padding: 8px;
      cursor: pointer;
      box-shadow: 0px 0.5px 0px 0px #f4f4f4;
      display: flex;

      &:hover {
        background: #edefff;
        flex-wrap: wrap;
        align-items: baseline;
        .firstTitle {
          color: var(--theme-color);
          .type-name-style {
            white-space: unset;
            overflow: visible;
          }
        }
      }
      .urgencyLevel {
        height: 18px;
        padding: 0 5px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: 400;
        margin-right: 4px;
        &.level1 {
          background: #e8ebff;
          border: 1px solid var(--theme-color);
          color: var(--theme-color);
        }
        &.level2 {
          background: #fff4ce;
          border: 1px solid #faaf58;
          color: #faaf58;
        }
        &.level3 {
          background: #ffe9db;
          border: 1px solid #ff6000;
          color: #ff6000;
        }
        &.level4 {
          background: #ffd3cc;
          border: 1px solid #ff3c1f;
          color: #ff3c1f;
        }
      }
      .firstTitle {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
        color: #333;
        .type-name-style {
          font-size: 14px;
          flex: 1 0;
          color: inherit;
          line-height: 18px;
        }
        .one-line {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .secondTitle {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0;
        font-weight: normal;
        font-size: 12px;
        color: #999;
        line-height: 20px;
        .dept {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: flex;
          align-items: center;
          .wf-stepName {
            flex: initial;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .show-time {
            display: inline-block;
            max-width: 100px;
            min-width: 70px;
          }
          .padding-left-8 {
            padding-left: 4px;
          }
        }
      }
    }
  }
  .bottom {
    padding: 3px 0;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    color: var(--theme-color);
    cursor: pointer;
    background: #fafafa;
  }
}
</style>
