<!-- 常用入口 -->
<template>
  <div class="userMenu">
    <div class="cardHead">
      <div class="headLeft">
        <i class="sign"></i>
        <div class="headTitle" data-reftable="0">常用入口</div>
      </div>
      <i class="oaicon oa-icon-shezhi1 headRight" @click="setMenu"></i>
    </div>
    <div class="menuBox" v-loading="loading">
      <div
        class="menuItem"
        v-for="(item, index) in menuList"
        :key="index"
        @click="toGo(item)"
      >
        <div class="access-item">
          <div class="access">
            <a href="javascript:;">
              <i :class="`oaicon ${item.menuIcon}`"></i>
              <p class="access-text">{{ item.menuName }}</p>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="menuFooter" v-if="allMenuList.length > pageSize">
      <div class="operate">
        <a href="javascript:;" class="control_prev" @click="preMenu"></a>
        <a href="javascript:;" class="control_next" @click="nextMenu"></a>
      </div>
    </div>
    <set-menu
      ref="setMenu"
      :allMenuList="allMenuList"
      @refresh="selectQuickMenuList"
    />
  </div>
</template>

<script>
import setMenu from './setMenu.vue';
import { deepClone } from '@/utils/deepClone';
import ResizeObserver from 'resize-observer-polyfill';
export default {
  components: { setMenu },
  data() {
    return {
      loading: false,
      allMenuList: [],
      menuList: [],
      pageSize: 8,
      pageNo: 1,
      pageTotal: 0,
      colmunWidth: 0,
      colmunHeigth: 0,
      resizeObserver: null
    };
  },
  mounted() {
    this.resizeObserver = new ResizeObserver(entries => {
      entries.forEach(entry => {
        const { width, height } = entry.contentRect;
        this.colmunWidth = width;
        this.colmunHeigth = height;
        let wNum = Math.floor(this.colmunWidth / 110),
          hNum = Math.floor(this.colmunHeigth / 85);
        this.pageSize = wNum * hNum;
        this.splitMenu();
      });
    });
    const observedElement = document.querySelector('.menuBox');
    this.resizeObserver.observe(observedElement);
    this.selectQuickMenuList();
  },
  destroyed() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    setMenu() {
      this.$refs.setMenu.open();
    },
    async selectQuickMenuList() {
      this.loading = true;
      let res = await this.ajax.selectQuickMenuList();
      this.loading = false;
      if (res.object) {
        this.allMenuList = res.object.map(i => {
          return {
            menuIcon: i.menuIcon || 'oa-icon-rukou',
            menuName: i.menuName,
            alink: i.menuUrl
          };
        });
        this.splitMenu();
      }
    },
    splitMenu() {
      let list = deepClone(this.allMenuList);
      this.pageTotal = Math.ceil(list.length / this.pageSize);
      this.menuList = this.allMenuList.slice(0, this.pageSize);
    },
    toGo(item) {
      this.$router.push(item.alink);
    },
    sliceMenuList() {
      this.menuList = this.allMenuList.slice(
        (this.pageNo - 1) * this.pageSize,
        this.pageNo * this.pageSize
      );
    },
    preMenu() {
      if (this.pageNo == 1) {
        this.pageNo = this.pageTotal;
      } else {
        this.pageNo = this.pageNo - 1;
      }
      this.sliceMenuList();
    },
    nextMenu() {
      if (this.pageNo == this.pageTotal) {
        this.pageNo = 1;
      } else {
        this.pageNo = this.pageNo + 1;
      }
      this.sliceMenuList();
    }
  }
};
</script>

<style lang="scss" scoped>
.userMenu {
  height: 100%;
  position: relative;
  background: #fff;
  .cardHead {
    height: 39px;
    background: #fff;
    box-shadow: 0px 1px 0px 0px #e4e4e4;
    font-size: 14px;
    font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
    font-weight: bold;
    color: rgba(51, 51, 51, 0.5);
    display: flex;
    justify-content: space-between;
    .headLeft {
      display: flex;
      align-items: center;
      .sign {
        width: 4px;
        height: 18px;
        background: var(--theme-color);
        border-radius: 2px;
        display: inline-block;
        margin: 0 6px 0 8px;
      }
      .headTitle {
        line-height: 30px;
        letter-spacing: 2px;
        color: #333333;
        font-size: 15px;
        font-weight: bold;
      }
    }
    .headRight {
      position: relative;
      top: 0;
      font-size: 25px;
      color: var(--theme-color);
      cursor: pointer;
    }
  }
  .menuBox {
    display: flex;
    flex-wrap: wrap;
    height: calc(100% - 80px);
    .menuItem {
      // width: 25%;
      width: 110px;
      position: relative;
      // height: 50%;
      height: 85px;
      cursor: pointer;
      .access-item {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: table;
        .access {
          height: 100%;
          width: 100%;
          display: table;
          vertical-align: middle;
          text-align: center;
          a {
            display: table-cell;
            height: 100%;
            width: 100%;
            vertical-align: middle;
            color: #333;
            text-decoration: none;
            &:hover {
              color: var(--theme-color);
            }
          }
          i {
            color: #888;
            font-size: 25px;
          }
        }
      }
    }
  }
  .menuFooter {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    .operate {
      margin: 0 auto;
      width: 35px;
      display: flex;
      justify-content: space-between;
      .control_prev,
      .control_next {
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 12px solid #e4e4e4;
        transform: rotate(90deg);
      }
      .control_prev {
        transform: rotate(-90deg);
      }
    }
  }
}
</style>
