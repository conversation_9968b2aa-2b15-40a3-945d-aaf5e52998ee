import { $get, $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  //获取医院页面设置信息 登录使用
  getSafeGlobalSetting() {
    return $get(`${service.tsBasics()}/globalSetting/getSafeGlobalSetting`);
  },
  // 登录成功使用
  getAllGlobalSetting() {
    return $get(`${service.tsBasics()}/globalSetting/getAllGlobalSetting`);
  },
  /**@desc 获取用户按钮权限信息 */
  getUserMenuSourceData(code) {
    return $get(`${service.tsSystem()}/resource/getMenuResource/${code}`);
  },
  uploadFile(data, moduleName = '') {
    return $api({
      url: `${service.tsBasics()}/fileAttachment/upload?moduleName=${moduleName}`,
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data
    });
  },
  // 保存全局设置
  globalSettingSave(data) {
    return $api({
      url: `${service.tsBasics()}/globalSetting/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },
  // 获取附件
  getFileAttachmentByBusinessId(params) {
    return $api({
      url: `${service.tsBasics()}/fileAttachment/getFileAttachmentByBusinessId`,
      method: 'get',
      params: params
    });
  },
  //根据id获取附件信息
  getFileAttachmentByBusinessIdOrId(id) {
    return $api({
      url: `${service.tsBasics()}/fileAttachment/getByIds?ids=${id}`,
      method: 'get'
    });
  },
  // 删除附件
  deleteFileId(params) {
    return $api({
      url: `${service.tsBasics()}/fileAttachment/deleteFileId`,
      method: 'get',
      params: params
    });
  },

  // 页面埋点
  modesUsesSave(data) {
    return $api({
      url: `${service.tsBasics()}/api/modesUses/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 群组获取
  selectOrgGroupList(data) {
    return $api({
      url: `/ts-basics-bottom/employee/orgGroup/selectOrgGroupList`,
      method: 'post',
      data: data
    });
  },

  //获取数据字典
  getDictItemByTypeCode(params) {
    return $api({
      url: `/ts-basics-bottom/dictItem/getDictItemByTypeCode`,
      method: 'get',
      params
    });
  },

  uploadToPing(data) {
    return $api({
      url: `/v1/files/upload`,
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: 'Bearer app-vCBFEshThfpasvpsBnNIXpyM'
      },
      data
    });
  }
};
