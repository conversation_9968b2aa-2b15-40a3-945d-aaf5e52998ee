const createUUID = function() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
};
export const processUtils = {
  createUUID,
  processDeal: {
    start: function(wfNo) {
      var uri = encodeURI(
        '/view-new/processView/start/index.html?workflowNo=' +
          wfNo +
          '&currentStepNo=start'
      );
      var son = window.open(uri, createUUID());
      return son;
    },
    confirm: function(data, isHideContent) {
      var uri = encodeURI(
        '/view-new/processView/audit/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&wfInstanceId=' +
          data.wfInstanceId +
          '&taskId=' +
          data.taskId +
          '&workflowNumber=' +
          data.workflowNumber +
          '&currentStepName=' +
          data.currentStepName +
          '&currentStepNo=' +
          data.currentStepNo +
          '&isHideContent=' +
          isHideContent +
          '&role=deal&type=confirm'
      );
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    countersign: function(data, isHideContent) {
      var uri = encodeURI(
        '/view-new/processView/audit/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&wfInstanceId=' +
          data.wfInstanceId +
          '&taskId=' +
          data.taskId +
          '&workflowNumber=' +
          data.workflowNumber +
          '&currentStepName=' +
          data.currentStepName +
          '&currentStepNo=' +
          data.currentStepNo +
          '&isHideContent=' +
          isHideContent +
          '&role=deal&type=countersign'
      );
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    dealSee: function(data, isHideContent, isCS = '', isHideProcessInfo) {
      var uri = '';
      if (data.status == 2 || data.status == 3 || data.status == 4) {
        data.currentStepNo = 'end';
      }
      uri = encodeURI(
        '/view-new/processView/see/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&wfInstanceId=' +
          data.wfInstanceId +
          '&taskId=' +
          data.taskId +
          '&workflowNumber=' +
          data.workflowNumber +
          '&currentStepName=' +
          data.currentStepName +
          '&currentStepNo=' +
          data.currentStepNo +
          '&isHideContent=' +
          isHideContent +
          '&isCS=' +
          isCS +
          '&isHideProcessInfo=' +
          isHideProcessInfo +
          '&role=deal&type=see'
      );
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    selfSee: function(data, isHideContent) {
      var uri = '';
      if (
        data.status == 2 ||
        data.status == 3 ||
        data.status == 4 ||
        data.status == 5
      ) {
        data.currentStepNo = 'end';
        uri = encodeURI(
          '/view-new/processView/see/index.html?' +
            'workflowNo=' +
            data.workflowNo +
            '&businessId=' +
            data.businessId +
            '&wfInstanceId=' +
            data.wfInstanceId +
            '&taskId=' +
            data.taskId +
            '&workflowNumber=' +
            data.workflowNumber +
            '&currentStepName=' +
            data.currentStepName +
            '&currentStepNo=' +
            data.currentStepNo +
            '&isHideContent=' +
            isHideContent +
            '&role=deal&type=see'
        );
      } else if (data.status == 1) {
        uri = encodeURI(
          '/view-new/processView/audit/index.html?' +
            'workflowNo=' +
            data.workflowNo +
            '&businessId=' +
            data.businessId +
            '&wfInstanceId=' +
            data.wfInstanceId +
            '&taskId=' +
            data.taskId +
            '&workflowNumber=' +
            data.workflowNumber +
            '&currentStepName=' +
            data.currentStepName +
            '&currentStepNo=' +
            data.currentStepNo +
            '&isHideContent=' +
            isHideContent +
            '&role=self&type=confirm'
        );
      }
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    onlySee: function(data, isHideContent, role) {
      var uri = encodeURI(
        '/view-new/processView/see/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&wfInstanceId=' +
          data.wfInstanceId +
          '&taskId=' +
          data.taskId +
          '&workflowNumber=' +
          data.workflowNumber +
          '&currentStepName=' +
          data.currentStepName +
          '&currentStepNo=' +
          data.currentStepNo +
          '&isHideContent=' +
          isHideContent +
          '&role=' +
          role +
          '&type=see'
      );
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    restart: function(data) {
      var uri = encodeURI(
        '/view-new/processView/audit/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&wfInstanceId=' +
          data.wfInstanceId +
          '&taskId=' +
          data.taskId +
          '&workflowNumber=' +
          data.workflowNumber +
          '&currentStepName=' +
          data.currentStepName +
          '&currentStepNo=' +
          data.currentStepNo +
          '&role=self&type=restart'
      );
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    },
    startAgain: function(data) {
      var uri = encodeURI(
        '/view-new/processView/start/index.html?' +
          'workflowNo=' +
          data.workflowNo +
          '&businessId=' +
          data.businessId +
          '&currentStepNo=startAgain&type=startAgain'
      );
      // var son = window.open(uri, '_blank', 'menubar=0,titlebar=0,toolbar=0,left=0,top=0,width=' + common.layoutSize.width + ',height=' + common.layoutSize.height + '');
      var son = window.open(uri, createUUID());
      son.openWiner = 1;
      return son;
    }
  }
};
