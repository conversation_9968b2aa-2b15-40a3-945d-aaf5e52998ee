<template>
  <div
    ref="toastContent"
    class="toast-content toast-come-in"
    v-if="showToast"
    :style="contentStyle"
  >
    <!-- 加载进度条 -->
    <span class="toast-loader"></span>
    <!-- 关闭标签 -->
    <span
      v-if="allowToastClose"
      class="toast-close-icon"
      @click="handleClose"
      :style="{ '--save': '1px' }"
      >&times;</span
    >
    <!-- 消息提示标题 -->
    <div class="toast-header">
      <div class="oaicon oa-icon-gonggao toast-head-icon"></div>
      <h2 class="toast-header-title">{{ heading }}</h2>
    </div>
    <div class="toast-message-content" :style="{ color: textColor }">
      <div
        v-for="(toast, index) of toastTextList"
        :key="index"
        style="white-space: pre-wrap;"
        class="toast-message-item"
        v-html="toast"
      ></div>
    </div>
    <div class="toast-footer">
      <slot name="footer">
        <template v-if="btns">
          <el-button
            v-for="(item, index) of btns"
            :key="index"
            @click="handleToastBtnClick($event, item)"
          >
            {{ index }}
          </el-button>
        </template>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    //提示标题
    heading: {
      type: String,
      default: () => '提示'
    },
    //提示内容
    text: {
      type: [String, Array],
      default: () => ['123']
    },
    //文字颜色
    textColor: {
      type: String,
      default: () => '#333'
    },
    //操作按钮
    btns: {
      type: Object,
      default: () => {}
    },
    //是否显示关闭按钮
    allowToastClose: {
      default: () => true
    },
    //提示时间
    hideAfter: {
      type: Number
    },
    position: {
      type: [String, Object],
      default: () => 'bottom-right',
      validator: function(val) {
        if (typeof val === 'string') {
          return (
            [
              'bottom-left',
              'bottom-right',
              'top-right',
              'top-left',
              'bottom-center',
              'top-center',
              'mid-center'
            ].indexOf(val) !== -1
          );
        }

        return true;
      }
    }
  },
  data() {
    return {
      showToast: true,
      toastTextList: [],
      contentStyle: {
        '--index': 0,
        '--side': 1
      },

      hideTimer: null //toast自动消失定时器
    };
  },
  created() {
    if (typeof this.text == 'string') {
      let val = this.text.replace(/null/g, '');
      this.toastTextList = [val];
    } else if (this.text instanceof Array) {
      this.toastTextList = this.text;
    }

    if (this.hideAfter >= 0) {
      this.hideTimer && clearTimeout(this.hideTimer);
      this.hideTimer = setTimeout(() => {
        this.handleClose();
      }, this.hideAfter);
    }
  },
  watch: {
    text: {
      handler(val) {
        if (typeof val == 'string') {
          let newVal = val.replace(/null/g, '');
          this.toastTextList = [newVal];
        } else if (val instanceof Array) {
          this.toastTextList = val;
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.computedContentPosition();
    });
  },
  methods: {
    handleClose() {
      this.$refs.toastContent.classList.add('toast-come-in');
      this.hideTimer && clearTimeout(this.hideTimer);
      this.$nextTick(() => {
        setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
      this.$emit('close');
      this.$store.dispatch('common/getMessageCount');
    },
    handleToastBtnClick(event, callback) {
      if (typeof callback !== 'function') {
        return;
      }
      callback(this);
    },
    computedContentPosition() {
      this.$refs.toastContent.classList.add('toast-come-in');
      let contentStyle = {};
      let left =
          document.documentElement.clientWidth / 2 -
          this.$refs.toastContent.clientWidth / 2,
        top = 8;
      switch (this.position) {
        case 'mid-center':
          top =
            document.documentElement.clientHeight / 2 -
            this.$refs.toastContent.clientHeight / 2;
          contentStyle = { '--left': left + 'px', '--top': top + 'px' };
          break;
        case 'top-center':
          contentStyle = { '--left': left + 'px', '--top': top + 'px' };
          break;
        case 'bottom-center':
          contentStyle = { '--left': left + 'px', '--bottom': '0px' };
          break;
        default:
          if (typeof this.position == 'string') {
            this.$refs.toastContent.classList.add(
              this.position || 'bottom-right'
            );
          } else if (typeof this.position == 'object') {
            ['top', 'bottom', 'left', 'right'].forEach(item => {
              typeof this.position[item] == 'number'
                ? this.position[item] == 0
                  ? (this.position[item] = '0')
                  : (this.position[item] += 'px')
                : null;

              this.position[item]
                ? (contentStyle[`--${item}`] = this.position[item])
                : null;
            });

            if (!Object.keys(contentStyle).length) {
              this.$refs.toastContent.classList.add('bottom-right');
            }
          } else {
            this.$refs.toastContent.classList.add('bottom-right');
          }
          break;
      }
      this.contentStyle = Object.assign({}, this.contentStyle, contentStyle);

      this.$nextTick(() => {
        this.$refs.toastContent.classList.remove('toast-come-in');
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.toast-content {
  position: absolute;
  top: var(--top);
  left: var(--left);
  bottom: var(--bottom);
  right: var(--right);
  z-index: calc(99999 + var(--index));
  width: 300px;
  overflow: hidden;
  display: block;
  margin: 0 0 5px;
  box-shadow: 1px 1px 3px 1px #ccc;
  border-radius: 4px;
  font-size: 12px;
  line-height: 17px;
  // position: relative;
  pointer-events: all !important;
  background-color: #fff;
  color: #fff;
  transition: all 0.3s;
  -o-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transform-origin: 100% 100%;

  &.bottom-right {
    bottom: 0;
    right: 8px;
  }
  &.bottom-left {
    bottom: 0;
    left: 20px;
  }
  &.top-left {
    top: 20px;
    left: 20px;
  }
  &.top-right {
    top: 20px;
    left: 35px;
  }
}
.toast-come-in {
  transform: scaleY(0);
}

.toast-close-icon {
  position: absolute;
  top: 10px;
  right: 7px;
  font-size: 24px;
  cursor: pointer;
  color: #fff !important;
  line-height: inherit;
  transition: all 0.3s;
  &:hover {
    transform: scale(1.1);
  }
}
.toast-loader {
  display: block;
  position: absolute;
  top: -2px;
  height: 5px;
  width: 0%;
  left: 0;
  border-radius: 5px;
  background: red;
}
.toast-header {
  background-image: url(../../assets/img/commonComponent/toast_bg.png);
  background-repeat: no-repeat;
  padding: 10px;
}
.toast-header-title {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.toast-head-icon {
  color: #fff;
  line-height: 20px;
}
.toast-message-content {
  padding: 10px;
  line-height: 18px;
  margin-top: 10px;
}
.toast-message-item {
  font: inherit;
  color: inherit;
  /deep/ {
    * {
      font: unset;
      color: inherit;
    }
  }
}
.toast-footer {
  height: 30px;
  padding: 5px 10px 10px;
  text-align: right;
  /deep/ {
    .el-button {
      border: none;
      outline: none;
      padding: 0;
      height: 30px;
      line-height: 30px;
      width: 70px;
      text-align: center;
      background: #81d3f8;
      color: #fff;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px;
      cursor: pointer;
      margin: 0 5px;
      &:hover {
        background: #03a6ff;
      }
    }
  }
}
</style>
