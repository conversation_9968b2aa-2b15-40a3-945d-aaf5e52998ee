import variables from '@/assets/css/var.scss';
import sidebarItem from './components/sidebarItem.vue';
import headerContainer from '@/components/header-container/index.vue';
import oldProject from '@/trasenLayout/components/oldProject.vue';
import tabNav from '@/trasenLayout/components/tabNav.vue';
import workSheetCallBox from '@/trasenLayout/components/workSheetCallBox.vue';
import workSheetWaitingList from '@/trasenLayout/components/workSheetWaitingList.vue';
import workSheetApi from '@/api/ajax/workSheetWebSocket';
import loadingBox from '@/components/loading/index.vue';
import DialogVersionTips from '@/components/dialog-version-tips/index.vue';
import DialogNewMessage from '@/components/dialog-new-message/index.vue';

export default {
  name: 'layout',
  components: {
    sidebarItem,
    headerContainer,
    tabNav,
    oldProject,
    workSheetCallBox,
    workSheetWaitingList,
    DialogVersionTips,
    DialogNewMessage,
    loadingBox
  },
  props: {
    sideBarWidth: {
      type: [String, Number],
      default: 160
    },
    sideBarMinWidth: {
      type: [String, Number],
      default: 50
    }
  },
  data() {
    return {
      dialogVersionTips: false,
      versionInfo: {},
      hasWorkSheet: this.$store.state.common.globalSetting.worksheetSwitch,

      isLoading: false, //是否在请求数据
      loadingCount: 0, //正在请求的个数

      headerbg: false, //头部弹框
      leftbg: 0, //0关闭  1小弹框  2大弹框
      headerHeight: 40, //头部高度
      variables, //全局css
      menuKey: true, //左边刷新菜单
      qiankunIndex: -1, //显示激活的子应用
      app: window.qiankuanappsLogin, //子应用程序
      iframeList: [], //弹窗列表

      workSheetWebsocket: null, //webSocket容器
      phoneSocketHeart: null, //心跳
      workSheetLoginUser: {}, //当前登录人信息
      workSheetSeatInform: {}, //当前坐席信息
      showCallingBox: false, //是否显示来电弹屏
      workSheetCallData: {}, //来电信息
      workSheetWaitingList: [], //等待列表
      workSheetWaitingTimer: null, //工单等待列表计时器,
      dialogNewMessage: false,
      remberSilberWidth: 160
    };
  },
  watch: {
    $route(to, from) {
      let newPath = to.path;
      let newFullPath = to.fullPath;
      if (
        to.path.startsWith('/ts-web-report-ai') &&
        !to.path.startsWith('/ts-web-report-ai/design')
      ) {
        // 解决设计器中其它子应用路由问题
        newPath = to.path.replace('/ts-web-report-ai/', '/');
        newFullPath = to.fullPath.replace('/ts-web-report-ai/', '/');
        this.$router.replace({
          path: newPath,
          query: to.query,
          params: to.params
        });
      }
      this.qiankunContainerFun();
      this.sideBarLinkage();
      this.buryingPoint(to.path);
      // let noPermission = this.$store.state.common.menuLineList.find(
      //     item => item.alink == this.$route.path
      //   ),
      //   whiteRouter =
      //     this.$store.state.common.whiteRouterList.findIndex(
      //       item => item == this.$route.path
      //     ) >= 0;
      // /**@desc 没有路由权限  && 不是路由白名单 */
      // if (!noPermission && !whiteRouter) {
      //   let homeUrl = (this.$refs.tabNav || {}).homUrl || '/index';
      //   this.$message.warning('尚未拥有该菜单权限，请联系管理员');
      //   this.$router.push(homeUrl);
      // }
    },
    '$store.state.common.activeMenuListParent'() {
      this.menuKey = false;
      this.$nextTick(() => {
        this.menuKey = true;
      });
    }
  },
  computed: {
    isCollapseWidth() {
      return this.$store.state.common.sideBarWidth;
    },
    isCollapse() {
      return this.$store.state.common.isCollapse;
    }
  },

  methods: {
    buryingPoint(modelUrls) {
      let model = this.findMenuName(
        this.$store.state.common.menuLineList,
        modelUrls
      );
      if (model) {
        this.ajax
          .modesUsesSave({
            modelName: model.menuname,
            modelId: model.id,
            modelUrls,
            teramType: 1
          })
          .then(res => {});
      }
    },
    findMenuName(menuList, url) {
      let obj = menuList.find(e => e.alink == url);
      return obj;
    },
    // 新版消息弹窗事件
    async handleOpenNewMessage() {
      this.dialogNewMessage = true;
    },
    async handleGetVersionDialog() {
      const res = await this.ajax.selectVersionRecord();
      if (res.success == false) {
        this.dialogVersionTips = false;
        return;
      }
      if (res.object && res.object?.isPush == 1) {
        this.versionInfo = res.object;
        this.dialogVersionTips = true;
      } else {
        this.versionInfo = {};
        this.dialogVersionTips = false;
      }
    },
    /**@desc 设置面包绡的展开和收起**/
    setIsCollapse() {
      this.$set(this.$store.state.common, 'isCollapse', !this.isCollapse);
      if (this.$store.state.common.isCollapse) {
        this.$set(this.$store.state.common, 'sideBarWidth', 50);
      } else {
        this.$set(
          this.$store.state.common,
          'sideBarWidth',
          this.remberSilberWidth
        );
      }
      this.$root.$emit('isCollapse');
      window.dispatchEvent(
        //eventdemo 事件名称
        //message 传送的数据源
        new CustomEvent('collapseChangeBack', {
          detail: this.$store.state.common.isCollapse
        })
      );
    },
    /**@desc 判断显示子应用**/
    qiankunContainerFun() {
      let _index = -1;
      this.app.map((item, index) => {
        if (this.$route.path.indexOf(item.userData.packageName) != -1) {
          _index = index;
        }
      });
      setTimeout(() => {
        this.qiankunIndex = _index;
      }, 100);
    },
    /**@desc 监听老项目弹框**/
    iframeAlert(obj = {}) {
      if (!obj.detail || !obj.detail.config) {
        return;
      }

      if (obj.detail.config.alertType == 'open') {
        if ((obj.detail.config.skin || '').indexOf('layui-layer-msg') >= 0) {
          return;
        }

        let isAlreadyFullScreen = this.iframeList.findIndex(
          item => item.area == '100%'
        );
        //储存 index  用来判断是否关闭弹窗
        this.iframeList.push({
          index: String(obj.detail.index),
          area: String(obj.detail.config.area[0] || '')
        });

        this.headerbg = true;
        if (isAlreadyFullScreen >= 0) {
          return;
        }
        if (obj.detail.config.area[0] == '100%') {
          this.leftbg = 2;
        } else {
          this.leftbg = 1;
        }
      } else if (obj.detail.config.alertType == 'close') {
        let closeIndex = -1,
          closeArea = '';
        this.iframeList.forEach((item, index) => {
          item.index == String(obj.detail.index)
            ? ((closeIndex = index), (closeArea = item.area))
            : null;
        });
        if (closeIndex == -1) {
          return;
        }

        this.iframeList.splice(closeIndex, 1);
        if (this.iframeList.length) {
          this.iframeList.findIndex(item => item.area == '100%') == -1 &&
          closeArea == '100%'
            ? (this.leftbg = 1)
            : null;
          return;
        }
        this.headerbg = false;
        this.leftbg = 0;
      }
    },
    //----------------- 我的工单webSocket事件 ---------------------------
    //获取当前登录人信息
    async loadScreenPopUp() {
      if (!this.hasWorkSheet) {
        return;
      }

      let userInfoRes = await workSheetApi.getLoginUserInfo(),
        userInfo = userInfoRes.object || {};
      if (userInfoRes.success == false) {
        this.$message.error(userInfoRes.message || '登录信息获取失败');
        return;
      }
      //确认是否添加在state里面去
      this.workSheetLoginUser = {
        ...userInfo
      };
      let seatInfoRes = await workSheetApi.getSearInfo(userInfo.userId);
      if (seatInfoRes.success == false) {
        return;
      }

      let {
        phone,
        fkUserId,
        fkUserDeptId,
        custometServiceStatus,
        pkCustometServiceId,
        playScreen,
        playVoice
      } = seatInfoRes.object || {};
      this.workSheetSeatInform = {
        phoneNumber: Number(phone),
        fkUserId,
        fkUserDeptId,
        pkCustometServiceId,
        custometServiceStatus,
        phoneSwtich: playScreen ? 'on' : undefined,
        videoSwtich: playVoice ? 'on' : undefined
      };
      if (
        !this.workSheetLoginUser.webSocket ||
        !this.workSheetSeatInform.phoneSwtich
      ) {
        return;
      }
      this.connectPhoneWebSocket();
    },
    //连接webSocket
    connectPhoneWebSocket() {
      return;
      if (typeof WebSocket == 'undefined') {
        console.log('您的浏览器不支持WebSocket');
        return;
      }
      this.workSheetWebsocket && this.workSheetWebsocket.close();
      let wsUrl =
        process.env.NODE_ENV == 'development'
          ? '**************'
          : location.hostname;
      //连接 WebSocket
      this.workSheetWebsocket = new WebSocket(
        `ws://${wsUrl}:9835/ts-worksheet/messagewebsocket/` +
          this.workSheetLoginUser.userId
      );
      this.workSheetWebsocket.onopen = () => {};
      this.workSheetWebsocket.onmessage = this.handleCallReminder;
      this.workSheetWebsocket.onclose = () => {};

      //设置心跳连接
      //清除定时器
      this.phoneSocketHeart && clearInterval(this.phoneSocketHeart);
      //重新赋值
      this.phoneSocketHeart = setInterval(() => {
        if (this.workSheetWebsocket.readyState == 1) {
          this.workSheetWebsocket.send('ping');
        } else {
          this.connectPhoneWebSocket();
        }
      }, 60000); //60s一次
    },
    //处理来电信息的事件处理
    handleCallReminder(res) {
      // workSheetWebSocketMessage;
      if (res.data == '链接成功') {
        return;
      }

      let datas = JSON.parse(res.data || '{}');
      let data = JSON.parse(datas.content);
      window.dispatchEvent(
        new CustomEvent('childWebsocketMessage', {
          detail: data
        })
      );
      // var url =
      //   'https://tts.baidu.com/text2audio?lan=zh&ie=UTF-8&spd=6&text=' +
      //   encodeURI('您有新的消息，请注意查收！');

      switch (data.type) {
        case '0': //来电为等待，无需弹屏，只用来刷新等待队列
          //更新等待列表数据
          this.workSheetWaitingList = [...data.wsCustometLogOutVoList]; //等待列表数据
          // new Audio(url).play();
          break;
        case '1': //首次来电，来电弹屏
          //如果来电弹屏按钮关闭showCallBox 或者 当前正在创建工单
          // if (!this.workSheetSeatInform.phoneSwtich) {
          //   return;
          // }
          if (this.workSheetSeatInform.videoSwtich) {
            new Audio(process.env.VUE_APP_BASE_URL + 'Audio.mp3').play();
            //显示来电弹屏  更新来电信息
            this.workSheetCallData = {
              ...data,
              repairType: '1',
              callType: '1'
            };
            this.workSheetWaitingList = [...data.wsCustometLogOutVoList]; //等待列表数据
            delete this.workSheetCallData.wsCustometLogOutVoList;
            this.showCallingBox = true;
          }
          break;
        case '2': //接听来电，显示来电弹屏
          //补充数据
          // this.workSheetCallData.pkCustometLogId = data.pkCustometLogId;
          this.workSheetCallData = data;
          //页面跳转
          if (this.$route.path != '/orderInfoDesk') {
            this.$router.push('/orderInfoDesk');
          }
          this.handleAnswer();
          break;
        case '3': //来电挂断，关闭来电弹屏或者
          //如果有弹屏，则关闭弹屏
          this.showCallingBox = false;
          break;
        default:
          break;
      }
    },
    //处理接听事件
    handleAnswer() {
      this.showCallingBox = false;

      if (this.$route.path != '/orderInfoDesk') {
        this.$router.push('/orderInfoDesk');
      }
      this.$root.$emit(
        'workSheetWebSocketMessage',
        JSON.parse(JSON.stringify(this.workSheetCallData))
      );
    },
    //----------------------- 结束处理 ---------------------------------

    //侧边导航栏以及头部导航栏的联动
    sideBarLinkage() {
      const path = this.$store.state.common.menuLineList.find(
        item => item.alink == this.$route.path
      );
      if (!path) {
        return true;
      }
      const topIndex = Number(path.parentIndex_.split('-')[0]);
      if (!(topIndex >= 0)) {
        return true;
      }
      const topItem = this.$store.state.common.menuList[topIndex];
      if (this.$store.state.common.activeMenuListParent == topIndex) {
        this.$root.$emit('updateFirstMenu', topItem);
        return;
      }
      this.$refs['headerContainer'].goto(topItem, topIndex, false);
    }
  },
  async created() {
    this.$root.$on('sendAjaxMessage', e => {
      if (e.data == 'sendMessage') {
        this.isLoading = true;
        this.loadingCount++;
      } else {
        this.loadingCount--;
        this.loadingCount <= 0 ? (this.isLoading = false) : null;
      }
    });
    this.$root.$on('hrmHomePageToEvent', e => {
      this.$router.push(e.data.path);
    });
    this.qiankunContainerFun();
    window.addEventListener('iframeAlert', this.iframeAlert, false);
    await this.loadScreenPopUp();

    this.$root.$on('tsWorkSheetConnectWebSocket', e => {
      if (!this.hasWorkSheet) {
        return;
      }
      this.workSheetSeatInform.videoSwtich = e.playVoice ? 'on' : undefined;
      if (!e.playScreen) {
        this.workSheetWebsocket && this.workSheetWebsocket.close();
        return;
      }
      if (this.workSheetWebsocket && this.workSheetWebsocket.readyState == 1) {
        return;
      }
      this.connectPhoneWebSocket();
    });
    this.handleGetVersionDialog();
  },
  beforeDestroy() {
    window.removeEventListener('iframeAlert', this.iframeAlert, false);
  },
  mounted() {
    let resizeHandles = document.querySelector('#section-left-box');
    let _this = this;
    let startX = null;
    let startWidth = null;
    resizeHandles.addEventListener('mousedown', function(event) {
      event.preventDefault();
      startX = event.clientX;
      startWidth = resizeHandles.offsetWidth;
      resizeHandles.addEventListener('mousemove', resize);
      resizeHandles.addEventListener('mouseup', stopResize);
    });
    function resize(evet) {
      const dx = evet.clientX - startX;
      let width = startWidth;
      width = startWidth + dx;
      if (parseInt(width) <= 160) return;
      _this.remberSilberWidth = width;
      _this.$store.commit('common/setData', {
        label: 'sideBarWidth',
        value: width
      });
    }
    function stopResize() {
      resizeHandles.removeEventListener('mousemove', resize);
      resizeHandles.removeEventListener('mouseup', stopResize);
    }
    window.addEventListener('mouseup', function() {
      resizeHandles.removeEventListener('mouseup', stopResize);
    });
  }
};
