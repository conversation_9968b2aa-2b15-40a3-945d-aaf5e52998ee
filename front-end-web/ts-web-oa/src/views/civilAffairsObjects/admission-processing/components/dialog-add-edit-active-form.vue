<template>
  <ts-dialog
    class="dialog-add-edit-active-form"
    fullscreen
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="status-container">
      <ul class="status-list">
        <li
          v-for="item of columnOptions"
          :key="item.formName"
          :class="{
            active: activeTab == item.formName
          }"
          class="status-item"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <div class="detail-container">
      <el-scrollbar
        class="flex-column"
        style="flex: 1;"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-content">
          <component
            v-if="activeTabsInfo"
            :type="type"
            :is="activeTabsInfo.formName"
            :ref="activeTabsInfo.formName"
            :bind="activeTabsInfo.props"
          />
        </div>
      </el-scrollbar>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :submitLoading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import FormCivilAffairsPersonnelInfo from '@/views/civilAffairsObjects/admission-processing/components/form-civil-affairs-personnel-info.vue';
import FormCivilResidenceInformation from '@/views/civilAffairsObjects/admission-processing/components/form-civil-residence-information.vue';
import FormCivilDisabilityCertificate from '@/views/civilAffairsObjects/admission-processing/components/form-civil-disability-certificate.vue';
import FormCivilAdmissionInformation from '@/views/civilAffairsObjects/admission-processing/components/form-civil-admission-information.vue';
import FormCivilSocialSecurity from '@/views/civilAffairsObjects/admission-processing/components/form-civil-social-security.vue';
import FormCivilDepartureInformation from '@/views/civilAffairsObjects/admission-processing/components/form-civil-departure-information.vue';
import FormCivilArchivalInformation from '@/views/civilAffairsObjects/admission-processing/components/form-civil-archival-information.vue';

export default {
  components: {
    FormCivilAffairsPersonnelInfo,
    FormCivilResidenceInformation,
    FormCivilDisabilityCertificate,
    FormCivilAdmissionInformation,
    FormCivilSocialSecurity,
    FormCivilDepartureInformation,
    FormCivilArchivalInformation
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      type: '',
      title: '',

      activeTabsInfo: null,
      columnOptions: [],

      activeTab: ''
    };
  },
  methods: {
    open({ data = {}, type, title, activeTabsInfo }) {
      this.title = title;
      this.type = type;

      this.activeTabsInfo = activeTabsInfo;
      this.columnOptions.push(activeTabsInfo);
      this.activeTab = activeTabsInfo.formName;
      this.visible = true;

      this.$nextTick(() => {
        let component = this.$refs[activeTabsInfo.formName];
        if (type == 'add') {
          component.handleSetInitForm && component.handleSetInitForm();
        }
        if (type == 'edit') {
          switch (activeTabsInfo.type) {
            case 'form':
              component.handleGetDetails(
                activeTabsInfo.PageList,
                data.personnelId
              );
              break;
            case 'table':
              break;
          }
        }
      });
    },

    async submit() {
      try {
        this.submitLoading = true;
        await this.$refs[this.activeTabsInfo.formName].submit();
        this.close();
        this.$emit('refresh');
      } catch (e) {
        console.error(e);
      } finally {
        this.submitLoading = false;
      }
    },
    close() {
      this.title = '';
      this.type = '';
      this.activeTabsInfo = null;
      this.columnOptions = [];
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-edit-active-form {
  ::v-deep {
    .el-dialog__body {
      background-color: transparent !important;
      display: flex;
      overflow: hidden;
      padding: 0 !important;
      height: calc(100vh - 135px) !important;
      .status-container {
        flex-shrink: 0;
        margin-right: $medium-spacing;
        .status-list {
          background-color: $ts-table-header-hover-bg;
          cursor: pointer;
          padding: 2px;
          .status-item {
            line-height: 30px;
            padding: 0 $card-spacing;
            &.active {
              background-color: #fff;
              color: $primary-blue;
            }
          }
        }
      }

      .detail-container {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        .form-content {
          padding: 8px;
          padding-right: 12px;
        }
      }
    }
  }
}
</style>
