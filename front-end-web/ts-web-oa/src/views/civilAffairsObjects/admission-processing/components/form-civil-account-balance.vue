<template>
  <div class="form-civil-account-balance">
    <!-- <description
      :columns="descriptionColumns"
      :tableData="details"
      :column="3"
      label-width="90px"
    >
      <template v-slot:operate>
        <div class="operate-container" @click="handleGoToChangeInfo">
          <img class="setting-img" src="@/assets/img/setting.svg" alt="" />

          <span class="operate-btn">
            去更新银行账户信息
          </span>
        </div>
      </template>
    </description> -->

    <base-table
      ref="table"
      class="form-table"
      border
      stripe
      v-loading="loading"
      :columns="tableColumns"
      :hasPage="false"
      show-summary
      :summary-method="handleSummary"
    />
  </div>
</template>

<script>
// import description from '@/components/description/index.vue';

export default {
  components: {
    // description
  },
  props: {
    details: {
      type: Object
    }
  },
  created() {},
  data() {
    return {
      loading: false,

      tableData: [],
      descriptionColumns: [
        {
          label: '开户行',
          prop: 'name'
        },
        {
          label: '开户账号',
          prop: 'age'
        },
        {
          label: '',
          prop: 'operate'
        }
      ],

      tableColumns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 60
        },
        {
          label: '姓名',
          prop: 'name',
          align: 'center',
          width: 80
        },
        {
          label: '身份证号',
          prop: 'idCard',
          align: 'center',
          width: 165
        },
        {
          label: '存折所在行',
          prop: 'bank',
          align: 'center',
          width: 120
        },
        {
          label: '存折号码',
          prop: 'account',
          align: 'center',
          width: 120
        },
        {
          label: '支取日期',
          prop: 'withdrawalDate',
          align: 'center',
          width: 90
        },
        {
          label: '经办人',
          prop: 'operator',
          align: 'center',
          width: 80
        },
        {
          label: '证明人',
          prop: 'certifier',
          align: 'center',
          width: 80
        },
        {
          label: '备注',
          prop: 'remarks',
          align: 'center'
        },
        {
          label: '支取明细',
          prop: 'postPaymentBalance',
          align: 'right',
          fixed: 'right',
          width: 115,
          formatter: row => {
            return (
              <ul>
                {row.withdrawalDetailList.map((d, i) => (
                  <li key={i}>
                    {d.typeText}：{d.amount}
                  </li>
                ))}
              </ul>
            );
          }
        },
        {
          label: '支前余额(元)',
          prop: 'prePaymentBalance',
          align: 'right',
          fixed: 'right',
          width: 110,
          formatter: row => {
            return <span class="primary">{row.prePaymentBalance}</span>;
          }
        },
        {
          label: '支取金额(元)',
          prop: 'withdrawalAmount',
          align: 'right',
          fixed: 'right',
          width: 110,
          formatter: row => {
            return <span class="primary">{row.withdrawalAmount}</span>;
          }
        },
        {
          label: '支后余额(元)',
          prop: 'postPaymentBalance',
          align: 'right',
          fixed: 'right',
          width: 110,
          formatter: row => {
            return <span class="primary">{row.postPaymentBalance}</span>;
          }
        }
      ]
    };
  },
  methods: {
    handleGetDetails() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        // pageSize = this.$refs.table.pageSize,
        pageSize = 999,
        searchForm = {
          pageNo,
          pageSize,
          personnelId: this.details.personnelId,
          sidx: 'create_date',
          sord: 'desc'
        };

      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });

      let res = await this.ajax.civilAffairsWithdrawalRecordsList(searchForm);

      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });

      this.$refs.table.handleMounted();
    },

    handleSummary({ columns, data }) {
      let sums = [];
      columns.map((col, index) => {
        if (
          ![
            'prePaymentBalance',
            'withdrawalAmount',
            'postPaymentBalance'
          ].includes(col.property)
        ) {
          sums[index] = '';
          return;
        }
        let sum = 0;
        data.map(row => {
          row[col.property] && (sum += row[col.property] * 100);
        });

        sums[index] = sum / 100;
      });
      sums[0] = '合计';
      return sums;
    }
  }
};
</script>

<style lang="scss" scoped>
.form-civil-account-balance {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  .operate-container {
    display: flex;
    align-items: center;
    cursor: pointer;

    .operate-btn {
      color: rgba(100, 132, 232, 1) !important;
    }

    .setting-img {
      width: 18px;
      height: 18px;
      margin-right: 4px;
      transform: translateY(-1px);
    }
  }

  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      ul {
        margin-bottom: 0;
        li {
          margin: 1px 0;
        }
      }

      // .scroll-wrap-class {
      //   margin-top: 30px;
      // }

      // .el-table {
      //   padding-bottom: 62px;
      // }

      .primary {
        color: $primary-blue;
      }

      .el-table__footer {
        .cell {
          color: rgb(255, 121, 0);
        }
      }
    }
  }
}
</style>
