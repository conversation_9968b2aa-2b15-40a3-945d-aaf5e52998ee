<template>
  <div class="table-container">
    <div class="operate" v-if="type != 'details'">
      <ts-button type="primary" @click="handleAdd">新 增</ts-button>
    </div>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :columns="columns"
      :hasPage="false"
    />

    <dialog-civil-county-contacts
      ref="DialogCivilCountyContacts"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import DialogCivilCountyContacts from './dialog-civil-county-contacts.vue';
export default {
  components: {
    DialogCivilCountyContacts
  },
  model: {
    prop: 'form',
    event: 'input'
  },
  props: {
    details: {
      type: Object
    },
    type: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '联系人姓名',
          prop: 'contactName',
          align: 'center',
          width: 130
        },
        {
          label: '电话',
          prop: 'telephone',
          align: 'center'
        },
        {
          label: '备注',
          prop: 'remarks',
          align: 'center'
        },
        {
          label: '操作',
          width: '120',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'delete-span'
              }
            ];
            let handle = { 'action-select': e => e(row) };
            return <BaseActionCell actions={actionList} on={handle} />;
          }
        }
      ]
    };
  },
  created() {
    if (this.type == 'details') {
      let index = this.columns.findIndex(f => f.label == '操作');
      if (index != -1) {
        this.columns.splice(index, 1);
      }
    }
  },
  methods: {
    handleAdd() {
      this.$refs.DialogCivilCountyContacts.open({
        title: '新增',
        type: 'edit',
        data: {
          personnelId: this.details.personnelId
        }
      });
    },

    handleEdit(data) {
      this.$refs.DialogCivilCountyContacts.open({
        title: '编辑',
        type: 'edit',
        data
      });
    },

    async handleDelete(row) {
      const { id } = row;
      try {
        await this.$confirm(`<span>您确认删除这条数据吗？</span>`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        const res = await this.ajax.CivilCountyContactsDelete(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleRefreshTable() {
      let pageNo = 1;
      let pageSize = 99999;
      let searchForm = {
        pageNo,
        pageSize,
        personnelId: this.details.personnelId,
        sidx: 'create_date',
        sord: 'desc'
      };
      let res = await this.ajax.CivilCountyContactsPageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.$emit('wirteRefresh');
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .operate {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
  }

  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      .el-table__body-wrapper {
        margin-top: 32px !important;
      }

      .delete-span {
        color: $red !important;
        cursor: pointer;
      }
    }
  }
}
::v-deep {
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
