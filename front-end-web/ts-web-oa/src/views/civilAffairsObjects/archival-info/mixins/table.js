let indexColumns = {
    label: '序号',
    prop: 'index',
    width: 70,
    align: 'center'
  },
  nameColumns = {
    label: '姓名',
    prop: 'name',
    width: 90,
    align: 'center'
  },
  idCardColumns = {
    label: '身份证号',
    prop: 'identityNumber',
    width: 175,
    align: 'center'
  },
  updateColumns = {
    label: '最近修改人',
    prop: 'updateUserName',
    width: 90,
    align: 'center'
  },
  updateTimeColumns = {
    label: '最近修改时间',
    prop: 'updateDate',
    width: 155,
    align: 'center'
  };
let nameSearch = {
    label: '姓名',
    value: 'name',
    element: 'ts-input',
    elementProp: {
      placeholder: '请输入'
    }
  },
  idCardSearch = {
    label: '身份证号码',
    value: 'identityNumber',
    element: 'ts-input',
    elementProp: {
      placeholder: '请输入'
    }
  };
import { deepClone } from '@/unit/commonHandle.js';
import { objectTypeOptions } from '@/views/civilAffairsObjects/configMixins/tabsMixins.js';

export default {
  data() {
    return {
      actions: [],
      commonColumns: [
        {
          label: '操作',
          align: 'center',
          width: 100,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'red'
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ],
      searchForm: {},
      searchList: [],
      columns: [],

      civilDisabilityCategoriesOptions: [],
      civilNursingLevelOptions: [],
      civilPaymentStatusOptions: []
    };
  },
  async created() {
    this.handleSetSearchItemAndColumns();
    await this.handleGetCivilDisabilityCategoriesOptions();
    await this.handleGetNursingLevelOptions();
    await this.handleGetPaymentStatusOptions();
  },
  methods: {
    handleSetSearchItemAndColumns() {
      let tabItemTableInfo = {
        civil_affairs_personnel_info: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            {
              label: '性别',
              prop: 'sexText',
              align: 'center',
              width: 80
            },
            idCardColumns,
            {
              label: '出生日期',
              prop: 'birthday',
              align: 'center',
              width: 115
            },
            {
              label: '对象类型',
              prop: 'objectTypeText',
              align: 'center',
              width: 120
            },
            {
              label: '来源',
              prop: 'rechargeNumbers',
              align: 'center',
              width: 100
            },
            {
              label: '入院日期',
              prop: 'inDate',
              align: 'center',
              width: 120
            },
            {
              label: '创建人',
              prop: 'createUserName',
              align: 'center',
              width: 85
            },
            {
              label: '创建时间',
              prop: 'createDate',
              align: 'center'
            },
            {
              label: '最近修改人',
              prop: 'updateUserName',
              align: 'center',
              width: 85
            },
            {
              label: '最近修改时间',
              prop: 'updateDate',
              align: 'center'
            }
          ]
        },
        civil_residence_information: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '户主姓名',
              value: 'householderName',
              element: 'ts-input',
              elementProp: {
                placeholder: '请输入'
              }
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '户主姓名',
              prop: 'householderName',
              align: 'center',
              width: 120
            },
            {
              label: '户主性质',
              prop: 'householderNatureText',
              align: 'center',
              width: 120
            },
            {
              label: '户主关系',
              prop: 'householderRelationshipText',
              align: 'center',
              width: 120
            },
            {
              label: '籍贯',
              prop: 'nativePlace',
              align: 'center',
              width: 80
            },
            {
              label: '住址',
              prop: 'address',
              align: 'center',
              width: 120
            },
            {
              label: '迁入日期',
              prop: 'migrationDate',
              align: 'center',
              width: 100
            },
            {
              label: '变动原因',
              prop: 'changeReasonText',
              align: 'center',
              width: 120
            },
            {
              label: '迁入地点',
              prop: 'relocationLocation',
              align: 'center',
              width: 120
            },
            {
              label: '婚姻状况',
              prop: 'maritalStatusText',
              align: 'center',
              width: 80
            },
            {
              label: '代办人',
              prop: 'agent',
              align: 'center',
              width: 80
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_disability_certificate: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '残疾类别',
              value: 'disabilityCategories',
              element: 'ts-select',
              elementProp: {
                clearable: true
              },
              childNodeList: []
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '残疾证号',
              prop: 'number',
              align: 'center',
              width: 120
            },
            {
              label: '签发日期',
              prop: 'issueDate',
              align: 'center',
              width: 100
            },
            {
              label: '有效截止日期',
              prop: 'effectiveEndTime',
              align: 'center',
              width: 120
            },
            {
              label: '残疾类别',
              prop: 'disabilityCategoriesText',
              align: 'center',
              width: 90
            },
            {
              label: '残疾等级',
              prop: 'disabilityLevelText',
              align: 'center',
              width: 90
            },
            {
              label: '迁入地',
              prop: 'relocationLocation',
              align: 'center',
              width: 100
            },
            {
              label: '批准机关',
              prop: 'approvalAuthority',
              align: 'center',
              width: 120
            },
            {
              label: '迁入日期',
              prop: 'migrationDate',
              align: 'center',
              width: 120
            },
            {
              label: '备注',
              prop: 'remarks',
              align: 'center'
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_admission_information: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '入院日期',
              value: 'inDate',
              element: 'ts-range-picker',
              elementProp: {
                valueFormat: 'YYYY-MM-DD'
              }
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '转安置机构',
              prop: 'placementInstitutions',
              align: 'center',
              width: 120
            },
            {
              label: '类型',
              prop: 'typeText',
              align: 'center',
              width: 100
            },
            {
              label: '安置日期',
              prop: 'placementDate',
              align: 'center',
              width: 120
            },
            {
              label: '入院日期',
              prop: 'inDate',
              align: 'center',
              width: 100
            },
            {
              label: '病区',
              prop: 'wardText',
              align: 'center',
              width: 90
            },
            {
              label: '床位',
              prop: 'bed',
              align: 'center',
              width: 100
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_contract_information: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '签订日期',
              prop: 'signingDate',
              align: 'center',
              width: 120
            },
            {
              label: '到期日期',
              prop: 'dueDate',
              align: 'center',
              width: 120
            },
            {
              label: '护理费标准',
              prop: 'nursingExpenses',
              align: 'center',
              width: 120
            },
            {
              label: '伙食费标准',
              prop: 'boardExpenses',
              align: 'center',
              width: 100
            },
            {
              label: '其他',
              prop: 'other',
              align: 'center'
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_family_information: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '家属姓名',
              prop: 'familyName',
              align: 'center',
              width: 90
            },
            {
              label: '关系',
              prop: 'relationship',
              align: 'center',
              width: 90
            },
            {
              label: '电话',
              prop: 'telephone',
              align: 'center',
              width: 120
            },
            {
              label: '备注',
              prop: 'remarks',
              align: 'center'
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_county_contacts: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '联系人姓名',
              prop: 'contactName',
              align: 'center',
              width: 90
            },
            {
              label: '电话',
              prop: 'telephone',
              align: 'center',
              width: 120
            },
            {
              label: '备注',
              prop: 'remarks',
              align: 'center'
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_hardship_protection: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '特困日期',
              prop: 'hardshipDate',
              align: 'center',
              width: 150
            },
            {
              label: '保障标准（元）',
              prop: 'hardshipStandard',
              align: 'center',
              width: 150
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_orphan_protection: {
          searchList: [nameSearch, idCardSearch],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '日期',
              prop: 'orphanDate',
              align: 'center',
              width: 150
            },
            {
              label: '保障标准（元）',
              prop: 'orphanStandard',
              align: 'center',
              width: 150
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_nursing_information: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '护理级别',
              value: 'nursingLevel',
              element: 'ts-select',
              elementProp: {
                clearable: true
              },
              childNodeList: []
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '日期',
              prop: 'nursingDate',
              align: 'center',
              width: 120
            },
            {
              label: '护理级别',
              prop: 'nursingLevelText',
              align: 'center',
              width: 120
            },
            {
              label: '保障标准（元）',
              prop: 'nursingStandard',
              align: 'center',
              width: 120
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_social_security: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '缴费状态',
              value: 'paymentStatus',
              element: 'ts-select',
              elementProp: {
                clearable: true
              },
              childNodeList: []
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '参保日期',
              prop: 'lnsuredDate',
              align: 'center',
              width: 120
            },
            {
              label: '缴费状态',
              prop: 'paymentStatusText',
              align: 'center',
              width: 120
            },
            {
              label: '领取日期',
              prop: 'collectionDate',
              align: 'center',
              width: 120
            },
            {
              label: '领取标准',
              prop: 'claimCriteria',
              align: 'center',
              width: 120
            },
            {
              label: '参保地',
              prop: 'insuredPlace',
              align: 'center',
              width: 120
            },
            {
              label: '转出地',
              prop: 'transferPlace',
              align: 'center',
              width: 120
            },
            {
              label: '转入日期',
              prop: 'transferDate',
              align: 'center',
              width: 120
            },
            {
              label: '社保卡号',
              prop: 'number',
              align: 'center',
              width: 120
            },
            {
              label: '开户行',
              prop: 'bankName',
              align: 'center',
              width: 120
            },
            {
              label: '开户日期',
              prop: 'dateOpened',
              align: 'center',
              width: 120
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_departure_information: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '离院类型',
              value: 'departureType',
              element: 'ts-select',
              elementProp: {
                clearable: true
              },
              childNodeList: [
                {
                  label: '送返',
                  value: '送返',
                  element: 'ts-option'
                },
                {
                  label: '死亡',
                  value: '死亡',
                  element: 'ts-option'
                }
              ]
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '离院类型',
              prop: 'departureType',
              align: 'center',
              width: 120
            },
            {
              label: '离院日期',
              prop: 'deathDate',
              align: 'center',
              width: 120
            },
            {
              label: '死亡日期',
              prop: 'departureDate',
              align: 'center',
              width: 120
            },
            {
              label: '经办人',
              prop: 'handlingName',
              align: 'center',
              width: 120
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_archival_information: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '编号',
              value: 'name',
              element: 'ts-input',
              elementProp: {
                placeholder: '请输入'
              }
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '离院类型',
              prop: 'departureType',
              align: 'center',
              width: 120
            },
            {
              label: '离院日期',
              prop: 'deathDate',
              align: 'center',
              width: 120
            },
            {
              label: '死亡日期',
              prop: 'departureDate',
              align: 'center',
              width: 120
            },
            {
              label: '经办人',
              prop: 'handlingName',
              align: 'center',
              width: 120
            },
            updateColumns,
            updateTimeColumns
          ]
        },
        civil_archiva_files: {
          searchList: [
            nameSearch,
            idCardSearch,
            {
              label: '编号',
              value: 'name',
              element: 'ts-input',
              elementProp: {
                placeholder: '请输入'
              }
            }
          ],
          columns: [
            indexColumns,
            nameColumns,
            idCardColumns,
            {
              label: '档案类别',
              prop: 'archiveCategoryText',
              align: 'center',
              width: 120
            },
            {
              label: '文件类别',
              prop: 'fileTypeText',
              align: 'center',
              width: 120
            },
            {
              label: '档案文件',
              prop: 'originalName',
              align: 'center',
              width: 120
            },
            updateColumns,
            updateTimeColumns
          ]
        }
      };

      Object.keys(this.objectTabRefs).forEach(key => {
        let { searchList = [], columns = [] } = tabItemTableInfo[key] || {};
        this.$set(this.objectTabRefs[key], 'searchList', searchList);
        this.$set(this.objectTabRefs[key], 'columns', columns);
      });
    },

    async handleGetCivilDisabilityCategoriesOptions() {
      await this.ajax.getDictionaries(
        'civil_disability_categories',
        this,
        'civilDisabilityCategoriesOptions'
      );
      let find = this.objectTabRefs['civil_disability_certificate'][
        'searchList'
      ].find(f => f.value == 'disabilityCategories');
      let options = this.civilDisabilityCategoriesOptions.map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });
      this.$set(find, 'childNodeList', options);
    },

    async handleGetNursingLevelOptions() {
      await this.ajax.getDictionaries(
        'civil_nursing_level',
        this,
        'civilNursingLevelOptions'
      );

      let find = this.objectTabRefs['civil_nursing_information'][
        'searchList'
      ].find(f => f.value == 'nursingLevel');
      let options = this.civilNursingLevelOptions.map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });
      this.$set(find, 'childNodeList', options);
    },

    async handleGetPaymentStatusOptions() {
      await this.ajax.getDictionaries(
        'civil_payment_status',
        this,
        'civilPaymentStatusOptions'
      );

      let find = this.objectTabRefs['civil_social_security']['searchList'].find(
        f => f.value == 'paymentStatus'
      );
      let options = this.civilPaymentStatusOptions.map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });
      this.$set(find, 'childNodeList', options);
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleAdd() {
      let { type } = this.activeTabsInfo;
      let data = {};
      if (type == 'form') {
        this.$refs.DialogAddEditActiveForm.open({
          activeTabsInfo: this.activeTabsInfo,
          type: 'add',
          title: '新增',
          data
        });
      } else {
        this.$refs[this.activeTabsInfo.DialogForm].open({
          type: 'add',
          title: '新增',
          data
        });
      }
    },

    handleExport() {
      let serviceExportApi = '/ts-oa' + this.activeTabsInfo.exportApi;

      let searchForm = deepClone(this.searchForm);
      if (searchForm.inDate && searchForm.inDate.length) {
        let [inDateBeging = '', inDateEnd = ''] = searchForm.inDate;
        searchForm.inDateBeging = inDateBeging;
        searchForm.inDateEnd = inDateEnd;
        delete searchForm.inDate;
      }

      let aDom = document.createElement('a'),
        conditionList = Object.keys(searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      aDom.href = serviceExportApi + '?' + conditionList.join('&');
      aDom.click();
    },

    async handleDownLoad() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请先勾选文件项');
        return;
      }

      let fileIds = this.selectionList.map(f => f.fileId);
      let fileName = this.activeTabsInfo.label + '.zip';
      let xhr = new XMLHttpRequest();
      let url = '/ts-basics-bottom/fileAttachment/batchDownload';
      xhr.open('post', url, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(JSON.stringify(fileIds));
      xhr.onload = function() {
        if (this.status === 200) {
          let url = window.URL.createObjectURL(new Blob([this.response]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      };
    },

    handleEdit(data) {
      let { type } = this.activeTabsInfo;
      if (type == 'form') {
        this.$refs.DialogAddEditActiveForm.open({
          activeTabsInfo: this.activeTabsInfo,
          type: 'edit',
          title: '编辑',
          data
        });
      } else {
        this.$refs[this.activeTabsInfo.DialogForm].open({
          title: '编辑',
          type: 'edit',
          data
        });
      }
    },

    async handleDelete(row) {
      try {
        await this.$confirm(`<span>您确认删除这条数据吗？</span>`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        let paramsId = row.id;
        // 身份证信息 主键是personnelId
        if (this.activeTabs === 'civil_affairs_personnel_info') {
          paramsId = row.personnelId;
        }

        const res = await this.ajax[this.activeTabsInfo.delete](paramsId);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleRefreshTable() {
      this.$refs.table.pageNo = 1;

      let searchForm = deepClone(this.searchForm);
      if (searchForm.inDate && searchForm.inDate.length) {
        let [inDateBeging = '', inDateEnd = ''] = searchForm.inDate;
        searchForm.inDateBeging = inDateBeging;
        searchForm.inDateEnd = inDateEnd;
        delete searchForm.inDate;
      }

      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchData = {
          ...searchForm,
          pageNo,
          pageSize,
          objectType: this.objectType
        };
      let res = await this.ajax[this.activeTabsInfo['PageList']](searchData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;

        item.objectTypeText =
          objectTypeOptions.find(f => f.value == item.objectType)?.label || '';
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
