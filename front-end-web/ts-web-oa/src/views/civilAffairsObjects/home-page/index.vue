<template>
  <div class="handling-departure">
    <el-date-picker
      style="width: 140px"
      v-model="queryYear"
      ref="yearHide"
      :append-to-body="true"
      value-format="yyyy"
      type="year"
      :picker-options="pickerOptions"
      popper-class="select-date-year-set-style"
      placeholder="请选择年度"
      v-clickoutside="yearHide"
      @change="refresh"
    >
    </el-date-picker>

    <el-scrollbar wrap-style="overflow-x: hidden;">
      <div class="content">
        <div class="statistics-container">
          <div class="item first">
            <div class="heard">
              <div class="heard-item-title blod">服务对象</div>
              <div class="total">
                <span class="label">总人数：</span>
                <span class="value blod"> {{ managerObjectData.zrs }}人 </span>
              </div>
            </div>
            <div class="target-info">
              <div class="item-target">
                <div class="value blod">{{ managerObjectData.mzdx_rc }}人</div>
                <div class="label">民政对象</div>
              </div>
              <div class="item-target">
                <div class="value blod">
                  {{ managerObjectData.qxjydx_rc }}人
                </div>
                <div class="label">寄养对象</div>
              </div>
              <!-- <div class="item-target">
                <div class="value blod">
                  {{ managerObjectData.jsjydx_rc }}人
                </div>
                <div class="label">家属寄养对象</div>
              </div> -->
            </div>
          </div>

          <div class="item second">
            <div class="heard">
              <div class="heard-item-title blod">专项资金</div>
              <div class="total">
                <span class="label">年度预算：</span>
                <span class="value blod">
                  ¥{{ fundData.budgetAmount || 0 }}万元
                </span>
              </div>
            </div>
            <div class="target-info">
              <div class="item-target">
                <div class="value blod">
                  {{ fundData.receiveAmount || 0 }}万
                </div>
                <div class="label">预算到位</div>
              </div>
              <div class="item-target">
                <div class="value blod">{{ fundData.expendAmount || 0 }}万</div>
                <div class="label">累计支出</div>
              </div>
              <div class="item-target">
                <div class="value blod">
                  {{ fundData.balanceAmount || 0 }}万
                </div>
                <div class="label">到位结余</div>
              </div>
            </div>
          </div>

          <div class="item third">
            <div class="heard">
              <div class="heard-item-title blod">代管账户</div>
              <div class="total">
                <span class="label">当前代管账号：</span>
                <span class="value blod">
                  {{ accountData.dqdgCount || 0 }}个
                </span>
              </div>
            </div>
            <div class="target-info">
              <div class="item-target">
                <div class="value blod">
                  {{ accountData.ndxzCount || 0 }}个 /
                  {{ accountData.ndtcCount || 0 }}个
                </div>
                <div class="label">新增/退出</div>
              </div>
              <div class="item-target">
                <div class="value blod">{{ accountData.ndzqJe || 0 }}万</div>
                <div class="label">累计支取缴存</div>
              </div>
              <div class="item-target">
                <div class="value blod">{{ accountData.jhzqJe || 0 }}万</div>
                <div class="label">支取计划</div>
              </div>
            </div>
          </div>
        </div>

        <div class="tips-container" id="TipsContainer">
          <div class="left">
            <div class="top-left-container">
              <div
                class="tips-item"
                v-for="item in categroyHeadList"
                :key="item.label"
                @click="handleTipsClick(item)"
              >
                <div
                  class="content"
                  :class="{
                    content: true,
                    active: item.label == activeTab
                  }"
                >
                  <img class="tips-image" :src="item.image" alt="" />
                  <span class="tips-label">{{ item.label }}</span>
                  <div class="processnum" v-if="item.num">{{ item.num }}</div>
                </div>
              </div>
            </div>

            <el-scrollbar
              ref="scroll"
              :style="{
                height: '242px'
              }"
              wrap-class="infinity-scrollbar"
              wrap-style="overflow-x: hidden; margin-bottom: 0px;"
            >
              <ul
                v-for="item of tipsCategroyList"
                :key="item.id"
                class="tips-list-item"
                @click="handleTipsRouterPush(item)"
              >
                <div class="flex-col-center">
                  <span class="applicant-user-name">
                    {{ item.content }}
                  </span>
                  <span class="time">
                    {{ item.createTime }}
                  </span>
                </div>
              </ul>
            </el-scrollbar>
          </div>
          <div class="quick-entry">
            <div class="title">快速入口</div>
            <ul class="entry-container">
              <li
                v-for="item in routerList"
                :key="item.path"
                class="entry-item"
                @click="handleClickPushRouter(item)"
              >
                <img src="@/assets/img/module.svg" alt="" />
                <span>{{ item.label }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="echarts-container">
          <div class="echarts-parent monthly-admission-departure">
            <div class="title">入/离院月度趋势</div>
            <div ref="MonthlyAdmissionDeparture" class="cell-echarts"></div>
          </div>

          <div class="echarts-parent disability-type">
            <div class="title">对象属性分布</div>
            <div ref="DisabilityType" class="cell-echarts"></div>
          </div>
        </div>

        <div class="echarts-container mtb-8">
          <div class="echarts-parent extremely-person-number">
            <div class="title">特困人数月度趋势</div>

            <div class="search center">
              <el-date-picker
                style="width: 140px"
                v-model="extremely.queryYear"
                ref="extremelyYearHide"
                :append-to-body="true"
                value-format="yyyy"
                type="year"
                popper-class="select-date-year-set-style"
                placeholder="请选择年度"
                :picker-options="pickerOptions"
                v-clickoutside="extremelyYearHide"
                @change="handleGetSelectHardshipMonthDetails"
              />
              <ts-select
                style="width: 150px;margin-left: 8px;"
                v-model="extremely.objectType"
                placeholder="请选择项目"
                :clearable="true"
                @change="handleGetSelectHardshipMonthDetails"
              >
                <ts-option
                  v-for="item of typeArrs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </div>

            <div ref="ExtremelyPersonNumber" class="cell-echarts"></div>
          </div>
          <div class="echarts-parent nursing-level">
            <div class="title">生活处理能力</div>

            <div class="search flex-column">
              <ts-month-picker
                style="width: 150px;"
                valueFormat="YYYY-MM"
                v-model="nursing.queryYear"
                type="date"
                placeholder="请选择"
                @change="handleGetRenderNursingLevel"
              />
              <ts-select
                style="width: 150px;margin-top: 8px;"
                v-model="nursing.objectType"
                placeholder="请选择项目"
                :clearable="true"
                @change="handleGetRenderNursingLevel"
              >
                <ts-option
                  v-for="item of typeArrs"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </div>

            <div ref="NursingLevel" class="cell-echarts"></div>
          </div>
        </div>

        <div class="echarts-container h600">
          <div class="left-echarts-box">
            <div class="echarts-parent special-fund">
              <div class="title">专项资金月度支出(元)</div>
              <div class="search">
                <ts-select
                  style="width: 150px"
                  v-model="specialFund.entryCode"
                  placeholder="请选择项目"
                  @change="handleGetRenderSpecialFund"
                  :clearable="true"
                >
                  <ts-option
                    v-for="item of civilEntryCodeOption"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
                <ts-select
                  style="width: 150px;margin-left: 8px;"
                  v-model="specialFund.subjectCode"
                  placeholder="请选择科目"
                  @change="handleGetRenderSpecialFund"
                  :clearable="true"
                >
                  <ts-option
                    v-for="item of civilSubjectCodeOption"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </div>
              <div ref="SpecialFund" class="cell-echarts"></div>
            </div>
            <div class="echarts-parent escrow-account">
              <div class="title">代管账号月度支取缴存(元)</div>
              <div ref="EscrowAccount" class="cell-echarts"></div>
            </div>
          </div>
          <div class="right-echarts-box">
            <div class="echarts-parent no-border special-fund-budget">
              <div class="title">专项资金预算及支出分布</div>
              <div class="search">
                <ts-select
                  style="width: 150px;"
                  v-model="specialFundBudget.subjectCode"
                  placeholder="请选择科目"
                  @change="
                    () => {
                      this.handleGetRenderSpecialFundBudget();
                      this.handleGetRenderSpecialFundExpenditure();
                    }
                  "
                  :clearable="true"
                >
                  <ts-option
                    v-for="item of civilSubjectCodeOption"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </div>
              <div ref="SpecialFundBudget" class="cell-echarts"></div>
            </div>
            <div class="echarts-parent no-border special-fund-expenditure">
              <div ref="SpecialFundExpenditure" class="cell-echarts"></div>
            </div>
          </div>
        </div>

        <div class="echarts-container h450 mtb-8">
          <div class="echarts-parent table-container">
            <div class="title">对象保障及护理</div>

            <ts-search-bar
              v-model="searchForm"
              :actions="actions"
              :formList="searchList"
              :elementCol="14"
              @search="search"
              :resetData="{
                type: '1',
                queryYear: this.$dayjs().format('YYYY')
              }"
            >
              <template v-slot:queryYear>
                <el-date-picker
                  style="width: 140px"
                  v-model="searchForm.queryYear"
                  ref="searchYearHide"
                  :append-to-body="true"
                  value-format="yyyy"
                  type="year"
                  popper-class="select-date-year-set-style"
                  placeholder="请选择年度"
                  :picker-options="pickerOptions"
                  v-clickoutside="searchYearHide"
                />
              </template>
            </ts-search-bar>

            <base-table
              class="form-table"
              ref="table"
              border
              stripe
              v-loading="loading"
              :columns="columns"
              @refresh="handleRefreshTable"
            />
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import table from './mixins/table';
import { randomColor } from '@/unit/common.js';
import { commonUtils } from '@/unit/common.js';
import { objectTypeOptions } from '@/views/civilAffairsObjects/configMixins/tabsMixins.js';
import ClickOutsideDirective from '@/directive/clickoutside.js';
export default {
  mixins: [table],
  directives: {
    clickoutside: ClickOutsideDirective
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(date) {
          const today = new Date();
          return date.getFullYear() > today.getFullYear();
        }
      },
      queryYear: this.$dayjs().format('YYYY'),
      activeTab: '证件到期',

      managerObjectData: {},
      fundData: {},
      accountData: {},

      extremely: {
        queryYear: this.$dayjs().format('YYYY'),
        objectType: ''
      },

      nursing: {
        queryYear: this.$dayjs().format('YYYY-MM'),
        objectType: ''
      },
      typeArrs: [{ label: '全部', value: '' }, ...objectTypeOptions],
      civilEntryCodeOption: [],
      civilSubjectCodeOption: [],

      specialFund: {},
      specialFundBudget: {},

      categroyHeadList: [
        {
          label: '证件到期',
          num: 0,
          image: require('@/assets/img/home-tips.svg')
        },
        {
          label: '保障标准提醒',
          num: 0,
          image: require('@/assets/img/process-tips.svg')
        },
        {
          label: '支取提醒',
          num: 0,
          image: require('@/assets/img/money-tips.svg')
        },
        {
          label: '代管账号',
          num: 0,
          image: require('@/assets/img/person-tips.svg')
        }
      ],

      routerList: [
        {
          label: '入院办理',
          path: '/civil-affairs-objects/admission-processing'
        },
        {
          label: '保障申请',
          path: '/civil-affairs-objects/guarantee-application'
        },
        {
          label: '办理离院',
          path: '/civil-affairs-objects/handling-departure'
        },
        {
          label: '档案信息',
          path: '/civil-affairs-objects/civil-affairs-objects'
        },
        {
          label: '资金预算管理',
          path: '/civil-affairs-objects/fund-budget-manage'
        },
        {
          label: '资金到位明细',
          path: '/civil-affairs-objects/fund-place-manage'
        },
        {
          label: '资金支出明细',
          path: '/civil-affairs-objects/fund-expenditure-manage'
        },
        {
          label: '代管账号',
          path: '/civil-affairs-objects/escrow-account'
        },
        {
          label: '账号交接记录',
          path: '/civil-affairs-objects/account-handover-record'
        }
      ],

      pageNo: 1,
      tipsCategroyList: [],
      MonthlyAdmissionDeparture: null,
      ExtremelyPersonNumber: null,
      DisabilityType: null,
      NursingLevel: null,
      SpecialFund: null,
      EscrowAccount: null,
      SpecialFundBudget: null,
      SpecialFundExpenditure: null
    };
  },
  created() {
    let columnsProps = [
      '01',
      '02',
      '03',
      '04',
      '05',
      '06',
      '07',
      '08',
      '09',
      '10',
      '11',
      '12'
    ];
    this.columns.forEach(f => {
      if (columnsProps.includes(f.prop)) {
        f.formatter = (row, prop, cell) => {
          let className = '';
          if (cell === '全护理') className = 'destitute-label';
          if (cell === '半护理') className = 'orphan-label';
          return <div class={className}>{cell}</div>;
        };
      }
    });
  },
  async mounted() {
    this.handleGetTipsCategroyList();
    this.refresh();

    // 消息提醒
    let that = this;
    let TipsContainer = document.querySelector('#TipsContainer');
    let dom = TipsContainer.querySelector('.el-scrollbar__wrap');
    dom.addEventListener(
      'scroll',
      commonUtils.debounce(function() {
        if (this.scrollHeight - this.scrollTop - this.offsetHeight <= 30) {
          that.handleGetTipsCategroyList();
        }
      }, 500)
    );
  },
  methods: {
    refresh() {
      this.ajax.getDictionaries(
        'civil_entry_code',
        this,
        'civilEntryCodeOption'
      );
      this.ajax.getDictionaries(
        'civil_subject_code',
        this,
        'civilSubjectCodeOption'
      );

      this.handleGetNoticeSourceSubject();
      this.handleGetHeaderTotal();
      this.handleGetSelectAdmissionByMonthly();
      this.handleGetSelectHardshipMonthDetails();
      this.handleGetObjectTypeDistributionType();
      this.handleGetRenderNursingLevel();
      this.handleGetRenderSpecialFund();
      this.handleGetRenderEscrowAccount();
      this.handleGetRenderSpecialFundBudget();
      this.handleGetRenderSpecialFundExpenditure();

      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    handleClickPushRouter(item) {
      this.$router.push(item.path);
    },

    handleTipsRouterPush(item) {
      let [serviceName, pathUrl] = (item?.toUrl || '').split('/ts-web-oa');
      this.$router.push(pathUrl);
    },

    async handleGetNoticeSourceSubject() {
      let res = await this.ajax.getNoticeSourceSubject();

      if (res.success == false) {
        this.$message.error(res.message || '获取消息统计失败');
        return;
      }
      res.forEach(f => {
        let [systemname, tipsname] = f.subject.split('——');
        this.categroyHeadList.find(fd => fd.label === tipsname).num =
          f.unReadCount;
      });
    },

    async handleGetHeaderTotal() {
      let api1 = await this.ajax.CivilAffairsPersonnelInfoSelectCountByDate();
      let api2 = this.ajax.CivilAffairsAnnualBudgetSelectCountByDate({
        queryYear: this.queryYear
      });
      let api3 = this.ajax.civilAffairsAccountManagementSelectCountByDate({
        queryYear: this.queryYear
      });

      let [
        managerObjectData = {},
        fundData = {},
        accountData = {}
      ] = await Promise.all([api1, api2, api3]);

      let {
        zrs = 0,
        mzdx_rc = 0,
        qxjydx_rc = 0,
        jsjydx_rc = 0
      } = managerObjectData;
      managerObjectData.zrs = zrs;
      managerObjectData.mzdx_rc = mzdx_rc;
      managerObjectData.qxjydx_rc = qxjydx_rc;
      managerObjectData.jsjydx_rc = jsjydx_rc;
      this.managerObjectData = managerObjectData;

      let {
        budgetAmount = 0,
        receiveAmount = 0,
        expendAmount = 0,
        balanceAmount = 0
      } = fundData;
      fundData.budgetAmount = (budgetAmount / 10000).toFixed(2);
      fundData.receiveAmount = (receiveAmount / 10000).toFixed(2);
      fundData.expendAmount = (expendAmount / 10000).toFixed(2);
      fundData.balanceAmount = (balanceAmount / 10000).toFixed(2);
      this.fundData = fundData;

      let { ndzqJe = 0, jhzqJe = 0 } = accountData;
      accountData.ndzqJe = (ndzqJe / 10000).toFixed(2);
      accountData.jhzqJe = (jhzqJe / 10000).toFixed(2);
      this.accountData = accountData;
    },

    //月度入院/离院人次统计折线图
    async handleGetSelectAdmissionByMonthly() {
      const res = await this.ajax.selectAdmissionByMonthly({
        queryYear: this.queryYear
      });

      let {
        ry_mzList: list1 = [],
        ry_jyList: list2 = [],
        ly_mzList: list3 = [],
        ly_jyList: list4 = []
      } = res;

      let data1 = new Array(12).fill(0),
        data2 = new Array(12).fill(0),
        data3 = new Array(12).fill(0),
        data4 = new Array(12).fill(0);

      list1.forEach(f => {
        f.yf = Number(f.yf);
        data1[f.yf - 1] = f.rc;
      });
      list2.forEach(f => {
        f.yf = Number(f.yf);
        data2[f.yf - 1] = f.rc;
      });
      list3.forEach(f => {
        f.yf = Number(f.yf);
        data3[f.yf - 1] = f.rc;
      });
      list4.forEach(f => {
        f.yf = Number(f.yf);
        data4[f.yf - 1] = f.rc;
      });

      this.renderSubAndFinishedTrendCell(data1, data2, data3, data4);
    },

    renderSubAndFinishedTrendCell(data1, data2, data3, data4) {
      let options = {
        calculable: true,
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: '10%',
          data: [
            '入院-服务对象',
            '入院-寄养对象',
            '离院-服务对象',
            '离院-寄养对象'
          ]
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: [
              '1月',
              '2月',
              '3月',
              '4月',
              '5月',
              '6月',
              '7月',
              '8月',
              '9月',
              '10月',
              '11月',
              '12月'
            ]
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '人数',
            splitLine: {
              show: true
            },
            axisLine: {
              lineStyle: {
                color: '#2f4554'
              }
            }
          }
        ],
        series: [
          {
            name: '入院-服务对象',
            type: 'bar',
            barMaxWidth: 40,
            stack: '入院',
            barGap: '10%',
            itemStyle: {
              normal: {
                color: 'rgba(76,132,247,1)',
                lineStyle: {
                  color: 'rgba(76,132,247,1)',
                  width: 1
                },
                label: {
                  show: true,
                  textStyle: {
                    color: '#fff'
                  },
                  formatter: function(p) {
                    return p.value > 0 ? p.value + '人' : '';
                  },
                  position: 'inside'
                }
              }
            },
            data: data1
          },
          {
            name: '入院-寄养对象',
            type: 'bar',
            barMaxWidth: 40,
            stack: '入院',
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  textStyle: {
                    color: '#fff'
                  },
                  formatter: function(p) {
                    return p.value > 0 ? p.value + '人' : '';
                  },
                  position: 'inside'
                },
                color: 'rgba(76,132,247,0.5)',
                lineStyle: {
                  color: 'rgba(76,132,247,0.5)',
                  width: 1
                }
              }
            },
            data: data2
          },
          {
            name: '离院-服务对象',
            type: 'bar',
            barMaxWidth: 40,
            stack: '离院',

            barMaxWidth: 35,
            barGap: '10%',
            itemStyle: {
              normal: {
                color: 'rgba(236,94,125,1)',
                lineStyle: {
                  color: 'rgba(236,94,125,1)',
                  width: 1
                },
                label: {
                  show: true,
                  textStyle: {
                    color: '#fff'
                  },
                  formatter: function(p) {
                    return p.value > 0 ? p.value + '人' : '';
                  },
                  position: 'inside'
                }
              }
            },
            data: data3
          },
          {
            name: '离院-寄养对象',
            type: 'bar',
            barMaxWidth: 40,
            stack: '离院',
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  textStyle: {
                    color: '#fff'
                  },
                  formatter: function(p) {
                    return p.value > 0 ? p.value + '人' : '';
                  },
                  position: 'inside'
                },
                color: 'rgba(236,94,125,0.5)',
                lineStyle: {
                  color: 'rgba(236,94,125,0.5)',
                  width: 1
                }
              }
            },
            data: data4
          }
        ]
      };

      if (!this.MonthlyAdmissionDeparture) {
        this.MonthlyAdmissionDeparture = this.$echarts.init(
          this.$refs.MonthlyAdmissionDeparture
        );
        window.addEventListener('resize', () => {
          this.MonthlyAdmissionDeparture.resize();
        });
      }
      this.MonthlyAdmissionDeparture.clear();
      this.MonthlyAdmissionDeparture.setOption(options);
    },

    //特困人数月度趋势
    async handleGetSelectHardshipMonthDetails() {
      const {
        MonthDetails = [],
        MonthDetailsAdd = [],
        MonthDetailsSubtract = []
      } = await this.ajax.selectHardshipMonthDetails(this.extremely);
      let total = Object.values(MonthDetails || []);
      let add = Object.values(MonthDetailsAdd || []);
      let subtract = Object.values(MonthDetailsSubtract || []);
      this.renderSelectHardshipMonthDetails(total, add, subtract);
    },

    renderSelectHardshipMonthDetails(total, add, subtract) {
      var colors = ['#5793f3', '#d14a61', '#675bba'];
      let options = {
        color: colors,

        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        grid: {
          left: '3%',
          right: '6%',
          bottom: '2%',
          containLabel: true
        },
        legend: {
          top: '10%',
          left: '10%',
          data: ['总数', '新增', '核减']
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: [
              '1月',
              '2月',
              '3月',
              '4月',
              '5月',
              '6月',
              '7月',
              '8月',
              '9月',
              '10月',
              '11月',
              '12月'
            ]
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '新增',
            min: 0,
            max: 20,
            position: 'right',
            axisLine: {
              lineStyle: {
                color: colors[0]
              }
            },
            axisLabel: {
              formatter: '{value} 人'
            }
          },
          {
            type: 'value',
            name: '核减',
            min: 0,
            max: 20,
            position: 'right',
            offset: 60,
            axisLine: {
              lineStyle: {
                color: colors[1]
              }
            },
            axisLabel: {
              formatter: '{value} 人'
            }
          },
          {
            type: 'value',
            name: '总数',
            position: 'left',
            axisLine: {
              lineStyle: {
                color: colors[2]
              }
            },
            axisLabel: {
              formatter: '{value} 人'
            }
          }
        ],
        series: [
          {
            name: '新增',
            type: 'bar',
            data: add,

            label: {
              show: true,
              textStyle: {
                color: colors[0]
              },
              formatter: function({ data }) {
                return data ? data + '人' : '';
              },
              position: 'top'
            }
          },
          {
            name: '核减',
            type: 'bar',
            yAxisIndex: 1,
            data: subtract,

            label: {
              show: true,
              textStyle: {
                color: colors[1]
              },
              formatter: function({ data }) {
                return data ? data + '人' : '';
              },
              position: 'top'
            }
          },
          {
            name: '总数',
            type: 'line',
            yAxisIndex: 2,
            data: total,

            label: {
              show: true,
              textStyle: {
                color: colors[2]
              },
              formatter: function({ data }) {
                return data ? data + '人' : '';
              },
              position: 'top'
            }
          }
        ]
      };

      if (!this.ExtremelyPersonNumber) {
        this.ExtremelyPersonNumber = this.$echarts.init(
          this.$refs.ExtremelyPersonNumber
        );
        window.addEventListener('resize', () => {
          this.ExtremelyPersonNumber.resize();
        });
      }
      this.ExtremelyPersonNumber.clear();
      this.ExtremelyPersonNumber.setOption(options);
    },

    //对象属性分布
    async handleGetObjectTypeDistributionType() {
      const res = await this.ajax.CivilAffairsPersonnelInfoSelectCountType();

      if (res && res.length) {
        let total = 0;

        let parent = res.map(m => {
          if (!isNaN(parseFloat(m.rc))) {
            total += parseFloat(m.rc);
          }

          return {
            name: m.object_type_lx,
            value: m.rc
          };
        });

        let children = res.reduce(function(p, r) {
          let item = r.countMapList.map(c => {
            return {
              name: c.lx,
              value: c.rc
            };
          });

          p.push(...item);
          return p;
        }, []);
        this.renderObjectTypeDistributionType(parent, children, total);
      }
    },

    renderObjectTypeDistributionType(parent = [], children = [], total = 0) {
      let options = {
        title: [
          {
            text: '{name|' + total + '人' + '}',
            top: 'center',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 18,
                  fontWeight: 'normal',
                  color: '#666666'
                }
              }
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b}:({c}个)'
        },
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            radius: ['30%', '50%'],
            label: {
              normal: {
                position: 'inner',
                formatter: function(params) {
                  return `${params.name}\n${params.value}人`;
                }
              }
            },
            data: parent
          },
          {
            type: 'pie',
            radius: ['50%', '70%'],
            labelLine: {
              show: true
            },
            label: {
              show: true,
              formatter: function(params) {
                return `${params.name}\n${params.value}人`;
              }
            },
            data: children
          }
        ]
      };

      if (!this.DisabilityType) {
        this.DisabilityType = this.$echarts.init(this.$refs.DisabilityType);
        window.addEventListener('resize', () => {
          this.DisabilityType.resize();
        });
      }
      this.DisabilityType.clear();
      this.DisabilityType.setOption(options);
    },

    //护理级别分布
    async handleGetRenderNursingLevel() {
      const res = await this.ajax.selectNursingLevelCount(this.nursing);
      let data = (res || []).map(m => {
        let name = m.wardText
          ? `${m.wardText}\n${m.nursingLevelText}`
          : m.nursingLevelText;
        return {
          name,
          value: m.number
        };
      });
      this.renderNursingLevel(data);
    },

    renderNursingLevel(data = []) {
      let color = [];
      for (let i = 0; i < data.length; i++) {
        color.push(randomColor());
      }

      let total = data.reduce((a, b) => {
        return a + b.value * 1;
      }, 0);

      let options = {
        color,
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}'
        },
        title: [
          {
            text: '{name|' + total + '人' + '}',
            top: 'center',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 18,
                  fontWeight: 'normal',
                  color: '#666666'
                }
              }
            }
          }
        ],
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['45%', '60%'],
            labelLine: {
              show: true,
              length: 15,
              length2: 20
            },
            label: {
              show: true,
              formatter: function(params) {
                return `${params.name}\n${params.value}人`;
              }
            },
            data
          }
        ]
      };

      if (!this.NursingLevel) {
        this.NursingLevel = this.$echarts.init(this.$refs.NursingLevel);
        window.addEventListener('resize', () => {
          this.NursingLevel.resize();
        });
      }
      this.NursingLevel.clear();
      this.NursingLevel.setOption(options);
    },

    //代管账号月度支取缴存(元)
    async handleGetRenderSpecialFund() {
      let params = {
        entryCode: this.specialFund.entryCode,
        subjectCode: this.specialFund.subjectCode,
        queryYear: this.queryYear
      };
      const list = await this.ajax.selectExpenditureByMonthly(params);
      let data = new Array(12).fill(0);
      (list || []).forEach(f => {
        f.yf = Number(f.yf);
        data[f.yf - 1] = f.expendAmount;
      });
      this.renderSpecialFund(data);
    },

    renderSpecialFund(data = []) {
      let options = {
        color: ['#3B84F4'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '4%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ],
          axisLabel: {
            show: true,
            interval: 0,
            rotate: 0,
            margin: 10,
            inside: false,
            textStyle: {
              fontWeight: '500'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '金额（元）',
          splitLine: {
            show: true
          },
          axisLine: {
            lineStyle: {
              color: '#2f4554'
            }
          }
        },
        series: [
          {
            type: 'bar',
            label: {
              show: true,
              textStyle: {
                color: '#fff'
              },
              formatter: function({ data }) {
                return data ? data + '元' : '';
              },
              position: 'inside'
            },
            data
          }
        ]
      };

      if (!this.SpecialFund) {
        this.SpecialFund = this.$echarts.init(this.$refs.SpecialFund);
        window.addEventListener('resize', () => {
          this.SpecialFund.resize();
        });
      }
      this.SpecialFund.clear();
      this.SpecialFund.setOption(options);
    },

    async handleGetRenderEscrowAccount() {
      const res = await this.ajax.civilAffairsWithdrawalRecordsSelectByYear({
        queryYear: this.queryYear
      });
      let data = new Array(12).fill(0);
      if (res.success) {
        (res.object || []).forEach(f => {
          f.yearMonth = Number(f.yearMonth);
          data[f.yearMonth - 1] = f.withdrawalAmount;
        });
      }
      this.renderEscrowAccount(data);
    },

    renderEscrowAccount(data = []) {
      let options = {
        color: ['#3B84F4'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '4%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ],
          axisLabel: {
            show: true,
            interval: 0,
            rotate: 0,
            margin: 10,
            inside: false,
            textStyle: {
              fontWeight: '500'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '金额（元）',
          splitLine: {
            show: true
          },
          axisLine: {
            lineStyle: {
              color: '#2f4554'
            }
          }
        },
        series: [
          {
            type: 'bar',
            label: {
              show: true,
              textStyle: {
                color: '#fff'
              },
              formatter: function({ data }) {
                return data ? data + '元' : '';
              },
              position: 'inside'
            },
            data
          }
        ]
      };

      if (!this.EscrowAccount) {
        this.EscrowAccount = this.$echarts.init(this.$refs.EscrowAccount);
        window.addEventListener('resize', () => {
          this.EscrowAccount.resize();
        });
      }
      this.EscrowAccount.clear();
      this.EscrowAccount.setOption(options);
    },

    async handleGetRenderSpecialFundBudget() {
      let params = {
        queryYear: this.queryYear,
        subjectCode: this.specialFundBudget.subjectCode
      };
      const res = await this.ajax.CivilAffairsAnnualBudgetSelectSubjectCountByEntry(
        params
      );
      let data = (res || []).map(m => {
        return {
          name: m.subjectText,
          value: m.budgetAmount
        };
      });
      this.renderSpecialFundBudget(data);
    },

    renderSpecialFundBudget(data = []) {
      let color = [];
      for (let i = 0; i < data.length; i++) {
        color.push(randomColor());
      }
      let options = {
        color,
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}'
        },
        title: [
          {
            text: '预算',
            top: 'center',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 18,
                  fontWeight: 'normal',
                  color: '#666666'
                }
              }
            }
          }
        ],
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['45%', '60%'],
            labelLine: {
              show: true,
              length: 15,
              length2: 20
            },
            label: {
              show: true,
              formatter: function(params) {
                return `${params.name}\n${params.value}元`;
              }
            },
            data
          }
        ]
      };

      if (!this.SpecialFundBudget) {
        this.SpecialFundBudget = this.$echarts.init(
          this.$refs.SpecialFundBudget
        );
        window.addEventListener('resize', () => {
          this.SpecialFundBudget.resize();
        });
      }
      this.SpecialFundBudget.clear();
      this.SpecialFundBudget.setOption(options);
    },

    async handleGetRenderSpecialFundExpenditure() {
      let params = {
        queryYear: this.queryYear,
        subjectCode: this.specialFundBudget.subjectCode
      };
      const res = await this.ajax.CivilAffairsFundingExpenditureSelectSubjectCountByEntry(
        params
      );
      let data = (res || []).map(m => {
        return {
          name: m.subjectText,
          value: m.expendAmount
        };
      });
      this.renderSpecialFundExpenditure(data);
    },

    renderSpecialFundExpenditure(data = []) {
      let color = [];
      for (let i = 0; i < data.length; i++) {
        color.push(randomColor());
      }
      let options = {
        color,
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}'
        },
        title: [
          {
            text: '支出',
            top: 'center',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 18,
                  fontWeight: 'normal',
                  color: '#666666'
                }
              }
            }
          }
        ],
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['45%', '60%'],
            labelLine: {
              show: true,
              length: 15,
              length2: 20
            },
            label: {
              show: true,
              formatter: function(params) {
                return `${params.name}\n${params.value}元`;
              }
            },
            data
          }
        ]
      };

      if (!this.SpecialFundExpenditure) {
        this.SpecialFundExpenditure = this.$echarts.init(
          this.$refs.SpecialFundExpenditure
        );
        window.addEventListener('resize', () => {
          this.SpecialFundExpenditure.resize();
        });
      }
      this.SpecialFundExpenditure.clear();
      this.SpecialFundExpenditure.setOption(options);
    },

    //重置所有表格大小
    resizeAll() {
      this.MonthlyAdmissionDeparture.resize();
      this.ExtremelyPersonNumber.resize();
      this.DisabilityType.resize();
      this.NursingLevel.resize();
      this.SpecialFund.resize();
      this.EscrowAccount.resize();
      this.SpecialFundBudget.resize();
      this.SpecialFundExpenditure.resize();
    },

    yearHide() {
      this.$refs.yearHide.hidePicker();
    },

    searchYearHide() {
      this.$refs.searchYearHide.hidePicker();
    },

    extremelyYearHide() {
      this.$refs.extremelyYearHide.hidePicker();
    },

    async handleTipsClick(e) {
      this.activeTab = e.label;

      this.tipsCategroyList = [];
      this.pageNo = 1;
      this.handleGetTipsCategroyList();
    },

    handleGetTipsCategroyList() {
      let pageSize = 5;
      let TipsContainer = document.querySelector('#TipsContainer');
      let dom = TipsContainer.querySelector('.el-scrollbar__wrap');

      let loadingDom = document.createElement('div');
      loadingDom.style.textAlign = 'center';
      loadingDom.style.color = '#999';
      loadingDom.innerHTML =
        '加载中<i class="el-icon-loading" style="margin-left: 8px;"></i>';

      let finishedDom = document.createElement('div');
      finishedDom.classList.add('no-more');
      finishedDom.style.color = '#999';
      finishedDom.style.textAlign = 'center';
      finishedDom.innerHTML = '到底啦~';

      let searchData = {
        subject: `民政对象——${this.activeTab}`,
        pageNo: this.pageNo,
        pageSize,
        source: '民政对象',
        sidx: 'create_date',
        sord: 'desc'
      };
      dom.append(loadingDom);
      let noMore = dom.querySelector('.no-more');
      if (noMore) noMore.remove();

      this.ajax.noticeListCiviAffairsObjects(searchData).then(res => {
        loadingDom.remove();
        if (res.success == false) {
          this.$message.error(res.message || '获取消息失败');
          return;
        }
        if (this.pageNo == 1) this.tipsCategroyList = [];
        this.tipsCategroyList.push(...res.rows);
        this.pageNo++;
        if (res.totalCount < pageSize * this.pageNo) {
          dom.append(finishedDom);
        }
      });
    }
  }
};
</script>

<style lang="scss">
.select-date-year-set-style {
  .el-date-picker__header-label {
    cursor: default;
    &:hover {
      color: rgb(96, 98, 102);
    }
  }
}
</style>

<style lang="scss" scoped>
.handling-departure {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .ts-table .cell {
      padding: 0px;
    }
    .destitute-label {
      width: 100%;
      line-height: 30px;
      color: #fff;
      background-color: #f6c179;
    }

    .orphan-label {
      width: 100%;
      line-height: 30px;
      color: #fff;
      background-color: #5694df;
    }
  }
  .no-border {
    border: 0px !important;
  }
  .title {
    position: absolute;
    left: 8px;
    top: 8px;
    font-size: 16px;
    font-weight: 700;
    &::before {
      content: '1';
      background-color: rgb(82, 96, 255);
      margin-right: 4px;
      height: 16px;
      width: 4px;
      border-radius: 4px;
      color: rgb(82, 96, 255);
    }
  }

  .search {
    position: absolute;
    right: 8px;
    top: 8px;
    font-size: 16px;
    font-weight: 700;
    display: flex;
    z-index: 999999;
    &.center {
      right: 50%;
      transform: translateX(50%);
    }
  }

  .content {
    width: 100%;
    height: 100%;
    margin-top: 8px;
    .statistics-container {
      display: flex;
      .item {
        flex: 1;
        margin-right: 8px;
        border-radius: 4px;
        padding: 8px;
        color: #fff;
        .blod {
          font-weight: 600;
        }

        .heard {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
          .value {
            font-size: 18px;
          }
          .heard-item-title {
            font-size: 16px;
          }
        }
        .target-info {
          display: flex;
          align-items: center;
          justify-content: space-around;

          .item-target {
            text-align: center;
            .value {
              font-size: 18px;
            }
            .label {
              font-size: 14px;
            }
          }
        }

        &:last-child {
          margin-right: 0px;
        }

        &.first {
          background-color: #3b84f4;
        }
        &.second {
          background-color: #f0a161;
        }
        &.third {
          background-color: #7a87f7;
        }
      }
    }

    .tips-container {
      display: flex;
      height: 260px;
      margin: 8px 0;
      .left {
        flex: 2;
        height: 100%;
        margin-right: 8px;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .top-left-container {
          display: flex;
          height: 58px;
          width: 100%;
          .tips-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #eee;
            .content {
              width: 100px;
              display: flex;
              flex-direction: column;
              align-items: center;
              position: relative;
              cursor: pointer;
              &.active {
                border-bottom: 2px solid #5260ff;
                .tips-label {
                  color: #5260ff !important;
                }
              }
              .processnum {
                position: absolute;
                top: -4px;
                left: 50%;
                transform: translate(70%, 5px);
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                padding: 0px 4px;
                border-radius: 10px;
                color: #fff !important;
                background-color: red;
              }
              .tips-image {
                width: 22px;
                height: 22px;
                margin-bottom: 4px;
              }
              .tips-label {
                color: #333 !important;
                font-size: 12px;
              }
            }
          }
        }

        .tips-list-item {
          padding: $primary-spacing $medium-spacing;
          cursor: pointer;

          &:not(:last-child) {
            border-bottom: 1px solid $theme-border-color;
          }
          &.active {
            background-color: $list-hover-color;
          }

          .applicant-user-name {
            font-size: 16px;
            font-weight: bold;
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      .quick-entry {
        flex: 1;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        position: relative;
        padding: 30px 8px 8px 8px;
        .entry-container {
          display: flex;
          flex-wrap: wrap;
          .entry-item {
            width: 33%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 16px;
            font-weight: 400;
            padding: 8px;
            &:hover {
              color: rgb(82, 96, 255) !important;
            }
            img {
              width: 24px;
              height: 24px;
              margin-bottom: 8px;
            }
          }
        }
      }
    }

    .echarts-container {
      display: flex;
      height: 328px;
      width: 100%;

      &.h600 {
        height: 680px;
      }

      &.h450 {
        height: 450px;
      }

      &.mtb-8 {
        margin: 8px 0;
      }

      .echarts-parent {
        position: relative;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        &.table-container {
          width: 100%;
          display: flex;
          flex-direction: column;
          padding: 35px 8px 8px 8px;

          ::v-deep {
            .left-search-content {
              position: absolute;
              right: 0;
              top: -30px;
            }
            .form-table {
              flex: 1;
              overflow: hidden;
              transform: scale(1);
            }
          }
        }
      }

      .monthly-admission-departure,
      .extremely-person-number {
        flex: 2;
        margin-right: 8px;
        .cell-echarts {
          height: calc(100%);
        }
      }

      .disability-type {
        flex: 1;
        .cell-echarts {
          height: calc(100%);
        }
      }

      .nursing-level {
        flex: 1;
        .cell-echarts {
          height: calc(100%);
        }
      }

      .left-echarts-box {
        flex: 2;
        margin-right: 8px;
        display: flex;
        flex-direction: column;
        position: relative;
        border-radius: 4px;
      }
      .right-echarts-box {
        flex: 1;
        display: flex;
        flex-direction: column;

        position: relative;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
      }
      .special-fund {
        margin-bottom: 8px;
      }

      .special-fund,
      .escrow-account,
      .special-fund-budget,
      .special-fund-expenditure {
        height: 340px;
        .cell-echarts {
          height: calc(100%);
        }
      }
    }
  }
}
</style>
