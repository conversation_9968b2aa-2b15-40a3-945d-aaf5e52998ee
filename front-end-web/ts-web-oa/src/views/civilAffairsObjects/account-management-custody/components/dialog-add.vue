<template>
  <!-- 代管账号 -->
  <ts-dialog
    custom-class="dialog-add"
    append-to-body
    :visible.sync="visible"
    :title="title"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item prop="personnelId" :rules="rules.required" label="姓名">
          <base-select
            style="width: 100%"
            v-model="form.personnelId"
            :inputText.sync="form.name"
            :loadMethod="handleGetPerson"
            label="name"
            value="personnelId"
            searchInputName="name"
            :clearable="false"
            :disabled="isDisabled"
            @select="handlePersonSelect"
          />
        </ts-form-item>

        <ts-form-item label="开户行" prop="bank" :rules="rules.required">
          <ts-input
            v-model="form.bank"
            :disabled="isDisabled"
            placeholder="请输入"
          />
        </ts-form-item>

        <ts-form-item label="账号" prop="account" :rules="rules.required">
          <ts-input
            v-model="form.account"
            :disabled="isDisabled"
            placeholder="请输入"
          />
        </ts-form-item>

        <ts-form-item label="账号余额(元)">
          <ts-input
            v-model="form.accountBalance"
            :disabled="isDisabled"
            placeholder="请输入"
            @input="validateTowDecimalPlaces($event, 'form', 'accountBalance')"
          />
        </ts-form-item>

        <ts-form-item
          label="登记日期"
          prop="registrationDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width: 100%;"
            valueFormat="YYYY-MM-DD"
            v-model="form.registrationDate"
            :disabled="isDisabled"
            type="date"
            placeholder="请选择"
          />
        </ts-form-item>

        <ts-form-item label="交付人" prop="deliverer" :rules="rules.required">
          <ts-input v-model="form.deliverer" placeholder="请输入" />
        </ts-form-item>

        <ts-form-item label="接收人" prop="recipient" :rules="rules.required">
          <ts-input v-model="form.recipient" placeholder="请输入" />
        </ts-form-item>

        <ts-form-item label="证明人" prop="certifier" :rules="rules.required">
          <ts-input v-model="form.certifier" placeholder="请输入" />
        </ts-form-item>

        <ts-form-item
          label="交接日期"
          prop="handoverDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width: 100%;"
            valueFormat="YYYY-MM-DD"
            v-model="form.handoverDate"
            type="date"
            placeholder="请选择"
          />
        </ts-form-item>

        <ts-form-item label="备注" v-if="!isDisabled">
          <ts-input
            v-model="form.remarks"
            type="textarea"
            class="textarea"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone, inputTowDecimalPlaces } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      title: '',
      type: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    isDisabled() {
      return ['back', 'tackover'].includes(this.type);
    }
  },
  methods: {
    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },

    async open(opt = {}) {
      let { title, type, row } = opt;
      this.title = title;
      this.type = type;

      this.$set(this, 'form', {
        registrationDate: this.$dayjs().format('YYYY-MM-DD'),
        handoverDate: this.$dayjs().format('YYYY-MM-DD')
      });
      if (this.type !== 'add') this.$set(this, 'form', deepClone(row));

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });

      this.visible = true;
    },

    async handleGetPerson(data) {
      let res = await this.ajax.CivilAffairsPersonnelInfoList({
        pageSize: 15,
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },

    handlePersonSelect(row) {
      this.$set(this.form, 'idCard', row.identityNumber || '');
    },

    async submit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.form);
      let API = null;

      switch (this.type) {
        case 'add':
          API = this.ajax.CivilAffairsAccountManagementSave;
          break;
        case 'edit':
          API = this.ajax.CivilAffairsAccountManagementUpdate;
          break;
        case 'back':
          API = this.ajax.civilAffairsAccountManagementReturnAccount;
          data.status = 2;
          break;
        case 'tackover':
          API = this.ajax.civilAffairsAccountManagementReturnAccount;
          data.status = 1;
          break;
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refresh');
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-add {
    width: 600px !important;
    height: 640px;

    .el-dialog__body {
      height: calc(100% - 88px);
      overflow: auto;
      padding-right: 64px !important;
    }
  }

  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
