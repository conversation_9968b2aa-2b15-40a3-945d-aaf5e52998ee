export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      actions: [],
      searchList: [
        {
          label: '姓名',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '身份证',
          value: 'idCard',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '存折号码',
          value: 'account',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '姓名',
          prop: 'name',
          align: 'center',
          sortable: true,
          width: 80
        },
        {
          label: '身份证号',
          prop: 'idCard',
          align: 'center',
          width: 170
        },
        {
          label: '存折所在行',
          prop: 'bank',
          align: 'center',
          sortable: true,
          width: 130,
          formatter: row => {
            return <span>{row.bankText}</span>;
          }
        },
        {
          label: '存折号码',
          prop: 'account',
          align: 'center',
          width: 180
        },
        {
          label: '支取日期',
          prop: 'withdrawalDate',
          align: 'center',
          sortable: true,
          width: 110
        },
        {
          label: '支前余额（元）',
          prop: 'prePaymentBalance',
          align: 'right',
          width: 130,
          sortable: true,
          formatter: row => {
            return <span class="operation-span"> {row.prePaymentBalance}</span>;
          }
        },
        {
          label: '支取金额（元）',
          prop: 'withdrawalAmount',
          align: 'right',
          width: 130,
          sortable: true,
          formatter: row => {
            return <span class="operation-span"> {row.withdrawalAmount}</span>;
          }
        },
        {
          label: '支后余额（元）',
          prop: 'postPaymentBalance',
          align: 'right',
          width: 130,
          sortable: true,
          formatter: row => {
            return (
              <span class="operation-span"> {row.postPaymentBalance}</span>
            );
          }
        },
        {
          label: '经办人',
          prop: 'operator',
          sortable: true,
          align: 'center'
        },
        {
          label: '证明人',
          prop: 'certifier',
          sortable: true,
          align: 'center'
        },
        {
          label: '附件',
          prop: 'originalName',
          align: 'center'
        },
        {
          label: '备注',
          prop: 'remarks',
          align: 'center'
        },
        {
          label: '最近修改人',
          prop: 'updateUserName',
          sortable: true,
          align: 'center',
          width: 110
        },
        {
          label: '最近修改时间',
          prop: 'updateDate',
          sortable: true,
          align: 'center',
          width: 170
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'red'
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
