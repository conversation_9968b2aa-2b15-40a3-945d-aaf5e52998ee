<template>
  <div class="task-supervision">
    <ts-tabs v-model="activeTab" class="tabs-container">
      <ts-tab-pane
        v-for="(item, index) in buttonList"
        :name="item.id"
        :key="index"
      >
        <span slot="label">{{ item.label }}</span>
      </ts-tab-pane>
    </ts-tabs>
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <template slot="registerType">
        <el-select v-model="searchForm.registerType" clearable filterable>
          <el-option
            v-for="(item, index) in registerTypeList"
            :key="index"
            :label="item.typeName"
            :value="item.id"
          ></el-option>
        </el-select>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :sidx="'create_date'"
      :columns="activeTab === 1 ? columns : columnsCopy"
      @refresh="handleRefreshTable"
    />
    <dialog-normal ref="dialogNormal" @refreshTable="search" />
    <dialog-action ref="dialogAction" @refreshTable="search" />
    <dialog-feedback ref="dialogFeedback" @refreshTable="search" />
  </div>
</template>
<script>
import table from './mixins/table';
import { camelToKebab } from '../config';
import dialogNormal from '../components/dialog-normal.vue';
import dialogAction from '../components/dialog-action.vue';
import dialogFeedback from '../components/dialog-feedback.vue';
export default {
  mixins: [table],
  components: { dialogNormal, dialogAction, dialogFeedback },
  data() {
    return {
      loading: false,
      totalCount: 0,
      totalCount1: 0,
      activeTab: 1,
      registerTypeList: []
    };
  },
  watch: {
    activeTab: {
      async handler(val) {
        this.searchForm = {};
        this.search();
      }
    }
  },
  created() {
    this.getSuperviseTypeList();
    this.$nextTick(() => {
      this.search();
    });
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    // 详情
    handleDetails(row) {
      this.$refs.dialogNormal.open(row, 5);
    },
    // 转办
    handleTransfer(row) {
      this.$refs.dialogNormal.open(row, 1);
    },
    // 办理
    handleHandle(row) {
      this.$refs.dialogNormal.open(row, 2);
    },
    // 验收
    handleCheck(row) {
      this.$refs.dialogNormal.open(row, 3);
    },
    // 批示
    handleApprove(row) {
      this.$refs.dialogNormal.open(row, 4);
    },
    // 进度反馈
    handleFeedBack(row) {
      if (row.registerStatus == '2') {
        this.$refs.dialogFeedback.open(row, 1, true);
      } else {
        this.ajax.getFeedbackDetails({ registerId: row.id }).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '获取失败');
            return;
          }
          if (res.object.length == 0) {
            this.$message.warning('暂无反馈记录');
          } else {
            this.$refs.dialogFeedback.open(row, 1);
          }
        });
      }
    },
    // 延期
    handleDelay(row) {
      this.$refs.dialogAction.open(row, 2, '延期');
    },
    handleAdd() {
      this.dialogAdd = true;
    },
    getSuperviseTypeList() {
      this.ajax.getSuperviseTypeList().then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '任务类型获取失败');
          return;
        }
        this.registerTypeList = res.object || [];
      });
    },
    async handleRefreshTable() {
      this.loading = true;
      let { completeDateList = [] } = this.searchForm;
      let [startDate = '', endDate = ''] = completeDateList;
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDate,
          endDate,
          taskIndex: this.activeTab,
          sidx: camelToKebab(this.$refs.table.sidx),
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      delete searchForm.completeDateList;
      this.ajax.getTaskRegistrationList(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$message.error(res.message || '列表数据获取失败');
          return;
        }
        if (this.activeTab === 1) {
          this.totalCount = res.totalCount;
        } else {
          this.totalCount1 = res.totalCount;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  },
  computed: {
    buttonList() {
      return [
        {
          id: 1,
          label: `待我处理${this.totalCount ? `(${this.totalCount})` : ''}`
        },
        {
          id: 2,
          label: `我已处理${this.totalCount1 ? `(${this.totalCount1})` : ''}`
        }
      ];
    }
  }
};
</script>
<style lang="scss" scoped>
.task-supervision {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
}
.details-span {
  color: #5260ff;
}
.hand {
  cursor: pointer;
}
.registerMatter {
  text-align: left;
  display: flex;
  cursor: pointer;
  .over {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .registerUrgency {
    margin-right: 2px;
    padding: 0 5px;
    border-radius: 5px;
    line-height: 19px;
  }
  .red {
    border: 1px solid #ff3b30;
    color: #ff3b30;
  }
  .yellow {
    border: 1px solid #fe9400;
    color: #fe9400;
  }
}
</style>
