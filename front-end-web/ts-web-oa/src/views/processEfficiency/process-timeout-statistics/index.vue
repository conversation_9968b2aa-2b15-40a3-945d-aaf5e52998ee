<template>
  <div class="process-timeout-statistics-box">
    <ts-search-bar
      v-model="searchForm"
      :actions="searchActions"
      :formList="searchList"
      @search="refresh"
    >
      <template slot="dateRange">
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.startDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="startPickerVisible"
          @openChange="handleStartPickerOpenChange"
          @panelChange="handleStartPickerChange"
        ></ts-date-picker>
        -
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.endDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="endPickerVisible"
          @openChange="handleEndPickerOpenChange"
          @panelChange="handleEndPickerChange"
        ></ts-date-picker>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />
    <dialog-urge
      v-model="visibleDialogUrge"
      :workflowData="workflowData"
      @submit="refresh"
    />
    <dialog-force-end
      v-model="visibleDialogForceEnd"
      :workflowData="workflowData"
      @submit="refresh"
    />
  </div>
</template>

<script>
import DialogUrge from './components/dialog-urge.vue';
import DialogForceEnd from './components/dialog-force-end.vue';
import table from './mixins/table';
export default {
  components: {
    DialogUrge,
    DialogForceEnd
  },
  mixins: [table],
  data() {
    return {
      loading: false,
      visibleDialogUrge: false,
      visibleDialogForceEnd: false,
      workflowData: {}
    };
  },
  methods: {
    handleStartPickerOpenChange(status) {
      if (status) {
        this.startPickerVisible = true;
        this.endPickerVisible = false;
      } else {
        this.startPickerVisible = false;
      }
    },
    handleEndPickerOpenChange(status) {
      if (status) {
        this.endPickerVisible = true;
        this.startPickerVisible = false;
      } else {
        this.endPickerVisible = false;
      }
    },
    handleStartPickerChange(value) {
      let start = this.$dayjs(value).format('YYYY-MM');
      if (start != this.searchForm.startDate) this.dateType = 'other';
      this.$set(this.searchForm, 'startDate', start);
      this.startPickerVisible = false;
    },
    handleEndPickerChange(value) {
      let end = this.$dayjs(value).format('YYYY-MM');
      if (end != this.searchForm.endDate) this.dateType = 'other';
      this.$set(this.searchForm, 'endDate', end);
      this.endPickerVisible = false;
    },
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      this.ajax.getWorkflowTimeoutList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleUrge(row) {
      this.workflowData = {
        wfInstId: row.wfInstId,
        wfDefId: row.wfDefId,
        workflowTitle: row.workflowTitle
      };

      this.visibleDialogUrge = true;
    },
    handleForceEnd(row) {
      this.workflowData = {
        workflowInstId: row.wfInstId
      };
      this.visibleDialogForceEnd = true;
    },
    handleExport() {
      let a = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      a.href =
        '/ts-workflow/workflow/efficiencyAnalysis/timeoutTaskList/export?' +
        conditionList.join('&');
      a.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.process-timeout-statistics-box {
  width: 100%;
  height: 100%;
  background: rgb(255, 255, 255);
  padding: 8px;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.form-table {
  flex: 1;
  overflow: hidden;
}
</style>
