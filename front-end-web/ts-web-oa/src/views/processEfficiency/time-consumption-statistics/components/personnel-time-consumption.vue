<template>
  <div class="process-timeout-statistics-box">
    <ts-search-bar
      v-model="searchForm"
      :actions="searchActions"
      :formList="searchList"
      @search="refresh"
      @reset="reset"
    >
      <template slot="dateRange">
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.startDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="startPickerVisible"
          @openChange="handleStartPickerOpenChange"
          @panelChange="handleStartPickerChange"
        ></ts-date-picker>
        -
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.endDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="endPickerVisible"
          @openChange="handleEndPickerOpenChange"
          @panelChange="handleEndPickerChange"
        ></ts-date-picker>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :pageSize="20"
      :columns="columns"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      searchForm: {
        startDate: this.$dayjs().format('YYYY') + '-01',
        endDate: this.$dayjs().format('YYYY') + '-12'
      },
      searchActions: [
        {
          label: '导出',
          click: this.handleExport
        }
      ],
      searchList: [
        {
          label: '审批人',
          value: 'assigneeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '时间范围',
          value: 'dateRange'
        }
      ],
      startPickerVisible: false,
      endPickerVisible: false,
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '工号',
          align: 'letf',
          prop: 'actAssigneeNo'
        },
        {
          label: '姓名',
          align: 'letf',
          prop: 'actAssigneeName'
        },
        {
          label: '部门',
          align: 'center',
          prop: 'organizationName'
        },
        {
          label: '审批数量',
          align: 'center',
          prop: 'approveNumbers'
        },
        {
          label: '总耗时(H)',
          align: 'center',
          prop: 'totalDuration'
        },
        {
          label: '平均耗时(H)',
          align: 'center',
          prop: 'avgDuration'
        }
      ]
    };
  },
  methods: {
    handleStartPickerOpenChange(status) {
      if (status) {
        this.startPickerVisible = true;
        this.endPickerVisible = false;
      } else {
        this.startPickerVisible = false;
      }
    },
    handleEndPickerOpenChange(status) {
      if (status) {
        this.endPickerVisible = true;
        this.startPickerVisible = false;
      } else {
        this.endPickerVisible = false;
      }
    },
    handleStartPickerChange(value) {
      let start = this.$dayjs(value).format('YYYY-MM');
      if (start != this.searchForm.startDate) this.dateType = 'other';
      this.$set(this.searchForm, 'startDate', start);
      this.startPickerVisible = false;
    },
    handleEndPickerChange(value) {
      let end = this.$dayjs(value).format('YYYY-MM');
      if (end != this.searchForm.endDate) this.dateType = 'other';
      this.$set(this.searchForm, 'endDate', end);
      this.endPickerVisible = false;
    },
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    reset() {
      this.searchForm.startDate = this.$dayjs().format('YYYY') + '-01';
      this.searchForm.endDate = this.$dayjs().format('YYYY') + '-12';
      this.refresh();
    },
    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      this.ajax.getPersonnelDuration(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleExport() {
      let a = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      a.href =
        '/ts-workflow/workflow/efficiencyAnalysis/personnelDuration/export?' +
        conditionList.join('&');
      a.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.process-timeout-statistics-box {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.form-table {
  flex: 1;
  overflow: hidden;
}
</style>
