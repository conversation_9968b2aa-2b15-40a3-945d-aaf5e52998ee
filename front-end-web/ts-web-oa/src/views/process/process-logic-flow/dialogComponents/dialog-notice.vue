<template>
  <ts-dialog
    class="dialog-notice"
    :title="title"
    width="800px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div>
      <ts-row>
        <ts-col :span="18">
          <ts-form
            ref="form"
            :model="form"
            label-width="110px"
            class="formTable"
          >
            <ts-form-item prop="msgtype" label="通知类型">
              <ts-select v-model="form.msgtype" disabled>
                <ts-option value="1" label="微信"></ts-option>
                <ts-option value="2" label="WEB"></ts-option>
                <ts-option value="3" label="短信"></ts-option>
                <ts-option value="4" label="邮箱"></ts-option>
              </ts-select>
            </ts-form-item>
            <ts-form-item prop="transferredToRemindType" label="通知人">
              <ts-radio-group v-model="form.transferredToRemindType">
                <ts-radio label="1">无</ts-radio>
                <ts-radio label="2">发起人</ts-radio>
                <ts-radio label="3">指定环节参与者</ts-radio>
              </ts-radio-group>
            </ts-form-item>
            <ts-form-item
              prop="stepReceiver"
              label="指定节点"
              v-if="form.transferredToRemindType == '3'"
            >
              <ts-select v-model="form.stepReceiver" style="width: 100%;">
                <ts-option
                  v-for="(item, index) in nodeList"
                  :key="index"
                  :label="item.text.value"
                  :value="item.id"
                ></ts-option>
              </ts-select>
            </ts-form-item>
            <ts-form-item
              prop="smsMsgTemp"
              label="通知模板"
              v-if="form.transferredToRemindType == '3'"
            >
              <ts-input
                type="textarea"
                class="textarea"
                @change="$forceUpdate"
                v-model="form.smsMsgTemp"
              />
            </ts-form-item>
          </ts-form>
        </ts-col>
        <ts-col :span="6" v-if="form.transferredToRemindType == '3'">
          <div class="fieldList">
            <el-scrollbar
              style="height: 100%;"
              wrap-style="overflow-x: hidden;"
            >
              <h1>表单字段</h1>
              <p
                v-for="(item, index) in fieldList.filter(
                  e => e.variableType == 2
                )"
                :key="index"
                @click="addFocus(item)"
              >
                {{ item.variableName }}
              </p>
              <h1>流程字段</h1>
              <p
                v-for="(item, index) in fieldList.filter(
                  e => e.variableType == 1
                )"
                :key="index"
                @click="addFocus(item)"
              >
                {{ item.variableName }}
              </p>
            </el-scrollbar>
          </div>
        </ts-col>
      </ts-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit"
        >保 存</ts-button
      >
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      title: '其他通知设置',
      form: {},
      fieldList: [],
      workFlow: {},
      lf: null,
      workFlowContent: {},
      nodeData: {}
    };
  },
  computed: {
    nodeList() {
      let list = this.workFlowContent.nodes.filter(
        e => e.id != this.nodeData.id
      );
      return list;
    }
  },
  methods: {
    open(workFlow, lf, nodeData) {
      this.workFlow = workFlow;
      this.nodeData = nodeData;
      this.lf = lf;
      this.form = deepClone(nodeData.properties);
      this.form.msgtype = '3';
      // this.form.transferredToRemindType =
      //   this.form.transferredToRemindType || '1';
      this.workFlowContent = this.lf.getGraphData();
      this.getPubVarAndFormFields();
      this.visible = true;
    },
    async getPubVarAndFormFields() {
      let param = {
        formId: '',
        isDeleted: 'N'
      };
      let res = await this.ajax.getPubVarAndFormFields(param);
      if (res.success) {
        this.fieldList = res.object || [];
      }
    },
    addFocus(item) {
      this.form.smsMsgTemp =
        (this.form.smsMsgTemp || '') + `{${item.variableName}}`;
      this.$forceUpdate();
    },
    submit() {
      let data = deepClone(this.form);
      data.transferredToRemindType = data.transferredToRemindType || '1';
      if (data.transferredToRemindType != '3') {
        data.smsMsgTemp = '';
        data.stepReceiver = '';
        data.stepReceiverName = '';
      } else {
        let nodeData = this.nodeList.find(e => e.id == data.stepReceiver);
        data.stepReceiverName = nodeData.text.value;
      }
      this.$props.lf.setProperties(this.nodeData.id, {
        ...data
      });
      this.$emit('ok', this.form);
      this.close();
    },
    close() {
      this.visible = false;
      this.form = {};
      this.workFlow = {};
      this.nodeData = {};
      this.lf = null;
      this.workFlowContent = {};
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .textarea textarea {
    min-height: 120px !important;
    max-height: 120px !important;
  }
  .formTable {
    padding-right: 10px;
    border-right: 1px solid #e4e4e4;
  }
  .fieldList {
    padding-left: 10px;
    height: 260px;
    h1 {
      font-size: 14px;
      font-weight: bold;
    }
    p {
      margin: 0;
      line-height: 24px;
      cursor: pointer;
      &:hover {
        color: #5460ff;
      }
    }
  }
}
</style>
