import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      filedTypeList: [
        {
          label: '单行文本',
          value: 'input'
        },
        {
          label: '多行文本',
          value: 'textarea'
        },
        {
          label: '数值',
          value: 'number'
        },
        {
          label: '时间',
          value: 'date'
        },
        {
          label: '单选框',
          value: 'radio'
        },
        {
          label: '复选框',
          value: 'checkbox'
        },
        {
          label: '下拉单选',
          value: 'select'
        },
        {
          label: '附件',
          value: 'file'
        },
        {
          label: '审批意见',
          value: 'comment'
        },
        {
          label: '签名',
          value: 'signature'
        },
        {
          label: '流水号',
          value: 'serialNumber'
        },
        {
          label: '人员选择',
          value: 'personChose'
        },
        {
          label: '部门选择',
          value: 'deptChose'
        },
        {
          label: '流程选择',
          value: 'processChoose'
        },
        {
          label: 'hrp超链接',
          value: 'hrpHyperlink'
        },
        {
          label: '附件模板',
          value: 'fileTemplate'
        },
        {
          label: '备注',
          value: 'remark'
        },
        {
          label: '子表单',
          value: 'childForm'
        },
        {
          label: '子表单汇总',
          value: 'childFormCount'
        },
        {
          label: '工单设置',
          value: 'workorderSetting'
        },
        {
          label: '查询病历文书',
          value: 'interworkCom'
        },
        {
          label: '查询病人信息',
          value: 'interworkSick'
        },
        {
          label: '取消结算',
          value: 'interworkSettle'
        },
        {
          label: '取消预交金',
          value: 'interworkPay'
        },
        {
          label: '医嘱费用明细',
          value: 'interworkHosPro'
        },
        {
          label: '检验医嘱',
          value: 'interworkTest'
        },
        {
          label: '查询医嘱耗材信息',
          value: 'inPatientOrder'
        },
        {
          label: '基本药品字典',
          value: 'essentialDrugDic'
        },
        {
          label: '药品采购信息',
          value: 'medicalSupplieDic'
        },
        {
          label: '查询病人住院信息',
          value: 'interworkHosInfo'
        },
        {
          label: '请假统计',
          value: 'leaveStatistics'
        }
      ],
      sourceTypeList: [
        {
          label: '手动录入',
          value: '1'
        },
        {
          label: '常用字段',
          value: '2'
        },
        {
          label: '公式计算',
          value: '3'
        },
        {
          label: '数据字典',
          value: '4'
        },
        {
          label: '外部来源',
          value: '5'
        },
        {
          label: '关联流程',
          value: '6'
        },
        {
          label: '接口服务',
          value: '7'
        },
        {
          label: '处理科室',
          value: '8'
        },
        {
          label: '业务类型',
          value: '9'
        },
        {
          label: '系统配置',
          value: '10'
        },
        {
          label: '子表单类型',
          value: '11'
        },
        {
          label: '子表单字段类型',
          value: '12'
        }
      ],
      sourceFieldList: [
        {
          label: '常用字段',
          options: [
            {
              label: '当前日期',
              value: 'nowDate'
            },
            {
              label: '转正日期',
              value: 'positiveTime'
            }
          ]
        },
        {
          label: '登录人信息',
          options: [
            {
              label: '登录人姓名',
              value: 'loginName'
            },
            {
              label: '登录人工号',
              value: 'loginCode'
            },
            {
              label: '登录人所属机构',
              value: 'loginOrg'
            },
            {
              label: '登录人部门',
              value: 'loginDept'
            },
            {
              label: '登录人电话',
              value: 'loginPhone'
            },
            {
              label: '登录人邮箱',
              value: 'loginEmail'
            },
            {
              label: '登录人身份证',
              value: 'loginIDCard'
            },
            {
              label: '登录人出生日期',
              value: 'loginBirth'
            },
            {
              label: '登录人年龄',
              value: 'loginAge'
            },
            {
              label: '登录人性别',
              value: 'loginSex'
            },
            {
              label: '登录人岗位',
              value: 'loginPost'
            },
            {
              label: '登录人岗位类型',
              value: 'loginPostType'
            },
            {
              label: '登录人职务',
              value: 'loginDuty'
            },
            {
              label: '登录人职称',
              value: 'loginPosttitle'
            },
            {
              label: '登录人入职日期',
              value: 'loginEntryDate'
            },
            {
              label: '登录人车牌号',
              value: 'loginCarno'
            },
            {
              label: '登录人人员类别',
              value: 'loginOrgAttributes'
            },
            {
              label: '登录人岗位属性',
              value: 'loginJob'
            }
          ]
        },
        {
          label: '考勤信息',
          options: [
            {
              label: '年假天数(天)',
              value: 'annualLeave'
            },
            {
              label: '已休年假(天)',
              value: 'annualLeave_h'
            }
          ]
        }
      ],
      decimalDigitList: [
        {
          label: '无限制',
          value: ''
        },
        {
          label: '0',
          value: '0'
        },
        {
          label: '1',
          value: '1'
        },
        {
          label: '2',
          value: '2'
        },
        {
          label: '3',
          value: '3'
        },
        {
          label: '4',
          value: '4'
        }
      ],
      dateFormatList: [
        {
          label: '年',
          value: 'yyyy'
        },
        {
          label: '年-月',
          value: 'yyyy-MM'
        },
        {
          label: '年-月-日',
          value: 'yyyy-MM-dd'
        },
        {
          label: '年-月-日 时:分',
          value: 'yyyy-MM-dd HH:mm'
        },
        {
          label: '年-月-日 时:分:秒',
          value: 'yyyy-MM-dd HH:mm:ss'
        }
      ],
      // 日期类型
      CommentDateFormatList: [
        {
          label: '年-月-日',
          value: 'yyyy-MM-dd'
        },
        {
          label: '年-月-日 时:分',
          value: 'yyyy-MM-dd HH:mm'
        },
        {
          label: '年-月-日 时:分:秒',
          value: 'yyyy-MM-dd HH:mm:ss'
        }
      ],
      // 接口服务
      interfaceServicesList: [
        {
          label: '科室列表',
          value: '/ts-worksheet/workSheet/itemMeauList',
          show: 'select'
        },
        {
          label: '工单院区列表',
          value: '/ts-worksheet/hospitalDistrictEnableList',
          show: 'select'
        },
        {
          label: 'HIS2.0临床数据接口',
          value: 'his',
          show: 'interworkCom'
        }
      ],
      // 数据格式 或 显示样式 类型判断
      formTab1CheckType: [
        'number',
        'date',
        'serialNumber',
        'radio',
        'checkbox',
        'comment',
        'remark'
      ],
      // 数据来源类型匹配
      fieldDataSourceMap: {
        input: [0, 1, 2],
        textarea: [0, 1, 2],
        number: [0, 2],
        date: [0, 1],
        radio: [0],
        checkbox: [0],
        select: [0, 3, 5, 6],
        file: [0],
        comment: [0],
        signature: [1],
        serialNumber: [0],
        personChose: [0],
        deptChose: [0],
        processChoose: [5],
        hrpHyperlink: [0],
        fileTemplate: [9],
        remark: [9],
        interworkCom: [4],
        interworkSick: [0],
        interworkSettle: [0],
        interworkPay: [0],
        interworkHosPro: [0],
        interworkTest: [0],
        workorderSetting: [7, 8],
        childForm: [10],
        childFormCount: [11],
        essentialDrugDic: [0],
        medicalSupplieDic: [0],
        leaveStatistics: [0]
      },
      // 默认值排除类型
      defaultValueCheckType: [
        'file',
        'comment',
        'signature',
        'serialNumber',
        'personChose',
        'deptChose',
        'processChoose',
        'hrpHyperlink',
        'fileTemplate',
        'remark',
        'interworkCom',
        'interworkSick',
        'interworkSettle',
        'interworkPay',
        'interworkHosPro',
        'interworkTest',
        'workorderSetting',
        'childForm',
        'childFormCount',
        'essentialDrugDic',
        'medicalSupplieDic',
        'leaveStatistics'
      ],
      // 子表单字段数据
      childFormFieldList: [],
      // 历史字段数据
      hisFieldList: [],
      // 流程列表数据
      workFlowList: [],
      deptTreeData: [],
      defaultExpandedKeys: [],
      // 关联流程字段列表
      workFlowFieldsList: [],
      // 表单字段默认
      formDefaultItem: {
        fieldType: 'input',
        showName: '',
        promptText: '',
        isMust: '',
        isReadonly: '',
        length: 100,
        decimalDigit: '',
        treatmentMethod: '',
        isThousandth: '',
        isMakeBigger: '',
        max: '',
        min: '',
        dataFormat: 'yyyy-MM-dd',
        dataSource: '1',
        sourceField: '',
        defaultValue: '',
        optionValue: '',
        calculationRole: '',
        fileTemplate: '',
        remark: '',
        isRed: '',
        isBold: '',
        wrap: '',
        isHideCommentFile: '',
        isMustCommentFile: '',
        hideApprovalTime: '',
        approvalTimeFormat: 'yyyy-MM-dd HH:mm:ss',
        serialNumberRule: '',
        serialNumberRoleList: [],
        associationMap: [],
        relationWorkflowId: '',
        relationWorkflowName: '',
        tableName: '',
        sumField: '',
        tableId: '',
        interfaceServices: ''
      },
      // 关联字段集合
      associationFormFieldList: [],
      // 表单字段集合
      wordConfigList: {},
      // 默认填充表单
      contentDefault:
        '<table style="border-collapse: collapse; width: 100%;" border="1" data-mce-selected="1"><tbody><tr><td style="width: 49.2163%;"><br></td><td style="width: 49.2693%;"><br></td></tr><tr><td style="width: 49.2163%;"><br></td><td style="width: 49.2693%;"><br data-mce-bogus="1"></td></tr></tbody></table>'
    };
  },
  mounted() {
    this.getWorkflowList();
  },
  computed: {
    sourceFilterList() {
      let list = [];
      let indexList = this.fieldDataSourceMap[this.formItem.fieldType];
      indexList.forEach(i => {
        list.push(this.sourceTypeList[i]);
      });
      if (!this.formItem.keyId) {
        this.formItem.dataSource = list[0].value;
      }
      return list;
    },
    toFieldList() {
      let list = [];
      for (let key in this.wordConfigList) {
        if (this.formItem.keyId != this.wordConfigList[key].keyId) {
          list.push(this.wordConfigList[key]);
        }
      }
      return list;
    }
  },
  methods: {
    // 初始化数据
    initForm() {
      this.activeTab = '1';
      if (this.form.id) {
        this.form.toaFieldSetList.forEach(item => {
          this.wordConfigList[item.keyId] = item;
        });
        this.form.javascriptText = this.form.javascriptText || '';
        this.$nextTick(() => {
          this.$refs.tinymc.setData(this.form.content);
          this.$refs.codeMirror.setData(this.form.javascriptText);
        });
      }
    },
    // 获取历史字段
    async findTableFieldByTemplateId() {
      if (this.form.id) {
        let res = await this.ajax.findTableFieldByTemplateId(this.form.id);
        if (res.success) {
          this.hisFieldList = res.object || [];
        }
      }
    },
    // 格式化流水号设置文本
    getserialNumberRuleText(item) {
      if (item.serialNumberRoleList.length) {
        let list = item.serialNumberRoleList.map(e => {
          if (e.ruleType == '3') {
            return `[${e.serialVal}位流水号]`;
          }
          if (e.ruleType == '1') {
            return `[${e.serialVal}]`;
          }
          if (e.ruleType == '2') {
            return `[${
              e.serialVal == 'yyyyMMdd'
                ? '年月日'
                : e.serialVal == 'yyyyMM'
                ? '年月'
                : e.serialVal == 'yyyy'
                ? '年'
                : ''
            }]`;
          }
        });
        return list.join().replaceAll(',', '');
      } else {
        return '设置';
      }
    },
    // 分类树
    async getTree() {
      const tree = await this.ajax.getClassifyTree({ classifyType: 1 });
      if (!tree.success) {
        this.$message.error(res.message || '获取出错');
      }
      this.deptTreeData = tree.object || [];
      this.defaultExpandedKeys = [this.deptTreeData[0].id];
    },
    // 分类树选择回调
    treeSelect(val, node, tree) {
      this.form.classifyName = node.data.name;
    },
    // 数据字典列表
    async getDictTypeList(data) {
      let res = await this.ajax.getDictTypeList({
        ...data,
        pageSize: 15,
        sidx: 'CREATE_DATE',
        sord: 'desc'
      });
      if (res.success == false) {
        this.$message.error(res.message || '数据字典数据获取失败');
        return false;
      }
      return res.rows;
    },
    // 流程列表
    async getWorkflowList() {
      let res = await this.ajax.getWorkflowList({
        pageNo: 1,
        pageSize: 10000
      });
      if (res.success == false) {
        this.$message.error(res.message || '流程数据获取失败');
        return false;
      }
      this.workFlowList = res.rows || [];
    },
    // 子表单
    async getDpTableList(data) {
      let res = await this.ajax.getDpTableList({
        ...data,
        pageSize: 15
      });
      if (res.success == false) {
        this.$message.error(res.message || '子表单数据获取失败');
        return false;
      }
      return res.rows;
    },
    // 子表单选择回调
    handleChildFormSelect(flag = true) {
      if (this.formItem.fieldType == 'childFormCount') {
        flag && (this.formItem.sumField = []);
        this.ajax.getChildHeadList(this.formItem.tableId).then(res => {
          if (res.success) {
            this.childFormFieldList = res.object || [];
          }
        });
      }
    },
    // 初始化表单字段设置数据
    initFormItem() {
      this.activeTab = '1';
      this.formItem = deepClone(this.formDefaultItem);
      this.$nextTick(() => {
        this.$refs.formTab1 && this.$refs.formTab1.clearValidate();
        this.$refs.formTab2 && this.$refs.formTab2.clearValidate();
      });
    },
    // 已有表单字段设置信息回显回调函数
    initFormItemById(ids) {
      this.activeTab = '1';
      let idList = ids.split(',');
      idList.forEach(id => {
        if (this.wordConfigList[id]) {
          this.formItem = deepClone(this.wordConfigList[id]);
        }
      });
      if (this.formItem.sumField) {
        this.handleChildFormSelect(false);
        this.formItem.sumField = this.formItem.sumField.split(',');
      }
      if (this.formItem.fieldType == 'processChoose') {
        this.formItem.relationWorkflowId = this.formItem.relationWorkflowId.split(
          ','
        );
      }
    },
    // 字段类型更换变更表单
    fieldTypeChange() {
      if (this.formItem.keyId) return;
      if (this.formItem.fieldType == 'number') {
        this.formItem.decimalDigit = '';
      }
      if (this.formItem.fieldType == 'date') {
        this.formItem.dataFormat = 'yyyy-MM-dd';
      }
      if (this.formItem.fieldType == 'signature') {
        this.formItem.dataSource = '2';
        this.formItem.sourceField = 'loginName';
      }
    },
    // 下拉单选-关联流程-流程选择回调
    async setAssociationFieldOption() {
      let workFlowId = this.formItem.relationWorkflowId;
      let res = await this.ajax.findByWorkflowId(workFlowId);
      this.workFlowFieldsList = [];
      if (res && res.object) {
        this.workFlowFieldsList = res.object.toaFieldSetList;
      }
    }
  }
};
