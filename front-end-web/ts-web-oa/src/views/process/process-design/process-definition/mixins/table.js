export default {
  data() {
    return {
      searchForm: {},

      actions: [],
      apiFunction: this.ajax.getClassifyTree,
      searchList: [
        {
          label: '关键字',
          value: 'condition',
          element: 'ts-input',
          elementProp: {
            placeholder: '流程编号，流程名称'
          }
        },
        {
          label: '流程状态',
          value: 'status',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            {
              label: '启用',
              value: '1',
              element: 'ts-option'
            },
            {
              label: '停用',
              value: '2',
              element: 'ts-option'
            }
          ]
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          label: '流程编号',
          prop: 'workflowNo',
          width: 100,
          align: 'left'
        },
        {
          label: '流程名称',
          prop: 'workflowName',
          minWidth: 300,
          align: 'left'
        },
        {
          label: '表单模板',
          prop: 'formName',
          minWidth: 200,
          align: 'left'
        },
        {
          label: '流程类型',
          prop: 'isNormal',
          align: 'left',
          minWidth: 110,
          formatter: row => {
            var data = {
              N: '自定义流程',
              Y: '普通流程'
            };
            return data[row.isNormal] || '';
          }
        },
        {
          label: '创建人',
          prop: 'createUserName',
          width: 100,
          align: 'left'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          minWidth: 170,
          align: 'center'
        },
        {
          label: '更新人',
          prop: 'updateUserName',
          width: 100,
          align: 'left'
        },
        {
          label: '更新时间',
          prop: 'updateDate',
          minWidth: 170,
          align: 'center'
        },
        {
          label: '状态',
          prop: 'status_name',
          width: 100,
          align: 'center',
          formatter: row => {
            if (row.status == 1) {
              return <span style="color:#35dc35">启用</span>;
            } else {
              return <span style="color:red">停用</span>;
            }
          }
        },
        {
          label: '操作',
          prop: '',
          width: 200,
          align: 'center',
          headerSlots: 'action',
          fixed: 'right',
          formatter: row => {
            let arr = [
              {
                label: '流程设计',
                event: this.handleDesign
              },
              {
                label: '编辑',
                event: this.handleEdit
              }
            ];
            if (row.status == 1) {
              arr.push({
                label: '停用',
                event: this.handleStop
              });
            } else {
              arr.push({
                label: '启用',
                event: this.handleStart
              });
            }
            if (row.isNormal == 'N') {
              arr.push({
                label: '删除',
                event: this.handleDelete
              });
            }
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
