<template>
  <div class="components-role">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <template slot="right">
        <ts-button type="primary" @click="handleAdd">
          新增
        </ts-button>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      :defaultSort="{
        prop: 'create_date',
        order: 'desc'
      }"
      @refresh="handleRefreshTable"
    />
    <dialog-role-add ref="dialogRoleAdd" @ok="search" />
    <dialog-role-transfer ref="dialogRoleTransfer" @ok="search" />
  </div>
</template>
<script>
import table from '../mixins/role';
import dialogRoleAdd from './dialog-role-add.vue';
import dialogRoleTransfer from './dialog-role-transfer.vue';
export default {
  components: { dialogRoleAdd, dialogRoleTransfer },
  mixins: [table],
  data() {
    return {
      loading: false
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.search();
    });
  },
  methods: {
    handleAdd() {
      this.$refs.dialogRoleAdd.open();
    },
    // 编辑
    handleEdit(row) {
      this.$refs.dialogRoleAdd.open(row);
    },
    // 授权
    handleEmpower(row) {
      this.$refs.dialogRoleTransfer.open(row.roleId);
    },
    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm(`确定要删除当前选中的数据吗？`, '提示', {
          type: 'warning'
        });
        this.ajax.workflowRoleDel(row.roleId).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '操作失败');
            return;
          }
          this.$message.success(res.message || '操作成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      this.ajax.getWorkFlowroleList(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$message.error(res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.components-role {
  width: 100%;
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
}
</style>
