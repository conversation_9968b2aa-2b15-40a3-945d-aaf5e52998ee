<template>
  <vxe-modal
    width="90%"
    height="90%"
    v-model="visible"
    :append-to-body="true"
    showFooter
    :before-hide-method="close"
    show-zoom
    class="dialog-form-render"
  >
    <template #title>
      <span>{{ title }}</span>
    </template>
    <template #default>
      <div class="content flex-column">
        <el-scrollbar
          ref="scroll"
          style="flex: 1;"
          wrap-style="overflow-x: hidden;"
        >
          <DynamicForm
            :html-template="htmlTemplate"
            :fields="toaFieldSetList"
            ref="formContainer"
          />
        </el-scrollbar>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">提 交</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>
<script>
import templateRender from '../mixins/templateRender';
import DynamicForm from './DynamicForm.vue';
export default {
  name: 'formRender',
  mixins: [templateRender],
  components: {
    DynamicForm
  },
  data() {
    return {
      htmlTemplate: '',
      elements: [],
      content: '',
      printTemplate: '',
      toaFieldSetList: [],
      formId: '',
      formTemplate: '',
      visible: false,
      title: '',
      workflowId: '',
      workflowCode: '',
      workFlow: {},
      formKey: 0
    };
  },
  mounted() {},
  computed: {},
  methods: {
    async open(workFlow) {
      this.workFlow = workFlow;
      this.workflowCode = workFlow.code;
      this.workflowId = workFlow.userData.wfDefinitionId;
      this.formId = workFlow.userData.formId;
      // this.formId = this.getWfInfo(this.workflowCode);
      this.visible = true;
      let obj = await this.getFormInfo(this.formId);
      this.htmlTemplate = obj.content;
      this.toaFieldSetList = obj.toaFieldSetList;
      this.formKey += 1;
      this.title = obj.workflowName;
    },
    updateValue(id, value) {},
    submit() {
      console.log(this.$refs.formContainer.FormData);
    },
    close() {
      this.htmlTemplate = '';
      this.toaFieldSetList = [];
      this.workFlow = {};
      this.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-form-render {
  /deep/ .content {
    height: 100%;
  }
}
</style>
