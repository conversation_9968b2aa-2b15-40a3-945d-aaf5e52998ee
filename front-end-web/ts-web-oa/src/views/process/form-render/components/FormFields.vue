<template>
  <div class="formItem">
    <component
      :key="field.id"
      :is="componentMap[field.fieldType]"
      :field="field"
      :fieldValue.sync="FormData[field.fieldName]"
      style="width: 100%;"
    >
    </component>
  </div>
</template>
<script>
import formInput from '@/components/form-component/form-input.vue';
import formTextArea from '@/components/form-component/form-textArea.vue';
import formDate from '@/components/form-component/form-date.vue';
import formNumber from '@/components/form-component/form-number.vue';
import formRadio from '@/components/form-component/form-radio.vue';
import formSelect from '@/components/form-component/form-select.vue';
import formCheckbox from '@/components/form-component/form-checkbox.vue';
import formFile from '@/components/form-component/form-file.vue';
import formDeptChose from '@/components/form-component/form-deptChose.vue';
export default {
  components: {
    formInput,
    formTextArea,
    formDate,
    formNumber,
    formRadio,
    formSelect,
    formCheckbox,
    formFile,
    formDeptChose
  },
  props: {
    field: {
      type: Object,
      required: false
    },
    FormData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    componentMap() {
      return {
        input: 'formInput',
        textarea: 'formTextArea',
        select: 'formSelect',
        radio: 'formRadio',
        number: 'formNumber',
        date: 'formDate',
        checkbox: 'formCheckbox',
        file: 'formFile',
        deptChose: 'formDeptChose'
      };
    }
  },
  mounted() {},
  methods: {
    parseOptions(optionValue) {
      return optionValue.split(',').map(option => option.trim());
    },
    getRealValue(field) {
      switch (field.sourceField) {
        case 'loginName':
          return '张三';
        case 'loginDept':
          return '信息科';
        case 'nowDate':
          return this.formatDate(new Date());
        default:
          return field.defaultValue;
      }
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // 返回格式化后的日期
    },
    updateValue(value) {
      //
    },
    handleInput(field, value) {
      console.log(`Field ${field.id} changed to:`, value);
      // 这里可以处理每个字段的变化
    },
    handleBlur(field, value) {
      if (field.interfaceServices) {
        console.log(
          '执行方法回调：' +
            field.interfaceServices +
            `Current value: ${event.target.value}`
        );
      }
    }
  }
};
</script>
