<template>
  <el-dialog
    custom-class="initiate_patrol_modal"
    :fullscreen="true"
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
  >
    <div class="initiate_patrol_modal_container" v-loading="print">
      <div class="form">
        <el-scrollbar
          ref="scroll"
          style="height: 100%"
          wrap-class="scroll-wrap"
          view-class="scroll-view"
        >
          <el-form :model="patrolForm" :rules="rules" ref="patrolForm">
            <div class="title">
              <span>
                {{ patrolForm.patrolName
                }}{{
                  patrolForm.startNum ? '(' + patrolForm.startNum + ')' : ''
                }}
              </span>
              <span>
                <img src="@/assets/img/icon_address.png" />
                {{ patrolForm.location }}
              </span>
            </div>
            <div class="column_box">
              <div class="column_box_item">
                巡查人（记录人）: {{ userInfoStr }}
              </div>
              <!-- 巡查日期 -->
              <div class="column_box_item">
                <el-form-item
                  label="巡查日期:"
                  label-width="105px"
                  prop="checkDate"
                  size="mini"
                >
                  <el-date-picker
                    :readonly="readonly"
                    v-model="patrolForm.checkDate"
                    type="date"
                    placeholder="巡查日期"
                    :picker-options="pickerOptions"
                    style="width: 140px"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </div>
              <!-- 责任科室 -->
              <!-- <div class="column_box_item">
                <el-form-item
                  label="责任科室:"
                  prop="responsibilityDeptId"
                  label-width="105px"
                  v-if="visible"
                  size="mini"
                >
                  <input-tree
                    v-model="patrolForm.responsibilityDeptId"
                    placeholder="请选择责任科室"
                    :treeData="deptTreeData"
                    :defaultExpandedKeys="defaultExpandedKeys"
                    style="width: 160px"
                    key="responsibilityDeptId"
                  ></input-tree>
                </el-form-item>
              </div> -->
            </div>
            <el-table
              :data="patrolForm.itemList"
              border
              class="initiate_patrol_table"
            >
              <el-table-column
                type="index"
                width="90"
                align="center"
                label="序号"
              >
              </el-table-column>
              <!-- :show-overflow-tooltip="true" -->
              <el-table-column
                prop="patrolItemName"
                width="285"
                label="巡查内容"
              >
              </el-table-column>
              <el-table-column label="">
                <template slot-scope="scope">
                  <div
                    class="cell-box"
                    v-for="(itemChild, itemChildIdx) in scope.row.itemChildList"
                    :key="itemChildIdx"
                  >
                    <el-form-item
                      size="mini"
                      class="table_form_item"
                      :label="itemChild.itemChildDescribe"
                      :rules="{
                        required: itemChild.itemChildRequired === 'Y',
                        message: '内容不能为空'
                      }"
                      :prop="
                        'itemList.' +
                          scope.$index +
                          '.itemChildList.' +
                          itemChildIdx +
                          '.checkValue'
                      "
                    >
                      <!-- 输入框 -->
                      <!-- {{ itemChild.checkValue }} -->
                      <div>
                        <component
                          :is="itemChild.itemChildType + 'Item'"
                          :readonly="readonly"
                          v-model="itemChild.checkValue"
                          :option="itemChild.itemChildContent"
                        ></component>
                      </div>
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-scrollbar>

        <table
          v-show="print"
          class="table-data"
          id="PrintFormTable"
          style="table-layout: fixed !important"
        >
          <tr>
            <td class="table-title" colspan="6">
              {{ patrolForm.patrolName
              }}{{ patrolForm.startNum ? '(' + patrolForm.startNum + ')' : '' }}
            </td>
          </tr>
          <tr>
            <td>地点坐标</td>
            <td>{{ patrolForm.location }}</td>
            <td>巡查人（记录人）</td>
            <td>{{ userInfoStr }}</td>
            <td>活动日期</td>
            <td>{{ patrolForm.checkDate }}</td>
          </tr>
          <tr class="title-tr">
            <td>序号</td>
            <td>巡查内容</td>
            <td colspan="4"></td>
          </tr>
          <tbody v-for="(item, index) in patrolForm.itemList" :key="index">
            <tr
              class="value-tr"
              v-for="(child, childIndex) in item.itemChildList"
              :key="child.id"
            >
              <td :rowspan="item.itemChildList.length" v-if="childIndex === 0">
                {{ index + 1 }}
              </td>
              <td :rowspan="item.itemChildList.length" v-if="childIndex === 0">
                {{ item.patrolItemName || '' }}
              </td>
              <td colspan="4">
                <div class="child-item">
                  <div class="label">{{ child.itemChildDescribe }}</div>
                  <span v-if="child.itemChildType !== 'FJ'" class="value">
                    {{
                      child.checkValue instanceof Array
                        ? child.checkValue.join(',')
                        : child.checkValue
                        ? child.checkValue
                        : '-'
                    }}
                  </span>
                  <file-item
                    v-if="child.itemChildType === 'FJ' && child.checkValue"
                    :businessId="child.checkValue"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="footer">
        <el-button
          v-if="!readonly && patrolForm.status == 0"
          class="trasen-perpul"
          @click="handleSave(0)"
        >
          存草稿
        </el-button>
        <el-button
          v-if="!readonly"
          class="trasen-perpul"
          @click="handleSave(2)"
        >
          提交
        </el-button>
        <el-button v-if="readonly && type === 'finished'" @click="handlePrint">
          打印
        </el-button>
        <el-button v-if="readonly && type === 'finished'" @click="handleExport">
          导出
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Print from 'print-js';
import FileItem from '@/views/political/political-management/components/file-item.vue';

import InputTree from '@/components/input-tree/index.vue';
import SRKItem from './SRKItem.vue';
import DXItem from './DXItem.vue';
import FXItem from './FXItem.vue';
import FJItem from './FJItem.vue';
import TPItem from './TPItem.vue';
import moment from 'moment';
export default {
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  components: { InputTree, SRKItem, DXItem, FXItem, FJItem, TPItem, FileItem },
  data() {
    return {
      title: '',
      visible: false,
      print: false,
      userInfoStr: '',
      patrolForm: {
        patrolId: '',
        checkDate: '',
        responsibilityDeptId: ''
      },
      rules: {
        checkDate: [{ required: true, message: '巡查日期不能为空' }],
        responsibilityDeptId: [{ required: true, message: '责任科室不能为空' }]
      },
      //时间选择配置
      pickerOptions: {
        disabledDate: this.computePickRange
      },
      deptTreeData: [],
      defaultExpandedKeys: [],
      conf: {},
      readonly: false
    };
  },
  methods: {
    handlePrint() {
      this.print = true;
      setTimeout(() => {
        Print({
          printable: 'PrintFormTable',
          type: 'html',
          header: '',
          targetStyles: ['*'],
          style: '@page {margin:0 10mm};',
          ignoreElements: [],
          properties: null
        });
        this.print = false;
      }, 1000);
    },
    handleExport() {
      let aDom = document.createElement('a');
      aDom.href = `/ts-oa/api/patrolStart/xjxxExport/${this.conf.id}`;
      aDom.click();
    },
    computePickRange(v) {
      return v.getTime() < new Date().getTime() - 86400000;
    },
    async getTreeHandle() {
      try {
        const tree = await this.ajax.getTree();
        if (!tree.success) {
          throw tree.message;
        }
        this.deptTreeData = tree.object || [];
        this.defaultExpandedKeys = [this.deptTreeData[0].id];
      } catch (e) {
        this.$message.error(tree.message || '出错啦');
      }
    },
    async open(row) {
      let _row = {
        type: '',
        id: '',
        patrolId: ''
      };

      _row = Object.assign(_row, row);
      this.conf = _row;
      this.title = _row.type === 'ADD' ? '发起巡查' : '巡查信息';
      this.patrolForm.patrolId = _row.patrolId;

      let patrolDetails;
      try {
        if (_row.type === 'EDIT') {
          this.patrolForm.id = _row.id;
          patrolDetails = await this.ajax.getPatrolStartDetails(_row.id);
        } else {
          patrolDetails = await this.ajax.getpatrolDetails(_row.patrolId);
        }
        this.patrolForm = Object.assign(this.patrolForm, patrolDetails.object);
        this.patrolForm.checkDate =
          this.patrolForm.checkDate || moment().format('YYYY-MM-DD');
        if (
          _row.type === 'ADD' ||
          (_row.type === 'EDIT' && !this.patrolForm.checkUserName)
        ) {
          const userInfo = this.$store.state.common.userInfo;
          this.userInfoStr = `${userInfo.orgName}-${userInfo.employeeName}${
            userInfo.empPhoneSecond ? '(' + userInfo.empPhoneSecond + ')' : ''
          }`;
        } else {
          this.userInfoStr = `${this.patrolForm.orgName}-${
            this.patrolForm.checkUserName
          }${
            this.patrolForm.checkUserPhone
              ? '(' + this.patrolForm.checkUserPhone + ')'
              : ''
          }`;
        }
        // 映射字段ID
        for (const i in this.patrolForm.itemList) {
          const item = this.patrolForm.itemList[i];
          for (const j in item.itemChildList) {
            const itemChild = item.itemChildList[j];
            itemChild.itemChildId = itemChild.itemChildId || itemChild.id;
            if (itemChild.itemChildType === 'FX' && itemChild.checkValue) {
              itemChild.checkValue = itemChild.checkValue.split(',');
            }
          }
        }
      } catch (error) {}
      this.visible = true;
    },
    handleSave(status) {
      this.$refs.patrolForm.validate(async valid => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.patrolForm));
          for (const i in params.itemList) {
            const item = params.itemList[i];
            for (const j in item.itemChildList) {
              const itemChild = item.itemChildList[j];

              if (itemChild.itemChildType === 'FX' && itemChild.checkValue) {
                itemChild.checkValue = itemChild.checkValue.join(',');
              }
            }
          }
          if (params.status != 3) {
            params.status = status;
          }
          try {
            if (this.conf.type === 'ADD') {
              if (params.id) delete params.id;

              await this.ajax.savePatrolStart(params);
            } else {
              await this.ajax.updatePatrolStart(params);
            }
            this.$refs.patrolForm.resetFields();
            this.patrolForm = this.$options.data().patrolForm;
            this.readonly = false;
            this.visible = false;
            this.$emit('ok');
          } catch (error) {}
        } else {
          return false;
        }
      });
    },
    handleCancel() {
      this.$refs.patrolForm.resetFields();
      this.patrolForm = this.$options.data().patrolForm;
      this.readonly = false;
      this.visible = false;
    },
    handleClose(done) {
      this.$refs.patrolForm.resetFields();
      this.patrolForm = this.$options.data().patrolForm;
      this.readonly = false;
      done();
    }
  },
  created() {
    this.getTreeHandle();
  }
};
</script>
<style lang="scss" scoped>
/deep/.initiate_patrol_modal {
  background: #f4f4f4;
  .el-dialog__header {
    background: white;
    margin: 0 !important;
    padding: 8px 16px !important;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }
  .el-dialog__body {
    padding: 0;
    margin: 0;
  }
  .initiate_patrol_modal_container {
    height: 100%;
    background: #f4f4f4;
    display: flex;
    flex-direction: column;
    .form {
      background: white;
      max-height: calc(100% - 46px);
      overflow-y: auto;
      width: 1190px;
      margin: 0 auto;
      padding: 16px 24px;
      .scroll-wrap {
        height: calc(100% + 17px) !important;
      }
      .title {
        display: flex;
        align-items: flex-end;
        margin-bottom: 16px;
        & > span:first-child {
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          margin-right: 24px;
        }
        & > span:last-child {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #333333;
          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
      }
      .column_box {
        display: flex;
        align-items: center;
        .column_box_item:first-child {
          font-size: 14px;
          color: #333333;
          padding-bottom: 8px;
        }
      }
    }
    .footer {
      height: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.table-data {
  width: 90%;
  border-spacing: 0;
  border-collapse: collapse;

  h1 {
    font-size: 1.6em;
    text-align: center;
    margin: 8px 0;
  }

  tr {
    width: 100%;

    > td {
      height: 40px;
      border: 1px solid #cecece;
      text-align: center;
      &:nth-child(2n-1) {
        color: #000;
        line-height: 1;
      }

      &:nth-child(2n) {
        padding: 8px;
        text-align: center;
      }

      .child-item {
        display: flex;
        align-items: center;
        min-height: 30px;
        padding-left: 12px;

        .label {
          width: 150px;
          color: rgb(96, 98, 102);
          text-align: left;
        }

        .value {
          flex: 1;
          text-align: left;
        }
      }
    }

    &.title-tr {
      > td {
        color: #000;
        text-align: center;
      }
    }

    &.value-tr {
      > td {
        color: rgb(96, 98, 102);
        text-align: center;
      }
    }

    .table-title {
      font-weight: 700;
      letter-spacing: 3px;
      font-size: 18px;
      text-align: center;
    }
  }
}

.table-data tr > td {
  line-height: 16px !important;
}
</style>
<style>
.initiate_patrol_modal .initiate_patrol_table .el-form-item__label {
  padding-bottom: 0 !important;
}
/* .initiate_patrol_modal .initiate_patrol_table .table_form_item.el-form-item {
  margin-bottom: 0px !important;
} */
.initiate_patrol_modal .initiate_patrol_table .cell-box {
  position: relative;
  padding-bottom: 2px;
}
.initiate_patrol_modal .initiate_patrol_table .cell-box::after {
  position: absolute;
  content: '';
  bottom: 0;
  height: 1px;
  background: rgb(235, 238, 245);
  left: -10px;
  right: -10px;
}
.initiate_patrol_modal .initiate_patrol_table .cell-box:last-child::after {
  height: 0;
}
.table_form_item .el-form-item__label {
  display: block;
  width: 100%;
  text-align: left !important;
}
.table_form_item .el-form-item__content {
  display: block;
  width: 100%;
}
</style>
