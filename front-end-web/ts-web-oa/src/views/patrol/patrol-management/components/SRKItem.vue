<template>
  <el-input size="medium" v-model="inputVal" :readonly="readonly"></el-input>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputVal: this.value
    };
  },
  watch: {
    value: {
      handler(newValue) {
        this.inputVal = newValue;
      },
      deep: true,
      immediate: true
    },
    inputVal(newValue) {
      if (!newValue) this.inputVal = '';
      this.$emit('input', newValue);
    }
  }
};
</script>

<style></style>
