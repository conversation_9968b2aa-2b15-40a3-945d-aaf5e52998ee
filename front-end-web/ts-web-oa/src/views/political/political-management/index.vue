<template>
  <div class="content flex">
    <div class="left-tree-content bg_white flex-column">
      <div class="flex">
        <el-input
          size="medium"
          v-model="politicalTreeSearchInput"
          placeholder="请输入名称"
        ></el-input>
        <!-- <el-button class="trasen-perpul" @click="serchPoliticalTree">
          搜索
        </el-button> -->
      </div>
      <div class="political-type-list flex-grow">
        <el-scrollbar
          ref="politicalTypeScroll"
          style="height: 100%; width: 100%;"
          wrap-class="left-scroll-wrap"
          view-class="left-scroll-view"
        >
          <el-tree
            node-key="id"
            ref="politicalTypeTreeRef"
            :data="politicalTypeList"
            :props="defaultProps"
            highlight-current
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            accordion
            default-expand-all
            @node-click="nodeClick"
          ></el-tree>
        </el-scrollbar>
      </div>
    </div>
    <div class="table-content bg_white flex-grow">
      <!-- <div class="red-toast">
        活动发起4期，缺失1期，下次活动2021-11-27，责任人张爱丽
      </div> -->
      <el-tabs
        v-model="queryParam.status"
        @tab-click="handleTabClick"
        type="card"
        class="table-tabs"
      >
        <el-tab-pane
          v-for="pane in tabConf"
          :key="pane.name"
          :label="pane.label"
          :name="pane.name"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="table-content-container">
        <management-table
          :type="active.ref"
          :ref="active.ref"
          :key="active.name"
          :fkPolitical="fkPolitical"
          :options="politicalStartList"
          :ipagination="ipagination"
          @initiate="handleInitiate"
        />
      </div>
    </div>
    <!-- 发起活动 -->
    <initiate-political
      ref="initiatePolitical"
      @ok="loadData(1)"
      :type="active.ref"
    ></initiate-political>
  </div>
</template>

<script>
import ManagementTable from './components/management-table.vue';
import InitiatePolitical from './components/initiate-political.vue';
export default {
  components: {
    ManagementTable,
    InitiatePolitical
  },
  data() {
    return {
      politicalTreeSearchInput: '',
      draftCount: 0,
      toBeInspectedCount: 0,
      politicalTypeList: [],
      activePoliticalType: {},
      filterResult: {},
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tabConf: [
        { label: '草稿', name: '0', ref: 'draft' },
        { label: '待组织', name: '1', ref: 'inspect' },
        // { label: '待确认', name: '2', ref: 'toBeConfirmed' },
        // { label: '不通过', name: '3', ref: 'noPass' },
        // { label: '整改中', name: '4', ref: 'zhenggai' },
        { label: '已完成', name: '2', ref: 'finished' },
        { label: '全部', name: '3', ref: 'all' }
      ],
      politicalStartList: [],
      queryParam: {
        status: '1',
        fkPoliticalTypeId: ''
      },
      ipagination: {
        current: 1,
        pageSize: 100,
        pageSizeOptions: [20, 40, 60, 80, 100, 200, 500, 1000, 2000],
        total: 0
      },
      fkPolitical: {}
    };
  },
  methods: {
    // 获取列表数据
    async loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      try {
        let params = JSON.parse(JSON.stringify(this.queryParam));
        params.pageSize = this.ipagination.pageSize;
        params.pageNo = this.ipagination.current;
        if (params.status == '3') {
          delete params.status;
        }
        if (this.politicalTypeList.length === 0) {
          params.fkPoliticalTypeId = '-1';
        }
        this.$nextTick(async () => {
          let children = JSON.parse(
            JSON.stringify(this.$refs[this.active.ref].queryParam)
          );
          params = Object.assign(params, children);
          const res = await this.ajax.getPoliticalStartList(params);

          this.politicalStartList = res.rows || [];
          this.ipagination.total = res.totalCount;
        });
      } catch (error) {}
    },
    refresh() {
      this.getPoliticalTypeList();
    },
    serchPoliticalTree() {
      this.getPoliticalTypeList();
    },
    handleTabClick() {
      this.loadData(1);
    },
    getPoliticalTypeList(arg) {
      let _arg = {
        selected: false,
        list: false
      };
      _arg = Object.assign(_arg, arg);
      this.ajax.politicalListTree().then(res => {
        if (res.success) {
          this.politicalTypeList = res.object || [];
          if (_arg.selected && this.politicalTypeList.length > 0) {
            this.queryParam.fkPoliticalTypeId = this.politicalTypeList[0].id;
            this.fkPolitical = this.politicalTypeList[0];
          }
          this.$nextTick(() => {
            this.$refs.politicalTypeTreeRef.setCurrentKey(this.fkPolitical.id);
          });
          this.$refs.politicalTypeScroll.update();
          if (_arg.list) {
            this.loadData(1);
          }
        }
      });
    },
    handleChoosePoliticalType(political = {}) {
      if (this.activePoliticalType.id != political.id) {
        this.activePoliticalType = political;
      }
    },
    nodeClick(data) {
      this.fkPolitical = data;
      this.queryParam.fkPoliticalTypeId = data.id;
      this.loadData(1);
    },
    handleInitiate() {
      this.$refs.initiatePolitical.open({
        type: 'ADD',
        politicalId: this.fkPolitical.id
      });
    },
    // 打开编辑页
    editPolitical(row) {
      this.$refs.initiatePolitical.readonly = false;
      this.$refs.initiatePolitical.open({
        type: 'EDIT',
        id: row.id,
        politicalId: row.id
      });
    },
    // 打开详情页
    detailsPolitical(row) {
      this.$refs.initiatePolitical.readonly = true;
      this.$refs.initiatePolitical.open({
        type: 'EDIT',
        id: row.id,
        politicalId: row.id
      });
    },
    handleSizeChange(val) {
      this.ipagination.pageSize = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.ipagination.current = val;
      this.loadData();
    },
    handleDelete(row) {
      const vm = this;
      this.$confirm(`确定删除该记录？`).then(() => {
        this.ajax.deletePoliticalStart(row.id).then(res => {
          vm.loadData();
        });
      });
    },
    filterNode(value, data) {
      if (!value) {
        this.$set(this, 'filterComputed', {});
        return true;
      }

      // 搜索查询到父级 子级不进行过滤查询 直接返回
      let filterResult = data.name.indexOf(value) !== -1;
      if (filterResult && data.parent) this.filterComputed[data.id] = true;

      if (this.filterComputed[data.pid] && !data.parent) {
        filterResult = true;
      }
      return filterResult;
    }
  },
  computed: {
    active() {
      return this.tabConf.find(e => {
        return e.name == this.queryParam.status;
      });
    }
  },
  created() {
    this.getPoliticalTypeList({ selected: true, list: true });
  },
  watch: {
    politicalTreeSearchInput(val) {
      this.$refs.politicalTypeTreeRef.filter(val);
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: calc(100% - 8px);
  margin-bottom: 8px;
  > .bg_white {
    padding: 8px;
    border-radius: 8px;
    background: #fff;
    &:not(:last-child) {
      margin-right: 8px;
    }
  }
}
.left-tree-content {
  flex-shrink: 0;
  width: 220px;
}
.table-content {
  overflow: hidden;
  position: relative;
  height: 100%;
  .table-content-container {
    height: calc(100% - 44px);
  }
}
.red-toast {
  position: absolute;
  right: 0;
}
/deep/ {
  .table-tabs {
    // height: 100%;
    .el-tabs__content {
      height: calc(100% - 48px);
      > div {
        height: 100%;
      }
    }
  }
  .tab-item {
    position: relative;
    height: 100%;
  }
}
.political-type-list {
  margin-top: 8px;
  overflow: hidden;
}
.political-type-item {
  padding-right: 10px;
  line-height: 26px;
  cursor: pointer;

  .political-type-action {
    margin-left: 8px;
  }
  &:hover {
    background: $theme-color-8;
  }
}
.active-political-type {
  color: $theme-color;
  font-weight: 600;
}
/deep/ {
  .left-scroll-wrap {
    height: calc(100% + 17px);
  }
}
</style>
