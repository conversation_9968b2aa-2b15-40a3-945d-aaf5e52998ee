<template>
  <ts-dialog
    :visible.sync="visible"
    type="fullscreen"
    custom-class="vechicle-dialog"
  >
    <template slot="title">
      <div class="flex-col-center">
        <div
          class="dialog-icon"
          :class="{
            'is-done-flow-icon': detail.status == 1
          }"
        >
          {{ detail.applyStatus == 5 ? '流程完结' : '流程在办' }}
        </div>
        <div>
          <p class="dialog-title">{{ title }}</p>
          <div>
            发起人：{{ detail.createUserName }}&emsp;当前节点：{{
              detail.wfStepName
            }}
          </div>
        </div>
      </div>
    </template>
    <div class="status-container">
      <ul class="status-list">
        <li
          v-for="item of sectionList"
          :key="item.name"
          :class="{
            active: activeTab == item.name
          }"
          @click="handlePreviewDetail(item)"
          class="status-item"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <div class="detail-container flex">
      <el-scrollbar
        ref="scroll"
        style="flex: 1;"
        wrap-style="height: calc(100% + 17px);"
      >
        <vechicle-basic-detail
          ref="basicDetail"
          :vehicleData="vehicleInfo"
          :detail="detail"
        />
        <flow-detail
          v-if="visible && detail.workflowNo"
          ref="flowDetail"
          :data="detail"
        />
        <dispatch-detail
          ref="dispatchDetail"
          v-if="detail.applyStatus >= 3"
          :data="detail"
        />
        <departure-inform
          ref="departureInform"
          v-if="detail.applyStatus >= 4 && detail.isDispatch == 1"
          :data="detail"
        />
        <return-inform
          ref="returnInform"
          v-if="detail.applyStatus >= 5 && detail.isDispatch == 1"
          :data="detail"
        />
        <!-- <related-files ref="relatedFiles" :data="detail" /> -->
      </el-scrollbar>
    </div>
    <template slot="footer">
      <slot name="footer">
        <ts-button @click="close">关闭</ts-button>
      </slot>
    </template>
  </ts-dialog>
</template>

<script>
import vechicleBasicDetail from './vechicle-basic-detail.vue'; // 基础信息
import flowDetail from './flow-detail.vue'; // 流程信息
import dispatchDetail from './dispatch-detail.vue'; // 派车信息
import departureInform from './departure-inform.vue'; // 出车信息
import returnInform from './return-inform.vue'; // 还车信息
// import relatedFiles from './related-files.vue'; // 相关附件

import { vehicleInfoSeeDetails } from '@/api/ajax/vehicle/vehicle-information';

export default {
  components: {
    vechicleBasicDetail,
    flowDetail,
    dispatchDetail,
    departureInform,
    returnInform
    // relatedFiles
  },
  data() {
    return {
      visible: false,
      activeTab: 'basicDetail',
      scrollTimer: null,
      isTabClick: false,

      vehicleInfo: {},
      detail: {}
    };
  },
  methods: {
    async open(data = {}) {
      const appointmentRes = await this.ajax.getVehicleAppointmentDetail(
        data.id
      );
      const vehicleRes = await vehicleInfoSeeDetails(
        appointmentRes.object.vehicleId
      );
      if (!vehicleRes.success || !appointmentRes.success) {
        this.$message.error('车辆预约详情获取失败');
        return;
      }
      this.vehicleInfo = vehicleRes.object;
      this.detail = appointmentRes.object;
      // 获取车辆图片信息
      this.ajax
        .getFileAttachmentByBusinessId({ businessId: this.vehicleInfo.picture })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.vehicleInfo.picture = res.object[0].realPath;
        });

      this.visible = true;
      this.$nextTick(() => {
        let wrap = this.$refs.scroll.wrap;
        wrap.removeEventListener('scroll', this.handleContentScroll);
        wrap.addEventListener('scroll', this.handleContentScroll);
        wrap.scrollTop = 0;

        this.$refs.returnInform && this.$refs.returnInform.init();
      });
    },
    close() {
      this.visible = false;
    },
    handlePreviewDetail({ name }) {
      name && (this.activeTab = name);
      let dom = this.$refs[name];
      dom = dom && dom.$el;

      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;
      if (!scrollDom.wrap.scrollHeight) {
        return;
      }
      this.isTabClick = true;
      scrollDom.wrap.scrollTo({ top: domScrollTop, behavior: 'smooth' });
    },
    handleContentScroll(e) {
      this.scrollTimer && clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isTabClick = false;
      }, 200);

      let wrap = this.$refs.scroll.wrap,
        childNodes = wrap.childNodes[0].childNodes,
        nodeList = [],
        previewTop = wrap.scrollTop,
        previewBottom = wrap.scrollTop + wrap.clientHeight;
      for (let i = 0; i < childNodes.length; i++) {
        let node = childNodes[i];
        nodeList.push({
          node,
          offsetTop: node.offsetTop,
          height: node.offsetHeight
        });
      }
      if (previewTop == 0) {
        this.activeTab = this.sectionList[0].name;
        return;
      } else if (previewTop == wrap.scrollHeight - wrap.clientHeight) {
        this.activeTab = this.sectionList[this.sectionList.length - 1].name;
        return;
      }
      nodeList.map((item, index) => {
        if (
          previewTop + wrap.clientHeight / 4 >= item.offsetTop &&
          !this.isTabClick
        ) {
          this.activeTab = (this.sectionList[index] || {}).name;
        }
      });
    }
  },
  computed: {
    title() {
      return {
        0: '车辆预约',
        1: '车辆待审批',
        2: '车辆待派车',
        3: '车辆待出车',
        4: '车辆待还车',
        5: '已完结'
      }[this.detail.applyStatus];
    },
    sectionList() {
      let sections = [
        {
          label: '预约信息',
          name: 'basicDetail'
        }
      ];
      if (this.detail.workflowNo) {
        sections.push({
          label: '流程信息',
          name: 'flowDetail'
        });
      }

      if (this.detail.applyStatus >= 3) {
        sections.push({
          label: '派车信息',
          name: 'dispatchDetail'
        });
      }
      if (this.detail.applyStatus >= 4 && this.detail.isDispatch == 1) {
        sections.push({
          label: '出车信息',
          name: 'departureInform'
        });
      }
      if (this.detail.applyStatus >= 5 && this.detail.isDispatch == 1) {
        sections.push({
          label: '还车信息',
          name: 'returnInform'
        });
      }
      return sections;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-icon {
  height: 48px;
  width: 48px;
  border-radius: 50%;
  background: $primary-blue;
  color: #fff;
  padding: 0 9px;
  display: flex;
  align-items: center;
  line-height: 1;
  margin-right: $primary-spacing;
  &.is-done-flow-icon {
    background-color: $disabled-color;
  }
}
.dialog-title {
  margin-bottom: 0;
  font-weight: bold;
  font-size: 16px;
}

/deep/ .vechicle-dialog .el-dialog__body {
  background-color: transparent !important;
  display: flex;
  overflow: hidden;
  padding: 0 !important;
  max-height: calc(100vh - 135px);
}
.status-container {
  flex-shrink: 0;
  margin-right: $medium-spacing;
}
.status-list {
  background-color: $ts-table-header-hover-bg;
  cursor: pointer;
  padding: 2px;
  .status-item {
    line-height: 30px;
    padding: 0 $card-spacing;
    &.active {
      background-color: #fff;
      color: $primary-blue;
    }
  }
}
.detail-container {
  flex: 1;
  background-color: #fff;
  padding: $medium-spacing $card-spacing;

  .el-scrollbar__view > div + div {
    margin-top: $card-spacing;
  }
}
</style>
