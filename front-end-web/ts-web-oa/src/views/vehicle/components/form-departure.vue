<template>
  <ts-form ref="ruleForm" :model="form" labelWidth="130px">
    <ts-form-item
      label="出车时间:"
      class="car-time"
      prop="outDate"
      :rules="rules.required"
    >
      <ts-date-picker
        style="width: 100%"
        v-model="form.outDate"
        format="YYYY-MM-DD HH:mm"
        valueFormat="YYYY-MM-DD HH:mm"
        show-time
      />
    </ts-form-item>

    <ts-form-item label="备注">
      <ts-input
        v-model="form.outRemark"
        type="textarea"
        class="textarea"
        maxlength="500"
        placeholder="请输入"
        show-word-limit
      />
    </ts-form-item>
  </ts-form>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      form: {
        outDate: '',
        outRemark: ''
      },

      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    clearValidate() {
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    async formSubmit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return false;
      }

      let data = Object.assign({}, this.form);
      return data;
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .car-time {
  .ant-calendar-time-picker-select {
    width: 50% !important;
    &:last-child {
      display: none !important;
    }
  }
}

/deep/ .textarea {
  .el-textarea__inner {
    min-height: 110px !important;
    max-height: 200px !important;
  }
}
</style>
