<template>
  <div class="ts-container my-reservation">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
    >
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      :cell-class-name="computedCellTableClassName"
      @refresh="handleRefreshTable"
    />
    <vechicle-detail-dialog ref="preview" />
  </div>
</template>

<script>
import vechicleDetailDialog from '../components/vechicle-detail-dialog.vue';

import table from './mixins/table';
export default {
  mixins: [table],
  components: { vechicleDetailDialog },
  data() {
    return {
      loading: false,

      visible: false
    };
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    computedCellTableClassName({ row, column }) {
      if (column.property == 'vehicleNo' && row.icon) {
        return 'has-changed-cell';
      }
      return '';
    },
    handleShowDetails(row) {
      this.$refs.preview.open(row);
    },
    handleRefreshTable(page = {}) {
      let { applyDateList = [] } = this.searchForm,
        [applyStartTimeSeach = '', applyEndTimeSeach = ''] = applyDateList,
        { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          applyStartTimeSeach,
          applyEndTimeSeach
        };

      if (data.applyStartTimeSeach) {
        data.applyStartTimeSeach = data.applyStartTimeSeach + ' 00:00';
      }
      if (data.applyEndTimeSeach) {
        data.applyEndTimeSeach = data.applyEndTimeSeach + ' 23:59';
      }

      delete data.applyDateList;

      this.ajax.getVehicleAppointmentTableData(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '我的预约数据获取失败');
          return;
        }
        let rows = res.rows.map((row, index) => {
          let { dispatchChange, applyResult } = row,
            isChange = dispatchChange == 1,
            isRepair = applyResult == 6,
            isEmergency = applyResult == 7;
          return {
            ...row,
            isChange,
            isRepair,
            isEmergency,
            pageIndex: (pageNo - 1) * pageSize + index + 1
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.my-reservation {
  width: 100%;
  height: 100%;
  background: rgb(255, 255, 255);
  padding: 8px;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .label {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .success {
      background: #d4ffd4;
      color: #000;
    }

    .pengding {
      background: #eaeaea;
      color: #000;
    }

    .error {
      background: #ffd4d4;
      color: #000;
    }

    .color-primary {
      color: $primary-blue;
    }

    .details-span {
      color: $primary-blue;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      position: relative;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }

    .oil-icon {
      display: inline-flex;
      height: 20px;
      width: 20px;
      justify-content: center;
      align-items: center;
      background-color: orange;
      color: #fff;
      border-radius: 50%;
      font-size: 12px;
      position: relative;
      margin-left: 4px;
      flex-shrink: 0;
      &::after {
        content: '油';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
      }
    }

    .change-icon {
      position: absolute;
      border-top: 18px solid $primary-blue;
      border-right: 18px solid transparent;
      border-left: 0;
      border-bottom: 0;
      left: 0;
      top: 0;
      cursor: default;
      &::before {
        content: '改';
        color: #fff;
        position: absolute;
        font-size: 12px;
        bottom: 2px;
        right: -12px;
        transform: scale(0.5);
      }
    }
    .repair-icon {
      position: absolute;
      border-bottom: 18px solid $tiffany;
      border-right: 18px solid transparent;
      border-top: 0;
      left: 0;
      bottom: 0;
      cursor: default;
      &::before {
        content: '补';
        color: #fff;
        position: absolute;
        font-size: 12px;
        transform: scale(0.7);
        top: 3px;
      }
    }
    .emergency-icon {
      position: absolute;
      border-bottom: 18px solid $error-color;
      border-left: 18px solid transparent;
      border-top: 0;
      right: 0;
      bottom: 0;
      cursor: default;
      &::before {
        content: '急';
        color: #fff;
        position: absolute;
        font-size: 12px;
        transform: scale(0.7);
        top: 5px;
        right: -2px;
      }
    }
  }
}
</style>
