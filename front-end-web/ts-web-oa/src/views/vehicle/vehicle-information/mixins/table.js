export default {
  data() {
    return {
      searchForm: {},

      actions: [
        {
          label: '新增',
          click: this.handleAdd,
          prop: { type: 'primary' }
        }
      ],

      searchList: [
        {
          label: '车牌号码:',
          value: 'vehicleNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入车牌号码'
          }
        },

        {
          label: '车辆状态:',
          value: 'vehicleStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '总里程数:',
          value: 'totalMileage'
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '车牌号',
          prop: 'vehicleNo',
          width: 120,
          align: 'center',
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.handleDetails(row);
                }}>
                {row.vehicleNo}
              </span>
            );
          }
        },
        {
          label: '车辆名称',
          prop: 'vehicleName',
          width: 130,
          align: 'center'
        },
        {
          label: '车颜色',
          prop: 'vehicleColor',
          align: 'center',
          width: 80
        },
        {
          label: '所属机构',
          prop: 'orgName',
          align: 'center'
        },
        {
          label: '车辆状态',
          prop: 'vehicleStatus',
          align: 'center',
          width: 85,
          formatter: row => {
            const label = this.vehicleStateList.find(
              item => item.value == row.vehicleStatus
            ).label;
            return <span>{label}</span>;
          }
        },
        {
          label: '座位数',
          prop: 'seats',
          align: 'center',
          width: 75
        },
        {
          label: '购买价格(元)',
          prop: 'vehiclePrice',
          align: 'center',
          width: 105
        },
        {
          label: '购置日期',
          prop: 'buyDate',
          align: 'center',
          width: 100
        },
        {
          label: '保险到期日期',
          prop: 'insureDate',
          align: 'center',
          width: 160,
          formatter: row => {
            const { insureDate } = row;
            function isLessThanOneMonth(dateString) {
              const inputDate = new Date(dateString);
              const timeDiff = new Date() - inputDate;
              const day = timeDiff / (1000 * 60 * 60 * 24);
              return day > -30;
            }
            let tipsName = isLessThanOneMonth(insureDate) ? 'red' : '';
            return <span class={tipsName}>{insureDate}</span>;
          }
        },
        {
          label: '年检到期日期',
          prop: 'inspectNextDate',
          align: 'center',
          width: 160,
          formatter: row => {
            const { inspectNextDate } = row;
            function isLessThanOneMonth(dateString) {
              const inputDate = new Date(dateString);
              const timeDiff = new Date() - inputDate;
              const day = timeDiff / (1000 * 60 * 60 * 24);
              return day > -30;
            }
            let tipsName = isLessThanOneMonth(inspectNextDate) ? 'red' : '';
            return <span class={tipsName}>{inspectNextDate}</span>;
          }
        },
        {
          label: '初始里程数(km)',
          prop: 'mileage',
          align: 'center',
          width: 120
        },
        {
          label: '总里程数(km)',
          prop: 'totalMileage',
          align: 'center',
          width: 160
        },
        {
          label: '发动机号码',
          prop: 'engineNo',
          align: 'center',
          width: 160
        },
        {
          label: '创建人',
          width: 100,
          prop: 'createUserName',
          align: 'center'
        },
        {
          label: '创建时间',
          width: 140,
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '保险信息',
                event: this.handleInsuranceInformation
              },
              {
                label: '车检信息',
                event: this.handleVehicleInspectionInfo
              },
              {
                label: '出险信息',
                event: this.handleAccidentInfo
              },
              {
                label: '保养信息',
                event: this.handleMaintenanceInfo
              },
              {
                label: '删除',
                event: this.handleDelete
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
