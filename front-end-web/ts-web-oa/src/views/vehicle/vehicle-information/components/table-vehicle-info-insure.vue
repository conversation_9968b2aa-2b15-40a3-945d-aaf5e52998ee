<template>
  <div class="item-box">
    <p class="basic-tips">出险信息</p>
    <div
      class="date-table-content"
      v-for="(value, key, index) in listData"
      :key="index"
    >
      <div class="time-tips">
        <span>{{ key }}</span>
      </div>
      <div class="item" v-for="item in value" :key="item.id">
        <div class="time">
          <span></span>
          <div>
            <ts-button type="primary" @click="handleEdit(item)">编辑</ts-button>
            <ts-button @click="handleDel(item)">删除</ts-button>
          </div>
        </div>
        <base-details-table
          class="item-table"
          :itemDatas="itemDatas"
          :formDatas="item"
        >
          <template v-slot:dangerPrice>{{ item.dangerPrice }}元</template>
          <template v-slot:artificialPrice>
            {{ item.artificialPrice }}元
          </template>
          <template v-slot:materialPrice>{{ item.materialPrice }}元</template>
          <template v-slot:file>
            <base-upload
              onlyRead
              :actions="['preview', 'downLoad']"
              :ref="`dangerFiles${item.id}`"
              v-model="item.dangerFiles"
            />
          </template>
        </base-details-table>
      </div>
    </div>

    <dialog-accident-info
      v-model="dialogAccidentInfo"
      :eachData="eachData"
      :openType="openType"
      @refreshTable="handleRefreshInfo"
    />
  </div>
</template>

<script>
import DialogAccidentInfo from './dialog-accident-info.vue';
import { vehicleInfoDangerDel } from '@/api/ajax/vehicle/vehicle-information.js';
export default {
  components: {
    DialogAccidentInfo
  },
  props: {
    listData: {
      type: Object
    }
  },
  data() {
    return {
      eachData: {},
      openType: '',
      dialogAccidentInfo: false,

      itemDatas: [
        [
          { label: '办理人', prop: 'dangerName', colspan: 1 },
          { label: '出险日期', prop: 'dangerDate', colspan: 1 }
        ],
        [{ label: '出险公司', prop: 'dangerCompany', colspan: 3 }],
        [{ label: '出险金额', slot: 'dangerPrice', colspan: 3 }],
        [
          { label: '人工金额', slot: 'artificialPrice' },
          { label: '材料金额', slot: 'materialPrice' }
        ],
        [{ label: '描述', prop: 'dangerDescribe', colspan: 3 }],
        [{ label: '修理项目', prop: 'repairItems', colspan: 3 }],
        [{ label: '材料', prop: 'material', colspan: 3 }],
        [{ label: '备注', prop: 'dangerRemark', colspan: 3, rowspan: 1 }],
        [{ label: '附件', slot: 'file', colspan: 3, rowspan: 1 }]
      ]
    };
  },
  methods: {
    handleEdit(row) {
      const data = Object.assign({}, row);

      this.eachData = data;
      this.openType = 'edit';
      this.dialogAccidentInfo = true;
    },
    async handleDel(row) {
      const { id } = row;
      try {
        await this.$confirm(`您确认删除该条数据吗`, '提示', {
          type: 'warning'
        });
        const res = await vehicleInfoDangerDel(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshInfo(row, 'del');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    handleRefreshInfo(data, type) {
      this.$emit('refeshDetails', data);
      if (type === 'del') {
        return;
      }
      let refName = `dangerFiles${data.id}`;
      this.$refs[refName][0].getFileList();
    }
  }
};
</script>

<style lang="scss" scoped>
.item-box {
  margin-bottom: 8px;
  .basic-tips {
    font-weight: 500;
    font-style: normal;
    color: #666666;
    margin-bottom: 0px;
  }
  .date-table-content {
    position: relative;
    .time-tips {
      position: absolute;
      left: 4px;
      top: 4px;
      > span {
        font-size: 20px;
        font-weight: 700;
        color: #333;
      }
    }
  }
  .time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  .item {
    margin-bottom: 8px;
    .item-table {
      margin-bottom: 8px;
    }
  }
}
</style>
