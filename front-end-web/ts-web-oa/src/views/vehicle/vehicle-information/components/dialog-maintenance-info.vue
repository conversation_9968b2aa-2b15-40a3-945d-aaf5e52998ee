<template>
  <ts-dialog
    class="dialog-maintenance-info"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="950"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="110px">
        <ts-form-item
          label="保养公司:"
          prop="maintenanceCompany"
          :rules="rules.required"
        >
          <ts-input v-model="form.maintenanceCompany" :maxlength="50" />
        </ts-form-item>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="办理人:"
              prop="maintenanceName"
              :rules="rules.required"
            >
              <ts-input v-model="form.maintenanceName" :maxlength="10" />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="保养类别:"
              prop="maintenanceType"
              :rules="rules.required"
            >
              <ts-select
                style="width: 100%"
                v-model="form.maintenanceType"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of maintenanceTypeList"
                  :key="item.id"
                  v-bind="item"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="保养日期:"
              prop="maintenanceDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.maintenanceDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="保养费用:"
              prop="maintenancePrice"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.maintenancePrice"
                :maxlength="50"
                placeholder="请输入"
                @input="
                  validateTowDecimalPlaces($event, 'form', 'maintenancePrice')
                "
                @blur="inputBlur($event, 'form', 'maintenancePrice')"
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item
          label="保养内容:"
          prop="maintenanceContent"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.maintenanceContent"
            type="textarea"
            class="textarea"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="更新配件:">
          <ts-input
            v-model="form.maintenanceAccessory"
            type="textarea"
            class="textarea"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="备注:">
          <ts-input
            v-model="form.maintenanceRemark"
            type="textarea"
            class="textarea"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件:">
          <base-upload ref="maintenanceFiles" v-model="form.maintenanceFiles" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import {
  vehicleInfoMaintenanceSave,
  vehicleInfoMaintenanceUpdate
} from '@/api/ajax/vehicle/vehicle-information.js';
import { inputTowDecimalPlaces } from '@/unit/commonHandle.js';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    eachData: {
      type: Object
    },
    openType: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      title: '',

      maintenanceTypeList: [
        { label: '一级', value: '一级' },
        { label: '二级', value: '二级' },
        { label: '三级', value: '三级' }
      ],

      rules: {
        required: { required: true, message: '必填' }
      },

      form: {
        maintenanceCompany: '',
        maintenanceName: '',
        maintenanceType: '',
        maintenanceDate: '',
        maintenancePrice: undefined,
        maintenanceContent: '',
        maintenanceAccessory: '',
        maintenanceRemark: '',
        maintenanceFiles: ''
      }
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.form = {};

          if (this.openType === 'add') {
            this.$set(this, 'form', {
              maintenanceName: this.$store.state.common.userInfo.employeeName
            });
            this.title = '保养信息新增';
          } else {
            this.$set(this, 'form', this.eachData);
            this.title = '保养信息编辑';
          }

          this.$nextTick(() => {
            this.$refs.ruleForm?.clearValidate();
          });
        }
        this.visible = val;
      }
    }
  },
  methods: {
    inputBlur(event, formName, setKey) {
      let value = parseFloat(event.target.value);
      this.$set(this[formName], setKey, isNaN(value) ? '' : value);
    },
    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign(this.eachData, this.form);

      let API = null;
      let titleType = '';
      if (this.openType === 'add') {
        API = vehicleInfoMaintenanceSave;
        titleType = '添加';
      } else {
        API = vehicleInfoMaintenanceUpdate;
        titleType = '编辑';
      }
      const submitres = await API(data);

      if (!submitres.success) {
        this.$message.error(submitres.message || '操作失败');
        return;
      }
      this.$message.success(`保养信息${titleType}成功!`);
      this.$emit('refreshTable', data);
      this.close();
    },

    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },

    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-maintenance-info {
  .content {
    height: 500px;
    overflow: auto;
    ::v-deep {
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
