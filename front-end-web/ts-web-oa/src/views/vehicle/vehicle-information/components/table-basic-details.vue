<template>
  <div class="item-box">
    <p class="basic-tips">基本信息</p>
    <base-details-table :itemDatas="itemDatas" :formDatas="detailsInfo">
      <template v-slot:seats> {{ detailsInfo.seats }} 人 </template>
      <template v-slot:vehiclePrice>
        {{ detailsInfo.vehiclePrice }} 元
      </template>
      <template v-slot:mileage> {{ detailsInfo.mileage }} km </template>
      <template v-slot:examine>
        {{ detailsInfo.examine == '1' ? '是' : '否' }}
      </template>
      <template v-slot:file>
        <base-upload
          onlyRead
          :actions="['preview', 'downLoad']"
          ref="vehicleFiles"
          v-model="detailsInfo.vehicleFiles"
        />
      </template>
    </base-details-table>
  </div>
</template>

<script>
export default {
  props: {
    detailsInfo: {
      type: Object
    }
  },
  data() {
    return {
      itemDatas: [
        [
          { label: '所属机构', prop: 'orgName' },
          { label: '车辆类型', prop: 'vehicleType' }
        ],
        [
          { label: '购买价格', slot: 'vehiclePrice' },
          { label: '购置日期', prop: 'buyDate' }
        ],
        [
          { label: '发动机号码', prop: 'engineNo' },
          { label: '初始里程数', slot: 'mileage' }
        ],
        [
          { label: '可使用人数', slot: 'seats' },
          { label: '车辆管理员', prop: 'managerName' }
        ],
        [{ label: '是否需要审批', slot: 'examine', colspan: 3 }],
        [{ label: '备注', prop: 'vehicleRemark', colspan: 3, rowspan: 1 }],
        [{ label: '附件', slot: 'file', colspan: 3, rowspan: 1 }]
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.item-box {
  margin-bottom: 8px;
}
.basic-tips {
  font-weight: 500;
  font-style: normal;
  color: #666666;
  margin-bottom: 0px;
}
</style>
