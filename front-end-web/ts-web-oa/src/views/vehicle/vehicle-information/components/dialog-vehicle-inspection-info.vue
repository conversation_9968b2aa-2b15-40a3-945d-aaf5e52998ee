<template>
  <ts-dialog
    class="dialog-insurance-information"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="950"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="110px">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="送检人:"
              prop="inspectName"
              :rules="rules.required"
            >
              <ts-input v-model="form.inspectName" :maxlength="10" />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="车检日期:"
              prop="inspectDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.inspectDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="车检费用:"
              prop="inspectPrice"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.inspectPrice"
                :maxlength="50"
                placeholder="请输入"
                @input="
                  validateTowDecimalPlaces($event, 'form', 'inspectPrice')
                "
                @blur="inputBlur($event, 'form', 'inspectPrice')"
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="下次车检日期:"
              prop="inspectNextDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.inspectNextDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item
          label="合格标识号:"
          prop="inspectQualified"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.inspectQualified"
            :maxlength="14"
            placeholder="请输入"
          />
        </ts-form-item>

        <ts-form-item
          label="车检情况:"
          prop="inspectSituation"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.inspectSituation"
            type="textarea"
            class="textarea"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item
          label="车管所名称:"
          prop="dmvName"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.dmvName"
            :maxlength="50"
            placeholder="请输入"
          />
        </ts-form-item>

        <ts-form-item
          label="车管所地址:"
          prop="dmvAddress"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.dmvAddress"
            :maxlength="100"
            placeholder="请输入"
          />
        </ts-form-item>

        <ts-form-item label="备注:">
          <ts-input
            v-model="form.inspectRemark"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件:">
          <base-upload ref="inspectFiles" v-model="form.inspectFiles" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import {
  vehicleInfoInspectSave,
  vehicleInfoInspectUpdate
} from '@/api/ajax/vehicle/vehicle-information.js';
import { inputTowDecimalPlaces } from '@/unit/commonHandle.js';
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    eachData: {
      type: Object
    },
    openType: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      title: '',

      rules: {
        required: { required: true, message: '必填' }
      },

      form: {
        inspectName: '',
        inspectDate: '',
        inspectPrice: undefined,
        inspectNextDate: '',
        inspectQualified: '',
        inspectSituation: '',
        dmvName: '',
        dmvAddress: '',
        inspectRemark: '',
        inspectFiles: ''
      }
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.form = {};

          if (this.openType === 'add') {
            this.$set(this, 'form', {
              inspectName: this.$store.state.common.userInfo.employeeName
            });
            this.title = '车检信息新增';
          } else {
            this.$set(this, 'form', this.eachData);
            this.title = '车检信息编辑';
          }

          this.$nextTick(() => {
            this.$refs.ruleForm?.clearValidate();
          });
        }
        this.visible = val;
      }
    }
  },
  methods: {
    inputBlur(event, formName, setKey) {
      let value = parseFloat(event.target.value);
      this.$set(this[formName], setKey, isNaN(value) ? '' : value);
    },

    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },

    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign(this.eachData, this.form);

      let API = null;
      let titleType = '';
      if (this.openType === 'add') {
        API = vehicleInfoInspectSave;
        titleType = '添加';
      } else {
        API = vehicleInfoInspectUpdate;
        titleType = '编辑';
      }
      const submitres = await API(data);

      if (!submitres.success) {
        this.$message.error(submitres.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success(`车检信息${titleType}成功!`);
      this.$emit('refreshTable', data);
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-insurance-information {
  .content {
    height: 500px;
    overflow: auto;
    ::v-deep {
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
