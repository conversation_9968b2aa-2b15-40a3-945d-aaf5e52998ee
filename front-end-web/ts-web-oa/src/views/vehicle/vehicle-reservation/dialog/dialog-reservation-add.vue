<template>
  <ts-dialog
    class="dialog-reservation-add"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="for-vehicle-box">
        <img
          v-on:mouseenter="handleMouseEnter"
          class="picture"
          :src="vehicleInfo.picture"
          alt=""
        />
        <div class="vehicle-message">
          <div class="top">
            <div class="vehicle-id-card">{{ vehicleInfo.vehicleNo }}</div>
            <div class="vehicle-status">
              {{ vehicleInfo.vehicleStatusLabel }}
            </div>
            <div class="vehicle-seats">{{ vehicleInfo.seats }}座</div>
            <span class="bind-oil" v-if="vehicleInfo.oilCard">绑定油卡</span>
            <span class="bind-oil no" v-else>未绑定油卡</span>
          </div>
          <div class="bottom">
            {{ vehicleInfo.vehicleName }} · {{ vehicleInfo.vehicleColor }} ·
            {{ carAge }}
          </div>
        </div>
      </div>

      <form-reservation v-if="visible" ref="FormReservation" />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    />
  </ts-dialog>
</template>

<script>
import unit from '@/views/vehicle/mixins/unit.js';
import FormReservation from '../../components/form-reservation';
import { vehicleApplySave } from '@/api/ajax/vehicle/vehicle-information.js';
export default {
  mixins: [unit],
  components: {
    FormReservation
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      vehicleInfo: {},

      previewFile: '',
      previewFileList: []
    };
  },

  computed: {
    carAge() {
      return this.calculateUsageYears(this.vehicleInfo.buyDate);
    }
  },
  methods: {
    async open(data = {}) {
      this.visible = true;

      this.vehicleInfo = data.vehicleInfo;
      this.type = data.type;

      this.handleGetVehicleImg();

      this.$nextTick(() => {
        const FormRefs = this.$refs.FormReservation;

        if (this.type === 'add') {
          this.title = '预约';
          this.$set(FormRefs.form, 'vehicleId', data.vehicleInfo.id);
          if (data.times) this.$set(FormRefs.form, 'applyTime', data.times);
        } else {
          this.title = '编辑';
        }
        FormRefs.$refs.ruleForm?.clearValidate();
      });
    },
    handleGetVehicleImg() {
      this.ajax
        .getFileAttachmentByBusinessId({ businessId: this.vehicleInfo.picture })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.vehicleInfo.picture = res.object[0].realPath;
        });
    },
    handleMouseEnter() {
      this.previewFile = location.origin + this.vehicleInfo.picture;
      this.previewFileList = [this.previewFile];
      this.$nextTick(() => {
        this.$refs.preview.clickHandler();
      });
    },
    async submit() {
      let validateForm = await this.$refs.FormReservation.formSubmit();
      if (!validateForm) {
        return;
      }

      const data = Object.assign({}, validateForm);

      let API = null;
      if (this.type === 'add') {
        API = vehicleApplySave;
      } else {
        API = vehicleDriverUpdate;
      }
      const submitres = await API(data);

      if (!submitres.success) {
        this.$message.error(submitres.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refreshTable');
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-reservation-add {
  background: #f4f4f4;
  border-radius: 0 !important;
  /deep/ .el-dialog__header {
    margin: 0;
    padding: 8px 16px !important;
    display: flex;
    align-items: center;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }
  /deep/.el-dialog__headerbtn {
    top: 20px !important;
    right: 16px;
  }
  /deep/ .el-dialog__body {
    padding: 0;
    margin: 0 auto !important;
    overflow: auto;
    .content {
      height: 100%;
      overflow: auto;
      background: #fff;
      display: flex;
      flex-direction: column;
      padding: 18px 30px 0;
      .for-vehicle-box {
        height: 80px;
        background-color: rgba(242, 243, 255, 1);
        border-radius: 4px;
        padding: 20px 0 0 12px;
        margin-bottom: 12px;
        display: flex;
        .picture {
          width: 40px;
          height: 46px;
          margin-right: 8px;
        }
        .top {
          display: flex;
          align-items: center;

          .vehicle-id-card {
            font-weight: 700;
            color: #000;
            font-size: 16px;
            margin-right: 16px;
          }
          .vehicle-status {
            color: #5260ff;
            margin-right: 16px;
          }

          .vehicle-seats {
            font-weight: 500;
            color: #666666;
            font-size: 13px;
            margin-right: 16px;
          }

          .bind-oil {
            display: inline-block;
            padding: 4px 12px;
            background: $primary-blue;
            color: $primary-white;
            border-radius: 50px;
            font-weight: 600;
            font-size: 11px;
            &.no {
              background: rgba(170, 170, 170, 1);
              color: #fff;
            }
          }
          .vehicle-available-time {
            font-weight: 500;
            color: #666666;
            font-size: 13px;
            margin-right: 16px;
          }
        }
        .bottom {
          font-weight: 400;
          color: #999999;
          font-size: 13px;
        }
      }
    }
  }
  /deep/ .el-dialog__footer {
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto !important;
  }
}
</style>
