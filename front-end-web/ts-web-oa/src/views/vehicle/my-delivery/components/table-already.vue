<template>
  <base-table
    class="form-table"
    ref="table"
    border
    stripe
    v-loading="loading"
    :columns="columns"
  />
</template>

<script>
import table from '../mixins/table-already.js';
export default {
  mixins: [table],
  methods: {
    handleDetails(e) {
      this.$parent.handleDetails(e);
    },
    handleWithdraw(e) {
      this.$parent.handleWithdraw(e);
    }
  }
};
</script>
