export default {
  data() {
    return {
      searchForm: {
        writtenDate: '',
        time: ''
      },
      actions: [
        {
          label: '新增',
          click: this.handleAddDriver,
          prop: { type: 'primary' }
        }
      ],
      searchList: [
        {
          label: '',
          value: 'driverName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入司机'
          }
        },
        {
          label: '准驾车型',
          value: 'driverQuasi',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '司机状态',
          value: 'driverStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center',
          width: 50
        },
        {
          label: '司机',
          prop: 'driverName',
          width: 80,
          align: 'center',
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.handleDetails(row);
                }}>
                {row.driverName}
              </span>
            );
          }
        },
        {
          label: '性别',
          prop: 'driverSex',
          width: 45,
          align: 'center'
        },
        {
          label: '年龄',
          prop: 'driverAge',
          width: 45,
          align: 'center'
        },
        {
          label: '科室',
          prop: 'driverDeptName',
          width: 130,
          align: 'center'
        },
        {
          label: '状态',
          prop: 'driverStatus',
          align: 'center',
          width: 120,
          formatter: row => {
            let color = `color: ${this.driverStatusColor[row.driverStatus]}`;
            return (
              <span style={color}>{this.driverStatus[row.driverStatus]}</span>
            );
          }
        },
        {
          label: '违章次数',
          prop: 'violationNumbers',
          align: 'center',
          width: 80,
          formatter: row => {
            return row.violationNumbers || '-';
          }
        },
        {
          label: '事故次数',
          prop: 'accidentNumbers',
          align: 'center',
          width: 80,
          formatter: row => {
            return row.accidentNumbers || '-';
          }
        },
        {
          label: '联系方式',
          prop: 'driverPhone',
          align: 'center',
          width: 105
        },
        {
          label: '驾龄(年)',
          prop: '',
          align: 'center',
          width: 80,
          formatter: row => {
            const nowYear = Number(new Date().getFullYear());
            const driverFirstDate = Number(row.driverFirstDate.slice(0, 4));
            return nowYear - driverFirstDate;
          }
        },
        {
          label: '准驾车型',
          prop: 'driverQuasi',
          align: 'center',
          width: 90
        },
        {
          label: '初次领证日期',
          prop: 'driverFirstDate',
          align: 'center',
          width: 110
        },
        {
          label: '驾驶证有效期',
          prop: 'driverEffective',
          align: 'center',
          width: 180,
          formatter: row => {
            const { driverEffectiveEnd } = row;

            function isLessThanOneMonth(dateString) {
              const inputDate = new Date(dateString);
              const timeDiff = new Date() - inputDate;
              const day = timeDiff / (1000 * 60 * 60 * 24);
              return day > -30;
            }
            let tipsName = isLessThanOneMonth(driverEffectiveEnd) ? 'red' : '';
            return <span class={tipsName}>{driverEffectiveEnd}</span>;
          }
        },
        {
          label: '实习期至',
          prop: 'internshipDate',
          align: 'center',
          width: 100
        },
        {
          label: '创建人',
          width: 70,
          prop: 'createUser',
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleEditDriver
              },
              {
                label: '删除',
                event: this.handleDeleteDriver
              },
              {
                label: '事故记录',
                event: this.handleAccidentRecords
              },
              {
                label: '违章记录',
                event: this.handleViolationRecords
              }
            ];

            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': event => event(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
