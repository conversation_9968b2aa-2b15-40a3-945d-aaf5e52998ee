<template>
  <ts-dialog
    class="dialog-vehicle-info-details"
    title="查看"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content" v-if="detailsInfo">
      <div class="vehicle-info-box">
        <div class="title-top">
          <div class="left">
            <img :src="detailsInfo.driverPicture" class="vechicle-image" />
            <div class="card-info">
              <div class="card-name">
                {{ detailsInfo.driverName }}
                <span class="primary-blue">{{
                  driverStatus[detailsInfo.driverStatus]
                }}</span>
              </div>
              <span>
                {{ detailsInfo.driverSex }} · {{ detailsInfo.driverAge }}岁·
                {{ carAge }} · {{ detailsInfo.driverPhone }}
              </span>
            </div>
          </div>
          <div class="right">
            <div class="item">
              <p class="remaining-amount">驾驶证有效期</p>
              <p>
                还剩
                <span
                  :class="
                    detailsInfo.periodOfValidity < 30
                      ? 'red-residue'
                      : 'blue-residue'
                  "
                >
                  {{ detailsInfo.periodOfValidity }}
                </span>
                天
              </p>
            </div>
          </div>
        </div>
        <div class="buttom-price">
          <div class="box-content">
            <div class="item" v-for="(item, index) in totalArr" :key="index">
              <p class="classification">{{ item.label }}</p>
              <p class="price">
                <span class="val" v-if="item.formatt">{{
                  detailsInfo[item.key] | formatMoney
                }}</span>
                <span class="val" v-else>{{ detailsInfo[item.key] }}</span>
                <span class="unit">{{ item.unit || '元' }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="record-box">
        <div class="status-container">
          <ul class="status-list">
            <li
              v-for="item of infoList"
              :key="item.name"
              :class="{
                active: activeTab == item.name
              }"
              @click="handlePreviewDetail(item)"
              class="status-item"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>

        <el-scrollbar
          ref="scroll"
          style="flex: 1;"
          wrap-style="height: calc(100% + 17px);"
        >
          <div class="table-content">
            <table-basic-details
              ref="TableBasicDetails"
              :detailsInfo="detailsInfo"
            />
            <table-departure-record
              ref="TableDepartureRecord"
              v-if="
                detailsInfo.vehicleApplyList &&
                  detailsInfo.vehicleApplyList.length
              "
              :listData="vehicleApplyList"
              @refeshDetails="refeshDetails"
            />
            <table-accident-record
              ref="TableAccidentRecord"
              v-if="
                detailsInfo.vehicleDriverAccidentList &&
                  detailsInfo.vehicleDriverAccidentList.length
              "
              :listData="vehicleDriverAccidentList"
              @refeshDetails="refeshDetails"
            />
            <table-violation-records
              ref="TableViolationRecords"
              v-if="
                detailsInfo.vehicleDriverViolationList &&
                  detailsInfo.vehicleDriverViolationList.length
              "
              :listData="vehicleDriverViolationList"
              @refeshDetails="refeshDetails"
            />
          </div>
        </el-scrollbar>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import unitMixins from '@/views/vehicle/mixins/unit.js';
import { driverStatus } from '@/views/config.js';
import { selectVehicleDriverById } from '@/api/ajax/driver/index.js';

import TableBasicDetails from './table-basic-details';
import TableDepartureRecord from './table-departure-record';
import TableAccidentRecord from './table-accident-record';
import TableViolationRecords from './table-violation-records';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  mixins: [unitMixins],
  components: {
    TableBasicDetails,
    TableDepartureRecord,
    TableAccidentRecord,
    TableViolationRecords
  },
  props: {
    show: {
      type: Boolean
    },
    eachData: {
      type: Object
    }
  },
  computed: {
    carAge() {
      return this.calculateUsageYears(this.detailsInfo.driverFirstDate);
    }
  },
  data() {
    return {
      visible: false,
      activeTab: '',
      isTabClick: false,
      scrollTimer: undefined,

      infoList: [],
      detailsInfo: null,
      vehicleApplyList: {},
      vehicleDriverAccidentList: {},
      vehicleDriverViolationList: {},

      driverStatus,

      totalArr: [
        {
          label: '出车次数',
          key: 'outNumbers',
          unit: '次'
        },
        {
          label: '行驶总里程',
          key: 'totalDistance',
          unit: 'KM'
        },
        {
          label: '事故次数',
          key: 'accidentNumbers',
          unit: '次'
        },
        {
          label: '保险赔偿总额',
          key: 'totalAccidentIca',
          unit: '元'
        },
        {
          label: '违章次数',
          key: 'violationNumbers',
          unit: '次'
        },
        {
          label: '违章罚款总额',
          key: 'totalViolationFine',
          unit: '元',
          formatt: true
        }
      ]
    };
  },
  methods: {
    async open(data = {}) {
      await this.handleGetDeatils(data.id);
      this.activeTab = 'TableBasicDetails';

      this.visible = true;
      setTimeout(() => {
        let wrap = this.$refs.scroll.wrap;
        wrap.removeEventListener('scroll', this.handleContentScroll);
        wrap.addEventListener('scroll', this.handleContentScroll);
        wrap.scrollTop = 0;
      }, 1000);
    },

    handleGetDeatils(id) {
      selectVehicleDriverById({
        id
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取司机详情失败!');
          return;
        }
        this.detailsInfo = res.object;

        this.ajax
          .getFileAttachmentByBusinessId({
            businessId: res.object.driverPicture
          })
          .then(res => {
            if (!res.success) {
              return;
            }
            this.detailsInfo.driverPicture = res.object[0].realPath;
          });

        this.detailsInfo.periodOfValidity = this.handleFilterLastDay(
          this.detailsInfo.driverEffectiveEnd
        );

        this.infoList = [
          {
            label: '基本信息',
            name: 'TableBasicDetails'
          }
        ];

        const {
          vehicleApplyList,
          vehicleDriverAccidentList,
          vehicleDriverViolationList
        } = this.detailsInfo;

        this.vehicleApplyList = {};
        this.vehicleDriverAccidentList = {};
        this.vehicleDriverViolationList = {};

        if (vehicleApplyList && vehicleApplyList.length) {
          this.infoList.push({
            label: '出车记录',
            name: 'TableDepartureRecord'
          });

          vehicleApplyList.forEach(item => {
            item.dateYearMonth = this.$dayjs(item.outDate).format('YYYY-MM');

            if (this.vehicleApplyList[item.dateYearMonth]) {
              this.vehicleApplyList[item.dateYearMonth].push(item);
            } else {
              this.vehicleApplyList[item.dateYearMonth] = [item];
            }
          });
        }
        if (vehicleDriverAccidentList && vehicleDriverAccidentList.length) {
          this.infoList.push({
            label: '事故记录',
            name: 'TableAccidentRecord'
          });

          vehicleDriverAccidentList.forEach(item => {
            item.dateYearMonth = this.$dayjs(item.accidentDate).format(
              'YYYY-MM'
            );

            if (this.vehicleDriverAccidentList[item.dateYearMonth]) {
              this.vehicleDriverAccidentList[item.dateYearMonth].push(item);
            } else {
              this.vehicleDriverAccidentList[item.dateYearMonth] = [item];
            }
          });
        }
        if (vehicleDriverViolationList && vehicleDriverViolationList.length) {
          this.infoList.push({
            label: '违章记录',
            name: 'TableViolationRecords'
          });

          vehicleDriverViolationList.forEach(item => {
            item.dateYearMonth = this.$dayjs(item.violationDate).format(
              'YYYY-MM'
            );

            if (this.vehicleDriverViolationList[item.dateYearMonth]) {
              this.vehicleDriverViolationList[item.dateYearMonth].push(item);
            } else {
              this.vehicleDriverViolationList[item.dateYearMonth] = [item];
            }
          });
        }
      });
    },
    handleFilterLastDay(val) {
      const targetDate = this.$dayjs(val);
      const currentDate = this.$dayjs();
      return targetDate.diff(currentDate, 'day');
    },
    handlePreviewDetail({ name }) {
      name && (this.activeTab = name);
      let dom = this.$refs[name];
      dom = dom && dom.$el;

      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;
      if (!scrollDom.wrap.scrollHeight) {
        return;
      }
      this.isTabClick = true;
      scrollDom.wrap.scrollTo({ top: domScrollTop, behavior: 'smooth' });
    },
    handleContentScroll(e) {
      this.scrollTimer && clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isTabClick = false;
      }, 200);

      let wrap = this.$refs.scroll.wrap,
        childNodes = wrap.childNodes[0].childNodes[0].childNodes,
        nodeList = [],
        previewTop = wrap.scrollTop,
        previewBottom = wrap.scrollTop + wrap.clientHeight,
        beforeHeight = 0;
      for (let i = 0; i < childNodes.length; i++) {
        let node = childNodes[i];
        if (node.nodeType === 1) {
          beforeHeight += node.clientHeight;
          nodeList.push({
            node,
            offsetTop: node.offsetTop,
            inBoxHeight: beforeHeight
          });
        }
      }
      if (previewTop == 0) {
        this.activeTab = this.infoList[0].name;
        return;
      } else if (previewTop == wrap.scrollHeight - wrap.clientHeight) {
        this.activeTab = this.infoList[this.infoList.length - 1].name;
        return;
      }
      for (let i = 0; i < nodeList.length; i++) {
        const item = nodeList[i];
        if (previewTop <= item.inBoxHeight && !this.isTabClick) {
          this.activeTab = (this.infoList[i] || {}).name;
          break;
        }
      }
    },
    refeshDetails(data) {
      this.handleGetDeatils(data.driverId);
    },
    close() {
      this.detailsInfo = null;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-vehicle-info-details {
  width: 100%;
  ::v-deep {
    .ts-dialog {
      width: 1000px !important;
      height: calc(100% - 40px) !important;
      .el-dialog__body {
        height: calc(100% - 90px) !important;
      }
    }
    .title-box {
      cursor: pointer;
    }
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    p {
      margin: 0;
    }
    ::v-deep {
      .vehicle-info-box {
        width: 100%;
        padding: 8px;
        padding-bottom: 0;
        background: rgba(242, 243, 255, 1);
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        .title-top {
          width: 100%;
          display: flex;
          margin-bottom: 8px;
          .left {
            flex: 1;
            display: flex;
            .vechicle-image {
              flex-shrink: 0;
              height: 48px;
              width: 48px;
              border-radius: $small-radius;
              margin-right: $primary-spacing;
            }
            .card-info {
              display: flex;
              flex-direction: column;
              .card-name {
                font-weight: 500;
                font-size: 16px;
                color: #333;
                .primary-blue {
                  color: $primary-blue;
                  margin-left: 10px;
                }
              }
            }
            .card-organization {
              color: #999999;
            }
          }
          .right {
            display: flex;
            .item {
              margin-left: 40px;
              .remaining-amount {
                font-weight: 500;
                font-size: 14px;
                color: #333333;
              }
              .red-residue {
                font-size: 17px;
                color: red;
              }
              .blue-residue {
                font-size: 17px;
                color: $primary-blue;
              }
            }
          }
        }
        .buttom-price {
          border-top: 1px solid #eee;
          padding-top: 4px;
          margin-top: 4px;
          .box-content {
            padding: 0 80px;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            .item {
              width: 110px;
              margin-bottom: 12px;
              .classification {
                font-weight: 500;
                color: #333;
              }
              .price {
                .val {
                  color: #5260ff;
                  font-size: 16px;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
      .record-box {
        margin-top: 8px;
        display: flex;
        flex: 1;
        overflow: auto;
        .status-container {
          flex-shrink: 0;
          margin-right: $medium-spacing;
          .status-list {
            background-color: $ts-table-header-hover-bg;
            cursor: pointer;
            padding: 2px;
            .status-item {
              line-height: 30px;
              padding: 0 $card-spacing;
              &.active {
                background-color: #fff;
                color: $primary-blue;
              }
            }
          }
        }

        .ts-steps {
          width: 100px;
          height: 55px !important;
          flex: inherit !important;
        }
        .table-content {
        }
      }
    }
  }
}
</style>
