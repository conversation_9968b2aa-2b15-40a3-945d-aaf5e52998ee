<template>
  <div class="item-box">
    <p class="basic-tips">事故记录</p>
    <div
      class="date-table-content"
      v-for="(value, key, index) in listData"
      :key="index"
    >
      <div class="time-tips">
        <span>{{ key }}</span>
      </div>
      <div class="item" v-for="item in value" :key="item.id">
        <div class="time">
          <span></span>
          <div>
            <ts-button type="primary" @click="handleEdit(item)">编辑</ts-button>
            <ts-button @click="handleDel(item)">删除</ts-button>
          </div>
        </div>
        <base-details-table
          class="item-table"
          :itemDatas="itemDatas"
          labelWidth="140"
          :formDatas="item"
        >
          <template v-slot:file>
            <base-upload
              onlyRead
              :actions="['preview', 'downLoad']"
              :ref="`accidentFiles${item.id}`"
              v-model="item.accidentFiles"
            />
          </template>
          <template v-slot:accidentVehicleNo>
            <div
              :class="{
                'accident-vehicle-no': item.responsible == 1
              }"
            >
              {{ item.accidentVehicleNo }}
            </div>
          </template>
          <template v-slot:responsible>
            {{ item.responsible == 1 ? '是' : '否' }}
          </template>
        </base-details-table>
      </div>
    </div>

    <dialog-accident-records
      v-model="dialogAccidentRecords"
      :eachData="eachData"
      :openType="openType"
      @refreshTable="handleRefreshInfo"
    />
  </div>
</template>

<script>
import { vehicleDriverAccidentDelete } from '@/api/ajax/driver/index.js';
import DialogAccidentRecords from './dialog-accident-records.vue';

export default {
  components: {
    DialogAccidentRecords
  },
  props: {
    listData: {
      type: Object
    }
  },
  data() {
    return {
      eachData: {},
      openType: '',
      dialogAccidentRecords: false,

      itemDatas: [
        [
          { label: '文件号', prop: 'accidentNo' },
          { label: '肇事日期', prop: 'accidentDate' }
        ],
        [
          { label: '关联车辆', slot: 'accidentVehicleNo' },
          { label: '是否主责', slot: 'responsible' }
        ],
        [
          { label: '事故种类', prop: 'accidentType' },
          { label: '肇事地点', prop: 'accidentAddress' }
        ],
        [{ label: '详细地址', prop: 'accidentDetailAddress', colspan: 3 }],
        [{ label: '事故说明', prop: 'accidentDesc', colspan: 3 }],
        [{ label: '处理情况', prop: 'accidentSituation', colspan: 3 }],
        [{ label: '处理结果', prop: 'accidentResult', colspan: 3 }],
        [
          { label: '定损人', prop: 'accidentDamage' },
          { label: '保险赔偿金额（元）', prop: 'accidentIca' }
        ],
        [{ label: '个人赔偿金额（元）', prop: 'accidentPca' }],
        [{ label: '备注', prop: 'accidentRemark', colspan: 3, rowspan: 1 }],
        [{ label: '附件', slot: 'file', colspan: 3, rowspan: 1 }]
      ]
    };
  },
  methods: {
    handleEdit(row) {
      const data = Object.assign({}, row);

      this.eachData = data;
      this.openType = 'edit';
      this.dialogAccidentRecords = true;
    },
    async handleDel(row) {
      const { id } = row;
      try {
        await this.$confirm(`您确认删除该条数据吗`, '提示', {
          type: 'warning'
        });
        const res = await vehicleDriverAccidentDelete(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshInfo(row, 'del');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    handleRefreshInfo(data, type) {
      this.$emit('refeshDetails', data);
      if (type === 'del') {
        return;
      }
      let refName = `accidentFiles${data.id}`;
      this.$refs[refName][0].getFileList();
    }
  }
};
</script>

<style lang="scss" scoped>
.accident-vehicle-no {
  color: red;
}
.item-box {
  margin-bottom: 8px;
  .basic-tips {
    font-weight: 500;
    font-style: normal;
    color: #666666;
    margin-bottom: 0px;
  }
  .date-table-content {
    position: relative;
    .time-tips {
      position: absolute;
      left: 4px;
      top: 4px;
      > span {
        font-size: 20px;
        font-weight: 700;
        color: #333;
      }
    }
  }
  .time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  .item {
    margin-bottom: 8px;
    .item-table {
      margin-bottom: 8px;
    }
  }
}
</style>
