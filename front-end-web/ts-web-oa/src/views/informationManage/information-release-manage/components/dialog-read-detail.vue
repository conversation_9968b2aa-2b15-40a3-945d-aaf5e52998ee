<template>
  <vxe-modal
    className="dialog-read-detail"
    width="80%"
    height="500"
    title=""
    v-model="visible"
    :showFooter="false"
  >
    <template #title>
      <div class="title-left flex-center">
        <h4>阅读详情</h4>
        <ts-tabs
          class="read-detail-tabs"
          v-model="activeTab"
          @tab-click="
            () => {
              handleOperateShowList(activeTab);
            }
          "
        >
          <ts-tab-pane
            :label="`全部(${reader.rederNumber} / ${reader.totalNumber})`"
            name="0"
          />
          <ts-tab-pane
            :label="`未读(${reader.totalNumber - reader.rederNumber})`"
            name="1"
          />
          <ts-tab-pane :label="`已读(${reader.rederNumber})`" name="2" />
        </ts-tabs>
      </div>

      <div class="title-right">
        <ts-button class="primary-border-btn" @click="handleExport">
          <img class="forward" src="@/assets/img/information/forward.svg" />
          导出
        </ts-button>
        <ts-button @click="close">关闭</ts-button>
      </div>
    </template>
    <template #default>
      <div class="content">
        <div class="read-box" v-for="(item, index) in showList" :key="index">
          <p>
            {{
              item.empDeptName +
                ':' +
                item.read +
                '/' +
                (item.read + item.noread)
            }}
          </p>
          <ul class="user-list">
            <li
              v-for="user in item.users"
              :key="user.code + user.name"
              :class="{ 'is-read': user.isRead == 1 }"
              v-show="
                activeTab == '0' ||
                  (activeTab == '1' && user.isRead == 0) ||
                  (activeTab == '2' && user.isRead == 1)
              "
            >
              {{ user.name }}
            </li>
          </ul>
        </div>
      </div>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  data() {
    return {
      visible: false,

      activeTab: '0',
      reader: {},
      details: {},
      userData: [],
      showList: []
    };
  },
  methods: {
    async open({ data }) {
      this.details = deepClone(data);
      await this.handleGetReaderSituation();
      await this.handleGetReaderGetReaderList();
      this.visible = true;

      this.handleOperateShowList(this.activeTab);
    },

    handleOperateShowList(activeTab) {
      switch (activeTab) {
        case '0':
          this.showList = deepClone(this.userData);
          break;
        case '1':
          this.showList = deepClone(this.userData).filter(f => f.noread != 0);
          break;
        case '2':
          this.showList = deepClone(this.userData).filter(
            f => f.noread != f.read + f.noread
          );
          break;
      }
    },

    async handleGetReaderSituation() {
      const res = await this.ajax.informationGetReaderSituation({
        informationId: this.details.id
      });
      if (!res.success) {
        this.$message.error(res.message || '获取信息阅读情况失败!');
        return;
      }
      this.reader = res.object;
    },

    async handleGetReaderGetReaderList() {
      this.userData = [];
      const res = await this.ajax.informationGetReaderList({
        informationId: this.details.id,
        pageSize: 1000,
        pageNo: 1,
        sidx: 'create_date',
        sord: 'desc'
      });
      var data = res.rows || [];
      for (var i = 0; i < data.length; i++) {
        var item = {
          empDeptName: data[i].empDeptName,
          users: [],
          noread: 0,
          read: 0
        };
        var users = data[i].empName.split(',');
        for (var j = 0; j < users.length; j++) {
          var us = users[j].split('-');
          if (us[2] == 0) {
            item.noread++;
          } else {
            item.read++;
          }
          item.users.push({
            name: us[0],
            code: us[1],
            isRead: us[2]
          });
        }
        this.userData.push(item);
      }
    },

    handleExport() {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href =
        '/ts-information/information/exportReaderList?informationId=' +
        this.details.id;
      a.click();
    },

    close() {
      this.activeTab = '0';
      this.reader = {};
      this.details = {};
      this.userData = [];
      this.showList = [];

      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-read-detail {
  width: 100% !important;
  height: 100% !important;

  ::v-deep {
    .vxe-modal--header-right {
      display: none;
    }

    .vxe-modal--header-title {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2) !important;
      padding: 0 12px !important;
      .title-left {
        height: 100%;
        h4 {
          font-size: 16px;
          margin: 0px;
          margin-right: 8px;
        }

        .read-detail-tabs {
          transform: scale(0.9) translateX(-18px);

          .el-tabs__header {
            margin: 0px;
          }
          .el-tabs__item {
            min-width: 100px;
            width: auto;
            &:hover {
              color: $primary-blue;
            }
          }
        }
      }

      .title-right {
        .primary-border-btn {
          border-color: $theme-color;
          color: $theme-color;
          img {
            width: 16px;
            height: 16px;
            transform: translateY(-2px);

            &.forward {
              width: 14px;
              height: 14px;
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    .vxe-modal--body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;
      background-color: #fff !important;
      overflow: auto;
      .content {
        .read-box {
          padding: 8px;
          padding-right: 0px;
          padding-bottom: 0px;
          border-bottom: 1px solid #ccc;
          cursor: default;
          p {
            margin-bottom: 8px;
          }
        }
        .user-list {
          padding-left: 8px;
          margin: 0;
          li {
            display: inline-block;
            font-size: 14px;
            height: 26px;
            line-height: 26px;
            border: 1px solid #ccc;
            padding: 0 10px;
            margin-right: 8px;
            margin-bottom: 8px;
            color: #999;
            border-radius: 4px;
            min-width: 50px;
            text-align: center;
            &.is-read {
              color: #5260ff;
              border-color: rgba(82, 96, 255, 30%);
              background-color: #e5e7ff;
            }
          }
        }
      }
    }
  }
}
</style>
