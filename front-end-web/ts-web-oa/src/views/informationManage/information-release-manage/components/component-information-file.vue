<template>
  <div class="file-box" v-if="showFileList">
    <p class="title flex-space">
      相关附件
      <span
        v-if="fileList.length > 1 && showDownload"
        @click="handleClickDownAllFile"
        class="all-down"
      >
        批量下载
      </span>
    </p>
    <ul class="file-list">
      <li class="file-item" v-for="item in fileList" :key="item.id">
        <a v-show="showDownload" :href="item.fileUrl" class="file-img">
          <img src="@/assets/img/information/download.png" alt="" />
        </a>
        <div class="fileInfo">
          <p class="file-title">{{ item.name }}</p>
          <div class="flex-space">
            <p class="file-size">{{ item.size }}</p>
            <div class="action-list">
              <span class="fileLink" @click="handleViewFile(item)">
                预览
              </span>
              <a v-show="showDownload" class="action-item" :href="item.fileUrl">
                下载
              </a>
            </div>
          </div>
        </div>
      </li>
    </ul>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    />
  </div>
</template>

<script>
import { commonUtils } from '@/unit/common.js';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  props: {
    showDownload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      previewFile: '',
      previewFileList: []
    };
  },
  computed: {
    showFileList() {
      return Array.isArray(this.fileList) && this.fileList.length > 0;
    }
  },
  methods: {
    async initFile(informationId) {
      this.fileList = [];
      const res = await this.ajax.selectAccessoryByInformationId({
        informationId
      });
      if (!res.success) {
        this.$newMessage(
          'error',
          res.message || '获取信息发布管理附件失败, 请联系管理员!'
        );
        return;
      }

      let data = deepClone(res.object || []);
      if (Array.isArray(data) && data.length > 0) {
        this.fileList = data.map(m => {
          let fileUrl = `/ts-document/attachment/downloadFile/${m.id}`;
          return {
            name: m.accessoryName,
            size: (m.fileSize / 1024).toFixed(1) + 'kb',
            id: m.id,
            fileUrl
          };
        });
      }
    },
    handleViewFile(file) {
      if (commonUtils.isDoc(file.name)) {
        commonUtils.viewerDocBase(file.fileUrl, file.name);
      } else {
        this.previewFile = file.fileUrl;
        this.previewFileList = [file.fileUrl];
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    },
    handleClickDownAllFile() {
      let ids = this.fileList.map(item => item.id).join(',');
      let url =
        location.origin +
        '/ts-document/attachment/batchDownloadByIds?ids=' +
        ids;
      var alink = document.createElement('a');
      alink.href = url;
      alink.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.file-box {
  width: 300px;
  box-shadow: 0 0 5px #ddd;
  margin-bottom: 20px;
  margin-left: 20px;
  background-color: #f1f4ff;

  .title {
    height: 40px;
    padding: 0 15px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 700;
    color: #333;
    border-bottom: 1px solid #ddd;

    .all-down {
      font-size: 14px;
      color: #5260ff;
      cursor: pointer;
    }
  }

  .file-list {
    min-height: 180px;
    overflow: hidden;
  }

  .file-item {
    padding: 0 10px;
    display: flex;
    box-sizing: border-box;
    margin: 10px 0;
  }

  .fileInfo {
    flex: 1;
    overflow: hidden;
    .file-title {
      font-size: 14px;
      color: #333;
    }
    .file-size {
      font-size: 14px;
      color: #666666;
    }

    .action-item {
      cursor: pointer;
      color: #5260ff;
      font-size: 14px;
    }
    .fileLink {
      margin-right: 10px;
      font-style: normal;
      color: #5260ff;
      cursor: pointer;
    }
  }

  .file-img {
    width: 40px;
    height: 40px;
    overflow: hidden;
    img {
      width: 100%;
    }
  }
}
</style>
