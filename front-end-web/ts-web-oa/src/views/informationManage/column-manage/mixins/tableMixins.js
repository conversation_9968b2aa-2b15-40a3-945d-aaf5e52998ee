export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      searchList: [
        {
          label: '栏目名称',
          value: 'channelName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '状态',
          value: 'status',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            {
              label: '全部',
              value: '',
              element: 'ts-option'
            },
            {
              label: '启用',
              value: '1',
              element: 'ts-option'
            },
            {
              label: '禁用',
              value: '2',
              element: 'ts-option'
            }
          ]
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '栏目名称',
          align: 'center',
          prop: 'channelName'
        },
        {
          label: '创建人',
          align: 'center',
          width: 90,
          prop: 'createUserName'
        },
        {
          label: '创建日期',
          align: 'center',
          width: 155,
          prop: 'createDate'
        },
        {
          label: '发布是否需审核',
          prop: 'createNeedCheckup',
          width: 120,
          align: 'center',
          render: (h, { row }) => {
            return h('span', {}, row.createNeedCheckup == 'on' ? '是' : '否');
          }
        },
        {
          label: '修改是否需审核',
          prop: 'updateNeedCheckup',
          align: 'center',
          width: 120,
          render: (h, { row }) => {
            return h('span', {}, row.updateNeedCheckup == 'on' ? '是' : '否');
          }
        },
        {
          label: '文章',
          prop: 'channelNumber',
          align: 'center',
          width: 70,
          render: (h, { row }) => {
            return h(
              'span',
              {},
              row.channelNumber == null ? '0' : row.channelNumber
            );
          }
        },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          width: 70,
          render: (h, { row }) => {
            return h(
              'span',
              {
                style: row.status == 1 ? {} : { color: 'red' }
              },
              row.status == 1 ? '启用' : '禁用'
            );
          }
        },
        {
          label: '允许打印',
          prop: 'isPrint',
          align: 'center',
          width: 80,
          render: (h, { row }) => {
            return h(
              'span',
              {
                style: row.isPrint == 1 ? {} : { color: 'red' }
              },
              row.isPrint == 1 ? '是' : '否'
            );
          }
        },
        {
          label: '允许下载',
          prop: 'isDownload',
          align: 'center',
          width: 80,
          render: (h, { row }) => {
            return h(
              'span',
              {
                style: row.isDownload == 1 ? {} : { color: 'red' }
              },
              row.isDownload == 1 ? '是' : '否'
            );
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 170,
          prop: 'actions',
          headerSlots: 'action',
          render: (h, { row }) => {
            let changeStatusLabel = row.status == '1' ? '禁用' : '启用';

            let actions = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '设置',
                event: this.handleSetting
              },
              {
                label: changeStatusLabel,
                event: () => {
                  this.handleChangeStatus(row, changeStatusLabel);
                }
              },
              {
                label: '删除',
                className: 'red',
                event: this.handleDelete
              }
            ];
            return h('BaseActionCell', {
              props: { actions },
              on: {
                'action-select': e => e(row)
              }
            });
          }
        }
      ]
    };
  }
};
