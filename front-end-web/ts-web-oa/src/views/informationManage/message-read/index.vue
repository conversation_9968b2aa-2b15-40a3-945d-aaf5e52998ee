<template>
  <div class="information-release-manage">
    <div class="left">
      <div class="top">
        <select-column-component
          v-model="typeId"
          title="栏目分类"
          :columns="channelColumns"
          ref="SelectColumnTypeComponent"
          :allowInternalChange="false"
          :onItemClick="val => this.handleItemClick(1, val)"
        />
      </div>
      <div class="bottom">
        <select-column-component
          v-model="collectId"
          title="我的收藏"
          :columns="collectColumn"
          ref="SelectColumnCollectComponent"
          :allowInternalChange="false"
          :onItemClick="val => this.handleItemClick(2, val)"
        />
      </div>
    </div>
    <div class="right">
      <ts-tabs @tab-click="handleRefreshTable" v-model="tabState">
        <ts-tab-pane name="1">
          <span slot="label">全部</span>
        </ts-tab-pane>
        <ts-tab-pane name="2">
          <span slot="label">未读</span>
        </ts-tab-pane>
        <ts-tab-pane name="3">
          <span slot="label">已读</span>
        </ts-tab-pane>
      </ts-tabs>

      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="resetData"
      >
        <template slot="date">
          <form-date-bar v-model="searchForm.date" />
        </template>
        <template slot="right">
          <ts-button class="shallowButton" type="primary" @click="handleExport">
            导出
          </ts-button>
          <ts-button class="shallowButton" type="primary" @click="handleRead">
            标记已读
          </ts-button>
        </template>
      </ts-search-bar>

      <TsVxeTemplateTable
        id="table_information_release_statistics"
        v-loading="loading"
        class="form-table"
        ref="table"
        :columns="columns"
        :defaultSort="{
          sord: 'desc',
          sidx: 'releaseDate'
        }"
        :sort-config="{ remote: true }"
        @refresh="handleRefreshTable"
        @selection-change="handleSelectionChange"
      />
    </div>

    <dialog-information-details
      ref="DialogInformationDetails"
      @refresh="handleRefreshTable"
    />

    <dialog-add-email ref="DialogAddEmail" @refresh="handleRefreshTable" />
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import { primaryBlue } from '@/assets/css/var.scss';

import tableMixins from './mixins/tableMixins';
import DialogInformationDetails from '@/views/informationManage/information-release-manage/components/dialog-information-details.vue';
import SelectColumnComponent from '@/views/informationManage/information-release-manage/components/select-column-component.vue';
import DialogAddEmail from '@/views/email/email-management/components/dialog-add-email.vue';

export default {
  mixins: [tableMixins],
  components: {
    DialogInformationDetails,
    SelectColumnComponent,
    DialogAddEmail
  },
  data() {
    return {
      selectList: [],

      tabState: '1',

      typeId: undefined,
      collectId: undefined,
      clickColumnsType: undefined,

      channelColumns: [],
      collectColumn: [],
      localEvent: null
    };
  },

  created() {
    this.typeId = '';
    this.clickColumnsType = 1;
    this.handleGetColumnChannel();
    this.handleGetCollectColumn();
    this.$nextTick(() => {
      this.handleRefreshTable();
    });
  },

  mounted() {
    this.localEvent = this.$event.create('mainMessage');
    this.localEvent.one('informationMessageRead', this.pageEventTrigger);
    window.addEventListener('popstate', this.goBack);
  },

  destroyed() {
    this.localEvent.remove('messageToastEvent', this.pageEventTrigger);
    window.removeEventListener('popstate', this.goBack);
  },

  methods: {
    goBack() {
      this.$refs.DialogInformationDetails?.close();
    },
    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    handleSelectionChange(e) {
      this.selectList = e;
    },

    handleItemClick(type, val) {
      if (type === 1) {
        this.collectId = undefined;
      } else {
        this.typeId = undefined;
      }

      let dir = {
        1: 'typeId',
        2: 'collectId'
      };
      if (val === '') {
        this[dir[type]] = val;
      } else {
        let ids = (this[dir[type]] || '').split(',').filter(f => f);

        let findIndex = ids.indexOf(val);
        if (findIndex !== -1) {
          ids.splice(findIndex, 1);
        } else {
          ids.push(val);
        }
        this[dir[type]] = ids.join(',');
      }
      this.clickColumnsType = type;
      this.handleRefreshTable();
    },

    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    //标记为已读
    async handleRead() {
      if (this.selectList.length === 0) {
        this.$newMessage('warning', '请选择需要操作的数据!');
        return;
      }
      try {
        await this.$confirm(
          `<span style="color: ${primaryBlue}">【已读】</span>确定标记为已读吗？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'shallowButton'
          }
        );
        let ids = this.selectList.map(m => m.id).join(',');
        const res = await this.ajax.informationSetAllRead(ids);

        if (!res.success) {
          this.$newMessage('error', res.message || '操作失败, 请联系管理员!');
          return;
        }
        this.$newMessage('success', '操作成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    handleExport() {
      if (this.selectList.length > 0) {
        let ids = this.selectList.map(m => m.id).join(',');
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = '/ts-information/information/xxcyExport?ids=' + ids;
        a.click();
      } else {
        let searchForm = this.handleGetTableSearchData(),
          queryList = [],
          aDom = document.createElement('a');

        delete searchForm.pageNo;
        delete searchForm.pageSize;

        Object.keys(searchForm).map(key => {
          queryList.push(key + '=' + searchForm[key]);
        });
        aDom.href =
          '/ts-information/information/xxcyExport?' + queryList.join('&');
        aDom.click();
      }
    },

    handleForward(row) {
      const { titleColor, informationTitle, id } = row;

      const informationTitleStr =
        titleColor == '1'
          ? `<span style='color:red'>${informationTitle}</span>`
          : informationTitle;

      const content = `
          <div class='informationDetail' id='${id}'>
            推荐这篇文章给你，《${informationTitleStr}》请点击此处查看。
          </div>
        `;

      const echoData = {
        type: 'informationForward',
        forWordInformationId: id,
        subject: informationTitle,
        content
      };

      this.$refs.DialogAddEmail.open({
        title: '转发',
        type: 'informationForward',
        echoData
      });
    },

    // 查看
    handleShowDetails(data) {
      if (data.validendTime) {
        const validendTime = new Date(data.validendTime.replace(/-/g, '/'));
        if (new Date() > validendTime) {
          this.$newMessage('warning', '该条信息已过期，不能进行查看!');
          return;
        }
      }

      this.$refs.DialogInformationDetails.open({
        type: 'show',
        data,
        reader: true
      });
    },

    // 查询栏目
    async handleGetColumnChannel() {
      this.channelColumns = [];
      const res = await this.ajax.informationSelectInfoCountByChannel({
        informationStatus: 1,
        index: 5
      });
      if (!res.success) {
        this.$newMessage('error', res.message || '获取栏目失败!');
        return;
      }

      this.channelColumns = res.object || [];
      this.channelColumns.unshift({
        channelId: '',
        channelName: '全部'
      });
    },

    // 查询我的收藏
    async handleGetCollectColumn() {
      this.collectColumn = [];
      const res = await this.ajax.informationCollectGetInfoChannel();
      if (!res.success) {
        this.$newMessage('error', res.message || '获取栏目失败!');
        return;
      }

      this.collectColumn = res.object || [];
      this.collectColumn.unshift({
        channelId: '',
        channelName: '全部'
      });
    },

    handleGetTableSearchData() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { date = [] } = this.searchForm,
        [beginDate = '', endDate = ''] = date,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          beginDate,
          endDate,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord,
          informationStatus: '1',
          index: '5'
        };

      let find = this.columns.find(f => f.prop === searchForm.sidx);
      if (find) searchForm.sidx = find.sortableIndex;

      if (!searchForm.sord) {
        searchForm.sord = 'desc';
        searchForm.sidx = 'releaseDate';
      }

      searchForm.channelId =
        this.clickColumnsType === 1 ? this.typeId : this.collectId;
      searchForm.messageReadType = this.clickColumnsType === 1 ? '' : 'collect';

      searchForm.isRead =
        this.tabState === '1' ? '' : this.tabState === '2' ? 'no' : 'yes';
      delete searchForm.date;

      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });
      return deepClone(searchForm);
    },

    // 查询列表数据
    async handleRefreshTable() {
      this.loading = true;
      let searchForm = this.handleGetTableSearchData();
      let res = await this.ajax.informationList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let tableIndex = (searchForm.pageNo - 1) * searchForm.pageSize + i + 1;

        return {
          tableIndex,
          ...item
        };
      });

      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    pageEventTrigger(val) {
      let { businessId, type, path } = val;

      if (
        location.pathname !==
          '/container/ts-web-oa/information-manage/message-read' &&
        !path.includes('/message-read')
      ) {
        return;
      }

      if (type === 'information-details') {
        this.tabState = '1';
        this.handleShowDetails({ id: businessId });
      }
      if (type === 'information-select') {
        this.typeId = businessId;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.information-release-manage {
  width: 100%;
  height: 100%;
  padding: 8px 8px 0 8px;
  display: flex;
  background: #fff;
  .left {
    width: 208px;
    margin-right: 8px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    .top,
    .bottom {
      flex: 1;
      height: 100%;
      width: 100%;
      position: relative;
    }

    .bottom {
      margin-top: 8px;
      border-top: 1px solid #eee;
    }
  }
  .right {
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
    border: 0.5px solid #295cf9;
    border-radius: 4px;

    .red {
      color: red !important;
    }
    ::v-deep {
      .form-table {
        flex: 1;
        overflow: hidden;
        transform: scale(1);
        .operation-span {
          color: $primary-blue;
        }

        .info-item-intro {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }

        .message_top {
          display: inline-block;
          font-size: 12px;
          width: 22px;
          line-height: 18px;
          text-align: center;
          border: 1px solid #f59a23;
          color: #f59a23;
          margin-right: 5px;
          border-radius: 3px;
          box-sizing: border-box;
        }

        .message_vip {
          display: inline-block;
          font-size: 12px;
          width: 22px;
          line-height: 18px;
          text-align: center;
          border: 1px solid #1677ff;
          color: #1677ff;
          margin-right: 5px;
          border-radius: 3px;
          box-sizing: border-box;
        }

        .dealLink {
          color: #333;
          &:hover {
            color: $primary-blue !important;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
