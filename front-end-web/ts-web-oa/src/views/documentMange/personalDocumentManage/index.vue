<template>
  <div class="personalDocumentManage">
    <div class="tabs">
      <el-tabs v-model="activeTab" class="new-tabs-container">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :name="`${index}`"
          :label="item.label"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="content" v-if="isListOverivew">
      <div class="left">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          :apiFunction="apiFunction"
          title="文档目录"
          :showNodeNum="true"
          :params="{
            scope: 'personal',
            index: tabList[activeTab]['tabCode']
          }"
          numKey="fileNumbers"
          :activeId="this.treeNode ? this.treeNode.id : ''"
          :placeholder="'搜索文档分类'"
          @beforeClick="clickItemTree"
        >
          <template #actionBar>
            <div class="classAction" v-if="activeTab == '0'">
              <ts-button type="primary" @click="handleClassAdd">
                新增
              </ts-button>
              <ts-button type="primary" class="edit" @click="handleClassEdit">
                修改
              </ts-button>
              <ts-button type="primary" class="del" @click="handleClassDel">
                删除
              </ts-button>
              <ts-button
                type="primary"
                class="share green"
                @click="handleClassShare('share')"
              >
                分享
              </ts-button>
            </div>
          </template>
        </new-base-search-tree>
      </div>
      <div class="right">
        <my-document
          ref="myDocument"
          v-show="activeTab == '0'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <share-document
          ref="shareDocument"
          v-show="activeTab == '1'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <recover-document
          ref="recoverDocument"
          v-show="activeTab == '3'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <my-share-document
          ref="myShareDocument"
          v-show="activeTab == '2'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
      </div>
    </div>
    <div class="content" v-if="!isListOverivew">
      <overivew ref="overivew" @cutOverivew="cutOverivew" />
    </div>
    <dialog-add-class ref="dialogAddClass" @refresh="refreshTree" />
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk" />
  </div>
</template>

<script>
import myDocument from './tableComponents/my-document.vue';
import shareDocument from './tableComponents/share-document.vue';
import recoverDocument from './tableComponents/recover-document.vue';
import myShareDocument from './tableComponents/my-share-document.vue';
import dialogAddClass from './component/dialog-add-class.vue';
import overivew from './tableComponents/overivew.vue';
import { deepClone } from '@/unit/commonHandle.js';
import { VXETable } from 'vxe-table';
export default {
  components: {
    myDocument,
    shareDocument,
    recoverDocument,
    myShareDocument,
    dialogAddClass,
    overivew
  },
  data() {
    return {
      activeTab: '0',
      tabList: [
        {
          label: '我的文档',
          ref: 'myDocument',
          tabCode: 1
        },
        {
          label: '分享给我的',
          ref: 'shareDocument',
          tabCode: 2
        },
        {
          label: '我的分享',
          ref: 'myShareDocument',
          tabCode: 5
        },
        {
          label: '回收站',
          ref: 'recoverDocument',
          tabCode: 3
        }
      ],
      apiFunction: this.ajax.getChannelZTree,
      treeNode: null,
      isListOverivew: true,
      nodeList: []
    };
  },
  watch: {
    activeTab: {
      async handler(val) {
        if (this.isListOverivew) {
          this.tabRefresh();
        } else {
          this.$refs.overivew.search(val, this.tabList[val]['tabCode']);
        }
      }
    }
  },
  methods: {
    async refresh() {
      if (this.isListOverivew) {
        this.$nextTick(() => {
          if (this.treeNode) {
            setTimeout(() => {
              this.$refs.searchTree.treeClass.selectNode(this.treeNode, false);
              this.clickItemTree(this.treeNode);
            }, 100);
          } else {
            this.$refs[this.tabList[this.activeTab].ref].refresh();
          }
        });
      } else {
        this.$refs.overivew.search(
          this.activeTab,
          this.tabList[this.activeTab]['tabCode']
        );
      }
    },
    async confirt() {
      await VXETable.modal.confirm({
        className: 'new-vxe-confirm',
        title: '提示',
        content: `<p>111</p>`,
        status: 'warning',
        transfer: true,
        confirmButtonText: '确定',
        iconStatus: 'vxe-icon-warning-circle-fill'
      });
    },
    // 树 item点击
    clickItemTree(node) {
      let ref = this.tabList[this.activeTab].ref;
      this.treeNode = node;
      this.nodeList = this.getAllNodes([node], node);
      this.$nextTick(() => {
        this.$refs[ref].search(node);
      });
    },
    getAllNodes(list = [], node) {
      if (node.pid) {
        list.unshift(node.getParentNode());
        this.getAllNodes(list, node.getParentNode());
      }
      return list;
    },
    tabRefresh() {
      this.$nextTick(() => {
        this.refreshTree();
        if (this.treeNode) {
          setTimeout(() => {
            this.$refs.searchTree.treeClass.selectNode(this.treeNode, false);
            this.clickItemTree(this.treeNode);
          }, 100);
        } else {
          this.$refs[this.tabList[this.activeTab].ref].refresh();
        }
      });
    },
    handleClassShare(key) {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【分享】的节点');
        return;
      }
      let dept = ['viewableDeptName', 'viewableDeptCode'];
      let emp = ['viewablePersonName', 'viewablePersonId'];
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: true,
        showGroupCheck: false,
        isRadio: false,
        echoData: {
          viewableDeptCode: '',
          group: '',
          viewablePersonId: ''
        },
        submitKeys: {
          dept,
          group: ['', ''],
          emp
        }
      });
    },
    handleSelectUserOk(result, key) {
      switch (key) {
        case 'share':
          const {
            viewableDeptCode,
            viewableDeptName,
            viewablePersonName,
            viewablePersonId
          } = result[key];
          let param = {
            folderId: this.treeNode.id,
            sharetoUserName: viewablePersonName,
            sharetoUser: viewablePersonId,
            sharetoDept: viewableDeptCode,
            sharetoDeptName: viewableDeptName
          };
          this.ajax.attachmentShareChannel(param).then(res => {
            if (!res.success) {
              this.$newMessage('error', '【分享】失败');
              return;
            }
            this.$newMessage('success', '【分享】成功');
          });
          break;
      }
    },
    cutOverivew(isListOverivew, form) {
      this.isListOverivew = isListOverivew;
      if (this.isListOverivew) {
        if (form == '' || form == undefined) {
          this.treeNode = null;
          this.tabRefresh();
        } else {
          setTimeout(() => {
            let node = this.$refs.searchTree.treeClass.getNodeByParam(
              'id',
              form
            );
            this.$refs.searchTree.treeClass.selectNode(node, false);
            this.clickItemTree(node);
          }, 500);
        }
      } else {
        this.$nextTick(() => {
          this.$refs.overivew.refresh(
            [{ id: '', name: '文档分类' }, ...this.nodeList],
            this.activeTab,
            this.tabList[this.activeTab]['tabCode']
          );
        });
      }
    },
    // 新增分类
    handleClassAdd() {
      this.$refs.dialogAddClass.open({
        data: this.treeNode,
        type: 'add',
        title: '新增'
      });
    },
    // 修改分类
    handleClassEdit() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【修改】的节点');
        return;
      }
      this.$refs.dialogAddClass.open({
        data: this.treeNode,
        type: 'edit',
        title: '修改'
      });
    },
    // 删除分类
    async handleClassDel() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【删除】的节点');
        return;
      }
      try {
        await this.$confirm(
          `【<span style="color: red">删除</span>】'${this.treeNode.name}'及所有文件？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'ts-button shallowButton'
          }
        );
        let data = deepClone(this.treeNode);
        this.ajax
          .documentChannelDel({ id: data.id, scope: 'personal' })
          .then(res => {
            if (!res.success) {
              this.$newMessage('error', res.message || '【删除】失败!');
              return;
            }
            this.treeNode = null;
            this.$newMessage('success', '【删除】成功!');
            this.refreshTree();
          });
      } catch (e) {
        console.error(e);
      }
    },
    // 更新数结构数据
    refreshTree() {
      this.$refs.searchTree.getTreeData();
    }
  }
};
</script>

<style lang="scss" scoped>
.personalDocumentManage {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .content {
      display: flex;
      height: calc(100% - 45px);
      .left {
        width: 216px;
        height: 100%;
        margin-right: 8px;
        background: #fff;
        .classAction {
          padding: 0 4px;
          margin: 4px 0;
          display: flex;
          flex-wrap: wrap;
          .share {
            margin-left: 0;
            margin-top: 4px;
          }
        }
      }
      .right {
        padding: 12px 8px;
        border-radius: 3px;
        border: 1px solid#295cf9;
        flex: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }
}
</style>
