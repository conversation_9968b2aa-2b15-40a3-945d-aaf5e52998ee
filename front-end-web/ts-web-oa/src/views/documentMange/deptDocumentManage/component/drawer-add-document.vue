<template>
  <el-drawer
    custom-class="ts-custom-default-drawer"
    :visible.sync="visible"
    :append-to-body="true"
    close-on-press-escape
    :title="title"
    @close="close"
    size="55%"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>

    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 50px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="form" :model="form" labelWidth="110px">
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  prop="channelId"
                  label="所属目录"
                  :rules="rules.required"
                >
                  <input-tree
                    v-if="deptTreeData.length && type == 'add'"
                    v-model="form.channelId"
                    ref="tree"
                    placeholder="请选择所属分类"
                    :treeData="deptTreeData"
                    :defaultExpandedKeys="defaultExpandedKeys"
                    style="width: 100%"
                    key="id"
                    @change="treeSelect"
                  ></input-tree>
                  <ts-input v-else v-model="form.channelName" disabled />
                </ts-form-item>
              </ts-col>

              <ts-col :span="12">
                <ts-form-item
                  prop="docNo"
                  label="文档编号"
                  :rules="rules.required"
                >
                  <ts-input v-model="form.docNo" :disabled="isDetail" />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="12">
                <ts-form-item
                  prop="docTitle"
                  label="文档标题"
                  :rules="rules.required"
                >
                  <ts-input v-model="form.docTitle" :disabled="isDetail" />
                </ts-form-item>
              </ts-col>

              <ts-col :span="12">
                <ts-form-item prop="validBegintime" label="有效期">
                  <div style="display: flex;">
                    <el-date-picker
                      style="flex: 1;"
                      class="date-picker"
                      v-model="form.validBegintime"
                      value-format="yyyy-MM-dd"
                      :picker-options="pickerOptions"
                      :disabled="isDetail"
                      placeholder="开始日期"
                    >
                    </el-date-picker>
                    <span style="line-height: 30px;">&nbsp;至&nbsp;</span>
                    <el-date-picker
                      style="flex: 1;"
                      class="date-picker"
                      v-model="form.validEndtime"
                      value-format="yyyy-MM-dd"
                      placeholder="结束日期"
                      :picker-options="pickerOptions1"
                      :disabled="isDetail"
                    >
                    </el-date-picker>
                  </div>
                </ts-form-item>
                <p class="tips">'有效期'默认为永久</p>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="24">
                <ts-form-item prop="viewablePersonName" label="可查看人">
                  <ts-input
                    v-model="form.viewablePersonName"
                    readonly
                    :disabled="isDetail"
                    placeholder="默认公开"
                    @focus="handleOpenSelPerson('viewablePersonId')"
                  />
                </ts-form-item>
                <p class="tips">
                  '可查看人'为空时默认有此所属目录权限内的人查看
                </p>
              </ts-col>
            </ts-row>
            <ts-row>
              <ts-col :span="24">
                <ts-form-item prop="docType" label="文档标签">
                  <ts-select
                    style="width: 100%"
                    v-model="form.docType"
                    filterable
                    allow-create
                    default-first-option
                    :disabled="isDetail"
                    placeholder="请输入文档标签"
                  >
                    <ts-option
                      v-for="item in locationList"
                      :key="item"
                      :label="item"
                      :value="item"
                    >
                    </ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-col>
            </ts-row>
            <!-- <ts-form-item label="已分享人员" v-if="isDetail">
              <p style="margin: 0;">{{ form.sharetoUserName }}</p>
            </ts-form-item> -->
            <ts-row>
              <ts-col :span="24">
                <ts-form-item prop="isoModifyReason" label="内容摘要">
                  <ts-input
                    v-model="form.isoModifyReason"
                    :disabled="isDetail"
                    type="textarea"
                    class="textarea"
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row v-if="!isDetail">
              <ts-col :span="24">
                <ts-form-item prop="uploadedFile" label="附件">
                  <table-module-upload
                    ref="tableUpload"
                    v-model="form.uploadedFile"
                    moduleName="document"
                  >
                    <div style="display: flex;">
                      <ts-button type="primary">上传附件</ts-button>
                      <p
                        style="margin: 0;text-align: left;flex: 1;color: #f59a23;"
                      >
                        (附件大小建议不要超过{{ allowFileSize }}MB)，
                        可允许上传文件后缀为{{ allowFileExtension }}
                      </p>
                    </div>
                  </table-module-upload>
                </ts-form-item>
              </ts-col>
            </ts-row>
            <ts-row v-else>
              <ts-col :span="24">
                <ts-form-item prop="key4" label="相关附件">
                  <file-list-batch ref="fileListBatch" :fileList="fileList" />
                </ts-form-item>
              </ts-col>
            </ts-row>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button
          type="primary"
          v-if="!isDetail"
          :loading="submitLoading"
          @click="submit"
        >
          提 交
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </div>
    </div>
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk1" />
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import TableModuleUpload from '@/components/table-module-upload';
import fileListBatch from '../../component/file-list-batch.vue';
import moment from 'moment';
export default {
  components: { TableModuleUpload, fileListBatch },
  data() {
    return {
      visible: false,
      title: '创建文档',
      type: '',
      submitLoading: false,
      locationList: [],
      isDetail: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      fileList: [],
      tableFileList: [],
      deptTreeData: [],
      defaultExpandedKeys: [],
      pickerOptions: {
        disabledDate: this.disabledDate
      },
      pickerOptions1: {
        disabledDate: this.disabledDate1
      }
    };
  },
  computed: {
    allowFileSize() {
      return this.$getParentStoreInfo().globalSetting.allowFileSize || 100;
    },
    allowFileExtension() {
      return this.$getParentStoreInfo().globalSetting.allowFileExtension;
    }
  },
  methods: {
    async open({ title, data = {}, type }) {
      await this.getMyDocumentType();
      this.title = title;
      this.type = type;
      if (type == 'add') {
        await this.getTree();
        this.form.docNo = this.getCurrentDocNo();
        if (data) {
          this.form.channelId = data.id;
          this.form.channelName = data.name;
        }
      }
      if (type == 'details') {
        this.isDetail = true;
        await this.getData(data.id);
        await this.getFileList(data.id);
      }
      if (type == 'edit') {
        await this.getData(data.id);
        await this.getFileList(data.id);
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        if (type == 'edit')
          this.$refs.tableUpload.handlePushFileData(this.tableFileList);
      });
    },
    async getMyDocumentType() {
      let res = await this.ajax.getMyDocumentType();
      this.locationList = res.object || [];
    },
    async getData(id) {
      let res = await this.ajax.selectDocumentById({ docId: id });
      this.form = res.object || {};
    },
    disabledDate(time) {
      if (this.form.validEndtime) {
        if (moment(time).isAfter(moment(this.form.validEndtime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    disabledDate1(time) {
      if (this.form.validBegintime) {
        if (moment(time).isBefore(moment(this.form.validBegintime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    async getTree() {
      let param = {
        scope: 'pub',
        type: 'create'
      };
      const tree = await this.ajax.getChannelZTree(param);
      if (!tree.success) {
        this.$newMessage('error', res.message || '获取出错');
      }
      this.deptTreeData = tree.object || [];
      let ids = this.deptTreeData.map(e => e.id);
      this.defaultExpandedKeys = ids;
    },
    // 分类树选择回调
    treeSelect(val, node, tree) {
      this.form.channelName = node.data.name;
    },
    async getFileList(id) {
      let res = await this.ajax.selectDocumentAccessoryByBocId({ docId: id });
      if (res.object.length > 0) {
        this.fileList = res.object.map(e => {
          return {
            url: e.id,
            fileName: e.name,
            fileSaveName: e.saveName
          };
        });
        this.tableFileList = res.object.map(e => {
          return {
            name: e.name,
            size: e.fileSize,
            fileId: e.id,
            status: '1'
          };
        });
      }
    },
    getCurrentDocNo() {
      let no = moment().format('YYYYMMDDHHmmss');
      return no;
    },
    handleOpenSelPerson(key) {
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: true,
        showGroupCheck: false,
        isRadio: false,
        echoData: {
          viewableDeptCode: this.form.viewableDeptCode,
          group: '',
          viewablePersonId: this.form.viewablePersonId
        },
        submitKeys: {
          dept: ['viewableDeptName', 'viewableDeptCode'],
          group: ['', ''],
          emp: ['viewablePersonName', 'viewablePersonId']
        }
      });
    },
    handleSelectUserOk1(result, key) {
      switch (key) {
        case 'viewablePersonId':
          const {
            viewableDeptCode,
            allNames: viewablePersonName,
            viewablePersonId
          } = result[key];

          this.$set(this.form, 'viewableDeptCode', viewableDeptCode);
          this.$set(this.form, 'viewablePersonName', viewablePersonName);
          this.$set(this.form, 'viewablePersonId', viewablePersonId);
          break;
      }
    },
    initData() {
      this.$nextTick(() => {
        this.$refs.tableUpload.clearFileList();
      });
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.form);
        this.submitLoading = true;
        let API = this.ajax.documentSave;
        let label = '新增';
        if (this.type == 'edit') {
          label = '修改';
          API = this.ajax.documentUpdate;
        }

        const res = await API(formData);
        if (res.success && res.statusCode === 200) {
          this.submitLoading = false;
          this.$newMessage('success', `【${label}】成功`);
          this.$emit('refresh');
          if (this.type == 'add') {
            this.$emit('refreshTree');
          }
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【${label}】失败!`);
        }
      } catch (error) {
        console.error(error);
      }
    },

    close() {
      this.title = '';
      this.type = '';
      this.isDetail = false;
      this.tableFileList = [];
      this.fileList = [];
      this.$set(this, 'form', {});
      this.initData();
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.content-container {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
    .tips {
      padding-left: 110px;
      color: red;
      margin: 0;
    }
  }
}
</style>
