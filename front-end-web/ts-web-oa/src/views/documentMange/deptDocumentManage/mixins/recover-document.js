export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'createUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索工号/姓名'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '文档标题',
          value: 'docTitle',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入文档标题'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '文档标签',
          value: 'docType',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入文档标签'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '发布日期',
          value: 'completeDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let [fromDate = '', toDate = ''] =
                this.searchForm?.completeDate ?? [];
              if ((fromDate && toDate) || (!fromDate && !toDate)) this.search();
            }
          }
        }
      ],
      columns: [
        {
          type: 'seq',
          width: 40,
          align: 'center'
        },
        {
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          label: '文档标题',
          prop: 'docTitle',
          minWidth: 200,
          sortable: true,
          sortBy: 'DOC_Title',
          align: 'left',
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetails(row) }
              },
              row.docTitle
            );
          }
        },
        {
          label: '文档标签',
          prop: 'docType',
          width: 100,
          align: 'center'
        },
        {
          label: '附件大小',
          prop: 'fileSize',
          sortable: true,
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            let fileSize = (row.fileSize / 1024 / 1024).toFixed(2) + 'MB';
            return h('span', {}, fileSize);
          }
        },
        {
          label: '下载次数',
          prop: 'downloadCount',
          sortable: true,
          width: 90,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'primary-span',
                on: { click: () => this.handleUploadTabel(row) }
              },
              row.downloadCount
            );
          }
        },
        {
          label: '发布人',
          prop: 'createUserName',
          minWidth: 100,
          align: 'center'
        },
        {
          label: '发布科室',
          prop: 'createDeptName',
          width: 160,
          sortable: true,
          sortBy: 'create_dept_name',
          align: 'center'
        },
        {
          label: '所属目录',
          prop: 'channelName',
          width: 140,
          align: 'center'
        },
        {
          label: '发布时间',
          prop: 'createDate',
          width: 150,
          sortable: true,
          sortBy: 'CREATE_DATE',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 160,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '还原',
                event: this.handleBatchReturn
              },
              {
                label: '下载',
                event: this.handleDetails
              },
              {
                label: '清空',
                event: this.handleBatchClear
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      btnList: [
        {
          btnName: '批量还原',
          btnClass: 'shallowButton',
          events: this.handleBatchReturn
        },
        {
          btnName: '批量清空',
          btnClass: 'shallowButton',
          events: this.handleBatchClear
        },
        {
          btnName: '操作记录',
          btnClass: 'shallowButton',
          events: this.handleRecord
        }
      ]
    };
  },
  methods: {}
};
