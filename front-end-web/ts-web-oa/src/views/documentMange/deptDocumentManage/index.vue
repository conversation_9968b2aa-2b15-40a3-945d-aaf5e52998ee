<template>
  <div class="deptDocumentManage">
    <div class="tabs">
      <el-tabs v-model="activeTab" class="new-tabs-container">
        <el-tab-pane name="0" label="文档库"></el-tab-pane>
        <el-tab-pane name="1" label="分享库"></el-tab-pane>
        <el-tab-pane name="2" label="归档库"></el-tab-pane>
        <el-tab-pane name="3" label="回收站"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="content" v-if="isListOverivew">
      <div class="left">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          :apiFunction="apiFunction"
          title="文档目录"
          :showNodeNum="true"
          :activeId="treeNode ? treeNode.id : ''"
          :params="{
            scope: 'pub',
            index: tabCode
          }"
          numKey="fileNumbers"
          :placeholder="'搜索文档分类'"
          @beforeClick="clickItemTree"
        >
          <template #actionBar>
            <div class="classAction" v-if="activeTab == '0'">
              <ts-button
                type="primary"
                @click="handleClassAdd"
                v-if="isDocAdmin || hasOperateBtn.includes('addCatalogue')"
              >
                新增
              </ts-button>
              <ts-button
                type="primary"
                class="edit"
                @click="handleClassEdit"
                v-if="isDocAdmin || hasOperateBtn.includes('editCatalogue')"
              >
                修改
              </ts-button>
              <ts-button
                type="primary"
                class="del"
                @click="handleClassDel"
                v-if="isDocAdmin || hasOperateBtn.includes('deleteCatalogue')"
              >
                删除
              </ts-button>
              <ts-button
                type="primary"
                class="share green"
                @click="handleClassShare('share')"
              >
                分享
              </ts-button>
              <ts-button
                type="primary"
                class="refresh shall"
                @click="syncDocumentChannel"
                v-if="isAdmin || hasOperateBtn.includes('syncCatalogue')"
              >
                同步
              </ts-button>
            </div>
          </template>
        </new-base-search-tree>
      </div>
      <div class="right">
        <dept-document
          ref="deptDocument"
          v-show="activeTab == '0'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <share-document
          ref="shareDocument"
          v-show="activeTab == '1'"
          :isShowTab="activeTab == '1'"
          :subActiveTab="subActiveTab"
          @change="shareDocumentChange"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <keeponfile-document
          ref="keeponfileDocument"
          v-show="activeTab == '2'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
        <recover-document
          ref="recoverDocument"
          v-show="activeTab == '3'"
          @cutOverivew="cutOverivew"
          @refreshTree="refreshTree"
        />
      </div>
    </div>
    <div class="content" v-if="!isListOverivew">
      <overivew
        ref="overivew"
        :isShowTab="activeTab == '1'"
        :isDocAdmin="isDocAdmin"
        :subActiveTab="subActiveTab"
        @change="shareDocumentChange"
        @cutOverivew="cutOverivew"
      />
    </div>
    <dialog-add-class ref="dialogAddClass" @refresh="refreshTree" />
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk" />
  </div>
</template>

<script>
import deptDocument from './tableComponents/dept-document.vue';
import shareDocument from './tableComponents/share-document.vue';
import keeponfileDocument from './tableComponents/keeponfile-document.vue';
import recoverDocument from './tableComponents/recover-document.vue';
import dialogAddClass from './component/dialog-add-class.vue';
import overivew from './tableComponents/overivew.vue';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  components: {
    deptDocument,
    shareDocument,
    keeponfileDocument,
    recoverDocument,
    dialogAddClass,
    overivew
  },
  data() {
    return {
      activeTab: '0',
      subActiveTab: '0', //分享库中tab页签索引
      apiFunction: this.ajax.getChannelZTree,
      treeNode: null,
      refsList: [
        'deptDocument',
        'shareDocument',
        'keeponfileDocument',
        'recoverDocument'
      ],
      isListOverivew: true,
      isDocAdmin: false,
      nodeList: []
    };
  },
  watch: {
    activeTab: {
      handler(val) {
        if (this.isListOverivew) {
          if (val == '1') {
            this.subActiveTab = this.$refs.shareDocument.activeTab;
            this.tabCode = Number(this.subActiveTab) == 0 ? 2 : 5;
          } else this.tabCode = Number(val) + 1;
          this.tabRefresh();
        } else {
          if (val == '1') {
            this.subActiveTab = this.$refs.overivew.activeTab;
            this.tabCode = Number(this.subActiveTab) == 0 ? 2 : 5;
          } else this.tabCode = Number(val) + 1;
          this.$refs.overivew.search(val);
        }
      },
      immediate: true
    },
    subActiveTab: {
      handler(val) {
        this.tabCode = Number(val) == 0 ? 2 : 5;
        if (this.isListOverivew) {
          this.tabRefresh();
        } else {
          this.$refs.overivew.search();
        }
      }
    }
  },
  computed: {
    isAdmin() {
      let name = this.$getParentStoreInfo('userInfo').employeeNo;
      return name == 'admin' || name == 'master';
    },
    hasOperateBtn() {
      return this.menuLimits.map(m => m.resourceId);
    },
    tabCode() {
      return Number(this.activeTab) + 1;
    }
  },
  methods: {
    async refresh() {
      let res = await this.ajax.documentIsDocAdmin();
      this.isDocAdmin = res.object == 'true' ? true : false;
    },
    // 树 item点击
    clickItemTree(node) {
      let ref = this.refsList[this.activeTab];
      this.treeNode = node;
      this.nodeList = this.getAllNodes([node], node);
      this.$nextTick(() => {
        this.$refs[ref].search(node);
      });
    },
    getAllNodes(list = [], node) {
      if (node.pid) {
        list.unshift(node.getParentNode());
        this.getAllNodes(list, node.getParentNode());
      }
      return list;
    },
    async syncDocumentChannel() {
      let res = await this.ajax.syncDocumentChannel();
      if (res.success) {
        this.$newMessage('success', '【同步】成功');
      } else {
        this.$newMessage('error', res.message || '【同步】失败');
        return;
      }
      this.refreshTree();
    },
    tabRefresh() {
      this.$nextTick(() => {
        this.refreshTree();
        if (this.treeNode) {
          setTimeout(() => {
            this.$refs.searchTree.treeClass.selectNode(this.treeNode, false);
            this.clickItemTree(this.treeNode);
          }, 100);
        } else {
          this.$refs[this.refsList[this.activeTab]].refresh();
        }
      });
    },
    handleClassShare(key) {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【分享】的节点');
        return;
      }
      let dept = ['viewableDeptName', 'viewableDeptCode'];
      let emp = ['viewablePersonName', 'viewablePersonId'];
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        isRadio: false,
        echoData: {
          viewableDeptCode: '',
          group: '',
          viewablePersonId: ''
        },
        submitKeys: {
          dept,
          group: ['', ''],
          emp
        }
      });
    },
    handleSelectUserOk(result, key) {
      switch (key) {
        case 'share':
          const {
            viewableDeptCode,
            allNames: viewablePersonName,
            viewablePersonId
          } = result[key];
          let param = {
            channelId: this.treeNode.id,
            sharetoUserName: viewablePersonName,
            sharetoUser: viewablePersonId,
            sharetoDept: viewableDeptCode
          };
          this.ajax.shareChannelDocument(param).then(res => {
            if (!res.success) {
              this.$newMessage('error', '【分享】失败');
              return;
            }
            this.$newMessage('success', '【分享】成功');
          });
          break;
      }
    },
    cutOverivew(isListOverivew, form) {
      this.isListOverivew = isListOverivew;
      this.refresh();
      if (this.isListOverivew) {
        if (this.activeTab == '1') {
          this.tabCode = Number(this.subActiveTab) == 0 ? 2 : 5;
        } else this.tabCode = Number(this.activeTab) + 1;
        if (form == '') {
          this.tabRefresh();
        } else {
          setTimeout(() => {
            let node = this.$refs.searchTree.treeClass.getNodeByParam(
              'id',
              form
            );
            this.$refs.searchTree.treeClass.selectNode(node, false);
            this.clickItemTree(node);
          }, 500);
        }
      } else {
        this.$nextTick(() => {
          this.$refs.overivew.refresh(
            [{ id: '', name: '文档分类' }, ...this.nodeList],
            this.activeTab
          );
        });
      }
    },
    shareDocumentChange(index) {
      this.subActiveTab = index;
    },
    // 新增分类
    handleClassAdd() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【操作】的节点');
        return;
      }
      // if (this.treeNode.scope == 'org') {
      //   this.$newMessage(
      //     'warning',
      //     '【新增】失败，组织架构下的科室目录无法新增'
      //   );
      //   return;
      // }
      this.$refs.dialogAddClass.open({
        data: this.treeNode,
        type: 'add',
        title: '新增'
      });
    },
    // 修改分类
    handleClassEdit() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【修改】的节点');
        return;
      }
      this.$refs.dialogAddClass.open({
        data: this.treeNode,
        type: 'edit',
        title: '修改'
      });
    },
    // 删除分类
    async handleClassDel() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【删除】的节点');
        return;
      }
      if (this.treeNode.scope == 'org') {
        this.$newMessage(
          'warning',
          '【删除】失败，组织架构下的科室目录无法删除'
        );
        return;
      }
      try {
        await this.$confirm(
          `【<span style="color: red">删除</span>】'${this.treeNode.name}'及所有文件？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'ts-button shallowButton'
          }
        );
        let data = deepClone(this.treeNode);
        this.ajax
          .documentChannelDel({ id: data.id, scope: 'pub' })
          .then(res => {
            if (!res.success) {
              this.$newMessage('error', res.message || '【删除】失败!');
              return;
            }
            this.$newMessage('success', '【删除】成功!');
            this.treeNode = null;
            this.refreshTree();
          });
      } catch (e) {
        console.error(e);
      }
    },
    // 更新数结构数据
    refreshTree() {
      this.$refs.searchTree.getTreeData();
    }
  }
};
</script>

<style lang="scss" scoped>
.deptDocumentManage {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .content {
      display: flex;
      height: calc(100% - 45px);
      .left {
        width: 216px;
        height: 100%;
        margin-right: 8px;
        background: #fff;
        .classAction {
          padding: 0 4px;
          margin: 4px 0;
          display: flex;
          flex-wrap: wrap;
          .share {
            margin-left: 0;
            margin-top: 4px;
          }
          .refresh {
            margin-top: 4px;
            margin-left: 8px;
          }
        }
      }
      .right {
        padding: 12px 8px;
        border-radius: 3px;
        border: 1px solid#295cf9;
        flex: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }
}
</style>
