<template>
  <vxe-modal
    className="dialog-registration-info"
    width="auto"
    height="80%"
    title="预约挂号信息"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <p class="prompt-text">
          <span>挂号日期：{{ form.scheduleDate }}</span>
          <span>挂号医生：{{ form.doctorName }}</span>
        </p>
        <TsVxeTemplateTable
          id="table_registration-info"
          ref="table"
          :hasPage="false"
          :columns="columns"
          @refresh="handleRefreshTable"
        />
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      form: {},
      columns: [
        {
          label: '排队号',
          prop: 'queueNo',
          align: 'center',
          width: 100
        },
        {
          label: '姓名',
          prop: 'patientName',
          width: 100,
          align: 'center'
        },
        {
          label: '性别',
          prop: 'sexName',
          align: 'center',
          width: 80
        },
        {
          label: '年龄',
          prop: 'age',
          width: 80,
          align: 'center'
        },
        {
          label: '门诊号',
          prop: 'visitNo',
          width: 120,
          align: 'center'
        },
        {
          label: '挂号来源',
          prop: 'regSource',
          align: 'center',
          width: 120
          // render: (h, { row }) => {
          //   let regSourceList = {
          //     0: '未知',
          //     1: '窗口',
          //     2: '自助设备',
          //     3: '移动终端',
          //     4: '医生站',
          //     12: '互联网医院',
          //     88: '体检',
          //     99: '转诊'
          //   };
          //   return <span>{regSourceList[row.regSource]}</span>;
          // }
        },
        {
          label: '联系电话',
          prop: 'mobile',
          width: 120,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    open({ data = {} }) {
      this.form = data;
      this.visible = true;
      this.handleRefreshTable(data);
    },
    async handleRefreshTable(data) {
      let res = await this.ajax.getDoctorShedule(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '数据获取失败!');
        return;
      }
      this.$refs.table.refresh({
        rows: res.object
      });
    },
    close() {
      this.form = {};
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .prompt-text {
    line-height: 1;
    padding-left: 8px;
    border-left: 4px solid $primary-blue;
    span {
      margin-right: 16px;
    }
  }
}
</style>
