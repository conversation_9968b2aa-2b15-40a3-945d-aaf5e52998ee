const tabSearchList = [
  ['检查日期', '检查路线', '所属项目'],
  [
    '检查日期',
    '检查路线',
    '是否达标',
    '检查人',
    '核查人',
    '被检查科室',
    '所属项目'
  ],
  ['检查日期', '检查路线', '检查人', '核查人', '被检查科室', '所属项目'],
  ['检查日期', '检查路线', '检查人', '核查人', '被检查科室', '所属项目'],
  [
    '检查日期',
    '检查路线',
    '检查人',
    '核查人',
    '是否整改',
    '被检查科室',
    '所属项目'
  ]
];
export default {
  data() {
    return {
      searchForm: {},
      searchAllList: [
        {
          label: '检查日期',
          value: 'checkDate'
        },
        {
          label: '检查路线',
          value: 'checkRoutes',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择检查路线',
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '是否达标',
          value: 'isStandard',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择是否达标',
            clearable: true
          },
          childNodeList: [
            { label: '是', value: '0', element: 'ts-option' },
            { label: '否', value: '1', element: 'ts-option' }
          ]
        },
        {
          label: '检查人',
          value: 'checkUser',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入核查人'
          }
        },
        {
          label: '核查人',
          value: 'examineUser',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入核查人'
          }
        },
        {
          label: '是否整改',
          value: 'isRectification',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择是否整改',
            clearable: true
          },
          childNodeList: [
            { label: '是', value: '0', element: 'ts-option' },
            { label: '否', value: '1', element: 'ts-option' }
          ]
        },
        {
          label: '被检查科室',
          value: 'inspectedDepartmentNameSearch',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入被检查科室'
          }
        },
        {
          label: '所属项目',
          value: 'projectName',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择所属项目'
          },
          childNodeList: []
        }
      ],
      searchActions: [
        {
          label: '发起检查',
          click: this.handleStartCheck,
          prop: { type: 'primary' }
        },
        {
          label: '导出',
          click: this.handleExport
        }
      ]
    };
  },
  computed: {
    columns() {
      return [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 50,
          fixed: 'left'
        },
        {
          label: '检查日期',
          prop: 'checkDate',
          align: 'center',
          width: 90,
          fixed: 'left'
        },
        {
          label: '所属项目',
          prop: 'projectName',
          align: 'center',
          width: 120,
          fixed: 'left'
        },
        {
          label: '检查路线',
          prop: 'routeName',
          align: 'left',
          width: 200,
          fixed: 'left',
          render: (h, { row }) => {
            return h(
              'span',
              this.activeTab == 3 || this.activeTab == 4
                ? {
                    class: 'action-cell',
                    on: {
                      click: () => {
                        this.showDetail(row);
                      }
                    }
                  }
                : {},
              row.routeName
            );
          }
        },
        {
          label: '检查地点',
          prop: 'checkAddress',
          align: 'left',
          width: 140
        },
        {
          label: '被检查科室',
          prop: 'inspectedDepartmentName',
          align: 'center',
          width: 160,
          visible: this.activeTab != 3
        },
        {
          label: '检查人',
          prop: 'checkUserName',
          align: 'center',
          width: 100
        },
        {
          label: '核查人',
          prop: 'examineUserName',
          align: 'center',
          width: 100
        },
        {
          label: '其他人员',
          prop: 'otherUserName',
          align: 'center',
          width: 100
        },
        {
          label: '检查结果',
          prop: 'checkResult',
          align: 'center',
          width: 100,
          visible: this.activeTab != 0 && this.activeTab != 4,
          render: (h, { row }) => {
            return (
              <span style={{ color: row.unqualifiedCount > 0 ? 'red' : '' }}>
                {row.unqualifiedCount > 0
                  ? row.unqualifiedCount + '项不达标'
                  : '全部达标'}
              </span>
            );
          }
        },
        {
          label: '不通过原因',
          prop: 'remark',
          align: 'center',
          width: 100,
          visible: this.activeTab == 2
        },
        {
          label: '交办人',
          prop: 'assignUserDeptName',
          align: 'center',
          visible: this.activeTab == 3
        },
        {
          label: '是否整改',
          prop: 'isRectification',
          align: 'center',
          width: 100,
          visible: this.activeTab == 4,
          render: (h, { row }) => {
            return <span>{row.unqualifiedCount > 0 ? '是' : '否'}</span>;
          }
        },
        {
          label: '整改项',
          prop: 'unqualifiedCount',
          align: 'center',
          width: 100,
          visible: this.activeTab == 4
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center',
          width: 130,
          visible: this.activeTab == 0,
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.createDate).format('YYYY-MM-DD hh:mm')}
              </span>
            );
          }
        },
        {
          label: '办结时间',
          prop: 'updateDate',
          align: 'center',
          width: 130,
          visible: this.activeTab == 4,
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.updateDate).format('YYYY-MM-DD hh:mm')}
              </span>
            );
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 90,
          headerSlots: 'action',
          fixed: 'right',
          // visible: this.activeTab != 3,
          render: (h, { row }) => {
            let actionList = [];
            if (this.activeTab == 0 || this.activeTab == 2) {
              actionList.push({
                label: '编辑',
                event: this.handleAddOrEdit
              });
            }
            if (this.activeTab == 1) {
              actionList.push({
                label: '确认',
                event: this.handleConfirm
              });
            }
            if (this.activeTab == 4) {
              actionList.push({
                label: '打印',
                event: this.handlePrint
              });
            }
            // if (this.activeTab == 0 || this.activeTab == 4) {
            //   actionList.push({
            //     label: '删除',
            //     event: this.handleDelete
            //   });
            // }
            actionList.push({
              label: '删除',
              event: this.handleDelete
            });

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ];
    },
    searchList() {
      let tabSearch = tabSearchList[Number(this.activeTab)];
      return this.searchAllList.filter(item =>
        tabSearch.some(i => i == item.label)
      );
    }
  },
  created() {
    this.getSafetyRouteList();
    this.getProjectDicData();
  },
  methods: {
    getProjectDicData() {
      this.ajax.getDataByDataLibrary('PATROL_PROJECT').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let rows = res.object || [];
        let packageList = rows.map(item => {
          return {
            label: item.itemName,
            value: item.itemName,
            element: 'ts-option'
          };
        });
        this.searchAllList.find(
          item => item.value === 'projectName'
        ).childNodeList = packageList;
      });
    },
    getSafetyRouteList() {
      this.ajax
        .getSafetyRouteList({
          page: 1,
          pageSize: 9999,
          sidx: 'create_date',
          sord: 'asc'
        })
        .then(res => {
          if (res.success == false) {
            this.$message.error(res.message || '表格数据获取失败');
            return;
          }
          let packageList = res.rows.map(item => {
            return {
              label: item.routeName,
              value: item.id,
              element: 'ts-option'
            };
          });
          this.searchAllList.find(
            item => item.value === 'checkRoutes'
          ).childNodeList = packageList;
        });
    }
  }
};
