export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '检查日期',
          value: 'checkDate'
        },
        {
          label: '检查地点',
          value: 'checkAddress',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入检查地点'
          }
        },
        {
          label: '检查内容',
          value: 'checkContent',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入检查内容'
          }
        },
        {
          label: '检查人',
          value: 'checkUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入检查人'
          }
        },
        {
          label: '被检查科室',
          value: 'inspectedDepartmentNameSearch',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入被检查科室'
          }
        },
        {
          label: '交办人',
          value: 'assignUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入交办人'
          }
        }
      ],
      actions: [
        {
          label: '导出',
          click: this.handleExport
        }
      ]
    };
  },
  computed: {
    searchActions() {
      let arr = [
        {
          label: '完成整改',
          click: this.handleCompleteRectification,
          prop: { type: 'primary' }
        }
      ];
      return this.activeTab == 0 ? [...arr, ...this.actions] : this.actions;
    },
    columns() {
      return [
        {
          type: 'checkbox',
          align: 'center',
          width: 50,
          fixed: 'left',
          visible: this.activeTab == 0
        },
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 50,
          fixed: 'left'
        },
        {
          label: '检查日期',
          prop: 'checkDate',
          align: 'center',
          width: 90,
          fixed: 'left'
        },
        {
          label: '所属项目',
          prop: 'projectName',
          align: 'center',
          width: 120,
          fixed: 'left'
        },
        {
          label: '检查地点',
          prop: 'checkAddress',
          align: 'left',
          width: 120,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.showDetail(row);
                  }
                }
              },
              row.checkAddress
            );
          }
        },
        {
          label: '被检查科室',
          prop: 'inspectedDepartmentName',
          align: 'center',
          width: 160
        },
        {
          label: '检查内容',
          prop: 'checkContent',
          align: 'left',
          width: 180
        },
        {
          label: '问题描述',
          prop: 'unqualifiedRemark',
          align: 'left',
          width: 180,
          visible: this.activeTab != 1
        },
        {
          label: '整改说明',
          prop: 'rectificationRemark',
          align: 'left',
          width: 180,
          visible: this.activeTab != 0
        },
        {
          label: '最近整改',
          prop: 'updateDate',
          align: 'center',
          width: 140,
          visible: this.activeTab != 0,
          render: (h, { row }) => {
            return (
              <span>
                {row.updateDate
                  ? this.$dayjs(row.updateDate).format('YYYY-MM-DD HH:mm')
                  : ''}
              </span>
            );
          }
        },
        {
          label: '检查人',
          prop: 'checkUserName',
          align: 'center',
          width: 100
        },
        {
          label: '核查人',
          prop: 'examineUserName',
          align: 'center',
          width: 100
        },
        {
          label: '交办人',
          prop: 'assignUserName',
          align: 'center',
          width: 100
        },
        {
          label: '操作',
          align: 'center',
          width: 90,
          headerSlots: 'action',
          fixed: 'right',
          visible: this.activeTab != 3,
          render: (h, { row }) => {
            let actionList = [];
            if (this.activeTab == 0) {
              actionList.push({
                label: '完成整改',
                event: this.handleCompleteRectification
              });
            } else if (this.activeTab == 1) {
              actionList.push({
                label: '复查',
                event: this.handleReexamine
              });
            }
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ];
    }
  }
};
