<template>
  <vxe-modal
    className="dialog-archive-dossier"
    width="680"
    height="560"
    :title="title"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="80px">
          <ts-form-item
            label="标题"
            prop="dossierTitle"
            :rules="rules.required"
          >
            <ts-input
              v-model="form.dossierTitle"
              :placeholder="disabled ? '' : '请输入标题'"
              maxlength="20"
              :disabled="disabled"
            />
          </ts-form-item>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item label="关键字" prop="dossierKeyWord">
                <ts-input
                  v-model="form.dossierKeyWord"
                  :placeholder="disabled ? '' : '请输入关键字'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="文号" prop="dossierFileNum">
                <ts-input
                  v-model="form.dossierFileNum"
                  :placeholder="disabled ? '' : '请输入文号'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="关联文号" prop="dossierAssofileNum">
                <ts-input
                  v-model="form.dossierAssofileNum"
                  :placeholder="disabled ? '' : '请输入关联文号'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="主办部门" prop="">
                <ts-input
                  v-model="form.dossierTransorgName"
                  :placeholder="disabled ? '' : '请输入主办部门'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="件号" prop="dossierDocNum">
                <ts-input
                  v-model="form.dossierDocNum"
                  :placeholder="disabled ? '' : '请输入件号'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="页数" prop="dossierPageNum">
                <ts-input
                  v-model="form.dossierPageNum"
                  :placeholder="disabled ? '' : '请输入页数'"
                  maxlength="20"
                  :disabled="disabled"
                  @input="
                    validateIntlacesNegative($event, 'form', 'dossierPageNum')
                  "
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="档案类型" prop="dossierType">
                <ts-select
                  style="width: 100%"
                  v-model="form.dossierType"
                  clearable
                  :placeholder="disabled ? '' : '请选择档案类型'"
                  :disabled="disabled"
                >
                  <ts-option
                    v-for="item of dossierTypeOptions"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="所属类目" prop="dossierCategoryId">
                <ts-select
                  style="width: 100%"
                  v-model="form.dossierCategoryId"
                  clearable
                  :placeholder="disabled ? '' : '请选择所属类目'"
                  :disabled="disabled"
                >
                  <ts-option
                    v-for="item of dossierCategoryOptions"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="密级" prop="dossierSecretlevel">
                <ts-select
                  style="width: 100%"
                  v-model="form.dossierSecretlevel"
                  clearable
                  :placeholder="disabled ? '' : '请选择密级'"
                  :disabled="disabled"
                >
                  <ts-option
                    v-for="item of dossierSecretlevelOptions"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="文件年度" prop="dossierYear">
                <ts-date-picker
                  style="width: 100%"
                  v-model="form.dossierYear"
                  mode="year"
                  format="YYYY年"
                  valueFormat="YYYY"
                  :placeholder="disabled ? '' : '请选择文件年度'"
                  :disabled="disabled"
                  :open="isopen"
                  @openChange="handYearChange"
                  @panelChange="panelYearChange"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="签发日期" prop="dossierAssignDate">
                <ts-date-picker
                  style="width: 100%"
                  v-model="form.dossierAssignDate"
                  valueFormat="YYYY-MM-DD"
                  :placeholder="disabled ? '' : '请选择签发日期'"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="发文日期" prop="dossierSendDate">
                <ts-date-picker
                  style="width: 100%"
                  v-model="form.dossierSendDate"
                  valueFormat="YYYY-MM-DD"
                  :placeholder="disabled ? '' : '请选择发文日期'"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="保管期限" prop="dossierKeepLimit">
                <ts-select
                  style="width: 100%"
                  v-model="form.dossierKeepLimit"
                  clearable
                  :placeholder="disabled ? '' : '请选择保管期限'"
                  :disabled="disabled"
                >
                  <ts-option
                    v-for="item of dossierKeepLimitOptions"
                    :key="item.itemNameValue"
                    :label="item.itemName"
                    :value="item.itemNameValue"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="责任者" prop="dossierDuty">
                <ts-input
                  v-model="form.dossierDuty"
                  :placeholder="disabled ? '' : '请输入责任者'"
                  maxlength="20"
                  :disabled="disabled"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-form-item label="文档链接">
            <span class="dossier-link" @click="handleCheckWorkflow">
              点击跳转
            </span>
          </ts-form-item>
          <ts-form-item label="相关附件">
            <table-module-upload
              v-if="!disabled"
              ref="fileUpload"
              v-model="form.uploadedFile"
              drag
              dragTips="点击上传或将文件拖拽到此处"
            />
            <file-list-batch v-else ref="fileListBatch" :fileList="fileList" />
          </ts-form-item>
        </ts-form>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button
          v-if="!disabled"
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          提 交
        </ts-button>
        <ts-button
          class="shallowButton"
          :disabled="submitLoading"
          @click="close"
          >关 闭</ts-button
        >
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import TableModuleUpload from '@/components/table-module-upload';
import FileListBatch from '@/components/file-list-batch/index.vue';
import { deepClone, inputIntPlacesNegative } from '@/unit/commonHandle.js';
import moment from 'moment';

export default {
  components: {
    TableModuleUpload,
    FileListBatch
  },
  data() {
    return {
      visible: false,
      isopen: false,
      type: '',
      title: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      submitLoading: false,
      fileList: [],
      tableFileList: [],
      dossierTypeOptions: [
        {
          itemName: '文书档案',
          itemNameValue: '1'
        },
        {
          itemName: '电子档案',
          itemNameValue: '2'
        },
        {
          itemName: '基建档案',
          itemNameValue: '3'
        },
        {
          itemName: '会计档案',
          itemNameValue: '4'
        },
        {
          itemName: '业务档案',
          itemNameValue: '5'
        },
        {
          itemName: '设备档案',
          itemNameValue: '6'
        },
        {
          itemName: '录音档案',
          itemNameValue: '7'
        },
        {
          itemName: '实物档案',
          itemNameValue: '8'
        }
      ],
      dossierCategoryOptions: [],
      dossierSecretlevelOptions: [
        {
          itemName: '一般',
          itemNameValue: '1'
        },
        {
          itemName: '秘密',
          itemNameValue: '2'
        },
        {
          itemName: '机密',
          itemNameValue: '3'
        },
        {
          itemName: '绝密',
          itemNameValue: '4'
        }
      ],
      dossierKeepLimitOptions: [
        {
          itemName: '永久',
          itemNameValue: '1'
        },
        {
          itemName: '长期',
          itemNameValue: '2'
        },
        {
          itemName: '短期',
          itemNameValue: '3'
        }
      ]
    };
  },
  computed: {
    disabled() {
      return this.type == 'check';
    }
  },
  methods: {
    validateIntlacesNegative(value, obj, attr) {
      let newVal = inputIntPlacesNegative(value);
      this.$set(this[obj], attr, newVal);
    },
    async handleGetDossierCategory() {
      let res = await this.ajax.getDossierCategoryList({
        pageNo: 1,
        pageSize: 1000
      });
      if (res.success == false) {
        this.$newMessage('error', res.message || '数据获取失败!');
        return;
      }
      this.dossierCategoryOptions = res.rows.map(i => ({
        itemName: i.categoryName,
        itemNameValue: i.id
      }));
    },
    handYearChange(open) {
      if (open) {
        this.isopen = true;
      } else {
        this.isopen = false;
      }
    },
    panelYearChange(value) {
      this.form.dossierYear = moment(value).format('YYYY');
      this.isopen = false;
    },
    async handleCheckWorkflow() {
      let { workflowNo, businessId, wfInstanceId } = this.form.workflowData;
      let res = await this.ajax.getDefinitionByWorkFlowNo(workflowNo);
      if (res.success == false) {
        this.$newMessage('error', res.message || '数据获取失败!');
        return;
      }
      if (res.object && res.object.isNormal == 'N') {
        let url = encodeURI(
          `/view-new/processView/see/index.html?workflowNo=${workflowNo}&businessId=${businessId}&currentStepName=&wfInstanceId=${wfInstanceId}&currentStepNo=end&role=deal&type=see`
        );
        window.open(url);
      } else if (res.object && res.object.isNormal == 'Y') {
        if (res.object.examinePageUrl.startsWith('/ts-web')) {
          this.handleNewFrameJump(this.form.workflowData, res);
        }
      } else {
        this.$newMessage('error', '数据获取失败!');
      }
    },
    handleNewFrameJump(data, res) {
      this.$devopParentTypeFun({
        type: 'changePath',
        data: res.object.examinePageUrl
      });

      // 等待父级页面加载完成 执行完 默认的refesh 再查询
      setTimeout(() => {
        this.$devopParentTypeFun({
          type: 'broadcastInformation',
          event: 'messageToastEvent',
          data: {
            path: res.object.examinePageUrl,
            type: 2,
            businessId: data.businessId,
            data
          }
        });
      }, 1000);
    },
    open({ data = {}, title, type }) {
      this.handleGetDossierCategory();
      this.title = title;
      this.type = type;
      this.getFileList(data.id);
      this.form = deepClone(data);
      this.$nextTick(() => {
        this.$refs?.form.clearValidate();
        this.$refs?.fileUpload?.handlePushFileData(this.tableFileList);
      });
      this.visible = true;
    },
    async getFileList(id) {
      let res = await this.ajax.getDossierAccessoryByDosId({ dosId: id });
      if (res.object.length > 0) {
        this.fileList = res.object.map(e => {
          return {
            url: e.id,
            fileName: e.accessoryName,
            fileSaveName: e.saveName
          };
        });
        this.tableFileList = res.object.map(e => {
          return {
            name: e.accessoryName,
            size: e.fileSize,
            fileId: e.id,
            status: '1'
          };
        });
      }
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        delete data.workflowData;
        this.submitLoading = true;
        let API = null;
        if (data.id) {
          API = this.ajax.updateExternalFile;
        } else {
          API = this.ajax.saveExternalFile;
          delete d.id;
        }
        const res = await API(data);
        this.submitLoading = false;
        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '操作成功!');
          this.close();
          this.$emit('success');
        } else {
          this.$newMessage('error', res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.$refs.fileUpload?.clearFileList();
      this.fileList = [];
      this.tableFileList = [];
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-archive-dossier {
  .content {
    height: 100%;
    overflow: auto;
  }
  .dossier-link {
    cursor: pointer;
    color: $primary-blue;
  }
}
</style>
