<template>
  <div class="basic-inform-of-party-member" v-loading="loading">
    <ts-search-bar
      v-model="searchForm"
      ref="SearchBar"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="right">
        <ts-button type="primary" @click="handleExport">
          导出
        </ts-button>
      </template>
    </ts-search-bar>
    <base-table ref="table" :has-page="false" @refresh="handleRefreshTable">
      <ts-table-column
        align="center"
        prop="dyzs"
        label="党员总数"
        width="80"
        fixed
      ></ts-table-column>
      <ts-table-column
        prop="xb_nv"
        label="女"
        width="50"
        align="center"
        fixed
      />
      <ts-table-column
        prop="xb_nan"
        label="男"
        width="50"
        align="center"
        fixed
      />
      <ts-table-column
        prop="ssmz"
        label="少数民族"
        width="90"
        align="center"
        fixed
      />
      <ts-table-column label="年龄">
        <ts-table-column
          prop="nl_25"
          label="25岁及以下"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_26"
          label="26岁-30岁"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_31"
          label="31岁-35岁"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_36"
          label="36岁-40岁"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_41"
          label="41岁-45岁"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_46"
          label="46岁-60岁"
          width="120"
          align="center"
        />
        <ts-table-column
          prop="nl_61"
          label="61岁及以上"
          width="120"
          align="center"
        />
      </ts-table-column>
      <ts-table-column label="学历">
        <!-- <ts-table-column align="center" prop="xl_gp" label="规培" width="120" /> -->
        <ts-table-column align="center" prop="xl_bs" label="博士" width="120" />
        <ts-table-column align="center" prop="xl_ss" label="硕士" width="120" />
        <ts-table-column
          align="center"
          prop="xl_yjs"
          label="研究生"
          width="120"
        />
        <ts-table-column
          align="center"
          prop="xl_dxbk"
          label="大学本科"
          width="120"
        />
        <ts-table-column
          align="center"
          prop="xl_dxzk"
          label="大学专科"
          width="120"
        />
        <ts-table-column align="center" prop="xl_zz" label="中专" width="120" />
        <ts-table-column
          align="center"
          prop="xl_gzzj"
          label="高中中技"
          width="120"
        />
        <ts-table-column
          align="center"
          prop="xl_czjyx"
          label="初中及以下"
          width="120"
        />
      </ts-table-column>
    </base-table>
  </div>
</template>

<script>
import Dictionary from '@/views/partyBuilding/dictionary.js';

export default {
  data() {
    return {
      Dictionary,
      searchForm: {},
      searchList: [
        {
          label: '人员类型',
          value: 'multipleAttributesList',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '人员状态',
          value: 'statusList',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        }
      ],
      tableData: [],
      orgAttributesList: [],
      loading: false
    };
  },
  created() {
    this.ajax.getDataByDataLibrary('ORG_ATTRIBUTES').then(res => {
      if (res.success) {
        this.orgAttributesList = res.object || [];

        let options = (res.object || []).map(
          ({ itemName: label, itemNameValue: value }) => {
            return {
              label,
              value,
              element: 'ts-option'
            };
          }
        );
        this.searchList.filter(
          item => item.value === 'multipleAttributesList'
        )[0].childNodeList = options;
      }
    });

    this.ajax
      .getDictItem({
        dicTypeId: 'employee_status'
      })
      .then(res => {
        let option = (res.rows || []).map(({ itemName, itemCode }) => {
          return {
            label: itemName,
            value: itemCode,
            element: 'ts-option'
          };
        });

        this.searchList.filter(
          item => item.value === 'statusList'
        )[0].childNodeList = option;
      });
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;

      if (
        this.searchForm.multipleAttributesList &&
        this.searchForm.multipleAttributesList.length > 0
      ) {
        this.searchForm.orgAttributesList = this.searchForm.multipleAttributesList.join(
          ','
        );
      }

      if (this.searchForm.statusList && this.searchForm.statusList.length > 0) {
        this.searchForm.employeeStatusList = this.searchForm.statusList.join(
          ','
        );
      }
      this.handleRefreshTable();
    },

    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    async handleRefreshTable() {
      this.loading = true;
      let searchForm = {
        ...this.searchForm,
        pageNo: 1,
        pageSize: 999,
        sidx: 'create_date',
        sord: 'desc'
      };

      delete searchForm.multipleAttributesList;
      delete searchForm.statusList;

      let res = await this.ajax.selectPartySituationStatistics(searchForm);
      if (res.success && res.statusCode === 200) {
        this.$refs.table.refresh({
          rows: res.object
        });
      } else {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      this.loading = false;
    },
    handleExport() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo;
      let pageSize = this.$refs.table.pageSize;

      this.searchForm.applyStartDate =
        (this.searchForm.applyDate && this.searchForm.applyDate[0]) || '';
      this.searchForm.applyEndDate =
        (this.searchForm.applyDate && this.searchForm.applyDate[1]) || '';
      let data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 'create_date',
          sord: 'desc'
        },
        queryList = [],
        aDom = document.createElement('a');
      delete data.applyDate;

      Object.keys(data).map(key => {
        queryList.push(key + '=' + data[key]);
      });
      aDom.href =
        '/ts-oa/api/partyBuildingManage/exportPartySituationStatistics?' +
        queryList.join('&');
      aDom.click();
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
.basic-inform-of-party-member {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .el-table--medium td,
    .el-table--medium th {
      padding: 0px !important;
    }

    .el-table thead.is-group th {
      background-color: rgb(210, 222, 240) !important;
    }
  }
}
</style>
