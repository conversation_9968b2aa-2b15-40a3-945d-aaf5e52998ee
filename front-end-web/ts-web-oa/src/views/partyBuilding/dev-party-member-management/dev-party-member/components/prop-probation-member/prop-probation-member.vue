<template>
  <div>
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :resetData="{}"
      @search="refresh"
    >
      <template slot="dept">
        <ts-ztree-select
          :data="innerDeptTreeData"
          :inpText.sync="searchForm.applyDeptName"
          :inpVal.sync="searchForm.applyDeptCode"
          style="display: inline-block"
        ></ts-ztree-select>
      </template>

      <template slot="right">
        <ts-button type="primary" class="long-text-btn" @click="openEditModal()"
          >拟定预备党员{{
            waitingHandleNum ? `(${waitingHandleNum})` : ''
          }}</ts-button
        >
      </template>
    </ts-search-bar>

    <base-table
      ref="table"
      :columns="columns"
      :defaultSort="{ prop: 'developDate', order: 'descending' }"
      @refresh="handleTableRefresh"
    ></base-table>
    <AddEditModal ref="addEditModal" @refresh="refresh" />
  </div>
</template>

<script>
import AddEditModal from './add-edit-modal.vue';
export default {
  components: {
    AddEditModal
  },
  props: {
    menuLimits: {
      type: Array,
      default: () => []
    }
  },
  inject: [
    'openDetailModal',
    'getDeptTreeData',
    'getDevPartyMemberGroupByStatusData'
  ],
  data() {
    return {
      innerDeptTreeData: [],

      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'applyUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名',
            clearable: true
          }
        },
        {
          label: '科室',
          value: 'dept'
        },
        {
          label: '拟定预备党员日期',
          value: 'dateList',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],
      waitingHandleNum: 0,

      columns: [
        {
          label: '',
          prop: 'pageIndex',
          width: '50px',
          fixed: 'left'
        },
        {
          label: '申请人',
          prop: 'applyUserName',
          fixed: 'left',
          formatter: (row, prop, cell) => {
            return (
              <span
                class="detail-trigger-cell"
                onclick={() => this.openDetailModal(row)}>
                {cell}
              </span>
            );
          }
        },
        {
          label: '所在科室',
          prop: 'applyDeptName',
          minWidth: 140
        },
        {
          label: '岗位名称',
          prop: 'applyPost'
        },
        {
          label: '性别',
          prop: 'applyGender',
          align: 'center'
        },
        {
          label: '年龄',
          prop: 'applyAge',
          align: 'right'
        },
        {
          label: '拟定预备党员日期',
          prop: 'prepareDate',
          align: 'center',
          minWidth: 160
        },
        {
          label: '拟定发展对象日期',
          prop: 'developDate',
          align: 'center',
          minWidth: 160
        },
        {
          label: '入党介绍人',
          prop: 'developIntroducerName',
          minWidth: 130
        },
        {
          label: '列为入党积极分子日期',
          prop: 'activistDate',
          minWidth: 160,
          align: 'center'
        },
        {
          label: '谈话日期',
          prop: 'applyTalkDate',
          minWidth: 110,
          align: 'center'
        },
        {
          label: '谈话人',
          prop: 'applyTalkerName'
        },
        {
          label: '申请入党日期',
          prop: 'applyDate',
          minWidth: 130,
          align: 'center',
          sortable: true
        },
        {
          label: '出生日期',
          prop: 'applyBirthday',
          minWidth: 110,
          align: 'center'
        },
        {
          label: '民族',
          prop: 'applyNation'
        },
        {
          label: '最高学历',
          prop: 'applyHighestDegree'
        },
        {
          label: '学校',
          prop: 'applySchool',
          minWidth: 140
        },
        {
          label: '手机号码',
          prop: 'applyPhone',
          minWidth: 110
        },
        {
          label: '创建人',
          prop: 'createUserName'
        },
        {
          label: '创建时间',
          prop: 'prepareCreateDate',
          minWidth: 160,
          align: 'center',
          sortable: true
        },
        {
          label: '操作',
          width: '80',
          align: 'center',
          fixed: 'right',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.openEditModal,
                keyCode: 'prob_edit'
              }
            ];
            actionList = actionList.filter(action =>
              this.menuLimits.some(limit => limit.resourceId == action.keyCode)
            );
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  },
  methods: {
    async refresh() {
      this.$refs.table?.handleMounted();
      this.$refs.table?.triggerRefresh();
      this.getDevPartyMemberGroupByStatusData();
      this.ajax
        .getApplyMemberTalkingListData({
          pageNo: 1,
          pageSize: 10,
          applyStatus: 4
        })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.waitingHandleNum = res.totalCount;
        });
      this.innerDeptTreeData = await this.getDeptTreeData();
    },
    /**@desc 打开 新增/编辑 弹窗 */
    openEditModal(data = {}) {
      let isEdit = !!Object.keys(data).length;
      this.$refs.addEditModal?.open({ isEdit, data });
    },
    handleTableRefresh({ pageNo = 1, pageSize = 100, sidx, sord }) {
      let [prepareStartDate = '', prepareEndDate = ''] =
          this.searchForm.dateList || [],
        params = {
          ...this.searchForm,
          pageNo,
          pageSize,
          prepareStartDate,
          prepareEndDate,
          applyStatus: 4,
          sord
        };
      params.sidx =
        { applyDate: 'apply_date', developDate: 'develop_date' }[sidx] ||
        'develop_date';
      delete params.dateList;
      this.ajax.getApplyPartyTableDataList(params).then(res => {
        if (res.success == false || !res.rows) {
          this.$message.error(res.message || '表格刷新失败');
          return;
        }
        let rows = res.rows.map((item, index) => ({
          ...item,
          pageIndex: (pageNo - 1) * pageSize + index + 1
        }));
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .long-text-btn span {
  max-width: unset;
}
</style>
