<template>
  <ts-dialog
    title="详情"
    :visible.sync="visible"
    fullscreen
    :appendToBody="true"
  >
    <details-person-info ref="DetailsPersonInfo" :detailData="detailData" />
    <div class="step-container">
      <div class="base-step-line flex">
        <div
          v-for="step of fullStepList"
          :key="step.statusKey"
          class="apply-step-list-item  flex-col-center flex-column"
        >
          {{ step.label }}
        </div>
      </div>
      <div class="active-step-line flex">
        <template v-for="step of fullStepList">
          <div
            v-if="step.statusKey <= detailData.applyStatus"
            class="apply-step-list-item flex-grow flex-col-center flex-column"
            :key="step.statusKey"
          >
            {{ step.label }}
            <div class="apply-step-date">
              {{ detailData[step.dateKey] }}
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="step-list-container flex flex-grow">
      <div class="status-container">
        <ul class="status-list">
          <li
            v-for="item of sectionList"
            :key="item.name"
            :class="{
              active: activeTab == item.name
            }"
            @click="handlePreviewDetail(item)"
            class="status-item"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
      <div class="detail-container flex flex-grow">
        <el-scrollbar
          ref="scroll"
          style="flex: 1;"
          wrap-style="height: calc(100% + 17px);"
        >
          <!-- 相关附件 -->
          <RelatedFile ref="relatedFile" :data="detailData" />
          <!-- 申请入党 -->
          <ApplyDetail
            v-if="maxApplyStatus >= 0"
            ref="applyDetail"
            :data="detailData"
          />
          <!-- 列为入党积极分子 -->
          <ActivistDetail
            v-if="maxApplyStatus >= 1"
            ref="activistDetail"
            :data="detailData"
          />
          <!-- 拟定发展对象 -->
          <DevTargetDetail
            v-if="maxApplyStatus >= 3"
            ref="devTargetDetail"
            :data="detailData"
          />
          <!-- 拟定预备党员 -->
          <ProbationMemberDetail
            v-if="maxApplyStatus >= 4"
            ref="probationMemberDetail"
            :data="detailData"
          />
          <!-- 正式党员 -->
          <FullMemberTable
            v-if="maxApplyStatus >= 5"
            ref="fullMemberTable"
            :data="detailData"
          />
          <FlowDetail
            v-if="detailData.isInflow == 1"
            ref="flowDetail"
            :data="detailData"
          />
        </el-scrollbar>
      </div>
    </div>
    <template slot="footer">
      <ts-button @click="close">关闭</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import ApplyDetail from './apply-detail.vue'; // 申请入党
import ActivistDetail from './activist-detail.vue'; // 列为入党积极分子
import DevTargetDetail from './dev-target-detail.vue'; // 拟定发展对象
import ProbationMemberDetail from './probation-member-detail.vue'; // 拟定预备党员
import FullMemberTable from './full-member-detail.vue'; // 正式党员
import FlowDetail from './flow-detail.vue'; // 流入信息
import RelatedFile from './related-file.vue'; // 相关附件
import DetailsPersonInfo from '@/views/partyBuilding/components/details-person-info.vue';

const fullStepList = [
  {
    statusKey: 0,
    label: '申请入党',
    name: 'applyDetail',
    dateKey: 'applyDate'
  },
  {
    statusKey: 1,
    label: '列为入党积极分子',
    name: 'activistDetail',
    dateKey: 'activistDate'
  },
  {
    statusKey: 3,
    label: '拟定发展对象',
    name: 'devTargetDetail',
    dateKey: 'developDate'
  },
  {
    statusKey: 4,
    label: '拟定预备党员',
    name: 'probationMemberDetail',
    dateKey: 'prepareDate'
  },
  {
    statusKey: 5,
    label: '正式党员',
    name: 'fullMemberTable',
    dateKey: 'fullDate'
  }
];

export default {
  components: {
    ApplyDetail,
    ActivistDetail,
    DevTargetDetail,
    ProbationMemberDetail,
    FullMemberTable,
    RelatedFile,
    FlowDetail,
    DetailsPersonInfo
  },
  data() {
    return {
      fullStepList,

      visible: false,
      detailData: {},
      maxApplyStatus: 0,

      activeTab: 'applyDetail',
      sectionList: [],
      scrollTimer: null, // 滚动防抖计时器
      isTabClick: false // 是否是侧边菜单点击，用来区分滚动和点击的区别
    };
  },
  methods: {
    /**@desc 打开详情弹窗 */
    open(data = {}) {
      this.detailData = data;
      this.maxApplyStatus =
        (data.applyOperateList || []).reduce((prev, next) => {
          if (next.applyStatus > prev) {
            return next.applyStatus;
          }
          return prev;
        }, 0) || 0;
      this.maxApplyStatus < data.applyStatus &&
        (this.maxApplyStatus = data.applyStatus);
      let stepList = fullStepList.filter(
        item => item.statusKey <= this.maxApplyStatus
      );

      if (this.detailData.isInflow == 1) {
        stepList.push({
          label: '流入信息',
          name: 'flowDetail'
        });
      }
      // stepList.push({
      //   label: '相关附件',
      //   name: 'relatedFile'
      // });
      this.sectionList = stepList;
      this.activeTab = this.sectionList[0].name;
      this.visible = true;
      this.$nextTick(() => {
        let wrap = this.$refs.scroll.wrap;
        wrap.removeEventListener('scroll', this.handleContentScroll);
        wrap.addEventListener('scroll', this.handleContentScroll);
        wrap.scrollTop = 0;
      });
    },
    close() {
      this.visible = false;
    },
    handlePreviewDetail({ name }) {
      name && (this.activeTab = name);
      let dom = this.$refs[name];
      dom = dom && dom.$el;
      if (!dom) {
        return;
      }

      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;
      if (!scrollDom.wrap.scrollHeight) {
        return;
      }
      this.isTabClick = true;
      scrollDom.wrap.scrollTo({ top: domScrollTop, behavior: 'smooth' });
    },
    handleContentScroll(e) {
      this.scrollTimer && clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isTabClick = false;
      }, 200);

      let wrap = this.$refs.scroll.wrap,
        childNodes = wrap.childNodes[0].childNodes,
        nodeList = [],
        previewTop = wrap.scrollTop,
        previewBottom = wrap.scrollTop + wrap.clientHeight;
      for (let i = 0; i < childNodes.length; i++) {
        let node = childNodes[i];
        nodeList.push({
          node,
          offsetTop: node.offsetTop,
          height: node.offsetHeight
        });
      }
      if (previewTop == 0) {
        this.activeTab = this.sectionList[0].name;
        return;
      } else if (previewTop == wrap.scrollHeight - wrap.clientHeight) {
        this.activeTab = this.sectionList[this.sectionList.length - 1].name;
        return;
      }
      nodeList.map((item, index) => {
        if (
          previewTop + wrap.clientHeight / 4 >= item.offsetTop &&
          !this.isTabClick
        ) {
          this.activeTab = (this.sectionList[index] || {}).name;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  height: calc(100vh - 111px);
  display: flex;
  flex-direction: column;
}

.status-container {
  flex-shrink: 0;
  margin-right: $medium-spacing;
}

.status-list {
  background-color: $ts-table-header-hover-bg;
  cursor: pointer;
  padding: 2px;

  .status-item {
    line-height: 30px;
    padding: 0 $card-spacing;
    text-align: center;

    &.active {
      background-color: #fff;
      color: $primary-blue;
    }
  }
}

.step-container {
  margin: $primary-spacing 0 $medium-spacing 0;
  position: relative;

  .base-step-line {
    background-color: $ts-progress-bg;
    border-radius: 32px;
    margin-bottom: 24px;
  }

  .active-step-line {
    position: absolute;
    top: 0;
    background-color: $primary-blue;
    color: #fff;
    border-radius: 32px;

    .apply-step-date {
      position: absolute;
      top: calc(100% + #{$primary-spacing});
      color: #333;
      font-size: 12px;
      line-height: 14px;
    }
  }

  .apply-step-list-item {
    line-height: 32px;
    width: 208px;

    &:first-child .apply-step-title {
    }

    &:last-child .apply-step-title {
      border-radius: 0 32px 32px 0;
    }

    &.active .apply-step-title {
      background-color: $primary-blue;
      color: #fff;
    }

    &.last-active .apply-step-title {
      border-radius: 0 32px 32px 0;
    }

    .apply-step-date {
      font-size: 12px;
    }
  }
}

.step-list-container {
  overflow: hidden;
}

.detail-container {
  overflow: hidden;
  /deep/ .el-scrollbar__view {
    padding-bottom: 12px;
  }
}

/deep/ {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    margin: $primary-spacing 0;
    padding-bottom: 4px;
    border-bottom: 1px solid $theme-border-color;

    &::before {
      content: ' ';
      height: 16px;
      width: 4px;
      border-radius: 4px;
      background-color: $primary-blue;
      margin-right: 4px;
    }
  }

  .second-section-title {
    padding-bottom: 4px;
    border-bottom: 1px solid $theme-border-color;
    margin: $primary-spacing 0;
    font-size: 16px;
    text-indent: 1rem;
  }

  .data-preview-table + .data-preview-table {
    margin-top: $primary-spacing;
  }
}
</style>
