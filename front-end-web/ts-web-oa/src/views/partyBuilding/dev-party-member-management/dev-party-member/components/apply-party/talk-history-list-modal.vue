<template>
  <ts-dialog
    custom-class="talk-history-list-modal"
    :visible.sync="visible"
    title="谈话记录"
    type="large"
  >
    <div class="flex">
      <div class="talk-list-content flex-column">
        <ts-search-bar
          v-model="searchForm"
          :formList="searchList"
          :resetData="{}"
          @search="refresh"
        ></ts-search-bar>
        <el-scrollbar
          ref="scroll"
          v-infinity-scroll="{
            loadMethod: handleGetTalkList,
            hasFinished: true,
            hasLoading: true,
            selector: '.el-scrollbar__wrap'
          }"
          wrap-class="infinity-scrollbar"
          wrap-style="overflow-x: hidden; margin-bottom: 0px;"
        >
          <div
            v-for="talkItem of talkList"
            :key="talkItem.id"
            class="talk-list-item"
            :class="{
              active: form.id == talkItem.id
            }"
            @click="handleSelectTalkItem(talkItem)"
          >
            <div class="flex-col-center">
              <span class="applicant-user-name">
                {{ talkItem.applyUserName }}
              </span>
              <span
                class="talk-status-icon"
                :class="{
                  'waiting-icon': talkItem.applyTalkStatus == 0
                }"
              >
                {{ talkItem.applyTalkStatus == 0 ? '待谈话' : '已谈话' }}
              </span>
              <span
                class="iconfont iconsousuo"
                @click.stop="openDetailModal(talkItem)"
              ></span>
            </div>
            <div>
              <span>申请入党日期：{{ talkItem.applyDate }}</span>
              <span>{{ talkItem.applyGender }}</span>
              <span>{{ talkItem.applyAge }}岁</span>
            </div>
            <div>所在科室： {{ talkItem.applyDeptName }}</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="talk-info-container flex-column">
        <ts-form
          v-if="form.id && form.applyTalkStatus == 0"
          ref="form"
          :model="form"
          label-width="90px"
          class="flex-grow"
        >
          <ts-form-item
            label="谈话人"
            prop="applyTalkerCode"
            :rules="requiredRow"
          >
            <base-select
              style="width: 100%"
              v-model="form.applyTalkerCode"
              :inputText.sync="form.applyTalkerName"
              :loadMethod="handleGetEmployeeList"
              label="empName"
              value="empCode"
              searchInputName="empName"
              :clearable="false"
            />
          </ts-form-item>
          <ts-form-item
            label="谈话日期"
            prop="applyTalkDate"
            :rules="requiredRow"
          >
            <ts-date-picker
              v-model="form.applyTalkDate"
              valueFormat="YYYY-MM-DD"
              :disabledDate="computedDisabledDate"
              style="width: 100%"
            ></ts-date-picker>
          </ts-form-item>
          <ts-form-item label="备注" prop="applyTalkRemark">
            <ts-input
              v-model="form.applyTalkRemark"
              type="textarea"
              rows="4"
              resize="none"
              show-word-limit
              maxlength="200"
              placeholder="请输入备注"
            ></ts-input>
          </ts-form-item>
          <ts-form-item label="谈话记录">
            <base-upload v-model="form.applyTalkFiles">
              <div class="flex-col-center">
                <ts-button>文件上传</ts-button>
                <ts-button type="text" @click.stop="handleDownLoadTemplate"
                  >模板下载
                </ts-button>
              </div>
            </base-upload>
          </ts-form-item>
        </ts-form>

        <div
          v-else-if="form.id && form.applyTalkStatus == 1"
          class="talk-detail-container flex-grow"
        >
          <table>
            <colgroup>
              <col width="90px" />
            </colgroup>
            <tr>
              <td>
                <div class="cell flex-row-center">谈话人</div>
              </td>
              <td>
                <div class="cell">{{ form.applyTalkerName }}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="cell flex-row-center">谈话日期</div>
              </td>
              <td>
                <div class="cell">{{ form.applyTalkDate }}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="cell flex-row-center">备注</div>
              </td>
              <td>
                <div class="cell">{{ form.applyTalkRemark }}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="cell flex-row-center">谈话记录</div>
              </td>
              <td>
                <div class="cell">
                  <base-upload
                    v-model="form.applyTalkFiles"
                    onlyRead
                    :actions="['preview', 'downLoad']"
                  ></base-upload>
                </div>
              </td>
            </tr>
          </table>

          <div v-if="form.applyStatus == 0" style="text-align: right;">
            <ts-button
              v-if="form.id && form.applyTalkStatus == 1"
              type="danger"
              @click="handleDeleteTalkInfo"
              >删除
            </ts-button>
          </div>
        </div>

        <div v-if="form.id && form.applyTalkStatus == 0" class="footer">
          <ts-button type="primary" @click="handleSubmit">确定</ts-button>
          <ts-button @click="visible = false">取消</ts-button>
        </div>
      </div>
    </div>
  </ts-dialog>
</template>

<script>
import infinityScroll from '@/unit/infinityScroll';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  mixins: [infinityScroll],
  inject: ['openDetailModal'],
  data() {
    return {
      visible: false,

      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'applyUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名',
            clearable: true
          }
        },
        {
          label: '状态',
          value: 'applyTalkStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            {
              label: '待谈话',
              value: '0',
              element: 'ts-option'
            },
            {
              label: '已谈话',
              value: '1',
              element: 'ts-option'
            }
          ]
        }
      ],

      pageNo: 1,
      talkList: [],

      form: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  methods: {
    open() {
      this.visible = true;
      this.$nextTick(() => {
        this.refresh();
      });
    },
    refresh() {
      this.pageNo = 1;
      this.talkList = [];
      this.form = {};
      let wrapDom = this.$refs.scroll.$refs.wrap;
      wrapDom.resetInfinityScrolling && wrapDom.resetInfinityScrolling();
    },
    handleGetTalkList(cb) {
      let searchData = {
          ...this.searchForm,
          pageNo: this.pageNo,
          pageSize: 15,
          sidx: 'apply_talk_status,apply_date',
          sord: 'desc'
        },
        employeeNo = this.$store.state.common.userInfo.employeeNo;
      employeeNo != 'admin' && (searchData.createUser = employeeNo);
      this.ajax.getApplyMemberTalkingListData(searchData).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '');
          return;
        }
        if (this.pageNo == 1) {
          this.talkList = [];
          res.rows[0] && this.handleSelectTalkItem(res.rows[0]);
        }
        this.talkList.push(...res.rows);
        this.pageNo++;
        cb(res.rows.length < 15);
      });
    },
    /**@desc 下载模板 */
    handleDownLoadTemplate() {},
    /**@desc 确认谈话内容 */
    async handleSubmit() {
      let confirm = await this.$refs.form.validate().catch(res => res);
      if (!confirm) {
        return;
      }
      let data = {
        ...this.form,
        applyTalkStatus: 1,
        createStatus: 0
      };
      this.ajax.handleEditApplyPartyMember(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '谈话记录录入失败');
          return;
        }
        this.$message.success('谈话记录录入成功');
        this.refresh();
        this.$emit('refresh');
      });
    },
    /**@desc 改变右侧弹窗内容 */
    handleSelectTalkItem(row) {
      this.form = deepClone(row);
      this.form.applyTalkStatus == 0 &&
        !this.form.applyTalkDate &&
        (this.form.applyTalkDate =
          this.$moment().diff(this.form.applyDate, 'months') > 0
            ? this.$moment(this.form.applyDate)
                .add(1, 'M')
                .subtract(1, 'd')
                .format('YYYY-MM-DD')
            : this.$moment().format('YYYY-MM-DD'));
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },
    async handleGetEmployeeList(data) {
      let res = await this.ajax.getEmployeeList({
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    /**@desc 计算禁止选择日期 */
    computedDisabledDate(current) {
      // 谈话时间必须在 入党申请后的一个月内 进行谈话， 时间最迟不超过当前时间
      let isBeforeApplyTime = current.isBefore(this.form.applyDate), // 申请时间之前禁止选择
        isInAMonth = current.diff(this.form.applyDate, 'months') >= 1, // 超过申请时间一个月时间禁止选择
        isAfterToday = !current.isBefore(this.today); // 超过今天禁止选择
      return isBeforeApplyTime || isInAMonth || isAfterToday;
    },
    /**@desc 删除谈话记录 */
    async handleDeleteTalkInfo() {
      let confirm = await this.$confirm('是否确认删除当前谈话记录？', '提示', {
        type: 'warning'
      }).catch(res => res);
      if (confirm != 'confirm') {
        return;
      }
      this.ajax
        .handleDeleteApplyMemberTalkHistory({
          id: this.form.id
        })
        .then(res => {
          if (!res.success) {
            this.$message.error(res.message || '谈话记录删除失败');
            return;
          }
          this.$message.success('谈话记录删除成功');
          this.refresh();
          this.$emit('refresh');
        });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .talk-history-list-modal {
  .ts-upload-file-list .text-item {
    height: auto !important;
  }

  .ts-upload-file-list .text-item .text-item-name {
    line-height: 24px !important;
    white-space: wrap !important;
  }
}

.talk-list-content {
  flex: 1;
  padding-right: $primary-spacing;
  border-right: 1px solid $theme-border-color;
  margin-right: $primary-spacing;
  height: 600px;
  max-height: 85vh;

  /deep/ .trasen-search-content .el-input {
    min-width: 110px;
  }

  .talk-list-item {
    padding: $primary-spacing $medium-spacing;
    cursor: pointer;

    &:not(:last-child) {
      border-bottom: 1px solid $theme-border-color;
    }

    > div {
      margin-bottom: 4px;
    }

    span + span {
      margin-left: $primary-spacing;
    }

    &.active {
      background-color: $list-hover-color;
    }

    .iconsousuo {
      font-size: 14px;
      cursor: pointer;
    }

    .applicant-user-name {
      font-size: 16px;
      font-weight: bold;
    }

    .talk-status-icon {
      display: inline-flex;
      font-size: 12px;
      padding: 4px 8px;
      border: 1px solid $theme-border-color;
      border-radius: 29px;
      background-color: $primary-bg;
      font-weight: bold;
      transform: scale(0.8);

      &.waiting-icon {
        background-color: $primary-blue;
        color: #fff;
      }
    }
  }
}

/deep/ .infinity-scrollbar {
  margin-bottom: 0 !important;
}

.talk-info-container {
  flex: 3;

  .footer {
    text-align: right;
    padding-top: $primary-spacing;
    border-top: 1px solid $theme-border-color;
  }
}

.talk-detail-container {
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: $primary-spacing;

    td {
      border: 1px solid $theme-border-color;

      .cell {
        min-height: 30px;
        padding: 0 $primary-spacing;
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
