<template>
  <div class="trasen-container flex-column">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      @search="refresh"
    >
      <template slot="right">
        <ts-button type="primary" @click="openAddOrEditModal({})">
          新增
        </ts-button>
      </template>
    </ts-search-bar>
    <base-table
      ref="table"
      :columns="columns"
      @refresh="handleTableRefresh"
    ></base-table>

    <ts-dialog
      :visible.sync="visible"
      :title="isEdit ? '编辑' : '新增'"
      type="large"
    >
      <ts-form ref="form" :model="editData">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="党员姓名"
              prop="hretireUserCode"
              :rules="requiredRow"
            >
              <base-select
                style="width: 100%"
                :disabled="isEdit"
                v-model="editData.hretireUserCode"
                :inputText.sync="editData.retireUserName"
                :loadMethod="partyBuildingManageList"
                label="showManageUserName"
                value="manageUserCode"
                searchInputName="manageUserName"
                :clearable="false"
                @select="handlePersonSelect"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="年龄" prop="retireAge" :rules="requiredRow">
              <ts-input
                v-model="editData.retireAge"
                disabled
                placeholder="请选择党员"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="党龄"
              prop="partyStanding"
              :rules="requiredRow"
            >
              <ts-input
                v-model="editData.partyStanding"
                disabled
                placeholder="请选择党员"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="退休类别"
              prop="retireType"
              :rules="requiredRow"
            >
              <ts-select
                v-model="editData.retireType"
                clearable
                style="width: 100%"
              >
                <ts-option
                  v-for="(option, index) of Dictionary.retireTypeList"
                  :key="index"
                  v-bind="option"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="退休日期"
              prop="retireDate"
              :rules="requiredRow"
            >
              <ts-date-picker
                v-model="editData.retireDate"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12" v-if="!isEdit">
            <ts-form-item
              label="变更支部"
              prop="changeBranchId"
              :rules="requiredRow"
            >
              <base-select
                style="width: 100%"
                v-model="editData.changeBranchId"
                :inputText.sync="editData.changeBranchName"
                :loadMethod="handleGetAllBranch"
                label="branchName"
                value="id"
                searchInputName="branchName"
                :clearable="false"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="原因">
          <ts-input
            v-model="editData.retireReason"
            type="textarea"
            class="textarea"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件">
          <base-upload ref="files" v-model="editData.retireFiles" />
        </ts-form-item>
      </ts-form>
      <template slot="footer">
        <ts-button type="primary" @click="handleSubmit">确定</ts-button>
        <ts-button @click="handleClose">取消</ts-button>
      </template>
    </ts-dialog>

    <ts-dialog title="详情" :visible.sync="detailVisible" type="large">
      <Description
        :tableData="detailData"
        :columns="previewColumn"
        :column="2"
        label-width="135px"
      >
        <template slot="retireFiles">
          <base-upload
            v-model="detailData.retireFiles"
            :onlyRead="true"
          ></base-upload>
        </template>
      </Description>
      <template slot="footer">
        <ts-button @click="detailVisible = false">关闭</ts-button>
      </template>
    </ts-dialog>
  </div>
</template>

<script>
import moment from 'moment';
import Dictionary from '@/views/partyBuilding/dictionary.js';
import { calculateUsageYears, deepClone } from '@/unit/commonHandle.js';
import Description from '@/views/partyBuilding/components/description.vue';

export default {
  components: { Description },
  data() {
    return {
      Dictionary,
      loading: false,
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      },

      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'retireUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名',
            clearable: true
          }
        },
        {
          label: '退休类别',
          value: 'retireType',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: Dictionary.retireTypeList
        },
        {
          label: '退休日期',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],

      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'retireUserName',
          align: 'center',
          width: '120',
          formatter: row => {
            return (
              <span
                class="detail-trigger-cell"
                onClick={() => {
                  this.openDetailModal(row);
                }}>
                {row.retireUserName}
              </span>
            );
          }
        },
        {
          label: '年龄',
          width: '80',
          align: 'center',
          prop: 'retireAge'
        },
        {
          label: '所属支部',
          align: 'center',
          prop: 'changeBranchName'
        },
        {
          label: '党龄(年)',
          width: '120',
          align: 'center',
          prop: 'partyStanding'
        },
        {
          label: '退休类别',
          width: '120',
          align: 'center',
          prop: 'retireType',
          formatter: (row, prop, cell) => {
            let find =
              this.Dictionary.retireTypeList.find(item => item.value == cell) ||
              {};
            return <span>{find.label || '-'}</span>;
          }
        },
        {
          label: '退休日期',
          align: 'center',
          width: 120,
          prop: 'retireDate'
        },
        {
          label: '创建人',
          align: 'center',
          width: 100,
          prop: 'createUserName'
        },
        {
          label: '创建时间',
          align: 'center',
          prop: 'createDate',
          width: 160
        },
        {
          label: '操作',
          width: '100',
          fixed: 'right',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.openAddOrEditModal,
                keyCode: 'edit'
              },
              {
                label: '删除',
                event: this.handleDelete,
                className: 'delete-span'
              }
            ];
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ],

      visible: false,
      isEdit: false,
      requiredRow: { required: true, message: '必填' },
      detailVisible: false,
      previewColumn: [
        {
          label: '党员姓名',
          prop: 'retireUserName'
        },
        {
          label: '年龄',
          prop: 'retireAge'
        },
        {
          label: '党龄(年)',
          prop: 'partyStanding'
        },
        {
          label: '退休类别',
          prop: 'retireTypeName'
        },
        {
          label: '退休日期',
          prop: 'retireDate'
        },
        {
          label: '原因',
          prop: 'retireReason',
          span: 2
        },
        {
          label: '附件',
          prop: 'retireFiles',
          span: 2
        }
      ],
      editData: {},
      detailData: {}
    };
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;

      this.searchForm.retireStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.retireEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      this.handleTableRefresh();
    },
    async handleTableRefresh() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.date;
      let res = await this.ajax.partyBuildingRetireList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    /**@desc 打开新增编辑弹窗 */
    openAddOrEditModal(row = {}) {
      this.editData = deepClone(row);
      this.isEdit = !!Object.keys(row).length;

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    async handleSubmit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.editData);
      let API = null;
      if (!this.isEdit) {
        API = this.ajax.partyBuildingRetireSave;
      } else {
        API = this.ajax.partyBuildingRetireUpdate;
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.handleClose();
      this.$message.success('操作成功!');
      this.handleTableRefresh();
    },
    async handleDelete(row) {
      const { id } = row;
      try {
        await this.$confirm(`<span>您确认删除这条数据吗？</span>`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        const res = await this.ajax.partyBuildingRetireDelete(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleTableRefresh();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    handleClose() {
      this.$set(this, 'editData', {});
      this.visible = false;
    },
    async partyBuildingManageList(data) {
      let res = await this.ajax.partyBuildingManageList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        isFull: '1',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '党员数据获取失败');
        return false;
      }
      res.rows.forEach(item => {
        item.showManageUserName = `${item.manageUserName} - ${item.manageDeptName}`;
      });
      return res.rows;
    },
    // 获取所有党支部
    async handleGetAllBranch(data) {
      let res = await this.ajax.partyBuildingBranchList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '党支部获取失败');
        return false;
      }

      return res.rows || [];
    },
    handlePersonSelect(item) {
      let retireAge = this.$moment().diff(item.manageBirthday, 'years');

      this.$set(
        this.editData,
        'partyStanding',
        calculateUsageYears(item.applyDate)
      );

      this.$set(this.editData, 'prepareDate', item.applyDate);
      this.$set(this.editData, 'hretireUserCode', item.manageUserCode);
      this.$set(this.editData, 'retireUserName', item.manageUserName);

      this.$set(this.editData, 'retireAge', retireAge);
      this.$set(this.editData, 'retireBranchId', item.branchId);
      this.$set(this.editData, 'retireBranchName', item.branchName);
    },
    openDetailModal(row = {}) {
      this.detailData = deepClone(row);

      let find =
        this.Dictionary.retireTypeList.find(
          item => item.value == row.retireType
        ) || {};

      this.detailData.retireTypeName = find.label || '-';
      this.detailVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-trigger-cell {
  color: $primary-blue;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

::v-deep {
  .delete-span {
    color: red !important;
    cursor: pointer;
  }

  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
