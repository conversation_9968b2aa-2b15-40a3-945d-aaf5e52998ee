<template>
  <div>
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      @search="refresh"
    >
    </ts-search-bar>
    <base-table
      ref="table"
      class="form-table"
      :columns="columns"
      @refresh="handleTableRefresh"
    ></base-table>
  </div>
</template>

<script>
import Dictionary from '@/views/partyBuilding/dictionary.js';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  data() {
    return {
      Dictionary,
      loading: false,
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'historyUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名',
            clearable: true
          }
        },
        {
          label: '党员类别',
          value: 'type',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: Dictionary.addPartyMemberType
        },
        {
          label: '减少方式',
          value: 'reduceStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: Dictionary.leavePartyTypeList
        },
        {
          label: '恢复党组织日期',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],

      columns: [
        {
          label: '',
          prop: 'pageIndex',
          width: 70,
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'historyUserName',
          width: 90,
          align: 'center',
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.openDetailModal(row);
                }}>
                {row.historyUserName}
              </span>
            );
          }
        },
        {
          label: '性别',
          width: '90',
          align: 'center',
          prop: 'historyGender',
          formatter: row => {
            let label = row.historyGender === '0' ? '男' : '女';
            return <span>{label}</span>;
          }
        },
        {
          label: '年龄',
          width: '90',
          align: 'center',
          prop: 'historyAge',
          formatter: row => {
            return <span>{row.historyAge || '-'}</span>;
          }
        },
        {
          label: '所属支部',
          align: 'center',
          minWidth: '150',
          prop: 'historyBranchName',
          formatter: row => {
            return <span>{row.historyBranchName || '-'}</span>;
          }
        },
        {
          label: '党员类别',
          prop: 'type',
          width: 120,
          align: 'center',
          formatter: row => {
            let find =
              this.Dictionary.addPartyMemberType.find(
                item => item.value == row.type
              ) || {};
            return <span>{find.label || '-'}</span>;
          }
        },
        {
          label: '党龄(年)',
          width: '120',
          align: 'center',
          prop: 'partyStanding'
        },
        {
          label: '减少方式',
          prop: 'reduceStatus',
          width: 120,
          align: 'center',
          formatter: row => {
            let find =
              this.Dictionary.leavePartyTypeList.find(
                item => item.value == row.reduceStatus
              ) || {};
            return <span>{find.label || '-'}</span>;
          }
        },
        {
          label: '恢复党组织日期',
          width: '150',
          align: 'center',
          prop: 'recoveryDate'
        },
        {
          label: '恢复原因',
          align: 'center',
          prop: 'recoveryReason'
        },
        {
          label: '恢复依据',
          align: 'center',
          prop: 'recoveryBasis'
        },
        {
          label: '创建人',
          width: '140',
          align: 'center',
          prop: 'createUserName'
        },

        {
          label: '创建时间',
          width: '160',
          align: 'center',
          prop: 'createDate'
        }
      ],

      previewColumn: [
        {
          label: '党员姓名',
          prop: 'historyUserName',
          span: 2
        },
        {
          label: '党员类别',
          prop: 'typeName'
        },
        {
          label: '性别',
          prop: 'historyGenderName'
        },
        {
          label: '年龄',
          prop: 'historyAge'
        },
        {
          label: '党龄(年)',
          prop: 'partyStanding'
        },
        {
          label: '减少方式',
          prop: 'reduceStatusName'
        },
        {
          label: '离开党组织日期',
          prop: 'departureDate'
        },
        {
          label: '恢复党组织日期',
          prop: 'recoveryDate'
        },
        {
          label: '恢复原因',
          prop: 'recoveryReason'
        },
        {
          label: '恢复依据',
          prop: 'recoveryBasis',
          span: 2
        },
        {
          label: '附件',
          prop: 'departureFiles',
          span: 2
        }
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      this.handleTableRefresh();
    });
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;

      this.searchForm.recoveryStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.recoveryEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      this.handleTableRefresh();
    },

    async handleTableRefresh() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          historyStatus: '1',
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.date;
      let res = await this.ajax.partyBuildingHistoryList(searchForm);

      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    openDetailModal(row = {}) {
      let details = deepClone(row);
      let find =
        this.Dictionary.leavePartyTypeList.find(
          item => item.value == row.reduceStatus
        ) || {};
      details.reduceStatusName = find.label || '-';
      details.historyGenderName = row.manageGender === '0' ? '男' : '女';

      let findType =
        this.Dictionary.addPartyMemberType.find(
          item => item.value == row.type
        ) || {};
      details.typeName = findType.label || '-';

      this.$emit('preview', this.previewColumn, details);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .delete-span {
      color: red;
      cursor: pointer;
    }
  }
}
</style>
