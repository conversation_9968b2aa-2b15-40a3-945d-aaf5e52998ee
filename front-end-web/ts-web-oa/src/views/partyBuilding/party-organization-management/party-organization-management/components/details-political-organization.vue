<template>
  <ts-dialog
    custom-class="details-political-organization"
    append-to-body
    :visible.sync="visible"
    title="查看"
    @close="close"
  >
    <div class="status-container">
      <ul class="status-list">
        <li
          v-for="item of sectionList"
          :key="item.name"
          :class="{
            active: activeTab == item.name
          }"
          @click="handlePreviewDetail(item)"
          class="status-item"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <div class="detail-container">
      <el-scrollbar
        ref="scroll"
        style="height: 100%;"
        wrap-style="height: calc(100% + 17px);"
      >
        <div ref="basicInfo">
          <div class="section-title">基本信息</div>
          <description
            :columns="basicColumns"
            :tableData="details"
            :column="2"
            label-width="135px"
          >
            <template slot="organizationFiles">
              <base-upload
                v-model="details.organizationFiles"
                :onlyRead="true"
              />
            </template>
            <template slot="areaText">
              {{ details.organizationProvince }}
              {{ details.organizationCity }}
              {{ details.organizationArea }}
            </template>
          </description>
        </div>

        <div
          ref="revokeInfo"
          v-if="details.revokeInfoList && details.revokeInfoList.length > 0"
        >
          <div class="section-title">撤销信息</div>
          <description
            style="margin-bottom: 8px"
            v-for="item in details.revokeInfoList"
            :key="item.id"
            :columns="revokeInfoColumns"
            :tableData="item"
            :column="2"
            label-width="135px"
          >
            <template slot="operateFiles">
              <base-upload v-model="item.operateFiles" :onlyRead="true" />
            </template>
          </description>
        </div>

        <div
          ref="restoreInfo"
          v-if="details.restoreInfoList && details.restoreInfoList.length > 0"
        >
          <div class="section-title">恢复信息</div>
          <description
            style="margin-bottom: 8px"
            v-for="item in details.restoreInfoList"
            :key="item.id"
            :columns="restoreInfoColumns"
            :tableData="item"
            :column="2"
            label-width="135px"
          >
            <template slot="operateFiles">
              <base-upload v-model="item.operateFiles" :onlyRead="true" />
            </template>
          </description>
        </div>

        <div ref="teamInfo" v-if="details.partyBuildingTeam">
          <div class="section-title">班子信息</div>
          <detail-team-building-config
            :readonly="false"
            :tableData="details.partyBuildingTeam"
          />
        </div>
      </el-scrollbar>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import Dictionary from '@/views/partyBuilding/dictionary.js';
import Description from '@/views/partyBuilding/components/description.vue';
import DetailTeamBuildingConfig from '@/views/partyBuilding/components/detail-team-building-config.vue';

export default {
  components: { Description, DetailTeamBuildingConfig },
  props: {},
  data() {
    return {
      visible: false,
      activeTab: 'basicInfo',
      scrollTimer: null,
      isTabClick: false,
      details: {},

      basicColumns: [
        {
          label: '党组织全称',
          prop: 'organizationName',
          span: 2
        },
        {
          label: '党组织简称',
          prop: 'shortName'
        },
        {
          label: '上级党组织',
          prop: 'superiorName'
        },
        {
          label: '党组织编码',
          prop: 'organizationCode'
        },
        {
          label: '组织类型',
          prop: 'organizationType'
        },
        {
          label: '行政区划分关系',
          prop: 'organizationAdr'
        },
        {
          label: '行政区域',
          prop: 'areaText'
        },
        {
          label: '党组织联系人',
          prop: 'organizationContactCode'
        },
        {
          label: '联系人手机',
          prop: 'organizationContactPhone'
        },
        {
          label: '建立日期',
          prop: 'creationDate'
        },
        {
          // label: '组织地址'
          // prop: 'key11'
        },
        {
          label: '详细地址',
          prop: 'organizationAddress',
          span: 2
        },
        {
          label: '备注',
          prop: 'organizationRemark',
          span: 2
        },
        {
          label: '附件',
          prop: 'organizationFiles',
          span: 2
        }
      ],

      revokeInfoColumns: [
        {
          label: '撤销日期',
          prop: 'operateDate'
        },
        {
          label: '撤销文号',
          prop: 'operateNumber'
        },
        {
          label: '撤销原因',
          prop: 'operateReason',
          span: 2
        },
        {
          label: '附件',
          prop: 'operateFiles',
          span: 2
        }
      ],

      restoreInfoColumns: [
        {
          label: '恢复日期',
          prop: 'operateDate'
        },
        {
          label: '恢复文号',
          prop: 'operateNumber'
        },
        {
          label: '恢复原因',
          prop: 'operateReason',
          span: 2
        },
        {
          label: '附件',
          prop: 'operateFiles',
          span: 2
        }
      ],

      Dictionary
    };
  },
  computed: {
    sectionList() {
      let sections = [
        {
          label: '基本信息',
          name: 'basicInfo'
        }
      ];

      if (
        this.details.revokeInfoList &&
        this.details.revokeInfoList.length > 0
      ) {
        sections.push({
          label: '撤销信息',
          name: 'revokeInfo'
        });
      }

      if (
        this.details.restoreInfoList &&
        this.details.restoreInfoList.length > 0
      ) {
        sections.push({
          label: '恢复信息',
          name: 'restoreInfo'
        });
      }

      if (this.details.partyBuildingTeam) {
        sections.push({
          label: '班子信息',
          name: 'teamInfo'
        });
      }

      return sections;
    }
  },
  methods: {
    async open(opt = {}) {
      this.activeTab = 'basicInfo';
      const res = await this.ajax.partyBuildingOrganizationDetails(opt.id);
      if (res.statusCode === 200 && res.success) {
        let { operateList = [] } = res.object;
        this.details = res.object;
        this.details.revokeInfoList = operateList.filter(
          item => item.operateType === '1'
        );
        this.details.restoreInfoList = operateList.filter(
          item => item.operateType === '2'
        );
      } else {
        this.$message.error('获取详情失败!');
        return false;
      }

      this.visible = true;

      this.$nextTick(() => {
        let wrap = this.$refs.scroll.wrap;
        wrap.removeEventListener('scroll', this.handleContentScroll);
        wrap.addEventListener('scroll', this.handleContentScroll);
        wrap.scrollTop = 0;
      });
    },
    close() {
      this.visible = false;
    },
    handlePreviewDetail({ name }) {
      name && (this.activeTab = name);
      let dom = this.$refs[name];
      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;

      if (!scrollDom.wrap.scrollHeight) {
        return;
      }
      this.isTabClick = true;
      scrollDom.wrap.scrollTo({ top: domScrollTop, behavior: 'smooth' });
    },
    handleContentScroll(e) {
      this.scrollTimer && clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isTabClick = false;
      }, 200);

      let wrap = this.$refs.scroll.wrap,
        childNodes = wrap.childNodes[0].childNodes,
        nodeList = [],
        previewTop = wrap.scrollTop,
        previewBottom = wrap.scrollTop + wrap.clientHeight;
      for (let i = 0; i < childNodes.length; i++) {
        let node = childNodes[i];
        nodeList.push({
          node,
          offsetTop: node.offsetTop,
          height: node.offsetHeight
        });
      }
      if (previewTop == 0) {
        this.activeTab = this.sectionList[0].name;
        return;
      } else if (previewTop == wrap.scrollHeight - wrap.clientHeight) {
        this.activeTab = this.sectionList[this.sectionList.length - 1].name;
        return;
      }
      nodeList.map((item, index) => {
        if (
          previewTop + wrap.clientHeight / 4 >= item.offsetTop &&
          !this.isTabClick
        ) {
          this.activeTab = (this.sectionList[index] || {}).name;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .details-political-organization {
    width: 900px !important;
    height: 650px;

    .el-dialog__body {
      display: flex;
      height: calc(100% - 88px);

      .status-container {
        flex-shrink: 0;
        margin-right: $medium-spacing;

        .status-list {
          background-color: $ts-table-header-hover-bg;
          cursor: pointer;
          padding: 2px;

          .status-item {
            line-height: 30px;
            padding: 0 $card-spacing;

            &.active {
              background-color: #fff;
              color: $primary-blue;
            }
          }
        }
      }

      .detail-container {
        height: 100%;
        flex: 1;
        background-color: #fff;
        padding: $medium-spacing $card-spacing;

        .el-scrollbar__view > div + div {
          margin-top: 16px;
          margin-bottom: 8px;
        }

        .section-title {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: $primary-spacing;
          display: flex;
          align-items: center;

          &::before {
            content: ' ';
            height: 16px;
            width: 4px;
            border-radius: 4px;
            margin-right: 4px;
            background-color: $primary-blue;
          }
        }
      }
    }
  }
}
</style>
