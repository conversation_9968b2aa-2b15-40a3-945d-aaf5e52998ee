<template>
  <vxe-modal
    className="dialog-add-contract"
    :title="typeTitle"
    v-model="visible"
    fullscreen
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-tabs :type="null" v-model="activeTab" @tab-click="handleTabChange">
          <ts-tab-pane
            v-for="item in tabList"
            :key="item.name"
            :label="item.label"
            :name="item.name"
          ></ts-tab-pane>
        </ts-tabs>
        <div class="tabs-content">
          <ts-form ref="form" :model="form" :rules="rules">
            <div :id="tabList[0].name" class="form-group-tips">
              <span>基本信息</span>
            </div>
            <equipment-contract-base-form
              ref="EquipmentContractBaseForm"
              :isAdd="isAdd"
              :isEdit="isEdit"
              :isUpdate="isUpdate"
              :form="form"
              :rules="rules"
              :innerDeptTreeData="innerDeptTreeData"
              :contractModelTypeList="contractModelTypeList"
              :purchaseTypeList="purchaseTypeList"
              :fundSourceList="fundSourceList"
              :yesNoList="yesNoList"
              :currencyTypeList="currencyTypeList"
            />

            <div :id="tabList[1].name" class="form-group-tips">
              <span>标的明细</span>
              <div v-show="!isUpdate">
                <ts-button
                  class="shallowButton"
                  type="primary"
                  @click="handleAddSubjectMatter"
                >
                  新增
                </ts-button>
                <ts-button
                  class="shallowButton"
                  type="primary"
                  @click="handleImportSubjectMatter"
                >
                  导入
                </ts-button>
              </div>
            </div>
            <equipment-contract-subject-matter
              :form="form"
              :rules="rules"
              :isUpdate="isUpdate"
              :currencyTypeList="currencyTypeList"
            />

            <div :id="tabList[2].name" class="form-group-tips">
              <span>付款信息</span>
              <div v-show="!isUpdate">
                <ts-button
                  class="shallowButton"
                  type="primary"
                  @click="handleAddPayment"
                >
                  新增
                </ts-button>
              </div>
            </div>
            <equipment-contract-payment
              ref="equipmentContractPayment"
              :form="form"
              :rules="rules"
              :isUpdate="isUpdate"
            />

            <div :id="tabList[3].name" class="form-group-tips">
              <span>验收信息</span>
            </div>
            <equipment-contract-acceptance
              :form="form"
              :isUpdate="isUpdate"
              :ymdList="ymdList"
            />

            <div :id="tabList[4].name" class="form-group-tips">
              <span>
                合同附件
                <span>
                  &nbsp;&nbsp;&nbsp;&nbsp;注意：每次上传的文件总数量不超过9个，文件总大小不超过{{
                    allowFileSize
                  }}M，支持{{ allowFileExtension }}类型；
                </span>
              </span>
            </div>
            <equipment-contract-files
              :form="form"
              :type="type"
              :isUpdate="isUpdate"
            />
          </ts-form>
        </div>
      </div>

      <dialog-import-subject-matter
        ref="DialogImportSubjectMatter"
        @submit="handleImportSubjectMatterSubmit"
      />
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button
          v-if="isAdd"
          type="primary"
          :loading="submitLoading"
          @click="handleSaveDraft"
        >
          存草稿
        </ts-button>
        <ts-button type="primary" :loading="submitLoading" @click="submit">
          确 定
        </ts-button>
        <ts-button
          class="shallowButton"
          :disabled="submitLoading"
          @click="close"
        >
          关 闭
        </ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import Decimal from 'decimal.js';
import { deepClone } from '@/unit/commonHandle.js';
import EquipmentContractBaseForm from './equipment-contract-base-form.vue';
import EquipmentContractSubjectMatter from './equipment-contract-subject-matter.vue';
import EquipmentContractPayment from './equipment-contract-payment.vue';
import EquipmentContractAcceptance from './equipment-contract-acceptance.vue';
import EquipmentContractFiles from './equipment-contract-files.vue';
import DialogImportSubjectMatter from './dialog-import-subject-matter.vue';

export default {
  components: {
    EquipmentContractBaseForm,
    EquipmentContractSubjectMatter,
    EquipmentContractPayment,
    EquipmentContractAcceptance,
    EquipmentContractFiles,
    DialogImportSubjectMatter
  },
  props: {
    innerDeptTreeData: {
      type: Array,
      default: () => []
    },
    contractModelTypeList: {
      type: Array,
      default: () => []
    },
    purchaseTypeList: {
      type: Array,
      default: () => []
    },
    fundSourceList: {
      type: Array,
      default: () => []
    },
    yesNoList: {
      type: Array,
      default: () => []
    },
    currencyTypeList: {
      type: Array,
      default: () => []
    },
    ymdList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    typeTitle() {
      return `${this.type === 'add' ? '新增' : '编辑'}合同`;
    },
    isAdd() {
      return this.type === 'add';
    },
    isEdit() {
      return this.type === 'edit';
    },
    isUpdate() {
      return this.type === 'update';
    }
  },
  data() {
    return {
      visible: false,
      activeTab: 'baseData',
      tabList: [
        { label: '基本信息', name: 'baseData' },
        { label: '标的物明细', name: 'objectData' },
        { label: '付款信息', name: 'paymentData' },
        { label: '验收信息', name: 'acceptanceData' },
        { label: '合同附件', name: 'contractFilesData' }
      ],

      submitLoading: false,
      allowFileSize: 200,
      allowFileExtension:
        'doc,docx,dotx,pdf,ppt,pptx,potx,ppsx,xls,xlsx,xlsm,xlsb,xltx',
      type: 'add',
      form: {
        contract: {},
        contractItemList: [],
        contractFundList: []
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async open({ type = 'add', data = {} }) {
      this.type = type;
      let { allowFileSize, allowFileExtension } = this.getParentStoreInfo(
        'globalSetting'
      );
      this.allowFileSize = allowFileSize;
      this.allowFileExtension = allowFileExtension;

      this.form = this.isAdd ? this.getInitialForm() : deepClone(data);

      if (!this.isAdd) {
        this.form.contract.validityPeriod = [
          data.contract.startDate || '',
          data.contract.endDate || ''
        ];

        this.form.contractItemList = data.contractItemList.map(item => {
          return {
            ...JSON.parse(item.modelJson)
          };
        });
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();

        if (!this.isAdd) {
          const paymentRefs = this.$refs.equipmentContractPayment.$refs;

          Object.values(paymentRefs).forEach(ref => {
            ref?.handleEcho?.();
            setTimeout(ref?.adjustHeight, 100);
          });
        }
      });
    },

    getParentStoreInfo(type) {
      return this.$getParentStoreInfo(type);
    },

    getInitialForm() {
      let { ssoOrgCode, ssoOrgName } = this.getParentStoreInfo('globalSetting');
      let { orgName, orgId } = this.getParentStoreInfo('userInfo');
      return {
        contract: {
          isTax: '1',
          currencyType: '1',
          dueIsRemind: '1',
          warrantyPeriod: 1,
          warrantyPeriodUnit: '0',
          signDate: this.$dayjs().format('YYYY-MM-DD'),
          validityPeriod: [this.$dayjs().format('YYYY-MM-DD'), ''],
          orgId: ssoOrgCode,
          orgName: ssoOrgName,
          deptName: orgName,
          deptId: orgId
        },
        contractItemList: [],
        contractFundList: []
      };
    },

    // 处理标签页切换
    handleTabChange() {
      const element = document.getElementById(this.activeTab);
      element?.scrollIntoView({ behavior: 'smooth' });
    },

    async handleSaveDraft() {
      let data = this.handleProcessingData(this.form);
      let res = await this.ajax.saveContractDraft(data);
      if (!res.success) {
        this.$newMessage('error', res.message || '暂存数据失败,请联系管理员!');
        return;
      }
      this.$newMessage('success', '暂存数据成功!');
      this.$emit('submit');
      this.close();
    },

    handleProcessingData(data) {
      let ProcessData = deepClone(data);
      ProcessData.contractItemList = ProcessData.contractItemList.map(item => ({
        modelJson: JSON.stringify(item),
        modelType: ProcessData.contract.modelType
      }));

      if (ProcessData.contract.validityPeriod[0]) {
        ProcessData.contract.startDate = ProcessData.contract.validityPeriod[0];
      }
      if (ProcessData.contract.validityPeriod[1]) {
        ProcessData.contract.endDate = ProcessData.contract.validityPeriod[1];
      }
      delete ProcessData.contract.validityPeriod;
      return ProcessData;
    },

    handleSubmitValidate(data) {
      if (data.contractFundList.length === 0) {
        this.$newMessage(
          'warning',
          '为方便后续跟进付款结算，请维护好当前合同的付款信息！'
        );
        return false;
      }

      const contractAmount = new Decimal(data.contract.amount || 0);
      const totalRatio = this.form.contractFundList.reduce(
        (acc, curr) => new Decimal(acc).plus(curr.ratio || 0),
        new Decimal(0)
      );
      const totalAmount = this.form.contractFundList.reduce(
        (acc, curr) => new Decimal(acc).plus(curr.amount || 0),
        new Decimal(0)
      );

      if (
        totalRatio.greaterThan(100) ||
        totalAmount.greaterThan(contractAmount)
      ) {
        this.$newMessage(
          'warning',
          '所有阶段累计比例总和不能大于100%,累计金额不能大于合同金额！'
        );
        return false;
      }

      if (totalRatio.lessThan(100) || totalAmount.lessThan(contractAmount)) {
        this.$newMessage(
          'warning',
          '当前合同金额未全部包含在付款信息中，请调整付款比例或金额！'
        );
        return false;
      }
      return true;
    },

    async submit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        if (!this.handleSubmitValidate(this.form)) {
          return;
        }
        let data = this.handleProcessingData(this.form);
        const action = this.type === 'add' ? 'saveContract' : 'updateContract';
        const res = await this.ajax[action](data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', res.message || `${this.typeTitle}成功!`);
          this.$emit('submit');
          this.close();
        } else {
          this.$newMessage('error', res.message || `${this.typeTitle}失败!`);
        }
      } catch (error) {
        console.error('提交失败:', error);
      } finally {
        this.submitLoading = false;
      }
    },

    // 标的物相关方法
    handleAddSubjectMatter() {
      this.form.contractItemList.push({
        id: Math.random(),
        name: '',
        model: '',
        manufacturer: '',
        quantity: '',
        unitprice: '',
        amount: ''
      });
    },

    handleImportSubjectMatter() {
      let modelType = this.form.contract.modelType;
      if (!modelType) {
        this.$newMessage('warning', '请先选择合同类别!');
        return;
      }

      this.$refs.DialogImportSubjectMatter.show({ data: { modelType } });
    },

    handleImportSubjectMatterSubmit(res) {
      res.forEach(item => {
        item.id = Math.random();
      });
      this.form.contractItemList = res;
      // this.form.contractItemList.push(...res);
    },

    // 付款信息相关方法
    handleAddPayment() {
      if (
        !this.form.contract.amount ||
        this.form.contract.amount === '0' ||
        this.form.contract.amount === '0.00' ||
        isNaN(this.form.contract.amount)
      ) {
        this.$newMessage('warning', '请先填写合同金额!');
        this.$refs.EquipmentContractBaseForm.$refs.contractAmountInput.focus();
        return;
      }

      this.form.contractFundList.push({
        step: '',
        stepElx: '',
        ratio: '',
        amount: '',
        leftAmount: '',
        schemeDate: '',
        actualDate: '',
        note: ''
      });
    },

    close() {
      this.form = {
        contract: {},
        contractItemList: [],
        contractFundList: []
      };
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-contract {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        background: #f2f2f2;
        > .vxe-modal--content {
          padding: 0 !important;
          > .content {
            min-width: 1312px !important;
            max-width: 98% !important;
            height: calc(100% - 48px) !important;

            margin: 24px auto 0px;
            padding: 16px 24px;
            display: flex;
            flex-direction: column;
            background: #fff;

            .tabs-content {
              flex: 1;
              padding-right: 8px;
              display: flex;
              overflow: auto;
            }

            .divider {
              width: 100%;
              height: 1px;
              background-color: #e5e5e5;
              margin: 16px 0;
            }

            .form-group-tips {
              width: 100%;
              color: #333;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 8px;
              margin: 8px 0;
              background-color: #f2f3f4;
              border-radius: 4px;

              > span {
                font-size: 15px;
                font-weight: 600;
                display: flex;
                align-items: center;

                &::before {
                  content: '';
                  display: inline-block;
                  width: 4px;
                  height: 16px;
                  background-color: $primary-blue;
                  margin-right: 4px;
                  border-radius: 4px;
                }
              }

              .shallowButton {
                margin-left: 8px;
              }
            }

            .delete-device {
              color: red;
              cursor: pointer;
            }

            .flex-item {
              .el-form-item__content {
                display: flex;
                align-items: center;
              }

              .date-picker-width {
                width: 140px !important;
                min-width: 140px !important;
                .el-input__inner {
                  width: 140px !important;
                }
              }

              .tar100w {
                text-align: right;
                width: 100%;
              }

              .tac100w {
                text-align: center;
                width: 100%;
              }

              .required-icon {
                color: #f56c6c;
                margin-right: 4px;
              }

              .min-input-120 {
                width: 120px !important;
                min-width: 120px !important;
                .el-input__inner {
                  width: 120px !important;
                  padding-right: 0px;
                }
              }

              .min-input {
                width: 100px !important;
                min-width: 100px !important;
                .el-input__inner {
                  width: 100px !important;

                  padding-right: 0px;
                  text-align: right;
                }
                .el-input__suffix {
                  right: 20px;
                }

                &.number {
                  width: 65px !important;
                  min-width: 65px !important;
                  .el-input__inner {
                    width: 65px !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
