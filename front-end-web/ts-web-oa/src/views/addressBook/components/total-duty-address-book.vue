<template>
  <div class="total-duty-address-book-box flex-column">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :actions="searchActions"
      :resetData="{
        dutyDate: this.$dayjs().format('YYYY-MM')
      }"
      @search="handleSearch"
    >
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_total_duty_address_book"
      ref="table"
      stripe
      border
      :columns="columns"
      @refresh="handleRefreshTable"
    />
    <DialogImportTotalDutyAddressBook
      ref="dialogImportTotalDutyAddressBook"
      @submit="handleSearch"
    />
    <DialogPrintTotalDutyDetail ref="dialogPrintTotalDutyDetail" />
    <ts-homs-select-person ref="tsHomsSelectPerson" @ok="handleSelectUserOk" />
  </div>
</template>
<script>
import DialogImportTotalDutyAddressBook from './dialog-import-total-duty-address-book.vue';
import DialogPrintTotalDutyDetail from './dialog-print-total-duty-detail.vue';
import cloneDeep from 'lodash-es/cloneDeep';
export default {
  components: {
    DialogImportTotalDutyAddressBook,
    DialogPrintTotalDutyDetail
  },
  data() {
    return {
      searchForm: {
        dutyDate: this.$dayjs().format('YYYY-MM')
      },
      searchList: [
        {
          label: '值班人',
          value: 'dutyPerson',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入值班人'
          }
        },
        {
          label: '值班时间',
          value: 'dutyDate',
          element: 'ts-month-picker',
          elementProp: {
            placeholder: '请选择值班时间',
            format: 'YYYY-MM',
            valueFormat: 'YYYY-MM'
          }
        }
      ],
      searchActions: [
        {
          label: '导入',
          click: this.handleImport,
          prop: { type: 'primary' }
        },
        {
          label: '导出',
          click: this.handleExport
        },
        {
          label: '预览',
          click: this.handlePrint
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '日期',
          prop: 'dutyDate',
          width: 100,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {},
              row.dutyDate ? this.$dayjs(row.dutyDate).format('YYYY-MM-DD') : ''
            );
          }
        },

        {
          label: '星期',
          prop: 'week',
          align: 'center',
          width: 80
        },
        {
          label: '值班人工号1',
          prop: 'dutyOneCode',
          align: 'center',
          width: 100
        },
        {
          label: '值班员1',
          prop: 'dutyOnePerson',
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.handleChangePerson(row, 'dutyOne');
                  }
                }
              },
              row.dutyOnePerson
            );
          }
        },
        {
          label: '值班人工号2',
          prop: 'dutyTwoCode',
          align: 'center',
          width: 100
        },
        {
          label: '值班员2',
          prop: 'dutyTwoPerson',
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.handleChangePerson(row, 'dutyTwo');
                  }
                }
              },
              row.dutyTwoPerson
            );
          }
        },
        {
          label: '二线值班领导工号',
          prop: 'leaderCode',
          align: 'center',
          width: 130
        },
        {
          label: '二线值班领导',
          prop: 'leaderPerson',
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.handleChangePerson(row, 'leader');
                  }
                }
              },
              row.leaderPerson
            );
          }
        },
        {
          label: '三线值班领导工号',
          prop: 'leaderTwoCode',
          align: 'center',
          width: 130
        },
        {
          label: '三线值班领导',
          prop: 'leaderTwoPerson',
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.handleChangePerson(row, 'leaderTwo');
                  }
                }
              },
              row.leaderTwoPerson
            );
          }
        },
        {
          label: '备注',
          prop: 'remark',
          width: 120,
          align: 'center'
        }
      ],
      dataList: [],
      curMonth: '',
      editData: {}
    };
  },
  mounted() {
    this.handleSearch();
  },
  methods: {
    switchRefresh() {
      this.$set(this, 'searchForm', {
        dutyDate: this.$dayjs().format('YYYY-MM')
      });
      this.$refs.table.pageNo = 1;

      this.$nextTick(() => {
        this.$refs.table.triggerRefresh();
      });
    },
    handleSearch() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page) {
      let { pageNo, pageSize } = page,
        data = {
          ...page,
          ...this.searchForm,
          sidx: 'create_date',
          sord: 'desc'
        };
      this.ajax.getTotalDutyContactList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        this.dataList = cloneDeep(res.rows);
        this.curMonth = data.dutyDate;
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleChangePerson(row, type) {
      this.editData = row;
      let echoData = {
        [`${type}Code`]: row[`${type}Code`],
        [`${type}Person`]: row[`${type}Person`]
      };
      this.$refs.tsHomsSelectPerson.open(type, {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: true,
        echoData,
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp: [`${type}Person`, `${type}Code`]
        }
      });
    },
    handleSelectUserOk(result, key) {
      let code = result[key][`${key}Code`],
        name = result[key][`${key}Person`];
      if (code == this.editData[`${key}Code`]) return false;
      let data = {
        id: this.editData.id,
        [`${key}Code`]: code,
        [`${key}Person`]: name
      };
      this.ajax.updateTotalDutyAddressBook(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功');
          this.editData = {};
          this.handleSearch();
        } else {
          this.$message.error(res.message || '操作失败');
        }
      });
    },
    handleImport() {
      this.$refs.dialogImportTotalDutyAddressBook.show();
    },
    handleExport() {
      let searchForm = {
        ...this.searchForm,
        sidx: 'create_date',
        sord: 'desc',
        pageNo: 1,
        pageSize: 9999
      };
      let aDom = document.createElement('a'),
        conditionList = Object.keys(searchForm).map(key => {
          let val = searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });

      aDom.href = `/ts-oa/api/totalDutySchedule/export?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    },
    handlePrint() {
      let curMonthStr = this.$dayjs(this.curMonth).format('YYYY年M月份');
      if (this.dataList.length == 0) {
        this.$message.warning(`${curMonthStr}暂无值班数据可预览！`);
        return;
      }

      let monthDayNum = this.$dayjs(this.curMonth).daysInMonth();

      if (this.dataList.length != monthDayNum) {
        this.$message.warning(
          `${curMonthStr}值班数据不全，无法预览，请补齐值班数据！`
        );
        return;
      }
      this.$refs.dialogPrintTotalDutyDetail.show({
        data: {
          monthStr: curMonthStr,
          dataList: this.dataList
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.total-duty-address-book-box {
  flex: 1;
  overflow: hidden;
}
</style>
