<template>
  <div class="internal-contact-person-box flex">
    <div class="left-wrap" v-if="isDefault == 1">
      <base-search-tree
        class="attendance-tree"
        ref="searchTree"
        placeholder="输入科室名进行搜索"
        :activeId="activeId"
        @tree="getTreeSuccess"
        @beforeClick="clickItemTree"
      />
    </div>
    <div class="right-wrap flex-column">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :actions="actions"
        :resetData="handleReset"
        @search="handleSearch"
      ></ts-search-bar>
      <TsVxeTemplateTable
        id="table_dept_address_book"
        ref="table"
        stripe
        border
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
    <DialogDeptAddressBookDetail
      ref="dialogDeptAddressBookDetail"
      :isDefault="isDefault"
      @submit="handleResetSearch"
    />
    <DialogImportDeptAddressBook
      ref="dialogImportDeptAddressBook"
      @submit="handleResetSearch"
    />
  </div>
</template>
<script>
import DialogDeptAddressBookDetail from './dialog-dept-address-book-detail.vue';
import DialogImportDeptAddressBook from './dialog-import-dept-address-book.vue';
export default {
  components: {
    DialogDeptAddressBookDetail,
    DialogImportDeptAddressBook
  },
  data() {
    return {
      org: {
        orgId: '',
        orgName: ''
      },
      activeId: '',
      dialogOrganizationTreeData: [],
      searchForm: {
        orgName: ''
      },
      searchList: [
        {
          label: '科室',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入科室'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '科室名称',
          prop: 'orgName',
          align: 'center'
        },
        {
          label: '联系科室（人）',
          prop: 'name',
          align: 'center'
        },
        {
          label: '电话',
          prop: 'tel',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            if (this.isAdmin) {
              let actionList = [
                {
                  label: '编辑',
                  event: this.handleAddOrEdit
                },
                {
                  label: '删除',
                  event: this.handleDelete
                }
              ];

              return h('BaseActionCell', {
                on: { 'action-select': event => event(row) },
                attrs: { actions: actionList }
              });
            } else return '';
          }
        }
      ]
    };
  },
  computed: {
    isDefault() {
      let orgContactType = this.$getParentStoreInfo().globalSetting
        .orgContactType;
      return orgContactType == 1;
    },
    isAdmin() {
      let isDeptAdressAdmin =
          this.$store.state.common.userInfo.sysRoleCode &&
          this.$store.state.common.userInfo.sysRoleCode.indexOf(
            'CONTACTS-CRUD'
          ) !== -1,
        isAdmin = this.$store.state.common.userInfo.isAdmin == 'Y';
      return isDeptAdressAdmin || isAdmin;
    },
    actions() {
      if (this.isAdmin)
        return [
          {
            label: '新增',
            click: this.handleAddOrEdit,
            prop: { type: 'primary' }
          },
          {
            label: '导入',
            click: this.handleImport
          }
        ];
      return [];
    }
  },
  mounted() {
    if (!this.isDefault) {
      this.handleSearch();
    }
  },
  methods: {
    switchRefresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.searchTree.searchVal = '';
      this.$refs.searchTree.getTreeData();

      this.$set(this, 'org', {});
      this.$set(this, 'activeId', '');
      this.$set(this, 'searchForm', {});

      this.$refs.searchTree.$refs.tsTree.cancelAllChecked();
      this.$nextTick(() => {
        this.$refs.table.triggerRefresh();
      });
    },
    getTreeSuccess(data) {
      this.dialogOrganizationTreeData = data;
      this.$nextTick(() => {
        this.handleSearch();
      });
    },
    async clickItemTree(node) {
      this.activeId = node.id;
      this.org.orgId = node.id;
      this.org.orgName = node.name;
      this.$set(this.searchForm, 'orgName', node.name);
      await this.handleSearch();
    },
    handleReset() {
      this.org = {};
      this.activeId = '';
      return {};
    },
    handleResetSearch() {
      this.org = {};
      this.activeId = '';
      this.handleSearch();
    },
    handleSearch() {
      if (this.searchForm.orgName != this.org.orgName || !this.org.orgName) {
        this.$refs.searchTree?.$refs?.tsTree?.cancelAllChecked();
      }
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page) {
      let { pageNo, pageSize } = page,
        data = {
          ...page,
          ...this.org,
          ...this.searchForm
        };
      this.ajax.getDeptContactList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleAddOrEdit(row = {}) {
      this.$refs.dialogDeptAddressBookDetail.show({
        treeData: this.dialogOrganizationTreeData,
        data: row
      });
    },
    handleImport() {
      this.$refs.dialogImportDeptAddressBook.show();
    },
    handleDelete(row) {
      this.$confirm('确定要删除该科室电话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.ajax.deleteDeptContact(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$message.success('操作成功!');
            this.handleSearch();
          } else {
            this.$message.error(res.message || '操作失败!');
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.internal-contact-person-box,
.right-wrap {
  flex: 1;
  overflow: hidden;
}
</style>
