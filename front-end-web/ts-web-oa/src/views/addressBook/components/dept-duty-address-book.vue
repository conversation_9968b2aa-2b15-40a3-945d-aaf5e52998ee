<template>
  <div class="dept-duty-address-book-box flex-column">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :actions="searchActions"
      :resetData="{}"
      @search="handleSearch"
    >
      <template slot="dutyTime">
        <base-date-range-picker
          v-model="searchForm.dutyTime"
          type="datetimerange"
        ></base-date-range-picker>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_dept_duty_address_book"
      ref="table"
      stripe
      border
      :columns="columns"
      @refresh="handleRefreshTable"
    />
    <DialogDeptDutyContactDetail
      ref="dialogDeptDutyContactDetail"
      @submit="handleSearch"
    />
    <DialogImportDutyAddressBook
      ref="dialogImportDutyAddressBook"
      @submit="handleSearch"
    />
  </div>
</template>
<script>
import DialogDeptDutyContactDetail from './dialog-dept-duty-contact-detail.vue';
import DialogImportDutyAddressBook from './dialog-import-duty-address-book.vue';
export default {
  components: {
    DialogDeptDutyContactDetail,
    DialogImportDutyAddressBook
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '值班人/值班科室',
          value: 'condition',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入值班人/值班科室'
          }
        },
        {
          label: '值班时间',
          value: 'dutyTime'
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '部门/科室',
          prop: 'deptName',
          width: 180,
          align: 'center'
        },

        {
          label: '值班人',
          prop: 'dutyPerson',
          align: 'center',
          width: 80
        },
        {
          label: '值班时间',
          align: 'center',
          width: 280,
          render: (h, { row }) => {
            return h(
              'span',
              {},
              row.starttime && row.endtime
                ? `${this.$dayjs(row.starttime).format(
                    'YYYY-MM-DD HH:mm'
                  )}~${this.$dayjs(row.endtime).format('YYYY-MM-DD HH:mm')}`
                : ''
            );
          }
        },
        {
          label: '值班电话',
          prop: 'telephone',
          align: 'center',
          width: 120
        },
        {
          label: '值班地点',
          prop: 'place',
          align: 'center'
        },
        {
          label: '值班领导',
          prop: 'leaderPerson',
          align: 'center',
          width: 80
        },
        {
          label: '值班领导电话',
          prop: 'leaderTelephone',
          align: 'center',
          width: 120
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleAddOrEdit
              },
              {
                label: '删除',
                event: this.handleDelete
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  computed: {
    isAdmin() {
      let isDeptAdressAdmin =
          this.$store.state.common.userInfo.sysRoleCode &&
          this.$store.state.common.userInfo.sysRoleCode.indexOf('zbgly') !== -1,
        isAdmin = this.$store.state.common.userInfo.isAdmin == 'Y';
      return isDeptAdressAdmin || isAdmin;
    },
    searchActions() {
      if (this.isAdmin)
        return [
          {
            label: '新增',
            click: this.handleAddOrEdit,
            prop: { type: 'primary' }
          },
          {
            label: '导入',
            click: this.handleImport
          },
          {
            label: '导出',
            click: this.handleExport
          }
        ];
      return [];
    }
  },
  mounted() {
    this.handleSearch();
  },
  methods: {
    switchRefresh() {
      this.$set(this, 'searchForm', {});
      this.$refs.table.pageNo = 1;

      this.$nextTick(() => {
        this.$refs.table.triggerRefresh();
      });
    },
    handleSearch() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page) {
      let { pageNo, pageSize } = page,
        data = {
          ...page,
          ...this.searchForm,
          sidx: 'create_date',
          sord: 'desc'
        };
      if (data.dutyTime) {
        data.starttime = data.dutyTime[0] || '';
        data.endtime = data.dutyTime[1] || '';
        delete data.dutyTime;
      }
      this.ajax.getDutyContactList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleAddOrEdit(row) {
      this.$refs.dialogDeptDutyContactDetail.show({
        data: row
      });
    },
    handleDelete(row) {
      this.$confirm('确定要刪除该值班人吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.ajax.deleteDeptDutyContact(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$message.success('操作成功!');
            this.handleSearch();
          } else {
            this.$message.error(res.message || '操作失败!');
          }
        });
      });
    },
    handleImport() {
      this.$refs.dialogImportDutyAddressBook.show();
    },
    handleExport() {
      let aDom = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });

      aDom.href = `/ts-oa/api/dutyAddressBook/export?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.dept-duty-address-book-box {
  flex: 1;
  overflow: hidden;
}
</style>
