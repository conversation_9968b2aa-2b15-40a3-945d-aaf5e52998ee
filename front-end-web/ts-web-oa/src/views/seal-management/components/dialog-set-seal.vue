<!-- 操作记录 -->
<template>
  <div>
    <vxe-modal width="800" v-model="visible" showFooter>
      <template #title>
        <span class="title">{{ title }}</span>
      </template>
      <template #default>
        <div class="content flex-column">
          <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
            <ts-form ref="ruleForm" :model="form" :rules="rules">
              <ts-form-item label="所属科室">
                <input-tree
                  v-if="deptTreeData.length"
                  v-model="form.deptId"
                  ref="tree"
                  placeholder="请选择所属科室"
                  :treeData="deptTreeData"
                  :defaultExpandedKeys="defaultExpandedKeys"
                  style="width: 100%"
                  key="id"
                  @change="treeSelect"
                ></input-tree>
              </ts-form-item>
              <ts-form-item
                label="印章名称"
                prop="sealName"
                :rules="rules.required"
              >
                <ts-input
                  v-model="form.sealName"
                  placeholder="请输入印章名称"
                  maxlength="100"
                />
              </ts-form-item>
              <ts-form-item label="印章管理人">
                <ts-input
                  v-model="form.sealUserName"
                  readonly
                  placeholder="请选择印章管理人"
                  @focus="handleClicksSelectAllUser('sealUser')"
                />
              </ts-form-item>
              <ts-form-item label="联系电话">
                <ts-input
                  v-model="form.tel"
                  placeholder="请输入联系电话"
                  maxlength="11"
                />
              </ts-form-item>
              <ts-form-item
                label="电子印章"
                prop="sealImage"
                :rules="rules.required"
              >
                <base-upload
                  ref="files"
                  v-model="form.sealImage"
                  :drag="true"
                  dragTips="建议上传大小不超过2MB的486*486或700*700的图片"
                  accept=".png,.jpg,.jepg"
                  :limit="1"
                  :moduleName="'seal'"
                />
              </ts-form-item>
              <ts-form-item label="备注">
                <ts-input
                  v-model="form.remark"
                  placeholder="请输入备注"
                  type="textarea"
                  class="textarea"
                  maxlength="200"
                  show-word-limit
                />
              </ts-form-item>
            </ts-form>
          </el-scrollbar>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <ts-button type="primary" @click="submit">提 交</ts-button>
          <ts-button class="shallowButton" @click="close">关 闭</ts-button>
        </span>
      </template>
    </vxe-modal>
    <ts-homs-select-person ref="tsHomsSelectPerson" @ok="handleSelectUserOk" />
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      title: '新增电子印章',
      visible: false,
      type: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  methods: {
    open({ data = null, type, title }) {
      this.type = type;
      this.title = title;
      this.getTree();
      this.visible = true;
      if (this.type != 'add') {
        this.form = data;
      }
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },
    // 分类树选择回调
    treeSelect(val, node, tree) {
      if (node) {
        this.form.deptName = node.data.name;
      } else {
        this.form.deptName = '';
        this.form.deptId = '';
      }
    },
    async getTree() {
      const tree = await this.ajax.getDeptTreeList();
      if (!tree.success) {
        this.$newMessage('error', res.message || '获取出错');
      }
      this.deptTreeData = tree.object || [];
      let ids = this.deptTreeData.map(e => e.id);
      this.defaultExpandedKeys = ids;
    },
    handleClicksSelectAllUser(key) {
      let echoData = {
        dept: '',
        group: '',
        sealUser: this.form.sealUser
      };
      let emp = ['sealUserName', 'sealUser'];
      this.$refs.tsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: false,
        echoData,
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp
        }
      });
    },

    handleSelectUserOk(result, key) {
      const { allNames: sealUserName, sealUser } = result[key];
      this.$set(this.form, 'sealUserName', sealUserName);
      this.$set(this.form, 'sealUser', sealUser);
      this.$forceUpdate();
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        let Api = this.ajax.commSealManageSave;
        if (this.type == 'edit') {
          Api = this.ajax.commSealManageUpdate;
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.type = '';
      this.title = '';
      this.$refs.ruleForm.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  color: #333;
  font-weight: 700;
}
.content {
  height: 500px;
  background: #fff;
  overflow: hidden;
  position: relative;
  /deep/ {
    .textarea {
      .el-textarea__inner {
        min-height: 120px !important;
        max-height: 120px !important;
      }
    }
  }
}
</style>
