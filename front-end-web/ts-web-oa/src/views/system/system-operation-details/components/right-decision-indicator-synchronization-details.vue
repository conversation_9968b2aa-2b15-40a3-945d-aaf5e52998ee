<template>
  <div class="children-table">
    <div class="title">指标详情</div>
    <ts-search-bar
      v-model="searchForm"
      :resetData="resetData"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    />

    <base-table
      ref="table"
      class="form-table"
      border
      stripe
      :propPageSize="500"
      :columns="columns"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import moment from 'moment';
export default {
  components: {},
  data() {
    return {
      searchForm: {
        date: [
          moment()
            .subtract(1, 'days')
            .format('YYYY-MM-DD'),
          moment()
            .subtract(1, 'days')
            .format('YYYY-MM-DD')
        ]
      },
      resetData: {
        date: [
          moment()
            .subtract(1, 'days')
            .format('YYYY-MM-DD'),
          moment()
            .subtract(1, 'days')
            .format('YYYY-MM-DD')
        ]
      },
      searchList: [
        {
          label: '',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入机构名称',
            clearable: true
          }
        },
        {
          label: '',
          value: 'pname',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入指标名称',
            clearable: true
          }
        },
        {
          label: '指标日期',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            allowClear: true,
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '机构名称',
          prop: 'orgName',
          align: 'center'
        },
        {
          label: '指标名称',
          prop: 'pname',
          align: 'center'
        },
        {
          label: '指标日期',
          prop: 'pdate',
          align: 'center'
        },
        {
          label: '指标值',
          prop: 'pvalue',
          align: 'center'
        },
        {
          label: '指标记录数',
          prop: 'zbCount',
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center',
          width: 95
        }
      ]
    };
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { date = [] } = this.searchForm,
        [beginDate = '', endDate = ''] = date,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          beginDate,
          endDate,
          sidx: 'pdate',
          sord: 'desc'
        };
      delete searchForm.date;

      let res = await this.ajax.PisZbCollectionList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });

      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.children-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;

  ::v-deep {
    .search-item {
      &:nth-child(1),
      &:nth-child(2) {
        width: 115px;
        .ts-input {
          min-width: 115px;
          width: 115px;
          .el-input__inner {
            width: 115px;
            padding-right: 0px;
          }
        }
      }
    }
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
    }
  }
}
</style>
