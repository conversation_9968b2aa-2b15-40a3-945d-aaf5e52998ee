<template>
  <vxe-modal
    className="dialog-queto-table"
    width="800"
    height="80%"
    title="公共库引用"
    v-model="visible"
    showFooter
  >
    <template #default>
      <div class="content flex-column">
        <ts-search-bar
          v-model="searchForm"
          :actions="[]"
          :formList="searchList"
          :resetData="{}"
          :elementCol="14"
          @search="search"
        >
          <template #right>
            <div>
              引用机构
              <ts-select
                v-model="sysOrgCode"
                filter
                multiple
                style="width: 200px;"
              >
                <ts-option
                  v-for="item in orgList.filter(e => e.id != -1)"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </div>
          </template>
        </ts-search-bar>
        <TsVxeTemplateTable
          ref="table"
          class="formTable"
          id="table_dialog-queto-table"
          :columns="columns"
          @refresh="handleRefreshTable"
          @selection-change="handleSelection"
        />
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">确 定</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
export default {
  props: {
    orgList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      sysOrgCode: [],
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'fuzzyScreening',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入类型编码，类型名称搜索'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 50
        },
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '类型编码',
          prop: 'typeCode',
          align: 'center',
          minWidth: 120
        },
        {
          label: '类型名称',
          prop: 'typeName',
          align: 'center',
          minWidth: 120
        },
        {
          label: '类型描述',
          prop: 'remark',
          align: 'center',
          minWidth: 120
        },
        {
          label: '系统编码',
          prop: 'sysCode',
          minWidth: 120,
          align: 'center'
        }
      ],
      selectionList: []
    };
  },
  methods: {
    async open() {
      this.visible = true;
      this.$nextTick(() => {
        this.search();
      });
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleSelection(e) {
      this.selectionList = e;
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          ssoOrgCode: '*PUBLIC*',
          sidx: 'CREATE_DATE',
          sord: 'desc'
        };
      let res = await this.ajax.dictTypeList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    async submit() {
      if (this.selectionList.length == 0) {
        this.$newMessage('error', '请选择需要引用的字典项目');
        return;
      }
      if (this.sysOrgCode.length == 0) {
        this.$newMessage('error', '请选择需要引用的机构');
        return;
      }
      let data = {
        dictTypeList: this.selectionList.map(e => e.id),
        ssoOrgCodeList: this.sysOrgCode
      };
      let API = this.ajax.dictMoreSync;
      const res = await API(data);

      if (!res.success) {
        this.$newMessage('error', res.message || '【引用】失败!');
        return;
      }
      this.$newMessage('success', '【引用】成功!');
      this.$emit('refresh');
      this.close();
    },

    close() {
      this.visible = false;
      this.sysOrgCode = [];
      this.selectionList = [];
      this.searchForm = {};
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-queto-table {
  /deep/ {
    .content {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
