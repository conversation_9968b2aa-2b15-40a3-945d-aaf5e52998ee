<template>
  <div class="multi-institution-dictionary flex-column">
    <div class="header">
      <colmun-head background="#fff">
        <template #title>
          <span class="title">所属机构</span>
          <ts-select
            v-model="orgCode"
            filterable
            placeholder="请选择机构"
            style="margin-left: 10px;"
            @change="changeCode"
          >
            <ts-option
              v-for="item in orgList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>
      </colmun-head>
    </div>
    <div class="container">
      <div class="container-left flex-column">
        <colmun-head title="数据字典" background="#fff" />
        <ts-search-bar
          v-model="searchFormLeft"
          :actions="[]"
          :formList="searchListLeft"
          :resetData="{}"
          :elementCol="14"
          @search="searchLeft"
        >
          <template v-slot:right>
            <ts-button
              type="primary"
              v-if="orgCode == '*PUBLIC*'"
              @click="handleAdd"
            >
              新增
            </ts-button>
            <ts-button
              class="shallowButton"
              v-if="orgCode == '*PUBLIC*'"
              @click="handleQueto"
            >
              引用到机构
            </ts-button>
          </template>
        </ts-search-bar>
        <TsVxeTemplateTable
          ref="tableLeft"
          class="formTable"
          id="table_multi_institution_dictionary_left"
          :columns="columnsLeft"
          disabled-row-field="isDeleted"
          disabled-row-value="Y"
          @refresh="handleRefreshTableLeft"
          @current-change="handleCurrent"
        />
      </div>
      <div class="container-right flex-column">
        <colmun-head
          :title="`${selectionList?.typeName || ''}字典项目`"
          background="#fff"
        />
        <ts-search-bar
          v-model="searchFormRight"
          :actions="[]"
          :formList="searchListRight"
          :resetData="{}"
          :elementCol="14"
          @search="searchRight"
        >
          <template v-slot:right>
            <ts-button type="primary" @click="handleAddItem">
              新增
            </ts-button>
          </template>
        </ts-search-bar>
        <TsVxeTemplateTable
          ref="tableRight"
          class="formTable"
          id="table_multi_institution_dictionary_right"
          disabled-row-field="isDeleted"
          disabled-row-value="Y"
          :columns="columnsRight"
          @refresh="handleRefreshTableRight"
        />
      </div>
    </div>
    <dialog-add
      ref="DialogAdd"
      @refresh="handleRefreshTableLeft"
      :typeList="typeList"
    />
    <dialog-add-dictionary-item
      ref="DialogAddDictionaryItem"
      @refresh="handleRefreshTableRight"
    />
    <dialog-queto-table
      ref="DialogQuetoTable"
      :orgList="orgList"
      @refresh="handleRefreshTableLeft"
    />
  </div>
</template>

<script>
import DialogAdd from './components/dialog-add.vue';
import DialogAddDictionaryItem from '../dictionary/components/dialog-add-dictionary-item.vue';
import DialogQuetoTable from './components/dialog-queto-table.vue';
import index from './mixins/index';
export default {
  mixins: [index],
  components: { DialogAdd, DialogAddDictionaryItem, DialogQuetoTable },
  data() {
    return {
      orgCode: '*PUBLIC*',
      orgList: []
    };
  },
  methods: {
    async refresh() {
      await this.getDeptOrgTree();
      await this.getDictionary();
      this.$nextTick(() => {
        this.searchLeft();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.multi-institution-dictionary {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 16px 8px;
  overflow: hidden;
  /deep/ {
    .header {
      height: 50px;
      .title {
        font-weight: bold;
      }
    }
    .container {
      flex: 1;
      max-height: calc(100% - 50px);
      display: flex;
      gap: 8px;
      .container-left,
      .container-right {
        width: calc(50% - 4px);
      }
    }
  }
}
</style>
