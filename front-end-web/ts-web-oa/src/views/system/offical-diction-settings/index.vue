<template>
  <div class="trasen-container offical-diction-settings-box">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    />
    <TsVxeTemplateTable
      id="table_offical_diction"
      class="offical-diction-table"
      ref="table"
      stripe
      border
      :defaultSort="{
        sord: 'IS_DEFAULT DESC,(sort_no+0)',
        sidx: 'asc'
      }"
      :columns="columns"
      :drag-row="{
        draggable: true,
        dragClass: '.drag-node'
      }"
      @refresh="handleRefreshTable"
      @row-drag-change="handleRowDragChange"
    />
    <DialogOfficalDictionDetail
      ref="dialogOfficalDictionDetail"
      @submit="search"
    />
  </div>
</template>

<script>
import DialogOfficalDictionDetail from './components/dialog-offical-diction-detail.vue';
import tableMixins from './mixins/table-mixins';
export default {
  components: {
    DialogOfficalDictionDetail
  },
  mixins: [tableMixins],
  data() {
    return {};
  },
  computed: {
    isAdmin() {
      return this.$store.state.common.userInfo.isAdmin == 'Y';
    }
  },
  created() {
    this.$nextTick(() => {
      this.search();
    });
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    async handleRefreshTable(page) {
      let { pageNo, pageSize } = page,
        data = {
          ...this.searchForm,
          ...page
        };
      await this.ajax.getOfficalDictionList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleEditDefaultValue(row) {
      let changeVal = row['isDefaultValue'] == '1' ? '0' : '1';
      let data = {
        id: row.id,
        isDefaultValue: changeVal
      };
      this.ajax.updateOfficalDictionDefaultValue(data).then(res => {
        if (res.success && res.statusCode === 200) {
          // row['isDefaultValue'] = changeVal;
          this.$refs.table.rows.forEach(item => {
            item.id == row.id
              ? (item.isDefaultValue = changeVal)
              : (item.isDefaultValue = 0);
          });
          this.$message.success('操作成功!');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      });
    },
    handleEditHideValue(row) {
      let changeVal = row['isHideValue'] == '1' ? '0' : '1';
      let data = {
        id: row.id,
        isHideValue: changeVal
      };
      this.ajax.updateOfficalDictionHideValue(data).then(res => {
        if (res.success && res.statusCode === 200) {
          row['isHideValue'] = changeVal;
          this.$message.success('操作成功!');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      });
    },
    handleRowDragChange(rows, page) {
      let { pageNo, pageSize } = page;
      rows.forEach((row, index) => {
        row.pageIndex = index + 1 + (pageNo - 1) * pageSize;
      });
      this.$refs.table.refresh({ rows });
    },
    handleAddOrEdit(row = {}) {
      this.$refs.dialogOfficalDictionDetail.show(row);
    },
    handleDelete(row) {
      this.$confirm('确定要删除该常用语吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.ajax
          .deleteOfficalDiction({
            id: row.id
          })
          .then(res => {
            if (res.success && res.statusCode === 200) {
              this.$message.success('操作成功!');
              this.search();
            } else {
              this.$message.error(res.message || '操作失败!');
            }
          });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.offical-diction-settings-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
/deep/ {
  .offical-diction-table .drag-node {
    cursor: move;
  }
}
</style>
