<!-- 问卷考评设置页面 -->
<template>
  <ts-dialog
    class="dialog-add"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="90%"
    @close="close"
  >
    <div class="content flex-column">
      <ts-search-bar
        v-model="searchForm"
        :actions="actions"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="{}"
      >
      </ts-search-bar>
      <base-table
        class="form-table"
        ref="table"
        border
        stripe
        v-loading="Loading"
        :columns="columns"
        @refresh="handleRefreshTable"
        @selection-change="handleSelectionChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关闭</ts-button>
    </span>
    <dialog-add-subject ref="dialogAddSubject" @refreshTable="search" />
    <question-detail ref="questionDetail" />
  </ts-dialog>
</template>
<script>
import settingQuestion from './mixins/settingQuestion';
import dialogAddSubject from './dialog-add-subject.vue';
import questionDetail from '../../components/question-detail.vue';
export default {
  mixins: [settingQuestion],
  components: { dialogAddSubject, questionDetail },
  created() {},
  data() {
    return {
      title: '',
      visible: false,
      allLoading: false,
      Loading: false,
      id: '',
      SelectionRows: [],
      disabled: false
    };
  },
  methods: {
    open(row) {
      this.id = row.id;
      this.title = '问卷设置';
      this.disabled = row.status == '1' ? true : false;
      if (!this.disabled) {
        this.actions = [
          {
            label: '新增',
            click: this.handleAdd,
            prop: { type: 'primary' }
          },
          {
            label: '删除',
            click: this.handleDelets,
            prop: { type: 'primary' }
          },
          {
            label: '预览',
            click: this.handleDetails,
            prop: { type: 'primary' }
          }
        ];
      } else {
        this.actions = [
          {
            label: '预览',
            click: this.handleDetails,
            prop: { type: 'primary' }
          }
        ];
      }
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
      this.visible = true;
    },
    close() {
      this.SelectionRows = [];
      this.disabled = false;
      this.visible = false;
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    // 批量选择
    handleSelectionChange(rows) {
      this.SelectionRows = rows;
    },
    // 批量删除
    async handleDelets() {
      if (this.SelectionRows.length == 0) {
        this.$message.warning('请选择需要删除的记录');
        return;
      }
      try {
        await this.$confirm(`<span>您确认删除题目？</span>`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        let ids = [];
        this.SelectionRows.forEach(item => {
          ids.push(item.id);
        });
        this.ajax.deleteBatchEvaluationSet({ ids: ids.join(',') }).then(res => {
          if (!res.success) {
            this.$message.error('删除失败');
            return;
          }
          this.$message.success('删除成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    // 预览
    handleDetails() {
      this.$refs.questionDetail.open(this.id);
    },
    handleAdd() {
      this.$refs.dialogAddSubject.open(this.id);
    },
    handleEdit(row) {
      this.$refs.dialogAddSubject.edit(row, this.disabled);
    },
    async handleDelete(row) {
      if (this.disabled) {
        this.$message.warning('考评进行中不能删除题目');
        return;
      }
      try {
        await this.$confirm(
          `<span>您确认删除 <span style="color: #5260ff">${row.optionTitle}</span> 题目？</span>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.ajax.deleteEvaluationSet({ ...row }).then(res => {
          if (!res.success) this.$message.error('删除失败');
          this.$message.success('删除成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    // 批量删除
    async handleDeleteBatch() {
      if (this.SelectionRows.length == 0) {
        this.$message.warning('请选择要删除的记录');
        return;
      }
      try {
        await this.$confirm(
          `<span>您确认删除 <span style="color: #5260ff">${row.title}</span> 题目？</span>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        let ids = [];
        this.SelectionRows.forEach(item => {
          ids.push(item.id);
        });
        this.ajax.deleteBatchEvaluationSet({ ids: ids.join(',') }).then(res => {
          if (!res.success) this.$message.error('删除失败');
          this.SelectionRows = [];
          this.$message.success('删除成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleRefreshTable() {
      this.loading = true;
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        masterId = this.id,
        searchForm = {
          ...this.searchForm,
          masterId,
          pageNo,
          pageSize,
          sidx: 'option_sort',
          sord: 'asc'
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      this.ajax.getQuestionSetManagelist(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$message.error(res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-add {
  .content {
    min-height: 500px;
    max-height: 500px;
    overflow: auto;
    .upload_Button {
      /deep/ .el-button {
        color: #5260ff;
        border: none;
        line-height: 30px;
      }
    }
    ::v-deep {
      .suffix-input {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #eee;
        color: rgb(121, 121, 121);
        padding-left: 4px;
      }
      .bts-showAll {
        width: 160px;
        span {
          max-width: 140px;
        }
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
