<!-- 新增 编辑题目 弹出页 --><template>
  <ts-dialog
    class="dialog-add"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="950"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="120px">
        <ts-form-item
          label="题目标题:"
          prop="optionTitle"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.optionTitle"
            :maxlength="50"
            placeholder="请输入"
          />
        </ts-form-item>
        <ts-form-item label="题目描述:">
          <ts-input
            v-model="form.optionRemark"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>
        <ts-form-item
          label="是否必须:"
          prop="optionRequired"
          :rules="rules.requireds"
        >
          <el-select v-model="form.optionRequired" :disabled="disabled">
            <el-option key="01" label="请选择" value=""></el-option>
            <el-option :key="1" label="是" :value="1"></el-option>
            <el-option :key="0" label="否" :value="0"></el-option>
          </el-select>
        </ts-form-item>
        <ts-form-item
          label="题目类型:"
          prop="optionType"
          :rules="rules.requireds"
        >
          <el-select v-model="form.optionType" :disabled="disabled">
            <el-option :key="0" label="请选择" value=""></el-option>
            <el-option :key="3" label="下拉框" :value="3"></el-option>
          </el-select>
        </ts-form-item>
        <ts-form-item
          label="题目选项值:"
          prop="optionValue"
          :rules="rules.required"
          v-if="form.optionType == 3"
        >
          <ts-input
            v-model="form.optionValue"
            :maxlength="50"
            placeholder="格式(选项间用分号隔开):选项1;选项2"
            :disabled="disabled"
          />
        </ts-form-item>
        <ts-form-item
          label="下拉框选项值:"
          prop="selectOption"
          :rules="rules.required"
          v-if="form.optionType == 3"
        >
          <ts-input
            v-model="form.selectOption"
            placeholder="格式(选项间用分号隔开):选项1;选项2"
            :disabled="disabled"
          />
        </ts-form-item>
        <ts-form-item
          label="下拉框选项分数:"
          prop="selectScore"
          :rules="rules.required"
          v-if="form.optionType == 3"
        >
          <ts-input
            v-model="form.selectScore"
            placeholder="格式(选项间用分号隔开):选项1;选项2"
            :disabled="disabled"
          />
        </ts-form-item>
        <ts-form-item label="序号:" prop="optionSort">
          <ts-input
            v-model="form.optionSort"
            :rules="rules.required"
            type="number"
            placeholder="请输入"
          />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit()">提交</ts-button>
      <ts-button @click="close">关闭</ts-button>
    </span>
  </ts-dialog>
</template>
<script>
export default {
  created() {},
  data() {
    return {
      title: '',
      visible: false,
      form: {
        optionTitle: '',
        optionRemark: '',
        optionRequired: '',
        optionType: '',
        optionValue: '',
        selectOption: '',
        selectScore: '',
        optionSort: '0'
      },
      rules: {
        required: { required: true, message: '必填' },
        requireds: { required: true, message: '必选' }
      },
      api: '',
      masterId: '',
      disabled: false
    };
  },
  methods: {
    open(id) {
      this.title = '题目添加';
      this.api = 'saveEvaluationSet';
      this.masterId = id;
      this.visible = true;
    },
    edit(row, disabled = false) {
      this.masterId = row.masterId;
      this.form = { ...row };
      this.title = '题目编辑';
      this.api = 'updateEvaluationSet';
      this.disabled = disabled;
      this.visible = true;
    },
    close() {
      this.api = '';
      this.disabled = false;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
        this.form = this.$options.data().form;
      });
      this.visible = false;
    },
    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.form);
      let length1 = data.selectOption.split(';').length;
      let length2 = data.selectScore.split(';').length;
      if (length1 != length2) {
        this.$message.warning('下拉选项值和下拉分数值不对应，请检查');
        return;
      }
      data.masterId = this.masterId;
      this.ajax[this.api]({ ...data }).then(res => {
        if (!res.success) {
          this.$message.error(submitres.message || '操作失败');
          return;
        }
        this.close();
        this.$emit('refreshTable');
        this.$message.success('新增成功!');
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-add {
  .content {
    max-height: 500px;
    overflow: auto;
    .upload_Button {
      /deep/ .el-button {
        color: #5260ff;
        border: none;
        line-height: 30px;
      }
    }
    ::v-deep {
      .suffix-input {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #eee;
        color: rgb(121, 121, 121);
        padding-left: 4px;
      }
      .bts-showAll {
        width: 160px;
        span {
          max-width: 140px;
        }
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
