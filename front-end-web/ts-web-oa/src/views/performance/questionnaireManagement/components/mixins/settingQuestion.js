export default {
  data() {
    return {
      searchForm: {},

      actions: [
        {
          label: '新增',
          click: this.handleAdd,
          prop: { type: 'primary' }
        },
        {
          label: '删除',
          click: this.handleDelets,
          prop: { type: 'primary' }
        },
        {
          label: '预览',
          click: this.handleDetails,
          prop: { type: 'primary' }
        }
      ],
      searchList: [
        {
          label: '题目名称:',
          value: 'optionTitle',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入题目名称'
          }
        }
      ],
      columns: [
        {
          label: '',
          type: 'selection',
          align: 'center'
        },
        {
          label: '题目名称',
          prop: 'optionTitle',
          align: 'center',
          minWidth: 180
        },
        {
          label: '题目描述',
          prop: 'optionRemark',
          align: 'center',
          minWidth: 180
        },
        {
          label: '题目类型',
          prop: 'optionType',
          align: 'center',
          width: 80,
          formatter: row => {
            return row.optionType == '3' ? (
              <span>下拉框</span>
            ) : (
              <span style="color:#fd9d53">是</span>
            );
          }
        },
        {
          label: '是否必须',
          prop: 'optionRequired',
          align: 'center',
          width: 80,
          formatter: row => {
            return row.optionRequired == '0' ? (
              <span>否</span>
            ) : (
              <span>是</span>
            );
          }
        },
        {
          label: '创建人',
          prop: 'createUserName',
          width: 80,
          align: 'center'
        },
        {
          label: '创建日期',
          prop: 'createDate',
          width: 180,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 90,
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDelete
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
