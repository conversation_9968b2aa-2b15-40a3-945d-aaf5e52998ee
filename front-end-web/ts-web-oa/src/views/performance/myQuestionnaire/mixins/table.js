export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '问卷标题:',
          value: 'title',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入问卷标题'
          }
        }
      ],
      columns: [
        {
          label: '考评人',
          prop: 'userName',
          width: 130,
          align: 'center'
        },
        {
          label: '问卷标题',
          prop: 'title',
          align: 'center',
          width: 150,
          formatter: row => {
            return (
              <span
                class="details-span hands"
                onClick={() => {
                  this.handleDetails(row);
                }}>
                {row.title}
              </span>
            );
          }
        },
        {
          label: '被考评人',
          prop: 'examUserName',
          align: 'center'
        },
        {
          label: '描述',
          prop: 'remark',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'releaseStatus',
          align: 'center',
          formatter: row => {
            return row.releaseStatus == 0 ? (
              <span>待提交</span>
            ) : (
              <span style="color:#fd9d53">已提交</span>
            );
          }
        },
        {
          label: '发布时间',
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '发布人',
          prop: 'createUserName',
          align: 'center'
        },
        {
          label: '提交时间',
          prop: 'releaseDate',
          align: 'center',
          formatter: row => {
            return row.releaseStatus == 0 ? (
              <span>无</span>
            ) : (
              <span>{row.releaseDate}</span>
            );
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 80,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '填写答卷',
                event: this.handleEdit,
                isStatus: '0'
              }
            ];
            let newArr = arr.filter(e => {
              return e.isStatus.indexOf(row.releaseStatus) > -1;
            });
            return (
              <BaseActionCell
                actions={newArr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  }
};
