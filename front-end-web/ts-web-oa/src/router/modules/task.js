export default [
  {
    path: '/task/task-registration',
    component: resolve =>
      require([`@/views/task/task-registration/index.vue`], resolve),
    styleName: '',
    name: '任务登记'
  },
  {
    path: '/task/task-supervision',
    component: resolve =>
      require([`@/views/task/task-supervision/index.vue`], resolve),
    styleName: '',
    name: '任务督办'
  },
  {
    path: '/task/task-handling',
    component: resolve =>
      require([`@/views/task/task-handling/index.vue`], resolve),
    styleName: '',
    name: '任务办理'
  },
  {
    path: '/task/task-to-my',
    component: resolve =>
      require([`@/views/task/task-to-my/index.vue`], resolve),
    styleName: '',
    name: '抄送给我的'
  },
  {
    path: '/task/all-tasks',
    component: resolve =>
      require([`@/views/task/all-tasks/index.vue`], resolve),
    styleName: '',
    name: '全部任务'
  },
  {
    path: '/task/task-setting',
    component: resolve =>
      require([`@/views/task/task-setting/index.vue`], resolve),
    styleName: '',
    name: '基础设置'
  },
  {
    path: '/systemLog/index',
    component: resolve => require([`@/views/systemLog/index.vue`], resolve),
    styleName: '',
    name: '系统日志'
  }
];
