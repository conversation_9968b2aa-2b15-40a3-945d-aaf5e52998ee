<template>
  <div ref="bodydiv">
    <el-popover
      ref="popoverRef"
      placement="bottom-start"
      :visible-arrow="false"
      :width="poppverWidth"
    >
      <el-tree
        :data="treeData"
        :node-key="nodeKey"
        :props="treeProp"
        ref="elTree"
        class="treeInput"
        empty-text="暂无数据"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="false"
        highlight-current
        @node-click="handleNodeClick"
        :filter-node-method="filterNode"
        @blur="handleBlur"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <span class="flex-col-center"
            ><i
              :class="
                node.level == 1
                  ? 'first-tree-icon'
                  : !node.isLeaf && (node.childNodes || []).length > 0
                  ? 'has-child-tree-icon'
                  : 'leaf-tree-icon'
              "
            ></i
            >{{ node.label }}</span
          >
        </span>
      </el-tree>
      <el-input
        slot="reference"
        v-model="fullPath"
        :clearable="true"
        :placeholder="placeholder"
        :disabled="disabled"
        @input="handleInputChange"
        @click.native="handleInputClick"
        class="addform-input"
      ></el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'InputTree',
  model: {
    prop: 'value',
    event: 'valuechange'
  },
  props: {
    nodeKey: {
      type: String,
      default: () => {
        return 'id';
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    treeProp: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          children: 'children'
        };
      }
    },
    textName: {
      type: String,
      default: () => {
        return 'name';
      }
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    value: {
      default: () => {
        return null;
      }
    },
    //input的placeholder
    placeholder: {
      type: String,
      default: () => {
        return '请输入';
      }
    }
  },
  mounted() {
    this.poppverWidth = this.$refs.bodydiv.clientWidth;
    this.poppverWidth > 300 ? null : (this.poppverWidth = 300);
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.$refs.elTree.setCurrentKey(val);
          });
        }
        if (!val) {
          this.fullPath = '';
        } else {
          this.findNode(this.treeData);
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      fullPath: '', //input全路径展示
      inputTimer: null, //输入防抖
      showPopover: false, //控制展示是否显示气泡框
      currentId: null, //当前点击选中的ID
      poppverWidth: null //气泡框的宽度
    };
  },
  methods: {
    // findNode( treeCol, fullPath, match ){
    //     for(let item of treeCol ){
    //         let path = fullPath;
    //         if( item[this.nodeKey] == match ){
    //             fullPath = (fullPath ? fullPath+'-':'') + item[this.treeProp.label];
    //             return fullPath
    //         }

    //         if( (item[this.treeProp.children] || []).length ){

    //         }
    //     }

    //     return false;
    // },
    findNode(treeCol) {
      let fullPath = '';
      for (let item of treeCol) {
        if (item[this.nodeKey] == this.value) {
          fullPath = item[this.textName];
          this.fullPath = item[this.textName];
          break;
          // return item.fullPath;
        }

        if (item[this.treeProp.children]) {
          fullPath = this.findNode(item[this.treeProp.children]);
        }
      }

      return fullPath;
    },
    //处理节点点击事件
    handleNodeClick(val, node, tree) {
      if (this.currentId == val.id) {
        this.$refs.elTree.setCurrentKey(null);
        this.currentId = null;
        this.fullPath = '';
        this.$emit('valuechange', null);
        this.$emit('change', null, null, tree);
      } else {
        this.currentId = val.id;
        this.fullPath = val[this.textName];
        this.$emit('valuechange', val.id);
        this.$emit('change', val, node, tree);
      }
      this.$refs.popoverRef.doClose();
      // if(node.checked){
      //     this.$refs.elTree.setCheckedKeys([val[this.nodeKey]]);
      //     // this.fullPath = val.fullPath;
      // }
    },
    //处理input失焦事件
    handleBlur() {
      this.showPopover = false;
    },
    //节点过滤
    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(label => label.indexOf(value) !== -1);
    },
    //处理输入框清空操作
    // handleInputClear(){

    // },
    //处理input 改变，搜索下拉框
    handleInputChange() {
      this.$emit('valuechange', null);
      this.$emit('change', null, null, this.$refs.elTree);
      this.currentId = null;
      this.$refs.elTree.setCurrentKey(null);
      this.showPopover = true;
      this.inputTimer && clearTimeout(this.inputTimer);

      this.$refs.elTree.filter(this.fullPath);
    },
    handleInputClick() {
      this.showPopover = !this.showPopover;
    }
  }
};
</script>

<style lang="scss" scoped>
.popover {
  width: 100%;
}
.treeInput {
  max-height: 250px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 8px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background: rgba(153, 153, 153, 0.8);
    }
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  &.el-tree {
    /deep/.el-tree-node__content {
      // display: block !important;
    }
    /deep/.el-tree-node__children {
      // overflow: visible !important;
    }
  }
}
.first-tree-icon {
  background: url(./../../assets/img/other/ztree_all.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.has-child-tree-icon {
  background: url(./../../assets/img/other/ztree_folder.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.leaf-tree-icon {
  background: url(./../../assets/img/other/ztree_file.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
</style>
