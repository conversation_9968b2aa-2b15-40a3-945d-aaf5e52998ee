
.trasen-search-contents {
  position: relative;
  z-index: 999;
  border-bottom: 1px solid $container-border;
  display: flex;
  justify-content: space-between;
}
.left-search-content {
  display: flex;
  flex-wrap: wrap;
}
.search-items {
  margin-bottom: 8px;
}
.left-search-content,
.reset-btn,
.more-search-content,
.search-items,
.search-lable {
  margin-right: 8px;
}
.reset-btn,
.more-search-content,
.search-items {
  height: 30px;
}
.search-items .el-date-editor {
  width: 205px;
}
.el-icon-refresh-right.reset-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-search-content {
  cursor: pointer;
  width: 40px;
  background-color: #fff;
  border: 1px solid #eee;
  color: #333;
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  box-sizing: border-box;
  text-align: center;
  
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover,
  &.active-search-list {
    color: $primary-blue !important;
    border-color: $primary-blue !important;
  }
  .oaicon.oa-icon-search_list {
    font-size: 25px;
    &:hover {
      opacity: 0.8;
    }
  }
}
.search-list-item {
  min-height: 32px;
  margin-bottom: $primary-spacing;
  display: flex;
  align-items: center;
}
.label-col {
  padding-right: $primary-spacing;
}
.reset-btn {
  width: 30px;
  height: 30px;
  font-size: 16px;
  cursor: pointer;
  &:hover {
    color: $primary-blue;
  }
}
.label-title {
  width: 100px;
  flex-shrink: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: right;
  margin-right: 8px;
}
.search-items{
  display: flex;
  white-space: nowrap;
  align-items: center;
  margin-right: 8px;
  margin-bottom: 8px;
}
.search-items {
  /deep/ .el-select,
  .el-input {
    width: 140px;
  }
}
.right-action-content {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  position: absolute;
  bottom: 8px;
  right: 0px;
}


.action-content {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.more-icon {
  margin-left: 4px;
}
.more-btn-content {
  position: relative;
  margin-left: $primary-spacing;
}
.hidden-btn-container {
  position: absolute;
  top: calc(100% + #{$primary-spacing}/ 2);
  right: 0;
  background-color: $primary-white;
  border-radius: $medium-radius;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
  min-width: 152px;
  max-height: 0;
  transition: all 0.3s;
  overflow: hidden;
  &.is-active {
    padding: 5px 0;
    max-height: 100vh;
  }
}
.hidden-btn-item {
  padding: 0 $medium-spacing;
  line-height: 30px;
  height: 30px;
  &:hover {
    background-color: $ts-menu-hover;
    color: $primary-blue;
    cursor: pointer;
  }
  &:active {
    color: $primary-blue-active;
  }
}

.search-describe {
  font-size: $primary-title-size;
  margin-right: $primary-spacing;
}

.toast-contents {
  color: #fff;
}
.SearchBtn {
  display: flex;
  margin-bottom: 8px;
}