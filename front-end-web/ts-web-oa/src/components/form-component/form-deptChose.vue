<template>
  <ts-ztree-select
    :disabled="disabled"
    :inpText.sync="internalValueLabel"
    :inpVal.sync="internalValue"
    :data="formItemOptions"
    @update:inpVal="updateValue"
  />
</template>

<script>
export default {
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    field: {
      type: Object,
      Required: true
    },
    value: {
      type: String,
      default: () => ''
    },
    formItemOptions: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.internalValue = newValue;

        // 回显科室中文
        if (newValue && !this.internalValueLabel) {
          this.handleGetTreeNodeLabel(newValue);
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      internalValue: this.value,
      internalValueLabel: ''
    };
  },
  methods: {
    updateValue(value) {
      this.$emit('change', value);
    },

    handleGetTreeNodeLabel(newValue) {
      let result = this.findNodeById(this.formItemOptions[0], newValue);
      if (result) {
        this.internalValueLabel = result.name;
      }
    },

    findNodeById(tree, id) {
      // 如果当前节点的 id 匹配，直接返回该节点
      if (tree.id === id) {
        return tree;
      }

      // 如果有子节点，遍历子节点递归查找
      if (tree.children && tree.children.length > 0) {
        for (let i = 0; i < tree.children.length; i++) {
          const result = this.findNodeById(tree.children[i], id);
          if (result) {
            return result;
          }
        }
      }

      return null;
    }
  }
};
</script>

<style lang="scss" scoped></style>
