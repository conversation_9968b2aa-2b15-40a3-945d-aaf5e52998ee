<!-- 下拉选项控件 -->
<template>
  <div class="flex">
    <ts-select
      clearable
      filterable
      :disabled="disabled"
      :placeholder="placeholder"
      style="width: 100%;"
    >
      <ts-option
        v-for="option in parseOptions(field.optionValue)"
        :key="option"
        :value="option"
        >{{ option }}</ts-option
      >
    </ts-select>
    <span v-if="field.isMust" class="required-indicator">*</span>
  </div>
</template>
<script>
export default {
  props: {
    field: {
      type: Object,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    parseOptions(optionValue) {
      return optionValue.split(',').map(option => option.trim());
    }
  }
};
</script>
