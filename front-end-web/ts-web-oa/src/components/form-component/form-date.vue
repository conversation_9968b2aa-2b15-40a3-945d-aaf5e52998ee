<template>
  <div class="flex">
    <el-date-picker
      class="form-date"
      style="width: 100%;"
      :readonly="readonly"
      v-model="fieldValue"
      :type="pickerType"
      :placeholder="placeholder"
      :required="field.isMust"
      :valueFormat="field.dataFormat"
      :value-format="field.dataFormat"
      @change="updateValue"
    ></el-date-picker>
    <span v-if="field.isMust" class="required-indicator">*</span>
  </div>
</template>
<script>
const TRIGGER_TYPES = {
  'yyyy-MM-dd': 'date',
  'yyyy-MM': 'month',
  'yyyy-MM-dd HH:mm:ss': 'datetime',
  yyyy: 'year'
};
export default {
  props: {
    field: {
      type: Object,
      required: true
    },
    fieldValue: {
      type: String,
      required: true
    }
  },
  computed: {
    pickerType() {
      return TRIGGER_TYPES[this.field.dataFormat];
    }
  },
  methods: {
    updateValue(value) {
      this.$emit('update:fieldValue', value);
      this.fieldValue = value;
    },
    handleInput(field, value) {
      console.log(`Field ${field.id} changed to:`, value);
      this.updateValue(value);
      // 这里可以处理每个字段的变化
    },
    handleBlur(field, value) {
      if (field.interfaceServices) {
        console.log(
          '执行方法回调：' +
            field.interfaceServices +
            `Current value: ${event.target.value}`
        );
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.form-date {
  /deep/ {
    .el-input__inner {
      padding: 0 30px !important;
    }
  }
}
</style>
