<template>
  <div class="flex">
    <ts-input
      :id="field.id"
      :value="fieldValue"
      :name="field.fieldName"
      :maxlength="field.max || 200"
      :placeholder="`请输入${field.showName}`"
      :readonly="field.isReadonly"
      :required="field.isMust"
      @input="handleInput(field, $event)"
      @blur="handleBlur(field, $event)"
    />
    <span v-if="field.isMust" class="required-indicator">*</span>
  </div>
</template>
<script>
import moment from 'moment';
export default {
  props: {
    field: Object,
    fieldValue: String
  },
  computed: {
    userInfo() {
      return this.$getParentStoreInfo('userInfo');
    }
  },
  methods: {
    // 数据来源-常用字段
    getRealValue() {
      switch (this.field.sourceField) {
        case 'nowDate': // 当前日期
          return moment().format('YYYY-MM-DD');
        case 'nowDate': // 转正日期
          return this.userInfo.positiveTime || 0;
        case 'loginName': //登陆人姓名
          return this.userInfo.empName || '';
        case 'loginCode': //登陆人工号
          return this.userInfo.empCode || '';
        case 'loginOrg': // 登陆人所属机构
          return this.userInfo.ssoOrgName || '';
        case 'loginDept': // 登陆人部门
          return (
            this.userInfo.organizationParttimeName || this.userInfo.empDeptName
          );
        case 'loginPhone': // 登陆人电话-设置验证公式
          return this.userInfo.empPhone || '';
        case 'loginEmail': // 登陆人邮箱-设置验证公式
          return this.userInfo.empEmail || '';
        case 'loginIDCard': // 登陆人身份证-设置验证公式
          return this.userInfo.empIdcard || '';
        case 'loginBirth': // 登陆人出生日期
          return this.userInfo.empBirth || '';
        case 'loginAge': // 登陆人年龄-设置验证公式
          return this.userInfo.empAge || '';
        case 'loginAge': // 登陆人性别
          return this.userInfo.empSex == 0 ? '男' : '女';
        case 'loginPost': // 登陆人岗位
          return this.userInfo.postName || '';
        case 'loginPostType': // 登陆人岗位类型
          return this.userInfo.postType || '';
        case 'loginDuty': // 登陆人职务
          return this.userInfo.empDutyName || '';
        case 'loginPosttitle': // 登陆人职称
          return this.userInfo.empPosttitle || '';
        case 'loginEntryDate': // 登陆人入职日期
          return this.userInfo.entryDate || '';
        case 'loginCarno': // 登陆人车牌号
          return this.userInfo.carNo || '';
        case 'loginOrgAttributes': // 登陆人人员类别
          return this.userInfo.orgAttributes || '';
        case 'loginJob': // 登陆人岗位属性
          return this.userInfo.jobAttributes || '';
        case 'loginWorkDate': // 登陆人参加工作时间
          return this.userInfo.workStartDate || '';
        case 'loginHospArea': // 登陆人院区
          return this.userInfo.hospName || '';
        case 'loginTechnical': // 登陆人技术职称
          return this.userInfo.technical || '';
        case 'loginEducation': // 登陆人最高学历
          return this.userInfo.educationTypeName || '';
        case 'loginOperationscope': // 登录人执业范围
          return this.userInfo.operationScope || '';
        case 'loginOperationName': // 登录人执业类别
          return this.userInfo.operationName || '';
        case 'loginAssessmentDate': // 登录人职称评定日期
          return this.userInfo.assessmentDate || '';
        case 'loginInauguralDate': // 登录人职称任职日期
          return this.userInfo.inauguralDate || '';
        case 'annualLeave': // 年假天数-设置验证公式
          return this.userInfo.yearDays || 0;
        case 'annualLeave_h': // 已休年假-设置验证公式
          return this.userInfo.yearNumber || 0;
        default:
          return field.defaultValue;
      }
    },
    updateValue(value) {
      this.$emit('update:fieldValue', value);
      this.fieldValue = value;
    },
    handleInput(field, value) {
      this.updateValue(value);
    },
    handleBlur(field, value) {
      if (field.interfaceServices) {
        console.log(
          '执行方法回调：' +
            field.interfaceServices +
            `Current value: ${event.target.value}`
        );
      }
    }
  }
};
</script>
