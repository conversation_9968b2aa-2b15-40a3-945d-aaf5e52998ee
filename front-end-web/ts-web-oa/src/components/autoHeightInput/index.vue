<template>
  <div
    ref="inputBox"
    :class="{
      'input-box': true,
      'el-input__inner': true,
      'is-disabled': disabled
    }"
    :contenteditable="!disabled"
    :placeholder="placeholder"
    @input="handleInput"
    @paste="handlePaste"
  />
</template>

<script>
export default {
  name: 'autoHeightInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入消息...'
    },
    maxHeight: {
      type: Number,
      default: 240
    },
    minHeight: {
      type: Number,
      default: 32
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 200
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.$nextTick(() => {
          if (this.isRequired) {
            const inputBox = this.$refs.inputBox;
            const formItem = inputBox.closest('.el-form-item');
            formItem.classList[newValue ? 'remove' : 'add']('is-error');
          }
          this.internalValue = newValue;
          this.adjustHeight();
        });
      },
      immediate: true
    }
  },
  data() {
    return {
      internalValue: this.value
    };
  },
  methods: {
    handleEcho() {
      this.$refs.inputBox.textContent = this.internalValue;
    },

    handleInput(e) {
      const content = e.target.textContent;
      if (content.length > this.maxLength) {
        e.target.textContent = content.slice(0, this.maxLength);
        const range = document.createRange();
        const sel = window.getSelection();
        range.selectNodeContents(e.target);
        range.collapse(false);
        sel.removeAllRanges();
        sel.addRange(range);
      }

      this.internalValue = content;
      this.$emit('input', this.internalValue);
    },

    handlePaste(e) {
      e.preventDefault();
      const text = (e.clipboardData || window.clipboardData).getData('text');

      const currentLength = e.target.textContent.length;
      const remainingLength = this.maxLength - currentLength;
      if (remainingLength <= 0) {
        return;
      }

      const truncatedText = text.slice(0, remainingLength);
      // 输入完内容后 删除为空 再复制 获取e.target.textContent为空
      document.execCommand('insertText', false, truncatedText || text);

      this.internalValue = e.target.textContent;
      this.$emit('input', this.internalValue);
    },

    adjustHeight() {
      const inputBox = this.$refs.inputBox;
      // 先重置高度为0，避免scrollHeight受当前高度影响
      inputBox.style.height = '0px';
      // 获取内容实际高度
      const contentHeight = inputBox.scrollHeight;
      // 如果内容为空，使用最小高度
      const height =
        contentHeight === 0
          ? this.minHeight
          : Math.min(contentHeight, this.maxHeight);
      inputBox.style.height = `${height}px`;
    }
  }
};
</script>
<style lang="scss" scoped>
.input-box {
  flex: 1;
  min-height: v-bind(minHeight + 'px');
  max-height: v-bind(maxHeight + 'px');
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  padding-left: 4px;
  padding-top: 4px;

  outline: none;
  overflow-y: auto;
  word-break: break-all;
  resize: none;
  line-height: 20px;
  text-align: left;

  &:active,
  &:focus {
    border-color: $primary-blue !important;
  }

  &.el-input__inner {
    line-height: 18px !important;
  }

  &.is-disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
  }
}

.input-box[placeholder]:empty:before {
  content: attr(placeholder);
  color: #c0c4cc;
}
</style>
