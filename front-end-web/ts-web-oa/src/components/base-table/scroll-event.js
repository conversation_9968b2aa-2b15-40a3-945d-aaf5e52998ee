export default {
  data() {
    return {
      resizeTimer: null,
      left: 0,
      top: 0
    };
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scroll.$refs.wrap;
    },
    hasFixedColumn() {
      return this.columns && this.columns.filter(item => item.fixed).length;
    }
  },
  mounted() {
    window.addEventListener('resize', this.computedTableWidth);
    this.scrollWrapper.addEventListener('scroll', this.handleScroll);
    this.handleMounted();
  },
  beforeDestroy() {
    this.scrollWrapper.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.computedTableWidth);
  },
  methods: {
    async handleMounted() {
      await this.$nextTick();
      this.computedTableWidth();

      let container = this.$refs.tableContainer;
      if (!container) {
        setTimeout(() => {
          this.handleMounted();
        }, 50);
        return;
      }
      let tableHead = container.querySelector('.el-table__header-wrapper'),
        tableBody = container.querySelector('.el-table__body-wrapper');

      tableBody.style.marginTop = tableHead.offsetHeight + 'px';
      setTimeout(() => {
        if (this.hasFixedColumn) {
          let fixedTable = container.querySelector('.el-table__fixed-right'),
            fixedWidth = Number(fixedTable.style.width.replace('px', ''));
          !isNaN(fixedWidth) && (fixedTable.style.width = fixedWidth + 'px');
        }
        this.computedTableScrollingPlace();
      }, 1000);
    },
    computedTableWidth() {
      this.resizeTimer && clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(async () => {
        let container = this.$refs.tableContainer,
          headDom = container.querySelector('.el-table__header'),
          viewDom = container.querySelector('.el-scrollbar__view');

        if (headDom.offsetWidth > container.offsetWidth) {
          viewDom.style.width = headDom.offsetWidth + 2 + 'px';
        } else {
          viewDom.style.width = '100%';
        }
        this.computedTableScrollingPlace();
        await this.$nextTick();
        this.$refs.scroll.update();
      }, 200);
    },
    handleScroll(e) {
      requestAnimationFrame(() => {
        const horizonScroll = this.left != e.target.scrollLeft,
          verticalScroll = this.top != e.target.scrollTop;
        this.top = e.target.scrollTop;
        this.left = e.target.scrollLeft;

        let container = this.$refs.tableContainer,
          headDom = container.querySelector('.el-table__header-wrapper');

        headDom.style.left = -e.target.scrollLeft + 'px';
        if (
          e.target.scrollWidth > container.offsetWidth &&
          this.hasFixedColumn &&
          horizonScroll
        ) {
          this.computedTableScrollingPlace();
        }
        if (verticalScroll) {
          if (
            e.target.scrollHeight >= container.offsetHeight &&
            this.hasFixedColumn
          ) {
            let fixedBody = container.querySelectorAll(
              '.el-table__fixed-body-wrapper'
            );

            [...fixedBody].forEach(item => {
              item.style.marginTop = -e.target.scrollTop + 'px';
            });
          }
        }
      });
    },
    computedTableScrollingPlace() {
      let container = this.$refs.tableContainer,
        tableBody = container.querySelector('.el-table__body-wrapper');

      const scrollLeft = this.$refs.scroll.$refs.wrap.scrollLeft,
        scrollWidth = this.$refs.scroll.$refs.wrap.scrollWidth;

      if (!this.hasFixedColumn || container.offsetWidth >= scrollWidth) {
        tableBody.className = 'el-table__body-wrapper is-scrolling-none';
      } else {
        if (scrollLeft == 0) {
          tableBody.className = 'el-table__body-wrapper is-scrolling-left';
        } else if (scrollWidth - container.offsetWidth > scrollLeft) {
          tableBody.className = 'el-table__body-wrapper is-scrolling-middle';
        } else {
          tableBody.className = 'el-table__body-wrapper is-scrolling-right';
        }
      }
    }
  }
};
