<template>
  <div :id="containerId" class="qiankun-child">
    <keep-alive>
      <router-view v-if="routerBool" />
    </keep-alive>
  </div>
</template>
<script>
export default {
  data() {
    return {
      containerId: process.env.VUE_APP_CONTAINER,
      cacheRoute: {}, //缓存路由对象,
      routerBool: true //刷新页面
    };
  },
  async created() {
    // ts-wen-oa 车辆审批
    this.$root.$on('messageToastEvent', ({ newVal: data }) => {
      if (
        data.path.includes('/ts-web-oa/vehicle/vehicle-approval') &&
        data.type === 1
      ) {
        let interval;
        const checkVariable = () => {
          if (this && this.$children.length) {
            let params = {
              id: data.businessId,
              taskId: data.data.taskId
            };
            this.$children[0].handleShowApproval(params);
            clearInterval(interval);
          }
        };
        interval = setInterval(checkVariable, 1000);
      }
    });
  },
  methods: {
    /**@desc 接收父应用发过来的页面缓存事件**/
    curdRouter(_obj) {
      let obj = _obj.detail;
      if (obj.type == 'deleteRouter') {
        this.deleteRouter(obj);
      } else if (obj.type == 'updateRouter') {
        this.updateRouter(obj);
      }
    },
    /**@desc 删除单个页面缓存**/
    deleteRouter(obj) {
      let key = `/${obj.alink.split(process.env.VUE_APP_BASE_URL)[1]}`;
      if (this.cacheRoute[key]) {
        this.cacheRoute[key].instances.default.$destroy();
        delete this.cacheRoute[key];
      }
    },
    /**@desc 刷新单个页面**/
    // updateRouter(obj) {
    //   let key = `/${obj.alink.split(process.env.VUE_APP_BASE_URL)[1]}`;
    //   if (this.cacheRoute[key]) {
    //     this.cacheRoute[key].instances.default.$destroy();
    //     delete this.cacheRoute[key];
    //     if (key == this.$route.fullPath) {
    //       this.routerBool = false;
    //       this.$nextTick(() => {
    //         this.routerBool = true;
    //       });
    //     }
    //   }
    // },
    updateRouter(obj) {
      let key = `/${obj.alink.split(process.env.VUE_APP_BASE_URL)[1]}`;
      if (this.cacheRoute[key]) {
        // start 处理右键重新加载无效 -- by 002746 Tang
        let refresh = this.cacheRoute[key].instances.default.refresh;
        refresh && typeof refresh == 'function' && refresh();
        // end 处理完毕
        // this.cacheRoute[key].instances.default.$destroy();
        // delete this.cacheRoute[key];
        // if (key == this.$route.fullPath) {
        //   this.routerBool = false;
        //   this.$nextTick(() => {
        //     this.routerBool = true;
        //   });
        // }
      }
    },
    /**@desc 获取缓存路由**/
    setRouterCache(to, form) {
      if (form.matched.length != 0) {
        let instancesObj = form.matched[0];
        this.cacheRoute[form.path] = instancesObj;
      }
      if (to.matched.length != 0) {
        let instancesObj = to.matched[0];
        this.cacheRoute[to.path] = instancesObj;
      }
    },
    /**@desc 新项目接收websocket消息**/
    webSocketMessage(msg) {
      console.log(msg, `新项目接收websocket`);
    }
  },

  mounted() {
    /**@desc 接收页签自定义事件**/
    window.addEventListener('updateDataQianKun', this.curdRouter, false);
    window.addEventListener('webSocketMessage', this.webSocketMessage, false);
    this.$onGlobalStateChange((state = {}, prev = {}) => {
      let { event, data: newVal } = state,
        oldVal = prev.data || {};
      // this.$event.$emit('mainMessage', { event, newVal, oldVal });
      this.$event.create('mainMessage').trigger(event, newVal, oldVal);
    });
  },
  watch: {
    $route(to, form) {
      this.setRouterCache(to, form);
    }
  },
  beforeDestroy() {
    window.removeEventListener('updateDataQianKun', this.curdRouter);
    window.removeEventListener('webSocketMessage', this.webSocketMessage);
  },
  name: 'App'
};
</script>
<style scoped lang="scss">
.qiankun-child {
  margin-top: 8px;
  width: calc(100% - 8px);
  height: calc(100% - 16px);
  background: #efeff4;
  border-radius: 4px;
  overflow: hidden;
  &.isLargeScreen {
    width: 100%;
    height: 100%;
    margin-top: 0px;
    border-radius: 0px;
  }
}
/deep/ {
  .trasen-container {
    padding: 8px !important;
    height: 100%;
    background-color: #fff;
  }
}

.qiankun-child-container {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
@import './assets/css/element-variables.scss';
@import './assets/css/common.scss';
</style>

<style lang="scss">
.action-item.el-dropdown-menu__item:not(.is-disabled):hover,
.action-item.el-dropdown-menu__item:focus {
  background: none;
  color: inherit;
}
.action-item.el-dropdown-menu__item {
  padding-right: 20px;
  height: 27px;
  line-height: 27px;
  color: #333;
  white-space: nowrap;
  .fa-trash-o {
    font-size: 17px;
  }
  div {
    line-height: 27px;
  }
  &:hover {
    background-color: rgba(82, 96, 255, 0.08) !important;
  }
}
</style>
