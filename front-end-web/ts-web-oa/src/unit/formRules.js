export const ipreg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
export const phonereg = /^[1][0-9][0-9]{9}$/;
export default {
  ip: [
    {
      required: true,
      validator(rule, value, callback) {
        if (value.length == 0) {
          callback(new Error('IP不能为空'));
        } else if (!ipreg.test(value)) {
          callback(new Error('IP不合法'));
        }
        callback();
      },
      trigger: ['blur']
    }
  ],
  phone: [
    {
      required: true,
      validator(rule, value, callback) {
        if (value.length == 0) {
          callback(new Error('手机号不能为空'));
        } else if (value.length != 11) {
          callback(new Error('手机号必须为11位'));
        } else if (!phonereg.test(value)) {
          callback(new Error('请输入正确的手机号'));
        }
        callback();
      },
      trigger: ['blur']
    }
  ]
};
