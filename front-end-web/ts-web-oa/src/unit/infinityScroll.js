import Vue from 'vue';
const _this = new Vue();
const h = _this.$createElement;

export default {
  directives: {
    'infinity-scroll': {
      bind: async function(el, binding) {
        await _this.$nextTick();
        let { value = {} } = binding,
          {
            limitHeight = 20,
            loadMethod,
            selector,
            finishedText = '到底啦~',
            hasFinished = true,
            hasLoading = true
          } = value,
          finished = false,
          loading = true,
          dom = el;

        let loadingDom = document.createElement('div');
        loadingDom.style.textAlign = 'center';
        loadingDom.style.color = '#999';
        loadingDom.innerHTML =
          '加载中<i class="el-icon-loading" style="margin-left: 8px;"></i>';

        let finishedDom = document.createElement('div');
        finishedDom.style.color = '#999';
        finishedDom.style.textAlign = 'center';
        finishedDom.innerHTML = finishedText;

        if (selector) {
          dom = el.querySelector(selector);
        }
        let loadFunction = function() {
          loading = true;
          hasLoading ? dom.append(loadingDom) : null;
          loadMethod(res => {
            finished = res ? true : false;
            loading = false;
            loadingDom.remove();
            if (finished) {
              hasFinished ? dom.append(finishedDom) : null;
            }
          });
        };

        dom.resetInfinityScrolling = function() {
          finished = false;
          loading = false;
          finishedDom.remove();
          loadingDom.remove();
          loadFunction();
        };
        dom.infinityScrollFunction = function() {
          if (
            this.scrollHeight - this.scrollTop - this.offsetHeight <=
              limitHeight &&
            !finished &&
            !loading
          ) {
            loadFunction();
          }
        };

        dom.addEventListener('scroll', dom.infinityScrollFunction);
        loadFunction();
      },
      unbind: function(el, binding) {
        let { value = {} } = binding,
          { selector } = value,
          dom = el;

        if (selector) {
          dom = el.querySelector(selector);
        }

        dom.removeEventListener('scroll', dom.infinityScrollFunction);
        delete dom.infinityScrollFunction;
        delete dom.resetInfinityScrolling;
      }
    }
  }
};
