import { $api, $get, $postJson } from '@/api/ajax';

const getRectificationList = function(data) {
  return $api({
    url: `/ts-oa/api/checkRectification/list`,
    method: 'post',
    data
  });
};

const completeRectification = function(data) {
  return $postJson(`/ts-oa/api/checkRectification/finishRectification`, data);
};

const checkRectification = function(data) {
  return $postJson(`/ts-oa/api/checkRectification/checkRectification`, data);
};

const getCheckRectificationDetail = function(id) {
  return $get(
    `/ts-oa/api/checkRectification/findCheckRectificationDetailById/${id}`
  );
};

export {
  getRectificationList,
  completeRectification,
  checkRectification,
  getCheckRectificationDetail
};

export default {
  getRectificationList,
  completeRectification,
  checkRectification,
  getCheckRectificationDetail
};
