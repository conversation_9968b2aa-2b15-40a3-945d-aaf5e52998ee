import { $api } from '@/api/ajax';
import config from '../../config';

export default {
  // 列表
  CivilAffairsPersonnelInfoselectCountPageListByCondition(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsPersonnelInfo/selectCountPageListByCondition`,
      method: 'get',
      params
    });
  },

  // 分页列表
  CivilAffairsPersonnelInfoSelectCountPageListByConditionPage(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsPersonnelInfo/selectCountPageListByConditionPage`,
      method: 'get',
      params
    });
  }
};
