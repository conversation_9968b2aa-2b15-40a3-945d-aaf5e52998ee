import { $api } from '@/api/ajax';
import config from '../../config';

export default {
  // 新增
  CivilAffairsChangeManageSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },
  // 编辑
  CivilAffairsChangeManageUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 票据校验
  CivilAffairsChangeRegisterCheckBillNumber(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/checkBillNumber`,
      method: 'get',
      params
    });
  },

  // 校验对象是否已经存在
  CivilAffairsChangeManageCheckName(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/checkName`,
      method: 'get',
      params
    });
  },

  // 获取对象零钱详情
  CivilAffairsChangeManageDetails(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/${id}`,
      method: 'get'
    });
  },

  // 列表
  CivilAffairsChangeManageRecordsList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/pageList`,
      method: 'get',
      params
    });
  },

  // 删除
  CivilAffairsChangeManageDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/delete/${id}`,
      method: 'post'
    });
  },

  // 零钱列表
  CivilAffairsChangeRegisterPageList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/pageList`,
      method: 'get',
      params
    });
  },

  // 零钱新增
  CivilAffairsChangeRegisterSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 零钱编辑
  CivilAffairsChangeRegisterUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 零钱详情接口
  CivilAffairsChangeRegisterDetails(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/${id}`,
      method: 'get'
    });
  },

  // 零钱删除
  CivilAffairsChangeRegisterDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/delete/${id}`,
      method: 'post'
    });
  },

  // 零钱作废
  CivilAffairsChangeRegisterUpdateStatus(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/updateStatus`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 台账新增
  CivilAffairsChangeLedgerSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 台账编辑
  CivilAffairsChangeLedgerUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 台账批量审批
  batchUpdateReviewStatus(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/batchUpdateReviewStatus`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 台账批量发送消息
  familyMessagePush(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/familyMessagePush`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 台账删除
  CivilAffairsChangeLedgerDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/delete/${id}`,
      method: 'post'
    });
  },

  // 台账列表
  CivilAffairsChangeLedgerPageList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/pageList`,
      method: 'get',
      params
    });
  },

  // 支出新增
  CivilAffairsChangeExpenditureSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 支出编辑
  CivilAffairsChangeExpenditureUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 支出作废
  CivilAffairsChangeExpenditureUpdateStatus(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/updateStatus`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 支出删除
  CivilAffairsChangeExpenditureDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/delete/${id}`,
      method: 'post'
    });
  },

  // 支出列表
  CivilAffairsChangeExpenditurePageList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/pageList`,
      method: 'get',
      params
    });
  },

  // 退还新增
  CivilAffairsChangeReturnSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeReturn/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 退还作废
  CivilAffairsChangeReturnUpdateStatus(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeReturn/updateStatus`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 退还编辑
  CivilAffairsChangeReturnUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeReturn/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },

  // 退还删除
  CivilAffairsChangeReturnDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeReturn/delete/${id}`,
      method: 'post'
    });
  },

  // 退还列表
  CivilAffairsChangeReturnPageList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeReturn/pageList`,
      method: 'get',
      params
    });
  },

  // 查询已经登记使用的票据(用于新增台账)
  selectAllByBillNumber(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeRegister/selectAllByBillNumber`,
      method: 'get',
      params
    });
  },

  // 查询已经产生的流水号(用于新增台账)
  selectSerialNumber(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeExpenditure/selectSerialNumber`,
      method: 'get',
      params
    });
  },

  // 查询当年最大流水号
  selectSerialNumberMax() {
    return $api({
      url: `${config.oa}/api/CivilArchivalInformation/selectSerialNumberMax`,
      method: 'get'
    });
  },

  // 查询零钱余额
  CivilAffairsChangeLedgerSelectAmount(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeLedger/selectAmount`,
      method: 'get',
      params
    });
  },

  // 导出支出账单数据查询
  selectExportChangeExpenditureData(params) {
    return $api({
      url: `${config.oa}//api/CivilAffairsChangeExpenditure/selectExportChangeExpenditureData`,
      method: 'get',
      params
    });
  },

  // 排序更新
  CivilAffairsChangeManageUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsChangeManage/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  }
};
