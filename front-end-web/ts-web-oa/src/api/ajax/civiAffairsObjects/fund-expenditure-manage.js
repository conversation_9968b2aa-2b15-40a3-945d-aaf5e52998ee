import { $api } from '@/api/ajax';
import config from '../../config';

export default {
  // 新增
  CivilAffairsFundingExpenditureSave(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsFundingExpenditure/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },
  // 编辑
  CivilAffairsFundingExpenditureUpdate(data) {
    return $api({
      url: `${config.oa}/api/CivilAffairsFundingExpenditure/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      },
      data
    });
  },
  // 列表
  CivilAffairsFundingExpenditureList(params) {
    return $api({
      url: `${config.oa}/api/CivilAffairsFundingExpenditure/PageList`,
      method: 'get',
      params
    });
  },
  // 删除
  CivilAffairsFundingExpenditureDelete(id) {
    return $api({
      url: `${config.oa}/api/CivilAffairsFundingExpenditure/delete/${id}`,
      method: 'post'
    });
  }
};
