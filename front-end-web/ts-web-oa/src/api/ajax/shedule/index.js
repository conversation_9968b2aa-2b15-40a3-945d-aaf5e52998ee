import { $api, $get, $postJson } from '@/api/ajax';

const getRegPool = function(data) {
  return $api({
    url: `/ts-external/hisApi/queryRegPool`,
    method: 'post',
    data
  });
};

const getHolidayList = function(datas) {
  return $postJson(`/ts-oa/schedule/getHolidayList`, datas);
};

const getScheduleList = function(datas) {
  return $postJson(`/ts-oa/schedule/getScheduleListByDate`, datas);
};

const deleteSchedule = function(data) {
  return $api({
    url: `/ts-oa/schedule/delete`,
    method: 'post',
    data
  });
};

const getScheduleDetails = function(datas) {
  return $postJson(`/ts-oa/schedule/selectScheduleDetails`, datas);
};

const getvalidateScheduleTime = function(params) {
  return $get(`/ts-oa/schedule/validateScheduleTime`, params);
};

const updateSchedule = function(datas) {
  return $postJson(`/ts-oa/schedule/insertOrUpdate`, datas);
};

const getDoctorShedule = function(params) {
  return $get(`/ts-oa/schedule/getDoctorShedule`, params);
};

export {
  getRegPool,
  getHolidayList,
  getScheduleList,
  deleteSchedule,
  getScheduleDetails,
  getvalidateScheduleTime,
  updateSchedule,
  getDoctorShedule
};

export default {
  getRegPool,
  getHolidayList,
  getScheduleList,
  deleteSchedule,
  getScheduleDetails,
  getvalidateScheduleTime,
  updateSchedule,
  getDoctorShedule
};
