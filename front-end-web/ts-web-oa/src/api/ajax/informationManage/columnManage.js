import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  informationChannelList(params) {
    return $api({
      url: `${service.tsInformation()}/informationChannel/list`,
      method: 'get',
      params
    });
  },

  informationChannelSave(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannel/save`,
      method: 'post',
      data
    });
  },

  informationChannelUpdate(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannel/update`,
      method: 'post',
      data
    });
  },

  informationChannelDeletedById(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannel/deletedById`,
      method: 'post',
      data
    });
  },

  informationChannelSubclassList(params) {
    return $api({
      url: `${service.tsInformation()}/informationChannelSubclass/list`,
      method: 'get',
      params
    });
  },

  informationChannelSubclassDeletedById(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannelSubclass/deletedById`,
      method: 'post',
      data
    });
  },

  informationChannelSubclassSave(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannelSubclass/save`,
      method: 'post',
      data
    });
  },

  informationChannelSubclassUpdate(data) {
    return $api({
      url: `${service.tsInformation()}/informationChannelSubclass/update`,
      method: 'post',
      data
    });
  }
};
