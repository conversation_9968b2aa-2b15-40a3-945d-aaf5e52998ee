import { $api, $get } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  /**@desc 获取我的审批表格数据 */
  getVehicleApprovalTableData(params) {
    return $get(`${service.tsOa()}/api/vehicleApply/getApprovalList`, params);
  },
  /**@desc 审批通过 */
  handleVehicleApprovalPass(data) {
    return $api({
      url: `${service.tsOa()}/api/vehicleApply/pass`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },
  /**@desc 审批不通过 */
  handleVehicleApprovalReject(data) {
    return $api({
      url: `${service.tsOa()}/api/vehicleApply/reject`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  }
};
