import { $api } from '@/api/ajax';
import { requestInterface } from '../config.js';

export default {
  /**@desc 获取 当前登录人医院信息 */
  getLoginUserHospitalMessage() {
    return $api({
      url: `${requestInterface.basics()}/organization/getEmpBelongingHospital`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  /**@desc 获取事发医院数据 */
  getHospitalList() {
    return $api({
      url: `${requestInterface.basics()}/organization/getAllHospital`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
