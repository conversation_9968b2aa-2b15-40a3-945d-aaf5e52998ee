import './public-path';

import Vue from 'vue';
import App from './App.vue';

// import '@/iconfont/font/iconfont.css';
// import '@/iconfont/awesome/font-awesome.min.css';
// import '@/iconfont/oa-pc/iconfont.css';

import component from '@/components/index.js';
Vue.use(component); //初始化要使用的各种组件

import { getRouter } from './router';

import store from './store';
Vue.prototype._$store = store;

import api from './api/index.js';
Vue.use(api);

import dayjs from 'dayjs';
Vue.prototype.$dayjs = dayjs;

import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;

import '@/unit/directive.js';

import unit from './unit/index';
Vue.use(unit); //初始化要使用的各种工具

import tsFormItem from '@/extends/ts-form-item/index.js';
Vue.use(tsFormItem);

Vue.config.productionTip = false;

//路由切换刷新
Vue.mixin({
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.refresh && typeof vm.refresh == 'function' && vm.refresh();
    });
  }
});

let instance = null;
function render(props = {}) {
  const router = getRouter(props);
  instance = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount(document.getElementById(process.env.VUE_APP_CONTAINER));
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}
export async function bootstrap(props) {}
export async function mount(props) {
  Vue.prototype.qiankunParentNode = document.getElementById(
    process.env.VUE_APP_CONTAINER
  ).parentNode;
  // 设置主应用下发的方法
  Object.keys(props.fn).forEach(method => {
    Vue.prototype[`$${method}`] = props.fn[method];
  });
  //监听主应用下发用户信息
  props.onGlobalStateChange((state, prevState) => {});
  // 设置通讯
  store.state.common.userInfo = props.fn.getUserInfo(); //主动获取用户信息
  store.commit('common/setData', { label: 'token', value: props.data.token });
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  Vue.prototype.$setGlobalState = props.setGlobalState;
  render(props);
}
export async function update(props) {
  instance.$emit('updateDataQianKun', props);
}

export async function unmount() {
  instance.$destroy();
}
