<template>
  <el-scrollbar
    style="height: 100%;"
    wrap-style="hight:calc(100% + 17px); overflow-x:hidden;"
  >
    <ts-steps direction="vertical" type="logs" style="margin-left: 50px;">
      <ts-step
        v-for="(item, index) of logsData"
        :key="index"
        :title="`【${item.operationContent}】`"
      >
        <template slot="description">
          <div v-if="item.approvalRemark">
            操作说明：{{ item.approvalRemark }}
          </div>
          <div>操作人：{{ item.updateUserName }}</div>
          <div>操作科室：{{ item.operationOrgName }}</div>
          <div>操作时间：{{ item.createDate }}</div>
          <div v-if="item.dispenseDept">处理科室：{{ item.dispenseDept }}</div>
          <div v-if="item.dispenseUser">处理人：{{ item.dispenseUser }}</div>
          <div v-if="item.completeDate">
            要求完成时间：{{ item.completeDate }}
          </div>
          <div v-if="item.handleDate">处理时间：{{ item.handleDate }}</div>
          <div v-if="item.handleType">
            处理方式：{{
              (resolventList.find(res => res.value == item.handleType) || {})
                .label
            }}
          </div>
          <div v-if="item.handleIndemnity">
            赔偿金额：{{ item.handleIndemnity }}
          </div>
          <div v-if="item.finishType">
            办结方式：{{
              (finishedWayList.find(res => res.value == item.finishType) || {})
                .label
            }}
          </div>
          <!-- <template v-if="item.operationType > 2">
            <div v-if="item.operationType == 4 || item.operationType == 6">
              操作方式：{{ item.operationType }}
            </div>
          </template> -->

          <div v-if="item.fileList && item.fileList.length">
            附件：
            <ts-upload-file-list
              :fileList.sync="item.fileList"
              :on-preview="handlePreview"
              :showRemove="false"
              type="mixture"
            ></ts-upload-file-list>
          </div>
        </template>
      </ts-step>
    </ts-steps>
    <el-image
      ref="imagePreview"
      style="display: none;"
      :src="previewImgUrl"
      :preview-src-list="[previewImgUrl]"
      :z-index="3000"
    ></el-image>
  </el-scrollbar>
</template>

<script>
import { commonUtils } from '@/utils/index.js';
import { resolventList, eventLevelList } from '@/assets/js/constants';
import asyncData from '@/assets/js/getAsyncData.js';

export default {
  mixins: [asyncData],
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      logsData: [],
      resolventList,

      operationType: {
        1: '上报',
        2: '登记',
        3: '分发',
        4: '处理',
        5: '审批',
        6: '办结',
        7: '驳回',
        8: '登记并分发'
      },
      previewImgUrl: ''
    };
  },
  watch: {
    data: {
      handler(val) {
        if (val.length) {
          this.handleGetFilesDetail();
        } else {
          this.logsData = [];
        }
        this.getFinishedWayList();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async handleGetFilesDetail() {
      let requestList = this.data.map(item => {
        if (item.businessId) {
          return this.ajax
            .getFileAttachmentByBusinessId(item.businessId)
            .then(res => {
              return res.object.map(item => ({
                ...item,
                fileName: `${item.id}.${item.fileExtension}`,
                fileRealName: item.originalName,
                fileId: item.id,
                uid: item.id,
                url: item.realPath,
                name: item.originalName,
                status: 'success'
              }));
            });
        }
        return Promise.resolve([]);
      });

      let resList = await Promise.all(requestList);
      this.logsData = resList
        .map((res, index) => ({
          fileList: res,
          ...this.data[index],
          index: index + 1
        }))
        .reverse();
    },
    handlePreview(file) {
      if (commonUtils.isDoc(file.name)) {
        commonUtils.viewerDocBase(file.url, file.name);
      } else if (commonUtils.isImg(file.name)) {
        this.previewImgUrl = file.url;
        this.$nextTick(() => {
          this.$refs.imagePreview.clickHandler();
        });
      }
    },
    handleDownLoad(fileList) {
      fileList.map(item => {
        let aDom = document.createElement('a');
        aDom.href = item.url;
        aDom.download = item.fileName;
        setTimeout(() => {
          aDom.click();
        }, 500);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .ts-step-title {
  line-height: unset !important;
  top: -1px !important;
}
</style>
