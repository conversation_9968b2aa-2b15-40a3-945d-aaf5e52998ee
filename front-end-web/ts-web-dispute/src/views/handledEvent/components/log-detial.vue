<template>
  <el-scrollbar
    style="height: 100%;"
    wrap-style="hight:calc(100% + 17px); overflow-x:hidden;"
  >
    <ts-steps direction="vertical" type="logs" style="margin-left: 50px;">
      <ts-step title="步骤1" icon="el-icon-s-platform">
        <span slot="title">插入Title</span>
        <template slot="description">
          一些有的没的东西
        </template>
      </ts-step>
      <ts-step title="步骤2" description="描述一下2" icon="el-icon-s-order">
        <span slot="description">插入内容</span>
      </ts-step>
      <ts-step
        title="步骤"
        description="描述一下3"
        icon="el-icon-upload"
      ></ts-step>
    </ts-steps>
  </el-scrollbar>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped></style>
