<template>
  <div class="trasen-container flex">
    <div class="left-content">
      <div
        v-for="(item, index) of templateTypeList"
        :key="index"
        class="template-tab-item"
        :class="{
          'is-active': activeType == item.value
        }"
        @click="handleChangeTemplateType(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="flex-column flex-grow" style="overflow: hidden;">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :actions="searchActions"
        @search="refresh"
      >
      </ts-search-bar>

      <base-table
        class="flex-grow"
        border
        stripe
        v-loading="loading"
        :columns="columns"
        :data="tableData"
      >
        <template #operate="{row}">
          {{ row }}
        </template>
      </base-table>
      <div class="pagination-content">
        <ts-pagination
          :current-page.sync="pageData.pageNo"
          :page-sizes="[20, 100, 200, 300, 400, 1000]"
          :page-size.sync="pageData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.totalCount"
          @size-change="refresh"
          @current-change="refresh"
        ></ts-pagination>
      </div>
    </div>
    <ts-dialog
      :title="Object.keys(detailData).length ? '编辑' : '新增'"
      :visible.sync="showEditModel"
    >
      <ts-form ref="form" :model="detailData">
        <ts-form-item
          label="模板类型"
          prop="templateType"
          :rules="rules.requiredRow"
        >
          <ts-select v-model="detailData.templateType" clearable>
            <ts-option
              v-for="(item, index) of templateTypeList"
              :key="index"
              v-bind="item"
            ></ts-option>
          </ts-select>
        </ts-form-item>

        <ts-form-item
          label="模板名称"
          prop="templateDetail"
          :rules="rules.requiredRow"
        >
          <ts-input
            v-model="detailData.templateDetail"
            type="textarea"
            resize="none"
            rows="4"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="状态" prop="status" :rules="rules.requiredRow">
          <ts-radio-group v-model="detailData.status">
            <ts-radio :label="3">启用</ts-radio>
            <ts-radio :label="6">禁用</ts-radio>
          </ts-radio-group>
        </ts-form-item>
      </ts-form>
      <template slot="footer">
        <ts-button type="primary" @click="handleSubmitEdit">确定</ts-button>
        <ts-button @click="handleCloseEditModel">取消</ts-button>
      </template>
    </ts-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabData: {},
      activeType: 'all',
      templateTypeList: [
        {
          label: '全部模板',
          value: 'all'
        },
        {
          label: '驳回模板',
          value: 'reject'
        },
        {
          label: '登记模板',
          value: 'register'
        },
        {
          label: '分发模板',
          value: 'distribute'
        },
        {
          label: '处理模板',
          value: 'deal'
        },
        {
          label: '办结模板',
          value: 'finished'
        },
        {
          label: '审批模板',
          value: 'approval'
        }
      ],

      searchForm: {},
      searchList: [
        {
          element: 'ts-input',
          value: 'name',
          elementProp: {
            placeholder: '请输入',
            clearable: true
          }
        }
      ],

      loading: false,
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 80
        },
        {
          label: '事件类型',
          prop: ''
        },
        {
          label: '状态',
          prop: ''
        },
        {
          label: '超时时间（小时）',
          prop: ''
        },
        {
          label: '操作人',
          prop: ''
        },
        {
          label: '操作时间',
          prop: ''
        },
        {
          label: '操作',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleOpenEdit
              },
              {
                label: '启用',
                event: this.handleChangeStatus
              },
              {
                label: '停用',
                event: this.handleChangeStatus
              },
              {
                label: '删除',
                event: this.handleDelete
              }
            ];
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ],
      tableData: [],
      searchActions: [
        {
          label: '新增',
          prop: {
            type: 'primary'
          },
          click: this.handleOpenEdit
        }
      ],

      pageData: { pageNo: 1, pageSize: 20, totalCount: 0 },

      showEditModel: false,
      detailData: {},
      rules: {
        requiredRow: [
          { required: true, message: '必填', trigger: ['blur', 'change'] }
        ]
      }
    };
  },
  watch: {
    activeType: {
      handler(val) {
        if (!this.tabData[val]) {
          this.tabData[val] = {
            data: [{ index: 1 }],
            searchForm: {},
            pageData: {
              pageNo: 1,
              pageSize: 20,
              totalCount: 0
            }
          };
        }
        let { data, searchForm, pageData } = this.tabData[val];
        this.tableData = data;
        this.searchForm = searchForm;
        this.pageData = pageData;
        this.refresh();
      },
      immediate: true
    }
  },
  methods: {
    refresh() {},
    handleExport() {},
    handleChangeTemplateType(type) {
      if (this.activeType == type) {
        return;
      }
      this.activeType = type;
    },
    handleOpenEdit(row = {}) {
      this.detailData = row;
      this.showEditModel = true;
    },
    handleSubmitEdit() {},
    handleCloseEditModel() {
      this.detailData = {};
      this.showEditModel = false;
    },
    handleDelete() {},
    handleChangeStatus() {}
  }
};
</script>

<style lang="scss" scoped>
.pagination-content {
  text-align: right;
  margin-top: $primary-spacing;
}
.left-content {
  // margin-right: $primary-spacing;
  padding-right: $primary-spacing;
  border-right: 1px solid $theme-border-color;
}
.template-tab-item {
  padding: $primary-spacing $medium-spacing;
  white-space: nowrap;
  cursor: pointer;
  border-radius: $medium-radius;
  &:hover {
    background-color: $list-hover-color;
  }
  &.is-active {
    color: #fff;
    background-color: $primary-blue;
  }
}
</style>
