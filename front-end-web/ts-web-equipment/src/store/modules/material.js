import Vue from 'vue';
import { getDataByDataLibrary } from '@/api/ajax/common';
import { Message } from '@trasen/trasen-element-ui/lib';
import tsElement from '@trasen-oa/trasen-ui-web';

let initDigits = '2';
let initPrintNum = '20';
export default {
  namespaced: true,
  state: () => ({
    // 物资 数字小数位数
    materialDigits: initDigits,
    // 物资 打印机类型
    materialPrintNum: initPrintNum,
    handleTsInputDecimal: value => {
      return tsElement.tsUtils.tsInputDecimal(value, initDigits);
    },
    handleTsNumToLocaleStr: value => {
      return tsElement.tsUtils.tsNumToLocaleStr(value, initDigits);
    }
  }),
  actions: {
    async getMaterialConfig({ commit }) {
      try {
        const DICT_NAME = 'AMS_M_BASE_SETTING';
        const res = await getDataByDataLibrary(DICT_NAME);
        if (!res.success) {
          throw new Error('获取物资基础配置信息失败');
        }

        let setting = res.object || [];
        // 默认2位小数
        const { itemNameValue: materialDigits = initDigits } =
          setting.find(({ itemCode }) => itemCode === 'digits') || {};
        commit('setData', { label: 'materialDigits', value: materialDigits });

        // 默认20条/页 打印机
        const { itemNameValue: materialPrintNum = initPrintNum } =
          setting.find(({ itemCode }) => itemCode === 'printNum') || {};
        commit('setData', {
          label: 'materialPrintNum',
          value: materialPrintNum
        });

        // 设置material input 和 numToLocaleStr 方法的精度
        const { tsNumToLocaleStr, tsInputDecimal } = tsElement.tsUtils;
        commit('setData', {
          label: 'handleTsInputDecimal',
          value: value => tsInputDecimal(value, materialDigits)
        });
        commit('setData', {
          label: 'handleTsNumToLocaleStr',
          value: value => tsNumToLocaleStr(value, materialDigits)
        });
      } catch (error) {
        Message.error(error.message);
      }
    }
  },
  getters: {
    materialDigits: state => state.materialDigits,
    materialPrintNum: state => state.materialPrintNum,
    handleTsInputDecimal: state => state.handleTsInputDecimal,
    handleTsNumToLocaleStr: state => state.handleTsNumToLocaleStr
  },
  mutations: {
    /**@desc 更新数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    setData(state, { label, value }) {
      Vue.set(state, label, value);
    }
  }
};
