import Vue from 'vue';
const pageFirst = 10;

export default {
  namespaced: true,
  state: () => ({
    pageSizeArr: [pageFirst, 20, 30, 40], //选择分页
    pageSize: pageFirst, //分页显示多少条数据
    cacheRoute: [], //缓存页面
    token: '',
    userMessage: '',
    userInfo: {
      token: ''
    }, //用户基本消息
    hospitalCode: '',
    systemCustomCode: {}, //客户定制参数
    personalSortData: {} //人员排序参数
  }),
  actions: {},
  mutations: {
    /**@desc 更新数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    setData(state, { label, value }) {
      Vue.set(state, label, value);
    },
    /**@desc 添加数组数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    addListData(state, { label, value }) {
      state[label].push(value);
    },
    /**@desc 更新数组数据
     * @param {String} label state里面的key值
     * @param {Object} updateData： index 索引 data 数据**/
    updateListData(state, { label, updateData }) {
      Vue.set(state[label], updateData.index, updateData.data);
    }
  }
};
