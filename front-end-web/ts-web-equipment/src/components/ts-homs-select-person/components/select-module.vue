<template>
  <div class="select-module-container">
    <div class="title flex-space">
      <span class="type-title">{{ selectTitle }}({{ data.length || 0 }})</span>
      <a class="remove-all" @click="handleClear">清空</a>
    </div>
    <div class="select-item-content">
      <div class="selected-item" v-for="(item, index) in data" :key="index">
        {{ item[showNameKey] }}
        <img
          class="delet-btn"
          @click="handleDelete(item)"
          src="@/assets/img/homs-icon-del.svg"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    selectTitle: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    },
    showNameKey: {
      type: String,
      default: 'name'
    }
  },
  methods: {
    handleClear() {
      this.$emit('handle-clear');
    },
    handleDelete(item) {
      this.$emit('handle-delete', item);
    }
  }
};
</script>

<style lang="scss" scoped>
.select-module-container {
  width: 100%;
  height: 100%;
  border: 1px solid #e4e4e4;

  .title {
    width: 100%;
    height: 30px;
    padding: 0 8px;
    text-align: center;
    background: #fafafa;
    font-size: 12px;
    color: #333333;

    .type-title {
      &::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 16px;
        background-color: $primary-blue;
        transform: translateY(3px);
        margin-right: 4px;
        border-radius: 2px;
      }
    }

    .remove-all {
      cursor: pointer;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
    }
  }

  .select-item-content {
    height: calc(100% - 30px);
    width: 100%;
    flex-grow: 1;
    padding: 8px 7px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(51, 51, 51, 0.7);
    line-height: 17px;
    overflow-y: auto;

    .selected-item {
      display: inline-block;
      padding: 2px 8px;
      padding-right: 4px;
      margin-right: 12px;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
      border-radius: 8px;
      border: 1px solid #5260ff;

      .delet-btn {
        width: 20px;
        height: 20px;
        color: red;
        cursor: pointer;
        transform: translate(-1px, -1px);
      }
    }
  }
}
</style>
