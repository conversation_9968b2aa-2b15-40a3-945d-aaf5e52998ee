<template>
  <div ref="bodydiv">
    <el-popover
      ref="popoverRef"
      popper-class="input-tree-popover"
      placement="bottom-start"
      :visible-arrow="false"
      :width="poppverWidth"
      trigger="click"
      :disabled="disabled"
      @show="handlePopoverShow"
      @hide="handlePopoverHide"
    >
      <el-tree
        :data="treeData"
        :node-key="nodeKey"
        :props="treeProp"
        ref="elTree"
        class="treeInput"
        empty-text="暂无数据"
        :default-expand-all="defaultExpandAll"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="false"
        highlight-current
        :filter-node-method="filterNode"
        :current-node-key="value"
        @node-click="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <span
            class="flex-col-center"
            :class="{
              'node-disabled':
                lastLevelSelect &&
                !node.isLeaf &&
                (node.childNodes || []).length > 0
            }"
          >
            <i
              :class="
                node.level == 1
                  ? 'first-tree-icon'
                  : !node.isLeaf && (node.childNodes || []).length > 0
                  ? 'has-child-tree-icon'
                  : 'leaf-tree-icon'
              "
            ></i>
            {{ node.label }}
          </span>
        </span>
      </el-tree>
      <el-input
        slot="reference"
        class="search-input"
        ref="searchInput"
        v-model="fullPath"
        :placeholder="isFocus ? inputText : placeholder"
        :disabled="disabled"
        @input="handleInputChange"
      >
        <span slot="suffix" class="action-suffix-content">
          <i
            class="el-input__icon el-icon-arrow-down"
            :class="{
              'has-clearable': clearable && fullPath
            }"
          ></i>
          <i
            class="el-input__icon el-icon-circle-close"
            @click.stop="handleClearInput"
            v-if="!disabled && clearable && fullPath"
          ></i>
        </span>
      </el-input>
    </el-popover>
  </div>
</template>

<script>
/**
 * @param String nodeKey 树节点用来作为唯一标识的属性
 * @param Array treeData 展示数据
 * @param Object treeProp 配置选项 label:指定节点标签为节点对象的某个属性值;children:指定子树为节点对象的某个属性值;
 * @param String textName 指定节点标签为节点对象的某个属性值
 * @param Boolean defaultExpandAll 是否默认展开所有节点
 * @param Array defaultExpandedKeys 默认展开的节点的 key 的数组
 * @param String value 选项选中的节点
 * @param Boolean clearable 是否可清空
 * @param Boolean disabled 是否禁用
 * @param String placeholder 输入框占位文本
 * @param Boolean dbClickQuitSelect 被点击的节点与选中的节点相同时，是否取消选中
 * @param Boolean lastLevelSelect 是否仅最后一层级的节点可选
 */
export default {
  name: 'InputTree',
  model: {
    prop: 'value',
    event: 'valuechange'
  },
  props: {
    spSearch: {
      type: Boolean,
      default: () => false
    },
    nodeKey: {
      type: String,
      default: () => {
        return 'id';
      }
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    treeProp: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          children: 'children'
        };
      }
    },
    textName: {
      type: String,
      default: () => {
        return 'name';
      }
    },
    defaultExpandAll: {
      type: Boolean,
      default: () => false
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    value: {
      default: () => {
        return null;
      }
    },
    placeholder: {
      type: String,
      default: () => {
        return '请输入';
      }
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    dbClickQuitSelect: {
      type: Boolean,
      default: () => false
    },
    lastLevelSelect: {
      type: Boolean,
      default: () => false
    }
  },
  mounted() {
    this.poppverWidth = this.$refs.bodydiv.clientWidth;
    this.poppverWidth > 300 ? null : (this.poppverWidth = 300);
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.findNode(this.treeData);
        } else {
          this.currentId = null;
          this.inputText = '';
          this.fullPath = '';
        }
        this.$refs.elTree?.setCurrentKey(this.value);
      },
      immediate: true
    }
  },
  data() {
    return {
      inputText: '', //当前选中值的文本
      fullPath: '', //input全路径展示
      inputTimer: null, //输入防抖
      currentId: null, //当前点击选中的ID
      poppverWidth: null, //气泡框的宽度
      isFocus: false
    };
  },

  methods: {
    findNode(treeCol) {
      let fullPath = '';
      for (let item of treeCol) {
        if (item[this.nodeKey] == this.value) {
          fullPath = item[this.textName];
          this.currentId = this.value;
          this.inputText = item[this.textName];
          this.fullPath = item[this.textName];

          break;
        }

        if (item[this.treeProp.children]) {
          fullPath = this.findNode(item[this.treeProp.children]);
        }
      }

      return fullPath;
    },

    //处理节点点击事件
    handleNodeClick(val, node, tree) {
      if (this.lastLevelSelect && node.childNodes.length > 0) {
        this.$refs.elTree.setCurrentKey(this.value);
      } else {
        if (this.currentId == val.id) {
          if (this.dbClickQuitSelect) {
            this.$emit('valuechange', null);
            this.$emit('change', null, null, tree);
          }
        } else {
          this.$emit('valuechange', val.id);
          this.$emit('change', val, node, tree);
        }
        this.$refs.popoverRef.doClose();
      }
    },

    //节点过滤
    filterNode(value, data, node) {
      if (!value) {
        node.expanded = false;
        return true;
      }
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      if (this.spSearch) {
        return (
          data.qp?.includes(value.toLowerCase()) ||
          data.sp?.includes(value.toLowerCase()) ||
          labels.some(label => label.indexOf(value) !== -1)
        );
      } else {
        return labels.some(label => label.indexOf(value) !== -1);
      }
    },

    //处理input 改变，搜索下拉框
    handleInputChange() {
      this.inputTimer && clearTimeout(this.inputTimer);
      this.inputTimer = setTimeout(() => {
        if (!this.fullPath) {
          this.fullPath = '';

          if (!this.checkViewIsIncomplete()) {
            this.resetViewTreeData();
          }
        } else {
          this.$refs.elTree.filter(this.fullPath);
        }
      }, 500);
    },

    handlePopoverShow() {
      this.isFocus = true;
      this.fullPath = '';

      if (!this.checkViewIsIncomplete()) {
        this.resetViewTreeData();
      }
    },

    handlePopoverHide() {
      this.isFocus = false;
      this.fullPath = this.inputText;
      this.$refs.searchInput.blur();
    },

    handleClearInput() {
      if (this.isFocus) {
        this.fullPath = '';
        this.$refs.searchInput.focus();

        if (!this.checkViewIsIncomplete()) {
          this.resetViewTreeData();
        }
      } else {
        this.$emit('valuechange', null);
        this.$emit('change', null, null, this.$refs.elTree);
      }
    },

    resetViewTreeData() {
      this.$refs.elTree.filter(this.fullPath);
      this.fn(this.$refs.elTree.store.root.childNodes);
    },

    // 检查试图数据是否完整
    checkViewIsIncomplete() {
      const checkFn = nodeList => {
        return nodeList.every(node => {
          if (!node.visible) return false;
          return node.childNodes.length ? checkFn(node.childNodes) : true;
        });
      };
      return checkFn(this.$refs.elTree.store.root.childNodes);
    },

    fn(nodes) {
      nodes.forEach(node => {
        node.expanded =
          Array.isArray(this.defaultExpandedKeys) &&
          this.defaultExpandedKeys.length &&
          this.defaultExpandedKeys.includes(node.key);

        if (node.childNodes.length > 0) {
          this.fn(node.childNodes);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.input-tree-popover {
  width: 100%;

  .treeInput {
    min-height: 250px;
    max-height: 250px;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &:hover::-webkit-scrollbar-thumb {
      border-radius: 8px;
      height: 50px;
      background: rgba(0, 0, 0, 0.2);
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }

    ::v-deep {
      .el-tree-node {
        &:focus {
          > .el-tree-node__content {
            background-color: rgba(137, 170, 252, 0.3) !important;
          }
        }
      }

      .el-tree-node__expand-icon {
        font-size: 26px;
      }

      .el-tree-node__content:hover,
      .el-upload-list__item:hover {
        background-color: rgba(137, 170, 252, 0.5) !important;
      }

      &.el-tree--highlight-current {
        .el-tree-node {
          &.is-current {
            > .el-tree-node__content {
              background-color: rgba(137, 170, 252, 0.5) !important;
            }
          }
        }
      }
    }
  }

  .first-tree-icon {
    background: url(./../../assets/img/other/ztree_all.png) no-repeat;
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-right: 2px;
  }

  .has-child-tree-icon {
    background: url(./../../assets/img/other/ztree_folder.png) no-repeat;
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-right: 2px;
  }

  .leaf-tree-icon {
    background: url(./../../assets/img/other/ztree_file.png) no-repeat;
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-right: 2px;
  }

  .flex-col-center.node-disabled {
    cursor: no-drop;
  }

  .search-input {
    &.can-not-search.is-focus .el-icon-arrow-down,
    &.is-focus .el-icon-arrow-down {
      transform: rotate(180deg);
    }

    .action-suffix-content .el-icon-circle-close {
      display: none;
      cursor: pointer;
    }

    &:hover {
      .action-suffix-content .el-icon-circle-close {
        display: inline-block;
      }

      .el-icon-arrow-down.has-clearable {
        display: none;
      }
    }

    /deep/ &.can-not-search {
      &.is-focus .el-input__inner {
        border-color: $primary-blue !important;
      }

      .el-input__inner {
        cursor: pointer !important;
        background-color: #fff !important;
      }
    }
  }
}
</style>
