export default [
  {
    path: '/setting/manufacturer-management',
    component: resolve =>
      require([`@/views/manufacturerManagement/index.vue`], resolve),
    name: '厂家管理'
  },
  {
    path: '/setting/brand-management',
    component: resolve =>
      require([`@/views/brandManagement/index.vue`], resolve),
    name: '品牌管理'
  },
  {
    path: '/setting/supplier-management',
    component: resolve =>
      require([`@/views/supplierManagement/index.vue`], resolve),
    name: '供应商管理'
  },
  {
    path: '/setting/classification-dictionary',
    component: resolve =>
      require([`@/views/classificationDictionary/index.vue`], resolve),
    name: '分类字典'
  },
  {
    path: '/setting/equipment-dictionary',
    component: resolve =>
      require([`@/views/equipmentDictionary/index.vue`], resolve),
    name: '资产字典'
  },
  {
    path: '/setting/system',
    component: resolve => require([`@/views/systemSetting/index.vue`], resolve),
    name: '系统设置'
  }
];
