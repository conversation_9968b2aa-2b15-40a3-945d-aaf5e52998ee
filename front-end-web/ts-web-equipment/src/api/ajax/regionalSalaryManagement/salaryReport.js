import { request } from '@/api/ajax';

// 获取工资条列表
const getSalaryReportList = function(data) {
  return request({
    url: `/ts-device/api/payslipUpload/upload/list`,
    method: 'get',
    data
  });
};

// 导入工资条
const importSalary = function(type, month, data) {
  return request({
    url: `/ts-device/api/payslipUpload/import/${type}?month=${month}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
};

//上报工资条
const reportSalaryList = function(id, data) {
  return request({
    url: `/ts-device/api/payslipUpload/upload/${id}`,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data
  });
};

// 删除工资条
const deleteSalaryList = function(id) {
  return request({
    url: '/ts-device/api/payslipUpload/delete/' + id,
    method: 'post'
  });
};

//获取工资条明细
const getSalaryDetail = function(id, data) {
  return request({
    url: '/ts-device/api/payslipLog/list/' + id,
    method: 'get',
    data
  });
};

const batchExportSalary = function(data) {
  return request({
    url: `/ts-device/api/payslipUpload/batchExport`,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data
  });
};

export {
  getSalaryReportList,
  importSalary,
  reportSalaryList,
  deleteSalaryList,
  getSalaryDetail,
  batchExportSalary
};
export default {
  getSalaryReportList,
  importSalary,
  reportSalaryList,
  deleteSalaryList,
  getSalaryDetail,
  batchExportSalary
};
