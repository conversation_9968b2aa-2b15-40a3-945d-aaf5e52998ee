import { request } from '@/api/ajax';

const deviceOutboundOrderList = function(data) {
  return request({
    url: `/ts-ams/api/device/outboundOrder/list`,
    method: 'get',
    data
  });
};

const deviceOutboundOrderDetails = function(id) {
  return request({
    url: `/ts-ams/api/device/outboundOrder/` + id,
    method: 'get'
  });
};

const deviceOutboundOrderSave = function(data) {
  return request({
    url: `/ts-ams/api/device/outboundOrder/save`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const deviceOutboundOrderUpdate = function(data) {
  return request({
    url: `/ts-ams/api/device/outboundOrder/update`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const deviceOutboundOrderDelete = function(id) {
  return request({
    url: '/ts-ams/api/device/outboundOrder/delete/' + id,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 }
  });
};

const deviceOutboundOrderConfirm = function(id) {
  return request({
    url: `/ts-ams/api/device/outboundOrder/confirm/` + id,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 }
  });
};

export {
  deviceOutboundOrderList,
  deviceOutboundOrderDetails,
  deviceOutboundOrderSave,
  deviceOutboundOrderUpdate,
  deviceOutboundOrderDelete,
  deviceOutboundOrderConfirm
};

export default {
  deviceOutboundOrderList,
  deviceOutboundOrderDetails,
  deviceOutboundOrderSave,
  deviceOutboundOrderUpdate,
  deviceOutboundOrderDelete,
  deviceOutboundOrderConfirm
};
