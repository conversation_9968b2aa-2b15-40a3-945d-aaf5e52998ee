import { request } from '@/api/ajax';

const inventoryPlanList = function(data) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/list`,
    method: 'get',
    data
  });
};

const inventoryPlanDetails = function(id) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/${id}`,
    method: 'get'
  });
};

const inventoryPlanSave = function(data) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/save`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const inventoryPlanUpdate = function(data) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/update`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const inventoryPlanDelete = function(id) {
  return request({
    url: '/ts-ams/api/device/inventoryPlan/delete/' + id,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 }
  });
};

const inventoryTaskStatusNums = function(id) {
  return request({
    url: `/ts-ams/api/device/inventoryTask/status/nums/${id}`,
    method: 'get'
  });
};

const inventoryTaskList = function(id, data) {
  return request({
    url: `/ts-ams/api/device/inventoryTask/list/${id}`,
    method: 'get',
    data
  });
};

const inventoryTaskUpdate = function(data) {
  return request({
    url: '/ts-ams/api/device/inventoryTask/update',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const inventoryPlanSure = function(id) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/sure/${id}`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 }
  });
};

const inventoryTaskExpList = function(data) {
  return request({
    url: `/ts-ams/api/device/inventoryTask/exp/list`,
    method: 'get',
    data
  });
};

const inventoryPlanTasks = function(data) {
  return request({
    url: `/ts-ams/api/device/inventoryPlan/tasks`,
    method: 'post',
    data
  });
};

export {
  inventoryPlanList,
  inventoryPlanSave,
  inventoryPlanUpdate,
  inventoryPlanDelete,
  inventoryPlanDetails,
  inventoryTaskStatusNums,
  inventoryTaskList,
  inventoryTaskUpdate,
  inventoryPlanSure,
  inventoryTaskExpList,
  inventoryPlanTasks
};

export default {
  inventoryPlanList,
  inventoryPlanSave,
  inventoryPlanUpdate,
  inventoryPlanDelete,
  inventoryPlanDetails,
  inventoryTaskStatusNums,
  inventoryTaskList,
  inventoryTaskUpdate,
  inventoryPlanSure,
  inventoryTaskExpList,
  inventoryPlanTasks
};
