import { request } from '@/api/ajax';

// 入库单转退货单
const materialReturnInbFmt = function(id) {
  return request({
    url: `/ts-ams/api/material/return/inbFmt/${id}`,
    method: 'get'
  });
};

// 物资仓库的物资分类树
const warehouseCategoryTreeByWhId = function(whId) {
  return request({
    url: `/ts-ams/api/material/warehouse/categoryTree/${whId}`,
    method: 'get'
  });
};

// 物料入库列表
const materialInbList = function(params) {
  return request({
    url: `/ts-ams/api/material/inb/list`,
    method: 'get',
    params
  });
};

// 批量删除入库单据
const materialInbBatchDelete = function(data) {
  return request({
    url: `/ts-ams/api/material/inb/batch/delete`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

// 批量审核入库单据
const materialInbBatchConfirm = function(data) {
  return request({
    url: `/ts-ams/api/material/inb/batch/confirm`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

// 批量取消审核入库单据
const mInbBRollbackConfirm = function(data) {
  return request({
    url: `/ts-ams/api/material/inb/batch/rollback-confirm`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialInbSave = function(data) {
  return request({
    url: '/ts-ams/api/material/inb/save',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialInbUpdate = function(data) {
  return request({
    url: '/ts-ams/api/material/inb/update',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

const materialInbDetail = function(id, params) {
  return request({
    url: `/ts-ams/api/material/inb/${id}`,
    params,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'get'
  });
};

const materialInbDelete = function(id) {
  return request({
    url: `/ts-ams/api/material/inb/delete/${id}`,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'post'
  });
};

const materialInbDirection = function(id, type) {
  return request({
    url: `/ts-ams/api/material/inb/${id}?direction=${type}`,
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    method: 'get'
  });
};

export default {
  materialReturnInbFmt,
  warehouseCategoryTreeByWhId,
  materialInbList,
  materialInbSave,
  materialInbUpdate,
  materialInbDetail,
  materialInbDelete,
  materialInbBatchDelete,
  materialInbBatchConfirm,
  mInbBRollbackConfirm,
  materialInbDirection
};
