import { request } from '@/api/ajax';

// 库房列表 关联物资分类
const materialWarehouseRelaVsCategoryList = function(warehouseId) {
  return request({
    url: `/ts-ams/api/material/warehouseRela/vs/category/list/${warehouseId}`,
    method: 'get'
  });
};

const materialWarehouseRelaVsCategory = function(data) {
  return request({
    url: '/ts-ams/api/material/warehouseRela/vs/category',
    method: 'post',
    headers: { 'Content-Type': 'application/json', messageErrorAlert: 1 },
    data
  });
};

export default {
  materialWarehouseRelaVsCategoryList,
  materialWarehouseRelaVsCategory
};
