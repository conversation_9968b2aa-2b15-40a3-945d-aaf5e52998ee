export default {
  data() {
    return {
      allColumns: [
        {
          label: '序号',
          fixed: 'left',
          prop: 'pageIndex',
          align: 'center',
          width: 50
        },
        {
          label: '单位名称',
          fixed: 'left',
          prop: 'dwmc',
          align: 'center',
          width: 100
        },
        {
          label: '姓名',
          fixed: 'left',
          prop: 'xm',
          align: 'center',
          width: 60
        },
        {
          label: '内设处(科)室',
          fixed: 'left',
          prop: 'ks',
          align: 'center',
          width: 100
        },
        {
          label: '人事性质',
          fixed: 'left',
          prop: 'rsxz',
          align: 'center',
          width: 80
        },
        {
          label: '身份证号',
          fixed: 'left',
          prop: 'sfzh',
          align: 'center',
          width: 185
        },
        {
          label: '应发工资',
          align: 'center',
          children: [
            {
              label: '职务工资',
              prop: 'zwgz',
              align: 'center',
              width: 80
            },
            {
              label: '级别工资',
              prop: 'jbgz',
              align: 'center',
              width: 80
            },
            {
              label: '岗位工资',
              prop: 'gwgz',
              align: 'center',
              width: 80
            },
            {
              label: '等(薪)级工资',
              prop: 'djgz',
              align: 'center',
              width: 100
            },
            {
              label: '教龄(护龄)工资',
              prop: 'jlhlgz',
              align: 'center',
              width: 100
            },
            {
              label: '基础性绩效',
              prop: 'jcxjx',
              align: 'center',
              width: 80
            },
            {
              label: '奖励性绩效',
              prop: 'jlxjx',
              align: 'center',
              width: 80
            },
            {
              label: '信访人员岗位津贴',
              prop: 'xfrygwjt',
              align: 'center',
              width: 120
            },
            {
              label: '公务交通补贴',
              prop: 'gwjtbt',
              align: 'center',
              width: 80
            },
            {
              label: '妇女卫生费',
              prop: 'fnwsf',
              align: 'center',
              width: 80
            },
            {
              label: '绩效奖金',
              prop: 'jxjj',
              align: 'center',
              width: 80
            },
            {
              label: '应发合计',
              prop: 'yfhj',
              align: 'center',
              width: 80
            }
          ]
        },
        {
          label: '应扣个人部分',
          align: 'center',
          children: [
            {
              label: '住房公积金',
              prop: 'sfgz',
              align: 'center',
              width: 80
            },
            {
              label: '养老保险基金',
              prop: 'ylbxjj',
              align: 'center',
              width: 80
            },
            {
              label: '医疗保险基金',
              prop: 'ylbxjj1',
              align: 'center',
              width: 80
            },
            {
              label: '职业年金',
              prop: 'zynj',
              align: 'center',
              width: 80
            },
            {
              label: '个人所得税',
              prop: 'grsds',
              align: 'center',
              width: 80
            },
            {
              label: '失业保险',
              prop: 'sybx',
              align: 'center',
              width: 80
            },
            {
              label: '扣发合计',
              prop: 'kfhj',
              align: 'center',
              width: 80
            }
          ]
        },
        {
          label: '实发工资',
          prop: 'sfgz',
          align: 'center',
          width: 80
        },
        {
          label: '备注',
          prop: 'bz',
          align: 'center',
          width: 200
        }
      ]
    };
  }
};
