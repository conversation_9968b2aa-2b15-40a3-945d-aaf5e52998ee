<template>
  <ts-dialog
    class="dialog-import-salary"
    :title="
      `${actionType == 'import' ? `导入${title}工资` : `重新上报${title}工资`}`
    "
    width="450px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancel"
  >
    <div class="content">
      <ts-form ref="form" :model="form">
        <ts-row>
          <ts-col :span="20">
            <ts-form-item prop="month" :rules="rules.required" label="月份">
              <ts-month-picker
                style="width: 100%"
                value-format="yyyy-MM"
                placeholder="请选择月份"
                v-model="form.month"
                :disabled="actionType == 'reimport'"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-row>
          <div class="content-file-box">
            <span class="label">工资表</span>
            <input
              v-show="false"
              id="fileUpload"
              ref="fileUpload"
              type="file"
              @change="handleAddFile"
              accept=".xls,.xlsx"
            />
            <ts-button @click="handleUploadFile">上传文件</ts-button>
            <div class="download-text" @click="handleDownloadTemplate">
              点击下载模版
            </div>
          </div>
          <ul class="file-list">
            <li v-for="(item, index) in file" :key="index">
              <span class="file-name">{{ item.name }}</span>
              <span
                class="red layui-icon layui-icon-close"
                @click="handleDelLocalFile"
              ></span>
            </li>
          </ul>
        </ts-row>
      </ts-form>
      <ReportStepForm
        v-if="actionType == 'reimport'"
        ref="reportStepForm"
        :stepList="stepList"
        @change="handleReportStepFormChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        取 消
      </ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/util/index.js';
import ReportStepForm from './report-step-form.vue';

export default {
  components: {
    ReportStepForm
  },
  data() {
    return {
      visible: false,
      title: '',
      submitLoading: false,

      actionType: '',
      type: '', //0-在编, 1-非编
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      file: [],

      workflowForm: {},
      stepList: []
    };
  },
  methods: {
    async show(data) {
      this.actionType = data.actionType;
      if (data.month) this.form.month = data.month;
      this.type = data.type;
      this.title = data.type == 0 ? '编制内' : '编制外';
      if (this.actionType == 'reimport') {
        this.workflowForm = {
          taskId: data.taskId,
          id: data.id,
          wfDefinitionId: data.wfDefinitionId
        };
        await this.getNextWfStepListByWfDefId({
          wfDefId: data.wfDefinitionId,
          workflowStart: 'Y'
        });
        this.$refs.reportStepForm?.$refs.form?.clearValidate();
      }
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.visible = true;
      });
    },
    async getNextWfStepListByWfDefId(data) {
      await this.ajax.getNextWfStepListByWfDefId(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.stepList = deepClone(res.object) || [];
        } else {
          this.$message.error(res.message || '数据获取失败');
        }
      });
    },
    // 删除本地文件记录
    handleDelLocalFile() {
      this.file = [];
      this.form.file = null;
      this.$refs.fileUpload.value = null;
    },
    // 触发上传组件
    handleUploadFile() {
      const fileUpload = this.$refs.fileUpload;
      fileUpload.value = null;
      fileUpload.click();
    },
    // 获取 文件
    handleAddFile() {
      const fileUpload = document.getElementById('fileUpload');
      if (fileUpload.files.length === 0) {
        return false;
      }
      this.file = fileUpload.files;
      this.form.file = fileUpload.files[0];
    },
    // 模版下载
    handleDownloadTemplate() {
      let a = document.createElement('a');
      a.href = `/ts-device/api/payslipUpload/tpl/download/${this.type}`;
      a.click();
    },
    handleReportStepFormChange(val) {
      this.workflowForm = {
        ...this.workflowForm,
        ...val
      };
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        if (this.file.length === 0) {
          this.$message.warning(`请上传${this.title}工资表!`);
        } else {
          var formData = new FormData();
          formData.append('file', this.form.file);
          this.submitLoading = true;
          const res = await this.ajax.importSalary(
            this.type,
            this.form.month,
            formData
          );
          if (res.success && res.statusCode === 200) {
            if (this.actionType == 'reimport') {
              let resubmitReportRes = await this.handleResubmitReport();
              if (
                resubmitReportRes.success &&
                resubmitReportRes.statusCode === 200
              ) {
                this.$message.success('上报成功，请等待审核!');
              } else {
                this.submitLoading = false;
                this.$message.error(resubmitReportRes.message || '操作失败!');
                return false;
              }
            }
            this.submitLoading = false;
            this.$message.success('操作成功!');
            this.$emit('submit');
            this.handleCancel();
          } else {
            this.submitLoading = false;
            this.$message.error(
              res.object
                ? res.object + '在人员档案中未匹配到正确信息！'
                : res.message || '操作失败!'
            );
          }
        }
      } catch (error) {
        console.error(error);
      }
    },
    async handleResubmitReport() {
      let formData = deepClone(this.workflowForm);
      formData.status = 1;
      if (this.workflowForm.users && this.workflowForm.users.length) {
        formData.users = this.workflowForm.users.join(',');
        formData.names = this.workflowForm.names.join(',');
      }
      return await this.ajax.reportSalaryList(formData.id, formData);
    },
    handleCancel() {
      this.submitLoading = false;
      this.form = {};
      this.file = [];
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-import-salary {
  .red {
    color: red;
  }
  .content-file-box {
    display: flex;
    align-items: center;

    .label {
      width: 124px;
      text-align: right;
      padding-right: 8px;
      position: relative;
      &::before {
        content: '*';
        color: rgb(245, 108, 108);
        margin-right: 4px;
      }
    }
    .download-text {
      color: #0000ff;
      font-size: 12px;
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .file-list {
    margin: 8px 0 0 124px;
    list-style: none;

    li {
      width: 100%;
      display: flex;
      justify-content: space-between;

      > .red {
        cursor: pointer;
      }
    }
  }
}
</style>
