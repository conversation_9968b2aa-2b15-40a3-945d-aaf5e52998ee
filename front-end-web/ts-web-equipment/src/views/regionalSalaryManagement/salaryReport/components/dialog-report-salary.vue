<template>
  <ts-dialog
    class="dialog-import-salary"
    :title="title"
    width="450px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancel"
  >
    <ReportStepForm
      ref="reportStepForm"
      :stepList="stepList"
      @change="handleReportStepFormChange"
    />
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        取 消
      </ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/util/index.js';
import ReportStepForm from './report-step-form.vue';
export default {
  components: {
    ReportStepForm
  },
  data() {
    return {
      visible: false,
      title: '',
      submitLoading: false,
      form: {},
      stepList: []
    };
  },
  methods: {
    async show(data) {
      this.type = data.type;
      this.title = `上报${data.month}${
        data.type == 0 ? '编制内' : '编制外'
      }工资表`;
      await this.getNextWfStepListByWfDefId({
        wfDefId: data.wfDefinitionId,
        workflowStart: 'Y'
      });
      this.form = {
        id: data.id,
        wfDefinitionId: data.wfDefinitionId
      };
      this.$nextTick(() => {
        this.$refs.reportStepForm?.$refs.form?.clearValidate();
        this.visible = true;
      });
    },
    async getNextWfStepListByWfDefId(data) {
      await this.ajax.getNextWfStepListByWfDefId(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.stepList = deepClone(res.object) || [];
        } else {
          this.$message.error(res.message || '数据获取失败');
        }
      });
    },
    handleReportStepFormChange(val) {
      this.form = {
        ...this.form,
        ...val
      };
    },
    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.reportStepForm.$refs.form.validate();
        let formData = deepClone(this.form);
        formData.status = 1;
        delete formData.users;
        delete formData.names;
        if (this.form.users && this.form.users.length) {
          formData.users = this.form.users.join(',');
          formData.names = this.form.names.join(',');
        }
        await this.ajax.reportSalaryList(this.form.id, formData).then(res => {
          this.submitLoading = false;
          if (res.success && res.statusCode === 200) {
            this.$message.success('上报成功，请等待审核!');
            this.handleCancel();
            this.$emit('submit');
          } else {
            this.$message.error(res.message || '上报失败');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      }
    },
    handleCancel() {
      this.submitLoading = false;
      this.visible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped></style>
