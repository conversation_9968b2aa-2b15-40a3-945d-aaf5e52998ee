<template>
  <div class="other-wait-status">
    <ts-search-bar-new
      ref="TsSearchBar"
      v-model="searchForm"
      :formList="searchList"
      :resetData="resetData"
      @search="search"
    >
      <template slot="category">
        <input-tree
          v-model="searchForm.category22Id"
          placeholder="请选择"
          :treeData="categoryTreeData"
          :defaultExpandAll="false"
          key="category22Id"
        />
      </template>
      <template slot="departmentBelonged">
        <input-tree
          v-model="searchForm.belongToOrgId"
          placeholder="请选择所属科室"
          :treeData="deptTreeData"
          :defaultExpandedKeys="defaultExpandedKeys"
          key="deptId"
        />
      </template>
      <template #right>
        <ts-button
          class="shallow-button"
          type="primary"
          @click="handleInstallAcceptance"
        >
          安装验收登记
        </ts-button>
      </template>
    </ts-search-bar-new>

    <ts-vxe-base-table
      class="form-table"
      id="other-wait-status-table"
      ref="table"
      :columns="otherNoInstallAcceptanceColumns"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectionChange"
    />

    <dialog-add-install-acceptance
      ref="DialogAddInstallAcceptance"
      @submit="handleRefreshTable"
      :ymdList="ymdList"
    />

    <dialog-install-acceptance-details ref="DialogInstallAcceptanceDetails" />
    <dialog-ledger-detail ref="DialogLedgerDetail" />
  </div>
</template>

<script>
import columns from '../minxis/columns.js';
import InputTree from '@/components/input-tree/index.vue';

import DialogAddInstallAcceptance from './dialog-add-install-acceptance.vue';
import DialogInstallAcceptanceDetails from './dialog-install-acceptance-details.vue';
import DialogLedgerDetail from '@/views/equipmentLedger/components/dialog-ledger-detail.vue';
export default {
  name: 'CompleteStatus',
  props: {
    ymdList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    DialogAddInstallAcceptance,
    DialogInstallAcceptanceDetails,
    InputTree,
    DialogLedgerDetail
  },
  mixins: [columns],
  data() {
    return {
      selection: [],
      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '设备名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入设备名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '医疗器械分类',
          value: 'category'
        },
        {
          label: '所属科室',
          value: 'departmentBelonged'
        }
      ],

      categoryTreeData: [],
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  created() {
    this.getCategoryTree();
    this.getDeptTree();
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        formData = {
          ...this.searchForm,
          pageNo,
          pageSize,
          logic: 'signoff'
        };

      let res = await this.ajax.deviceList4select(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });

      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    async getCategoryTree() {
      try {
        const res = await this.ajax.getClassificationTree();
        if (!res.success) {
          throw res.message;
        }
        this.categoryTreeData = res.object || [];
      } catch (e) {
        this.$message.error(e || '分类数据获取失败');
      }
    },

    async getDeptTree() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw res.message;
        }
        this.deptTreeData = res.object || [];
        this.defaultExpandedKeys = [this.deptTreeData[0]?.id];
      } catch (e) {
        this.$message.error(e || '科室数据获取失败');
      }
    },

    async handleClickName(row) {
      let res = await this.ajax.getEquipmentLedgerDetail(row.id);
      if (!res.success) {
        this.$newMessage('error', res.message || '获取设备详情失败!');
        return;
      }
      this.$refs.DialogLedgerDetail.show({
        data: res.object || {}
      });
    },

    handleInstallAcceptance() {
      if (this.selection.length === 0) {
        this.$newMessage('warning', '请先勾选需要安装验收的设备！');
        return;
      }

      // 点击安装验收
      // selectType 1 采入购库未登记 2 其他入库未登记
      this.$refs.DialogAddInstallAcceptance.open({
        type: 'add',
        selectType: '2',
        params: {
          purchaseOrderId: '',
          signoffDetailList: this.selection
        }
      });
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    }
  }
};
</script>

<style lang="scss" scoped>
.other-wait-status {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid $primary-blue;
  padding: 8px;
  border-radius: 4px;

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }
}
</style>
