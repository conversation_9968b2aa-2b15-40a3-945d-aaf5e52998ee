<template>
  <el-drawer
    class="ts-custom-default-drawer dialog-add-install-acceptance"
    :visible.sync="visible"
    direction="ttb"
    size="100%"
    append-to-body
    destroy-on-close
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ typeTitle }}</span>
    </template>

    <div class="content-container">
      <ts-tabs :type="null" v-model="activeTab" @tab-click="handleTabChange">
        <ts-tab-pane
          v-for="item in tabList"
          :key="item.name"
          :label="item.label"
          :name="item.name"
        />
      </ts-tabs>

      <div class="tabs-content">
        <ts-form ref="form" :model="form" :rules="rules">
          <div :id="tabList[0].name" class="form-group-tips">
            <span>{{ tabList[0].label }}</span>
          </div>
          <!-- 基本信息 -->
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="验收人员"
                prop="signoff.sureUserName"
                :rules="rules.required"
              >
                <ts-input
                  v-model="form.signoff.sureUserName"
                  placeholder="请选择"
                  style="width: 100%;"
                  readonly
                >
                  <template v-slot:suffix>
                    <img
                      class="person-icon"
                      src="@/assets/img/defUserPhoto.png"
                      @click="handleOpenSelectPerson('sureUser')"
                    />
                  </template>
                </ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="验收日期"
                prop="signoff.sureDate"
                :rules="rules.required"
              >
                <ts-date-picker
                  v-model="form.signoff.sureDate"
                  type="date"
                  placeholder="请选择验收日期"
                  style="width: 100%;"
                  valueFormat="YYYY-MM-DD"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>

          <ts-form-item class="custom-textarea-container" label="验收情况">
            <textarea
              v-model="form.signoff.note"
              class="custom-textarea"
              maxlength="200"
              placeholder="请输入"
            ></textarea>
            <div class="word-limit" v-if="form.signoff">
              {{ form.signoff.note?.length || 0 }}/200
            </div>
          </ts-form-item>

          <div class="divider"></div>

          <div :id="tabList[1].name" class="form-group-tips">
            <span>{{ tabList[1].label }}</span>
            <div>
              <ts-button
                class="shallowButton"
                type="primary"
                @click="handleAddInstallAcceptanceEquipment"
              >
                追加设备
              </ts-button>
              <ts-button
                class="shallowButton"
                type="primary"
                @click="handleBatchEnterInstallAcceptanceInfo"
              >
                批量录入信息
              </ts-button>
            </div>
          </div>
          <!-- 安装验收详情优化 -->
          <install-acceptance-details-optimize
            ref="InstallAcceptanceDetails"
            :form="form"
            :rules="rules"
            :ymdList="ymdList"
            :cycleColumns="cycleColumns"
          />
          <div class="divider"></div>

          <div :id="tabList[2].name" class="form-group-tips">
            <span>{{ tabList[2].label }} {{ fileText }} </span>
          </div>
          <install-acceptance-files :form="form" />
        </ts-form>
      </div>

      <div class="drawer-footer">
        <ts-button type="primary" :loading="submitLoading" @click="submit">
          确 定
        </ts-button>
        <ts-button
          class="shallowButton"
          :disabled="submitLoading"
          @click="close"
        >
          关 闭
        </ts-button>
      </div>
    </div>

    <dialog-batch-set-install-acceptance-info
      ref="DialogBatchSetInstallAcceptanceInfo"
      :ymdList="ymdList"
      :cycleColumns="cycleColumns"
      @submit="handleBatchSetInstallAcceptanceInfoSubmit"
    />
    <dialog-add-install-acceptance-equipment
      ref="DialogAddInstallAcceptanceEquipment"
      :activeRowId="form.signoff.purchaseOrderId"
      :ignoreIdList="ignoreIdList"
      @submit="addInstallAcceptanceEquipmentSubmit"
    />
    <dialog-multiple-device
      renderType="6"
      ref="DialogMultipleDevice"
      @submit="handleMultipleDeviceSubmit"
    />
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleOk" />
  </el-drawer>
</template>

<script>
import columns from '../minxis/columns.js';
import { deepClone } from '@/unit/commonHandle.js';
import InstallAcceptanceFiles from './install-acceptance-files.vue';
import DialogBatchSetInstallAcceptanceInfo from './dialog-batch-set-install-acceptance-info.vue';
import DialogAddInstallAcceptanceEquipment from './dialog-add-install-acceptance-equipment.vue';
import DialogMultipleDevice from '@/views/inventoryManagement/components/dialog-multiple-device.vue';
import InstallAcceptanceDetailsOptimize from './install-acceptance-details-optimize.vue';
export default {
  components: {
    InstallAcceptanceFiles,
    DialogBatchSetInstallAcceptanceInfo,
    DialogAddInstallAcceptanceEquipment,
    DialogMultipleDevice,
    InstallAcceptanceDetailsOptimize
  },
  props: {
    ymdList: {
      type: Array,
      default: () => []
    }
  },
  mixins: [columns],
  computed: {
    typeTitle() {
      return `${this.type === 'add' ? '新增' : '编辑'}安装验收`;
    },
    isAdd() {
      return this.type === 'add';
    },
    isEdit() {
      return this.type === 'edit';
    },
    ignoreIdList() {
      return this.form.signoffDetailList.map(item => item.id);
    },
    fileText() {
      return `注意：每次上传的文件总数量不超过20个，文件总大小不超过${this.allowFileSize}M，支持${this.allowFileExtension}类型；`;
    }
  },
  data() {
    return {
      loading: null,
      visible: false,
      activeTab: 'baseData',
      tabList: [
        { label: '安装验收信息', name: 'baseData' },
        { label: '设备信息', name: 'objectData' },
        { label: '附件信息', name: 'paymentData' }
      ],

      // 周期列
      cycleColumns: [
        {
          label: '使用年限',
          width: 110,
          render: 'cycle',
          prop: 'lifespanVal'
        },
        {
          label: '厂家保修时长',
          width: 115,
          render: 'cycle',
          prop: 'warrantyPeriodVal',
          requiredIcon: true
        },
        {
          label: '保养周期',
          width: 110,
          render: 'cycle',
          prop: 'maintCycleVal'
        },
        {
          label: '巡检周期',
          width: 110,
          render: 'cycle',
          prop: 'inspectionCycleVal'
        },
        {
          label: '是否强检',
          width: 80,
          prop: 'calibrationType',
          render: 'switch'
        },
        {
          label: '计量周期',
          width: 110,
          render: 'cycle',
          prop: 'calibrationCycleVal'
        }
      ],

      submitLoading: false,
      type: 'add',
      selectType: '',

      allowFileSize: 200,
      allowFileExtension:
        'doc,docx,dotx,pdf,ppt,pptx,potx,ppsx,xls,xlsx,xlsm,xlsb,xltx',

      form: {
        signoff: {
          purchaseOrderId: '',
          sureDate: '',
          sureUser: '',
          sureUserName: '',
          note: '',
          fileSet: ''
        },
        signoffDetailList: []
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async open({ type = 'add', params = {}, data = {}, selectType }) {
      this.type = type;
      // 1订单类型未登记 / 2其他入库未登记
      this.selectType = selectType;
      this.visible = true;

      this.loading = this.$loading({
        target: document.querySelector('.dialog-add-install-acceptance'),
        text: '数据加载中, 请稍等...'
      });
      // 获取文件大小和类型
      this.handleSetFileConfig();
      // 确保先打开drawer 再设置数据
      await this.handleAwait();

      // 尝试优化 table数据渲染
      requestAnimationFrame(() => {
        let form = this.isAdd ? this.getInitialForm(params) : data;
        this.$set(this, 'form', form);
      });
      setTimeout(() => {
        this.loading.close();
      }, 1000);
    },

    handleAwait() {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve();
        }, 100);
      });
    },

    handleSetFileConfig() {
      let { allowFileSize, allowFileExtension } = this.$getParentStoreInfo(
        'globalSetting'
      );
      this.allowFileSize = allowFileSize;
      this.allowFileExtension = allowFileExtension;
    },

    // 打开选择人员弹窗
    handleOpenSelectPerson(key) {
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: false,
        echoData: {
          sureUser: this.form.signoff.sureUser
        },
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp: ['sureUserName', 'sureUser']
        }
      });
    },

    // 选择人员弹窗确认
    handleOk(result, key) {
      switch (key) {
        case 'sureUser':
          const { allNames: sureUserName, sureUser } = result[key];
          this.$set(this.form.signoff, 'sureUserName', sureUserName);
          this.$set(this.form.signoff, 'sureUser', sureUser);
          break;
      }
    },

    // 获取初始表单数据
    getInitialForm({ purchaseOrderId, signoffDetailList }) {
      return {
        signoff: {
          purchaseOrderId,
          sureDate: '',
          sureUser: '',
          sureUserName: '',
          note: '',
          fileSet: ''
        },
        signoffDetailList: this.formatterList(signoffDetailList)
      };
    },

    // 处理安装验收设备列表 工厂函数
    // 年月 单位默认为年 value: 1
    // 启用日期 默认当前日期
    formatterList(list) {
      return list.map(item => ({
        ...item,
        useDate: item.useDate || this.$dayjs().format('YYYY-MM-DD'),
        deviceId: item.id,
        lifespanUnit: item.lifespanUnit || '1',
        warrantyPeriodUnit: item.warrantyPeriodUnit || '1',
        maintCycleUnit: item.maintCycleUnit || '1',
        inspectionCycleUnit: item.inspectionCycleUnit || '1',
        calibrationCycleUnit: item.calibrationCycleUnit || '1',
        isCheck: false
      }));
    },

    // 处理标签页切换
    handleTabChange() {
      const element = document.getElementById(this.activeTab);
      element?.scrollIntoView({ behavior: 'smooth' });
    },

    // 追加安装验收设备
    async handleAddInstallAcceptanceEquipment() {
      if (this.selectType === '1') {
        this.$refs.DialogAddInstallAcceptanceEquipment.show();
      } else {
        this.$refs.DialogMultipleDevice.show({
          columns: this.otherNoInstallAcceptanceColumns,
          ignoreId: this.ignoreIdList.join(',')
        });
      }
    },

    // 订单类型 追加安装验收设备提交
    addInstallAcceptanceEquipmentSubmit(selection) {
      const list = this.formatterList(selection);
      this.form.signoffDetailList.push(...list);
    },

    // 其他入库未登记 追加安装验收设备提交
    handleMultipleDeviceSubmit(selection) {
      const list = this.formatterList(selection);
      this.form.signoffDetailList.push(...list);
    },

    // 批量录入安装验收信息
    async handleBatchEnterInstallAcceptanceInfo() {
      let selectList = this.$refs.InstallAcceptanceDetails.getSelectList();
      if (selectList.length === 0) {
        this.$newMessage('warning', '请先选择批量安装验收设备!');
        return;
      }

      this.$refs.DialogBatchSetInstallAcceptanceInfo.show({
        deviceIds: selectList.map(item => item.deviceId)
      });
    },

    // 批量设置安装验收信息
    handleBatchSetInstallAcceptanceInfoSubmit(deviceIds, data) {
      const fields = [
        { key: 'birthDate' },
        { key: 'useDate' },
        { key: 'calibrationType' },
        { key: 'lifespanVal', unit: 'lifespanUnit' },
        { key: 'warrantyPeriodVal', unit: 'warrantyPeriodUnit' },
        { key: 'maintCycleVal', unit: 'maintCycleUnit' },
        { key: 'inspectionCycleVal', unit: 'inspectionCycleUnit' },
        { key: 'calibrationCycleVal', unit: 'calibrationCycleUnit' }
      ];
      const update = (row, key, value) => this.$set(row, key, value);

      this.form.signoffDetailList.forEach(row => {
        if (!deviceIds.includes(row.deviceId)) return;

        // fill 为1 或者 字段为空 则更新
        fields.forEach(({ key, unit }) => {
          if (data.fill === '1' || !row[key]) {
            update(row, key, data[key]);

            if (unit && (data.fill === '1' || !row[unit])) {
              update(row, unit, data[unit]);
            }
          }
        });
      });

      this.$forceUpdate();
    },

    async submit() {
      if (this.form.signoffDetailList.length === 0) {
        this.$newMessage('warning', '安装验收至少有一条设备信息, 请添加!');
        return;
      }
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        let data = deepClone(this.form);

        let API = this.isAdd ? 'signoffSave' : 'signoffUpdate';
        let res = await this.ajax[API](data);
        if (!res.success) {
          this.$newMessage('error', res.message || this.typeTitle + '失败!');
          return;
        }
        this.$newMessage('success', res.message || this.typeTitle + '成功!');
        // 提交成功后返回 订单ID 刷新设备列表
        this.$emit('submit', data.signoff.purchaseOrderId);
        this.close();
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      let form = this.getInitialForm({
        purchaseOrderId: '',
        signoffDetailList: []
      });
      this.$set(this, 'form', form);
      this.$refs.form.clearValidate();
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-install-acceptance {
  ::v-deep {
    .content-container {
      display: flex;
      flex-direction: column;
      background: #fff;
      padding-bottom: 36px;
      transform: translateZ(0);
      will-change: transform;
      contain: strict;

      .tabs-content {
        flex: 1;
        display: flex;
        overflow-y: auto;
        padding: 0 24px;

        .ts-form {
          width: 100%;
        }
      }

      .divider {
        width: 100%;
        height: 1px;
        background-color: #e5e5e5;
      }

      .custom-textarea-container {
        position: relative;
        .word-limit {
          position: absolute;
          right: 10px;
          bottom: 16px;
          color: #909399;
          font-size: 12px;
        }

        .custom-textarea {
          width: 100%;
          height: 100px;
          padding: 8px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          outline: none;
          resize: vertical;
          font-family: inherit;
          font-size: 14px;
          line-height: 1.5;

          &::placeholder {
            color: #c0c4cc;
          }

          &:focus {
            border-color: $primary-blue;
          }
        }
      }

      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 800;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;

        > span {
          font-weight: 800;
          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: $primary-blue;
            margin-right: 8px;
            border-radius: 4px;
            transform: translateY(2px);
          }
        }
      }
    }
  }
}
</style>
