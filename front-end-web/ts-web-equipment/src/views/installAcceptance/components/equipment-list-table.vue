<template>
  <div class="equipment-list-table">
    <ts-search-bar-new
      ref="TsSearchBar"
      v-model="searchForm"
      :formList="searchList"
      @search="search"
    >
      <template #right>
        <ts-button
          v-if="!ignoreIdList.length"
          class="shallow-button"
          type="primary"
          @click="handleInstallAcceptance"
        >
          安装验收登记
        </ts-button>
      </template>
    </ts-search-bar-new>

    <ts-vxe-base-table
      class="form-table"
      id="equipment-list-table"
      ref="table"
      empty-text="暂无可安装验收的设备！"
      :hasPage="false"
      :columns="columns"
      @selection-change="handleSelectionChange"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
export default {
  name: 'EquipmentListTable',
  props: {
    activeRowId: {
      type: String,
      default: ''
    },
    // 追加安装验收的设备id列表
    ignoreIdList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selection: [],
      searchForm: {},
      searchList: [
        {
          label: '设备名称',
          value: 'deviceName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入设备名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],

      columns: [
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '设备名称',
          align: 'center',
          minWidth: 120,
          prop: 'name',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => this.handleClickName(row)
                }
              },
              row.name
            );
          }
        },
        {
          label: '设备编号',
          align: 'center',
          prop: 'uniqueNo',
          width: 135
        },
        {
          label: '入库单号',
          align: 'center',
          width: 140,
          prop: 'inboundOrderFlowNo',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => this.handleClickBatchNo(row)
                }
              },
              row.inboundOrderFlowNo
            );
          }
        },
        {
          label: '规格型号',
          align: 'center',
          minWidth: 140,
          prop: 'model'
        },
        {
          label: '品牌',
          align: 'center',
          minWidth: 140,
          prop: 'brandName'
        },
        {
          label: '生产厂家',
          align: 'center',
          minWidth: 140,
          prop: 'manufacturerName'
        }
      ]
    };
  },
  methods: {
    clearTableData() {
      this.$refs.table.refresh([]);
    },

    search() {
      this.handleRefreshTable();
    },

    getSelection() {
      return this.selection;
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    },

    handleClickName(row) {
      this.$emit('clickName', row);
    },

    handleClickBatchNo(row) {
      this.$emit('clickBatchNo', row);
    },

    async handleRefreshTable() {
      let params = {
        ...this.searchForm
      };
      if (this.ignoreIdList.length) {
        params.ignoreIdList = this.ignoreIdList;
      }
      let res = await this.ajax.signoffPenddingDeviceList(
        this.activeRowId,
        params
      );
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.object.map((item, i) => {
        return {
          pageIndex: i + 1,
          ...item
        };
      });
      this.$refs.table.refresh({ rows });
    },

    handleInstallAcceptance() {
      if (this.selection.length === 0) {
        this.$newMessage('warning', '请先勾选需要安装验收的设备！');
        return;
      }
      this.$emit('clickInstallAcceptance', this.selection);
    }
  }
};
</script>

<style lang="scss" scoped>
.equipment-list-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid $primary-blue;
  padding: 8px;
  border-radius: 4px;

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }
}
</style>
