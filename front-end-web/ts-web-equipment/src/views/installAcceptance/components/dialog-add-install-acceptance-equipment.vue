<template>
  <vxe-modal
    className="dialog-add-install-acceptance-equipment"
    title="追加安装验收设备列表"
    v-model="visible"
    width="85%"
    height="80%"
    showFooter
    mask
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <equipment-list-table
          ref="EquipmentListTable"
          :activeRowId="activeRowId"
          :ignoreIdList="ignoreIdList"
        />
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">
          确 定
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import EquipmentListTable from './equipment-list-table.vue';
export default {
  components: {
    EquipmentListTable
  },
  props: {
    activeRowId: String,
    ignoreIdList: Array
  },
  data() {
    return {
      visible: false
    };
  },
  methods: {
    async show() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.EquipmentListTable.handleRefreshTable();
      });
    },

    submit() {
      let selection = this.$refs.EquipmentListTable.getSelection();
      if (!selection.length) {
        this.$newMessage('warning', '请选择追加安装验收设备!');
        return;
      }

      this.$emit('submit', selection);
      this.close();
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-install-acceptance-equipment {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        > .vxe-modal--content {
          > .content {
            width: 100%;
            height: 100%;

            display: flex;
            flex-direction: column;

            .component-table-apply {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;
              .form-table {
                flex: 1;
                overflow: hidden;
                transform: scale(1);
              }
            }
          }
        }
      }
    }
  }
}
</style>
