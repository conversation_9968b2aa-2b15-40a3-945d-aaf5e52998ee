<template>
  <ts-dialog
    custom-class="dialog-service-detail-box"
    :title="
      actionType == 'add' ? '新增' : actionType == 'edit' ? '编辑' : '详情'
    "
    :visible.sync="visible"
    :fullscreen="true"
  >
    <template v-if="visible">
      <el-container class="detail-content">
        <div>
          <el-tabs
            tab-position="left"
            @tab-click="tabshandleClick($event.index)"
            v-model="activeTab"
            style="height: 100%;"
          >
            <el-tab-pane
              v-for="item in tabList"
              :key="item.name"
              :label="item.label"
              :name="item.name"
            ></el-tab-pane>
          </el-tabs>
        </div>
        <el-scrollbar
          ref="rightSrcollbar"
          style="width: 100%;"
          wrap-style="overflow-x: hidden;"
        >
          <ts-form id="SetFormTableForm" ref="form" :model="form">
            <div class="clearfix">
              <div id="basicInfo" class="notes_text">
                <el-divider content-position="left">基本信息</el-divider>
                <template v-if="actionType == 'check'">
                  <description
                    column="3"
                    :columns="baseInfoColumns"
                    :tableData="tableData"
                  >
                    <template slot="files">
                      <base-upload v-model="tableData.files" :onlyRead="true" />
                    </template>
                  </description>
                </template>
                <template v-else>
                  <ts-row>
                    <ts-col :span="8">
                      <ts-form-item
                        label="项目名称"
                        prop="services.name"
                        :rules="rules.required"
                      >
                        <ts-input
                          v-model="form.services.name"
                          placeholder="请输入项目名称"
                        />
                      </ts-form-item>
                    </ts-col>
                    <!-- <ts-col :span="8">
                      <ts-form-item
                        label="服务期限"
                        prop="purchaseLog.purchaseSpec"
                      >
                        <ts-range-picker
                          style="width: 100%"
                          v-model="purchaseLog.purchaseSpec"
                          type="daterange"
                          valueFormat="YYYY-MM-DD"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                        />
                      </ts-form-item>
                    </ts-col> -->
                  </ts-row>
                  <ts-row>
                    <ts-col :span="16">
                      <ts-form-item label="相关文档" prop="services.files">
                        <base-upload v-model="form.services.files">
                        </base-upload>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                </template>
              </div>
              <div id="tenderAndProcurementInfo" class="notes_text">
                <el-divider content-position="left">招标采购</el-divider>
                <template
                  v-if="
                    actionType == 'add' ||
                      (actionType == 'edit' &&
                        !form.purchaseResult.wfInstanceId)
                  "
                >
                  <ts-row>
                    <ts-col :span="8">
                      <ts-form-item
                        label="采购项目名称"
                        prop="purchaseLog.name"
                      >
                        <ts-input
                          v-model="form.purchaseLog.name"
                          placeholder="请输入采购项目名称"
                        />
                      </ts-form-item>
                    </ts-col>
                    <ts-col :span="8">
                      <ts-form-item
                        prop="purchaseLog.purchaseDate"
                        :rules="rules.required"
                        label="采购(合同)时间"
                      >
                        <ts-date-picker
                          style="width:100%"
                          v-model="form.purchaseLog.purchaseDate"
                          valueFormat="YYYY-MM-DD"
                          placeholder="请选择采购(合同)时间"
                        />
                      </ts-form-item>
                    </ts-col>
                    <ts-col :span="8">
                      <ts-form-item label="服务商" prop="purchaseLog.supplier">
                        <ts-input
                          v-model="form.purchaseLog.supplier"
                          placeholder="请输入服务商"
                        />
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                  <ts-row>
                    <ts-col :span="8">
                      <ts-form-item
                        label="采购方式"
                        prop="purchaseLog.purchaseWay"
                      >
                        <ts-select
                          style="width: 100%"
                          v-model="form.purchaseLog.purchaseWay"
                          clearable
                          placeholder="请选择"
                        >
                          <ts-option
                            v-for="(item, index) of options.purchaseType"
                            :key="'purchaseType_' + index"
                            :label="item.itemName"
                            :value="item.itemNameValue"
                          ></ts-option>
                        </ts-select>
                      </ts-form-item>
                    </ts-col>
                    <ts-col :span="8">
                      <ts-form-item
                        label="采购金额(万元)"
                        prop="purchaseLog.purchasePrice"
                        :rules="rules.required"
                      >
                        <ts-input
                          v-model="form.purchaseLog.purchasePrice"
                          placeholder="请输入采购金额"
                          @input="
                            validateInputFourDecimal(
                              $event,
                              'purchaseLog',
                              'purchasePrice'
                            )
                          "
                        />
                      </ts-form-item>
                    </ts-col>
                    <ts-col :span="8">
                      <ts-form-item
                        label="资金来源"
                        prop="purchaseLog.sourcesFunds"
                      >
                        <ts-select
                          style="width: 100%"
                          v-model="form.purchaseLog.sourcesFunds"
                          clearable
                          placeholder="请选择资金来源"
                        >
                          <ts-option
                            v-for="(item, index) of options.fundsSources"
                            :key="'fundsSources_' + index"
                            :label="item.fundsSourcesName"
                            :value="item.fundsSourcesName"
                          ></ts-option>
                        </ts-select>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                  <ts-row>
                    <ts-col :span="16">
                      <ts-form-item
                        label="采购合同"
                        prop="purchaseLog.purchaseFiles"
                      >
                        <base-upload v-model="form.purchaseLog.purchaseFiles">
                        </base-upload>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                  <ts-row>
                    <ts-col :span="16">
                      <ts-form-item
                        label="招标参数"
                        prop="purchaseGroup.tenderParameter"
                      >
                        <base-upload
                          v-model="form.purchaseGroup.tenderParameter"
                        >
                        </base-upload>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                  <ts-row>
                    <ts-col :span="16">
                      <ts-form-item
                        label="招投标备案资料"
                        prop="purchaseGroup.tenderFiles"
                      >
                        <base-upload v-model="form.purchaseGroup.tenderFiles">
                        </base-upload>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                </template>
                <template v-else>
                  <description
                    column="3"
                    :columns="tenderAndPurchaseInfoColumns"
                    :tableData="tableData"
                  >
                    <template slot="purchaseFiles">
                      <base-upload
                        v-model="tableData.purchaseFiles"
                        :onlyRead="true"
                        :actions="['preview', 'downLoad']"
                      />
                    </template>
                    <template slot="tenderParameter">
                      <base-upload
                        v-model="tableData.tenderParameter"
                        :onlyRead="true"
                        :actions="['preview', 'downLoad']"
                      />
                    </template>
                    <template slot="tenderFiles">
                      <base-upload
                        v-model="tableData.tenderFiles"
                        :onlyRead="true"
                        :actions="['preview', 'downLoad']"
                      />
                    </template>
                  </description>
                </template>
              </div>
            </div>
          </ts-form>
        </el-scrollbar>
      </el-container>
    </template>
    <template slot="footer">
      <!-- 同意 -->
      <ts-button
        type="primary"
        v-if="actionType == 'add' || actionType == 'edit'"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        提 交
      </ts-button>

      <ts-button
        class="cancel-btn"
        :disabled="submitLoading"
        @click="handleCancelModal"
      >
        {{ actionType == 'check' ? '关 闭' : '取 消' }}
      </ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/util/index.js';
import dialogServiceDetailJs from '../mixins/dialog-service-detail';

import Description from '@/components/description';
export default {
  name: 'DialogServiceDetail',
  mixins: [dialogServiceDetailJs],
  components: {
    Description
  },
  data() {
    return {
      visible: false,
      actionType: 'edit',
      submitLoading: false,

      tabList: [
        {
          label: '基本信息',
          name: 'basicInfo'
        },
        {
          label: '招标采购',
          name: 'tenderAndProcurementInfo'
        }
      ],
      activeTab: 'basicInfo',

      form: {
        services: {},
        purchaseGroup: {},
        purchaseLog: {}
      },
      tableData: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    isReadOnly() {
      return (
        this.actionType == 'check' ||
        (this.actionType == 'edit' &&
          0 == !this.form.purchaseResult.wfInstanceId)
      );
    }
  },
  methods: {
    getCalendarContainer(e) {
      return document.getElementById('SetFormTableForm');
    },
    async show(data) {
      let { id = '', type, hrefId = 'basicInfo' } = data;
      this.actionType = type;
      if (id) await this.getServiceDetail(id);
      else
        this.form = {
          services: {},
          purchaseGroup: {},
          purchaseLog: {}
        };

      this.visible = true;
      await this.$nextTick(async () => {
        await this.$refs.form?.clearValidate();
      });
      this.$nextTick(() => {
        this.$refs.rightSrcollbar.wrap.addEventListener(
          'scroll',
          this.windowScroll
        );
      });
      if (hrefId) {
        this.activeTab = hrefId;
        let index = this.tabList.findIndex(item => item.name == hrefId);
        this.tabshandleClick(index);
      }
    },
    async getServiceDetail(id) {
      await this.ajax.getServiceDetail(id).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }

        this.form = deepClone(res.object);
        this.form.purchaseLog.purchaseSpec = res.object.purchaseLog.purchaseSpec
          .split('-')
          .map(time => {
            return this.$dayjs(time).format('YYYY-MM-DD');
          });
        this.tableData = {
          ...res.object.services,
          ...res.object.purchaseLog,
          ...res.object.purchaseGroup
        };
      });
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        this.form.serviceMaintenanceLogList.forEach(item => {
          if (item.maintenanceTime) {
            item.maintenanceStartAt = item.maintenanceTime[0] || '';
            item.maintenanceEndAt = item.maintenanceTime[1] || '';
          }
        });
        let data = deepClone(this.form);
        delete data.purchaseResult;
        this.submitLoading = true;
        this.ajax.updateServiceData(data).then(res => {
          this.submitLoading = true;
          if (res.success && res.statusCode === 200) {
            this.$message.success('操作成功!');
            this.handleCancelModal();
            this.$emit('submit');
          } else {
            this.$message.error(res.message || '操作失败!');
          }
        });
      } catch (error) {
        this.submitLoading = true;
        if (typeof error === 'boolean' && !error) {
          this.$message.error('请将表单信息填写完整!');
          return 'validate fail';
        }
        throw error;
      }
    },
    handleCancelModal() {
      this.$refs.rightSrcollbar.wrap.removeEventListener(
        'scroll',
        this.windowScroll
      );
      this.visible = false;
    },
    validateInputFourDecimal(value, group, field) {
      let newVal = this.formatFourDecimal(value);
      this.$set(this.form[group], field, newVal);
    },
    formatFourDecimal(value) {
      let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,4}|\.{1})?/);
      return matchList && matchList[0];
    },
    formatTowDecimal(value) {
      let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2}|\.{1})?/);
      return matchList && matchList[0];
    },
    /**@desc 校验输入整数 */
    validateInputIntNum(value, group, index, field) {
      let matchList = value.match(/\d+/);
      let newVal = matchList && matchList[0];
      this.$set(this.form[group][index], field, newVal);
    },
    tabshandleClick(index) {
      let newIndex = Number(index);
      let blocks = document.querySelectorAll('.notes_text');
      let currentScrollTop = this.$refs.rightSrcollbar.wrap.scrollTop;
      let targetOffsetTop = blocks[newIndex].offsetTop + 24;
      let step = 44; //滚动步长
      if (currentScrollTop > targetOffsetTop) {
        const smoothUp = () => {
          if (currentScrollTop >= targetOffsetTop) {
            if (currentScrollTop - targetOffsetTop >= step) {
              currentScrollTop -= step;
              setTimeout(smoothUp, 1);
            } else {
              currentScrollTop = targetOffsetTop - step;
            }
            this.$refs.rightSrcollbar.wrap.scrollTop = currentScrollTop;
          }
        };
        smoothUp();
      } else {
        const smoothDown = () => {
          if (currentScrollTop <= targetOffsetTop) {
            // 如果和目标相差距离大于等于step 就跳 step
            if (targetOffsetTop - currentScrollTop >= step) {
              currentScrollTop += step;
              setTimeout(smoothDown, 1);
            } else {
              // 否则直接跳到目标点，防止跳过
              currentScrollTop = targetOffsetTop - step;
            }
            this.$refs.rightSrcollbar.wrap.scrollTop = currentScrollTop;
          }
        };
        smoothDown();
      }
    },
    //监听方法
    windowScroll() {
      let scrollTop = this.$refs.rightSrcollbar.wrap.scrollTop;
      let blocks = document.querySelectorAll('.notes_text');
      blocks.forEach(item => {
        if (scrollTop >= item.offsetTop - 40) {
          this.activeTab = item.id;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-content {
  height: calc(100vh - 143px);
  overflow: hidden;
}
.action-cell:not(.title) {
  cursor: pointer;
  color: $primary-blue;
  &:active {
    color: $primary-blue-active;
  }
}
.slide-nav .slide-nav-item {
  padding: 0 !important;
}
.slide-nav .slide-nav-item a {
  color: #333;
}
.required-icon {
  color: #f56c6c;
  margin-right: 4px;
}
.form-table {
  display: inline-block;
}
/deep/ {
  .flex-item .el-form-item__content {
    display: flex;
    align-items: center;
  }
  #SetFormTableForm .el-input {
    min-width: unset;
  }
  .file-ul {
    margin-bottom: 0px;
  }
  .el-scrollbar__bar.is-horizontal {
    height: 10px;
  }
  .dialog-service-detail-box .el-dialog__body {
    margin: 24px 24px 0 !important;
    width: auto !important;
  }
  .dialog-service-detail-box .el-dialog__footer {
    margin: 0 24px 0 !important;
    width: auto !important;
  }
  .el-tabs--left .el-tabs__item.is-left {
    background: transparent;
    border: 0;
    padding: 0 !important;
    width: 80px;
    text-align: left !important;
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    color: $primary-blue !important;
  }
  .el-tabs__item.is-active {
    line-height: 40px !important;
  }
  .el-tabs__active-bar {
    background-color: $primary-blue !important;
  }
  .el-tabs .el-tabs__nav {
    background: transparent;
  }
  #SetFormTableForm .el-divider__text.is-left {
    left: 0;
    padding: 0 8px;
    border-left: 3px solid $primary-blue;
  }
  .el-main {
    padding: 0 0 0 20px !important;
  }
}
</style>
