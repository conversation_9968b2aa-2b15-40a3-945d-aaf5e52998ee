import { labelList, workFlowStatusList } from './constants.js';
export default {
  data() {
    return {
      organizationList: []
    };
  },
  methods: {
    /**@desc 获取科室信息 */
    async getOrganizationList() {
      await this.ajax.getOrganizationList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let rows = res.object || [];
        this.organizationList = rows.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.name,
            element: 'ts-option'
          };
        });
      });
    },
    searchListMap(activeType) {
      let searchList = new Map([
        [
          '申报年度',
          {
            label: '申报年度',
            value: 'applyYear'
          }
        ],
        [
          '申报单位',
          {
            label: '申报单位',
            value: 'applyOrg',
            element: 'ts-select',
            elementProp: {
              clearable: true,
              filterable: true,
              placeholder: '请选择申报单位'
            },
            childNodeList: this.organizationList
          }
        ],
        [
          '名称',
          {
            label: labelList[activeType].get('名称'),
            value: 'deviceName',
            element: 'ts-input',
            elementProp: {
              placeholder: '请输入设备名称',
              clearable: true
            }
          }
        ],
        [
          '发起时间',
          {
            label: '发起时间',
            value: 'startDate',
            element: 'ts-range-picker',
            elementProp: {
              format: 'YYYY-MM-DD',
              valueFormat: 'YYYY-MM-DD'
            }
          }
        ],
        [
          '金额',
          {
            label: '申请' + labelList[activeType].get('金额'),
            value: 'amount'
          }
        ],
        [
          '状态',
          {
            label: '流程状态',
            value: 'wfStatus',
            element: 'ts-select',
            elementProp: {
              clearable: true,
              placeholder: '请选择流程状态'
            },
            childNodeList: this.wfStatusList.map(item => {
              return workFlowStatusList[item];
            })
          }
        ],
        [
          '节点',
          {
            label: '流程节点',
            value: 'wfStepId',
            element: 'ts-select',
            elementProp: {
              clearable: true,
              placeholder: '请选择流程节点'
            },
            childNodeList: []
          }
        ]
      ]);
      return searchList;
    }
  }
};
