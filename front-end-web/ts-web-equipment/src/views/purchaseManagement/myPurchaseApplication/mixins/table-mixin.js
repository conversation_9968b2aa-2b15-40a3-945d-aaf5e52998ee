import columnsMap from '../../js/column-map.js';
import { columnKeyLists } from '../../js/constants.js';
const projectType = ['equipment', 'equipment', 'construction', 'service'];
const hideColumnLists = {
  equipment: [
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '办结时间',
      '审核数量',
      '审核单价',
      '审核金额',
      '批复数量',
      '批复单价',
      '批复金额'
    ],
    [
      '申报年度',
      '申报单位',
      '流程状态',
      '当前节点',
      '办结时间',
      '审核数量',
      '审核单价',
      '审核金额',
      '批复数量',
      '批复单价',
      '批复金额'
    ],
    ['多选', '申报年度', '申报单位', '审核数量', '审核单价', '审核金额'],
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '当前节点',
      '审核数量',
      '审核单价',
      '审核金额',
      '批复数量',
      '批复单价',
      '批复金额'
    ]
  ],
  construction: [
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '办结时间',
      '审核金额',
      '批复金额'
    ],
    ['申报年度', '申报单位', '办结时间', '审核金额', '批复金额'],
    ['多选', '申报年度', '申报单位', '审核金额'],
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '当前节点',
      '审核金额',
      '批复金额'
    ]
  ],
  service: [
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '办结时间',
      '审核数量',
      '审核金额',
      '批复数量',
      '批复金额'
    ],
    [
      '申报年度',
      '申报单位',
      '办结时间',
      '审核数量',
      '审核金额',
      '批复数量',
      '批复金额'
    ],
    ['多选', '申报年度', '申报单位', '审核数量', '审核金额'],
    [
      '多选',
      '申报年度',
      '申报单位',
      '流程状态',
      '当前节点',
      '审核数量',
      '审核金额',
      '批复数量',
      '批复金额'
    ]
  ]
};

export default {
  mixins: [columnsMap],
  data() {
    return {
      statusAction: {
        0: [1],
        1: [3, 4],
        2: [1],
        3: [0, 4]
      },
      columnsList: []
    };
  },
  computed: {
    columns() {
      return this.columnsList[this.activeProjectType][this.activeStatus];
    },
    titleAction() {
      return this.activeStatus == '3' ? 'edit' : 'check';
    },
    showSummary() {
      return this.statusList[this.activeStatus].showSummary;
    }
  },
  created() {
    let columnsList = [];
    projectType.map(typeItem => {
      let typeColumnList = columnKeyLists[typeItem]; //该类型所有字段
      let typeHideColumnList = hideColumnLists[typeItem]; //该类型不同状态下隐藏的字段
      columnsList[typeItem] = [];
      typeHideColumnList.map(hideList => {
        let columns = [];
        typeColumnList.map(key => {
          var columnsMapList = this.columnsMap(typeItem);
          columnsMapList.has(key) &&
            !hideList.includes(key) &&
            columns.push(columnsMapList.get(key));
        });
        columnsList[typeItem].push(columns);
      });
    });
    this.columnsList = columnsList;
  }
};
