export const searchListMap = new Map([
  [
    '申报单位',
    {
      label: '申报单位',
      value: 'applyOrg',
      element: 'ts-select',
      elementProp: {
        clearable: true,
        filterable: true,
        placeholder: '请选择申报单位'
      },
      childNodeList: []
    }
  ],
  [
    '审核日期',
    {
      label: '审核日期',
      value: 'meetingDate',
      element: 'ts-date-picker',
      elementProp: {
        placeholder: '请选择审核日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY.MM.DD'
      }
    }
  ],
  [
    '批复日期',
    {
      label: '批复日期',
      // value: 'approvalDate',
      value: 'meetingDate',
      element: 'ts-date-picker',
      elementProp: {
        placeholder: '请选择批复日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY.MM.DD'
      }
    }
  ],
  [
    '上会日期',
    {
      label: '上会日期',
      value: 'meetingDate',
      element: 'ts-date-picker',
      elementProp: {
        placeholder: '请选择上会日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY.MM.DD'
      }
    }
  ],
  [
    '名称',
    {
      label: '设备/项目名称',
      value: 'deviceOrProjectName',
      element: 'ts-input',
      elementProp: {
        placeholder: '请输入设备/项目名称',
        clearable: true
      }
    }
  ],
  [
    '类型',
    {
      label: '类型',
      value: 'type',
      element: 'ts-select',
      childNodeList: [
        {
          element: 'ts-option',
          label: '医疗专用设备采购申请表',
          value: '医疗专用设备采购申请表'
        },
        {
          element: 'ts-option',
          label: '一般设备采购申请表',
          value: '一般设备采购申请表'
        },
        {
          element: 'ts-option',
          label: '工程项目采购申请表',
          value: '工程项目采购申请表'
        },
        {
          element: 'ts-option',
          label: '服务项目采购申请表',
          value: '服务项目采购申请表'
        }
      ],
      elementProp: {
        placeholder: '请选择类型',
        clearable: true
      }
    }
  ],
  [
    '导出状态',
    {
      label: '导出状态',
      value: 'export',
      element: 'ts-select',
      childNodeList: [
        {
          element: 'ts-option',
          label: '未导出',
          value: '0'
        },
        {
          element: 'ts-option',
          label: '已导出',
          value: '1'
        }
      ],
      elementProp: {
        placeholder: '请选择导出状态',
        clearable: true
      }
    }
  ]
]);
