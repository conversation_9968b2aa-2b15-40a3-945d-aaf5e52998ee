import { labelList } from '../../js/constants.js';
export default {
  data() {
    return {
      wfDefId: {
        '06985B07055046639923C386071BA66E': {
          name: '医疗专用设备',
          type: 'equipment'
        },
        A37FCFB6631B405EA3A790E32FE88655: {
          name: '一般设备设备',
          type: 'general-equipment'
        },
        ADA7FFF3AFBC46258C6A3D2E6A9D7DE0: {
          name: '工程项目',
          type: 'construction'
        },
        '1866B74A6BC3400F990AB9E3C2DAA1A6': {
          name: '服务项目',
          type: 'service'
        }
      },
      projectTypeData: {
        equipment: {
          field: {
            name: '',
            purchaseDate: '',
            supplier: '',
            purchaseWay: '',
            producer: '',
            purchaseBrand: '',
            purchaseSpec: '',
            commonName: '',
            cateId: '',
            purchaseNumbers: '',
            purchasePrice: '',
            purchaseTotalPrice: '',
            purchaseFiles: '',
            deviceList: []
          },
          hideColumnKeyList: [],
          columnsList: []
        },
        construction: {
          field: {
            name: '',
            purchaseDate: '',
            supplier: '',
            purchaseWay: '',
            acceptDate: '',
            purchaseSpec: '',
            purchaseTotalPrice: '',
            purchaseFiles: ''
          },
          hideColumnKeyList: ['生产厂商', '品牌', '数量', '单价'],
          columnsList: []
        },
        service: {
          field: {
            name: '',
            purchaseDate: '',
            supplier: '',
            purchaseWay: '',
            purchaseNumbers: '',
            // purchasePrice: '',
            purchaseTotalPrice: '',
            purchaseFiles: ''
          },
          hideColumnKeyList: ['生产厂商', '品牌', '单价'],
          columnsList: []
        }
      }
    };
  },
  methods: {
    columnsMap(formType) {
      return new Map([
        [
          '序号',
          {
            label: '序号',
            align: 'center',
            prop: 'pageIndex',
            minWidth: 50
          }
        ],
        [
          '名称',
          {
            label: labelList[formType].get('名称'),
            align: 'center',
            prop: 'name',
            minWidth: 180,
            formatter: (row, prop, cell) => {
              return (
                <span
                  class="action-cell"
                  onclick={() => this.handleShowDetail(row)}>
                  {cell}
                </span>
              );
            }
          }
        ],
        [
          '采购时间',
          {
            label: '采购(合同)时间',
            align: 'center',
            prop: 'purchaseDate',
            minWidth: 120
          }
        ],
        [
          '供应商',
          {
            label: labelList[formType].get('供应商'),
            prop: 'supplier',
            align: 'center',
            width: 180
          }
        ],
        [
          '采购方式',
          {
            label: '采购方式',
            prop: 'purchaseWay',
            align: 'center',
            width: 100
          }
        ],
        [
          '生产厂商',
          {
            label: '生产厂商',
            prop: 'producer',
            align: 'center',
            width: 180
          }
        ],
        [
          '品牌',
          {
            label: '品牌',
            prop: 'purchaseBrand',
            align: 'center',
            width: 100
          }
        ],
        [
          '型号',
          {
            label: labelList[formType].get('型号'),
            prop: 'purchaseSpec',
            align: 'center',
            minWidth: 140
          }
        ],
        [
          '数量',
          {
            label: labelList[formType].get('数量'),
            prop: 'purchaseNumbers',
            align: 'center',
            width: 100
          }
        ],
        [
          '单价',
          {
            label: '单价(万元)',
            prop: 'purchasePrice',
            align: 'center',
            minWidth: 100
          }
        ],
        [
          '金额',
          {
            label: '金额(万元)',
            prop: 'purchaseTotalPrice',
            align: 'center',
            minWidth: 120
          }
        ],
        // [
        //   '采购合同',
        //   {
        //     label: '采购合同',
        //     prop: 'purchaseFiles',
        //     align: 'center',
        //     minWidth: 240,
        //     formatter: (row, prop, cell) => {
        //       if (!cell || cell == null) return '';
        //       debugger;
        //       let fileDom = JSON.parse(cell).map(item => {
        //         return (
        //           <span
        //             class="action-cell"
        //             onclick={() => this.showFileDetail(item)}>
        //             {item.original_name}
        //           </span>
        //         );
        //       });
        //       return <div>{fileDom}</div>;
        //     }
        //   }
        // ],
        [
          '操作',
          {
            label: '操作',
            fixed: 'right',
            width: 100,
            prop: 'header',
            headerSlots: 'action',
            formatter: row => {
              let actions = [
                {
                  label: this.actionType == 'edit' ? '编辑' : '查看',
                  event: this.handleShowDetail
                }
              ];
              return (
                <BaseActionCell
                  actions={actions}
                  on={{ 'action-select': event => event(row) }}
                />
              );
            }
          }
        ]
      ]);
    }
  }
};
