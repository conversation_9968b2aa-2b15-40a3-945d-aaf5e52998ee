const statusList = [
    {
      label: '待维护',
      value: 0,
      element: 'ts-option'
    },
    {
      label: '己维护，待审核',
      class: 'unaudited',
      value: 1,
      element: 'ts-option'
    },
    {
      label: '审核通过',
      class: 'approved',
      value: 2,
      element: 'ts-option'
    },
    {
      label: '审核不通过',
      class: 'not-approved',
      value: 3,
      element: 'ts-option'
    },
    {
      label: '已取消采购',
      class: 'unpurchased',
      value: -1,
      element: 'ts-option'
    }
  ],
  purchaseStatusList = [
    {
      label: '否',
      value: 0,
      element: 'ts-option'
    },
    {
      label: '是',
      value: 1,
      element: 'ts-option'
    }
  ];
export default {
  computed: {
    sysRoleCode() {
      return this.$getParentStoreInfo('sysRoleCode') || '';
    },
    searchActions() {
      if (this.sysRoleCode.includes('YYCGZG'))
        return [
          {
            label: '批量上传文档',
            prop: { type: 'primary' },
            click: this.handleBatchUploadFile
          },
          {
            label: '导入',
            click: this.handleImport
          },
          {
            label: '导出',
            click: this.handleExport
          }
        ];
      else if (this.sysRoleCode.includes('WJJ_MASTER')) {
        return [
          {
            label: '批量通过',
            click: this.handleBatchApproval
          },
          {
            label: '批量不通过',
            click: this.handleBatchDisapproval
          }
        ];
      }
    }
  },
  data() {
    return {
      yearPickerVisible: false,
      searchForm: {},
      searchList: [
        {
          label: '申报单位',
          value: 'applyOrg',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            filterable: true,
            placeholder: '请选择申报单位'
          },
          childNodeList: []
        },
        {
          label: '设备/项目名称',
          value: 'deviceName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入设备/项目名称'
          }
        },
        {
          label: '类型',
          value: 'type',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择采购类型'
          },
          childNodeList: [
            {
              label: '医疗专用设备采购申请表',
              value: '医疗专用设备采购申请表',
              element: 'ts-option'
            },
            {
              label: '一般设备采购申请表',
              value: '一般设备采购申请表',
              element: 'ts-option'
            },
            {
              label: '工程项目采购申请表',
              value: '工程项目采购申请表',
              element: 'ts-option'
            },
            {
              label: '服务项目采购申请表',
              value: '服务项目采购申请表',
              element: 'ts-option'
            }
          ]
        },
        {
          label: '申报年份',
          value: 'applyYear',
          element: 'ts-date-picker',
          elementProp: {
            mode: 'year',
            valueFormat: 'YYYY'
          }
        },
        {
          label: '局务会时间',
          value: 'approvalDate',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        },
        {
          label: '状态',
          value: 'status',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: statusList
        },
        {
          label: '是否采购',
          value: 'isPurchase',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: purchaseStatusList
        }
      ],
      columns: [
        {
          type: 'checkbox',
          prop: 'checkbox',
          align: 'center',
          width: 50,
          fixed: 'left'
        },
        {
          label: '项目序号',
          prop: 'pageIndex',
          width: 75,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '申报单位',
          prop: 'applyOrg',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '类型',
          prop: 'type',
          align: 'center',
          minWidth: 130,
          fixed: 'left',
          render: (h, scope) => {
            return h('span', {}, scope.row.type.replace(/申请表/g, ''));
          }
        },
        {
          label: '设备/项目名称',
          prop: 'deviceName',
          minWidth: 245
        },
        {
          label: '申报时间',
          prop: 'applyDate',
          align: 'center',
          width: 100
        },
        {
          label: '规格型号/服务期限',
          prop: 'spec',
          align: 'center',
          width: 170
        },
        {
          label: '批复数量',
          prop: 'applyNumbers',
          align: 'center',
          minWidth: 120
        },
        {
          label: '批复单价(万元)',
          prop: 'applyPrice',
          width: 120,
          align: 'center',
          render: (h, { row }) => {
            return <span>{row.applyPrice ? Number(row.applyPrice) : ''}</span>;
          }
        },
        {
          label: '批复金额(万元)',
          prop: 'applyTotalPrice',
          align: 'center',
          minWidth: 120,
          render: (h, { row }) => {
            return (
              <span>
                {row.applyTotalPrice ? Number(row.applyTotalPrice) : ''}
              </span>
            );
          }
        },
        {
          label: '局务会议时间',
          prop: 'approvalDate',
          align: 'center',
          width: 105,
          render: (h, scope) => {
            return h(
              'span',
              {},
              scope.row.approvalDate
                ? this.$dayjs(scope.row.approvalDate).format('YYYY-MM-DD')
                : ''
            );
          }
        },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          minWidth: 110,
          render: (h, scope) => {
            let status =
              statusList.find(item => item.value == scope.row.status) || {};
            return h(
              'span',
              {
                class: status.class
              },
              status.label || ''
            );
          }
        },
        {
          label: '设备、项目、服务名称',
          prop: 'purchaseName',
          align: 'center',
          width: 180
        },
        {
          label: '采购(合同)时间',
          prop: 'purchaseDate',
          align: 'center',
          width: 120
        },
        {
          label: '供应商/承建商/服务商',
          prop: 'supplier',
          align: 'center',
          width: 180
        },
        // {
        //   label: '生产厂商',
        //   prop: 'producer',
        //   align: 'center',
        //   width: 180
        // },
        {
          label: '品牌',
          prop: 'purchaseBrand',
          align: 'center',
          width: 180
        },
        {
          label: '规格型号/服务期限',
          prop: 'purchaseSpec',
          align: 'center',
          width: 180
        },
        {
          label: '数量',
          prop: 'purchaseNumbers',
          align: 'center',
          width: 80
        },
        {
          label: '单价(万元)',
          prop: 'purchasePrice',
          align: 'center',
          width: 120
        },
        {
          label: '金额(万元)',
          prop: 'purchaseTotalPrice',
          align: 'center',
          width: 120
        },
        {
          label: '备注',
          prop: 'purchaseRemark',
          align: 'center',
          width: 180
        },
        {
          label: '操作',
          prop: 'header',
          align: 'center',
          width: 170,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, scope) => {
            let actions = [];
            if (this.sysRoleCode.includes('YYCGZG') || this.isAdmin === 'Y') {
              if (scope.row.status == 0) {
                actions = [
                  {
                    label: '维护信息',
                    event: 'edit'
                  },
                  {
                    label: '取消采购',
                    event: 'cancel'
                  }
                ];
              } else if (scope.row.status != -1) {
                actions = [
                  {
                    label: '更新信息',
                    event: 'edit'
                  },
                  {
                    label: '查看',
                    event: 'check'
                  }
                ];
                let approvalDate = this.$dayjs(scope.row.approvalDate),
                  comparisonDate = this.$dayjs('2022-01-01');
                if (approvalDate.isBefore(comparisonDate)) {
                  actions.push({
                    label: '取消采购',
                    event: 'cancel'
                  });
                }
              }
            } else {
              if (
                scope.row.status == 1 &&
                this.sysRoleCode.includes('WJJ_MASTER')
              ) {
                actions.push({
                  label: '资料审核',
                  event: 'approval'
                });
              }
              if (scope.row.status != 0 && scope.row.status != -1) {
                actions.push({
                  label: '查看',
                  event: 'check'
                });
              }
            }
            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => this.action(e, scope.row)
                },
                attrs: {
                  actions: actions
                }
              },
              this.$slots.default
            );
          }
        }
      ]
    };
  },
  created() {
    this.getOrganizationList();
  },
  methods: {
    handleYearPickerOpenChange(status) {
      this.yearPickerVisible = status;
    },
    handleYearPickerChange(value) {
      this.searchForm.applyYear = this.$dayjs(value).format('YYYY');
      this.yearPickerVisible = false;
    },
    getOrganizationList() {
      this.ajax.getOrganizationList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let rows = res.object || [];
        let packageList = rows.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.name,
            element: 'ts-option'
          };
        });
        this.searchList.find(
          item => item.value === 'applyOrg'
        ).childNodeList = packageList;
      });
    }
  }
};
