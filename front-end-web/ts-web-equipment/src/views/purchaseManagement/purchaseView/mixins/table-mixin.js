import columnsMap from '../../js/column-map.js';
import { columnKeyLists } from '../../js/constants.js';
const projectType = ['equipment', 'equipment', 'construction', 'service'];

const hideColumnLists = {
  equipment: [
    ['多选', '办结时间', '批复数量', '批复单价', '批复金额'],
    ['多选', '审核数量', '审核单价', '审核金额']
  ],
  construction: [
    ['多选', '办结时间', '批复金额'],
    ['多选', '审核数量', '审核金额']
  ],
  service: [
    ['多选', '办结时间', '批复金额'],
    ['多选', '审核数量', '审核金额']
  ]
};

export default {
  mixins: [columnsMap],
  data() {
    return {
      columnsList: [],
      statusAction: {
        0: [5, 4],
        1: [6, 4]
      },
      titleAction: 'check'
    };
  },
  computed: {
    columns() {
      return this.columnsList[this.activeProjectType][this.activeStatus];
    }
  },
  created() {
    let columnsList = [];
    projectType.map(typeItem => {
      let typeColumnList = columnKeyLists[typeItem]; //该类型所有字段
      let typeHideColumnList = hideColumnLists[typeItem]; //该类型不同状态下隐藏的字段
      columnsList[typeItem] = [];
      typeHideColumnList.map((hideList, index) => {
        let columns = [];
        typeColumnList.map(key => {
          var columnsMapList = this.columnsMap(typeItem);
          columnsMapList.has(key) &&
            !hideList.includes(key) &&
            columns.push(columnsMapList.get(key));
        });
        columnsList[typeItem].push(columns);
      });
    });
    this.columnsList = columnsList;
  }
};
