<template>
  <div class="table-box">
    <div
      :style="{
        height:
          editTableData.length < 4
            ? '160px'
            : editTableData.length * 32 + 42 + 'px'
      }"
    >
      <vxe-table
        id="equipmentTable"
        ref="tsVxeTable"
        height="auto"
        border
        stripe
        keep-source
        show-overflow
        :column-config="{
          resizable: true
        }"
        :row-config="{
          keyField: 'ID'
        }"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
        :edit-rules="validRules"
        :data="editTableData"
      >
        <vxe-column
          type="seq"
          title="序号"
          width="60"
          align="center"
          fixed="left"
        ></vxe-column>
        <vxe-column
          field="applyYear"
          title="申报年度"
          width="75"
          align="center"
        ></vxe-column>
        <vxe-column
          field="orgName"
          title="申报单位"
          width="160"
          align="center"
        ></vxe-column>
        <vxe-column
          field="xmmc"
          title="服务名称"
          width="200"
          align="center"
          :edit-render="editRole('xmmc', { placeholder: '请输入服务名称' })"
        ></vxe-column>
        <vxe-column
          field="fwStartAndEnd"
          title="服务起止时间"
          width="220"
          align="center"
        >
          <template
            #header="{ column }"
            v-if="actionType == 'agree' && judgeIsEdit('fwkssj')"
          >
            <span>
              <i class="vxe-cell--required-icon"></i>
              <i class="vxe-cell--edit-icon vxe-icon-edit"></i>
              <span>{{ column.title }}</span>
            </span>
          </template>
          <template #default="{ row }">
            <ts-range-picker
              v-if="actionType == 'agree' && judgeIsEdit('fwkssj')"
              style="width: 100%;"
              class="range-picker"
              v-model="row.fwStartAndEnd"
              type="daterange"
              valueFormat="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :getCalendarContainer="getCalendarContainer"
            />
            <span class="djis" v-else>{{ row.fwkssj + '~' + row.fwjssj }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="sl"
          title="服务年份(年)"
          width="110"
          align="center"
        ></vxe-column>
        <vxe-column
          field="zje"
          title="总金额(万元)"
          width="120"
          align="center"
        ></vxe-column>
        <vxe-column
          field="sbly"
          title="申报理由"
          width="200"
          align="center"
        ></vxe-column>
        <vxe-column
          field="zjly"
          title="资金来源"
          width="120"
          align="center"
        ></vxe-column>

        <vxe-column field="sqbg" title="申请报告" width="240" align="center">
          <template #default="{row}">
            <template v-if="row.sqbg">
              <div
                v-for="(item, index) in JSON.parse(row.sqbg)"
                :key="item.id + '_sqbg_' + index"
                class="action-cell"
                @click="handleShowFile(item)"
              >
                {{ item.original_name }}
              </div>
            </template>
          </template>
        </vxe-column>
        <vxe-column
          field="kxxlzbg"
          title="可行性论证报告"
          width="240"
          align="center"
        >
          <template #default="{row}">
            <template v-if="row.kxxlzbg">
              <div
                v-for="(item, index) in JSON.parse(row.kxxlzbg)"
                :key="item.id + '_kxxlzbg_' + index"
                class="action-cell"
                @click="handleShowFile(item)"
              >
                {{ item.original_name }}
              </div>
            </template>
          </template>
        </vxe-column>
        <vxe-column
          field="jtyjhyjl"
          title="集体研究会议记录"
          width="240"
          align="center"
        >
          <template #default="{row}">
            <template v-if="row.jtyjhyjl">
              <div
                v-for="(item, index) in JSON.parse(row.jtyjhyjl)"
                :key="item.id + '_jtyjhyjl_' + index"
                class="action-cell"
                @click="handleShowFile(item)"
              >
                {{ item.original_name }}
              </div></template
            >
          </template>
        </vxe-column>
        <vxe-column
          field="zdhtljl"
          title="职代会讨论记录"
          width="240"
          align="center"
        >
          <template #default="{row}">
            <template v-if="row.zdhtljl">
              <div
                v-for="(item, index) in JSON.parse(row.zdhtljl)"
                :key="item.id + '_zdhtljl_' + index"
                class="action-cell"
                @click="handleShowFile(item)"
              >
                {{ item.original_name }}
              </div></template
            >
          </template>
        </vxe-column>
        <vxe-column field="bz" title="备注" width="200"></vxe-column>
        <vxe-column
          field="shsl"
          title="审核数量"
          width="105"
          align="center"
          :fixed="judgeIsEdit('shsl') ? 'right' : ''"
          :edit-render="
            editRole(
              'shsl',
              {
                placeholder: '请输入审核数量',
                type: 'number'
              },
              {
                input: ({ row, column }) => {
                  row[column.field] = validateInputIntNum(row[column.field]);
                }
              }
            )
          "
        >
        </vxe-column>
        <vxe-column
          field="shje"
          title="审核金额(万元)"
          width="145"
          align="center"
          :fixed="judgeIsEdit('shje') ? 'right' : ''"
          :edit-render="
            editRole(
              'shje',
              {
                placeholder: '请输入审核金额',
                type: 'number'
              },
              {
                input: ({ row, column }) => {
                  row[column.field] = validateInputFourDecimal(
                    row[column.field]
                  );
                }
              }
            )
          "
        >
        </vxe-column>
        <vxe-column
          field="pfsl"
          title="批复数量"
          width="105"
          align="center"
          fixed="right"
          :visible="judgeIsEdit('pfsl') && actionType == 'agree'"
          :edit-render="
            editRole(
              'pfsl',
              {
                placeholder: '请输入批复数量',
                type: 'number'
              },
              {
                input: ({ row, column }) => {
                  row[column.field] = validateInputIntNum(row[column.field]);
                }
              }
            )
          "
        >
        </vxe-column>
        <vxe-column
          field="pfje"
          title="批复金额(万元)"
          width="145"
          align="center"
          fixed="right"
          :visible="judgeIsEdit('pfje') && actionType == 'agree'"
          :edit-render="
            editRole(
              'pfje',
              {
                placeholder: '请输入批复金额',
                type: 'number'
              },
              {
                input: ({ row, column }) => {
                  row[column.field] = validateInputFourDecimal(
                    row[column.field]
                  );
                }
              }
            )
          "
        ></vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
import dialogTable from '../mixins/dialog-table';

export default {
  name: 'constructionTable',
  mixins: [dialogTable],
  data() {
    return {};
  }
};
</script>
<style lang="scss" scoped>
.table-box {
  overflow: hidden;
  transform: scale(1);
}

::v-deep {
  .action-cell {
    cursor: pointer;
    color: rgb(82, 96, 255);
  }
  .range-picker .ant-input {
    border: 0;
  }
  .ant-calendar-picker-clear {
    right: 0;
  }
}
</style>
