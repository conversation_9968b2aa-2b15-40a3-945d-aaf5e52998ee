<template>
  <div class="classification-dictionary-box">
    <div class="left">
      <base-search-tree
        :spSearch="true"
        ref="searchTree"
        placeholder="输入名称进行搜索"
        :apiFunction="apiFunction"
        :isAll="true"
        @tree="getTreeSuccess"
        @beforeClick="clickItemTree"
      />
    </div>

    <div class="right">
      <ts-vxe-base-table
        id="table_classification_dictionary"
        ref="table"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      curTreeNode: {
        id: '',
        name: ''
      },
      apiFunction: this.ajax.getClassificationTree,
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '分类代码',
          align: 'center',
          width: 110,
          prop: 'code'
        },
        {
          label: '分类名称',
          align: 'center',
          prop: 'name',
          minWidth: 200
        },
        {
          label: '分类描述',
          align: 'center',
          prop: 'elx',
          minWidth: 200
        },
        {
          label: '预期用途',
          align: 'center',
          prop: 'useTo',
          minWidth: 200
        },
        {
          label: '品名举例',
          align: 'center',
          prop: 'skuEx',
          minWidth: 200
        }
      ]
    };
  },
  methods: {
    // 树加载成功
    getTreeSuccess(data, id, name) {
      this.curTreeNode.id = id;
      this.curTreeNode.name = name;
      this.handleRefreshTable();
    },

    // 树 item点击
    clickItemTree(node) {
      this.$refs.table.pageNo = 1;
      this.curTreeNode.id = node.id;
      this.curTreeNode.name = node.name;
      this.handleRefreshTable();
    },

    handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let data = {
        pageNo,
        pageSize,
        ...this.searchForm,
        ...this.curTreeNode,
        id: this.curTreeNode.id
      };
      this.ajax.getClassification(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.classification-dictionary-box {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  padding: 8px;
  background: #fff;
}
.left {
  width: 326px;
  height: 100%;
  margin-right: 8px;
  border-radius: 8px;
  overflow: hidden;
  border: 0.5px solid #295cf9;

  ::v-deep {
    .search-tree-box {
      border-radius: 8px;
      width: 100%;
    }
  }
}
.right {
  flex: 1;
  height: 100%;
  padding: 8px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  border: 0.5px solid #295cf9;
}
</style>
