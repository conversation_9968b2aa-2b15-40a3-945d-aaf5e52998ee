<template>
  <div class="budget-usage-statistics-box">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
    >
    </ts-search-bar>
    <base-table
      ref="table"
      class="form-table"
      border
      stripe
      v-loading="loading"
      :summary-method="handleSummary"
      show-summary
      @refresh="handleRefreshTable"
    >
      <ts-table-column
        label="序号"
        width="48"
        prop="pageIndex"
        align="center"
        fixed="left"
      ></ts-table-column>
      <ts-table-column
        label="预算单位"
        min-width="140"
        prop="deptName"
        align="center"
        fixed="left"
      >
      </ts-table-column>
      <ts-table-column
        label="年份"
        width="60"
        prop="year"
        align="center"
        fixed="left"
      >
      </ts-table-column>
      <ts-table-column label="总预算" class-name="column-total">
        <ts-table-column
          label="总预算(万元)"
          prop="totalQuota"
          width="105"
          align="center"
          class-name="column-total"
        >
        </ts-table-column>
        <ts-table-column
          label="使用进度"
          prop="totalRate"
          width="75"
          align="center"
          class-name="column-total"
        >
          <template v-slot:default="{ row }">
            <span class="action-cell" @click="handleShowDetail(row, 1)">
              {{ Number(row.totalRate) ? Number(row.totalRate) + '%' : 0 }}
            </span>
          </template>
        </ts-table-column>
        <ts-table-column
          label="已用金额(万元)"
          prop="totalQuotaUse"
          width="115"
          align="center"
          class-name="column-total"
        >
        </ts-table-column>
        <ts-table-column
          label="剩余(万元)"
          prop="totalQuotaLeft"
          width="105"
          align="center"
          class-name="column-total"
        >
        </ts-table-column>
      </ts-table-column>
      <ts-table-column label="资产类项目预算" class-name="column-project">
        <ts-table-column
          label="预算额(万元)"
          prop="projectQuota"
          width="105"
          align="center"
          class-name="column-project"
        >
        </ts-table-column>
        <ts-table-column
          label="使用进度"
          prop="projectRate"
          width="75"
          align="center"
          class-name="column-project"
        >
          <template v-slot:default="{ row }">
            <span class="action-cell" @click="handleShowDetail(row, 2)">
              {{ Number(row.projectRate) ? Number(row.projectRate) + '%' : 0 }}
            </span>
          </template>
        </ts-table-column>
        <ts-table-column
          label="已用金额(万元)"
          prop="projectQuotaUse"
          width="115"
          align="center"
          class-name="column-project"
        >
        </ts-table-column>
        <ts-table-column
          label="剩余(万元)"
          prop="projectQuotaLeft"
          width="105"
          align="center"
          class-name="column-project"
        >
        </ts-table-column>
      </ts-table-column>
      <ts-table-column label="费用类项目预算" class-name="column-service">
        <ts-table-column
          label="预算额(万元)"
          prop="serviceQuota"
          width="105"
          align="center"
          class-name="column-service"
        >
        </ts-table-column>
        <ts-table-column
          label="使用进度"
          prop="serviceRate"
          width="75"
          align="center"
          class-name="column-service"
        >
          <template v-slot:default="{ row }">
            <span class="action-cell" @click="handleShowDetail(row, 3)">
              {{ Number(row.serviceRate) ? Number(row.serviceRate) + '%' : 0 }}
            </span>
          </template>
        </ts-table-column>
        <ts-table-column
          label="已用金额(万元)"
          prop="serviceQuotaUse"
          width="115"
          align="center"
          class-name="column-service"
        >
        </ts-table-column>
        <ts-table-column
          label="剩余(万元)"
          prop="serviceQuotaLeft"
          width="105"
          align="center"
          class-name="column-service"
        >
        </ts-table-column>
      </ts-table-column>
    </base-table>
    <DialogBudgetUsageDetail ref="dialogDetail" />
  </div>
</template>

<script>
import DialogBudgetUsageDetail from './components/dialog-budget-usage-detail';
export default {
  components: { DialogBudgetUsageDetail },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '年份',
          value: 'year',
          element: 'el-date-picker',
          elementProp: {
            type: 'year',
            format: 'yyyy年',
            valueFormat: 'yyyy',
            clearable: true,
            placeholder: '请选择年份'
          }
        },
        {
          label: '单位',
          value: 'deptName',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            filterable: true,
            placeholder: '请选择单位'
          },
          childNodeList: []
        }
      ],
      loading: false
    };
  },
  created() {
    this.getOrganizationList();
  },
  methods: {
    getOrganizationList() {
      this.ajax.getOrganizationList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let rows = res.object || [];
        let packageList = rows.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.name,
            element: 'ts-option'
          };
        });
        this.searchList.find(
          item => item.value === 'deptName'
        ).childNodeList = packageList;
      });
    },
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      if (this.$store.state.common.userInfo.orgCode != '20888576') {
        data.deptId = this.$store.state.common.userInfo.orgCode;
      }
      this.ajax.getBudgetStatistics(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleShowDetail(row, type) {
      this.$refs.dialogDetail.show(row, type);
    },

    handleSummary(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((col, index) => {
        if (index === 0) {
          sums[index] = '合计';
        } else if (index === 1 || index === 2) {
          sums[index] = '';
        } else {
          if (index === 4) {
            sums[index] = this.handleQuotData(
              data,
              'totalQuota',
              'totalQuotaUse'
            );
          } else if (index === 8) {
            sums[index] = this.handleQuotData(
              data,
              'projectQuota',
              'projectQuotaUse'
            );
          } else if (index === 12) {
            sums[index] = this.handleQuotData(
              data,
              'serviceQuota',
              'serviceQuotaUse'
            );
          } else {
            let values = data.map(item => Number(item[col.property])),
              sum = values.reduce((prev, curr) => {
                let value = Number(curr);
                if (!isNaN(value)) {
                  return prev + curr * 100;
                } else {
                  return prev;
                }
              }, 0);
            sums[index] = Number((sum / 100).toFixed(2));
          }
        }
      });
      return sums;
    },
    handleQuotData(data, dividend, divisor) {
      let total = data.map(item => Number(item[dividend])),
        totalUse = data.map(item => Number(item[divisor])),
        totalSum = total.reduce((prev, curr) => {
          let value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr * 100;
          } else {
            return prev;
          }
        }, 0),
        totalUseSum = totalUse.reduce((prev, curr) => {
          let value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr * 100;
          } else {
            return prev;
          }
        }, 0);
      return totalUseSum
        ? Number(((totalUseSum / totalSum) * 100).toFixed(2)) + '%'
        : 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.budget-usage-statistics-box {
  height: 100%;
  background: #fff;
  padding: 8px;
  display: flex;
  flex-direction: column;
}
.form-table {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
.action-cell {
  cursor: pointer;
  color: $primary-blue;
  &:active {
    color: $primary-blue-active;
  }
}
/deep/ {
  .column-project .cell {
    background: #f3f0ff !important;
  }
  .column-total .cell {
    background: #f0f9eb !important;
  }
  .column-service .cell {
    background: #fdf5e6 !important;
  }
  .el-scrollbar__bar.is-horizontal {
    height: 10px;
  }
}
</style>
