<template>
  <vxe-modal
    className="dialog-asset-tag-print"
    title="普通资产卡片打印"
    v-model="visible"
    fullscreen
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <div id="print-area" class="print-item" v-if="visible">
          <asset-tag-tpl
            v-for="(item, index) in list"
            :key="index"
            :tpl-data="item"
            :tpl-id="tplId"
          />
        </div>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="handlePrint">打印</ts-button>

        <ts-button class="shallowButton" @click="close">
          关 闭
        </ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import AssetTagTpl from '../../../components/asset-tag-tpl/index.vue';
import printJS from 'print-js';

export default {
  components: {
    AssetTagTpl
  },
  data() {
    return {
      visible: false,
      list: [],
      tplId: '1'
    };
  },
  methods: {
    async show({ list = [] }) {
      await this.getSettingValue();

      this.list = list;
      this.visible = true;
    },
    async getSettingValue() {
      const res = await this.ajax.getAssetTagTpl();
      if (res.object) {
        this.tplId = res.object;
      }
    },
    close() {
      this.list = [];
      this.visible = false;
      this.$emit('close');
    },
    handlePrint() {
      printJS({
        printable: 'print-area',
        type: 'html',
        scanStyles: false,
        style: `
        @media print {
        @page {
          margin: 0; /* 移除页眉和页脚的默认间距 */
        }
        body {
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center; /* 水平居中 */
          text-align: center;
        }
        .asset-tag-tpl-container {
            margin-top: 0.20cm;
            background-color: #f2f2f2;
            page-break-after: auto;
            page-break-before: always;
        }
        #print-area {
          display: inline-block; /* 确保内容按需自适应 */
          margin-top: 0; /* 确保内容从顶部开始 */
        }
        table {
          width: 6.85cm;
          height: 4.73cm !important;
          table-layout: fixed;
          border-collapse: collapse;
        }
        .key {
          min-width: 1.54cm;
          text-align: center;
        }
        .v1 {
          white-space: nowrap;
          max-width: 3.18cm;
          overflow: hidden;
        }
        .v2 {
          white-space: nowrap;
          max-width: 4.93cm;
          overflow: hidden;
        }
        th,
        tr,
        td {
          height: 0.53cm;
          border: 1px solid #000;
          text-align: left;
          font-size: 0.30cm;
        }
        th {
          background-color: #f2f2f2;
        }
        .qrcode {
          text-align: center;
          img {
            width: 100% !important;
            max-width: 100% !important;
            object-fit: cover;
          }
        }
      }
    `,
        documentTitle: '资产卡片打印',
        onPrintDialogClose: () => {
          let ids = this.list.map(item => item.id);
          this.ajax.printEquipmentLedger('normal', ids);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-asset-tag-print {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        background: #f2f2f2;
        > .vxe-modal--content {
          padding: 0 !important;
          > .content {
            width: 1280px !important;
            height: calc(100% - 48px) !important;

            margin: 24px auto 0px;
            padding: 16px 24px;
            display: flex;
            flex-direction: column;
            background: #fff;
            overflow: auto;
            .print-item {
              width: fit-content;

              .asset-tag-tpl-container {
                margin-bottom: 30px;
              }
            }

            #print-area {
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }
  }
}

@media print {
  /* 隐藏页面中不需要打印的内容 */
  /* 仅显示特定区域 */
  #print-area {
    display: block;
  }
}
</style>
