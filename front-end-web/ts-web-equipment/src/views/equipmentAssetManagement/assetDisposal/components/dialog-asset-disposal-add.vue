<template>
  <vxe-modal
    className="dialog-asset-disposal-add"
    width="95%"
    height="95%"
    :title="title"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <ts-form ref="form" :model="form">
        <div class="form-tips-item">基本信息</div>
        <ts-form-item
          label="资产类别"
          prop="disposal.skuType"
          :rules="rules.required"
        >
          <ts-radio-group
            v-model="form.disposal.skuType"
            @change="handleChangeSkuType"
          >
            <ts-radio
              :disabled="isEdit"
              v-for="item in skuTypeList"
              :key="item.itemNameValue"
              :label="item.itemNameValue"
            >
              {{ item.itemName }}
            </ts-radio>
          </ts-radio-group>
        </ts-form-item>

        <ts-form-item label="处置原因">
          <ts-input
            v-model="form.disposal.note"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
            placeholder="请输入处置原因"
          />
        </ts-form-item>

        <div class="form-tips-item">明细信息</div>
        <div class="table-operate">
          <ts-button
            @click="handleSelectAssets"
            class="shallowButton"
            type="primary"
          >
            选择资产
          </ts-button>
          <ts-button
            @click="handleBatchDelete"
            class="shallowButton"
            type="primary"
          >
            批量删除
          </ts-button>
          <ts-button
            @click="handleBatchSetDisposalInfo"
            class="shallowButton more-text-btn"
            type="primary"
          >
            批量设置处置信息
          </ts-button>
        </div>

        <form-table
          ref="FormTable"
          class="form-table"
          :formData="form"
          operateDataKey="disposalDetailList"
          hideOperation
          :columns="columns"
        >
          <template v-slot:name="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:statusShow="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div
                :style="{
                  color: equipmentStatus.find(
                    item => item.value == row['status']
                  )?.color
                }"
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <template v-slot:model="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:gzwbh="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:assetCode="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:belongToOrgName="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:type="{ row, index, column }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`disposalDetailList.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-select
                class="min-width-select"
                v-model="row[column.property]"
                placeholder="请选择"
                clearable
              >
                <ts-option
                  v-for="item in amsDisposalType"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </ts-select>
            </ts-form-item>
          </template>

          <template v-slot:cost="{ row, index, column }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`disposalDetailList.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                v-model="row[column.property]"
                class="number-input right-aligned-input"
                placeholder="请输入"
                @input="validateInputDouble($event, row, column.property)"
              />
            </ts-form-item>
          </template>

          <template v-slot:revenue="{ row, index, column }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`disposalDetailList.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                v-model="row[column.property]"
                class="number-input right-aligned-input"
                placeholder="请输入"
                @input="validateInputDouble($event, row, column.property)"
              />
            </ts-form-item>
          </template>

          <template v-slot:useDate="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:lifespanVal="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}年</div>
            </ts-form-item>
          </template>
          <template v-slot:zysc="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:originalVal="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:totalDecVal="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>
          <template v-slot:bookVal="{ row, column }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div class="tac100w">{{ row[column.property] }}</div>
            </ts-form-item>
          </template>

          <template v-slot:operate="{ row, index }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div
                class="tac100w delete-device"
                @click="() => handleDeleteDeviceRow(index)"
              >
                删除
              </div>
            </ts-form-item>
          </template>
        </form-table>
      </ts-form>

      <dialog-multiple-device
        renderType="5"
        :otherSearchParams="{
          skuType: form.disposal.skuType
        }"
        ref="DialogMultipleDevice"
        @submit="handleSelectDeviceEnd"
      />

      <dialog-batch-set-disposal-info
        :amsDisposalType="amsDisposalType"
        ref="DialogBatchSetDisposalInfo"
        @submit="handleBatchSetSuccess"
      />
    </template>

    <template #footer>
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone, inputTowDecimalPlaces } from '@/unit/commonHandle.js';
import { equipmentStatus } from '@/assets/js/constants.js';
import FormTable from '@/components/form-table.vue';
import DialogMultipleDevice from '@/views/inventoryManagement/components/dialog-multiple-device.vue';
import DialogBatchSetDisposalInfo from './dialog-batch-set-disposal-info.vue';
export default {
  components: { DialogMultipleDevice, DialogBatchSetDisposalInfo, FormTable },
  watch: {},
  data() {
    return {
      visible: false,
      equipmentStatus,
      actionType: 'add',
      skuTypeList: [],
      amsDisposalType: [],

      columns: [
        {
          prop: 'name',
          label: '资产名称',
          align: 'center',
          minWidth: 140
        },
        {
          prop: 'statusShow',
          label: '设备状态',
          align: 'center',
          width: 100
        },
        {
          prop: 'model',
          label: '规格型号',
          align: 'center',
          width: 120
        },
        {
          prop: 'gzwbh',
          label: '国有资产编码',
          align: 'center',
          width: 110
        },
        {
          prop: 'assetCode',
          label: '资产编码',
          align: 'center',
          width: 100
        },
        {
          prop: 'belongToOrgName',
          label: '原所属科室',
          align: 'center',
          width: 130
        },
        {
          prop: 'type',
          label: '处置类型',
          align: 'center',
          width: 120
        },
        {
          prop: 'cost',
          label: '处置费用(元)',
          align: 'center',
          width: 120
        },
        {
          prop: 'revenue',
          label: '处置收入(元)',
          align: 'center',
          width: 120
        },
        {
          prop: 'useDate',
          label: '启用日期',
          align: 'center',
          width: 115
        },
        {
          prop: 'lifespanVal',
          label: '预计使用时长',
          align: 'center',
          width: 110
        },
        {
          prop: 'zysc',
          label: '在役时间',
          align: 'center',
          width: 130
        },
        {
          prop: 'originalVal',
          label: '原值(元)',
          align: 'center',
          width: 130
        },
        {
          prop: 'totalDecVal',
          label: '累计折旧(元)',
          align: 'center',
          width: 130
        },
        {
          prop: 'bookVal',
          label: '净值(元)',
          align: 'center',
          width: 130
        },
        {
          prop: 'operate',
          label: '操作',
          width: 80
        }
      ],

      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    isEdit() {
      return this.actionType === 'edit';
    },
    title() {
      return this.isEdit ? '编辑处置记录' : '新增处置记录';
    }
  },
  methods: {
    async show({ type = 'add', data = {} }) {
      this.getSkuTypeList();
      this.getTypeDicAMS_DISPOSAL_TYPE();

      this.actionType = type;
      this.$set(this, 'form', deepClone(data));

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    handleChangeSkuType() {
      this.form.disposalDetailList = [];
    },

    // 选择资产
    handleSelectAssets() {
      if (!this.form.disposal.skuType) {
        this.$newMessage('warning', '请先选择资产类别!');
        this.$refs.form.validateField('disposal.skuType');
        return;
      }

      let ignoreId = this.form.disposalDetailList
        .map(m => m.deviceId)
        .join(',');
      this.$refs.DialogMultipleDevice.show({
        ignoreId
      });
    },

    // 选择资产结束
    handleSelectDeviceEnd(list = []) {
      if (list.length === 0) {
        return;
      }
      list.map(f => {
        f.deviceId = f.id;
      });
      this.form.disposalDetailList.push(...list);
    },

    // 批量删除资产
    async handleBatchDelete() {
      if (this.form.disposalDetailList.length === 0) {
        this.$newMessage('warning', '暂无操作数据, 请添加处置资产!');
        return false;
      }
      if (this.$refs.FormTable.selectList.length === 0) {
        this.$newMessage('warning', '请勾选需要操作的处置资产数据!');
        return false;
      }

      try {
        await this.$newConfirm(
          `您是否确认【<span style="color: red">删除</span>】当前数据？`
        );

        let selectActiveIds = this.$refs.FormTable.selectList.map(i => i.id);
        this.form.disposalDetailList = this.form.disposalDetailList.filter(
          f => !selectActiveIds.includes(f.id)
        );
      } catch (e) {
        console.error(e);
      }
    },

    // 删除单个资产
    async handleDeleteDeviceRow(index) {
      try {
        await this.$newConfirm(
          `您是否确认【<span style="color: red">删除</span>】当前数据？`
        );
        this.form.disposalDetailList.splice(index, 1);
      } catch (e) {
        console.error(e);
      }
    },

    // 批量设置处置信息
    handleBatchSetDisposalInfo() {
      if (this.form.disposalDetailList.length === 0) {
        this.$newMessage('warning', '暂无操作数据, 请添加处置资产!');
        return false;
      }
      this.$refs.DialogBatchSetDisposalInfo.show();
    },

    // 批量设置处置信息成功
    handleBatchSetSuccess(data) {
      let update = (row, key, value) => this.$set(row, key, value);

      if (data.fill === '0') {
        this.form.disposalDetailList.forEach(row => {
          update(row, 'type', data.type);
          update(row, 'cost', data.cost);
          update(row, 'revenue', data.revenue);
        });
      } else {
        this.form.disposalDetailList.forEach(row => {
          if (!row.type) update(row, 'type', data.type);
          if (!row.cost) update(row, 'cost', data.cost);
          if (!row.revenue) update(row, 'revenue', data.revenue);
        });
      }
      this.$forceUpdate();
    },

    async getSkuTypeList() {
      let res = await this.ajax.getSkuTypeList('');
      if (!res.success) {
        this.$newMessage('error', res.message || '资产类别数据获取失败!');
        return;
      }
      this.skuTypeList = res.object || [];
    },

    getTypeDicAMS_DISPOSAL_TYPE() {
      this.ajax.getDataByDataLibrary('AMS_DISPOSAL_TYPE').then(res => {
        if (!res.success) {
          this.$newMessage('error', '字典数据获取失败!');
          return false;
        }

        this.amsDisposalType = (res.object || []).map(item => ({
          element: 'ts-option',
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },

    /**@desc 校验输入两位小数 */
    validateInputDouble(value, obj, name) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(obj, name, newVal);
    },

    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        data.disposalDetailList = data.disposalDetailList.map(m => {
          return {
            deviceId: m.deviceId,
            type: m.type,
            cost: m.cost,
            revenue: m.revenue,
            isDeleted: m.isDeleted
          };
        });

        let typeTitle = this.isEdit ? '编辑' : '新增';
        await this.ajax[
          this.isEdit ? 'updateEquipmentDisposal' : 'addEquipmentDisposal'
        ](data).then(res => {
          this.submitLoading = false;

          if (res.success && res.statusCode === 200) {
            this.$newMessage('success', res.message || typeTitle + '成功!');
            this.$emit('refresh');
            this.close();
          } else {
            this.$newMessage('error', res.message || typeTitle + '失败!');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-asset-disposal-add {
  .form-tips-item {
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 18px;
      border-radius: 12px;
      background-color: $primary-blue;
      color: $primary-blue;
      margin-right: 4px;
    }
  }

  ::v-deep {
    .flex-item {
      .el-form-item__content {
        display: flex;
        align-items: center;
        width: 100%;
        > div {
          width: 100%;
        }
      }
    }

    .form-item-label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .req {
        color: rgb(245, 108, 108);
        margin-right: 2px;
      }
    }

    .more-text-btn {
      width: 140px;
      > span {
        max-width: 140px !important;
      }
    }

    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }

    .el-table__row {
      .cell {
        min-height: 30px;
        > div {
          min-height: 30px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .min-width-select {
      width: 90px;
      min-width: 90px;
      .el-input {
        width: 90px;
        min-width: 90px;
        input {
          width: 90px;
          min-width: 90px;
        }
      }
    }

    .number-input {
      width: 90px !important;
      min-width: 90px !important;
      .el-input__inner {
        width: 90px !important;
      }
    }
  }

  .table-operate {
    margin: 8px 0;
  }

  .tac100w {
    text-align: center;
    width: 100px;
  }

  .delete-device {
    color: rgb(245, 108, 108);
    cursor: pointer;
  }

  .required-icon {
    color: #f56c6c;
    margin-right: 4px;
  }
}
</style>
