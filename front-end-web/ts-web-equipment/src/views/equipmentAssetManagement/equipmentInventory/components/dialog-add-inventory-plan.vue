<template>
  <vxe-modal
    className="dialog-add-inventory-plan"
    width="880"
    height="530"
    :title="typeTitle"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <ts-form ref="form" :model="form">
        <div class="form-tips-item">基本信息</div>

        <ts-form-item label="盘点任务" prop="name" :rules="rules.required">
          <ts-input v-model="form.name" placeholder="请输入" maxLength="100" />
        </ts-form-item>

        <ts-form-item
          label="盘点负责人"
          prop="engineerNameSet"
          :rules="rules.required"
        >
          <ts-input
            v-model="form.engineerNameSet"
            readonly
            placeholder="请选择"
            style="width: 100%;"
          >
            <template v-slot:suffix>
              <img
                class="person-icon"
                src="@/assets/img/defUserPhoto.png"
                @click="handleOpenSelectPerson('engineerIdSet')"
              />
            </template>
          </ts-input>
        </ts-form-item>

        <ts-form-item label="盘点日期" prop="date" :rules="rules.required">
          <base-date-range-picker v-model="form.date" type="daterange" />
        </ts-form-item>

        <ts-form-item label="盘点方式">
          <ts-radio-group v-model="form.isDefPd">
            <ts-radio label="0">RFID盘点</ts-radio>
            <ts-radio label="1">手工盘点</ts-radio>
          </ts-radio-group>
        </ts-form-item>

        <div class="form-tips-item">盘点范围</div>
        <ts-form-item label="资产类别" prop="skuType" :rules="rules.required">
          <ts-select
            style="width: 100%"
            v-model="form.skuType"
            placeholder="请选择"
            @change="handleChangeSkuType"
          >
            <ts-option
              v-for="item in skuTypeList"
              :key="item.itemNameValue"
              :value="item.itemNameValue"
              :label="item.itemName"
            />
          </ts-select>
        </ts-form-item>

        <ts-form-item label="科室范围">
          <ts-ztree-multi-select
            v-if="visible"
            v-model="form.orgIdSet"
            :echoValue="form.orgNameSet"
            :data="treeData"
            Y="s"
            N="ps"
            @hide="handleGetTasksNumber"
            @clear="handleGetTasksNumber"
          />
          <span>默认盘点所有科室</span>
        </ts-form-item>

        <template v-if="isMedicalEquipment">
          <ts-form-item label="医疗器械分类">
            <ts-ztree-multi-select
              ref="cate22Ref"
              v-if="visible"
              v-model="form.cate22IdSet"
              :echoValue="form.cate22NameSet"
              :data="categoryTreeData"
              Y="s"
              N="ps"
              @hide="handleGetTasksNumber"
              @clear="handleGetTasksNumber"
            />
            <span>默认盘点所有医疗器械分类</span>
          </ts-form-item>
        </template>
        <template v-else>
          <ts-form-item label="固定资产分类">
            <ts-ztree-multi-select
              ref="cateIdRef"
              v-if="visible"
              v-model="form.cateIdSet"
              :echoValue="form.cateNameSet"
              :data="deviceClassifyTree"
              Y="s"
              N="ps"
              @hide="handleGetTasksNumber"
              @clear="handleGetTasksNumber"
            />
            <span>默认盘点所有固定资产分类</span>
          </ts-form-item>
        </template>
      </ts-form>

      <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleOk" />
    </template>

    <template #footer>
      <span>
        {{ isMedicalEquipment ? '设备' : '资产' }}总数为：{{ deviceNum }}台
        &nbsp;&nbsp;&nbsp;&nbsp;
      </span>
      <ts-button
        type="primary"
        :disabled="deviceNum === 0"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        保 存
      </ts-button>
      <ts-button :disabled="submitLoading" class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    categoryTreeData: {
      type: Array,
      default: () => []
    },
    deviceClassifyTree: {
      type: Array,
      default: () => []
    }
  },
  watch: {},
  computed: {
    typeTitle() {
      let title = this.actionType === 'add' ? '新增' : '编辑';
      return title + '盘点任务';
    },
    isMedicalEquipment() {
      return this.form.skuType === '0';
    }
  },
  data() {
    return {
      skuTypeList: [],

      visible: false,
      submitLoading: false,
      actionType: 'add',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      deviceNum: 0,
      treeData: []
    };
  },
  methods: {
    async show({ type = 'add', data = {} }) {
      await this.handleGetTree();
      await this.getSkuTypeList();
      this.actionType = type;
      this.$set(this, 'form', { isDefPd: '0' });

      if (this.actionType === 'edit') {
        this.formatterDataSet(data, 'orgNameSet', 'orgIdSet');
        this.formatterDataSet(data, 'cate22NameSet', 'cate22IdSet');
        this.formatterDataSet(data, 'cateNameSet', 'cateIdSet');

        data.date = [data.startAt, data.endAt];
        this.$set(this, 'form', deepClone(data));
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
      await this.handleGetTasksNumber();
    },

    async getSkuTypeList() {
      let res = await this.ajax.getSkuTypeList('inventoryPlanCreate');
      if (!res.success) {
        this.$newMessage('error', res.message || '资产类别数据获取失败!');
        return;
      }
      this.skuTypeList = res.object || [];
    },

    formatterDataSet(data, nameKey, idKey) {
      if (data[nameKey] && data[idKey]) {
        const ids = data[idKey].split(',');
        const texts = data[nameKey].split(',');

        data[idKey] = ids.map((id, index) => ({
          id,
          name: texts[index]
        }));
      } else {
        data[idKey] = [];
      }
    },

    handleChangeSkuType() {
      this.handleGetTasksNumber();

      this.$set(this.form, 'cate22IdSet', []);
      this.$set(this.form, 'cate22NameSet', '');
      this.$set(this.form, 'cateIdSet', []);
      this.$set(this.form, 'cateNameSet', '');

      if (this.$refs.cate22Ref) {
        this.$refs.cate22Ref.searchVal = '';
      }
      if (this.$refs.cateIdRef) {
        this.$refs.cateIdRef.searchVal = '';
      }
      this.$forceUpdate();
    },

    async handleGetTasksNumber() {
      try {
        let searchParams = Object.assign(
          {
            skuType: this.form.skuType
          },
          this.getTreeSelectIdToString()
        );
        let res = await this.ajax.inventoryPlanTasks(searchParams);
        if (!res.success) {
          throw res.message || '获取设备总数失败, 请联系管理员!';
        }
        this.deviceNum = isNaN(Number(res.object)) ? 0 : Number(res.object);
      } catch (error) {
        this.$newMessage('error', error);
      }
    },

    getTreeSelectIdToString() {
      const mapIdsToString = arr =>
        arr?.length > 0 ? arr.map(m => m.id).join(',') : '';

      const data = {
        orgIdSet: mapIdsToString(this.form.orgIdSet),
        cate22IdSet: mapIdsToString(this.form.cate22IdSet),
        cateIdSet: mapIdsToString(this.form.cateIdSet)
      };

      return data;
    },

    async handleGetTree() {
      try {
        let res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw res.message || '获取科室信息失败, 请联系管理员!';
        }
        this.treeData = deepClone(res.object || []);
      } catch (error) {
        this.$newMessage('error', error);
      }
    },

    handleOpenSelectPerson(key) {
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: false,
        echoData: {
          engineerIdSet: this.form.engineerIdSet
        },
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp: ['engineerNameSet', 'engineerIdSet']
        }
      });
    },

    handleOk(result, key) {
      switch (key) {
        case 'engineerIdSet':
          const { allNames: engineerNameSet, engineerIdSet } = result[key];
          this.$set(this.form, 'engineerNameSet', engineerNameSet);
          this.$set(this.form, 'engineerIdSet', engineerIdSet);
          break;
      }
    },

    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();

        let data = deepClone(this.form);
        let { date = [] } = data,
          [startAt = '', endAt = ''] = date;
        data.startAt = startAt;
        data.endAt = endAt;
        delete data.date;

        data = {
          ...data,
          ...this.getTreeSelectIdToString()
        };

        await this.ajax[
          this.actionType === 'add'
            ? 'inventoryPlanSave'
            : 'inventoryPlanUpdate'
        ](data).then(res => {
          this.submitLoading = false;

          if (res.success && res.statusCode === 200) {
            this.$newMessage(
              'success',
              res.message || this.typeTitle + '成功!'
            );
            this.$emit('refresh');
            this.close();
          } else {
            this.$newMessage('error', res.message || this.typeTitle + '失败!');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.form = {};
      this.deviceNum = 0;
      this.treeData = [];

      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss">
.ts-ztree-select-popper .tree-select-box .ztree .button::before {
  border-width: 7px !important;
  top: 4px !important;
}
</style>

<style lang="scss" scoped>
.dialog-add-inventory-plan {
  .form-tips-item {
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 18px;
      border-radius: 12px;
      background-color: $primary-blue;
      color: $primary-blue;
      margin-right: 4px;
    }
  }

  .ts-button {
    &.is-disabled {
      background-color: #eee !important;
      border-color: rgb(210, 220, 252) !important;
      color: rgb(41, 92, 249) !important;
    }
  }

  .person-icon {
    margin-top: 3px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
</style>
