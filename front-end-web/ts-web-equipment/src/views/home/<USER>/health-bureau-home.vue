<template>
  <div class="home-container">
    <div class="wrap" style="height: 142px">
      <BusinessTargetStatistic
        ref="businessTargetStatistic"
        class="wrap-item wrap-left home-block"
      />
      <BudgetAndPurchaseStatistic
        ref="budgetAndPurchaseStatistic"
        class="wrap-item home-block"
      />
    </div>
    <div class="wrap" style="height: 218px">
      <ToDoReview ref="toDoReview" class="wrap-item wrap-left home-block" />

      <div class="wrap-item wrap">
        <ToDoStatistic
          ref="toDoStatistic"
          class="wrap-item wrap-left home-block"
        />
        <div class="quick-entrance-box home-block wrap-item">
          <div class="content-view">
            <div class="top-label-search">
              <div class="tips-label">快捷入口</div>
            </div>
            <div class="link-list">
              <div
                v-for="(item, index) in linkItem"
                :key="index"
                class="link-item"
                @click="jumpPage(item.path)"
              >
                <img
                  class="item-img"
                  :src="require(`@/assets/img/equipment-home/${item.img}.png`)"
                  alt=""
                />
                <p class="item-label">{{ item.label }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="budget-usage-table-wrap">
      <BudgetUsageTable
        ref="budgetTable"
        class="budget-table"
        :year="budgetUsageYear"
        :deptName="budgetUsageDeptName"
      />
    </div>
  </div>
</template>

<script>
import BusinessTargetStatistic from './business-target-statistic.vue';
import BudgetAndPurchaseStatistic from './budget-and-purchase-statistic.vue';
import ToDoReview from './to-do-review.vue';
import ToDoStatistic from './to-do-statistic.vue';
import BudgetUsageTable from './budget-usage-table.vue';

export default {
  components: {
    BusinessTargetStatistic,
    BudgetAndPurchaseStatistic,
    ToDoReview,
    ToDoStatistic,
    BudgetUsageTable
  },
  data() {
    return {
      refreshTimer: null,
      budgetUsageDeptOption: [],
      budgetUsageDeptName: '',
      budgetUsageYear: this.$dayjs().format('YYYY'),
      linkItem: [
        {
          img: 'img4',
          label: '预算审批',
          path: '/budget/approval'
        },
        {
          img: 'img1',
          label: '预算查询',
          path: '/budget/view'
        },
        {
          img: 'img4',
          label: '采购审批',
          path: '/purchase/approval'
        },
        {
          img: 'img1',
          label: '采购查询',
          path: '/purchase/view'
        },
        {
          img: 'img2',
          label: '设备管理',
          path: '/management/equipmentManagement'
        },
        {
          img: 'img2',
          label: '工程项目管理',
          path: '/management/constructionManagement'
        },
        {
          img: 'img2',
          label: '服务项目管理',
          path: '/management/serviceManagement'
        },
        {
          img: 'img3',
          label: '效益分析',
          path: '/statistic/budgetStatistics'
        }
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      this.getOrganizationList();
      this.handleRefreshBudgetUsageTable();
      // this.refreshTimer && clearInterval(this.refreshTimer);
      // this.refreshTimer = setInterval(() => {
      //   this.handleRefreshBudgetUsageTable();
      // }, 60000);
    });
  },
  beforeDestroy() {
    clearInterval(this.refreshTimer);
  },
  methods: {
    getOrganizationList() {
      this.ajax.getOrganizationList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let rows = res.object || [];
        this.budgetUsageDeptOption = rows.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.name
          };
        });
      });
    },
    handleRefreshBudgetUsageTable() {
      this.$refs.businessTargetStatistic.refresh();
      this.$refs.budgetAndPurchaseStatistic.refresh();
      this.$refs.toDoReview.refresh();
      this.$refs.toDoStatistic.refresh();
      this.$refs.budgetTable.refresh();
    },
    jumpPage(path) {
      this.$router.push(path);
    }
  }
};
</script>

<style lang="scss" scoped>
.home-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .wrap {
    display: flex;
    justify-content: space-between;
    .wrap-item {
      flex: 1;
    }
    .wrap-left {
      margin-right: 8px;
    }
  }

  .home-block {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  .link-list {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;

    .link-item {
      width: calc(25% - 8px);
      height: 50%;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      .item-img {
        width: 30px;
        height: 30px;
        margin: 10px 0;
      }
      .item-label {
        font-size: 13px;
        margin-bottom: 0;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
  .budget-usage-table-wrap {
    flex: 1;
    overflow: hidden;
  }
  /deep/ {
    .content-view {
      padding: 0 8px 8px;
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
    }
    .top-label-search {
      padding: 8px 0;
    }
    .tips-label {
      display: flex;
      align-items: center;
      font-weight: 800;
      font-size: 16px;
      color: #333;
      &::before {
        content: '';
        display: inline-block;
        color: #5260ff;
        width: 5px;
        height: 18px;
        background: #5260ff;
        border-radius: 4px;
        margin-right: 6px;
      }
    }
  }
}
</style>
