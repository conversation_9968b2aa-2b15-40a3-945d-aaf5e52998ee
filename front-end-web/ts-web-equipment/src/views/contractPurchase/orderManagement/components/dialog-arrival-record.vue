<template>
  <vxe-modal
    className="dialog-arrival-record"
    width="88%"
    height="85%"
    :title="`${projectName}到货记录`"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <div
          v-for="(batch, index) in batchList"
          :key="index"
          class="batch-item"
        >
          <div class="batch-title">批次{{ index + 1 }}</div>

          <div class="section-content">
            <div class="section-title">
              基本信息：
            </div>

            <div class="basic-info">
              <span class="expand-button" @click="toggleExpand(index)">
                {{ batch.isExpanded ? '收起' : '查看更多' }}
              </span>

              <div class="row">
                <div class="item">
                  <span class="label">货物单号：</span>
                  <span class="value">
                    {{ batch.inboundOrder.deliveryNo }}
                  </span>
                </div>
                <div class="item">
                  <span class="label">登记人：</span>
                  <span class="value">
                    {{ batch.inboundOrder.createUserName }}
                  </span>
                </div>
                <div class="item">
                  <span class="label">登记时间：</span>
                  <span class="value">
                    {{ batch.inboundOrder.createDate }}
                  </span>
                </div>
              </div>

              <template v-if="batch.isExpanded">
                <div class="row">
                  <div class="item">
                    <span class="label">入库类型：</span>
                    <span class="value">
                      {{ batch.inboundOrder.typeShow }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">入库单号：</span>
                    <span class="value">
                      {{ batch.inboundOrder.batchNo }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">入库状态：</span>
                    <span class="value">
                      {{ batch.inboundOrder.statusShow }}
                    </span>
                  </div>
                </div>

                <div class="row">
                  <div class="item">
                    <span class="label">入库人：</span>
                    <span class="value">
                      {{ batch.inboundOrder.doerName }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">入库时间：</span>
                    <span class="value">
                      {{ batch.inboundOrder.doDate }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">供应商：</span>
                    <span class="value">
                      {{ batch.inboundOrder.supplyName }}
                    </span>
                  </div>
                </div>

                <div class="row">
                  <div class="item">
                    <span class="label">资产类别：</span>
                    <span class="value">
                      {{ batch.inboundOrder.skuTypeShow }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">所属科室：</span>
                    <span class="value">
                      {{ batch.inboundOrder.belongToOrgName }}
                    </span>
                  </div>
                  <div class="item">
                    <span class="label">库存位置：</span>
                    <span class="value">
                      {{ batch.inboundOrder.loc }}
                    </span>
                  </div>
                </div>

                <div class="row">
                  <div class="item" style="width: 100%;">
                    <span class="label">备注：</span>
                    <span class="value">
                      {{ batch.inboundOrder.note }}
                    </span>
                  </div>
                </div>
              </template>
            </div>

            <div class="section-title">
              到货资产信息：
            </div>

            <!-- 表格 -->
            <ts-vxe-base-table
              class="form-table"
              :id="`table_arrival_record_${index}`"
              :ref="`table_${index}`"
              :columns="columns"
              :hasPage="false"
              minHeight="32"
            />
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <ts-button class="shallowButton" @click="close">
        关闭
      </ts-button>
    </template>
  </vxe-modal>
</template>

<script>
import { Decimal } from 'decimal.js';

export default {
  data() {
    return {
      visible: false,
      projectName: '',

      batchList: [],
      columns: [
        {
          label: '序号',
          prop: 'index',
          width: 60,
          align: 'center',
          render: (h, { row, data, rowIndex: index }) => {
            return h(
              'span',
              {
                class: {
                  'footer-cell': index === data.length - 1
                }
              },
              row['index']
            );
          }
        },
        { prop: 'name', label: '资产名称', align: 'center', minWidth: 140 },
        {
          prop: 'category22Name',
          label: '医疗器械分类',
          align: 'center',
          minWidth: 140
        },
        { prop: 'model', label: '规格型号', align: 'center', width: 110 },
        {
          prop: 'brandName',
          label: '品牌',
          align: 'center',
          minWidth: 100
        },
        {
          prop: 'contractNo',
          label: '合同编号',
          align: 'center',
          width: 130
        },
        {
          prop: 'num',
          label: '数量',
          align: 'center',
          width: 65,
          render: (h, { row, data, rowIndex: index }) => {
            return h(
              'span',
              {
                class: {
                  'footer-cell': index === data.length - 1
                }
              },
              this.formatNumber(row.num)
            );
          }
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'right',
          width: 105,
          render: (h, { row, rowIndex: index }) => {
            return h('span', null, this.formatNumber(row.price));
          }
        },
        {
          prop: 'amount',
          label: '金额(元)',
          align: 'right',
          width: 105,
          render: (h, { row, data, rowIndex: index }) => {
            return h(
              'span',
              {
                class: {
                  'footer-cell': index === data.length - 1
                }
              },
              this.formatNumber(row.amount)
            );
          }
        }
      ]
    };
  },
  methods: {
    async show({ name, id }) {
      this.projectName = `【${name}】到货记录`;

      const result = await this.handleGetDetails(id);
      if (!result) return;

      // 格式化数据 工厂函数
      this.batchList = this.handleFormatterData(result);

      // 设置合计数据
      this.handleSetFooterData();

      this.visible = true;
      this.$nextTick(() => {
        this.batchList.forEach((batch, index) => {
          if (Array.isArray(batch.inboundOrderDetailExtResp)) {
            this.$refs[`table_${index}`]?.[0]?.refresh({
              rows: batch.inboundOrderDetailExtResp
            });
          }
        });
      });
    },

    async handleGetDetails(id) {
      const res = await this.ajax.getInboundDetailListByRelaId(id);
      if (!res.success) {
        this.$newMessage(
          'error',
          res.message || '获取到货记录失败,请联系管理员!'
        );
        return false;
      }

      if (!res.object.length) {
        this.$newMessage('warning', '该订单暂无到货记录!');
        return false;
      }

      return res.object;
    },

    handleFormatterData(result) {
      // 计算每行总金额
      function format(list) {
        return list.map((item, i) => {
          let num = item.num;
          let price = item.price;
          let amount = 0;

          const isNumValid = !isNaN(num);
          const isPriceValid = !isNaN(price);
          if (isNumValid && isPriceValid) {
            amount = Decimal.mul(item.price, item.num).toNumber();
          }

          return {
            ...item,
            index: i + 1,
            amount
          };
        });
      }

      return result.map(batch => {
        if (Array.isArray(batch.inboundOrderDetailExtResp)) {
          batch.inboundOrderDetailExtResp = format(
            batch.inboundOrderDetailExtResp
          );
        }

        return {
          ...batch,
          isExpanded: false
        };
      });
    },

    handleSetFooterData() {
      const calculateTotal = (batch, field) => {
        return batch.inboundOrderDetailExtResp.reduce((sum, item) => {
          return sum.plus(item[field] || 0);
        }, new Decimal(0));
      };

      this.batchList.forEach(batch => {
        if (Array.isArray(batch.inboundOrderDetailExtResp)) {
          batch.inboundOrderDetailExtResp.push({
            index: '合计',
            num: calculateTotal(batch, 'num'),
            amount: calculateTotal(batch, 'amount')
          });
        }
      });
    },

    formatNumber(value) {
      return isNaN(Number(value))
        ? value
        : Number(value).toLocaleString('zh-CN');
    },

    // 展开收起
    toggleExpand(index) {
      this.$set(
        this.batchList[index],
        'isExpanded',
        !this.batchList[index].isExpanded
      );
    },

    // 关闭弹窗
    close() {
      this.batchList = [];
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-arrival-record {
  padding-right: 0px;

  ::v-deep {
    .vxe-modal--content {
      padding-right: 0px !important;
      padding-left: 8px !important;
      padding-bottom: 16px !important;
    }

    .content {
      height: 100%;
      padding-right: 8px;
      overflow-y: auto;

      .batch-item {
        height: auto;
        border-radius: 6px;
        background-color: #f1f1f1;
        border: 1px solid $primary-blue;
        margin-bottom: 16px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .batch-title {
          padding: 8px 0;
          color: #333;
          font-size: 16px;
          background-color: #d3ddfc;
          padding-left: 8px;
          margin-bottom: 8px;
          border-radius: 6px 6px 0 0;

          &::before {
            content: '1';
            display: inline-block;
            width: 4px;
            height: 100%;
            background-color: $primary-blue;
            color: transparent;
            margin-right: 4px;
            border-radius: 4px;
          }
        }

        .section-content {
          width: calc(100% - 16px);
          margin-left: 8px;
          padding: 8px;
          margin-bottom: 8px;
          border-radius: 6px;
          background-color: #fff;
          overflow: hidden;

          .section-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
          }

          .basic-info {
            margin-bottom: 8px;
            border-radius: 6px;
            background-color: #fff;
            position: relative;

            .expand-button {
              color: $primary-blue;
              position: absolute;
              right: 0px;
              top: 0px;
              padding: 0;
              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              text-decoration: none;
              cursor: pointer;
            }

            .row {
              display: flex;
              width: 100%;
              margin-bottom: 8px;

              .item {
                width: 33%;
                display: flex;

                .label {
                  width: 90px;
                  text-align: right;
                  display: inline-flex;
                  align-items: center;
                  justify-content: flex-end;
                  font-size: 14px;
                  font-weight: 500;
                }

                .value {
                  flex: 1;
                  color: #333;
                  font-size: 14px;
                  overflow: hidden;
                  overflow-wrap: break-word;
                }
              }
            }
          }

          .form-table {
            flex: 1;
            overflow: hidden;
            transform: scale(1);
            .footer-cell {
              font-weight: bold;
              color: $warning-color;
            }
          }
        }
      }
    }
  }
}
</style>
