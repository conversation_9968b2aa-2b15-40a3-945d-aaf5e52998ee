<template>
  <vxe-modal
    className="dialog-add-material-return-store"
    :title="title"
    v-model="visible"
    width="80%"
    height="85%"
    :before-hide-method="beforeHideMethod"
  >
    <template #default>
      <div class="content-container">
        <operate-dialog-btns
          :stat="form.returnStk?.stat"
          @btnOperate="handleBtnOperate"
          type="4"
        />

        <div class="form-container">
          <ts-form ref="form" :model="form" v-if="isAdd || isEdit">
            <div class="form-group-tips">
              <span>单据信息</span>
            </div>

            <material-return-store-basic-form
              ref="materialReturnStoreBasicForm"
              :form="form"
              :rules="rules"
              :isTo="isTo"
            />

            <div class="form-group-tips">
              <span>退库物资明细</span>

              <div>
                <ts-button
                  v-show="!isTo"
                  class="shallowButton"
                  type="primary"
                  @click="handleAddMaterialDictionary"
                >
                  添加物资
                </ts-button>
                <ts-button
                  type="danger"
                  @click="handleRemoveMaterialDictionary"
                >
                  删除
                </ts-button>
              </div>
            </div>

            <material-return-store-details-dictionary
              ref="materialReturnStoreDetailsDictionary"
              :form="form"
              :rules="rules"
              :isBatch="isBatch"
              :isTo="isTo"
            />
          </ts-form>

          <!-- 单据详情 -->
          <dialog-details-material-return-store
            v-if="isDetails"
            ref="dialogDetailsMaterialReturnStore"
            :form="form"
            :isBatch="isBatch"
            :isTo="isTo"
          />
        </div>

        <dialog-multiple-material-dictionary
          renderType="4"
          ref="dialogMultipleMaterialDictionary"
          @submit="handleSubmitMaterialDictionary"
        />

        <dialog-material-print renderType="4" ref="dialogMaterialPrint" />
        <dialog-material-print-zs renderType="4" ref="dialogMaterialPrintZs" />
      </div>
    </template>
  </vxe-modal>
</template>

<script>
import { exportFile } from '@/unit/export.js';
import { primaryBlue } from '@/styles/variables.scss';
import cloneDeep from 'lodash-es/cloneDeep';
import MaterialReturnStoreBasicForm from './components/material-return-store-basic-form.vue';
import MaterialReturnStoreDetailsDictionary from './components/material-return-store-details-dictionary.vue';
import DialogMultipleMaterialDictionary from '@/views/moduleMaterial/inventoryManagement/components/dialog-multiple-material-dictionary.vue';
import OperateDialogBtns from '@/views/moduleMaterial/inventoryManagement/components/operate-dialog-btns.vue';
import DialogDetailsMaterialReturnStore from './dialog-details-material-return-store.vue';
import DialogMaterialPrint from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print.vue';
import DialogMaterialPrintZs from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print-zs.vue';
export default {
  components: {
    MaterialReturnStoreBasicForm,
    MaterialReturnStoreDetailsDictionary,
    DialogMultipleMaterialDictionary,
    OperateDialogBtns,
    DialogDetailsMaterialReturnStore,
    DialogMaterialPrint,
    DialogMaterialPrintZs
  },
  props: {
    whId: {
      type: String,
      default: ''
    },
    handleGetReceiptDetailApi: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      stkMtd: '',
      whName: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      disabled: true
    };
  },

  computed: {
    title() {
      return `【${this.whName}】【 ${this.form.returnStk?.flowNo ||
        ''}】退库单`;
    },

    isTo() {
      return !!this.form.returnStk?.outbFlowNo;
    },

    isAdd() {
      return this.form.returnStk?.stat == '0';
    },

    isEdit() {
      return this.form.returnStk?.stat == '1';
    },

    isDetails() {
      return this.form.returnStk?.stat == '2';
    },

    isBatch() {
      return this.stkMtd == '0';
    }
  },

  methods: {
    async show({ data = {}, whName = '', stkMtd = '' }) {
      this.whName = whName;
      this.stkMtd = stkMtd;
      if (JSON.stringify(data) !== '{}') {
        data.returnStkDtlList.forEach(item => {
          item.isCheck = false;
          // 为编辑 减去新增时填写的退库数量
          if (this.isEdit) {
            item.inNum = item.inNum - item.num;
          }
        });
        this.$set(this, 'form', cloneDeep(data));
      } else {
        this.$set(this, 'form', this.initForm());
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        // 编辑单据 回显autoInput
        if (this.isEdit) {
          this.$refs.materialReturnStoreDetailsDictionary.echoAutoInput();
        }
        // 详情单据 回显表单
        if (this.isDetails) {
          this.$refs.dialogDetailsMaterialReturnStore.setTableData();
        }
      });
    },

    initForm() {
      let parentStoreInfo = this.$getParentStoreInfo();
      let userInfo = parentStoreInfo?.userInfo || {};
      return {
        returnStk: {
          stat: '0',
          whId: this.whId,
          returnDate: this.$dayjs().format('YYYY-MM-DD'),
          returnDeptId: '',
          returnDeptName: '',
          returnerName: userInfo.employeeName
        },
        returnStkDtlList: []
      };
    },

    // 添加物资明细 弹窗
    handleAddMaterialDictionary() {
      let ignoreIdList = '';
      if (this.form.returnStkDtlList.length) {
        ignoreIdList = this.form.returnStkDtlList
          .map(item => item.id)
          .join(',');
      }

      this.$refs.dialogMultipleMaterialDictionary.show({
        otherParams: {
          status: '1',
          warehouseId: this.whId,
          ignoreIdList
        }
      });
    },

    async handleRemoveMaterialDictionary() {
      let selection = this.$refs.materialReturnStoreDetailsDictionary.getSelection();
      if (selection.length === 0) {
        this.$newMessage('error', '请选择要删除的物资');
        return;
      }

      try {
        await this.$newConfirm(
          '您是否确认【<span style="color: red">删除</span>】当前数据？'
        );

        this.form.returnStkDtlList = this.form.returnStkDtlList.filter(
          item => !selection.includes(item.id)
        );
        this.$refs.materialReturnStoreDetailsDictionary.checkAll = false;
      } catch (error) {
        console.error(error);
      }
    },

    handleSubmitMaterialDictionary(data) {
      let formatData = this.handleFormatMaterialDictionary(data);
      this.form.returnStkDtlList.push(...formatData);
      this.form.returnStkDtlList.sort((a, b) =>
        a.flowNo.localeCompare(b.flowNo)
      );
    },

    handleFormatMaterialDictionary(data) {
      return data.map(item => {
        return {
          isCheck: false,
          id: item.id,
          skuId: item.id,

          flowNo: item.flowNo,
          name: item.name,
          categoryName: item.categoryName,
          model: item.model,
          unit: item.unit,
          unitShow: item.unitShow,

          batchNo: item.batchNo,
          inNum: item.inNum,
          stock: item.stock,
          num: '',
          price: item.price,
          amount: 0,

          prodNo: item.prodNo || '',
          prodDate: item.prodDate || '',
          expireDate: item.expireDate || '',

          regNo: item.regNo,
          brand: item.brand,
          manufacturerName: item.manufacturerName,
          remark: ''
        };
      });
    },

    handleBtnOperate(event) {
      this[event]();
    },

    formatSaveData(data) {
      data.returnStkDtlList = data.returnStkDtlList.map(item => {
        return {
          batchNo: item.batchNo || '',
          num: item.num,
          price: item.price,
          skuId: item.skuId,

          prodNo: item.prodNo || '',
          prodDate: item.prodDate || '',
          expireDate: item.expireDate || '',

          remark: item.remark || ''
        };
      });
      return data;
    },

    // 保存单据
    async operateSave() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();

        if (this.form.returnStkDtlList.length === 0) {
          throw new Error('请添加退库物资明细!');
        }

        let data = cloneDeep(this.form);
        let API = this.isEdit
          ? this.ajax.materialReturnStkUpdate
          : this.ajax.materialReturnStkSave;

        let title = this.isEdit ? '【修改】退库单据' : '【新增】退库单据';

        let saveData = this.formatSaveData(data);
        let res = await API(saveData);
        if (!res.success) {
          throw new Error(res.message || `${title}失败!`);
        }
        this.$newMessage('success', res.message || `${title}成功!`);
        let detailData = await this.handleGetReceiptDetailApi(res.object);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      } finally {
        this.submitLoading = false;
      }
    },

    // 新增单据
    operateAdd() {
      this.resetFormRender({});
    },

    // 清空单据 等于新增单据
    operateClear() {
      this.operateAdd();
      this.$newMessage('success', '【清空】成功!');
    },

    // 删除单据
    async operateDel() {
      const confirmHtml = `确定【<span style="color: red">删除</span>】当前数据？`;
      await this.$newConfirm(confirmHtml, '提示');
      const res = await this.ajax.materialReturnStkBatchDelete([
        this.form.returnStk.id
      ]);
      if (!res.success) {
        throw new Error(res.message || '【删除】失败!');
      }
      this.$newMessage('success', '【删除】成功!');
      this.operateAdd();
    },

    // 审核单据
    async operateAudit() {
      try {
        const confirmHtml = `确定【<span style="color: ${primaryBlue}">审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.materialReturnStkBatchConfirm([
          this.form.returnStk.id
        ]);
        if (!res.success) {
          throw new Error(res.message || '【审核出库】失败!');
        }
        this.$newMessage('success', '【审核出库】成功!');

        let id = this.form.returnStk.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 取消审核
    async operateCancelAudit() {
      try {
        const confirmHtml = `确定【<span style="color: red">取消审核</span>】当前数据？`;
        await this.$newConfirm(confirmHtml, '提示');
        const res = await this.ajax.materialReturnStkBatchRollbackConfirm([
          this.form.returnStk.id
        ]);
        if (!res.success) {
          throw new Error(res.message || '【取消审核】失败!');
        }
        this.$newMessage('success', '【取消审核】成功!');

        let id = this.form.returnStk.id;
        let detailData = await this.handleGetReceiptDetailApi(id);
        this.resetFormRender(detailData);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 上一条 单据切换
    async operatePrevious() {
      this.handleDirection('prev');
    },

    // 下一条 单据切换
    async operateNext() {
      this.handleDirection('next');
    },

    async handleDirection(type) {
      try {
        let id = this.form.returnStk.id;
        let tips = `【切换】${type === 'prev' ? '上' : '下'}一条单据`;
        const res = await this.ajax.materialReturnStkDirection(id, type);
        if (!res.success) {
          throw new Error(res.message || `${tips}失败!`);
        }
        this.$newMessage('success', `${tips}成功!`);
        this.resetFormRender(res.object);
      } catch (error) {
        error.message && this.$newMessage('error', error.message);
      }
    },

    // 打印单据
    operatePrint() {
      let params = {
        data: {
          obj: this.form.returnStk,
          list: this.form.returnStkDtlList
        },
        whName: this.whName
      };
      this.$refs.dialogMaterialPrintZs.show(params);
    },

    operateExport() {
      let ids = [this.form.returnStk.id];
      exportFile({
        url: '/ts-ams/api/material/returnStk/export',
        filename: `退库单据导出.zip`,
        data: ids
      });
    },

    resetFormRender(data) {
      this.show({
        whName: this.whName,
        stkMtd: this.stkMtd,
        data
      });
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    },

    beforeHideMethod() {
      this.$emit('submit');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-return-store {
  ::v-deep {
    .content-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: normal !important;
      background: #fff;

      .form-container {
        flex: 1;
        overflow-y: auto;
      }

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        margin: 8px 0;
        background-color: #f2f3f4;
        border-radius: 4px;

        > span {
          font-size: 15px;
          font-weight: 600;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #5260ff;
            margin-right: 4px;
            border-radius: 4px;
          }
        }

        .shallowButton {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
