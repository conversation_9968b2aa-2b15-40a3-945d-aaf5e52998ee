<template>
  <div class="material-return-store-details-container">
    <ts-form ref="form" :model="form">
      <div class="form-group-tips">
        <span>单据信息</span>
      </div>

      <div class="basic-info">
        <div class="item" v-for="item in basicInfoColumns" :key="item.prop">
          <span class="label">{{ item.label }}：</span>
          <span class="value">
            {{ form.returnStk[item.prop] }}
          </span>
        </div>
      </div>
      <div class="form-group-tips">
        <span>退库物资明细</span>
      </div>

      <ts-vxe-base-table
        class="material-return-store-details-table"
        id="material-return-store-details-table"
        ref="table"
        height="auto"
        min-height="auto"
        :hasPage="false"
        :columns="detailsColumns"
        show-footer
        :footer-data="footerData"
        footer-cell-class-name="footer-val-styles"
      />
    </ts-form>
  </div>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import Decimal from 'decimal.js';
import { mapGetters } from 'vuex';

import {
  baseMaterialDictionaryColumns,
  baseMaterialDictionaryColumns1
} from '@/views/moduleMaterial/inventoryManagement/config/dic';
export default {
  name: 'DialogDetailsMaterialReturnStore',
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    isBatch: {
      type: Boolean
    },
    isTo: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      footerData: [],
      basicInfoColumns: [
        { label: '退库科室', prop: 'returnDeptName' },
        { label: '退库日期', prop: 'returnDate' },
        { label: '退库人', prop: 'returnerName' },
        ...(this.isTo ? [{ label: '关联单据号', prop: 'outbFlowNo' }] : []),
        { label: '备注', prop: 'remark' }
      ]
    };
  },

  computed: {
    ...mapGetters('material', ['handleTsNumToLocaleStr']),

    detailsColumns() {
      let arr = cloneDeep([
        ...baseMaterialDictionaryColumns,
        ...(this.isTo
          ? [
              {
                prop: 'batchNo',
                label: '批次号',
                align: 'center',
                width: 155
              },
              {
                prop: 'outNum',
                label: '出库数量',
                align: 'right',
                width: 80
              }
            ]
          : []),
        {
          prop: 'num',
          label: '退库数量',
          align: 'right',
          width: 75
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'right',
          width: 140,
          render: (h, { row }) => {
            return h('span', null, this.handleTsNumToLocaleStr(row.price));
          }
        },
        {
          prop: 'totalAmt',
          label: '金额(元)',
          align: 'right',
          width: 140,
          render: (h, { row }) => {
            return h('span', null, this.handleTsNumToLocaleStr(row.totalAmt));
          }
        },
        {
          prop: 'prodNo',
          label: '生产批号',
          align: 'center',
          width: 155
        },
        {
          prop: 'prodDate',
          label: '生产日期',
          align: 'center',
          width: 145
        },
        {
          prop: 'expireDate',
          label: '失效日期',
          align: 'center',
          width: 145
        },
        ...baseMaterialDictionaryColumns1
      ]);

      // 处理列表 按库存 不显示指定列 固定左侧列
      arr = arr.filter(item => {
        let filterArr = ['isCheck'];
        if (!this.isBatch) {
          let fr = ['batchNo', 'prodNo', 'prodDate', 'expireDate', 'inNum'];
          filterArr.push(...fr);
        }
        return !filterArr.includes(item.prop);
      });

      arr.forEach(item => {
        if (['index', 'flowNo', 'name', 'categoryName'].includes(item.prop)) {
          item.fixed = 'left';
        }
        if (typeof item.render === 'string') {
          delete item.render;
        }
      });

      return arr;
    }
  },

  methods: {
    setTableData() {
      let rows = cloneDeep(this.form.returnStkDtlList);
      rows.forEach((item, index) => {
        item.index = index + 1;
      });

      this.$refs.table.refresh({
        rows
      });
      this.footerData = this.handleGetFooterData(rows);
    },

    handleGetFooterData(rows) {
      const calculateTotal = key => {
        return rows
          .map(row => row[key])
          .reduce((prev, curr) => {
            return !isNaN(curr) ? prev.plus(curr) : prev;
          }, new Decimal(0));
      };

      return [
        {
          index: '合计',
          num: calculateTotal('num'),
          totalAmt: this.handleTsNumToLocaleStr(calculateTotal('totalAmt'))
        }
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
.material-return-store-details-container {
  padding: 0px;
  height: 100%;

  .ts-form {
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-group-tips {
      width: 100%;
      color: #333;
      font-weight: 800;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      > span {
        font-weight: 800;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: $primary-blue;
          margin-right: 8px;
          border-radius: 4px;
          transform: translateY(2px);
        }
      }
    }
    .basic-info {
      display: flex;
      flex-wrap: wrap;
      padding-top: 12px;
      background: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.14);
      border: 1px solid #e5e5e5;
      border-bottom: none;
      margin-bottom: 8px;

      .item {
        width: 33%;
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .label {
          width: 130px;
          text-align: right;
          color: #606266;
          font-size: 14px;
          font-weight: 500;
        }

        .value {
          flex: 1;
          color: #333;
          font-size: 14px;
          overflow: hidden;
          overflow-wrap: break-word;
        }
      }
    }

    .material-return-store-details-table {
      ::v-deep {
        .footer-val-styles {
          background-color: #eceef3;
          .vxe-cell--item {
            font-weight: bold;
            color: $warning-color;
          }
        }
      }
    }
  }
}
</style>
