<template>
  <div class="material-store-manage-box">
    <warehouse-tabs
      ref="warehouseTabs"
      v-model="whId"
      :tabsName.sync="whName"
      @tab-click="handleTabClick"
    />
    <div class="receipt-container">
      <store-receipt-search
        ref="storeReceiptSearch"
        @search="handleStoreReceiptSearch"
        @add="operateAdd"
        @delete="operateDelete"
        @audit="operateAudit"
        @cancelAudit="operateCancelAudit"
        @returnGoods="operateReturnGoods"
        @print="operatePrint"
        @export="operateExport"
      />

      <store-receipt-table
        ref="storeReceiptTable"
        @receiptEdit="operateEdit"
        @currentChange="handleCurrentChange"
        @refresh="handleRefreshStoreReceiptTable"
      />
    </div>

    <receipt-details-table-container
      ref="receiptDetailsTable"
      type="1"
      :columns="detailsColumns"
      :parentCurrentRecord="parentCurrentRecord"
      :handleGetReceiptDetailApi="handleGetReceiptDetailApi"
    />

    <dialog-add-material-store
      ref="dialogAddMaterialStore"
      :whId="whId"
      :methodOptions="methodOptions"
      :handleGetReceiptDetailApi="handleGetReceiptDetailApi"
      @submit="handleRefreshStoreReceiptTable"
    />

    <dialog-material-print
      renderType="1"
      ref="dialogMaterialPrint"
      @print="handlePrintRefresh"
    />

    <dialog-material-print-zs
      renderType="1"
      ref="dialogMaterialPrintZs"
      @print="handlePrintRefresh"
    />
  </div>
</template>

<script>
import { exportFile } from '@/unit/export.js';
import { primaryBlue } from '@/styles/variables.scss';
import { detailsColumns } from './details/detailsColumns';
import WarehouseTabs from '../components/warehouse-tabs.vue';
import StoreReceiptSearch from './receipt/store-receipt-search.vue';
import StoreReceiptTable from './receipt/store-receipt-table.vue';

import ReceiptDetailsTableContainer from '../components/receipt-details-table-container.vue';
import DialogMaterialPrint from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print.vue';
import DialogMaterialPrintZs from '@/views/moduleMaterial/inventoryManagement/components/dialog-material-print-zs.vue';
import DialogAddMaterialStore from './dialog/dialog-add-material-store.vue';
export default {
  name: 'MaterialStoreManage',
  components: {
    StoreReceiptSearch,
    StoreReceiptTable,
    ReceiptDetailsTableContainer,
    DialogAddMaterialStore,
    WarehouseTabs,
    DialogMaterialPrint,
    DialogMaterialPrintZs
  },
  data() {
    return {
      detailsColumns,
      whId: '0',
      whName: '',
      methodOptions: []
    };
  },
  methods: {
    async refresh() {
      // 获取入库方式
      this.handleGetMaterialMethodCodeList();

      // 获取仓库列表
      await this.$refs.warehouseTabs?.handleGetAllWarehouseList();
      // 刷新入库单据列表
      this.handleRefreshStoreReceiptTable();
    },

    // 入库单据列表查询
    handleStoreReceiptSearch() {
      this.$refs.storeReceiptTable.setPageNo(1);
      this.handleRefreshStoreReceiptTable();
    },

    // 获取仓库出库方式
    handleGetStkMtd() {
      return this.$refs.warehouseTabs.getStkMtd();
    },

    // 新增入库单据
    operateAdd() {
      this.$refs.dialogAddMaterialStore.show({
        stkMtd: this.handleGetStkMtd(),
        whName: this.whName,
        data: {}
      });
    },

    // 单据操作dialog
    async operateEdit(row) {
      try {
        let data = await this.handleGetReceiptDetailApi(row.id);

        this.$refs.dialogAddMaterialStore.show({
          stkMtd: this.handleGetStkMtd(),
          whName: this.whName,
          data
        });
      } catch (error) {
        this.$newMessage('error', error.message);
      }
    },

    // 通用操作方法
    async handleBatchOperation({ action, confirmColor, apiMethod }) {
      const selection = this.$refs.storeReceiptTable.getSelection();
      if (!selection.length) {
        this.$newMessage('warning', `请选择要${action}的入库单据！`);
        return;
      }

      try {
        const confirmHtml = `确定【<span style="color: ${confirmColor}">${action}</span>】选中的数据？`;
        await this.$newConfirm(confirmHtml, '提示');

        const inbIdList = selection.map(item => item.id);
        const res = await this.ajax[apiMethod](inbIdList);
        if (res.success === false) {
          this.$newMessage('error', res.message || '操作失败');
          return;
        }
        this.$newMessage('success', '操作成功');
        this.handleRefreshStoreReceiptTable();
      } catch (error) {
        console.error(error);
      }
    },

    // 删除入库单据
    operateDelete() {
      this.handleBatchOperation({
        action: '删除',
        confirmColor: 'red',
        apiMethod: 'materialInbBatchDelete'
      });
    },

    // 审核入库单据
    operateAudit() {
      this.handleBatchOperation({
        action: '审核',
        confirmColor: primaryBlue,
        apiMethod: 'materialInbBatchConfirm'
      });
    },

    // 取消审核入库单据
    operateCancelAudit() {
      this.handleBatchOperation({
        action: '取消审核',
        confirmColor: 'red',
        apiMethod: 'mInbBRollbackConfirm'
      });
    },

    // 退货登记
    async operateReturnGoods() {
      try {
        const {
          id,
          stat,
          returnStat
        } = this.$refs.storeReceiptTable.handleGetCurrentRecord();
        if (!id) {
          throw new Error('请选择要退货的入库单据！');
        }
        if (stat == '1') {
          throw new Error('该条入库单据未审核！');
        }
        if (returnStat !== '0' && returnStat !== null) {
          throw new Error('该条入库单据已登记过退货!');
        }
        let res = await this.ajax.materialReturnInbFmt(id);
        if (!res.success) {
          throw new Error(res.message || '获取入库单转退货单数据失败');
        }
        let { returnDtlList = [] } = res.object;
        if (!returnDtlList.length || returnDtlList.every(e => e.stock == 0)) {
          throw new Error(`当前入库单所有物资均已出库，无法进行退货登记！`);
        }
        this.$router.push({
          path: '/material/inventory-management/material-return-goods-manage'
        });
        let event = this.$event.create('inStoreReturnGoods');
        event.trigger('openAddDialog', { id });
      } catch (error) {
        this.$newMessage('error', error.message || '操作失败!');
      }
    },

    // 单据打印
    async operatePrint() {
      try {
        const currentRecord = this.$refs.storeReceiptTable.handleGetCurrentRecord();
        if (!currentRecord) {
          throw new Error('请选择要打印的入库单据！');
        }
        if (currentRecord.stat == '1') {
          throw new Error('该条入库单据未审核！');
        }
        const data = await this.handleGetReceiptDetailApi(currentRecord.id);
        let params = {
          data: {
            obj: data.inb,
            list: data.inbDtlList
          },
          whName: this.whName
        };
        this.$refs.dialogMaterialPrintZs.show(params);
      } catch (error) {
        this.$newMessage('error', error.message || '打印失败');
      }
    },

    handlePrintRefresh() {
      // 清空详情表格数据
      this.$refs.receiptDetailsTable.clearTableData([]);
      this.$refs.storeReceiptTable.handleSetCurrentRow(null);

      // 刷新入库单据列表
      this.handleRefreshStoreReceiptTable();
    },

    // 导出
    operateExport() {
      const selection = this.$refs.storeReceiptTable.getSelection();
      if (!selection.length) {
        this.$newMessage('warning', `请选择要导出的入库单据！`);
        return;
      }

      let ids = selection.map(item => item['id']);
      exportFile({
        url: '/ts-ams/api/material/inb/export',
        filename: `入库单据导出.zip`,
        data: ids
      });
    },

    // 入库单据列表 刷新
    async handleRefreshStoreReceiptTable() {
      if (!this.checkQueryParam()) {
        return;
      }

      let searchForm = this.$refs.storeReceiptSearch.searchForm;
      let { date = [] } = searchForm,
        table = this.$refs.storeReceiptTable,
        pageNo = table.getPageNo(),
        pageSize = table.getPageSize(),
        formData = {
          ...searchForm,
          pageNo,
          pageSize,
          whId: this.whId
        };

      const [startD, endD] = date;
      formData.inbDateQuery =
        startD && endD ? `${startD} 00:00:00,${endD} 23:59:59` : '';
      !formData.inbDateQuery && delete formData.inbDateQuery;
      delete formData.date;

      let res = await this.ajax.materialInbList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });

      table.setPageData({
        ...res,
        rows
      });

      // 设置第一行选中
      // 刷新入库单据详情
      if (Array.isArray(rows) && rows.length) {
        let currentRow = this.parentCurrentRecord();
        if (!currentRow) {
          this.$refs.storeReceiptTable.handleSetCurrentRow(rows[0]);
          this.$refs.receiptDetailsTable.detailsRefresh();
        }
      } else {
        this.$refs.receiptDetailsTable.clearTableData([]);
      }
    },

    // 切换仓库
    handleTabClick() {
      // 清空搜索条件
      this.$refs.storeReceiptSearch.clearSearchForm();

      // 清空详情表格数据
      this.$refs.receiptDetailsTable.clearSearchForm();
      this.$refs.receiptDetailsTable.clearTableData([]);

      // 获取仓库入库方式
      this.handleGetMaterialMethodCodeList();
      this.handleStoreReceiptSearch();
    },

    // 入库单 高亮数据 切换
    handleCurrentChange() {
      this.$refs.receiptDetailsTable.detailsRefresh();
    },

    // 获取入库单据详情 （提供给 dialog 详情表格 使用）
    async handleGetReceiptDetailApi(id, params) {
      const res = await this.ajax.materialInbDetail(id, params);
      if (!res.success) {
        throw new Error(res.message || '获取入库单据详情失败');
      }
      return res.object || {};
    },

    // 获取单据列表 当前选中行数据（提供给 详情表格 使用）
    parentCurrentRecord() {
      return this.$refs.storeReceiptTable.handleGetCurrentRecord();
    },

    checkData([start, end]) {
      let num = 0;
      start && num++;
      end && num++;
      return num === 1;
    },

    checkQueryParam() {
      const { date = [] } = this.$refs.storeReceiptSearch.searchForm;
      if (this.checkData(date)) {
        this.$newMessage('warning', '请选择入库日期完整的时间区间查询');
        return false;
      }
      return true;
    },

    // 获取入库方式
    async handleGetMaterialMethodCodeList() {
      try {
        const res = await this.ajax.getMaterialMethodCodeList({
          method: '1',
          status: '1',
          warehouseIdSet: this.whId,
          pageNo: 1,
          pageSize: 99999
        });

        let options = Array.isArray(res.rows) ? res.rows : [];
        options.forEach(item => {
          item.element = 'ts-option';
          item.label = item.name;
          item.value = item.id;
        });
        this.methodOptions = options;

        let searchOptions = [
          {
            label: '全部',
            value: '',
            element: 'ts-option'
          },
          ...options
        ];
        this.$refs.storeReceiptSearch.setMethodOptions(searchOptions);
      } catch (error) {
        this.$newMessage('error', error.message || '获取入库方式失败!');
        this.methodOptions = [];
      }
    }
  }
};
</script>

<style scoped lang="scss">
.material-store-manage-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .el-radio-group {
      label {
        margin-right: 4px;
      }
    }
    .type-tabs-container .el-tabs__item {
      width: auto !important;
    }

    .type-tabs-container .el-tabs__nav-wrap::after {
      height: 0;
    }
    .base-date-range-picker {
      display: inline-flex;
      align-items: center;
      .range-separator {
        margin: 0 4px;
      }
      .date-picker {
        display: flex;
        align-items: center;
        background: transparent;
        width: 140px !important;
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }

  .receipt-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid#295cf9;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }
}
</style>
