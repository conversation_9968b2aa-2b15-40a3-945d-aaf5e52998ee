export const materialInventoryDic = {
  1: 'store',
  2: 'outbound',
  3: 'returnGoods',
  4: 'returnStore'
};

export const materialPrintDic = {
  1: '入库验收单',
  2: '出库单',
  3: '退货单',
  4: '退库单'
};

export const baseMaterialDictionaryColumns = [
  {
    label: '',
    render: 'checkbox',
    prop: 'isCheck',
    width: 50
  },
  {
    label: '序号',
    prop: 'index',
    render: 'index',
    align: 'center',
    width: 50
  },
  {
    label: '物资编码',
    prop: 'flowNo',
    render: 'text',
    align: 'center',
    width: 160
  },
  {
    prop: 'name',
    label: '物资名称',
    align: 'center',
    render: 'text',
    width: 150,
    requiredIcon: true
  },
  {
    prop: 'categoryName',
    label: '物资分类',
    align: 'center',
    render: 'text',
    width: 135
  },
  {
    prop: 'model',
    label: '规格型号',
    align: 'center',
    render: 'text',
    width: 135
  },
  {
    prop: 'unitShow',
    label: '单位',
    align: 'center',
    render: 'text',
    width: 95
  }
];

export const baseMaterialDictionaryColumns1 = [
  {
    prop: 'manufacturerName',
    label: '生产厂家',
    align: 'center',
    render: 'text',
    width: 135
  },
  {
    prop: 'regNo',
    label: '注册证号',
    align: 'center',
    render: 'text',
    width: 135
  },
  {
    prop: 'brand',
    label: '品牌',
    align: 'center',
    render: 'text',
    width: 135
  },
  {
    prop: 'remark',
    label: '备注',
    align: 'center',
    render: 'auto-height-input',
    maxlength: 255,
    placeholder: '请输入备注',
    width: 145
  }
];
