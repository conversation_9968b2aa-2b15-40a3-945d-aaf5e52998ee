<template>
  <vxe-modal
    className="dialog-add-monthly-settlement-processing"
    v-model="visible"
    width="560"
    title="月结处理"
    showFooter
  >
    <template #default>
      <div class="tips-container">
        <p>说明</p>
        <p>1、库房月结是根据上月結存，本月出入库数据，计算本月結存</p>
        <p>2、开始月结前请确认本月的名类单据己经車核处理完成</p>
        <p>3、月結会根据库房各物资分类分别进行结账</p>
        <p>4、月结后，可以再次选择结账月份，进行取消月結</p>
      </div>

      <ts-form ref="form" :model="form" labelWidth="110px">
        <ts-form-item prop="whIdList" label="库房级别" :rules="rules.required">
          <ts-select
            v-model="form.whIdList"
            placeholder="请选择库房级别"
            style="width: 100%;"
            filterable
            clearable
            multiple
          >
            <ts-option
              v-for="item in warehouseList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </ts-select>
        </ts-form-item>

        <ts-form-item prop="id" label="结账月份" :rules="rules.required">
          <ts-select
            v-model="form.id"
            placeholder="请选择结账月份"
            style="width: 100%;"
            filterable
            clearable
          >
            <ts-option
              v-for="item in accountingPeriodList"
              :key="item.itemValue"
              :label="item.itemName"
              :value="item.itemValue"
            />
          </ts-select>
        </ts-form-item>

        <ts-form-item label="起止时间">
          {{ startEndTime }}
        </ts-form-item>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.remark"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
            placeholder="请输入内容"
          />
        </ts-form-item>
      </ts-form>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="() => submit('1')">
          开始结账
        </ts-button>
        <ts-button type="primary" @click="() => submit('0')">
          取消月结
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      type: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      warehouseList: [],
      accountingPeriodList: []
    };
  },

  computed: {
    isAdd() {
      return this.type === 'add';
    },

    isEdit() {
      return this.type === 'edit';
    },

    startEndTime() {
      let find = this.accountingPeriodList.find(
        item => item.itemValue === this.form.id
      );
      if (!find) {
        return '';
      }

      return `${find.startDate} 至 ${find.endDate}`;
    }
  },

  methods: {
    async open({ data, type }) {
      await Promise.all([
        this.handleGetAllWarehouseList(),
        this.handleGetAccountingPeriodList()
      ]);
      this.type = type;

      if (this.isAdd) {
        let find = this.accountingPeriodList.find(f => {
          return (
            f.y === this.$dayjs().format('YYYY') &&
            f.ym ===
              this.$dayjs()
                .subtract(1, 'month')
                .format('MM')
          );
        });

        this.$set(this, 'form', {
          whIdList: [],
          id: find?.itemValue || '',
          remark: ''
        });
      } else {
        this.$set(this, 'form', deepClone(data));
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    // 获取所有库房
    async handleGetAllWarehouseList() {
      try {
        let parentStoreInfo = this.$getParentStoreInfo();
        let { employeeNo } = parentStoreInfo?.userInfo || {};

        const res = await this.ajax.materialWarehouseListByEmp(employeeNo);
        if (!res.success) {
          this.$newMessage('error', res.message || '获取库房列表失败');
          return;
        }
        let data = res.object || {};
        this.warehouseList = Array.isArray(data.rows) ? data.rows : [];
      } catch (e) {
        this.$newMessage && this.$newMessage('error', '获取库房列表失败');
        this.warehouseList = [];
      }
    },

    // 获取会记期间
    async handleGetAccountingPeriodList() {
      let data = {
        pageNo: 1,
        pageSize: 10000
      };
      const res = await this.ajax.getMaterialAccountingPeriodList(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '方法数据获取失败!');
        return;
      }
      this.accountingPeriodList = Array.isArray(res.rows) ? res.rows : [];

      this.accountingPeriodList.forEach(item => {
        item.itemValue = item.id;
        item.itemName = `${item.y}年${item.ym}月`;
      });
    },

    async submit(stat) {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.form);
        formData.stat = stat;

        this.submitLoading = true;
        const res = await this.ajax.monthlySettlementSettle(formData);
        if (!res.success) {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `操作失败!`);
          return;
        }

        this.submitLoading = false;
        this.$newMessage('success', `操作成功`);
        this.$emit('submit');
        this.close();
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-monthly-settlement-processing {
  .tips-container {
    > p {
      color: #333;
      margin-bottom: 4px;
      padding-left: 50px;
    }
  }

  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 140px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
