<template>
  <div class="inventory-query-container">
    <div class="inventory-query-left">
      <select-material-warehouse
        v-model="selectMaterialWarehouseId"
        ref="selectMaterialWarehouse"
        @input="handleSelectMaterialWarehouse"
      />
      <com-material-dictionary-tree
        ref="comMaterialDictionaryTree"
        v-model="selectMaterialDictionaryId"
        :warehouseId="selectMaterialWarehouseId"
        @change="handleSelectMaterialDictionary"
      />
    </div>

    <div class="inventory-query-right">
      <ts-search-bar-new
        v-model="searchForm"
        :formList="searchList"
        @search="search"
        @reset="reset"
      >
        <template slot="queryType">
          <ts-radio-group v-model="searchForm.queryType" @change="search">
            <ts-radio label="batch">按批次明细</ts-radio>
            <ts-radio label="stock">库房汇总</ts-radio>
          </ts-radio-group>
        </template>
        <template slot="right">
          <ts-button type="primary" @click="handleExport">导出</ts-button>
        </template>
      </ts-search-bar-new>

      <ts-vxe-base-table
        id="table_inventory_query"
        ref="table"
        :columns="columns"
        @refresh="handleTableSearch"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import moment from 'moment';
import SelectMaterialWarehouse from '@/views/moduleMaterial/basicSetting/components/select-material-warehouse.vue';
import ComMaterialDictionaryTree from './components/com-material-dictionary-tree.vue';
export default {
  components: {
    SelectMaterialWarehouse,
    ComMaterialDictionaryTree
  },
  name: 'InventoryQuery',
  data() {
    return {
      selectMaterialWarehouseId: '',
      selectMaterialDictionaryId: [],

      searchForm: {
        queryType: 'batch',
        name: ''
      },
      allId: '99999',
      searchList: [
        {
          label: '查询方式',
          value: 'queryType'
        },
        {
          label: '物资名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ]
    };
  },

  computed: {
    isBatch() {
      return this.searchForm.queryType === 'batch';
    },
    columns() {
      let arr = [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 50,
          align: 'center'
        },
        {
          label: '物资编码',
          align: 'center',
          prop: 'flowNo',
          width: 160
        },
        {
          label: '物资名称',
          align: 'center',
          prop: 'name',
          minWidth: 150
        },
        {
          prop: 'categoryName',
          label: '物资分类',
          align: 'center',
          width: 135
        },
        {
          label: '规格型号',
          align: 'center',
          prop: 'model',
          minWidth: 120
        },
        { label: '单位', align: 'center', prop: 'unitShow', width: 70 },
        {
          label: '单价(元)',
          align: 'right',
          prop: 'price',
          width: 140,
          render: (h, { row }) => {
            let price = this.isBatch ? row.batchPrice : row.price;
            return h('span', null, this.handleTsNumToLocaleStr(price));
          }
        },
        {
          label: '库存数量',
          align: 'center',
          prop: 'stock',
          width: 80
        },
        {
          label: '金额(元)',
          align: 'right',
          prop: 'totalAmt',
          width: 140,
          render: (h, { row }) => {
            return h('span', null, this.handleTsNumToLocaleStr(row.totalAmt));
          }
        },
        {
          label: '入库日期',
          align: 'center',
          prop: 'inDate',
          width: 100,
          render: (h, { row }) => {
            return h(
              'span',
              null,
              row.inDate ? moment(row.inDate).format('YYYY-MM-DD') : ''
            );
          }
        },
        {
          label: '批次号',
          align: 'center',
          prop: 'batchNo',
          width: 135
        },
        { label: '供应商', align: 'center', prop: 'supplyName', minWidth: 140 },
        {
          label: '生产厂家',
          align: 'center',
          prop: 'manufacturerName',
          width: 140
        },
        {
          label: '生产批号',
          prop: 'prodNo',
          width: 100,
          align: 'center'
        },
        {
          label: '生产日期',
          prop: 'prodDate',
          align: 'center',
          width: 100,
          render: (h, { row }) => {
            return h(
              'span',
              null,
              row.prodDate ? moment(row.prodDate).format('YYYY-MM-DD') : ''
            );
          }
        },
        {
          label: '失效日期',
          prop: 'expireDate',
          align: 'center',
          width: 100,
          render: (h, { row }) => {
            return h(
              'span',
              null,
              row.expireDate ? moment(row.expireDate).format('YYYY-MM-DD') : ''
            );
          }
        },
        {
          prop: 'regNo',
          label: '注册证号',
          align: 'center',
          width: 135
        },
        { label: '品牌', align: 'center', prop: 'brand', minWidth: 140 }
      ];

      // 按库存 不显示批次号
      if (!this.isBatch) {
        let filterArr = [
          'batchNo',
          'inbDate',
          'prodDate',
          'expireDate',
          'prodNo'
        ];
        arr = arr.filter(item => !filterArr.includes(item.prop));
      }
      return arr;
    },
    ...mapGetters('material', ['handleTsNumToLocaleStr'])
  },

  methods: {
    refresh() {
      // 1.获取库房列表
      this.$refs.selectMaterialWarehouse.handleRefreshTable();
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleTableSearch();
    },

    reset() {
      this.$set(this, 'searchForm', {
        queryType: 'batch',
        name: ''
      });
    },

    async handleTableSearch() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let data = {
        ...this.searchForm,
        pageNo,
        pageSize,
        status: '1',
        warehouseId: this.selectMaterialWarehouseId,
        categoryIdList: this.selectMaterialDictionaryId
          .map(item => item.id)
          .join(',')
      };
      if (data.categoryIdList.includes(this.allId)) {
        data.categoryIdList = '';
      }

      let res = await this.ajax.getMaterialSkuStock(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '表格数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, index) => {
        return {
          ...item,
          pageIndex: index + 1 + (pageNo - 1) * pageSize
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows: this.formatRows(rows)
      });
    },

    formatRows(rows) {
      // 物资出库 物资退货
      // 按批次 主键是ID*批次号
      // 生产日期 失效日期 格式化
      rows.forEach(item => {
        if (this.isBatch) {
          item.id = `${item.id}*${item.batchNo}`;
        }
      });

      return rows;
    },

    handleSelectMaterialWarehouse(val) {
      if (!val) {
        return;
      }

      this.$nextTick(() => {
        this.$refs.comMaterialDictionaryTree.refreshTree();
        this.handleTableSearch();
      });
    },

    handleSelectMaterialDictionary() {
      this.handleTableSearch();
    },

    handleExport() {
      let stockUrl = '/ts-ams/api/material/sku/stock/export/stock';
      let batchUrl = '/ts-ams/api/material/sku/stock/export/batch';
      let url = this.isBatch ? batchUrl : stockUrl;

      let aDom = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });

      conditionList.push(`status=1`);
      conditionList.push(`warehouseId=${this.selectMaterialWarehouseId}`);
      conditionList.push(
        `categoryIdList=${this.selectMaterialDictionaryId
          .map(item => item.id)
          .join(',')}`
      );
      aDom.href = url + '?' + conditionList.join('&');
      aDom.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-query-container {
  padding: 8px;
  background: #fff;
  gap: 8px;
  display: flex;
  height: 100%;
  .el-radio-group {
    label {
      margin-right: 4px;
    }
  }
  .inventory-query-left {
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    .select-material-warehouse {
      flex: 1;
      border: 1px solid#295cf9;
      border-radius: 3px;
      overflow: hidden;
    }
    .search-tree-box {
      flex: 1;
      border: 1px solid#295cf9;
      border-radius: 3px;
      overflow: hidden;
    }
  }
  .inventory-query-right {
    padding: 12px 8px;
    border-radius: 3px;
    border: 1px solid#295cf9;
    flex: 1;
    height: 100%;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }
}
</style>
