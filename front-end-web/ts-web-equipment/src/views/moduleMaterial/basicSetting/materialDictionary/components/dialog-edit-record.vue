<template>
  <vxe-modal
    className="dialog-edit-record"
    width="75%"
    height="80%"
    title="操作记录"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-search-bar-new
          v-model="searchForm"
          :resetData="resetData"
          :formList="searchList"
          @search="search"
        />
        <ts-vxe-base-table
          class="form-table"
          id="table_edit_record"
          ref="table"
          :columns="columns"
          @refresh="handleRefreshTable"
        />
      </div>
    </template>

    <template #footer>
      <ts-button class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '物资编码',
          value: 'rowPkFlowNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资编码'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '物资名称',
          value: 'rowName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '修改类型',
          value: 'rowModifyType',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择修改类型',
            filterable: true,
            clearable: true
          },
          childNodeList: []
        }
      ],

      columns: [
        { label: '序号', prop: 'pageIndex', width: 50, align: 'center' },
        {
          label: '物资编码',
          align: 'center',
          prop: 'rowPkFlowNo',
          width: 160
        },
        {
          label: '物资名称',
          align: 'center',
          prop: 'rowName',
          width: 140,
          showOverflow: false
        },
        {
          label: '修改类型',
          align: 'center',
          prop: 'rowModifyTypeShow',
          width: 100
        },
        {
          label: '修改明细',
          align: 'center',
          prop: 'rowDiff',
          minWidth: 150,
          showOverflow: false,
          render: (h, { row }) => {
            const html = (row.rowDiff || '').replace(/\n/g, '<br>');
            return h('div', {
              domProps: {
                innerHTML: html
              }
            });
          }
        },
        { label: '修改人', align: 'center', prop: 'createUser', width: 80 },
        { label: '修改时间', align: 'center', prop: 'createDate', width: 155 }
      ]
    };
  },
  methods: {
    async open(rowPkFlowNo) {
      this.$set(this, 'searchForm', {
        rowPkFlowNo,
        rowName: '',
        rowModifyType: ''
      });
      this.$set(this, 'resetData', {
        rowPkFlowNo: '',
        rowName: '',
        rowModifyType: ''
      });

      this.ajax.getDataByDataLibrary('AMS_DATA_MODIFY_TYPE').then(res => {
        if (!res.success) {
          this.$newMessage('error', res.message || '获取修改类型列表失败!');
          return;
        }
        this.searchList[2].childNodeList = (res.object || []).map(item => {
          return {
            label: item.itemName,
            value: item.itemNameValue,
            element: 'ts-option'
          };
        });

        this.searchList[2].childNodeList.unshift({
          label: '全部',
          value: '',
          element: 'ts-option'
        });
      });

      this.visible = true;
      this.$nextTick(() => {
        this.search();
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          tableName: 'm_sku'
        };
      Object.keys(data).map(key => {
        if (data[key] === null || data[key] === undefined) {
          delete data[key];
        }
      });

      this.ajax.materialModifyLogList(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-edit-record {
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
    }
  }
}
</style>
