<template>
  <div class="template-table-box">
    <div class="template-name"></div>
    <div class="template-table">
      <table>
        <tbody>
          <tr>
            <td><div>科室名称</div></td>
            <td>
              <div>{{ tableData.deptName }}</div>
            </td>
            <td><div>科室用户</div></td>
            <td :colspan="2">
              <div>{{ tableData.deptUser }}</div>
            </td>
          </tr>
          <tr>
            <td><div>设备名称</div></td>
            <td>
              <div>{{ tableData.deviceName }}</div>
            </td>
            <td><div>设备型号</div></td>
            <td :colspan="2">
              <div>{{ tableData.deviceModel }}</div>
            </td>
          </tr>
          <tr>
            <td><div>系统编号</div></td>
            <td>
              <div>{{ tableData.systemNo }}</div>
            </td>
            <td><div>设备序列号</div></td>
            <td :colspan="2">
              <div>{{ tableData.deviceSerialNo }}</div>
            </td>
          </tr>
          <tr>
            <td><div>二维码编号</div></td>
            <td>
              <div>{{ tableData.qrCodeNo }}</div>
            </td>
            <td><div>设备编号</div></td>
            <td :colspan="2">
              <div>{{ tableData.deviceNo }}</div>
            </td>
          </tr>
          <tr>
            <td><div>计划执行人</div></td>
            <td :colspan="4">
              <div>
                <ts-input
                  v-if="isTemplate || isReadonly"
                  v-model="tableData.planExecutor"
                ></ts-input>
              </div>
            </td>
          </tr>
          <tr>
            <td><div>保养工程师</div></td>
            <td>
              <div>保养开始时间</div>
            </td>
            <td>
              <div>保养结束时间</div>
            </td>
            <td><div>保养工时</div></td>
            <td><div>旅行时间</div></td>
          </tr>
          <tr
            v-for="(item, index) in tableData.maintenanceList"
            :key="`maintenance_${index}`"
          >
            <td>
              <div>{{ item.maintenanceEngineer }}</div>
            </td>
            <td>
              <div>{{ item.maintenanceStartDate }}</div>
            </td>
            <td>
              <div>{{ item.maintenanceEndDate }}</div>
            </td>
            <td>
              <div>{{ item.maintenanceHours }}</div>
            </td>
            <td>
              <div>{{ item.travelTime }}</div>
            </td>
          </tr>
          <tr>
            <td><div>保养项目</div></td>
            <td :colspan="4">
              <div>保养内容</div>
            </td>
          </tr>
          <tr
            v-for="(item, index) in tableData.maintenanceProjectList"
            :key="`maintenanceProject_${index}`"
          >
            <td v-if="item.rowspan" :rowspan="item.rowspan">
              <div>
                <ts-input
                  v-model="item.projectName"
                  :readonly="!isTemplate"
                ></ts-input>
                <span
                  v-if="index == 0 && isTemplate"
                  class="addImg-left"
                  @click="handleAddProject"
                >
                  + 添加新项目
                </span>
                <span
                  v-if="index != 0 && isTemplate"
                  class="delImg-left"
                  @click="handleDelProject(item, index)"
                >
                  - 删除项目
                </span>
              </div>
            </td>
            <td :colspan="2">
              <div v-if="isTemplate">
                <ts-radio-group v-model="item.optionType1">
                  <ts-radio label="0">选择</ts-radio>
                  <ts-radio label="1">输入框</ts-radio>
                </ts-radio-group>
              </div>
              <div>
                <el-checkbox v-model="item.checked1" :disabled="isTemplate">
                  <ts-input
                    v-model="item.label1"
                    :readonly="!isTemplate"
                  ></ts-input>
                </el-checkbox>
              </div>
            </td>
            <td :colspan="2">
              <div v-if="isTemplate">
                <ts-radio-group v-model="item.optionType2">
                  <ts-radio label="0">选择</ts-radio>
                  <ts-radio label="1">输入框</ts-radio>
                </ts-radio-group>
              </div>
              <div>
                <el-checkbox v-model="item.checked2" :disabled="isTemplate">
                  <ts-input
                    v-model="item.label2"
                    :readonly="!isTemplate"
                  ></ts-input>
                </el-checkbox>
              </div>
              <span
                v-if="item.trIndex == 0 && isTemplate"
                class="addImg-right"
                @click="handleAddOption(item, index)"
              >
                + 添加新选项
              </span>
              <span
                v-if="item.trIndex != 0 && isTemplate"
                class="delImg-right"
                @click="handleDelOption(item, index)"
              >
                - 删除该行
              </span>
            </td>
          </tr>
          <tr>
            <td :rowspan="tableData.maintenanceAccessoryList.length + 1">
              <div>保养配件信息</div>
            </td>
            <td>
              <div>配件名称</div>
            </td>
            <td>
              <div>配件货号</div>
            </td>
            <td><div>配件序列号</div></td>
            <td><div>配件数量</div></td>
          </tr>
          <tr
            v-for="(item, index) in tableData.maintenanceAccessoryList"
            :key="`maintenanceAccessory_${index}`"
          >
            <td>
              <div>
                <ts-input
                  v-if="!isTemplate"
                  v-model="item.accessoryName"
                  :readonly="isTemplate || isReadonly"
                ></ts-input>
              </div>
            </td>
            <td>
              <div>
                <ts-input
                  v-if="!isTemplate"
                  v-model="item.accessoryArticleNo"
                  :readonly="isTemplate || isReadonly"
                ></ts-input>
              </div>
            </td>
            <td>
              <div>
                <ts-input
                  v-if="!isTemplate"
                  v-model="item.accessorySerialNo"
                  :readonly="isTemplate || isReadonly"
                ></ts-input>
              </div>
            </td>
            <td>
              <div>
                <ts-input
                  v-if="!isTemplate"
                  v-model="item.accessoryNum"
                  :readonly="isTemplate || isReadonly"
                ></ts-input>
                <span
                  class="addImg-right"
                  @click="handleAddAccessory"
                  v-if="index == 0 && !isTemplate && !isReadonly"
                >
                  + 添加新配件
                </span>
                <span
                  class="delImg-right"
                  @click="handleDelAccessory(item, index)"
                  v-if="index != 0 && !isTemplate && !isReadonly"
                >
                  - 删除配件
                </span>
              </div>
            </td>
          </tr>
          <tr>
            <td><div>设备保养后工况</div></td>
            <td :colspan="4">
              <div>
                <ts-radio-group
                  v-model="tableData.workingCondition"
                  :disabled="isTemplate || isReadonly"
                >
                  <ts-radio label="0">正常使用</ts-radio>
                  <ts-radio label="1">可以使用,部分功能失效</ts-radio>
                  <ts-radio label="2">无法使用,故障维修</ts-radio>
                  <ts-radio label="3">无法维修,待报废</ts-radio>
                </ts-radio-group>
              </div>
            </td>
          </tr>
          <tr>
            <td><div>服务满意度</div></td>
            <td :colspan="4">
              <div>
                <ts-radio-group
                  v-model="tableData.serviceSatisfaction"
                  :disabled="isTemplate || isReadonly"
                >
                  <ts-radio label="0">非常满意</ts-radio>
                  <ts-radio label="1">比较满意</ts-radio>
                  <ts-radio label="2">一般</ts-radio>
                  <ts-radio label="3">不满意</ts-radio>
                  <ts-radio label="4">非常不满意</ts-radio>
                </ts-radio-group>
              </div>
            </td>
          </tr>
          <tr>
            <td><div>备注内容</div></td>
            <td :colspan="4">
              <div>
                <ts-input
                  v-if="!isTemplate"
                  v-model="tableData.remark"
                ></ts-input>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="maintain-name">
        <span>科室签字：<img /></span>
      </div>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    editTableData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'addTemplate'
    }
  },
  data() {
    return {
      tableData: {}
    };
  },
  watch: {
    editTableData: {
      handler(val) {
        this.initTableData();
      },
      immediate: true
    }
  },
  computed: {
    isTemplate() {
      return this.type == 'addTemplate' || this.type == 'editTemplate';
    },
    isReadonly() {
      return this.type == 'check';
    }
  },
  methods: {
    initTableData() {
      let projectList = [],
        accessoryList = [];
      if (this.type == 'addTemplate') {
        projectList.push({
          projectName: '目视检查',
          projectOptions: [
            {
              optionType: '0',
              checked: false,
              optionContent: ''
            },
            {
              optionType: '0',
              checked: false,
              optionContent: ''
            }
          ]
        });
        accessoryList.push({
          accessoryName: '',
          accessoryArticleNo: '',
          accessorySerialNo: '',
          accessoryNum: ''
        });
      } else {
        projectList = deepClone(this.editTableData.maintenanceProjectList);
        accessoryList = deepClone(this.editTableData.maintenanceAccessoryList);
      }
      let maintenanceProjectList = [];
      projectList.forEach(item => {
        let optionArr = item.projectOptions,
          trList = [];
        for (let i = 0; i < optionArr.length; i += 2) {
          //选项进行两两分组
          let group = [optionArr[i]];
          if (i + 1 < optionArr.length) {
            group.push(optionArr[i + 1]);
          }
          trList.push(group);
        }
        trList.forEach((tdItem, index) => {
          let trData = {
            projectName: item.projectName,
            trIndex: index
          };
          if (index == 0) trData.rowspan = trList.length;
          if (tdItem.length == 2) {
            maintenanceProjectList.push({
              ...trData,
              ...{
                optionType1: tdItem[0].optionType,
                checked1: tdItem[0].checked,
                optionContent1: tdItem[0].optionContent,
                optionType2: tdItem[1].optionType,
                checked2: tdItem[1].checked,
                optionContent2: tdItem[1].optionContent
              }
            });
          } else {
            maintenanceProjectList.push(
              ...trData,
              ...{
                optionType1: tdItem[0].optionType,
                checked1: tdItem[0].checked,
                optionContent1: tdItem[0].optionContent,
                optionType2: '0',
                checked2: false,
                optionContent2: ''
              }
            );
          }
        });
      });

      this.tableData = {
        ...this.editTableData,
        maintenanceProjectList,
        maintenanceAccessoryList: accessoryList
      };
    },
    handleAddProject() {
      this.tableData.maintenanceProjectList.push({
        projectName: '',
        trIndex: 0,
        rowspan: 1,
        optionType1: '0',
        checked1: false,
        optionContent1: '',
        optionType2: '0',
        checked2: false,
        optionContent2: ''
      });
    },
    handleDelProject(item, index) {
      this.tableData.maintenanceProjectList.splice(index, item.rowspan);
    },
    handleAddOption(item, index) {
      this.tableData.maintenanceProjectList.splice(index + 1, 0, {
        projectName: '',
        trIndex: item.rowspan,
        optionType1: '0',
        checked1: false,
        optionContent1: '',
        optionType2: '0',
        checked2: false,
        optionContent2: ''
      });
      this.tableData.maintenanceProjectList[index].rowspan += 1;
    },
    handleDelOption(item, index) {
      let firstTrIndex = this.findIndexBefore(
        this.tableData.maintenanceProjectList,
        index
      );
      this.tableData.maintenanceProjectList.splice(index, 1);

      this.tableData.maintenanceProjectList[firstTrIndex].rowspan -= 1;
    },
    findIndexBefore(arr, fromIndex) {
      if (fromIndex >= arr.length) {
        return -1;
      }
      // 从前往后遍历数组，直到找到符合条件的元素或遍历完成
      for (let i = fromIndex - 1; i >= 0; i--) {
        if (arr[i].rowspan) {
          return i;
        }
      }
      return -1;
    },
    handleAddAccessory() {
      this.tableData.maintenanceAccessoryList.push({
        accessoryName: '',
        accessoryArticleNo: '',
        accessorySerialNo: '',
        accessoryNum: ''
      });
    },
    handleDelAccessory(item, index) {
      this.tableData.maintenanceAccessoryList.splice(index, 1);
    }
  }
};
</script>

<style lang="scss" scoped>
.template-name {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}
.template-table {
  width: 48vw;
  margin: 0 auto;
  table {
    width: 100% !important;
    border: 1px solid #333;
    tr {
      min-height: 28px;
    }
    td {
      padding: 4px 6px;
      position: relative;
      border-top: 1px solid #333;
      border-right: 1px solid #333;
      div {
        color: #333;
        min-height: 24px;
      }
      .addImg-left,
      .delImg-left,
      .addImg-right,
      .delImg-right {
        position: absolute;
        display: inline-block;
        top: 50%;
        margin-top: -10px;
        cursor: pointer;
        text-align: center;
        color: #fff;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
      }
      .addImg-left {
        background: url(../../../../assets/img/icon-Excle.png) top no-repeat;
        left: -100px;
        background-position: 0vw -0.4vw;
        background-size: 13.54vw;
        width: 100px;
      }
      .delImg-left {
        background: url(../../../../assets/img/icon-Excle.png) top no-repeat;
        left: -90px;
        background-position: -0.3vw -4.6vw;
        background-size: 13.54vw;
        width: 90px;
      }
      .addImg-right {
        background: url(../../../../assets/img/icon-Excle.png) top no-repeat;
        right: -100px;
        background-position: -0.3vw -2.5vw;
        background-size: 13.54vw;
        width: 100px;
      }
      .delImg-right {
        background: url(../../../../assets/img/icon-Excle.png) top no-repeat;
        right: -90px;
        background-position: -0.3vw -6.7vw;
        background-size: 13.54vw;
        width: 90px;
      }
    }
  }
  .maintain-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    padding-top: 10px;
  }
}
</style>
