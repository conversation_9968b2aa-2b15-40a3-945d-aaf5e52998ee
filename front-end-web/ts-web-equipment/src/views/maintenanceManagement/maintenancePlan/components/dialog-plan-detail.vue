<template>
  <ts-dialog
    custom-class="dialog-plan-detail-box"
    :title="actionType == 'add' ? '新增' : '编辑'"
    :visible.sync="visible"
    :append-to-body="true"
    fullscreen
    @close="handleCancel"
  >
    <p class="title">基础信息</p>
    <ts-form ref="form" :model="form">
      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="计划名称"
            prop="maintenancePlanName"
            :rules="rules.required"
          >
            <ts-input
              v-model="form.name"
              clearable
              placeholder="请输入计划名称"
            ></ts-input>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="开始时间"
            prop="startDate"
            :rules="rules.required"
          >
            <ts-date-picker
              v-model="form.startAt"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            ></ts-date-picker>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item label="结束时间" prop="endDate" :rules="rules.required">
            <ts-date-picker
              v-model="form.endAt"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            ></ts-date-picker>
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="保养级别"
            prop="maintenanceLevel"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="form.level"
              placeholder="请选择保养级别"
            >
              <ts-option
                v-for="item in maintenanceLevel"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            class="form-item"
            label="执行人"
            prop="implementer"
            :rules="rules.required"
          >
            <ts-select
              v-model="form.implementerName"
              placeholder="请选择执行人"
              multiple
              clearable
              style="width: 100%"
            >
              <ts-option
                v-for="item in engineerList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-form-item
        label="计划描述"
        prop="planDescription"
        :rules="rules.required"
      >
        <ts-input
          v-model="form.planDescription"
          type="textarea"
          class="textarea"
          maxlength="200"
          show-word-limit
          placeholder="请输入,200字以内"
        />
      </ts-form-item>
    </ts-form>
    <p class="title">
      保养设备
      <ts-button type="primary" @click="showDialogEquipmentList">
        选择设备
      </ts-button>
    </p>
    <ts-vxe-base-table
      id="table_maintenance_equipment"
      ref="table"
      minHeight="100%"
      :columns="columns"
    />
    <template slot="footer">
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        关 闭
      </ts-button>
    </template>
    <ts-user-dept-select ref="userSelect" @ok="handleSaveUserSelect" />
    <DialogEquipmentList
      ref="dialogEquipmentList"
      @change="handleEquipmentChange"
    />
  </ts-dialog>
</template>
<script>
import { maintenanceLevel } from '@/assets/js/constants.js';
import { deepClone } from '@/unit/commonHandle.js';
import TsUserDeptSelect from '@/components/ts-user-dept-select/index.vue';
import DialogEquipmentList from '../../../dialog-equipment-list/index.vue';

export default {
  components: {
    TsUserDeptSelect,
    DialogEquipmentList
  },
  data() {
    return {
      visible: false,
      actionType: 'add',
      submitLoading: false,
      maintenanceLevel: maintenanceLevel,
      engineerList: [],
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '设备编号',
          align: 'center',
          prop: 'uniqueNo',
          width: 160
        },
        {
          label: '资产编码',
          align: 'center',
          width: 160,
          prop: 'assetCode'
        },
        {
          label: '设备名称',
          align: 'center',
          width: 120,
          prop: 'name'
        },
        {
          label: '规格型号',
          align: 'center',
          width: 110,
          prop: 'model'
        },
        {
          label: '品牌',
          align: 'center',
          width: 120,
          prop: 'brandName'
        },
        {
          label: '厂家',
          align: 'center',
          width: 140,
          prop: 'manufacturerName'
        },
        {
          label: '设备分类',
          align: 'center',
          width: 140,
          prop: 'category22Name'
        },
        {
          label: '所属科室',
          align: 'left',
          width: 120,
          prop: 'belongToOrgName'
        },
        {
          label: '位置',
          align: 'left',
          width: 140,
          prop: 'loc'
        },
        {
          label: '状态',
          align: 'center',
          width: 120,
          prop: 'status'
        },
        {
          label: '操作',
          fixed: 'right',
          width: 50,
          render: (h, { row }) => {
            let actions = [
              {
                label: '移除',
                event: this.handleDel
              }
            ];
            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions: actions
                }
              },
              this.$slots.default
            );
          }
        }
      ]
    };
  },
  methods: {
    async show({ data = {} }) {
      this.form = deepClone(data);
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
      this.getEngineerList();
    },
    getEngineerList() {
      const data = { itemSet: '1' };
      this.ajax.getEngineerList(data).then(res => {
        if (res.success && res.statusCode === 200) {
          this.engineerList = res.object.map(item => {
            return {
              value: item.id,
              label: item.name
            };
          });
        }
      });
    },
    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        return;
        this.ajax.dealMeasurementAbnormal(data).then(res => {
          this.submitLoading = false;
          if (res.success && res.statusCode === 200) {
            this.$message.success(res.message || '操作成功');
            this.handleCancel();
            this.$emit('submit');
          } else {
            this.$message.error(res.message || '操作失败');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      }
    },
    handleOpenUserSelect(key, isRadio = false) {
      let names = this.form[key + 'Name']?.split(',') || [],
        ids = this.form[key]?.split(',') || [],
        empList = [];
      names.map((name, index) => {
        empList.push({
          empCode: ids[index],
          empName: name
        });
      });
      this.$refs.userSelect.open(key, {
        empList,
        appendToBody: true,
        showCheckbox: false,
        isRadio
      });
    },
    handleSaveUserSelect(res) {
      Object.keys(res).map(key => {
        let empList = res[key].empList || [];
        this.$set(
          this.form,
          key + 'Name',
          empList.map(item => item.empName).join(',')
        );
        this.$set(this.form, key, empList.map(item => item.empCode).join(','));
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      });
    },
    showDialogEquipmentList() {
      this.$refs.dialogEquipmentList.show();
    },
    handleEquipmentChange(row) {},
    handleDel(row) {},
    handleCancel() {
      this.submitLoading = false;
      this.visible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .dialog-plan-detail-box .el-dialog__body {
  width: 1280px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dialog-plan-detail .title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.person-icon {
  margin-top: 3px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}
</style>
