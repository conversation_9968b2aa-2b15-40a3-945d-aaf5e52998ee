#!/bin/bash

# 编译测试类
echo "编译测试类..."
cd /Users/<USER>/Data/trasen/projects/apps/ts-ams

# 创建临时目录
mkdir -p temp-test

# 编译测试类
javac -cp "target/classes:$(find ~/.m2/repository -name "*.jar" | grep -E "(jsqlparser|junit)" | head -5 | tr '\n' ':')" \
    -d temp-test \
    src/test/java/cn/trasen/ams/common/interceptor/SqlPreprocessorTest.java

# 运行测试
echo "运行测试..."
java -cp "temp-test:target/classes:$(find ~/.m2/repository -name "*.jar" | grep -E "(jsqlparser)" | head -3 | tr '\n' ':')" \
    cn.trasen.ams.common.interceptor.SqlPreprocessorTest

# 清理
rm -rf temp-test

echo "测试完成"
