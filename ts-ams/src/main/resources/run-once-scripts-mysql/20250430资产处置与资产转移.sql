-- CREATE TABLE `d_transfer` (`id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键', `flow_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流水号', `sku_type` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产类型', `sku_name_set` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关资产冗余字段设计', `nums` int DEFAULT NULL COMMENT '本次转移的数量', `asset_total_value` decimal(24,2) DEFAULT NULL COMMENT '资产总价值', `note` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产转移原因', `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批状态', `do_date` datetime DEFAULT NULL COMMENT '审批时间', `do_note` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批备注', `doer_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人', `doer_name` varchar(90) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人名称', `do_file_set` text COLLATE utf8mb4_general_ci COMMENT '审批附件', `create_date` datetime DEFAULT NULL COMMENT '创建时间', `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人账号', `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称', `update_date` datetime DEFAULT NULL COMMENT '更新时间', `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人账号', `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称', `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码', `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称', `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除 Y N', `dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门ID', `dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称', PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资产转移';
SET
@exist := (
    SELECT COUNT(1)
    FROM information_schema.tables
    WHERE table_name = 'd_transfer'
      AND table_schema = database()
);
SET
@sqlstmt := IF(
    @exist = 0,
    'CREATE TABLE d_transfer(     `id` VARCHAR(50) NOT NULL  COMMENT ''主键'' ,     `flow_no` VARCHAR(50)   COMMENT ''流水号'' ,     `sku_type` VARCHAR(32)   COMMENT ''资产类型'' ,     `sku_name_set` VARCHAR(900)   COMMENT ''相关资产冗余字段设计'' ,     `nums` INT   COMMENT ''本次转移的数量'' ,     `asset_total_value` DECIMAL(24,2)   COMMENT ''资产总价值'' ,     `note` VARCHAR(900)   COMMENT ''资产转移原因'' ,     `status` VARCHAR(255)   COMMENT ''审批状态'' ,     `do_date` DATETIME   COMMENT ''审批时间'' ,     `do_note` VARCHAR(900)   COMMENT ''审批备注'' ,     `doer_id` VARCHAR(50)   COMMENT ''审批人'' ,     `doer_name` VARCHAR(90)   COMMENT ''审批人名称'' ,     `do_file_set` TEXT   COMMENT ''审批附件'' ,     `create_date` DATETIME   COMMENT ''创建时间'' ,     `create_user` VARCHAR(50)   COMMENT ''创建人账号'' ,     `create_user_name` VARCHAR(50)   COMMENT ''创建人名称'' ,     `update_date` DATETIME   COMMENT ''更新时间'' ,     `update_user` VARCHAR(50)   COMMENT ''更新人账号'' ,     `update_user_name` VARCHAR(50)   COMMENT ''更新人名称'' ,     `sso_org_code` VARCHAR(50)   COMMENT ''机构编码'' ,     `sso_org_name` VARCHAR(50)   COMMENT ''机构名称'' ,     `is_deleted` CHAR(1)   COMMENT ''是否删除 Y N'' ,     `dept_id` VARCHAR(50)   COMMENT ''部门ID'' ,     `dept_name` VARCHAR(50)   COMMENT ''部门名称'' ,     PRIMARY KEY (id) )  COMMENT = ''资产转移'''
    ,
    'SELECT ''INFO: d_transfer 表已存在.'''
);
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


-- CREATE TABLE `d_transfer_detail` (`id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键', `transfer_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主表ID', `device_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产ID', `cur_org_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '当前科室ID', `next_org_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转入后科室ID', `next_loc` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转入后放置位置', `yjr_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '移交人ID', `jsr_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接受人ID', `create_date` datetime DEFAULT NULL COMMENT '创建时间', `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人账号', `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称', `update_date` datetime DEFAULT NULL COMMENT '更新时间', `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人账号', `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称', `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码', `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称', `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除 Y N', `dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门ID', `dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称', PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资产转移明细表';

SET
@exist := (
    SELECT COUNT(1)
    FROM information_schema.tables
    WHERE table_name = 'd_transfer_detail'
      AND table_schema = database()
);
SET
@sqlstmt := IF(
    @exist = 0,
    'CREATE TABLE d_transfer_detail(     `id` VARCHAR(50) NOT NULL  COMMENT ''主键'' ,     `transfer_id` VARCHAR(50)   COMMENT ''主表ID'' ,     `device_id` VARCHAR(50)   COMMENT ''资产ID'' ,     `cur_org_id` VARCHAR(50)   COMMENT ''当前科室ID'' ,     `next_org_id` VARCHAR(50)   COMMENT ''转入后科室ID'' ,     `next_loc` VARCHAR(255)   COMMENT ''转入后放置位置'' ,     `yjr_id` VARCHAR(50)   COMMENT ''移交人ID'' ,     `jsr_id` VARCHAR(50)   COMMENT ''接受人ID'' ,     `create_date` DATETIME   COMMENT ''创建时间'' ,     `create_user` VARCHAR(50)   COMMENT ''创建人账号'' ,     `create_user_name` VARCHAR(50)   COMMENT ''创建人名称'' ,     `update_date` DATETIME   COMMENT ''更新时间'' ,     `update_user` VARCHAR(50)   COMMENT ''更新人账号'' ,     `update_user_name` VARCHAR(50)   COMMENT ''更新人名称'' ,     `sso_org_code` VARCHAR(50)   COMMENT ''机构编码'' ,     `sso_org_name` VARCHAR(50)   COMMENT ''机构名称'' ,     `is_deleted` CHAR(1)   COMMENT ''是否删除 Y N'' ,     `dept_id` VARCHAR(50)   COMMENT ''部门ID'' ,     `dept_name` VARCHAR(50)   COMMENT ''部门名称'' ,     PRIMARY KEY (id) )  COMMENT = ''资产转移明细表'''
    ,
    'SELECT ''INFO: d_transfer_detail 表已存在.'''
);
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- CREATE TABLE `d_disposal` (`id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键', `flow_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流水号', `sku_type` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产类型', `sku_name_set` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关资产冗余字段设计', `nums` int DEFAULT NULL COMMENT '本次转移的数量', `asset_total_value` decimal(24,2) DEFAULT NULL COMMENT '资产总价值', `note` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产处置原因', `status` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批状态', `do_date` datetime DEFAULT NULL COMMENT '审批时间', `do_note` varchar(900) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批备注', `doer_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人', `doer_name` varchar(90) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人名称', `do_file_set` text COLLATE utf8mb4_general_ci COMMENT '审批附件', `create_date` datetime DEFAULT NULL COMMENT '创建时间', `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人账号', `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称', `update_date` datetime DEFAULT NULL COMMENT '更新时间', `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人账号', `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称', `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码', `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称', `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除 Y N', `dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门ID', `dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称', PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资产报废';
SET
@exist := (
    SELECT COUNT(1)
    FROM information_schema.tables
    WHERE table_name = 'd_disposal'
      AND table_schema = database()
);
SET
@sqlstmt := IF(
    @exist = 0,
    'CREATE TABLE d_disposal(     `id` VARCHAR(50) NOT NULL  COMMENT ''主键'' ,     `flow_no` VARCHAR(50)   COMMENT ''流水号'' ,     `sku_type` VARCHAR(32)   COMMENT ''资产类型'' ,     `sku_name_set` VARCHAR(900)   COMMENT ''相关资产冗余字段设计'' ,     `nums` INT   COMMENT ''本次转移的数量'' ,     `asset_total_value` DECIMAL(24,2)   COMMENT ''资产总价值'' ,     `note` VARCHAR(900)   COMMENT ''资产处置原因'' ,     `status` VARCHAR(255)   COMMENT ''审批状态'' ,     `do_date` DATETIME   COMMENT ''审批时间'' ,     `do_note` VARCHAR(900)   COMMENT ''审批备注'' ,     `doer_id` VARCHAR(50)   COMMENT ''审批人'' ,     `doer_name` VARCHAR(90)   COMMENT ''审批人名称'' ,     `do_file_set` TEXT   COMMENT ''审批附件'' ,     `create_date` DATETIME   COMMENT ''创建时间'' ,     `create_user` VARCHAR(50)   COMMENT ''创建人账号'' ,     `create_user_name` VARCHAR(50)   COMMENT ''创建人名称'' ,     `update_date` DATETIME   COMMENT ''更新时间'' ,     `update_user` VARCHAR(50)   COMMENT ''更新人账号'' ,     `update_user_name` VARCHAR(50)   COMMENT ''更新人名称'' ,     `sso_org_code` VARCHAR(50)   COMMENT ''机构编码'' ,     `sso_org_name` VARCHAR(50)   COMMENT ''机构名称'' ,     `is_deleted` CHAR(1)   COMMENT ''是否删除 Y N'' ,     `dept_id` VARCHAR(50)   COMMENT ''部门ID'' ,     `dept_name` VARCHAR(50)   COMMENT ''部门名称'' ,     PRIMARY KEY (id) )  COMMENT = ''资产报废'''
    ,
    'SELECT ''INFO: d_disposal 表已存在.'''
);
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- CREATE TABLE `d_disposal_detail` (`id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键', `disposal_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主表ID', `device_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资产ID', `type` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处置类型', `cost` decimal(24,2) DEFAULT NULL COMMENT '处置费用', `revenue` decimal(24,2) DEFAULT NULL COMMENT '处置收入', `create_date` datetime DEFAULT NULL COMMENT '创建时间', `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人账号', `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称', `update_date` datetime DEFAULT NULL COMMENT '更新时间', `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人账号', `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称', `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码', `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称', `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除 Y N', `dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门ID', `dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称', PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资产处置明细表';

SET
@exist := (
    SELECT COUNT(1)
    FROM information_schema.tables
    WHERE table_name = 'd_disposal_detail'
      AND table_schema = database()
);
SET
@sqlstmt := IF(
    @exist = 0,
    'CREATE TABLE d_disposal_detail(     `id` VARCHAR(50) NOT NULL  COMMENT ''主键'' ,     `disposal_id` VARCHAR(50)   COMMENT ''主表ID'' ,     `device_id` VARCHAR(50)   COMMENT ''资产ID'' ,     `type` VARCHAR(32)   COMMENT ''处置类型'' ,     `cost` DECIMAL(24,2)   COMMENT ''处置费用'' ,     `revenue` DECIMAL(24,2)   COMMENT ''处置收入'' ,     `create_date` DATETIME   COMMENT ''创建时间'' ,     `create_user` VARCHAR(50)   COMMENT ''创建人账号'' ,     `create_user_name` VARCHAR(50)   COMMENT ''创建人名称'' ,     `update_date` DATETIME   COMMENT ''更新时间'' ,     `update_user` VARCHAR(50)   COMMENT ''更新人账号'' ,     `update_user_name` VARCHAR(50)   COMMENT ''更新人名称'' ,     `sso_org_code` VARCHAR(50)   COMMENT ''机构编码'' ,     `sso_org_name` VARCHAR(50)   COMMENT ''机构名称'' ,     `is_deleted` CHAR(1)   COMMENT ''是否删除 Y N'' ,     `dept_id` VARCHAR(50)   COMMENT ''部门ID'' ,     `dept_name` VARCHAR(50)   COMMENT ''部门名称'' ,     PRIMARY KEY (id) )  COMMENT = ''资产处置明细表'''
    ,
    'SELECT ''INFO: d_disposal_detail 表已存在.'''
);
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;