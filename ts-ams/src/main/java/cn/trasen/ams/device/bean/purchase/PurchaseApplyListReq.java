package cn.trasen.ams.device.bean.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseApplyListReq
 * @author: chenbin
 * @description: TODO
 * @date: 2025/6/21 09:55
 * @version: 1.0
 */

@Data
public class PurchaseApplyListReq {

    @ApiModelProperty(value = "忽略查询的ID", required = false)
    List<String> ignoreIds; // 忽略的申请单ID列表

    @ApiModelProperty(value = "选中的ID", required = false)
    List<String> selectedIds;

    @ApiModelProperty(value = "查询条件", required = false)
    Map<String, String> params;

    @ApiModelProperty(value = "是否匹配 1 表示匹配", required = false)
    String withMatch;

}
