package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.ams.device.bean.device.*;
import cn.trasen.ams.device.constant.DeviceConst;
import cn.trasen.ams.device.model.DeviceAttachment;
import cn.trasen.ams.device.service.DeviceAttachmentService;
import cn.trasen.ams.common.bean.ImportErrRow;
import cn.trasen.ams.common.bean.ImportResp;
import cn.trasen.ams.common.service.OrgService;
import cn.trasen.ams.common.service.RedisService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DeviceController
 * @Description 设备控制器
 * @date 2024年9月11日 上午9:14:02
 */
@RestController
@Api(tags = "DeviceController")
public class DeviceController {

    private transient static final Logger logger = LoggerFactory.getLogger(DeviceController.class);

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DeviceAttachmentService deviceAttachmentService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveDevice
     * @Description 新增
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/save")
    public PlatformResult<String> saveDevice(@Validated @RequestBody DeviceInsertReq record) {
        try {
            String id = deviceService.insert(record);
            return PlatformResult.success(id);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateDevice
     * @Description 编辑
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/update")
    public PlatformResult<String> updateDevice(@Validated @RequestBody DeviceInsertReq record) {
        try {
            deviceService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Device>
     * @Title selectDeviceById
     * @Description 根据ID查询
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/{id}")
    public PlatformResult<DeviceDetailResp> selectDeviceById(@PathVariable String id) {
        try {
            DeviceDetailResp record = deviceService.selectDetailById(id);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteDeviceById
     * @Description 根据ID删除
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/delete/{id}")
    public PlatformResult<String> deleteDeviceById(@PathVariable String id) {
        try {
            deviceService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Device>
     * @Title selectDeviceList
     * @Description 查询列表
     * @date 2024年9月11日 上午9:14:02
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @RequestMapping(value = "/api/device/list", method = {RequestMethod.GET, RequestMethod.POST})
    public DataSet<DeviceExtResp> selectDeviceList(Page page, DeviceListReq record) {
        return deviceService.getDataSetList(page, record);
    }


    @ApiOperation(value = "资产选择", notes = "资产选择")
    @GetMapping("/api/device/list4select")
    public DataSet<DeviceExtResp> selectDeviceList4Select(Page page, DeviceList4SelectReq record) {
        return deviceService.getDataSetList4Select(page, record);
    }


    @ApiOperation(value = "生成资产编码", notes = "生成资产编码")
    @GetMapping("/api/device/genCode/{skuId}")
    public PlatformResult<String> genCode(@PathVariable String skuId) {
        try {
            String code = deviceService.genAssetNo();
            return PlatformResult.success(code);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "获取款号下公共附件", notes = "获取款号下公共附件")
    @GetMapping("/api/device/shareAttachmentList/{skuId}")
    public PlatformResult<List<DeviceAttachment>> shareAttachmentList(@PathVariable String skuId) {
        try {
            List<DeviceAttachment> deviceAttachmentList = deviceAttachmentService.selectListBySkuId(skuId);
            return PlatformResult.success(deviceAttachmentList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @GetMapping(value = "/api/device/tpl")
    @ApiOperation(value = "导入模板", notes = "导入模板")
    public void tpl(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "设备导入模板.xlsx";
            String template = "template/deviceImportTpl.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ResponseEntity<byte[]> exportDeviceList(DeviceListReq record, String templatePath, String filename) throws IOException {
        try {
            List<DeviceExtResp> exportList = deviceService.getList(record);

            exportList.forEach(device -> {
                device.setLifespanUnit(String.format("%d (%s)", device.getLifespanVal(), device.getLifespanUnitShow()));
                device.setMaintCycleUnit(String.format("%d (%s)", device.getMaintCycleVal(), device.getMaintCycleUnitShow()));
                device.setCalibrationCycleUnit(String.format("%d (%s)", device.getCalibrationCycleVal(), device.getCalibrationCycleUnitShow()));
                device.setWarrantyPeriodUnit(String.format("%d (%s)", device.getWarrantyPeriodVal(), device.getWarrantyPeriodUnitShow()));
                device.setInspectionCycleUnit(String.format("%d (%s)", device.getInspectionCycleVal(), device.getInspectionCycleUnitShow()));
                device.setMaintPeriodUnit(String.format("%d (%s)", device.getMaintPeriodVal(), device.getMaintPeriodUnitShow()));
            });

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath(templatePath));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);

            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String(filename.getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/device/export")
    public ResponseEntity<byte[]> export(DeviceListReq record) throws IOException {
        return exportDeviceList(record, "template/deviceExportTpl.xlsx", "设备台账.xlsx");
    }

    @ApiOperation(value = "有库存的资产导出", notes = "有库存的资产导出")
    @GetMapping(value = "/api/device/exportInWarehouse")
    public ResponseEntity<byte[]> exportInWarehouse(DeviceListReq record) throws IOException {
        record.setWarehouseStatus(DeviceConst.WAREHOUSE_STATUS_ZK);
        return exportDeviceList(record, "template/assetStockExportTpl.xlsx", "资产台账.xlsx");
    }


    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/api/device/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {

        List<DeviceImport> imports = (List<DeviceImport>) ImportExcelUtil.getExcelDatas(file, DeviceImport.class);

        try {
            ImportResp importResp = deviceService.importByExcel(imports);

            StringBuffer sb = new StringBuffer("导入失败:");
            if (importResp.getErrs() > 0) {
                // 拼接错误消息
                for (ImportErrRow err : importResp.getErrRows()) {
                    sb.append("第" + err.getIndex() + "行" + err.getErrMsg());
                }
                return PlatformResult.failure(sb.toString());
            }

            if (importResp.getSuccs() == 0) {
                return PlatformResult.failure("导入失败:无数据");
            }

            return PlatformResult.success(importResp);

        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }

    }


    @ApiOperation(value = "批量标记打印", notes = "批量标记打印")
    @PostMapping("/api/device/print/{type}")
    public PlatformResult print(@PathVariable String type, @RequestBody List<String> idList) throws Exception {

        try {
            deviceService.printTag(type, idList);
            return PlatformResult.success("标记成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }

    }


    @GetMapping("/api/device/test")
    public PlatformResult test() throws Exception {

        Map<String, String> orgMap = (Map<String, String>) redisService.fetch("bingo", () -> {
            return orgService.getOrgMap();
        }, 100);

        deviceService.autoFillCode();

        return PlatformResult.success(orgMap);
    }


}
