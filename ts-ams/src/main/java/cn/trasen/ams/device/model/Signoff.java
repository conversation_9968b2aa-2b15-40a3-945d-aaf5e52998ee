package cn.trasen.ams.device.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_signoff")
@Setter
@Getter
public class Signoff {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流水号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "流水号")
    private String flowNo;

    /**
     * 采购订单ID
     */
    @Column(name = "purchase_order_id")
    @ApiModelProperty(value = "采购订单ID")
    private String purchaseOrderId;

    @Column(name = "sku_type")
    @ApiModelProperty(value = "订单相关资产类型")
    private String skuType;

    /**
     * 验收物件清单记录 某物品*数量
     */
    @Column(name = "sku_name_set")
    @ApiModelProperty(value = "Q:验收物件清单记录 某物品*数量")
    private String skuNameSet;

    /**
     * 验收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "sure_date")
    @ApiModelProperty(value = "验收日期")
    private Date sureDate;


    @Transient
    @ApiModelProperty(value = "Q:验收日期查询")
    private String sureDateQuery;

    /**
     * 验收人，多个逗号隔开
     */
    @Column(name = "sure_user")
    @ApiModelProperty(value = "验收人，多个逗号隔开")
    private String sureUser;

    /**
     * 验收人名称，多个逗号隔开
     */
    @Column(name = "sure_user_name")
    @ApiModelProperty(value = "验收人名称，多个逗号隔开")
    private String sureUserName;

    /**
     * 验收情况描述
     */
    @ApiModelProperty(value = "验收情况描述")
    private String note;

    /**
     * 附件
     */
    @Column(name = "file_set")
    @ApiModelProperty(value = "附件")
    private String fileSet;

    @Column(name = "status")
    @ApiModelProperty(value = "Q:状态")
    private String status;

    @Transient
    @ApiModelProperty(value = "状态显示")
    private String statusShow;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}