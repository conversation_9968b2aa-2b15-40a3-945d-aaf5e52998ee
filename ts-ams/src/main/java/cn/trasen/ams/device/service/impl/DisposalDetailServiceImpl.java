package cn.trasen.ams.device.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.constant.DisposalConst;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.DisposalDetailMapper;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.ams.device.service.DisposalDetailService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DisposalDetailServiceImpl
 * @Description TODO
 * @date 2025年4月27日 下午4:49:48
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DisposalDetailServiceImpl implements DisposalDetailService {

    @Autowired
    private DisposalDetailMapper mapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DisposalDetail record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DisposalDetail record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DisposalDetail record = new DisposalDetail();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DisposalDetail selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DisposalDetail> getDataSetList(Page page, DisposalDetail record) {
        Example example = new Example(DisposalDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<DisposalDetail> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByDisposalId(String disposalId) {
        // 根据disposalId删除
        Assert.hasText(disposalId, "ID不能为空.");
        DisposalDetail record = new DisposalDetail();
        record.setDisposalId(disposalId);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        Example example = new Example(DisposalDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("disposalId", disposalId);
        mapper.updateByExampleSelective(record, example);

    }

    @Transactional(readOnly = false)
    @Override
    public List<DisposalDetail> selectByDisposalId(String disposalId) {
        // disposalId 查询
        Example example = new Example(DisposalDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("disposalId", disposalId);
        List<DisposalDetail> records = mapper.selectByExample(example);
        if (records == null || records.isEmpty()) {
            return Collections.emptyList();
        }
        // 格式化数据
        records.forEach(this::dataFmt);

        // 抽取所有的deviceId
        List<String> deviceIds = records.stream()
                .map(DisposalDetail::getDeviceId)
                .collect(Collectors.toList());
        String deviceIdsStr = String.join(",", deviceIds);
        // TODO 对于只是需要取deviceId 下面的是开销是没有必要的
        // 查询设备信息
        if (!deviceIds.isEmpty()) {
            Map<String, DeviceExtResp> deviceMap = deviceService.getListByIds(deviceIdsStr).stream()
                    .collect(Collectors.toMap(DeviceExtResp::getId, device -> device));

            // 装载设备属性到 records 中
            records.forEach(record -> {

                DeviceExtResp device = deviceMap.get(record.getDeviceId());
                if (device != null) {
                    record.setName(device.getName());
                    record.setModel(device.getModel());
                    record.setAssetCode(device.getAssetCode());
                    record.setGzwbh(device.getGzwbh());
                    record.setBelongToOrgId(device.getBelongToOrgId());
                    record.setBelongToOrgName(device.getBelongToOrgName());
                    record.setLoc(device.getLoc());
                    record.setOriginalValue(device.getOriginalVal());
                    record.setBookVal(device.getBookVal());
                    if (device.getOriginalVal() == null) {
                        device.setOriginalVal(BigDecimal.ZERO);
                    }
                    if (device.getBookVal() == null) {
                        // 默认0
                        device.setBookVal(BigDecimal.ZERO);
                    }
                    // 减法
                    record.setTotalDecVal(device.getOriginalVal().subtract(device.getBookVal()));
                    record.setUseDate(device.getUseDate());
                    record.setLifespanVal(device.getLifespanVal());
                    record.setLifespanUnit(device.getLifespanUnit());
                    record.setLifespanUnitShow(device.getLifespanUnitShow());
                    if (device.getUseDate() != null) {
                        record.setZysc(CommonUtil.howLong(device.getUseDate(), 4));
                    }
                }

            });
        }

        return records;
    }

    private void dataFmt(DisposalDetail record) {
        record.setTypeShow(dictService.cgetNameByValue(DisposalConst.DISPOSAL_TYPE, record.getType()));
    }

}
