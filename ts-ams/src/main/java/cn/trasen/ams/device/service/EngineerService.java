package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Engineer;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName EngineerService
 * @Description TODO
 * @date 2024年12月25日 下午4:45:40
 */
public interface EngineerService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    Integer save(Engineer record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    Integer update(Engineer record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Engineer
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    Engineer selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Engineer>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年12月25日 下午4:45:40
     * <AUTHOR>
     */
    DataSet<Engineer> getDataSetList(Page page, Engineer record);

    List<Engineer> getList(Engineer record);
}
