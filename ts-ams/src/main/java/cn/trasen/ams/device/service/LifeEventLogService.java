package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.LifeEventLog;

/**
 * @ClassName LifeEventLogService
 * @Description TODO
 * @date 2024年9月13日 上午10:51:12
 * <AUTHOR>
 * @version 1.0
 */
public interface LifeEventLogService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024年9月13日 上午10:51:12
	 * <AUTHOR>
	 */
	Integer save(LifeEventLog record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024年9月13日 上午10:51:12
	 * <AUTHOR>
	 */
	Integer update(LifeEventLog record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024年9月13日 上午10:51:12
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return LifeEventLog
	 * @date 2024年9月13日 上午10:51:12
	 * <AUTHOR>
	 */
	LifeEventLog selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<LifeEventLog>
	 * @date 2024年9月13日 上午10:51:12
	 * <AUTHOR>
	 */
	DataSet<LifeEventLog> getDataSetList(Page page, LifeEventLog record);
}
