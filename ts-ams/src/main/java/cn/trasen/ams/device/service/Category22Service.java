package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.comm.TreeModel;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Category22;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName Category22Service
 * @Description TODO
 * @date 2024年9月9日 上午11:54:45
 */
public interface Category22Service {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年9月9日 上午11:54:45
     * <AUTHOR>
     */
    Integer save(Category22 record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年9月9日 上午11:54:45
     * <AUTHOR>
     */
    Integer update(Category22 record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年9月9日 上午11:54:45
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Category22
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年9月9日 上午11:54:45
     * <AUTHOR>
     */
    Category22 selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Category22>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年9月9日 上午11:54:45
     * <AUTHOR>
     */
    DataSet<Category22> getDataSetList(Page page, Category22 record);

    List<TreeModel> getTreeList(Integer level);

    Set fetchAllCategory22Name();

    Map fetchAllCategory22Name2Map();

//    List<String> betterMatch(String name) throws Exception;

    List<Category22> getListByIds(List<String> ids);

    List<Category22> getSonListByIds(List<String> ids);
}
