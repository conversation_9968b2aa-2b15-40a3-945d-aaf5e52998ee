package cn.trasen.ams.device.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.PurchaseOrderDetailMerge;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName PurchaseOrderDetailMergeService
 * @Description TODO
 * @date 2025年6月18日 上午9:39:56
 */
public interface PurchaseOrderDetailMergeService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年6月18日 上午9:39:56
     * <AUTHOR>
     */
    Integer save(PurchaseOrderDetailMerge record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年6月18日 上午9:39:56
     * <AUTHOR>
     */
    Integer update(PurchaseOrderDetailMerge record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年6月18日 上午9:39:56
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return PurchaseOrderDetailMerge
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年6月18日 上午9:39:56
     * <AUTHOR>
     */
    PurchaseOrderDetailMerge selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<PurchaseOrderDetailMerge>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年6月18日 上午9:39:56
     * <AUTHOR>
     */
    DataSet<PurchaseOrderDetailMerge> getDataSetList(Page page, PurchaseOrderDetailMerge record);

    void batchInsert(List<PurchaseOrderDetailMerge> list);

    void deleteByPurchaseOrderId(String purchaseOrderId);

    List<PurchaseOrderDetailMerge> selectByPurchaseOrderId(String purchaseOrderId);
}
