package cn.trasen.ams.device.model;

import cn.trasen.ams.device.constant.DeviceAttachmentConst;
import cn.trasen.ams.common.validator.dict.ConstValid;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;
import org.springframework.validation.annotation.Validated;

@Table(name = "d_device_attachment")
@Setter
@Getter
@Validated
public class DeviceAttachment {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Column(name = "name")
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /**
     * 设备字典ID
     */
    @Column(name = "sku_id")
    @ApiModelProperty(value = "设备字典ID")
    private String skuId;

    /**
     * 归属者 1 设备 2 设备字典
     */

    @ConstValid(constant = {DeviceAttachmentConst.OWNER_DEVICE, DeviceAttachmentConst.OWNER_SKU}, message = "归属者不正确")
    @ApiModelProperty(value = "归属者 1 设备 2 设备字典")
    private String owner;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 附件ID
     */
    @NotNull(message = "附件ID不能为空")
    @Column(name = "attachment_id")
    @ApiModelProperty(value = "附件ID")
    private String attachmentId;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}