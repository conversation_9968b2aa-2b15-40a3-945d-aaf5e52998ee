package cn.trasen.ams.device.dao;

import cn.trasen.ams.device.bean.maintTask.MaintTaskDetailResp;
import cn.trasen.ams.device.bean.maintTask.MaintTaskListReq;
import cn.trasen.ams.device.bean.maintTask.MaintTaskListResp;
import cn.trasen.ams.device.model.MaintTask;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface MaintTaskMapper extends Mapper<MaintTask> {
    void batchInsert(@Param("list") List<MaintTask> maintTaskList);

    List<MaintTaskListResp> getList(Page page, @Param("req") MaintTaskListReq req);
}