package cn.trasen.ams.device.controller;

import cn.trasen.ams.device.bean.inventory.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InventoryTask;
import cn.trasen.ams.device.service.InventoryTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InventoryTaskController
 * @Description TODO
 * @date 2025年2月20日 下午5:33:21
 */
@RestController
@Api(tags = "InventoryTaskController")
public class InventoryTaskController {

    private transient static final Logger logger = LoggerFactory.getLogger(InventoryTaskController.class);

    @Autowired
    private InventoryTaskService inventoryTaskService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateInventoryTask
     * @Description 编辑
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    @ApiOperation(value = "盘点接口", notes = "盘点接口")
    @PostMapping("/api/device/inventoryTask/update")
    public PlatformResult<String> updateInventoryTask(@Validated @RequestBody InventoryTask record) {
        try {
            inventoryTaskService.doit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @return PlatformResult<String>
     * @Title updateInventoryTask
     * @Description 编辑
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    @ApiOperation(value = "批量盘点接口", notes = "批量盘点接口")
    @PostMapping("/api/device/inventoryTask/batchUpdate")
    public PlatformResult doitByRfid(@Valid @RequestBody InventoryTaskDoitBatchReq inventoryTaskDoitBatchReq) {
        try {
            List<InventoryTaskExtResp> res = inventoryTaskService.doit4Rfid(inventoryTaskDoitBatchReq);
            return PlatformResult.success(res);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @return PlatformResult<String>
     * @Title updateInventoryTask
     * @Description 扫码盘点接口
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    @ApiOperation(value = "扫码盘点接口", notes = "扫码盘点接口")
    @PostMapping("/api/device/inventoryTask/scanUpdate")
    public PlatformResult doitByScan(@Valid @RequestBody InventoryTaskDoitScanReq inventoryTaskDoitScanReq) {
        try {
            InventoryTaskExtResp res = inventoryTaskService.doit4Scan(inventoryTaskDoitScanReq);
            return PlatformResult.success(res);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param page
     * @param record
     * @return DataSet<InventoryTask>
     * @Title selectInventoryTaskList
     * @Description 查询列表
     * @date 2025年2月20日 下午5:33:21
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/inventoryTask/list/{id}")
    public DataSet<InventoryTaskExtResp> selectInventoryTaskList(@PathVariable String id, Page page, InventoryTaskListReq record) {
        record.setInventoryPlanId(id);
        return inventoryTaskService.getDataSet(page, record);
    }

    @ApiOperation(value = "盘点统计情况", notes = "盘点统计情况")
    @RequestMapping(value = "/api/device/inventoryTask/status/nums/{id}", method = {RequestMethod.GET, RequestMethod.POST})
    public PlatformResult<InventoryTaskStatusNumsResp> selectStatusNums(@PathVariable String id, @RequestParam(required = false) String cateId, @RequestParam(required = false) String orgId) {
        try {
            // 为了做兼容 没有直接做对象接收
            InventoryTaskListReq inventoryTaskListReq = new InventoryTaskListReq();

            inventoryTaskListReq.setInventoryPlanId(id);
            inventoryTaskListReq.setOrgId(orgId);
            inventoryTaskListReq.setCateId(cateId);

            return PlatformResult.success(inventoryTaskService.getInventoryTaskStatusNumsExt(inventoryTaskListReq));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "盘点异常列表", notes = "盘点异常列表")
    @GetMapping("/api/device/inventoryTask/exp/list")

    public DataSet<InventoryTaskExtResp> selectInventoryTaskExpList(Page page, InventoryTaskListReq record) {
        record.setIsExp("1");
        return inventoryTaskService.getDataSet(page, record);
    }


}
