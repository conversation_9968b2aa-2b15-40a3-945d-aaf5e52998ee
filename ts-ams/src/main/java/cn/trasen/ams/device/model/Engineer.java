package cn.trasen.ams.device.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "d_engineer")
@Setter
@Getter
public class Engineer {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户名称
     */
    @Column(name = "name")
    @ApiModelProperty(value = "用户名称")
    private String name;

    /**
     * 支持多个逗号分隔 1 保养 2 巡检 3 盘点
     */
    @ApiModelProperty(value = "支持多个逗号分隔 1 保养 2 巡检 3 盘点")
    private String itemSet;


    @Transient
    @ApiModelProperty(value = "身份翻译")
    private String itemSetShow;

    /**
     * 供应商ID
     */
    @Column(name = "supplier_id")
    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @Transient
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 状态 1 启用 0 禁用
     */
    @ApiModelProperty(value = "状态 1 启用 0 禁用")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")

    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
}