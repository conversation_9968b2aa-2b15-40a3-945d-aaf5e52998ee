package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintCost;
import cn.trasen.ams.device.service.MaintCostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MaintCostController
 * @Description TODO
 * @date 2024年12月17日 下午4:33:22
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "MaintCostController")
public class MaintCostController {

	private transient static final Logger logger = LoggerFactory.getLogger(MaintCostController.class);

	@Autowired
	private MaintCostService maintCostService;

	/**
	 * @Title saveMaintCost
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/maintCost/save")
	public PlatformResult<String> saveMaintCost(@RequestBody MaintCost record) {
		try {
			maintCostService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMaintCost
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/maintCost/update")
	public PlatformResult<String> updateMaintCost(@RequestBody MaintCost record) {
		try {
			maintCostService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMaintCostById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MaintCost>
	 * @date 2024年12月17日 下午4:33:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/maintCost/{id}")
	public PlatformResult<MaintCost> selectMaintCostById(@PathVariable String id) {
		try {
			MaintCost record = maintCostService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMaintCostById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/maintCost/delete/{id}")
	public PlatformResult<String> deleteMaintCostById(@PathVariable String id) {
		try {
			maintCostService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMaintCostList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MaintCost>
	 * @date 2024年12月17日 下午4:33:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/maintCost/list")
	public DataSet<MaintCost> selectMaintCostList(Page page, MaintCost record) {
		return maintCostService.getDataSetList(page, record);
	}
}
