package cn.trasen.ams.device.bean.purchase;

import cn.trasen.ams.device.model.PurchaseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.purchase
 * @className: PurchaseOrderWaitDeliveryResp
 * @author: chenbin
 * @description: TODO
 * @date: 2025/6/24 14:11
 * @version: 1.0
 */
@Data
public class PurchaseOrderWaitDeliveryDetailResp {

    @ApiModelProperty(value = "采购订单")
    PurchaseOrder purchaseOrder;

    @ApiModelProperty(value = "待到货的明细")
    List<PurchaseOrderWaitDeliveryItem> PurchaseOrderWaitDeliveryItemList;

}
