package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.MaintWorkHours;
import cn.trasen.ams.device.service.MaintWorkHoursService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MaintWorkHoursController
 * @Description TODO
 * @date 2024年12月17日 下午4:33:52
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "MaintWorkHoursController")
public class MaintWorkHoursController {

	private transient static final Logger logger = LoggerFactory.getLogger(MaintWorkHoursController.class);

	@Autowired
	private MaintWorkHoursService maintWorkHoursService;

	/**
	 * @Title saveMaintWorkHours
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/maintWorkHours/save")
	public PlatformResult<String> saveMaintWorkHours(@RequestBody MaintWorkHours record) {
		try {
			maintWorkHoursService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMaintWorkHours
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/maintWorkHours/update")
	public PlatformResult<String> updateMaintWorkHours(@RequestBody MaintWorkHours record) {
		try {
			maintWorkHoursService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMaintWorkHoursById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MaintWorkHours>
	 * @date 2024年12月17日 下午4:33:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/maintWorkHours/{id}")
	public PlatformResult<MaintWorkHours> selectMaintWorkHoursById(@PathVariable String id) {
		try {
			MaintWorkHours record = maintWorkHoursService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMaintWorkHoursById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024年12月17日 下午4:33:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/maintWorkHours/delete/{id}")
	public PlatformResult<String> deleteMaintWorkHoursById(@PathVariable String id) {
		try {
			maintWorkHoursService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMaintWorkHoursList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MaintWorkHours>
	 * @date 2024年12月17日 下午4:33:52
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/maintWorkHours/list")
	public DataSet<MaintWorkHours> selectMaintWorkHoursList(Page page, MaintWorkHours record) {
		return maintWorkHoursService.getDataSetList(page, record);
	}
}
