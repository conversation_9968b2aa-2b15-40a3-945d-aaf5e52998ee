package cn.trasen.ams.device.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.ams.device.bean.device.DeviceExtResp;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffDeviceListReq;
import cn.trasen.ams.device.bean.signoff.PenddingSignoffPurchaseOrder;
import cn.trasen.ams.device.bean.signoff.SignoffExt;
import cn.trasen.ams.device.bean.signoff.SignoffInsertReq;
import cn.trasen.ams.device.constant.*;
import cn.trasen.ams.device.model.Device;
import cn.trasen.ams.device.model.PurchaseOrder;
import cn.trasen.ams.device.model.SignoffDetail;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.device.service.SignoffDetailService;
import cn.trasen.ams.common.constant.PermissionConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.PermissionService;
import cn.trasen.ams.common.service.SerialNoGenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.device.dao.SignoffMapper;
import cn.trasen.ams.device.model.Signoff;
import cn.trasen.ams.device.service.SignoffService;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName SignoffServiceImpl
 * @Description TODO
 * @date 2025年7月7日 上午11:36:34
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SignoffServiceImpl implements SignoffService {

    @Autowired
    private SignoffMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private SignoffDetailService signoffDetailService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PermissionService permissionService;


    @Transactional(readOnly = false)
    @Override
    public Integer save(Signoff record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    private String genFlowNo() {
        // 生成 flow_no 的逻辑
        // 例如：record.setFlowNo("F" + IdGeneraterUtils.nextId());
        // 注意：此处的逻辑需要根据实际业务需求来实现
        return serialNoGenService.genByDate("CZ");
    }

    private void checkSignoffDetialRepeat(List<SignoffDetail> signoffDetail) {
        // 检测当前的设备是否已经在安装登记列表
        List<String> deviceIdList = signoffDetail.stream().map(SignoffDetail::getDeviceId).collect(Collectors.toList());
        boolean ok = signoffDetailService.checkDeviceHasSignoff(deviceIdList);
        if (!ok) {
            throw new RuntimeException("资产已存在安装验收记录，请勿重复登记");
        }
    }

    private void prepare(SignoffInsertReq record) {

        Signoff signoff = record.getSignoff();
        List<SignoffDetail> signoffDetail = record.getSignoffDetailList();

        if (signoff == null) {
            throw new RuntimeException("安装验收主数据必填");
        }

        if (signoffDetail == null || signoffDetail.size() == 0) {
            throw new RuntimeException("安装验收详情数据必填");
        }

        // 设置状态
        signoff.setStatus(SignoffConst.SIGNOFF_STATUS_UNSURE);
        // 新增
        if (StringUtils.isEmpty(signoff.getId())) {
            // 装载ID flow_no 生成
            signoff.setId(IdGeneraterUtils.nextId());
            signoff.setFlowNo(genFlowNo());
        }

        // loop 处理 signoffDetail
        for (SignoffDetail detail : signoffDetail) {
            // 每次都重新设置ID
            detail.setId(IdGeneraterUtils.nextId());
            detail.setSignoffId(signoff.getId());
            // 由于batchInsert 所以需要填充必要信息
            detail.setCreateDate(new Date());
            detail.setUpdateDate(new Date());
            detail.setIsDeleted("N");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                detail.setCreateUser(user.getUsercode());
                detail.setCreateUserName(user.getUsername());
                detail.setUpdateUser(user.getUsercode());
                detail.setUpdateUserName(user.getUsername());
                detail.setSsoOrgCode(user.getCorpcode());
                detail.setSsoOrgName(user.getOrgName());
                detail.setDeptId(user.getDeptId());
                detail.setDeptName(user.getDeptname());
            }
        }

        // sku_name_set 构建 根据 signoffDetail 中的 sku_id 分组
        // 格式化名称统计结果为 A*2,B*1,C*3 的形式
        Map<String, Long> skuCountMap = signoffDetail.stream().collect(Collectors.groupingBy(SignoffDetail::getName, Collectors.counting()));

        String skuNameSet = skuCountMap.entrySet().stream().map(e -> e.getKey() + "*" + e.getValue()).collect(Collectors.joining(","));

        signoff.setSkuNameSet(skuNameSet);

    }

    @Transactional(readOnly = false)
    @Override
    public void insert(SignoffInsertReq record) {
        prepare(record);
        Signoff signoff = record.getSignoff();
        List<SignoffDetail> signoffDetail = record.getSignoffDetailList();
        checkSignoffDetialRepeat(signoffDetail);
        // 插入主表
        this.save(signoff);
        // 批量插入详情表
        signoffDetailService.batchInsert(signoffDetail);
    }

    @Transactional(readOnly = false)
    @Override
    public void edit(SignoffInsertReq record) {
        prepare(record);
        Signoff signoff = record.getSignoff();
        List<SignoffDetail> signoffDetail = record.getSignoffDetailList();
        // 更新主表
        this.update(signoff);
        // 删除原有详情
        signoffDetailService.deleteBySignoffId(signoff.getId());

        checkSignoffDetialRepeat(signoffDetail);

        // 批量插入新的详情表
        signoffDetailService.batchInsert(signoffDetail);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Signoff record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        Signoff signoff = selectById(id);
        if (signoff == null) {
            throw new RuntimeException("安装验收记录不存在，无法删除");
        }
        if (SignoffConst.SIGNOFF_STATUS_SURED.equals(signoff.getStatus())) {
            throw new RuntimeException("安装验收记录已确认，无法删除");
        }

        // 删除子表数据
        signoffDetailService.deleteBySignoffId(id);

        Signoff record = new Signoff();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Signoff selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Signoff> getDataSetList(Page page, Signoff record) {
        Example example = new Example(Signoff.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Signoff> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public DataSet<SignoffExt> selectSignoffedList(Page page, SignoffExt record) {
        // ！！！ 防止前端传入的 record 中有 pmsql 字段，导致查询异常，不允许删除
        // ！！！ 防止前端传入的 record 中有 pmsql 字段，导致查询异常，不允许删除
        record.setPmsql(null);

        StringBuffer sql = new StringBuffer();
        String business = PermissionConst.二级业务类型_安装验收已登记;
        permissionService.appendPermissionSql(business, sql);

        if (sql != null) {
            record.setPmsql(sql.toString());
        }

        List<SignoffExt> records = mapper.selectSignoffedList(page, record);
        // dataFmt
        for (SignoffExt item : records) {
            dataFmt(item);
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);

    }

    @Override
    public DataSet<PenddingSignoffPurchaseOrder> getPenddingDataSetList(Page page, PurchaseOrder purchaseOrder) {

        // ！！！ 防止前端传入的 purchaseOrder 中有 pmsql 字段，导致查询异常，不允许删除
        // ！！！ 防止前端传入的 purchaseOrder 中有 pmsql 字段，导致查询异常，不允许删除
        // ！！！ 防止前端传入的 purchaseOrder 中有 pmsql 字段，导致查询异常，不允许删除
        purchaseOrder.setPmsql(null);

        String business = PermissionConst.二级业务类型_安装验收采购入库登记列表;
        StringBuffer sql = new StringBuffer();
        permissionService.appendPermissionSql(business, sql);
        if (sql != null) {
            purchaseOrder.setPmsql(sql.toString());
        }

        List<PenddingSignoffPurchaseOrder> penddingSignoffPurchaseOrderList = mapper.getPenddingList(page, purchaseOrder);


        // loop penddingSignoffPurchaseOrderList
        for (PenddingSignoffPurchaseOrder item : penddingSignoffPurchaseOrderList) {
            // 设置状态显示
            item.setStatusShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_ORDER_STATUS, item.getStatus()));
            // 设置采购订单类型显示
            item.setPurchaseTypeShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_TYPE, item.getPurchaseType()));
            // 资金来源
            item.setFundSourceShow(dictService.cgetNameByValue(PurchaseConst.PURCHASE_FUND_SOURCE, item.getFundSource()));
            // 资产类型
            item.setSkuTypeShow(dictService.cgetNameByValue(SkuConst.SKU_TYPE, item.getSkuType()));
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), penddingSignoffPurchaseOrderList);
    }

    @Override
    public List<DeviceExtResp> getPenddingSignoffDeviceList(PenddingSignoffDeviceListReq req) {
        return mapper.getPenddingSignoffDeviceList(req);
    }

    @Transactional
    @Override
    public void sure(String purchaseOrderId) {
        // 确认安装验收
        // 对安装验收的详细数据对资产进行更新
        Assert.hasText(purchaseOrderId, "采购单ID不能为空");
        // 查询验收主表
        Example example = new Example(Signoff.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", purchaseOrderId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        Signoff signoff = mapper.selectOneByExample(example);
        if (signoff == null) {
            throw new RuntimeException("未找到对应的安装验收记录");
        }
        if (SignoffConst.SIGNOFF_STATUS_SURED.equals(signoff.getStatus())) {
            throw new RuntimeException("安装验收记录已确认，无法重复确认");
        }

        // 更新主表状态
        signoff.setStatus(SignoffConst.SIGNOFF_STATUS_SURED);
        signoff.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            signoff.setUpdateUser(user.getUsercode());
            signoff.setUpdateUserName(user.getUsername());
        }
        mapper.updateByPrimaryKeySelective(signoff);
        asyncUpdateDeviceInfo(signoff);
    }

    @Transactional
    @Async
    protected void asyncUpdateDeviceInfo(Signoff signoff) {

        List<SignoffDetail> signoffDetailList = signoffDetailService.selectBySignoffId(signoff.getId());

        if (signoffDetailList == null || signoffDetailList.isEmpty()) {
            return;
        }
        // 更新设备信息
        List<String> deviceIds = signoffDetailList.stream().map(SignoffDetail::getDeviceId).collect(Collectors.toList());

        // deviceIds 转逗号字符串
        String deviceIdsStr = String.join(",", deviceIds);
        List<DeviceExtResp> deviceExtResps = deviceService.getListByIds(deviceIdsStr);
        // 按照设备ID分组
        Map<String, DeviceExtResp> deviceMap = deviceExtResps.stream().collect(Collectors.toMap(DeviceExtResp::getId, device -> device, (existing, replacement) -> existing));

        // 逐个更新
        for (SignoffDetail detail : signoffDetailList) {
            DeviceExtResp device = deviceMap.get(detail.getDeviceId());
            if (device == null) {
                // 正常来说这里不可能为null 做一个程序级别的容错，体现求生本能
                continue;
            }
            Device updateDevice = new Device();
            updateDevice.setId(detail.getDeviceId());
            if (DeviceConst.STATUS_UN_INS.equals(device.getStatus())) {
                updateDevice.setStatus(DeviceConst.STATUS_USE);
            }

            if (detail.getLifespanVal() != null) {
                updateDevice.setLifespanVal(detail.getLifespanVal());
            }
            if (detail.getLifespanUnit() != null) {
                updateDevice.setLifespanUnit(detail.getLifespanUnit());
            }
            if (detail.getMaintCycleVal() != null) {
                updateDevice.setMaintCycleVal(detail.getMaintCycleVal());
            }
            if (detail.getMaintCycleUnit() != null) {
                updateDevice.setMaintCycleUnit(detail.getMaintCycleUnit());
            }
            if (detail.getInspectionCycleVal() != null) {
                updateDevice.setInspectionCycleVal(detail.getInspectionCycleVal());
            }
            if (detail.getInspectionCycleUnit() != null) {
                updateDevice.setInspectionCycleUnit(detail.getInspectionCycleUnit());
            }
            if (detail.getWarrantyPeriodVal() != null) {
                updateDevice.setWarrantyPeriodVal(detail.getWarrantyPeriodVal());
            }
            if (detail.getWarrantyPeriodUnit() != null) {
                updateDevice.setWarrantyPeriodUnit(detail.getWarrantyPeriodUnit());
            }
            if (detail.getCalibrationType() != null) {
                updateDevice.setCalibrationType(detail.getCalibrationType());
            }
            if (detail.getCalibrationCycleVal() != null) {
                updateDevice.setCalibrationCycleVal(detail.getCalibrationCycleVal());
            }
            if (detail.getCalibrationCycleUnit() != null) {
                updateDevice.setCalibrationCycleUnit(detail.getCalibrationCycleUnit());
            }

            // 设置其他相关字段
            if (detail.getSn() != null) {
                updateDevice.setSerialNo(detail.getSn());
            }
            if (detail.getYthzcbm() != null) {
                updateDevice.setYthzcbm(detail.getYthzcbm());
            }
            if (detail.getBirthDate() != null) {
                updateDevice.setManufactureDate(detail.getBirthDate());
            }
            if (detail.getUseDate() != null) {
                updateDevice.setUseDate(detail.getUseDate());
            }

            updateDevice.setAcceptanceDate(signoff.getSureDate());
            updateDevice.setUpdateDate(new Date());

            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                updateDevice.setUpdateUser(user.getUsercode());
                updateDevice.setUpdateUserName(user.getUsername());
            }

            deviceService.update(updateDevice);
        }

    }


    public void dataFmt(Signoff record) {
        record.setStatusShow(dictService.cgetNameByValue(SignoffConst.SIGNOFF_STATUS, record.getStatus()));
    }
}
