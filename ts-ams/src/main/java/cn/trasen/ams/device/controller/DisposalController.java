package cn.trasen.ams.device.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.device.bean.comm.CommApproveReq;
import cn.trasen.ams.device.bean.disposal.DisposalDetailResp;
import cn.trasen.ams.device.bean.disposal.DisposalInsertReq;
import cn.trasen.ams.device.model.DisposalDetail;
import cn.trasen.ams.device.service.DisposalDetailService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.Disposal;
import cn.trasen.ams.device.service.DisposalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DisposalController
 * @Description TODO
 * @date 2025年4月27日 下午4:48:41
 */
@RestController
@Api(tags = "DisposalController")
public class DisposalController {

    private transient static final Logger logger = LoggerFactory.getLogger(DisposalController.class);

    @Autowired
    private DisposalService disposalService;

    @Autowired
    private DisposalDetailService disposalDetailService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveDisposal
     * @Description 新增
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/device/disposal/save")
    public PlatformResult<String> saveDisposal(@Validated @RequestBody DisposalInsertReq record) {
        try {
            disposalService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateDisposal
     * @Description 编辑
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/device/disposal/update")
    public PlatformResult<String> updateDisposal(@Validated @RequestBody DisposalInsertReq record) {
        try {
            disposalService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Disposal>
     * @Title selectDisposalById
     * @Description 根据ID查询
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/device/disposal/{id}")
    public PlatformResult<DisposalDetailResp> selectDisposalById(@PathVariable String id) {
        try {
            Disposal record = disposalService.selectById(id);
            disposalService.dataFmt(record);
            List<DisposalDetail> disposalDetailList = disposalDetailService.selectByDisposalId(id);

            DisposalDetailResp disposalDetailResp = new DisposalDetailResp();

            disposalDetailResp.setDisposal(record);
            disposalDetailResp.setDisposalDetailList(disposalDetailList);

            return PlatformResult.success(disposalDetailResp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    @ApiOperation(value = "批量详情", notes = "批量详情")
    @PostMapping("/api/device/disposal/batchDetail")
    public PlatformResult selectTransferListByIdList(@RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return PlatformResult.failure("ID列表不能为空");
        }

        List<DisposalDetailResp> list = new ArrayList<>();
        for (String id : idList) {
            Disposal record = disposalService.selectById(id);
            List<DisposalDetail> disposalDetailList = disposalDetailService.selectByDisposalId(id);
            if (record == null) {
                continue;
            }
            disposalService.dataFmt(record);
            DisposalDetailResp disposalDetailResp = new DisposalDetailResp();
            disposalDetailResp.setDisposal(record);
            disposalDetailResp.setDisposalDetailList(disposalDetailList);
            list.add(disposalDetailResp);
        }
        return PlatformResult.success(list);
    }

    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteDisposalById
     * @Description 根据ID删除
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/device/disposal/delete/{id}")
    public PlatformResult<String> deleteDisposalById(@PathVariable String id) {
        try {
            disposalService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Disposal>
     * @Title selectDisposalList
     * @Description 查询列表
     * @date 2025年4月27日 下午4:48:41
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/device/disposal/list")
    public DataSet<Disposal> selectDisposalList(Page page, Disposal record) {
        return disposalService.getDataSetList(page, record);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/api/device/disposal/export")
    public ResponseEntity<byte[]> export(Disposal record) throws IOException {

        try {

            List<Disposal> exportList = disposalService.getList(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/disposalExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("资产处置列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /*
     * @param record:
     * @return PlatformResult<String>
     * <AUTHOR>
     * @description 资产转移审核
     * @date 2025/4/22 14:11
     */
//    @RightValid(action = "审核资产处置单", message = "您没有审批资产转移单的权限")
    @ApiOperation(value = "审核资产处置单", notes = "审核资产处置单")
    @PostMapping("/api/device/disposal/approve")
    public PlatformResult<String> approve(@RequestBody CommApproveReq record) {
        try {
            disposalService.approve(record);
            return PlatformResult.success();
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }
}
