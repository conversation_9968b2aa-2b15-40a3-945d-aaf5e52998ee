package cn.trasen.ams.device.eventPublisher.maintPlan;

import cn.trasen.ams.device.model.MaintPlan;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.event.maint
 * @className: DeletedEvent
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 09:47
 * @version: 1.0
 */

@Getter
public class MaintPlanDeletedEvent extends ApplicationEvent {
    private MaintPlan maintPlan;

    public MaintPlanDeletedEvent(MaintPlan maintPlan) {
        super(maintPlan);
        this.maintPlan = maintPlan;
    }
}
