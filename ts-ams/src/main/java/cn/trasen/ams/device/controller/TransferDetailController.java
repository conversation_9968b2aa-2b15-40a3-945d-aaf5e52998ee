package cn.trasen.ams.device.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.TransferDetail;
import cn.trasen.ams.device.service.TransferDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName TransferDetailController
 * @Description TODO
 * @date 2025年4月18日 上午9:26:50
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "TransferDetailController")
public class TransferDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(TransferDetailController.class);

	@Autowired
	private TransferDetailService transferDetailService;

	/**
	 * @Title saveTransferDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/device/transferDetail/save")
	public PlatformResult<String> saveTransferDetail(@RequestBody TransferDetail record) {
		try {
			transferDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateTransferDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/device/transferDetail/update")
	public PlatformResult<String> updateTransferDetail(@RequestBody TransferDetail record) {
		try {
			transferDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectTransferDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<TransferDetail>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/device/transferDetail/{id}")
	public PlatformResult<TransferDetail> selectTransferDetailById(@PathVariable String id) {
		try {
			TransferDetail record = transferDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteTransferDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/device/transferDetail/delete/{id}")
	public PlatformResult<String> deleteTransferDetailById(@PathVariable String id) {
		try {
			transferDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectTransferDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<TransferDetail>
	 * @date 2025年4月18日 上午9:26:50
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/device/transferDetail/list")
	public DataSet<TransferDetail> selectTransferDetailList(Page page, TransferDetail record) {
		return transferDetailService.getDataSetList(page, record);
	}
}
