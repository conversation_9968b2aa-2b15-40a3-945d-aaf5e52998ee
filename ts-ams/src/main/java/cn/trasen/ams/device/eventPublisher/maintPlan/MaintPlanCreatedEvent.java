package cn.trasen.ams.device.eventPublisher.maintPlan;

import cn.trasen.ams.device.model.MaintPlan;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.event.maint
 * @className: CreatedEvent
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 09:39
 * @version: 1.0
 */

@Getter
public class MaintPlanCreatedEvent extends ApplicationEvent {
    private MaintPlan maintPlan;

    public MaintPlanCreatedEvent(MaintPlan maintPlan) {
        super(maintPlan);
        this.maintPlan = maintPlan;
    }
}
