package cn.trasen.ams.device.bean.signoff;

import cn.trasen.ams.device.model.Signoff;
import cn.trasen.ams.device.model.SignoffDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.signoff
 * @className: SignoffInsertReq
 * @author: chenbin
 * @description: 安装验收新增和编辑数据提交
 * @date: 2025/7/9 17:50
 * @version: 1.0
 */
@Data
@Validated
public class SignoffInsertReq {

    @NotNull(message = "安装验收信息不能为空")
    @ApiModelProperty(name = "安装验收信息", notes = "安装验收信息")
    @Valid
    private Signoff signoff;

    @NotNull(message = "安装验收详情信息不能为空")
    @ApiModelProperty(name = "安装验收详情信息", notes = "安装验收详情信息")
    @Valid
    private List<SignoffDetail> signoffDetailList;

}
