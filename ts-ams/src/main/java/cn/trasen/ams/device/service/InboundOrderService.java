package cn.trasen.ams.device.service;

import cn.trasen.ams.device.bean.outin.InBoundOrderDetailResp;
import cn.trasen.ams.device.bean.outin.InBoundOrderInsertReq;
import cn.trasen.ams.device.bean.outin.InBoundOrderListReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.device.model.InboundOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InboundOrderService
 * @Description TODO
 * @date 2025年2月11日 下午5:02:53
 */
public interface InboundOrderService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    Integer save(InboundOrder record);

    String insert(InBoundOrderInsertReq record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    Integer update(InboundOrder record);

    String edit(InBoundOrderInsertReq record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InboundOrder
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    InboundOrder selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InboundOrder>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年2月11日 下午5:02:53
     * <AUTHOR>
     */
    DataSet<InboundOrder> getDataSetList(Page page, InBoundOrderListReq record);

    List<InboundOrder> getListNoPage(InBoundOrderListReq record);

    void dataFmt(InboundOrder record);

    List<InboundOrder> getList(Page page, InBoundOrderListReq record);

    void confirm(String id);

    void batchConfirm(List<String> ids);

    List<InBoundOrderDetailResp> getInboundDetailListByRelaId(String relaId);
}
