package cn.trasen.ams.device.bean.signoff;

import cn.trasen.ams.device.model.PurchaseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.bean.signoff
 * @className: PenddingSignoffPurchaseOrder
 * @author: chenbin
 * @description: 用于待安装验收的采购订单列表
 * @date: 2025/7/9 15:02
 * @version: 1.0
 */
@Data
public class PenddingSignoffPurchaseOrder extends PurchaseOrder {

    @ApiModelProperty(value = "总采购数量")
    private String totalPurchaseNums;

    @ApiModelProperty(value = "已入库数量")
    private String completedInboundNums;

    @ApiModelProperty(value = "已安装验收数量")
    private String completedSignoffNums;

}
