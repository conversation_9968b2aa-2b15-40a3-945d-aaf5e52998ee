package cn.trasen.ams.device.eventPublisher.inspection;

import cn.trasen.ams.device.model.InspectionPlan;
import cn.trasen.ams.device.model.MaintPlan;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.device.eventPublisher.inspection
 * @className: InspectionPlanCreatedEvent
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/20 16:05
 * @version: 1.0
 */

@Getter
public class InspectionPlanCreatedEvent extends ApplicationEvent {
    private InspectionPlan inspectionPlan;

    public InspectionPlanCreatedEvent(InspectionPlan inspectionPlan) {
        super(inspectionPlan);
        this.inspectionPlan = inspectionPlan;
    }
}
