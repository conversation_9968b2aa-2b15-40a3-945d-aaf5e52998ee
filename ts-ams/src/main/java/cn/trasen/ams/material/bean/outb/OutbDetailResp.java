package cn.trasen.ams.material.bean.outb;

import cn.trasen.ams.material.model.Outb;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.outb
 * @className: OutbDetailResp
 * @author: chenbin
 * @description: 物资出库单详情响应体
 * @date: 2025/8/1 16:30
 * @version: 1.0
 */
@Data
public class OutbDetailResp {

    @ApiModelProperty(value = "出库单主数据")
    private Outb outb;

    @ApiModelProperty(value = "出库单明细数据")
    private List<OutbDtlResp> outbDtlList;

}
