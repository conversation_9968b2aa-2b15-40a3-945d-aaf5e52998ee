package cn.trasen.ams.material.model;

import cn.trasen.ams.common.util.CommonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "m_return_stk_dtl")
@Setter
@Getter
public class ReturnStkDtl {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 退库单主表ID
     */
    @Column(name = "return_stk_id")
    @ApiModelProperty(value = "退库单主表ID")
    private String returnStkId;

    /**
     * 物资ID
     */
    @Column(name = "sku_id")
    @NotNull(message = "物资ID不能为空")
    @ApiModelProperty(value = "物资ID")
    private String skuId;

    /**
     * 批次号
     */
    @Column(name = "batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private Integer num;

    /**
     * 总金额
     */
    @Column(name = "total_amt")
    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmt;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;


    @Transient
    @ApiModelProperty(value = "产品批号")
    private String prodNo;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生产日期")
    private Date prodDate;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "失效日期")
    private Date expireDate;

    public void setPrice(BigDecimal price) {
        this.price = CommonUtil.dcmDgtFmt(price);
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = CommonUtil.dcmDgtFmt(totalAmt);
    }
}