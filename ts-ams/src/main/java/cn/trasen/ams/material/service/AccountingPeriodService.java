package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.accountPeriod.SettleListResp;
import cn.trasen.ams.material.bean.accountPeriod.SettleReq;
import cn.trasen.ams.material.bean.config.AccountingPeriodConfig;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.AccountingPeriod;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName AccountingPeriodService
 * @Description TODO
 * @date 2025年7月22日 上午10:18:32
 */
public interface AccountingPeriodService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月22日 上午10:18:32
     * <AUTHOR>
     */
    Integer save(AccountingPeriod record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月22日 上午10:18:32
     * <AUTHOR>
     */
    Integer update(AccountingPeriod record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月22日 上午10:18:32
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return AccountingPeriod
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月22日 上午10:18:32
     * <AUTHOR>
     */
    AccountingPeriod selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<AccountingPeriod>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月22日 上午10:18:32
     * <AUTHOR>
     */
    DataSet<AccountingPeriod> getDataSetList(Page page, AccountingPeriod record);

    /**
     * @param year:
     * @return void
     * <AUTHOR>
     * @description 会计区间生成
     * @date 2025/7/22 10:33
     */
    void gen(String year);


    AccountingPeriodConfig getAccountingPeriodConfig();

    // 会计期间结算
    void settle(SettleReq record);

    /**
     * 获取结算记录列表
     *
     * @param page   分页参数
     * @param record 查询条件
     * @return 结算记录列表
     */
    DataSet<SettleListResp> getSettleRecordList(Page page, SettleListResp record);

    void access(String warehouseId, Date date);

}
