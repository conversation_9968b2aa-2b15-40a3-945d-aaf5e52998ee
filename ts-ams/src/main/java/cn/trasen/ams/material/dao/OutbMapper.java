package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.Outb;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface OutbMapper extends Mapper<Outb> {

    List<Outb> getList(Page page,Outb record);
    /**
     * 获取上一条出库单ID
     * @param currentId 当前出库单ID
     * @return 上一条出库单ID
     */
    String getPrevId(@Param("whId") String whId, @Param("currentId") String currentId);

    /**
     * 获取下一条出库单ID
     * @param currentId 当前出库单ID
     * @return 下一条出库单ID
     */
    String getNextId(@Param("whId") String whId, @Param("currentId") String currentId);

    void updateReturnStat();
}