package cn.trasen.ams.common.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CertConst;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.CertMapper;
import cn.trasen.ams.common.model.Cert;
import cn.trasen.ams.common.service.CertService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CertServiceImpl
 * @Description TODO
 * @date 2025年7月25日 下午5:20:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CertServiceImpl implements CertService {

    @Autowired
    private CertMapper mapper;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Cert record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Cert record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Cert record = new Cert();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Cert selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Cert> getDataSetList(Page page, Cert record) {
        Example example = new Example(Cert.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Cert> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    @Transactional(readOnly = false)
    @Override
    public void deleteByModelId(String modelId) {
        // 逻辑删除
        Example example = new Example(Cert.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("modelId", modelId);
        Cert record = new Cert();
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Transactional(readOnly = false)
    @Override
    public void batchInsert(List<Cert> certList) {
        // 数量不会很大 循环save
        certList.forEach(cert -> {
            // 根据有效期判断证件状态 1 有效期大于当前日期 2 长期有效的情况
            if (cert.getValidOverDate() != null && cert.getValidOverDate().after(new Date())) {
                cert.setStatus(CommonConst.YES);
            } else if (CommonConst.YES.equals(cert.getIsForever())) {
                cert.setStatus(CommonConst.YES);
            } else {
                cert.setStatus(CommonConst.NO);
            }
            save(cert);
        });
    }

    @Override
    public void dataFmt(Cert record) {
        record.setStatusShow(dictService.cgetNameByValue(CertConst.CERT_STATUS, record.getStatus()));
        record.setIsForeverShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getIsForever()));
        record.setTypeShow(dictService.cgetNameByValue(CertConst.CERT_TYPE, record.getType()));
    }

    @Override
    public List<Cert> getListByModelId(String modelId) {
        Example example = new Example(Cert.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("modelId", modelId);
        // order by
        example.setOrderByClause("create_date desc");
        List<Cert> records = mapper.selectByExample(example);
        records.forEach(this::dataFmt);
        
        return records;
    }

}
