package cn.trasen.ams.common.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.service.CategoryService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.device.service.DeviceService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.CategoryMapper;
import cn.trasen.ams.common.model.Category;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CategoryServiceImpl
 * @Description TODO
 * @date 2025年3月6日 下午3:31:39
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryMapper mapper;

    @Autowired
    private RedisService redisService;

    private String cacheKeyPrefix = "m:service:CategoryService:";

    @Transactional(readOnly = false)
    @Override
    public Integer save(Category record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        record.setLevel(1);
        record.setIsLeafNode("Y");
        record.setTreeIds(record.getId());
        record.setIsEnable(CommonConst.YES);
        autoFillColumn(record);
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        if (!StringUtils.isBlank(record.getParentId())) {
            Category category = selectById(record.getParentId());
            if (category != null) {
                String treeIds = category.getTreeIds() + "," + record.getId();
                record.setParentCode(category.getCode());
                record.setTreeIds(treeIds);
                // treeIds 根据逗号转数组的长度等于level
                String[] treeIdArr = treeIds.split(",");
                record.setLevel(treeIdArr.length);

                // 更新父节点为非叶子节点
                category.setIsLeafNode("N");
                mapper.updateByPrimaryKeySelective(category);

            } else {
                throw new RuntimeException("父级分类不存在");
            }
        }
        record.setCode(genCode(record));

        if (record.getLevel() > 3) {
            throw new RuntimeException("分类层级不能超过3级");
        }

        // 清除缓存
        redisService.clearPrefix(cacheKeyPrefix);

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Category record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();

        autoFillColumn(record);

        Category category = selectById(record.getId());
        // 父级分类可能不存在

        Boolean parentChanged = false;
        // 如果父级的ID存在 并且变化了才操作
        if (StringUtils.isNotBlank(record.getParentId()) && !record.getParentId().equals(category.getParentId())) {
            // 要么是新更新父类ID
            Category categoryParent = selectById(record.getParentId());
            if (categoryParent == null) {
                throw new RuntimeException("父级分类不存在");
            }
            // 防止成环
            if (categoryParent.getTreeIds().contains(record.getId())) {
                throw new RuntimeException("不能将父级分类设置为自己的子分类");
            }

            String treeIds = categoryParent.getTreeIds() + "," + record.getId();
            record.setParentCode(categoryParent.getCode());
            record.setTreeIds(treeIds);
            // treeIds 根据逗号转数组的长度等于level
            String[] treeIdArr = treeIds.split(",");
            record.setLevel(treeIdArr.length);

            // 更新当前父节点为非叶子节点
            categoryParent.setIsLeafNode("N");
            mapper.updateByPrimaryKeySelective(categoryParent);

            // 标记上级变化了
            parentChanged = true;

        }

        // 转成根节点，但是之前不是根节点
        if (StringUtils.isBlank(record.getParentId()) && StringUtils.isNotBlank(category.getParentId())) {
            // 假设把当前节点变成了根节点
            record.setParentCode("");
            record.setParentId("");
            record.setTreeIds(record.getId());
            record.setLevel(1);
            parentChanged = true;
        }

        // 不管什么情况，不改变一开始的编码规则
//        record.setCode(genCode(record));

        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        int res = mapper.updateByPrimaryKeySelective(record);


        // 对父亲负责
        // 检查是不是之前就有父节点，假设有的话，查询是否还存在子集，重新给是否是叶节点赋值
        if (category.getParentId() != null && parentChanged) {
            // 查询曾经的父亲是否还有孩子
            updateIsLeafNodeInfo(category.getParentId());
        }

        // 对儿子负责，更新所有儿子的 treeIds 和 level
        if (parentChanged) {
            mapper.changeSonNode(category.getParentId(), category.getTreeIds(), record.getTreeIds(), record.getLevel() - category.getLevel());
        }

        // 清除缓存
        redisService.clearPrefix(cacheKeyPrefix);
        return res;
    }


    private void autoFillColumn(Category record) {
        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    // 当节点变更父级或者节点被删除时候，维护父节点的叶节点情况
    private void updateIsLeafNodeInfo(String categoryId) {
        if (categoryId == null) {
            return;
        }
        Boolean hasSon = hasSon(categoryId);
        if (!hasSon) {
            // 如果没有了需要设置isLeafNode 为 Y
            Category categoryParentPrev = selectById(categoryId);
            categoryParentPrev.setIsLeafNode("Y");
            categoryParentPrev.setUpdateDate(new Date());
            mapper.updateByPrimaryKeySelective(categoryParentPrev);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");

        Category cur = selectById(id);
        if (cur == null) {
            throw new RuntimeException("分类不存在");
        }

        cur.setId(id);
        cur.setUpdateDate(new Date());
        cur.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            cur.setUpdateUser(user.getUsercode());
            cur.setUpdateUserName(user.getUsername());
        }

        // 先删除当前节点
        int res = mapper.updateByPrimaryKeySelective(cur);
        updateIsLeafNodeInfo(cur.getParentId());

        // 清除缓存
        redisService.clearPrefix(cacheKeyPrefix);

        return res;
    }

    private String genCode(Category category) {
        Example example = new Example(Category.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        // 不把自己包含在哪
        criteria.andNotEqualTo("id", category.getId());

        if (category.getParentId() == null) {
            criteria.andEqualTo("parentId", "");
        } else {
            criteria.andEqualTo("parentId", category.getParentId());
        }
        int count = mapper.selectCountByExample(example);
        count++;

        if (category.getParentCode() == null) {
            return String.format("%02d", count);
        }
        return category.getParentCode() + String.format("%02d", count);
    }

    @Override
    public Category selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public Category cSelectById(String id) {
        try {
            return (Category) redisService.fetch(cacheKeyPrefix + "cSelectById", () -> selectById(id), 3600);
        } catch (Exception e) {
            throw new RuntimeException("获取物资分类失败");
        }
    }

    @Transactional(readOnly = true)
    @Override
    public Boolean hasSon(String id) {
        Example example = new Example(Category.class);
        example.createCriteria().andEqualTo("parentId", id).andEqualTo("isDeleted", "N");

        int count = mapper.selectCountByExample(example);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }


    public List<Category> getList(Page page, Category record) {
        Example example = new Example(Category.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (!StringUtil.isEmpty(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }

        if (record.getIsLeafNode() != null && record.getIsLeafNode().equals("Y")) {
            criteria.andEqualTo("isLeafNode", "Y");
        }

        if (!StringUtil.isEmpty(record.getParentId())) {
            criteria.andLike("treeIds", "%" + record.getParentId() + "%");
        }

        if (!StringUtil.isEmpty(record.getSysType())) {
            criteria.andEqualTo("sysType", record.getSysType());
        }

        if (!StringUtil.isEmpty(record.getIsEnable())) {
            criteria.andEqualTo("isEnable", record.getIsEnable());
        }

        // order by code
        example.setOrderByClause("code,level");

        List<Category> list = null;

        if (page == null) {
            list = mapper.selectByExample(example);
        } else {
            list = mapper.selectByExampleAndRowBounds(example, page);
        }

        // 根据list 拿到所有的 parentId
        List<String> parentIdList = list.stream().map(Category::getParentId).collect(Collectors.toList());
        // 根据 parentIdList 获取 parentList

        if (CollectionUtils.isNotEmpty(parentIdList)) {
            Example example1 = new Example(Category.class);
            example1.createCriteria().andIn("id", parentIdList);
            List<Category> parentList = mapper.selectByExample(example1);
            Map<String, Category> parentMap = parentList.stream().collect(Collectors.toMap(Category::getId, item -> item));
            list.stream().forEach(item -> {
                if (parentMap.containsKey(item.getParentId())) {
                    item.setParentName(parentMap.get(item.getParentId()).getName());
                }
            });
        }

        return list;
    }

    @Override
    public List<Category> getListByIds(List<String> ids) {
        Example example = new Example(Category.class);
        example.createCriteria().andIn("id", ids);
        return mapper.selectByExample(example);
    }

    @Override
    public Map fetchAllCategoryName2Map(String sysType) {
        Example example = new Example(Category.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        // 只查 叶节点
        criteria.andEqualTo("isLeafNode", "Y");

        if (!StringUtils.isEmpty(sysType)) {
            criteria.andEqualTo("sysType", sysType);
        }

        List<Category> list = mapper.selectByExample(example);
        Map<String, String> map = new HashMap<>();

        for (Category category : list) {
            map.put(category.getName(), category.getId());
        }
        return map;
    }

    @Override
    public List<Category> getSonListByParentId(String parentId) {
        Example example = new Example(Category.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andLike("treeIds", "%" + parentId + "%");
        List<Category> list = mapper.selectByExample(example);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list;
    }

    @Override
    public String getTopIdById(String id) {
        Category category = selectById(id);
        if (category == null) {
            return null;
        }
        String treeIds = category.getTreeIds();
        if (StringUtils.isEmpty(treeIds)) {
            return null;
        }
        String[] ids = treeIds.split(",");
        if (ids.length == 0) {
            return null;
        }
        return ids[0];
    }


    @Override
    public DataSet<Category> getDataSetList(Page page, Category record) {
        List<Category> records = getList(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<TreeModel> getTreeList(Integer level, String isEnable, String sysType) {
        // 参数验证
        if (level != null && level < 0) {
            throw new IllegalArgumentException("Level cannot be negative");
        }

        Example example = new Example(Category.class);
        example.setOrderByClause("seq_no ASC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        // 添加系统类型过滤条件
        if (StringUtils.isNotEmpty(sysType)) {
            criteria.andEqualTo("sysType", sysType);
        }
        // 添加启用状态过滤条件
        if (StringUtils.isNotEmpty(isEnable)) {
            criteria.andEqualTo("isEnable", isEnable);
        }

        // 添加层级过滤条件
        if (level != null && level > 0) {
            example.and().andCondition(" LENGTH(tree_ids)-LENGTH(REPLACE(tree_ids,',',''))+1 <= ", level);
        }

        List<Category> categories = mapper.selectByExample(example);
        return buildTreeFromCategories(categories);
    }

    @Override
    public List<TreeModel> getTreeListByIds(List<String> ids) {
        // 参数验证
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        // 首先查询指定的分类，获取它们的tree_ids
        List<Category> targetCategories = getCategoriesByIds(ids);
        if (CollectionUtils.isEmpty(targetCategories)) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的ID（包括目标分类及其所有上级分类）
        Set<String> allRequiredIds = collectAllRequiredIds(targetCategories);

        // 查询所有相关的分类数据（如果有新的ID需要查询）
        List<Category> allCategories;
        if (allRequiredIds.size() > ids.size()) {
            // 有新的上级分类ID需要查询
            allCategories = getCategoriesByIds(new ArrayList<>(allRequiredIds));
        } else {
            // 没有上级分类，直接使用已查询的数据
            allCategories = targetCategories;
        }

        return buildTreeFromCategories(allCategories);
    }

    /**
     * 收集所有需要查询的ID（包括目标分类及其所有上级分类）
     *
     * @param targetCategories 目标分类列表
     * @return 所有相关的分类ID集合
     */
    private Set<String> collectAllRequiredIds(List<Category> targetCategories) {
        Set<String> allIds = new HashSet<>();

        for (Category category : targetCategories) {
            // 添加当前分类ID
            allIds.add(category.getId());

            // 解析tree_ids，添加所有上级分类ID
            if (StringUtils.isNotEmpty(category.getTreeIds())) {
                String[] treeIdArray = category.getTreeIds().split(",");
                for (String treeId : treeIdArray) {
                    if (StringUtils.isNotEmpty(treeId.trim())) {
                        allIds.add(treeId.trim());
                    }
                }
            }
        }

        return allIds;
    }

    /**
     * 根据ID列表查询分类信息
     *
     * @param ids ID列表
     * @return 分类列表
     */
    private List<Category> getCategoriesByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        Example example = new Example(Category.class);
        example.setOrderByClause("seq_no ASC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        return mapper.selectByExample(example);
    }


    /**
     * 将Category列表转换为TreeModel树结构
     *
     * @param categories Category列表
     * @return TreeModel树结构列表
     */
    private List<TreeModel> buildTreeFromCategories(List<Category> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return new ArrayList<>();
        }

        // 使用Stream API一次性完成转换和树结构构建，提高性能
        List<TreeModel> nodes = categories.stream()
                .map(category -> {
                    TreeModel node = new TreeModel();
                    node.setId(category.getId());
                    node.setPid(StringUtils.isBlank(category.getParentId()) ? "" : category.getParentId());
                    node.setName(category.getName());
                    node.setCode(category.getCode());
                    return node;
                })
                .collect(Collectors.toList());

        // 构建树结构
        CommTree commTree = new CommTree();
        List<TreeModel> trees = commTree.CommTreeList(nodes);
        return trees != null ? trees : new ArrayList<>();
    }

}
