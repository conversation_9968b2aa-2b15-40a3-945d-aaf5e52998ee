package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.dao.TableMapper;
import cn.trasen.ams.common.service.TableService;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: DictServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/9/3 16:47
 * @version: 1.0
 */

@Service
public class TableServiceImpl implements TableService {

    @Autowired
    private TableMapper mapper;

    /**
     * @param table:
     * @param pk:
     * @param id:
     * @param ignoreIsDelete:
     * @return boolean
     * <AUTHOR>
     * @description 请不要直接从controller 调用，让前台的动态参数入参到本函数，会出现严重注入安全问题
     * @date 2024/9/11 09:06
     */
    @Override
    public boolean has(String table, String pk, String id, String ignoreIsDelete) {
        if (StringUtils.isBlank(table) || StringUtils.isBlank(id)) {
            return false;
        }

        return mapper.hasById(table, pk, id, ignoreIsDelete) >= 1 ? true : false;
    }

    /**
     * @param table:
     * @param pk:
     * @param id:
     * @param ignoreIsDelete:
     * @return boolean
     * <AUTHOR>
     * @description 请不要直接从controller 调用，让前台的动态参数入参到本函数，会出现严重注入安全问题
     * @date 2024/9/11 09:06
     */
    @Override
    public boolean has(String table, String pk, String[] id, String ignoreIsDelete) {
        if (StringUtils.isBlank(table)) {
            return false;
        }

        if (id == null || id.length <= 0) {
            return false;
        }

        int count = mapper.hasByIdList(table, pk, id, ignoreIsDelete);

        return count == id.length ? true : false;
    }

    @Override
    public List<DictItemResp> getDictItemByTypeCode(String typeCode) {

        String ssoOrgCode = "";
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            ssoOrgCode = user.getCorpcode();
        }

        return mapper.getDictItemByTypeCode(typeCode, ssoOrgCode);
    }
}
