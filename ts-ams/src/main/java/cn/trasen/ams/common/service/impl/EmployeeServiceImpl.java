package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.EmployeeService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.sso.ThpsUserSimple;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.sso.SystemMenusClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: EmployeeServiceImpl
 * @author: chenbin
 * @description: 员工相关的远程调用
 * @date: 2025/2/21 11:03
 * @version: 1.0
 */
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private SystemMenusClient systemMenusClient;

    @Override
    public EmployeeResp cgetEmployeeByCode(String code) {
        EmployeeResp employeeResp;
        String cacheKey = "employee:" + code;
        try {
            employeeResp = (EmployeeResp) redisService.fetch(cacheKey, () -> {
                return hrmsEmployeeFeignService.getEmployeeDetailByCode(code).getObject();
            }, 60 * 5);
        } catch (Exception e) {
            throw new RuntimeException("员工读取远程调用失败", e);
        }

        if (employeeResp == null) {
            redisService.clearFetch(cacheKey);
            throw new RuntimeException("员工未配置");
        }
        return employeeResp;
    }

    @Override
    public String cgetThpsUserSetByOperationId(String operationId) {
        try {
            String cacheKey = "operationId:" + operationId;

            String codeSet = (String) redisService.fetch(cacheKey, () -> {
                DataSet<ThpsUserSimple> thpsUserSimpleDataSet = systemMenusClient.getUserlistByOperationId(operationId, "desc", "1", "10000");
                List<String> userIds = new ArrayList<>();
                thpsUserSimpleDataSet.getRows().forEach(row -> userIds.add(row.getUsercode()));

                if (userIds.isEmpty()) {
                    return null;
                }

                return String.join(",", userIds);
            }, 60 * 5);

            if (codeSet == null) {
                redisService.clearFetch(cacheKey);
            }

            return codeSet;
        } catch (Exception e) {
            throw new RuntimeException("用户读取远程调用失败", e);
        }
    }
}
