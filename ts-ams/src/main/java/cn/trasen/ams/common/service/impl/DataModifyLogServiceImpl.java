package cn.trasen.ams.common.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.DataModifyLogConst;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.homs.core.utils.StringUtil;
import com.mysql.cj.x.protobuf.MysqlxDatatypes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.common.dao.DataModifyLogMapper;
import cn.trasen.ams.common.model.DataModifyLog;
import cn.trasen.ams.common.service.DataModifyLogService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.beanutils.PropertyUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DataModifyLogServiceImpl
 * @Description TODO
 * @date 2025年7月24日 下午6:39:31
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DataModifyLogServiceImpl implements DataModifyLogService {

    @Autowired
    private DataModifyLogMapper mapper;

    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DataModifyLog record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DataModifyLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DataModifyLog record = new DataModifyLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DataModifyLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DataModifyLog> getDataSetList(Page page, DataModifyLog record) {
        Example example = new Example(DataModifyLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        if (!StringUtil.isEmpty(record.getTableName())) {
            criteria.andEqualTo("tableName", record.getTableName()); // 如果没有指定表名，则查询所有表的日志
        }

        if (!StringUtil.isEmpty(record.getRowPkFlowNo())) {
            criteria.andEqualTo("rowPkFlowNo", record.getRowPkFlowNo());
        }

        if (!StringUtil.isEmpty(record.getRowName())) {
            // like
            criteria.andLike("rowName", "%" + record.getRowName() + "%");
            //criteria.andEqualTo("rowName", record.getRowName());
        }

        if (!StringUtil.isEmpty(record.getRowModifyType())) {
            criteria.andEqualTo("rowModifyType", record.getRowModifyType());
        }

        if (!StringUtil.isEmpty(record.getSearch())) {
            // like
            criteria.andLike("search", "%" + record.getSearch() + "%");
        }

        if (!CollectionUtils.isEmpty(record.getSearchList())) {
            // like and
            for (String searchItem : record.getSearchList()) {
                if (!StringUtil.isEmpty(searchItem)) {
                    criteria.andLike("search", "%" + searchItem + "%");
                }
            }
        }
        // order by
        example.setOrderByClause("create_date desc");

        List<DataModifyLog> records = mapper.selectByExampleAndRowBounds(example, page);
        records.forEach(this::dataFmt);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    public void dataFmt(DataModifyLog record) {
        record.setRowModifyTypeShow(dictService.cgetNameByValue(DataModifyLogConst.DATA_MODIFY_TYPE, record.getRowModifyType()));
    }

    @Override
    public void writeLog(String tableName, String pkValue, String flowNo, String name, String type, Object o, Object n, String search) {
        try {
            // 创建日志记录
            DataModifyLog logRecord = new DataModifyLog();
            logRecord.setTableName(tableName);
            logRecord.setRowPkValue(pkValue);
            logRecord.setRowPkFlowNo(flowNo);
            logRecord.setRowName(name);
            logRecord.setRowModifyType(type);
            logRecord.setSearch(search);


            // 根据操作类型处理数据
            switch (type.toLowerCase()) {
                case DataModifyLogConst.CREATE: // 创建
                    if (o != null) {
                        logRecord.setRowJsonOld(null);
                        logRecord.setRowJsonNew(convertToJson(o));
                        logRecord.setRowDiff("新增记录");
                    }
                    break;
                case DataModifyLogConst.DELETE: // 删除
                    if (o != null) {
                        logRecord.setRowJsonOld(convertToJson(o));
                        logRecord.setRowJsonNew(null);
                        logRecord.setRowDiff("删除记录");
                    }
                    break;
                case DataModifyLogConst.UPDATE: // 更新
                    if (o != null && n != null) {
                        logRecord.setRowJsonOld(convertToJson(o));
                        logRecord.setRowJsonNew(convertToJson(n));
                        logRecord.setRowDiff(generateDiff(o, n));
                    }
                    break;
                case DataModifyLogConst.SETTLE:
                    // 结算操作，通常不涉及具体数据变更
                    logRecord.setRowJsonOld(convertToJson(o));
                    logRecord.setRowJsonNew(null);
                    logRecord.setRowDiff("结算操作");
                    break;
                case DataModifyLogConst.UNSETTLE:
                    // 反结算操作，通常不涉及具体数据变更
                    logRecord.setRowJsonOld(convertToJson(o));
                    logRecord.setRowJsonNew(null);
                    logRecord.setRowDiff("反结算操作");
                    break;
                default:
                    log.warn("未知的操作类型: {}", type);
                    return;
            }

            // 保存日志记录
            save(logRecord);

        } catch (Exception e) {
            log.error("写入数据修改日志失败: tableName={}, flowNo={}, type={}", tableName, flowNo, type, e);
            throw new RuntimeException("写入数据修改日志失败" + e.getMessage());
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJson(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("对象转JSON失败: {}", e.getMessage());
            return "序列化失败: " + e.getMessage();
        }
    }


    /**
     * 生成两个对象的差异记录
     */
    private static final Set<String> IGNORE_DIFF_FIELDS = new HashSet<>(Arrays.asList("createDate", "createUser", "createUserName", "deptId", "deptName", "updateDate", "updateUser", "updateUserName", "ssoOrgCode", "ssoOrgName", "isDeleted"));

    private String generateDiff(Object oldObj, Object newObj) {
        if (oldObj == null || newObj == null) {
            return "对象为空，无法比较差异";
        }

        try {
            List<String> differences = new ArrayList<>();
            Class<?> clazz = oldObj.getClass();

            // 获取所有字段
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                // 跳过静态字段、transient字段和忽略字段
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || java.lang.reflect.Modifier.isTransient(field.getModifiers()) || IGNORE_DIFF_FIELDS.contains(field.getName())) {
                    continue;
                }

                field.setAccessible(true);

                try {
                    Object oldValue = field.get(oldObj);
                    Object newValue = field.get(newObj);

                    // 如果新值为null，说明该字段没有被修改，跳过差异比较
                    if (newValue == null || oldValue == null) {
                        continue;
                    }


                    // 比较值是否相等，对BigDecimal类型进行特殊处理
                    boolean isEqual;
                    if (oldValue instanceof BigDecimal && newValue instanceof BigDecimal) {
                        // BigDecimal使用compareTo方法比较数值是否相等（忽略精度差异）
                        isEqual = ((BigDecimal) oldValue).compareTo((BigDecimal) newValue) == 0;
                    } else {
                        // 其他类型使用Objects.equals比较
                        isEqual = Objects.equals(oldValue, newValue);
                    }

                    if (!isEqual) {
                        String fieldName = getFieldDisplayName(field);
                        String oldValueStr = oldValue.toString();
                        String newValueStr = newValue.toString();

                        // 格式化差异记录：{属性名称}:【{旧值}】-> 【新值】
                        String diff = String.format("%s:【%s】-> 【%s】", fieldName, oldValueStr, newValueStr);
                        differences.add(diff);
                    }
                } catch (IllegalAccessException e) {
                    log.warn("访问字段失败: {}", field.getName(), e);
                }
            }

            if (differences.isEmpty()) {
                return "无差异";
            } else {
                return String.join("\n", differences);
            }

        } catch (Exception e) {
            log.error("生成差异记录失败", e);
            return "生成差异记录失败: " + e.getMessage();
        }
    }

    /**
     * 获取字段的显示名称
     * 优先获取@ApiModelProperty的value值，如果没有则使用字段名
     */
    private String getFieldDisplayName(Field field) {
        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
        if (apiModelProperty != null && apiModelProperty.value() != null && !apiModelProperty.value().trim().isEmpty()) {
            return apiModelProperty.value().trim();
        }
        return field.getName();
    }
}
