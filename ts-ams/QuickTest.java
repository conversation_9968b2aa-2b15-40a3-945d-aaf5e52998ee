/**
 * 快速测试操作符修复
 */
public class QuickTest {
    
    public static void main(String[] args) {
        QuickTest test = new QuickTest();
        test.testOperatorFix();
    }
    
    public void testOperatorFix() {
        System.out.println("=== 操作符修复测试 ===");
        
        // 测试原始问题
        String sql = "SELECT * FROM table WHERE num <= 100000 AND id >= 1";
        System.out.println("原始SQL: " + sql);
        
        String result = standardizeSqlFormat(sql);
        System.out.println("处理后: " + result);
        
        if (result.contains("< =") || result.contains("> =")) {
            System.out.println("❌ 失败：仍然包含错误的分割操作符");
        } else if (result.contains(" <= ") && result.contains(" >= ")) {
            System.out.println("✅ 成功：操作符处理正确");
        }
        
        // 测试已经被分割的情况
        String brokenSql = "SELECT * FROM table WHERE num < = 100000";
        System.out.println("\n已分割SQL: " + brokenSql);
        
        String fixed = cleanMyBatisDynamicSql(brokenSql);
        System.out.println("修复后: " + fixed);
        
        if (fixed.contains("< =")) {
            System.out.println("❌ 修复失败");
        } else {
            System.out.println("✅ 修复成功");
        }
    }
    
    private String cleanMyBatisDynamicSql(String sql) {
        return sql.replaceAll("\\s*<\\s*=\\s*", " <= ")
                  .replaceAll("\\s*>\\s*=\\s*", " >= ")
                  .replaceAll("\\s*!\\s*=\\s*", " != ")
                  .replaceAll("\\s*<\\s*>\\s*", " <> ");
    }
    
    private String standardizeSqlFormat(String sql) {
        // 使用占位符方法
        sql = sql.replaceAll("\\s*<=\\s*", " __LE__ ")
                .replaceAll("\\s*>=\\s*", " __GE__ ")
                .replaceAll("\\s*<>\\s*", " __NE1__ ")
                .replaceAll("\\s*!=\\s*", " __NE2__ ");
        
        sql = sql.replaceAll("\\s*,\\s*", ", ")
                .replaceAll("\\s*=\\s*", " = ")
                .replaceAll("\\s*<\\s*", " < ")
                .replaceAll("\\s*>\\s*", " > ");
        
        sql = sql.replaceAll(" __LE__ ", " <= ")
                .replaceAll(" __GE__ ", " >= ")
                .replaceAll(" __NE1__ ", " <> ")
                .replaceAll(" __NE2__ ", " != ");
        
        return sql.replaceAll("\\s+", " ").trim();
    }
}
