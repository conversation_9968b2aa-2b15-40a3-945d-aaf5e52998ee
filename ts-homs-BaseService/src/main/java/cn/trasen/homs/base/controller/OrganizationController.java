package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.OrgChildrenEmplListReq;
import cn.trasen.homs.base.bean.OrgChildrenEmplListRes;
import cn.trasen.homs.base.bean.OrganizationChildrenListResp;
import cn.trasen.homs.base.bean.OrganizationFrameworkResp;
import cn.trasen.homs.base.bean.OrganizationImport;
import cn.trasen.homs.base.bean.OrganizationLeaderResp;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.bean.OrganizationListResp;
import cn.trasen.homs.base.bean.OrganizationListSimpleRes;
import cn.trasen.homs.base.bean.OrganizationMergeReq;
import cn.trasen.homs.base.bean.OrganizationMoveReq;
import cn.trasen.homs.base.bean.OrganizationSaveReq;
import cn.trasen.homs.base.bean.OrganizationSplitReq;
import cn.trasen.homs.base.bean.PlatformBody;
import cn.trasen.homs.base.bo.OrgEmpInBO;
import cn.trasen.homs.base.bo.OrgEmpOutBO;
import cn.trasen.homs.base.dto.ReplaceLeader;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.saasOrg.model.HrmsOrg;
import cn.trasen.homs.base.saasOrg.service.HrmsOrgService;
import cn.trasen.homs.base.service.CommInterfaceRegisterService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationService;
import cn.trasen.homs.base.service.IOrganizationVersionHistoryService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 组织机构
 * @return:
 * @author: liyuan
 * @createTime: 2021/7/26 10:14
 */
@Slf4j
@Api(tags = "组织机构")
@RequestMapping("/organization")
@RestController
public class OrganizationController {


    @Autowired
    IOrganizationService organizationService;

    @Autowired
    IOrganizationVersionHistoryService organizationVersionHistoryService;

    @Autowired
    CommInterfaceRegisterService commInterfaceRegisterService;
    
    @Autowired
    private IDictItemService dictItemService;
    
    @Autowired
    private HrmsOrgService hrmsOrgService;


    @ApiOperation(value = "获取组织架构图历史版本", notes = "获取组织架构图历史版本")
    @RequestMapping(value = "/getFrameworkHistoryVersionList", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult getFrameworkHistoryVersionList() {
        return PlatformResult.success(organizationVersionHistoryService.getList());
    }

    @ApiOperation(value = "保存组织机构图版本", notes = "保存组织机构图版本")
    @RequestMapping(value = "/saveFrameworkVersion", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult saveFrameworkVersion() {
        OrganizationListReq organizationListReq = new OrganizationListReq();
        List<OrganizationFrameworkResp> organizationFrameworkRespList = organizationService.getFramework(organizationListReq);
        return PlatformResult.success(organizationVersionHistoryService.add(JSON.toJSONString(organizationFrameworkRespList)));
    }

    @ApiOperation(value = "组织机构图", notes = "组织机构图")
    @PostMapping(value = "/getFramework")
    public PlatformResult<List<OrganizationFrameworkResp>> getFramework(@RequestBody OrganizationListReq organizationListReq) {
        return PlatformResult.success(organizationService.getFramework(organizationListReq));
    }

    @ApiOperation(value = "获取treetable", notes = "获取treetable")
    @PostMapping(value = "/getChildrenList")
    public PlatformResult<List<OrganizationChildrenListResp>> getChildrenList(@RequestBody OrganizationListReq organizationListReq) {
        return PlatformResult.success(organizationService.getChildrenList(organizationListReq));
    }

    @ApiOperation(value = "根据ID批量获取组织机构", notes = "根据ID批量获取组织机构")
    @PostMapping(value = "/getListByIds")
    public PlatformResult<List<OrganizationListResp>> getListByIds(@RequestBody List<String> idList) {
        return PlatformResult.success(organizationService.getListByIds(idList));
    }

    @ApiOperation(value = "获取列表", notes = "获取列表")
    @PostMapping(value = "/getSimpleList")
    public PlatformResult<List<OrganizationListSimpleRes>> getSimpleList(@RequestBody OrganizationListReq organizationListReq) {
        return PlatformResult.success(organizationService.getSimpleList(organizationListReq));
    }

    @ApiOperation(value = "获取列表", notes = "获取列表")
    @PostMapping(value = "/getAllList")
    public DataSet<OrganizationListResp> getAllPageList(Page page, OrganizationListReq organizationListReq) {
        return organizationService.getAllPageList(page, organizationListReq);
    }


    @ApiOperation(value = "获取列表", notes = "获取列表")
    @PostMapping(value = "/pageSimple")
    public DataSet<OrganizationListSimpleRes> pageSimple(Page page, @RequestBody OrganizationListReq organizationListReq) {
        return organizationService.pageSimple(page, organizationListReq);
    }


    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/add")
    public PlatformResult add(@RequestBody @Validated OrganizationSaveReq organizationAddReq) {

        Organization organization = organizationService.add(organizationAddReq);

        try {
            organizationService.syncOrganization2ContactsSingleTime("add", organization, null);
        } catch (Exception e) {
            log.error("同步通讯录：" + e.getMessage(), e);
        }
        try {
        	commInterfaceRegisterService.addSyncOrg(organization.getOrganizationId());
        } catch (Exception e) {
            log.error("同步科室异常：" + e.getMessage(), e);
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = "/edit")
    public PlatformResult edit(@RequestBody @Validated OrganizationSaveReq organizationAddReq) {
        Organization o = organizationService.selectOneById(organizationAddReq.getOrganizationId());
        organizationService.update(organizationAddReq);

        try {
            Organization n = organizationService.selectOneById(organizationAddReq.getOrganizationId());
            organizationService.syncOrganization2ContactsSingleTime("edit", n, o);
        } catch (Exception e) {
            log.error("同步通讯录：" + e.getMessage(), e);
        }

        try {
        	commInterfaceRegisterService.updateSyncOrg(organizationAddReq.getOrganizationId());
        } catch (Exception e) {
            log.error("同步科室异常：" + e.getMessage(), e);
        }
        return PlatformResult.success();
    }

    @ApiOperation(value = "删除", notes = "删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")})
    @RequestMapping(value = "/del", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult del(@RequestParam("orgId") String orgId) {

        organizationService.delete(orgId);
        try {
            Organization o = organizationService.selectOneById(orgId);
            organizationService.syncOrganization2ContactsSingleTime("delete", o, null);
        } catch (Exception e) {
            log.error("同步通讯录：" + e.getMessage(), e);
        }

        try {
        	commInterfaceRegisterService.updateSyncOrg(orgId);
        } catch (Exception e) {
            log.error("同步科室异常：" + e.getMessage(), e);
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "验证删除", notes = "验证删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")})
    @RequestMapping(value = "/verifyDel", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult verifyDel(@RequestParam("orgId") String orgId) {
        organizationService.verifyEnable(orgId, "组织下有子级或人员，不能删除！");
        return PlatformResult.success();
    }


    @ApiOperation(value = "获取详情", notes = "获取详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")})
    @RequestMapping(value = "/getAll", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult getAll(String orgId) {
        return PlatformResult.success(organizationService.getAll(orgId));
    }


    @ApiOperation(value = "启用禁用", notes = "启用禁用")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String"), @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")})
    @RequestMapping(value = "/enable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult enable(@RequestParam("orgId") String orgId, @RequestParam("enable") String enable) {
        organizationService.enable(orgId, enable);
        try {
        	commInterfaceRegisterService.updateSyncOrg(orgId);
        } catch (Exception e) {
            log.error("同步科室异常：" + e.getMessage(), e);
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "验证禁用", notes = "验证禁用")
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")})
    @RequestMapping(value = "/verifyEnable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult verifyEnable(@RequestParam("orgId") String orgId) {
        organizationService.verifyEnable(orgId, "组织下有子级或人员，不能禁用！");
        return PlatformResult.success();
    }

    @ApiOperation(value = "合并", notes = "合并")
    @PostMapping(value = "/merge")
    public PlatformResult merge(@RequestBody OrganizationMergeReq organizationMergeReq) {
        organizationService.merge(organizationMergeReq);
        return PlatformResult.success();
    }

    @ApiOperation(value = "拆分", notes = "拆分")
    @PostMapping(value = "/split")
    public PlatformResult split(@RequestBody OrganizationSplitReq organizationSplitReq) {
        organizationService.split(organizationSplitReq);
        return PlatformResult.success();
    }

    @ApiOperation(value = "移动", notes = "移动")
    @PostMapping(value = "/move")
    public PlatformResult move(@RequestBody OrganizationMoveReq organizationMoveReq) {
        organizationService.move(organizationMoveReq);
        try {
        	commInterfaceRegisterService.updateSyncOrg(organizationMoveReq.getOrganizationId());
        } catch (Exception e) {
            log.error("同步科室异常：" + e.getMessage(), e);
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "修改排序", notes = "修改排序")
    @PostMapping(value = "/updateSort")
    public PlatformResult updateSort(@RequestBody List<OrganizationSaveReq> organizationSaveReqs) {
        organizationService.updateSort(organizationSaveReqs);
        return PlatformResult.success();
    }

    @GetMapping(value = "/download/template/importOrganization")
    @ApiOperation(value = "下载机构模板导入模板", notes = "下载机构模板导入模板")
    public void downloadImportOrganizationTemplate(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "组织导入模板.xls";
            String template = "template/importOrganization.xls";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入", notes = "导入")
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "type 1:增量 2全量", required = true, dataType = "String")})
    @PostMapping(value = "/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file, String type) {
        List<OrganizationImport> organizationImports = (List<OrganizationImport>) ImportExcelUtil.getExcelDatas(file, OrganizationImport.class);
        return organizationService.excelImportOrganization(organizationImports, type);
    }

    @ApiOperation(value = "同步部门机构", notes = "同步部门机构")
    @GetMapping(value = "/synAllSystemDept")
    public PlatformResult synAllSystemDept() {
        organizationService.synAllSystemDept();
        return PlatformResult.success();
    }

    @ApiOperation(value = "导出", notes = "导出")
    @RequestMapping(value = "/export", method = {RequestMethod.POST, RequestMethod.GET})
    public void export(OrganizationListReq organizationListReq, HttpServletResponse response, HttpServletRequest request) throws Exception {
        Page page = new Page(1, Integer.MAX_VALUE);
        organizationListReq = new OrganizationListReq();
        DataSet<OrganizationListResp> dataSet = organizationService.getAllPageList(page, organizationListReq);
        List<OrganizationListResp> organizationListRespList = dataSet.getRows();
        List<OrganizationListResp> list = new ArrayList<>();
        list.add(organizationListRespList.get(0));
        organizationListRespList.forEach(o->{
            List<OrganizationListResp> reList = organizationService.getChildrenResp(organizationListRespList, o.getOrganizationId());
            if(CollUtil.isNotEmpty(reList)){
                list.addAll(reList);
            }
        });
        String excelName = "组织机构";
        List<String> headList = new ArrayList<>();
        headList.add("组织名称");
        headList.add("上级组织");
        headList.add("组织类型");
        headList.add("人数");
        headList.add("领导");
        headList.add("状态");

        headList.add("组织描述");

        List<String> fieldList = new ArrayList<>();
        fieldList.add("组织名称");
        fieldList.add("上级组织");
        fieldList.add("组织类型");
        fieldList.add("人数");
        fieldList.add("领导");
        fieldList.add("状态");
        fieldList.add("组织描述");
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> map;
        for (OrganizationListResp o : list) {
            map = new HashMap<>();
            map.put("组织名称", o.getName());
            map.put("上级组织", o.getParentName());
            map.put("组织类型", o.getOrgFlagLable());
            map.put("人数", o.getEmployeeNum());

            String leaderstr = "";
            if (o.getLeaders() != null) {
                for (OrganizationLeaderResp leaderResp : o.getLeaders()) {

                    if (leaderResp.getEmployeeIdList() != null) {
                        int i = 0;
                        for (HrmsEmployeeResp hrmsEmployeeResp : leaderResp.getEmployeeIdList()) {
                            i++;
                            leaderstr = leaderstr + hrmsEmployeeResp.getEmployeeName() + "(" + leaderResp.getRoleName() + ")";
                            if (i < o.getLeaders().size()) {
                                leaderstr = leaderstr + "、";
                            }
                        }
                    }
                }
            }
            map.put("领导", leaderstr);


            map.put("状态", o.getIsEnableLable());
            map.put("组织描述", o.getRemark());
            dataList.add(map);
        }
        ExportUtil.createExcel(excelName, headList, fieldList, dataList, response, request);
    }

    @ApiOperation(value = "获取子机构和员工信息", notes = "获取子机构和员工信息")
    @PostMapping(value = "/getOrgChildrenEmplList")
    public PlatformResult<OrgChildrenEmplListRes> getOrgChildrenEmplList(@RequestBody OrgChildrenEmplListReq orgEmplListReq) {
        return PlatformResult.success(organizationService.getOrgChildrenEmplList(orgEmplListReq));
    }

    @ApiOperation(value = "获取组织机构和员工列表", notes = "获取组织机构和员工列表")
    @PostMapping(value = "/getOrgEmp")
    public PlatformResult<OrgEmpOutBO> getOrgEmp(@RequestBody OrgEmpInBO orgEmpInBO) {
        return PlatformResult.success(organizationService.getOrgEmp(orgEmpInBO));
    }

    @ApiOperation(value = "获取所有组织机构", notes = "获取所有组织机构")
    @PostMapping(value = "/getAllOrgList")
    public DataSet<Organization> getAllOrgList(Page page, Organization organizatio) {
        List<Organization> list = organizationService.getAllOrgList(page, organizatio);
        return new DataSet<Organization>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    @ApiOperation(value = "获取当前员工所属医院", notes = "获取当前员工所属医院")
    @PostMapping(value = "/getEmpBelongingHospital")
    public PlatformResult<Organization> getEmpBelongingHospital() {
        return PlatformResult.success(organizationService.getEmpBelongingHospital());
    }

    @ApiOperation(value = "获取所有医院", notes = "获取所有医院")
    @PostMapping(value = "/getAllHospital")
    public PlatformResult<List<Organization>> getAllHospital(OrganizationListResp record) {
        return PlatformResult.success(organizationService.getAllHospital(record));
    }
    
    @ApiOperation(value = "同步平台科室信息", notes = "同步平台科室信息")
    @PostMapping(value = "/syncOrganizationByPlatform")
    public PlatformResult<String> syncEmployeeByPlatform(@RequestBody PlatformBody platformBody) {
    	
    	 try {
    	    	
    		    UserLoginService.loginContext("admin");
    		    
    		    Map<String, Object> bodyData = platformBody.getBody();
    		    
//    		    Map<String, Object> bodyData = (Map<String, Object>) body.get("body");
    		    log.info("集成平台获取到科室的platformBody：" + bodyData);
    		    
    	    	Organization organization = organizationService.selectOneById((String) bodyData.get("deptId"));
    	    	//先判断获取数据字典，平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构，默认安单机构处理
    		    String platformOrgType = "1";
    		    List<DictItem> itemList = dictItemService.getDictItemByTypeCode("PLATFORM_ORG_TYPE", "*PUBLIC*");
    		    if(CollUtil.isNotEmpty(itemList)){
    		    	for(DictItem item : itemList){
    		    		if(item.getItemCode().equals("ORG_TYPE")){
    		    			platformOrgType = item.getItemNameValue();
    		    		}
    		    	}
    		    }
    	    	 
    		    String corpcode = (String) bodyData.get("orgCode");//机构编码
    		    String orgName = (String) bodyData.get("orgName");//机构名称
    		    String hospCode = (String) bodyData.get("hospCode");//院区编码
    		    String hospName = (String) bodyData.get("hospName");//院区名称
    		    String parentId = (String) bodyData.get("parentCode");//上级科室ID
    		    String deptId = (String) bodyData.get("deptId");//科室ID
    		    String deptCode = (String) bodyData.get("deptCode");//科室编码
    		    String deptName = (String) bodyData.get("name");//科室名称
    		    
    		    Organization selectRecord = new Organization();
    		    selectRecord.setAllData(true);
    		    Page page = new Page();
    		    page.setPageSize(Integer.MAX_VALUE);
    		    List<Organization> deptList = organizationService.getAllOrgList(page, selectRecord);
    		    Map<String, Organization> deptMap = new HashMap<>();//key 为 机构编码+科室编码
    		    if (CollUtil.isNotEmpty(deptList)) {
    		    	for(Organization dept : deptList){
    		    		deptMap.put(dept.getSsoOrgCode() + dept.getCode(), dept);
    		    	}
    		    }
    	    	if(null != organization) {
    	    		OrganizationSaveReq organizationSaveReq = new OrganizationSaveReq();
    	    		if(platformOrgType.equals("2")){ //紧密型(益阳三)，取院区编码不为空的当做机构编码，院区编码为空的数据，其他机构都需要复制一份相同的科室编码，科室名称数据
    	    			if(!ObjectUtils.isEmpty(hospCode) && !ObjectUtils.isEmpty(hospCode.trim())){//实际科室-同步到OA
    	    				organizationSaveReq.setSsoOrgCode(hospCode);
    	    				organizationSaveReq.setSsoOrgCode(hospName);
    	    				
    	    				//根据ID查询对应的科室编码，再根据当前机构和科室编码查询上级科室ID
    	    	    		if(!ObjectUtils.isEmpty(parentId)){
    	    	    			Organization parent = organizationService.selectOneById(parentId);
    	    	    			if(null != parent){
    	    	    				if(deptMap.containsKey(organizationSaveReq.getSsoOrgCode() + parent.getCode())){
    	    	    					organizationSaveReq.setParentId(deptMap.get(organizationSaveReq.getSsoOrgCode() + parent.getCode()).getOrganizationId());
    	    	    				}
    	    	    			}
    	    	    		}
    	    			} else {//虚拟科室-同步到OA，需要将每一个虚拟科室在其他科室同步复制一份
    	    				organizationSaveReq.setSsoOrgCode(corpcode);
        	    			organizationSaveReq.setSsoOrgName(orgName);
        	    			//根据ID查询对应的科室编码，再根据当前机构和科室编码查询上级科室ID
        	    			Organization parent = null;
    	    	    		if(!ObjectUtils.isEmpty(parentId)){
    	    	    			parent = organizationService.selectOneById(parentId);
    	    	    		}
        	    			//获取当前机构的子机构
        	    			List<HrmsOrg> orgList = hrmsOrgService.selectChildren(corpcode);
        	    			if(CollUtil.isNotEmpty(orgList) && orgList.size() > 1){
        	    				organizationSaveReq.setOrganizationId(deptId);
        	    	    		organizationSaveReq.setCode(deptCode);
        	    	    		organizationSaveReq.setName(deptName);
        	    	    		organizationSaveReq.setOrgFlag("2");
        	    				for(HrmsOrg org : orgList){
        	    					if(!corpcode.equals(org.getOrgCode())){
        	    						if(deptMap.containsKey(org.getOrgCode() + deptCode)){
        	    							OrganizationSaveReq req = new OrganizationSaveReq();
        	    				            BeanUtils.copyProperties(organizationSaveReq, req);
        	    							if(null != parent){
        	    	    	    				if(deptMap.containsKey(org.getOrgCode() + parent.getCode())){
        	    	    	    					req.setParentId(deptMap.get(org.getOrgCode() + parent.getCode()).getOrganizationId());
        	    	    	    				}
        	    	    	    			}
        	    							edit(req);
        	    						}
        	    					}
        	    				}
        	    				//返回
        	    				return PlatformResult.success();
        	    			}
    	    			}
    	    		} else {
    	    			organizationSaveReq.setParentId((String) bodyData.get("parentCode"));
    	    			organizationSaveReq.setSsoOrgCode(corpcode);
    	    			organizationSaveReq.setSsoOrgName(orgName);
    	    		}
    	    		organizationSaveReq.setOrganizationId(deptId);
    	    		organizationSaveReq.setCode(deptCode);
    	    		organizationSaveReq.setName(deptName);
    	    		organizationSaveReq.setOrgFlag("2");
    				
    				edit(organizationSaveReq);
    	    	}else {
    	    		OrganizationSaveReq organizationSaveReq = new OrganizationSaveReq();
    	    		if(platformOrgType.equals("2")){ //紧密型(益阳三)，取院区编码不为空的当做机构编码，院区编码为空的数据，其他机构都需要复制一份相同的科室编码，科室名称数据
    	    			if(!ObjectUtils.isEmpty(hospCode) && !ObjectUtils.isEmpty(hospCode.trim())){//实际科室-同步到OA
    	    				organizationSaveReq.setSsoOrgCode(hospCode);
    	    				organizationSaveReq.setSsoOrgCode(hospName);
    	    				
    	    				//根据ID查询对应的科室编码，再根据当前机构和科室编码查询上级科室ID
    	    	    		if(!ObjectUtils.isEmpty(parentId)){
    	    	    			Organization parent = organizationService.selectOneById(parentId);
    	    	    			if(null != parent){
    	    	    				if(deptMap.containsKey(organizationSaveReq.getSsoOrgCode() + parent.getCode())){
    	    	    					organizationSaveReq.setParentId(deptMap.get(organizationSaveReq.getSsoOrgCode() + parent.getCode()).getOrganizationId());
    	    	    				}
    	    	    			}
    	    	    		}
    	    			} else {//虚拟科室-同步到OA，需要将每一个虚拟科室在其他科室同步复制一份
    	    				organizationSaveReq.setSsoOrgCode(corpcode);
        	    			organizationSaveReq.setSsoOrgName(orgName);
        	    			//根据ID查询对应的科室编码，再根据当前机构和科室编码查询上级科室ID
        	    			Organization parent = null;
    	    	    		if(!ObjectUtils.isEmpty(parentId)){
    	    	    			parent = organizationService.selectOneById(parentId);
    	    	    		}
        	    			//获取当前机构的子机构
        	    			List<HrmsOrg> orgList = hrmsOrgService.selectChildren(corpcode);
        	    			if(CollUtil.isNotEmpty(orgList) && orgList.size() > 1){
        	    				organizationSaveReq.setOrganizationId(deptId);
        	    	    		organizationSaveReq.setCode(deptCode);
        	    	    		organizationSaveReq.setName(deptName);
        	    	    		organizationSaveReq.setOrgFlag("2");
        	    				for(HrmsOrg org : orgList){
        	    					if(!corpcode.equals(org.getOrgCode())){
        	    						if(deptMap.containsKey(org.getOrgCode() + deptCode)){
        	    							OrganizationSaveReq req = new OrganizationSaveReq();
        	    				            BeanUtils.copyProperties(organizationSaveReq, req);
        	    							if(null != parent){
        	    	    	    				if(deptMap.containsKey(org.getOrgCode() + parent.getCode())){
        	    	    	    					req.setParentId(deptMap.get(org.getOrgCode() + parent.getCode()).getOrganizationId());
        	    	    	    				}
        	    	    	    			}
        	    							add(req);
        	    						}
        	    					}
        	    				}
        	    				//返回
        	    				return PlatformResult.success();
        	    			}
    	    			}
    	    		} else {
    	    			organizationSaveReq.setParentId((String) bodyData.get("parentCode"));
    	    			organizationSaveReq.setSsoOrgCode(corpcode);
    	    			organizationSaveReq.setSsoOrgName(orgName);
    	    		}
    	    		organizationSaveReq.setOrganizationId(deptId);
    	    		organizationSaveReq.setCode(deptCode);
    	    		organizationSaveReq.setName(deptName);
    	    		organizationSaveReq.setParentId(parentId);
    	    		organizationSaveReq.setOrgFlag("2");
    	    		add(organizationSaveReq);
    	    	} 
    	    	
    	    	return PlatformResult.success();
    	 } catch (Exception e) {
             e.printStackTrace();
             return PlatformResult.failure("同步科室数据失败,失败原因：" + e.getMessage());
         }
    }
    
    @ApiOperation(value = "科室领导批量替换", notes = "科室领导批量替换")
    @PostMapping(value = "/bacthReplaceLeader")
    public PlatformResult<String> bacthReplaceLeader(@RequestBody ReplaceLeader replaceLeader) {
    	 try {
    		    organizationService.bacthReplaceLeader(replaceLeader);
    	    	return PlatformResult.success();
    	 } catch (Exception e) {
             e.printStackTrace();
             return PlatformResult.failure("批量替换失败,失败原因：" + e.getMessage());
         }
    }
}