package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class OrganizationMergeReq {

    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "需要合并组织机构列表")
    private List<String> mergeOrganizationId;


    /**
     * 组织机构名称
     */
    @ApiModelProperty(value = "组织机构名称")
    private String name;


    /**
     * 父类ID
     */
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 组织机构类型
     */
    @ApiModelProperty(value = "组织机构类型")
    private String orgFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "部门领导")
    private List<OrganizationLeaderSaveReq> leaders;

}