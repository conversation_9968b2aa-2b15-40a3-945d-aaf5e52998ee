package cn.trasen.homs.base.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.trasen.homs.base.bean.OrganizationAllocationSaveNumReq;
import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.bean.OrganizationReq;
import cn.trasen.homs.base.mapper.OrganizationAllocationMapper;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.base.model.OrganizationAllocation;
import cn.trasen.homs.base.model.OrganizationBed;
import cn.trasen.homs.base.model.OrganizationLeader;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.service.IOrganizationAllocationService;
import cn.trasen.homs.base.service.IOrganizationBedService;
import cn.trasen.homs.base.service.IOrganizationService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/8/4 10:14
 * @description
 */

@Service
public class OrganizationAllocationService implements IOrganizationAllocationService {

    @Autowired
    IDictItemService dictItemService;

    @Autowired
    OrganizationAllocationMapper organizationAllocationMapper;

    @Autowired
    IOrganizationService organizationService;

    @Autowired
    IOrganizationBedService organizationBedService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * @description: 保存编制
     * @param: organizationAllocationSaveNumReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/4 13:34
     */
    public void saveNum(OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq) {
        Example example = new Example(OrganizationAllocation.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", organizationAllocationSaveNumReq.getOrgId());
        organizationAllocationMapper.deleteByExample(example);
        for (OrganizationAllocationSaveNumReq.PersonalIdentityNum p : organizationAllocationSaveNumReq.getPersonalIdentityNumList()) {
            OrganizationAllocation organizationAllocation = BeanUtils.InitBean(OrganizationAllocation.class);
            organizationAllocation.setOrgId(organizationAllocationSaveNumReq.getOrgId());
            organizationAllocation.setPersonalIdentityCode(p.getPersonalIdentityCode().replace("personalIdentity_", ""));
            organizationAllocation.setNum(p.getNum());
            organizationAllocation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            organizationAllocationMapper.insertSelective(organizationAllocation);
        }
        organizationBedService.saveNum(organizationAllocationSaveNumReq);
    }


    @Override
    /** 
    * @description: 导入数据
* @param: importData
    * @return: void
    * @author: liyuan
    * @createTime: 2021/8/4 16:52
    */
    @Transactional(rollbackFor = Exception.class)
    public PlatformResult excelImportNum(List<Map<String,Object>> importData, String type) {

        List<String> errorList = new ArrayList<>();

        int i = 0;
        for (Map<String, Object> map : importData) {
            i++;
            if (map.getOrDefault("组织机构", "").equals("")) {
                errorList.add("第" + i + "行“组织机构”不能为空");
                continue;
            }
            OrganizationReq organizationReq = new OrganizationReq();
            organizationReq.setEqName(map.get("组织机构").toString());
            Organization organization = organizationService.getBase(organizationReq);
            if (organization == null) {
                errorList.add("第" + i + "行“组织机构”不存在");
                continue;
            }

//            if (type.equals("2") == false) {
//                Example example = new Example(OrganizationAllocation.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("orgId", organization.getOrganizationId());
//                if (organizationAllocationMapper.selectOneByExample(example) != null) {
//                    errorList.add("第" + i + "行“组织机构”已存在");
//                    continue;
//                }
//            }
            map.put("orgId", organization.getOrganizationId());
        }
        if (errorList.size() < 1) {

            if (type.equals("2")) {
                Example example = new Example(OrganizationAllocation.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andIsNotNull("orgId");
                organizationAllocationMapper.deleteByExample(example);
                organizationBedService.delAll();
            }
            Map<String, String> dictItemList = dictItemService.convertDictMap("personal_identity");

            for (Map<String, Object> map : importData) {
                OrganizationAllocationSaveNumReq organizationAllocationSaveNumReq = new OrganizationAllocationSaveNumReq();
                organizationAllocationSaveNumReq.setBedNum(Integer.parseInt(map.getOrDefault("床位", "0").toString()));
                organizationAllocationSaveNumReq.setOrgId(map.get("orgId").toString());
                List<OrganizationAllocationSaveNumReq.PersonalIdentityNum> personalIdentityNumList = new ArrayList<>();

                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    if (dictItemList.containsValue(entry.getKey())) {
                        OrganizationAllocationSaveNumReq.PersonalIdentityNum personalIdentityNum = new OrganizationAllocationSaveNumReq.PersonalIdentityNum();
                        try {
                            personalIdentityNum.setNum(Integer.parseInt(entry.getValue().toString()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        for (Map.Entry<String, String> d : dictItemList.entrySet()) {
                            if(d.getValue().equals(entry.getKey())) {
                                personalIdentityNum.setPersonalIdentityCode(d.getKey());
                                break;
                            }
                        }
                        personalIdentityNumList.add(personalIdentityNum);
                    }
                }
                organizationAllocationSaveNumReq.setPersonalIdentityNumList(personalIdentityNumList);

                saveNum(organizationAllocationSaveNumReq);
            }
            return PlatformResult.success("", "导入数量：" + importData.size());
        } else {
            String error="";
            for (String s:errorList)
            {
                error=error+s+"</br>";
            }
            return PlatformResult.failure(error);

        }
    }


    @Override
    public List<OrganizationAllocation> getBaseList() {
        Example example = new Example(OrganizationAllocation.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        return organizationAllocationMapper.selectByExample(example);
    }

    @Override
/**
 * @description: 获取列表
 * @param: organizationListReq
 * @return: java.util.Map<java.lang.String, java.lang.Object>
 * @author: liyuan
 * @createTime: 2021/8/4 13:27
 */
    public Map<String, Object> getList(OrganizationListReq organizationListReq) {
        List<DictItem> dictItemList = dictItemService.getDictItemByTypeCode("personal_identity");
        List<Organization> organizations = organizationService.getBaseList(organizationListReq);

        Example example = new Example(OrganizationLeader.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());

        List<OrganizationAllocation> organizationAllocationList = organizationAllocationMapper.selectByExample(example);


        List<OrganizationBed> organizationBedList = organizationBedService.getList();


        Map<String, Object> data = new HashMap<>();
        Map<String, Object> headMap = Maps.newLinkedHashMap();
        headMap.put("OrgId", "组织机构");
        headMap.put("OrgName", "组织机构");
        headMap.put("bedNum", "床位");
        for (DictItem dictItem : dictItemList) {
            headMap.put("personalIdentity_" + dictItem.getItemCode(),
                    dictItem.getItemName());
        }
        headMap.put("total", "合计");
        data.put("head", headMap);

        List<Map<String, Object>> datalist = new ArrayList<>();


        Map<String, Object> organizationAllocationMap;

        for (Organization organization : organizations) {
            organizationAllocationMap = Maps.newLinkedHashMap();
            organizationAllocationMap.put("OrgId", organization.getOrganizationId());
            organizationAllocationMap.put("OrgName", organization.getName());
            organizationAllocationMap.put("bedNum", getBedNum(organization.getOrganizationId(), organizationBedList));
            for (DictItem dictItem : dictItemList) {
                organizationAllocationMap.put("personalIdentity_" + dictItem.getItemCode(),
                        getPersonalIdentityNum(
                                organization.getOrganizationId(),
                                dictItem.getItemCode(),
                                organizationAllocationList
                        ));
            }
            organizationAllocationMap.put("total", total(organizationAllocationMap));
            datalist.add(organizationAllocationMap);
        }

        data.put("body", datalist);


        return data;
    }


    private Integer getBedNum(String orgId, List<OrganizationBed> organizationBedList) {
        for (OrganizationBed o : organizationBedList) {
            if (orgId.equals(o.getOrgId())) {
                return o.getNum();
            }
        }
        return 0;
    }

    private Integer getPersonalIdentityNum(String orgId, String personalIdentityCode, List<OrganizationAllocation> organizationAllocationList) {
        for (OrganizationAllocation o : organizationAllocationList) {
            if (orgId.equals(o.getOrgId()) && personalIdentityCode.equals(o.getPersonalIdentityCode())) {
                return o.getNum();
            }
        }
        return 0;
    }

    private Integer total(Map<String, Object> bodyMap) {
        Integer num = 0;
        for (Map.Entry<String, Object> entry : bodyMap.entrySet()) {
            if (entry.getKey().indexOf("personalIdentity_") >= 0) {
                num = num + Integer.parseInt(entry.getValue().toString());
            }
        }
        num = num + Integer.parseInt(bodyMap.get("bedNum").toString());
        return num;
    }
}