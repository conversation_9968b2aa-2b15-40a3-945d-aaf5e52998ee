package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
//import org.jeecgframework.poi.excel.annotation.Excel;

//import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/27 10:24
 * @description
 */

@Data
public class EmployeeListReq {
	
	@ApiModelProperty(value = "员工id")
    private String employeeId;

    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;
    
    @ApiModelProperty(value = "机构ID")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;
    
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;
    
    @ApiModelProperty(value = "启用禁用 0禁用 1启用")
    private String isEnable;
    
    @ApiModelProperty(value = "简拼")
    private String nameSpell;
    
    @ApiModelProperty(value = "入职日期")
    private String entryDate;
    
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;
    
    @ApiModelProperty(value = "身份证")
    private String identityNumber;
    
    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "性别")
    private String gender;
    
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "短号")
    private String businessPhone;
    
    private String isDeleted;

    @ApiModelProperty(value = "办公电话")
    private String landlineNumber;
    
    private  List<String> noEmployeeStatusList;

    private  List<String> employeeStatusList;
    
    @ApiModelProperty(value = "同级机构ID")
    private String eqOrgId;
    
    @ApiModelProperty(value = "机构")
    private List<String> orgIdList;
    
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;
    
    private String personalIdentity;

    @ApiModelProperty(value = "模糊机构名称")
    private String likeOrgName;

    private String searchKey;
    
    private String ssoOrgCode;
    
    private String ssoOrgName;
    
    private String cblx;
}
