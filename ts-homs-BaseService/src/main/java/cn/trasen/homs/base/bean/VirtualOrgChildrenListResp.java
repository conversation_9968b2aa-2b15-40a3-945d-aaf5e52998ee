package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/5/30 17:42
 * @description
 */
@Data
public class VirtualOrgChildrenListResp {

    @ApiModelProperty(value = "id")
    String id;

    @ApiModelProperty(value = "名称")
    String name;

    @ApiModelProperty(value = "父ID")
    String parentId;

    @ApiModelProperty(value = "编码")
    String code;

    @ApiModelProperty(value = "子节点")
    List<VirtualOrgChildrenListResp> children = null;

}