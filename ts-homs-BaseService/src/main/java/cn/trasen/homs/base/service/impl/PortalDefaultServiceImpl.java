package cn.trasen.homs.base.service.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.dao.PortalDefaultMapper;
import cn.trasen.homs.base.model.PortalDefault;
import cn.trasen.homs.base.service.PortalDefaultService;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class PortalDefaultServiceImpl implements PortalDefaultService{

    @Resource
    private PortalDefaultMapper portalDefaultMapper;

    @Override
    @Transactional(readOnly = false)
    public void setDefaultTheme(String themeId) {
        PortalDefault defaultTheme = new PortalDefault();
        defaultTheme.setUserCode(UserInfoHolder.getCurrentUserCode());
        defaultTheme.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        portalDefaultMapper.delete(defaultTheme);
        defaultTheme.setId(String.valueOf(IdWork.id.nextId()));
        defaultTheme.setThemeId(themeId);
        portalDefaultMapper.insertSelective(defaultTheme);
    }
    
}
