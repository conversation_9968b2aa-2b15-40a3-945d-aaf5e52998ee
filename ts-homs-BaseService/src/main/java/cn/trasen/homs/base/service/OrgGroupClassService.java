package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.model.OrgGroupClass;
import cn.trasen.homs.core.feature.orm.mybatis.Page;

/**
 * @Description: 自定义群组类型Service层
 * @Date: 2020/1/13 18:33
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Company: 湖南创星
 */
public interface OrgGroupClassService {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 查询自定义群组类型列表
     * @Date: 2020/1/11 17:01
     * @Param:
     * @return: java.util.List<cn.trasen.hrm.model.EmployeeTransfer>
     **/
    List<OrgGroupClass> getDataList(Page page, OrgGroupClass orgGroupClass);
    
    /**
     * 
     * @MethodName: getList
     * @Description: TODO
     * <AUTHOR>
     * @param page
     * @param orgGroupClass
     * @return List<OrgGroupClass>
     * @date 2022-10-16 05:02:22
     */
    List<OrgGroupClass> getList(OrgGroupClass orgGroupClass);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 新增自定义群组类型
     * @Date: 2020/1/13 8:42
     * @Param:
     * @return: int
     **/
    int insert(OrgGroupClass orgGroupClass);

    /**
     * @Author: Lizhihuo
     * @Description: 修改自定义群组类型
     * @Date: 2020/1/13 9:24
     * @Param:
     * @return: int
     **/
    int update(OrgGroupClass orgGroupClass);

    /**
     * @Author: Lizhihuo
     * @Description: 删除自定义群组类型
     * @Date: 2020/1/13 10:25
     * @Param:
     * @return: int
     **/
    int deleted(String id);

    /**
     * 
     * @MethodName: updateSort
     * @Description: TODO
     * <AUTHOR>
     * @param record void
     * @date 2023-07-25 02:54:19
     */
	void updateSort(List<OrgGroupClass> record);

}
