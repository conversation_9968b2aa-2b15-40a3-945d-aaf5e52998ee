package cn.trasen.homs.base.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "comm_group_jurisdiction")
@Setter
@Getter
public class CommGroupJurisdiction {
    @Id
    private String id;

    /**
     * 字段分组id
     */
    @Column(name = "field_group_id")
    @ApiModelProperty(value = "字段分组id")
    private String fieldGroupId;

    /**
     * 权限人员id
     */
    @Column(name = "emp_ids")
    @ApiModelProperty(value = "权限人员id")
    private String empIds;

    /**
     * 权限人员名称
     */
    @Column(name = "emp_names")
    @ApiModelProperty(value = "权限人员名称")
    private String empNames;


    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;
    
    
    @ApiModelProperty(value = "权限字段列表")
    @Transient
    private List<CommGroupJurisdictionField>  fields;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    @Column(name = "sso_org_name")
    private String ssoOrgName;
}