package cn.trasen.homs.base.controller;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;

import cn.trasen.homs.base.webSocket.JettyWebsocket;
import cn.trasen.homs.bean.base.MessageWebPushReq;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @createTime 2021/8/27 10:56
 * @description
 */

@Api(tags = "消息")
@RequestMapping("/message")
@RestController
@Slf4j
public class MessageController {

//    @Autowired
//    MessageWebSocket messageWebSocket;
    
    @Autowired
    JettyWebsocket jettyWebsocket;

    @ApiOperation(value = "web推送", notes = "web推送")
    @PostMapping(value = "/webPush")
    public PlatformResult webPush(@RequestBody MessageWebPushReq messageWebPushReq) throws IOException {
        if (!messageWebPushReq.getReceiver().equals("all")) {
        	log.info(" !all webPush消息推送：{}", JSONArray.toJSON(messageWebPushReq.getWebMessageTemplate()).toString());
        	log.info(" !all webPush消息推送人员：{}", messageWebPushReq.getReceiver());
        	
            for (String s : messageWebPushReq.getReceiver().split(",")) {
            	jettyWebsocket.sendtoUser(JSONArray.toJSON(messageWebPushReq.getWebMessageTemplate()).toString(), s);
            }
        } else {
        	log.info(" webPush消息推送：{}", JSONArray.toJSON(messageWebPushReq.getWebMessageTemplate()).toString());
        	jettyWebsocket.sendtoUser(JSONArray.toJSON(messageWebPushReq.getWebMessageTemplate()).toString());
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "web推送", notes = "web推送")
    @GetMapping(value = "/getWebSocket")
    public PlatformResult getWebSocket() {
//        ConcurrentHashMap<String, ConcurrentHashMap<String, Session>> concurrentHashMap = messageWebSocket.getMessageWebSocket();
//        for (Map.Entry<String, ConcurrentHashMap<String, Session>> v : concurrentHashMap.entrySet()) {
//            for (Map.Entry<String, Session> d : v.getValue().entrySet()) {
//                System.out.println("-----------------"+d.getKey() + "-" + v.getKey());
//            }
//        }

        return PlatformResult.success();
    }
}