package cn.trasen.homs.base.customEmployee.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 档案分组查看权限-岗位
 *
 */
@Table(name = "cust_emp_auth_identity")
@Setter
@Getter
public class CustomEmployeeAuthorityIdentity {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "分组id")
    private String groupId;

    /**
     * 岗位编码
     */
    @Column(name = "personal_identity")
    @ApiModelProperty(value = "岗位编码")
    private String personalIdentity;

    /**
     * 岗位名称
     */
    @Column(name = "personal_identity_name")
    @ApiModelProperty(value = "岗位名称")
    private String personalIdentityName;

    /**
     * 权限类型 1查看  2编辑
     */
    @Column(name = "authority_type")
    @ApiModelProperty(value = "权限类型 1查看  2编辑")
    private Integer authorityType;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}