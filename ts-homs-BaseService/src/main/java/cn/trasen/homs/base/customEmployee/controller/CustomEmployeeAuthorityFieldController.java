package cn.trasen.homs.base.customEmployee.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.customEmployee.model.CustomEmployeeAuthorityField;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeAuthorityFieldService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CustomEmployeeAuthorityFieldController
 * @Description TODO
 * @date 2024��9��10�� ����6:52:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CustomEmployee分组权限字段表")
public class CustomEmployeeAuthorityFieldController {

	private transient static final Logger logger = LoggerFactory.getLogger(CustomEmployeeAuthorityFieldController.class);

	@Autowired
	private CustomEmployeeAuthorityFieldService customEmployeeAuthorityFieldService;

	/**
	 * @Title saveCustomEmployeeAuthorityField
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��9��10�� ����6:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/customEmployeeAuthorityField/save")
	public PlatformResult<String> saveCustomEmployeeAuthorityField(@RequestBody CustomEmployeeAuthorityField record) {
		try {
			customEmployeeAuthorityFieldService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCustomEmployeeAuthorityField
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��9��10�� ����6:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/customEmployeeAuthorityField/update")
	public PlatformResult<String> updateCustomEmployeeAuthorityField(@RequestBody CustomEmployeeAuthorityField record) {
		try {
			customEmployeeAuthorityFieldService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCustomEmployeeAuthorityFieldById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CustomEmployeeAuthorityField>
	 * @date 2024��9��10�� ����6:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/customEmployeeAuthorityField/{id}")
	public PlatformResult<CustomEmployeeAuthorityField> selectCustomEmployeeAuthorityFieldById(@PathVariable String id) {
		try {
			CustomEmployeeAuthorityField record = customEmployeeAuthorityFieldService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCustomEmployeeAuthorityFieldById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��9��10�� ����6:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/customEmployeeAuthorityField/delete/{id}")
	public PlatformResult<String> deleteCustomEmployeeAuthorityFieldById(@PathVariable String id) {
		try {
			customEmployeeAuthorityFieldService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCustomEmployeeAuthorityFieldList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CustomEmployeeAuthorityField>
	 * @date 2024��9��10�� ����6:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/customEmployeeAuthorityField/list")
	public DataSet<CustomEmployeeAuthorityField> selectCustomEmployeeAuthorityFieldList(Page page, CustomEmployeeAuthorityField record) {
		return customEmployeeAuthorityFieldService.getDataSetList(page, record);
	}
	
}
