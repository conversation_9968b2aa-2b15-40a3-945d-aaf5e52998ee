<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.customEmployee.dao.CustEmpStorageMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.base.customEmployee.model.CustEmpStorage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="storage_date" jdbcType="VARCHAR" property="storageDate" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
</mapper>