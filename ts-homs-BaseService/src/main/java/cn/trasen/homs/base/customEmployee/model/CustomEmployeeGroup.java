package cn.trasen.homs.base.customEmployee.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 档案配置分组
 *
 */
@Table(name = "cust_emp_group")
@Setter
@Getter
@Accessors(chain = true)
public class CustomEmployeeGroup {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 分组名称
     */
    @Column(name = "group_name")
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    /**
     * 表名
     */
    @Column(name = "table_name")
    @ApiModelProperty(value = "表名")
    private String tableName;

    /**
     * 是否隐藏分组(0 否 1 是)
     */
    @Column(name = "is_hide")
    @ApiModelProperty(value = "是否隐藏分组(0 否 1 是)")
    private String isHide;

    /**
     * 是否开启明细添加(0 否 1 是)
     */
    @Column(name = "is_detailed")
    @ApiModelProperty(value = "是否开启明细添加(0 否 1 是)")
    private String isDetailed;

    /**
     * 是否开启院内履职 (0 不显示 1 显示)
     */
    @Column(name = "show_pass_by")
    @ApiModelProperty(value = "是否开启院内履职 (0 不显示 1 显示)")
    private String showPassBy;

    /**
     * 是否展开分组数据(0 否 1 是)
     */
    @Column(name = "show_open_by")
    @ApiModelProperty(value = "是否展开分组数据(0 否 1 是)")
    private String showOpenBy;

    /**
     * 是否隐藏分组删除-必须保留一条数据 (0 否 1是)
     */
    @Column(name = "show_delete")
    @ApiModelProperty(value = "是否隐藏分组删除-必须保留一条数据 (0 否 1是)")
    private String showDelete;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer seq;

    /**
     * 排序字段
     */
    @Column(name = "sort_field")
    @ApiModelProperty(value = "排序字段")
    private String sortField;

    /**
     * 是否停用(0 否 1 是)
     */
    @Column(name = "is_disabled")
    @ApiModelProperty(value = "是否停用(0 否 1 是)")
    private String isDisabled;

    /**
     * 是否允许删除(0否 1 是)
     */
    @Column(name = "is_allow_deleted")
    @ApiModelProperty(value = "是否允许删除(0否 1 是)")
    private String isAllowDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Transient
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 档案类型
     */
    @Column(name = "archives_type")
    @ApiModelProperty(value = "档案类型")
    private String archivesType;

    /**
     * 是否同步查看(0 否 1 是)
     */
    @Column(name = "is_sync_view")
    @ApiModelProperty(value = "是否同步查看(0 否 1 是)")
    private String isSyncView;

    /**
     * 是否同步编辑(0否 1 是)
     */
    @Column(name = "is_sync_edit")
    @ApiModelProperty(value = "是否同步编辑(0否 1 是)")
    private String isSyncEdit;
    
    @Transient
    private List<CustomEmployeeField> fields;
    
    @Transient
    private List<CustomEmployeeAuthority> authList;
    
    @Transient
    private String userCode;  //当前用户code,用于查询
    
    @Transient
    private Integer isEdit; // 是否只读
    
    @Transient
    private String seePersonalIdentity; // 查看权限
    
    @Transient
    private String seePersonalIdentityName; // 查看权限名称
    
    @Transient
    private Integer authNumber; // 编辑权限
    
    @Transient
    private Integer setValue;
    
    @Transient
    private String showName;
    
    @Transient
    private String fieldName;
    
}