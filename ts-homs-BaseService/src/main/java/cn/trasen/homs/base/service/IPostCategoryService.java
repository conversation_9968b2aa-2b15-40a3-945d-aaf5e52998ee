package cn.trasen.homs.base.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.PostCategoryListResp;
import cn.trasen.homs.base.bean.PostCategorySaveReq;

/**
 * <AUTHOR>
 * @createTime 2021/8/6 15:40
 * @description
 */
public interface IPostCategoryService {
    /**
     * @description: 获取分页
     * @param: postListReq
     * @param: page
     * @return: cn.trasen.BootComm.model.DataSet
     * @author: liyuan
     * @createTime: 2021/8/6 12:42
     */
    List<PostCategoryListResp> getList();

    /**
     * @description: 新增
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void add(PostCategorySaveReq postCategorySaveReq);

    /**
     * @description: 修改
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void update(PostCategorySaveReq postCategorySaveReq);

    /**
     * @description: 删除类别
     * @param: postSaveReq
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/6 10:48
     */
    @Transactional(rollbackFor = Exception.class)
    void delete(String dictTypeId, String id);

    /**
     * @description: 修改
     * @param: id
     * @param: enable
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/29 17:43
     */
    @Transactional(rollbackFor = Exception.class)
    void enable(String dictTypeId, String id, String enable);

    void verify(String id);
}
