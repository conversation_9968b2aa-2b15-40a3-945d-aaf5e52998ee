package cn.trasen.homs.base.bean;

import cn.trasen.BootComm.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/2/8 15:58
 */
@Setter
@Getter
public class OrgGroupEmpExportExcel {



    /**
     * 群组名称
     */
    @ExcelField(title = "群组",index = 0)
    private String groupName;

    /**
     * 名称
     */
    @ExcelField(title = "姓名",index = 2)
    private String empName;

    /**
     * 工号
     */
    @ExcelField(title = "工号",index = 1)
    private String empCode;


    /**
     * 机构
     */
    @ExcelField(title = "组织机构",index = 4)
    private String orgName;


    @ExcelField(title = "手机号",index = 3)
    private String empMobile;


    /**
     * 岗位名称
     */
    @ExcelField(title = "岗位",index = 5)
    private String personalIdentityName;


    @ExcelField(title = "职务",index = 6)
    private String positionName;


}
