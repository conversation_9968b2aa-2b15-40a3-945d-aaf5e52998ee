/**
 * @Title: CustomEmployeeResp.java  
 * @Package: package cn.trasen.homs.baseservice.bean;  
 * @Date: 2021年6月30日
 * @Author: jyq
 * @Description: TODO
 */

package cn.trasen.homs.base.bean;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: CustomEmployeeResp
 * @Author: 86189
 * @Date: 2021年6月30日
 */
@Getter
@Setter
public class CustomEmployeeResp {

	@ApiModelProperty(value = "编辑操作id")
	private String operationId;

	@ApiModelProperty(value = "员工编号")
	private String employeeNo;

	@ApiModelProperty(value = "姓名")
	private String employeeName;

	@ApiModelProperty(value = "性别")
	private String gender;

	@ApiModelProperty(value = "所属部门")
	private String orgName;

	@ApiModelProperty(value = "身份证号")
	private String identityNumber;

	@ApiModelProperty(value = "出生日期")
	private String birthday;

	@ApiModelProperty(value = "入院日期")
	private String entryDate;

	@ApiModelProperty(value = "发起人")
	private String createUserName;

	@ApiModelProperty(value = "发起时间")
	private String createDate;

	@ApiModelProperty(value = "当前节点")
	private String currentStepName;

	@ApiModelProperty(value = "流程状态")
	private String wfStatus;

	@ApiModelProperty(value = "发起人部门")
	private String createUserDeptName;

	@ApiModelProperty(value = "审核状态 1 由我办理 2 我已办理 3 由我发起")
	private String handStatus;

	@ApiModelProperty(value = "员工Code,用于查询")
	private String userCode;

	@ApiModelProperty(value = "审核状态")
	private String auditStatus;

	@ApiModelProperty(value = "任务id")
	private String taskId;

	@ApiModelProperty(value = "流程实例id")
	private String wfInstanceId;

	@ApiModelProperty(value = "当前节点名称")
	private String wfStepName;

	@ApiModelProperty(value = "当前节点id")
	private String wfStepNo;

	@ApiModelProperty(value = "查询时组织机构集合")
	private List<String> orgIds;

	@ApiModelProperty(value = "查询时开始时间")
	private String startTime;

	@ApiModelProperty(value = "办理时间")
	private String handleTime;

	@ApiModelProperty(value = "查询条件")
	private String condition;

	@ApiModelProperty(value = "发起人")
	private String createUser;

	/**
	 * 抄送人codes
	 */
	private String copyToUsers;

	/**
	 * 抄送人名称
	 */
	private String copyToUserNames;

	private String remark; // 备注
	
    private String ssoOrgCode;
    
    private String ssoOrgName;
}
