package cn.trasen.homs.base.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.base.bo.IntroductionPublicBO;
import cn.trasen.homs.base.dto.BelongingSystemDTO;
import cn.trasen.homs.base.vo.BelongingSystemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.base.bean.DictItemListReq;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @Description: 字典项目Service层
 * @Date: 2020/4/29 10:46
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface IDictItemService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int insert(DictItem entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int update(DictItem entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    int deleted(DictItem entity);

    @Transactional(readOnly = false)
    void delete(String typeId, String itemCode);

    @Transactional(readOnly = false)
    void enable(String typeId, String itemCode, String enable);

    /**
     * @Author: Lizhihuo
     * @Description: 启用
     * @Date: 2020/5/16 10:52
     * @Param: 
     * @return: int
     **/
    int dictItemEnable(DictItem entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<version></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月29日  下午1:49:22 </p>
     */
    List<DictItem> getDataList(Page page, DictItem entity);

    /**
     * @Author: Lizhihuo
     * @Description: 通过ID查询字典项目
     * @Date: 2020/4/29 16:15
     * @Param: 
     * @return: cn.trasen.system.model.DictItem
     **/
    DictItem getDictItemById(DictItem entity);

    List<DictItem> getDictItemByTypeCodeAll(String typeCode);

    List<DictItem> getDictItemByTypeCode(DictItemListReq dictItemListReq);

    DataSet<DictItem> getDictItemByTypeCode(Page page, DictItemListReq dictItemListReq);

    /**
     * 根据dic_type_id查询公共字典值
     * @param dicTypeId
     * @return
     */
    List<DictItem> getPublicDictItemBydicTypeId(String dicTypeId);

    /**
     * 
     * @Title: getDictItemByTypeCode   
     * @Description: TODO(描述这个方法的作用)
     * @param: @param typeCode
     * @param: @return
     * @return: List<DictItem>
     * @author: YueC
     * @date:   2020年7月30日 下午2:07:58
     * @throws
     */
	List<DictItem> getDictItemByTypeCode(@Param("typeCode")String typeCode);

    void setDictItemByTypeCode(DictItem record);
    /**
     *
     * @Title: getDictItemByTypeCode
     * @Description: 根据多个字典编码查询
     * @param: @param typeCode
     * @param: @return
     * @return: PlatformResult<List < DictItem>>
     * @author: 龙涌
     * @date: 2025年8月15日
     * @throws
     */
    List<DictItem> getDictItemByTypeCodeList(@Param("typeCode") List<String> typeCode);


    /**
     *
     * @Title: getDictItemByTypeCode
     * @Description: 指定字典编号+加机构编号过滤
     * @param: @param typeCode
     * @param: @param ssoOrgCode
     * @param: @return
     * @return: List<DictItem>
     * @author: 龙涌
     * @date:   2025年8月5日
     * @throws
     */
    List<DictItem> getDictItemByTypeCode(@Param("typeCode")String typeCode, @Param("ssoOrgCode")String ssoOrgCode);

    /**
     * @Title: convertDictMap
     * @Description: 查询字典列表并转换成Map形式
     * @Param: dictType 数据字典类型
     * <AUTHOR>
     * @date 2020年3月26日 下午3:18:24
     */
    Map<String, String> convertDictMap(String dictType);
    
    /**
     * 
    * @Title: getDictMap  
    * @Description: 获取所有字典值
    * @Params: @return      
    * @Return: Map<String,List<Map<String,String>>>
    * <AUTHOR>
    * @date:2021年7月1日
    * @Throws
     */
    Map<String,Map<String,String>> getDictMap();

    /**
     * @param dictTypeId:
     * @param itemNameValue:
     * @return DictItem
     * <AUTHOR>
     * @description 根据字段类型ID + 字典项目字段值 查询字典
     * @date 2024/6/8 11:25
     */
    DictItem getDictItemByDictTypeIdAndItemNameValue(String dictTypeId,String itemNameValue,String ssoOrgCode);

    /**
     * @Description: 获取字典树
     * <AUTHOR>
     * @Date    2025/7/28
     **/
    List<Map<String,Object>> getDictItemTreeByCode(String typeCode);
}
