package cn.trasen.homs.base.webSocket;

import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.websocket.api.RemoteEndpoint;
import org.eclipse.jetty.websocket.api.Session;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketClose;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketConnect;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketError;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketMessage;
import org.eclipse.jetty.websocket.api.annotations.WebSocket;
import org.eclipse.jetty.websocket.common.WebSocketSession;

import com.google.common.base.Objects;

import lombok.extern.slf4j.Slf4j;

@WebSocket
@Slf4j
public class JettyWebsocket {

	private WebSocketSession session;
	private RemoteEndpoint remote;

	// 新的在线用户列表，支持同一帐户可以在不同的终端通讯
//	public static ConcurrentHashMap<WebSocketSession, String> userList = new ConcurrentHashMap<>();
	// 使用 ConcurrentHashMap 来存储用户会话，保证线程安全
	private static final Map<WebSocketSession, String> userList = new ConcurrentHashMap<>();

	private static final Set<WebSocketSession> sessionsToRemove = ConcurrentHashMap.newKeySet();

	@OnWebSocketClose
	public void onClose(Session session, int statusCode, String reason) {
		userList.remove(session);
		sessionsToRemove.remove(session);
		log.info("退出状态：{}, 原因：{}, 当前会话人数：{}", statusCode, reason, userList.size());
	}

	@OnWebSocketConnect
	public void onConnect(Session session) {
		log.info("开始连接：{}, 会话本地ip:{}, 当前会话人数：{}", session.getClass(), session.getLocalAddress(),userList.size());
		if (session instanceof WebSocketSession) {
			this.session = (WebSocketSession) session;
			this.session.setIdleTimeout(5 * 60 * 1000);
			this.remote = session.getRemote();
			getSessionInfo(this.session);
		} else {
			log.error("Session is not an instance of WebSocketSession");
		}
	}

	@OnWebSocketMessage
	public void onMessage(String msg) {
		log.info("收到信息：" + msg);
		Set<WebSocketSession> toRemove = new HashSet<>();
		for (Map.Entry<WebSocketSession, String> entry : userList.entrySet()) {
			WebSocketSession session = entry.getKey();
			if (!session.isOpen()) {
				toRemove.add(session);
			} else {
				try {
					session.getRemote().sendString(msg);
				} catch (IOException e) {
					e.printStackTrace();
					toRemove.add(session);
				}
			}
		}
		toRemove.forEach(userList::remove);
	}

	@OnWebSocketError
	public void onWebSocketError(Session session, Throwable cause) {
		log.info("超时WebSocket连接关闭！");
		if (session != null && session.isOpen()) {
			session.close();
		}
	}

	// 关闭 WebSocket 连接并清理
	public void closeConnection(Session session) {
		try {
			if (session != null && session.isOpen()) {
				session.close();
			}
		} finally {
			userList.remove(session); // 确保清理 session
			sessionsToRemove.remove(session);
		}
	}

	/**
	 * 从session会话中获取当前url的相关规则，然后再作相对应任务处理
	 * 
	 * @param session
	 */
	private void getSessionInfo(WebSocketSession session) {
		String requestUri = session.getRequestURI().getPath();
		String[] requestUriArr = requestUri.split("/");
		// /ts-basics-bottom/messagewebsocket/usercode
		if (requestUriArr.length == 4) {
			String userCode = requestUriArr[requestUriArr.length - 1];
			log.info("连接用户帐户：" + userCode);
			// 普通用户消息模式，将用户会话存入缓存中
			// 检查当前session是否存在，如果不存在则存入
			if (!userList.containsKey(session)) {
				if (!StringUtils.isEmpty(userCode)) {
					userList.put(session, userCode);
				}
			}
		}
	}

	/**
	 * 用户消息广播
	 * 
	 * @param message
	 * @throws IOException
	 */
	public void sendtoUser(String message) throws IOException {
		Set<WebSocketSession> toRemove = new HashSet<>();
		for (Map.Entry<WebSocketSession, String> entry : userList.entrySet()) {
			WebSocketSession session = entry.getKey();
			if (!session.isOpen()) {
//				userList.remove(session);
				toRemove.add(session);
			} else {
				try {
					session.getRemote().sendString(message);
				} catch (IOException e) {
					e.printStackTrace();
					toRemove.add(session);
				}
			}
		}
		// 批量移除不再活跃的会话
		toRemove.forEach(userList::remove);
	}

	public boolean sendtoUser(String message, String userCode) throws IOException {
		log.info("========sendtoUser--userCode:" + userCode);
		Set<WebSocketSession> sessions = findSessionsByValue(userList, userCode);

//		log.info("========sendtoUser--session:" + sessions);
		Set<WebSocketSession> toRemove = new HashSet<>();
		// 如果当前用户不在线，是否保存此消息下次再推送？
		for (WebSocketSession session : sessions) {
//			log.info("========session.isOpen():" + session.isOpen());
			log.info("========sendtoUser--userCode:" + userCode);
			if (!session.isOpen()) { // 无效websocketsession，直接进行移除
				toRemove.add(session);
				continue;
			} else {
				try {
					session.getRemote().sendString(message);
				} catch (IOException e) {
					e.printStackTrace();
					toRemove.add(session);
					return false;
				}
			}
		}
		toRemove.forEach(userList::remove);
		return true;
	}

	public void sendMessage(WebSocketSession session, String message) throws IOException {
//		synchronized (session) {
//			session.getRemote().sendString(message);
//		}
		try {
			session.getRemote().sendString(message);
		} catch (IOException e) {
			log.error("Error sending message to session: {}", e);
//            throw e;  // 重新抛出异常，调用者可以选择如何处理
		}
	}

	public Set<WebSocketSession> findSessionsByValue(Map<WebSocketSession, String> map, String value) {
		return map.entrySet().stream().filter(entry -> Objects.equal(value, entry.getValue())).map(Map.Entry::getKey)
				.collect(Collectors.toSet());
	}

//	public static synchronized int getOnlineCount() {
//		return userList.size();
//	}

}
