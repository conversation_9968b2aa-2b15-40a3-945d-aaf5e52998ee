package cn.trasen.homs.base.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.model.VersionAccessory;
import tk.mybatis.mapper.common.Mapper;

public interface VersionAccessoryMapper extends Mapper<VersionAccessory> {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 批量增加附件信息
     * @Date: 2020/4/28 9:33
     * @Param:
     * @return: int
     **/
    int batchInsert(List<VersionAccessory> versionAccessories);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 根据版本ID删除
     * @Date: 2020/4/28 9:49
     * @Param: 
     * @return: void
     **/
    void deleteByVersionId (@Param("versionId") String versionId);

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 初始化查询附件信息
     * @Date: 2020/4/28 9:55
     * @Param: 
     * @return: java.util.List
     **/
    List<VersionAccessory> selectVersionAccessoryByVersionId(@Param("versionId") String versionId);
}