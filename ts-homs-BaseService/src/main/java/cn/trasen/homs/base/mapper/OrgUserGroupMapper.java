package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.bo.OrgGroupEmpInBO;
import cn.trasen.homs.base.bo.OrgGroupEmpOutBO;
import cn.trasen.homs.base.model.OrgUserGroup;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface OrgUserGroupMapper extends Mapper<OrgUserGroup> {

    /**
     * @Author: <PERSON><PERSON><PERSON><PERSON>
     * @Description: 批量新增
     * @Date: 2020/5/9 13:54
     * @Param:
     * @return: int
     **/
    int batchInsert(List<OrgUserGroup> orgUserGroupList);

    /**
     * 获取用户列表
     *
     * @param req
     * @return java.util.List<cn.trasen.basicsbottom.bo.OrgGroupEmpOutBO>
     * <AUTHOR>
     * @date 2022/2/8 16:56
     */
    List<OrgGroupEmpOutBO> list(@Param("req") OrgGroupEmpInBO req);


    List<OrgGroupEmpOutBO> list(Page page, @Param("req") OrgGroupEmpInBO req);

    void updateUserGroupSort(OrgUserGroup orgUserGroup);

    List<Map<String, Object>> selectUserGroupList(Page page, @Param("groupId")String groupId,@Param("ssoOrgCode")String ssoOrgCode);

    void deleteByGroupId(@Param("groupId")String groupId);

}

