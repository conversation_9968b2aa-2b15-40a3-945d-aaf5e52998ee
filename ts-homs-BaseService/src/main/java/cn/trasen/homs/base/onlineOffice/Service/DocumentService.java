/************************************************
* @功能描述: TODO
* @Title: DocumentService.java
* @Package cn.trasen.homs.base.onlineOffice.Service
* <AUTHOR>
* @date 2025年6月16日 上午11:58:33
* @version V1.0
*************************************************/
package cn.trasen.homs.base.onlineOffice.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Date;
import java.util.List;
import java.util.Scanner;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.trasen.homs.base.mapper.FileAttachmentMapper;
import cn.trasen.homs.base.model.FileAttachment;
import cn.trasen.homs.base.onlineOffice.Config.OfficeConfig;
import cn.trasen.homs.base.onlineOffice.DTO.CreateDocumentDto;
import cn.trasen.homs.base.onlineOffice.DTO.OfficeCallbackDto;
import cn.trasen.homs.base.service.impl.FileAttachmentService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import lombok.extern.slf4j.Slf4j;

/**
* @ClassName: DocumentService
* @Description: TODO 
* <AUTHOR>
* @date 2025年6月16日 上午11:58:33
*
*/

@Service
@Slf4j
public class DocumentService {
    
    @Value("${appconfig.attachment.savePath}")
    private String uploadDir;
    
//    @Value("${office.docserver.url:*************}")
//    private String docServerUrl;
    
    @Autowired
    private FileAttachmentService fileAttachmentService;
    
    @Autowired
    FileAttachmentMapper fileAttachmentMapper;
       
    public FileAttachment createDocument(CreateDocumentDto dto) {
    	FileAttachment document = new FileAttachment();
    	document.setId(IdWork.getStrId());
        document.setOriginalName(dto.getName());
        document.setFileExtension(dto.getFileType());
        document.setCreateUser(dto.getUserId());
        document.setCreateDate(new Date());
        document.setUpdateDate(new Date());
        document.setUpdateUser(dto.getUserId());
        document.setFileSize(0L);
        document.setModuleName("onlineOffice");
        document.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        
        // 生成空文件
        String fileName = document.getId() + "." + dto.getFileType();
        String filePath = uploadDir + File.separator + fileName;
//        String tmpFilePath = uploadDir +File.separator +"/office_tmp/" + File.separator + fileName;
        document.setFilePath(filePath);
        document.setRealPath("/ts-basics-bottom/fileAttachment/downloadFile/"+document.getId());
        
        // 创建空文件
        try {
            Files.createFile(Paths.get(filePath));
        } catch (IOException e) {
            throw new RuntimeException("Failed to create document file", e);
        }
        fileAttachmentService.save(document);
        return document;
    }
    
    public FileAttachment getDocument(String id) {
    	return fileAttachmentService.getById(id,null);
    }
    
    public OfficeConfig getDocumentConfig(String id, String userId) {
    	FileAttachment document = fileAttachmentService.getById(id,null);
        
        OfficeConfig config = new OfficeConfig();
        config.setDocumentContext(document);
        config.setDocumentType(getDocumentType(document.getFileExtension()));
        
        OfficeConfig.EditorConfig editorConfig = new OfficeConfig.EditorConfig();
        editorConfig.setCallbackUrl("/api/documents/callback");
        editorConfig.setUser(new OfficeConfig.User(userId.toString(), "User " + userId));
        
        OfficeConfig.Document documentConfig = new OfficeConfig.Document();
        documentConfig.setTitle(document.getOriginalName());
        documentConfig.setUrl("/api/documents/" + id + "/content");
        documentConfig.setFileType(document.getFileExtension());
        documentConfig.setKey(document.getId().toString());
        
        config.setEditorConfig(editorConfig);
        config.setDocument(documentConfig);
        
        return config;
    }
    
    public void handleCallback(OfficeCallbackDto callbackDto) throws IOException {
    	if (callbackDto.getStatus() == 2 || callbackDto.getStatus() == 6) { // 文档已准备好保存
    		Path tempFile = null;
    	    try {
    	        // 获取文档信息
    	        FileAttachment document = fileAttachmentService.getById(callbackDto.getKey(),null);
    	        log.info("开始处理文档保存回调: {}", callbackDto.getUrl());
    	        
    	        // 下载新版本文件到临时目录
    	        String newVersionPath = downloadNewVersion(callbackDto.getUrl());
    	        tempFile = Paths.get(newVersionPath);
    	        
    	        // 复制文件到正式位置
    	        Path targetPath = Paths.get(document.getFilePath());
    	        Files.copy(tempFile, targetPath, StandardCopyOption.REPLACE_EXISTING);
    	        
    	        // 更新文档信息
    	        document.setFileSize(Files.size(targetPath));
    	        document.setUpdateDate(new Date());
    	        document.setUpdateUser(UserInfoHolder.getCurrentUserCode());
    	        fileAttachmentMapper.updateByPrimaryKey(document);
    	        
    	        // 保存版本记录
//    	        saveNewVersion(document, newVersionPath, callbackDto.getChangesurl());
    	        
    	        log.info("文档保存成功: {}", document.getFilePath());
    	    } catch (Exception e) {
    	        log.error("文档保存失败", e);
    	        throw new RuntimeException("文档保存失败", e);
    	    } finally {
    	        // 清理临时文件
    	        if (tempFile != null) {
    	            try {
    	                Files.deleteIfExists(tempFile);
    	                log.info("已清理临时文件: {}", tempFile);
    	            } catch (IOException e) {
    	                log.warn("临时文件清理失败: {}", tempFile, e);
    	            }
    	        }
    	    }
        }
    }

    
    /**
     * 从OnlyOffice服务器下载新版本文档
     * @param fileUrl OnlyOffice返回的文件URL
     * @return 下载到本地的文件路径
     */
    private String downloadNewVersion(String fileUrl) {
        // 1. 验证URL有效性
        if (StringUtils.isBlank(fileUrl)) {
            throw new IllegalArgumentException("File URL cannot be empty");
        }

        // 2. 创建临时目录
        String tempDir = uploadDir + File.separator + "office_temp";
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }

        // 3. 生成唯一文件名
        String fileName = "onlyoffice_" + System.currentTimeMillis() + ".tmp";
        String filePath = tempDir + File.separator + fileName;

        // 4. 配置HTTP客户端（带超时设置）
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionTimeToLive(30, TimeUnit.SECONDS)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setSocketTimeout(30000)
                        .setConnectTimeout(30000)
                        .build())
                .build();

        // 5. 执行下载
        try {
            HttpGet httpGet = new HttpGet(fileUrl);
            // 添加必要的请求头
//            httpGet.addHeader("Authorization", "Bearer " + onlyOfficeConfig.getApiToken());
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet);
                 FileOutputStream out = new FileOutputStream(filePath)) {
                
                // 验证响应状态
                if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                    throw new IOException("Failed to download file: " + response.getStatusLine());
                }
                
                // 流式下载文件
                HttpEntity entity = response.getEntity();
                try (InputStream in = entity.getContent()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
                
                // 验证文件完整性
                if (new File(filePath).length() == 0) {
                    throw new IOException("Downloaded file is empty");
                }
                
                return filePath;
            }
        } catch (Exception e) {
            // 清理可能已部分下载的文件
            new File(filePath).delete();
            throw new RuntimeException("Failed to download document from OnlyOffice: " + e.getMessage(), e);
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("Error closing HTTP client", e);
            }
        }
    }
    /**
     * 保存文档新版本
     * @param document 文档实体
     * @param newVersionPath 新版本文件路径
     * @param changesUrl 变更历史URL
     */
    private void saveNewVersion(FileAttachment document, String newVersionPath, String changesUrl) {
        // 1. 创建版本目录
        String versionDir = uploadDir + File.separator + "versions" + File.separator + document.getId();
        File versionDirFile = new File(versionDir);
        if (!versionDirFile.exists()) {
            versionDirFile.mkdirs();
        }

//        // 2. 生成版本文件名
//        String versionFileName = "v" + (document.getVersion() + 1) + "_" + document.getOriginalName();
//        String versionFilePath = versionDir + File.separator + versionFileName;
//
//        // 3. 复制文件到版本目录
//        try {
//            Files.copy(Paths.get(newVersionPath), Paths.get(versionFilePath), StandardCopyOption.REPLACE_EXISTING);
//        } catch (IOException e) {
//            throw new RuntimeException("Failed to save document version", e);
//        }

//        // 4. 更新文档信息
//        document.setFilePath(newVersionPath);
//        document.setChangesUrl(changesUrl);
//        document.setVersion(document.getVersion() + 1);
//        document.setModified(new Date());
//        document.setFileSize(new File(newVersionPath).length());
//
//        // 5. 保存版本记录
//        DocumentVersion version = new DocumentVersion();
//        version.setDocumentId(document.getId());
//        version.setVersion(document.getVersion());
//        version.setFilePath(versionFilePath);
//        version.setCreated(new Date());
//        version.setChangesUrl(changesUrl);
//        documentVersionService.save(version);
//
//        // 6. 更新主文档
//        fileAttachmentService.updateById(document);
    }

    private String downloadChanges(String changesUrl) throws Exception {
        URL url = new URL(changesUrl);
        try (InputStream in = url.openStream();
             Scanner scanner = new Scanner(in, StandardCharsets.UTF_8.name())) {
            return scanner.useDelimiter("\\A").next();
        }
    }
    
    private String getDocumentType(String fileType) {
        if (fileType.equals("docx")) return "word";
        if (fileType.equals("xlsx")) return "cell";
        if (fileType.equals("pptx")) return "slide";
        return "word";
    }
    /**
     * 
    * @功能描述: 返回在线文件属于自己创建的文档内容，后续还需要增加分享给我的文档，也一并进行需要考虑
    * @Title: getOnlineOffices
    * @param @return    参数
    * @return List<FileAttachment>    返回类型
    * @throws
    * <AUTHOR>
    * @date 2025年6月24日 上午11:46:05
     */
    public List<FileAttachment> getOnlineOffices(Page page, FileAttachment record) {
//    	Example example = new Example(FileAttachment.class);
//        example.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
//         	.andEqualTo("module_name", "onlineOffice")
//         	.andLike("original_name", record.getOriginalName())
//         	.andLike("file_extension", record.getFileExtension());
//        
// 		Example.Criteria orCriteria = example.createCriteria();
//     		orCriteria.orEqualTo("create_user", UserInfoHolder.getCurrentUserCode())
//     	     		  .orEqualTo("update_user", UserInfoHolder.getCurrentUserCode());
//     	example.or(orCriteria); 
    	record.setIsDeleted("N");
    	record.setCreateUser(UserInfoHolder.getCurrentUserCode());
    	record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
    	return fileAttachmentMapper.getFileAttachmentList(page, record);
    }
}
