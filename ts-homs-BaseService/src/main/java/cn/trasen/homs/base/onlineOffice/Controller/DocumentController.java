/************************************************
* @功能描述: TODO
* @Title: DocumentController.java
* @Package cn.trasen.homs.base.onlineOffice.Controller
* <AUTHOR>
* @date 2025年6月16日 下午12:28:08
* @version V1.0
*************************************************/
package cn.trasen.homs.base.onlineOffice.Controller;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.model.FileAttachment;
import cn.trasen.homs.base.onlineOffice.Config.OfficeConfig;
import cn.trasen.homs.base.onlineOffice.DTO.CreateDocumentDto;
import cn.trasen.homs.base.onlineOffice.DTO.OfficeCallbackDto;
import cn.trasen.homs.base.onlineOffice.Service.DocumentService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import lombok.extern.slf4j.Slf4j;

/**
* @ClassName: DocumentController
* @Description: TODO 
* <AUTHOR>
* @date 2025年6月16日 下午12:28:08
*
*/
@RestController
@Slf4j
@RequestMapping("/api/documents")
public class DocumentController {
    
    @Autowired
    private DocumentService documentService;
    
    // 创建新文档
    @PostMapping("/new")
    public ResponseEntity<FileAttachment> createDocument(@RequestBody CreateDocumentDto dto) {
        FileAttachment document = documentService.createDocument(dto);
        return ResponseEntity.ok(document);
    }
    
    // 上传文件
//    @PostMapping("/upload")
//    public ResponseEntity<Document> uploadDocument(@RequestParam("file") MultipartFile file,
//                                                 @RequestParam Long userId) {
//        Document document = documentService.uploadDocument(file, userId);
//        return ResponseEntity.ok(document);
//    }
    
    // 获取文档
    @CrossOrigin(exposedHeaders = {"Content-Type", "Content-Length"})
    @GetMapping("/getFileResource/{id}")
    public ResponseEntity<Resource> getDocument(@PathVariable String id) throws IOException {
    	FileAttachment document = documentService.getDocument(id);
        
        // 添加异常处理
        if (document == null || !new File(document.getFilePath()).exists()) {
            return ResponseEntity.notFound().build();
        }

        // 添加文件类型检测
        String mimeType = Files.probeContentType(Paths.get(document.getFilePath()));
        
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_TYPE, mimeType)
            .header(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=\"" + URLEncoder.encode(document.getOriginalName(), "UTF-8") + "\"")
            .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
            .body(new FileSystemResource(document.getFilePath()));
        
//    	FileAttachment document = documentService.getDocument(id);
//    	String filePath = document.getFilePath();
//    	File file = new File(filePath);
//    	Resource resource = new FileSystemResource(file);
//    	return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"")
//                .body(resource);
    	
    }
    
    // 获取文档编辑配置
    @GetMapping("/{id}/config")
    public ResponseEntity<OfficeConfig> getDocumentConfig(@PathVariable String id,
                                                            @RequestParam String userId) {
        OfficeConfig config = documentService.getDocumentConfig(id, userId);
        return ResponseEntity.ok(config);
    }
    
    // 获取文档历史版本
//    @GetMapping("/{id}/versions")
//    public ResponseEntity<List<DocumentVersion>> getDocumentVersions(@PathVariable Long id) {
//        List<DocumentVersion> versions = documentService.getDocumentVersions(id);
//        return ResponseEntity.ok(versions);
//    }
    
    // 获取特定版本文档
//    @GetMapping("/{id}/versions/{versionId}")
//    public ResponseEntity<DocumentVersion> getDocumentVersion(@PathVariable Long id,
//                                                            @PathVariable Long versionId) {
//        DocumentVersion version = documentService.getDocumentVersion(id, versionId);
//        return ResponseEntity.ok(version);
//    }
    
    // 回调接口 - OnlyOffice 保存文档后调用
    @PostMapping("/callback")
    public ResponseEntity<?> handleCallback(@RequestBody OfficeCallbackDto callbackDto) throws IOException {
    	try {
            documentService.handleCallback(callbackDto);
            //响应体必须包含 {"error":0} 表示成功
            return ResponseEntity.ok().body(Collections.singletonMap("error", 0));
        } catch (Exception e) {
            log.error("文档保存失败", e);
            return ResponseEntity.ok().body(Collections.singletonMap("error", 1));
        }
    }
    
    @GetMapping("/getOnlineOfficeList")
    public DataSet<FileAttachment> getOnlineOfficeList(HttpServletRequest request,Page page, FileAttachment record) {
    	List<FileAttachment> list = documentService.getOnlineOffices(page, record);
    	return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }
}
