package cn.trasen.homs.base.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LoginLogStatResp {

    @ApiModelProperty(value = "在线人数")
    private int onlines;

    @ApiModelProperty(value = "累积访问人数")
    private int logins;

    @ApiModelProperty(value = "PC端访问人数")
    private int loginsByPc;

    @ApiModelProperty(value = "移动端访问人数")
    private int loginsByWx;

    @ApiModelProperty(value = "累积访问次数")
    private int accesses;
}
