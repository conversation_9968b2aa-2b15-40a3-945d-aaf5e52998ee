package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeImport;
import cn.trasen.homs.base.bean.VirtualOrgEmployeeListReq;
import cn.trasen.homs.base.bean.VirtualOrgSaveEmployeeReq;
import cn.trasen.homs.base.bean.VirtualOrgSaveReq;
import cn.trasen.homs.base.service.IVirtualOrgEmployeeService;
import cn.trasen.homs.base.service.IVirtualOrgService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/** 
* @description: 组织机构
* @return: 
* @author: liyuan
* @createTime: 2021/7/26 10:14
*/
@Api(tags = "虚拟机构")
@RequestMapping("/virtualOrg")
@RestController
public class VirtualOrgController {


	@Autowired
	IVirtualOrgService virtualOrgService;

	@Autowired
	IVirtualOrgEmployeeService virtualOrgEmployeeService;


	@ApiOperation(value = "获取树状格式", notes = "获取树状格式")
	@RequestMapping(value = "/getTreeData", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult<List<TreeModel>> getTreeData() {
		return PlatformResult.success(virtualOrgService.getTreeData());
	}

	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping(value = "/add")
	public PlatformResult add(@RequestBody @Validated  VirtualOrgSaveReq virtualOrgSaveReq) {
		virtualOrgService.add(virtualOrgSaveReq);
		return PlatformResult.success();
	}


	@ApiOperation(value = "修改", notes = "修改")
	@PostMapping(value = "/edit")
	public PlatformResult edit(@RequestBody @Validated  VirtualOrgSaveReq virtualOrgSaveReq) {
		virtualOrgService.update(virtualOrgSaveReq);
		return PlatformResult.success();
	}

	@ApiOperation(value = "删除", notes = "删除")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")
	})
	@RequestMapping(value = "/del", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult del(@RequestParam("orgId") String orgId) {
		virtualOrgService.delete(orgId);
		return PlatformResult.success();
	}


	@ApiOperation(value = "验证删除", notes = "验证删除")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyDel", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyDel(@RequestParam("orgId") String orgId) {
		virtualOrgService.verifyEnable(orgId,"虚拟组织下有子级，不能删除！");
		return PlatformResult.success();
	}


	@ApiOperation(value = "启用禁用", notes = "启用禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")
			, @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
	})
	@RequestMapping(value = "/enable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult enable(@RequestParam("orgId") String orgId, @RequestParam("enable") String enable) {
		virtualOrgService.enable(orgId, enable);
		return PlatformResult.success();
	}


	@ApiOperation(value = "验证禁用", notes = "验证禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")
	})
	@RequestMapping(value = "/verifyEnable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult verifyEnable(@RequestParam("orgId") String orgId) {
		virtualOrgService.verifyEnable(orgId,"虚拟组织下有子级，不能禁用！");
		return PlatformResult.success();
	}


	@ApiOperation(value = "获取虚拟组织员工列表分页", notes = "获取虚拟组织员工列表分页")
	@PostMapping(value = "/getEmployeePageList")
	public DataSet<HrmsEmployeeResp> getEmployeePageList(Page page, VirtualOrgEmployeeListReq virtualOrgEmployeeListReq) {
		return virtualOrgService.getEmployeePageList(page, virtualOrgEmployeeListReq);
	}

	@ApiOperation(value = "获取虚拟组织员工列表", notes = "获取虚拟组织员工列表")
	@PostMapping(value = "/getEmployeeList")
	public List<HrmsEmployeeResp> getEmployeeList(@RequestBody VirtualOrgEmployeeListReq virtualOrgEmployeeListReq) {
		return virtualOrgService.getEmployeeList(virtualOrgEmployeeListReq);
	}

	@ApiOperation(value = "保存虚拟组织员工", notes = "保存虚拟组织员工")
	@PostMapping(value = "/saveEmployee")
	public PlatformResult saveEmployee(@RequestBody VirtualOrgSaveEmployeeReq virtualOrgSaveEmployeeReq) {
		virtualOrgService.saveEmployee(virtualOrgSaveEmployeeReq);
		return PlatformResult.success();
	}

	@ApiOperation(value = "删除虚拟组织员工", notes = "删除虚拟组织员工")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "机构ID", required = true, dataType = "String")
	})
	@PostMapping(value = "/deleteEmployee")
	public PlatformResult deleteEmployee(@RequestParam("orgId") String orgId, @RequestParam("employeeId") String employeeId) {
		virtualOrgEmployeeService.deleteEmployee(orgId, employeeId);
		return PlatformResult.success();
	}

	/**
	 * @description: 模板下载
	 * @param: response
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/8/4 15:31
	 */
	@GetMapping(value = "/download/template/importVirtualOrg")
	@ApiOperation(value = "下载虚拟组织导入模板", notes = "下载虚拟组织导入模板")
	public void downloadImportVirtualOrgTemplate(HttpServletResponse response) {
		try {
			ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
			String filename = "虚拟组织导入模板.xls";
			String template = "template/importVirtualOrg.xls";
			ClassPathResource resource = new ClassPathResource(template);
			exportExcelUtil.downloadExportExcel(filename, response, resource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@ApiOperation(value = "导入", notes = "导入")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orgId", value = "组织ID", required = true, dataType = "String"),
			@ApiImplicitParam(name = "type", value = "type 1:增量 2全量", required = true, dataType = "String")

	})
	@PostMapping(value = "/import")
	public PlatformResult move(@RequestParam("file") MultipartFile file, String orgId, String type) {
		List<VirtualOrgEmployeeImport> virtualOrgEmployeeImport = (List<VirtualOrgEmployeeImport>) ImportExcelUtil.getExcelDatas(file, VirtualOrgEmployeeImport.class);

		return virtualOrgEmployeeService.excelImportEmployee(virtualOrgEmployeeImport, orgId, type);
	}


	@ApiOperation(value = "导出", notes = "导出")
	@RequestMapping(value = "/export", method = {RequestMethod.POST, RequestMethod.GET})
	public void export(VirtualOrgEmployeeListReq virtualOrgEmployeeListReq, HttpServletResponse response, HttpServletRequest request) throws Exception {
		List<HrmsEmployeeResp> employeeResps = virtualOrgService.getEmployeeList(virtualOrgEmployeeListReq);
		String excelName = "虚拟组织员工列表";
		List<String> headList = new ArrayList<>();
		headList.add("工号");
		headList.add("姓名");
		headList.add("手机号码");
		headList.add("组织机构");
		headList.add("虚拟组织机构");
		headList.add("岗位");
		headList.add("职务");

		List<String> fieldList = new ArrayList<>();
		fieldList.add("工号");
		fieldList.add("姓名");
		fieldList.add("手机号码");
		fieldList.add("组织机构");
		fieldList.add("虚拟组织机构");

		fieldList.add("岗位");
		fieldList.add("职务");
		List<Map<String, Object>> dataList = new ArrayList<>();
		Map<String, Object> map;
		for (HrmsEmployeeResp o : employeeResps) {
			map = new HashMap<>();
			map.put("工号", o.getEmployeeNo());
			map.put("姓名", o.getEmployeeName());
			map.put("手机号码", o.getPhoneNumber());
			map.put("组织机构", o.getOrgName());
			map.put("虚拟组织机构", o.getVirtualOrgName());
			map.put("岗位", o.getPostName());
			map.put("职务", o.getPositionName());
			dataList.add(map);
		}
		ExportUtil.createExcel(excelName, headList, fieldList, dataList, response, request);
	}
}