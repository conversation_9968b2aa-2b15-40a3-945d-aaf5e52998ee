package cn.trasen.homs.base.saasOrg.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 机构树
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class HrmsOrgTreeVo {

    @ApiModelProperty("机构编码")
    private String id;
    
    @ApiModelProperty(value = "机构名称", required = true)
    private String name;
    
    @ApiModelProperty("上级机构编码")
    private String pid;
    
    @ApiModelProperty("管理类别编码:	WJW-卫计委,YLT-医联体,INTERNET_HOSP-互联网医院,YLJG-医疗机构,AREA-院区,TJ_YQ-体检的院区标识")
    private String marCatgCode;
    
    @ApiModelProperty("管理类别名称")
    private String marCatgName;
    
    @ApiModelProperty("子机构集合")
    private List<HrmsOrgTreeVo> children;
}
