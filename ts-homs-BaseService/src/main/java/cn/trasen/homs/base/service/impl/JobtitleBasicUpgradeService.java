package cn.trasen.homs.base.service.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.base.bean.JobtitleBasicUpgradeSaveReq;
import cn.trasen.homs.base.mapper.JobtitleBasicUpgradeMapper;
import cn.trasen.homs.base.model.JobtitleBasicUpgrade;
import cn.trasen.homs.base.service.IJobtitleBasicUpgradeService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @createTime 2021/8/7 15:51
 * @description
 */

@Service
public class JobtitleBasicUpgradeService implements IJobtitleBasicUpgradeService {

    @Autowired
    JobtitleBasicUpgradeMapper jobtitleBasicUpgradeMapper;


    @Transactional(rollbackFor = Exception.class)

    @Override
    /**
     * @description: 保存
     * @param: jobtitleBasicUpgradeList
     * @return: void
     * @author: liyuan
     * @createTime: 2021/8/7 16:02
     */
    public void save(String jobtitleBasicId, List<JobtitleBasicUpgradeSaveReq> jobtitleBasicUpgradeList) {

        if (CollectionUtils.isEmpty(jobtitleBasicUpgradeList)) {
            return;
        }
        Example example = new Example(JobtitleBasicUpgrade.class);
        example.createCriteria().andEqualTo("jobtitleBasicId", jobtitleBasicId);
        jobtitleBasicUpgradeMapper.deleteByExample(example);
        for (JobtitleBasicUpgradeSaveReq jobtitleBasicUpgradeSaveReq : jobtitleBasicUpgradeList) {
            JobtitleBasicUpgrade jobtitleBasicUpgrade = BeanUtils.InitBean(JobtitleBasicUpgrade.class);
            BeanUtil.copyProperties(jobtitleBasicUpgradeSaveReq, jobtitleBasicUpgrade);
            jobtitleBasicUpgrade.setJobtitleBasicId(jobtitleBasicId);
            jobtitleBasicUpgradeMapper.insertSelective(jobtitleBasicUpgrade);
        }
    }


    @Override
    /**
     * @description: 获取列表
     * @param: jobtitleBasicId
     * @return: java.util.List<cn.trasen.basicsbottom.bean.JobtitleBasicUpgradeListResp>
     * @author: liyuan
     * @createTime: 2021/8/7 16:05
     */
    public List<JobtitleBasicUpgrade> getBaseList(String jobtitleBasicId) {

        Example example = new Example(JobtitleBasicUpgrade.class);

        Example.Criteria criteria = example.createCriteria();
        example.setOrderByClause("create_date ASC");

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo("jobtitleBasicId", jobtitleBasicId);

        return jobtitleBasicUpgradeMapper.selectByExample(example);
    }
}