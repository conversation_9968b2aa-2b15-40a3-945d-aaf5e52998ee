package cn.trasen.homs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * @Author: Liz<PERSON>huo
 * @Description: 群组实体类
 * @Date: 2020/1/14 14:56
 * @Param:
 * @return:
 **/
@Table(name = "COMM_ORG_GROUP")
@Setter
@Getter
public class OrgGroup {
    /**
     * ID
     */
    @Id
    @Column(name = "GROUP_ID")
    @ApiModelProperty(value = "ID")
    private String groupId;

    /**
     * 群组名称
     */
    @Column(name = "GROUP_NAME")
    @ApiModelProperty(value = "群组名称")
    private String groupName;

    /**
     * 群组描述
     */
    @Column(name = "GROUP_DESCRIPTION")
    @ApiModelProperty(value = "群组描述")
    private String groupDescription;

    /**
     * 域标识
     */
    @Column(name = "DOMAIN_ID")
    @ApiModelProperty(value = "域标识")
    private String domainId;

    /**
     * 组类型 0:系统组  1:用户自定义组
     */
    @Column(name = "GROUP_TYPE")
    @ApiModelProperty(value = "组类型 0:系统组  1:用户自定义组")
    private Short groupType;

    /**
     * 群组分类ID
     */
    @Column(name = "GROUP_CLASS_ID")
    @ApiModelProperty(value = "群组分类ID")
    private String groupClassId;

    /**
     * 群组分类名称
     */
    @Column(name = "GROUP_CLASS_NAME")
    @ApiModelProperty(value = "群组分类名称")
    private String groupClassName;

    /**
     * 分组排序
     */
    @Column(name = "GROUP_ORDER")
    @ApiModelProperty(value = "分组排序")
    private String groupOrder;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 是否删除 N 正常   Y 删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除 N 正常   Y 删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 简称
     */
    @Column(name = "GROUP_PINYIN")
    @ApiModelProperty(value = "简称")
    private String groupPinyin;

    /**
     * 组用户名称串
     */
    @Column(name = "GROUP_USER_NAMES")
    @ApiModelProperty(value = "组用户名称串")
    private String groupUserNames;

    /**
     * 组用户ID串
     */
    @Column(name = "GROUP_USER_STRING")
    @ApiModelProperty(value = "组用户ID串")
    private String groupUserString;

    /**
     * 使用人范围名称
     */
    @Column(name = "RANGE_NAME")
    @ApiModelProperty(value = "使用人范围名称")
    private String rangeName;

    /**
     * 使用人ID
     */
    @Column(name = "RANGE_EMP")
    @ApiModelProperty(value = "使用人ID")
    private String rangeEmp;

    /**
     * 使用组织
     */
    @Column(name = "RANGE_ORG")
    @ApiModelProperty(value = "使用组织")
    private String rangeOrg;

    /**
     * 移动端 是否置顶（0：是、1：否）
     */
    @Column(name = "IS_TOP")
    @ApiModelProperty(value = "移动端 是否置顶（0：是、1：否）")
    private String isTop;


    /**
     * 是否启用: 1=是; 2=否;
     */
    private String isEnable;
    
    @Column(name = "sso_org_code")
    String ssoOrgCode;
}