package cn.trasen.homs.base.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.ImportExcel;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.base.bean.LinkmanPo;
import cn.trasen.homs.base.bean.ResultData;
import cn.trasen.homs.base.model.Linkman;
import cn.trasen.homs.base.service.LinkmanService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description: Excel导入Controller层
 * @Date: 2020/2/26 09:24
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Api(tags = "Excel导入Controller层")
@RestController
public class LinkmanImportExcelController {

    private static final Logger logger = LoggerFactory.getLogger(LinkmanImportExcelController.class);

    @Autowired
    private ImportExcel importExcel;

    @Autowired
    private LinkmanService linkmanService;

//    @Autowired
//    private EmployeeService employeeService;
//
//    @Autowired
//    private DutyMapper dutyMapper;
//
//    @Autowired
//    private EmployeeFeignClient employeeFeignClient;

    @Resource
    private ResourceLoader resourceLoader;

    /**
     * @Author: Lizhihuo
     * @Description: 下载员工基本信息导入模板
     * @Date: 2020/3/3 15:45
     * @Param:
     * @return: void
     **/
    @GetMapping(value = "/employee/import/importEmployee")
    @ApiOperation(value = "下载员工基本信息导入模板", notes = "下载员工基本信息导入模板")
    public void expotEmployee(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "员工基本信息.xls";
            String path = "template/importEmployee.xls";
            exportExcelUtil.downloadExportExcel(filename, path, response, resourceLoader);
        } catch (Exception e) {
            e.printStackTrace();
        }

        /*//设置表头
        String exportHeaders = "发薪号(必填),员工工号(必填),姓名(必填),身份证,性别,部门编码(必填),职务,手机号码";
        //设置对应字段
        String exportFields = "empCode,userAccounts,empName,empIdcard,empSex,empDeptCode,empDutyName,empPhone";
        //设置页签名
        String sheetName = "员工基本信息";
        String filePath = "template/importEmployee.xls";
        File file = new File(filePath);
        String fileName = "员工基本信息.xls";
        ExportExcelUtil<EmployeePo> excelUtil = new ExportExcelUtil<EmployeePo>();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.flushBuffer();
        InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
        OutputStream outputStream = response.getOutputStream();
        excelUtil.exportExcel(null, exportHeaders, exportFields, null, outputStream);*/
    }

    /**
     * @Author: Lizhihuo
     * @Description: 下载个人联系人导入模板
     * @Date: 2020/3/5 15:53
     * @Param:
     * @return: void
     **/
    @GetMapping(value = "/employee/import/downloadImportLinkMan")
    @ApiOperation(value = "下载个人联系人导入模板", notes = "下载个人联系人导入模板")
    public void downloadImportLinkMan(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "个人联系人信息.xls";
            String path = "template/importLinkman.xls";
            exportExcelUtil.downloadExportExcel(filename, path, response, resourceLoader);
        } catch (Exception e) {
            e.printStackTrace();
        }

         /*//设置表头
        String exportHeaders = "员工姓名,单位,电子邮件,手机,职务,职业,部门,联系人分类";
        //设置对应字段
        String exportFields = "linkmanName,linkmanUnit,linkmanEmail,mobilePhone,linkmanDuty,linkmanProfession,linkmanDepart,className";
        //设置页签名
        //String sheetName = "个人联系人信息";

        String fileName = "个人联系人信息.xls";
        ExportExcelUtil<Linkman> excelUtil = new ExportExcelUtil<Linkman>();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.flushBuffer();
        OutputStream outputStream = response.getOutputStream();
        excelUtil.exportExcel(null, exportHeaders, exportFields, null, outputStream);*/
    }

    /**
     * @Author: Lizhihuo
     * @Description: 个人联系人信息导入
     * @Date: 2020/3/5 15:57
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult
     **/
    @PostMapping(value = "/employee/import/linkManImport")
    @ApiOperation(value = "个人联系人信息导入", notes = "个人联系人信息导入")
    public PlatformResult linkManImport(@RequestPart("file") MultipartFile file) throws Exception {
        long startTime = System.currentTimeMillis();   //获取开始时间
        logger.error("=====个人联系人信息导入=====" + startTime);
        List<Linkman> countLinkmanList = new ArrayList<>();//总条数
        List<Linkman> successLinkmanList = new ArrayList<>();//成功条数
        List<Linkman> failLinkmanList = new ArrayList<>();//失败条数
        List<ResultData> resultDataList = new ArrayList<>();//失败信息
        PlatformResult platformResult = new PlatformResult();
        Linkman linkman = new Linkman();
        //List<LinkmanPo> linkmanPoList = importExcel.readExcelFileToDTO(file, LinkmanPo.class); //有头部标题 但是只能指定字段
        List<LinkmanPo> linkmanPoList = (List<LinkmanPo>) ImportExcelUtil.getExcelDatas(file, LinkmanPo.class);//无头部标题 无需指定Excel里面的字段
        if (linkmanPoList != null && linkmanPoList.size() > 0) {
            for (int i = 0; i < linkmanPoList.size(); i++) {
                if (StringUtils.isNoneBlank(linkmanPoList.get(i).getLinkmanName())) {
                    linkman.setLinkmanName(linkmanPoList.get(i).getLinkmanName());
                    linkman.setLinkmanUnit(linkmanPoList.get(i).getLinkmanUnit());
                    linkman.setLinkmanDepart(linkmanPoList.get(i).getLinkmanDepart());
                    linkman.setLinkmanProfession(linkmanPoList.get(i).getLinkmanProfession());
                    linkman.setLinkmanDuty(linkmanPoList.get(i).getLinkmanDuty());
                    linkman.setLinkmanEmail(linkmanPoList.get(i).getLinkmanEmail());
                    linkman.setMobilePhone(linkmanPoList.get(i).getMobilePhone());
                    linkman.setClassName(linkmanPoList.get(i).getClassName());
                    linkman.setLinkmanDescribe(linkmanPoList.get(i).getLinkmanDescribe());
                    if(StringUtils.isBlank(linkmanPoList.get(i).getLinkmanSex())==false)
                    {
                       if(linkmanPoList.get(i).getLinkmanSex().equals("男"))
                       {
                           linkman.setLinkmanSex("0");
                       }
                       else if(linkmanPoList.get(i).getLinkmanSex().equals("女"))
                        {
                            linkman.setLinkmanSex("1");

                        }
                    }


//                    linkman.setClassName(linkmanPoList.get(i).getClassName());
//                    linkman.setClassName(linkmanPoList.get(i).getClassName());
//                    linkman.setClassName(linkmanPoList.get(i).getClassName());
//                    linkman.setClassName(linkmanPoList.get(i).getClassName());
                    int count = linkmanService.insert(linkman);
                    if (count > 0) {
                        successLinkmanList.add(linkman);
                    } else {
                        /*platformResult.setMessage("数据表不符合要求,请下载模板填写！");
                        platformResult.setSuccess(false);
                        return platformResult;*/
                        failLinkmanList.add(linkman);
                    }
                } else {
                    ResultData resultData = new ResultData();
                    resultData.setData("第 " + (i + 3) + " 行员工姓名为必填项、此行导入失败");
                    resultDataList.add(resultData);
                    failLinkmanList.add(linkman);
                }
                countLinkmanList.add(linkman);
            }
        } else {
            platformResult.setMessage("表格数据为空、导入失败");
            platformResult.setSuccess(false);
            return platformResult;
        }
        platformResult.setMessage("信息导入成功 " + " 总条数: " + countLinkmanList.size() + " 成功: " + successLinkmanList.size() + " 失败: " + "<span style='color: red;'>" + failLinkmanList.size() + "</span>");
        platformResult.setObject(resultDataList);//返回失败信息
        platformResult.setSuccess(true);
        long endTime = System.currentTimeMillis(); //获取结束时间
        logger.error("=====个人联系人信息导入结束=====" + (endTime - startTime) + "ms");
        return platformResult;
    }


}
