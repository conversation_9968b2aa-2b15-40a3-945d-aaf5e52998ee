package cn.trasen.homs.base.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.base.bean.OrganizationListReq;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import tk.mybatis.mapper.common.Mapper;

public interface OrganizationMapper extends Mapper<Organization> {



    /** 
    * @description: 全部删除
* @param: list
    * @return: int
    * @author: liyuan
    * @createTime: 2021/7/28 18:14
    */
    int deleteAll();

	List<Organization> getAllPageList(Page page,OrganizationListReq organizationListReq);
	
	
	List<String> selectLeaderListByDeptCode(@Param("orgId")String orgId,@Param("roleIds")List<String> roleIds);

	List<Map<String, String>> selectLeaderList(@Param("employeeIdArray")List<String> employeeIdArray);
}