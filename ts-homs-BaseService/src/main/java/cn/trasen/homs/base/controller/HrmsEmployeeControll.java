package cn.trasen.homs.base.controller;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.BootComm.utils.PasswordHash;
import cn.trasen.homs.base.bean.EmployeeImport;
import cn.trasen.homs.base.bean.EmployeeListReq;
import cn.trasen.homs.base.bean.EmployeeSelectListReq;
import cn.trasen.homs.base.bean.EmployeeSelectListRes;
import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.bean.HrmsEmployeeSaveReq;
import cn.trasen.homs.base.bean.PlatformBody;
import cn.trasen.homs.base.bean.ResetJobNumberReq;
import cn.trasen.homs.base.bean.ResetPasswordReq;
import cn.trasen.homs.base.bo.EmployeeListInBO;
import cn.trasen.homs.base.bo.EmployeeListOutBO;
import cn.trasen.homs.base.customEmployee.controller.CustomEmployeeInfoController;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.model.CommLoginLogs;
import cn.trasen.homs.base.model.DictItem;
import cn.trasen.homs.base.model.HrmsEmployee;
import cn.trasen.homs.base.service.CommLoginLogsService;
import cn.trasen.homs.base.service.HrmsEmployeeService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.bean.oa.EmployeeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.PageDataReq;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.OAEmployeeFeignService;
import cn.trasen.homs.feign.sso.RightFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @createTime 2021/6/18 11:23
 * @description
 */


@Slf4j
@Api(tags = "员工基本信息Controller")
@RestController
public class HrmsEmployeeControll {


    @Autowired
    private HrmsEmployeeService hrmsEmployeeService;
    
    @Value("${changePwdByUserCodePlatformUrl}")
    private String changePwdByUserCodePlatformUrl;
    
    @Autowired
    private CommLoginLogsService commLoginLogsService;
    
    @Autowired
    private CustomEmployeeInfoService customEmployeeInfoService;
    
    @Autowired
    private CustomEmployeeBaseService customEmployeeBaseService;
    
    @Autowired
    private IDictItemService dictItemService;


    @ApiOperation(value = "查询批量员工详情", notes = "查询批量员工详情")
    @PostMapping(value = "/employee/getEmployeeDetailByCodes")
    public PlatformResult<List<HrmsEmployeeResp>> getEmployeeDetailByCodes(@RequestBody List<String> employeeCodes) {
        try {
            List<HrmsEmployeeResp> hrmsEmployees = hrmsEmployeeService.getEmployeeDetailByCodes(employeeCodes);

            return PlatformResult.success(hrmsEmployees);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    @ApiOperation(value = "查询批量员工详情", notes = "查询批量员工详情")
    @PostMapping(value = "/employee/getEmployeeDetailByIds")
    public PlatformResult<List<HrmsEmployeeResp>> getEmployeeDetailByIds(@RequestBody List<String> employeeIds) {
        try {
            List<HrmsEmployeeResp> hrmsEmployees = hrmsEmployeeService.getEmployeeDetailByIds(employeeIds);

            return PlatformResult.success(hrmsEmployees);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * @description: 查询自己详情
     * @param: employeeCode
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/6/18 17:37
     */
    @ApiOperation(value = "查询自己详情", notes = "查询自己详情")
    @RequestMapping(value = "/employee/getMyEmployeeDetail", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<HrmsEmployeeResp> getMyEmployeeDetail(String employeeNo) {
        try {
        	if(StringUtils.isBlank(employeeNo)){
        		employeeNo = UserInfoHolder.getCurrentUserCode();
        	}
            HrmsEmployeeResp hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(employeeNo);
            if(UserInfoHolder.ISADMIN()){
            	hrmsEmployee.setIsAdmin("Y");
            }else{
            	hrmsEmployee.setIsAdmin("N");
            }
           
            return PlatformResult.success(hrmsEmployee);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * @description: 查询当前登录员工详情
     * @param: employeeCode
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/6/18 17:37
     */
    @ApiOperation(value = "查询员工详情", notes = "查询员工详情")
    @PostMapping(value = "/employee/getEmployeeDetailByCode/{employeeCode}")
    public PlatformResult<HrmsEmployeeResp> getEmployeeDetailByCode(@PathVariable String employeeCode) {
        try {
            HrmsEmployeeResp hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(employeeCode);

            return PlatformResult.success(hrmsEmployee);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @description: 查询当前登录员工详情
     * @param: employeeCode
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/6/18 17:37
     */
    @ApiOperation(value = "查询员工详情", notes = "查询员工详情")
    @PostMapping(value = "/employee/findByEmployeePhoneNumber/{phoneNumber}")
    public PlatformResult<HrmsEmployeeResp> findByEmployeePhoneNumber(@PathVariable String phoneNumber) {
        try {
            HrmsEmployeeResp hrmsEmployee = hrmsEmployeeService.findByEmployeePhoneNumber(phoneNumber);

            return PlatformResult.success(hrmsEmployee);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * @description: 查询当前登录员工详情
     * @param: employeeCode
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.basicsbottom.model.HrmsEmployee>
     * @author: liyuan
     * @createTime: 2021/6/18 17:37
     */
    @ApiOperation(value = "查询员工详情", notes = "查询员工详情")
    @PostMapping(value = "/employee/findByEmployeeId/{employeeId}")
    public PlatformResult<HrmsEmployeeResp> findByEmployeeId(@PathVariable String employeeId) {
        try {
            HrmsEmployeeResp hrmsEmployee = hrmsEmployeeService.findByEmployeeId(employeeId);
            return PlatformResult.success(hrmsEmployee);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    @ApiOperation(value = "查询员工信息列表(分页)", notes = "查询员工信息列表(分页)")
    @PostMapping(value = "/employee/getEmployeePageList")
    public DataSet<HrmsEmployeeResp> getEmployeePageList(Page page, EmployeeListReq record) {
        List<HrmsEmployeeResp> list = hrmsEmployeeService.getEmployeePageList(page, record);
        return new DataSet<HrmsEmployeeResp>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    @ApiOperation(value = "查询员工信息列表(分页)", notes = "查询员工信息列表(分页)")
    @PostMapping(value = "/employee/getEmployeeAllPageList")
    public DataSet<HrmsEmployeeResp> getEmployeeAllPageList(@RequestBody PageDataReq<EmployeeListReq> pageDataReq) {
        Page page = new Page(pageDataReq.getPageNum(), pageDataReq.getPageSize());
//        page.setPageNo();
//        page.setPageSize();
        List<HrmsEmployeeResp> list = hrmsEmployeeService.getEmployeePageList(page, pageDataReq.getData());
        return new DataSet<HrmsEmployeeResp>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }


    @ApiOperation(value = "查询我的员工信息列表", notes = "查询我的员工信息列表")
    @PostMapping(value = "/employee/getMyEmployeeList")
    public PlatformResult<List<HrmsEmployeeResp>> getMyEmployeeList(@RequestBody EmployeeListReq record) {

        //数据权限
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if (!UserInfoHolder.ISADMIN()) {    // 是否管理员
            if (!StringUtils.isEmpty(orgRang)) {//查询组织范围数据
                orgRang = orgRang.replace("'", "").replace("(", "").replace(")", "");
                String[] orgIdListArr = orgRang.split(",");
                if (orgIdListArr != null && orgIdListArr.length > 0) {
                    record.setOrgIdList(Arrays.asList(orgIdListArr));
                }
            }
        }

        return PlatformResult.success(hrmsEmployeeService.getEmployeeList(record));
    }


    @ApiOperation(value = "查询员工信息列表", notes = "查询员工信息列表")
    @PostMapping(value = "/employee/getEmployeeList")
    public PlatformResult<List<HrmsEmployeeResp>> getEmployeeList(@RequestBody EmployeeListReq record) {
        return PlatformResult.success(hrmsEmployeeService.getEmployeeList(record));
    }

    @ApiOperation(value = "修改员工个人配置信息", notes = "修改员工个人配置信息")
    @PostMapping(value = "/employee/updateUserConfig")
    public PlatformResult updateUserConfig(@RequestBody HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        if (hrmsEmployeeService.updateUserConfig(hrmsEmployeeSaveReq) > 0) {
            return PlatformResult.success();

        }
        return PlatformResult.failure();
    }


    @ApiOperation(value = "修改员工信息", notes = "修改员工信息")
    @PostMapping(value = "/employee/updateEmployee")
    public PlatformResult updateEmployee(@RequestBody HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        if (hrmsEmployeeService.updateEmployee(hrmsEmployeeSaveReq) > 0) {
            return PlatformResult.success();

        }
        return PlatformResult.failure();
    }

    @ApiOperation(value = "字段权限设置查询员工信息列表", notes = "字段权限设置查询员工信息列表")
    @PostMapping(value = "/employee/getJurisdictionEmployeePageList")
    public DataSet<HrmsEmployeeResp> getJurisdictionEmployeePageList(Page page, EmployeeListReq record) {
        List<HrmsEmployeeResp> list = hrmsEmployeeService.getJurisdictionEmployeePageList(page, record);
        return new DataSet<HrmsEmployeeResp>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }


    @Autowired
    SystemUserFeignService systemUserFeignService;
    @Autowired
    RightFeignService rightService;

    @Autowired
    OAEmployeeFeignService oAEmployeeFeignService;

    @ApiOperation(value = "同步员工", notes = "同步员工")
    @RequestMapping(value = "/employee/syn", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult syn() {

        List<HrmsEmployeeResp> hrmsEmployeeRespList = hrmsEmployeeService.getEmployeeList(new EmployeeListReq());

        for (HrmsEmployeeResp h : hrmsEmployeeRespList) {

            ThpsUserReq thpsUser = new ThpsUserReq();
            thpsUser.setId(h.getEmployeeId());
            thpsUser.setUsercode(h.getEmployeeNo());
            thpsUser.setOldusercode(h.getEmployeeNo());
            thpsUser.setUsername(h.getEmployeeName());
            thpsUser.setMobileNo(h.getPhoneNumber());
            thpsUser.setSex(h.getGender());
            thpsUser.setDeptcode(h.getOrgId());
            String corpCode = UserInfoHolder.getCurrentUserCorpCode();
            if (StringUtils.isNoneBlank(corpCode)) {
                thpsUser.setCorpcode(corpCode);
            } else {
                thpsUser.setCorpcode("ZZSFYBJY");
            }
            // 权限系统同步
            systemUserFeignService.saveOrUpdate(thpsUser);

            String message = JSON.toJSONString(rightService.roleUserSave(h.getEmployeeId(), "00E8EFF2705D46A18D4251C5961744F6", "", ""));
            System.out.println("=============" + message + "===============");


            try {
                EmployeeReq employeeReq = new EmployeeReq();
                BeanUtil.copyProperties(h, employeeReq);
                employeeReq.setSsoOrgCode(corpCode);
                System.out.println(oAEmployeeFeignService.insertOrUpdate(employeeReq) + "-------------------");

            } catch (Exception ex) {
                log.error(JSON.toJSONString(h), ex);
                throw ex;
            }


        }
        return PlatformResult.success();

    }


    @ApiOperation(value = "同步员工", notes = "同步员工")
    @RequestMapping(value = "/employee/initPassword", method = {RequestMethod.POST, RequestMethod.GET})
    public  PlatformResult initPassword() {
        List<HrmsEmployeeResp> hrmsEmployeeRespList = hrmsEmployeeService.getEmployeeList(new EmployeeListReq());
        for (HrmsEmployeeResp h : hrmsEmployeeRespList) {
            ThpsUserReq thpsUser = new ThpsUserReq();
            thpsUser.setId(h.getEmployeeId());
            thpsUser.setUsercode(h.getEmployeeNo());
            try {
                thpsUser.setPassword(PasswordHash.createHash("111111"));
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            } catch (InvalidKeySpecException e) {
                e.printStackTrace();
            }
            systemUserFeignService.saveOrUpdate(thpsUser);
        }
        return PlatformResult.success();
    }


    @ApiOperation(value = "重置密码", notes = "重置密码")
    @RequestMapping(value = "/employee/resetPassword", method = {RequestMethod.POST})
    public PlatformResult resetPassword(@RequestBody ResetPasswordReq resetPasswordReq) throws NoSuchAlgorithmException, InvalidKeySpecException {
    	
    	String orgRang =UserInfoHolder.getCurrentUserInfo().getSysRoleCode();
    	
    	if (UserLoginService.ISADMIN() == false  ) {
    		if(!orgRang.contains("SYS_ARCHIVIST")) {
    			  throw new BusinessException("无权限！");
    		}
        }

        HrmsEmployeeResp hrmsEmployeeResp = hrmsEmployeeService.findByEmployeeNo(resetPasswordReq.getEmpCode());
       
        List<Map<String, String>> multipleOrg = commLoginLogsService.getMultipleOrg(null,resetPasswordReq.getEmpCode());
        if(CollectionUtils.isNotEmpty(multipleOrg)){
        	for (Map<String, String> map : multipleOrg) {
    			systemUserFeignService.pwdReset(map.get("employee_id"),resetPasswordReq.getPassWord());
			}
        }else{
        	 systemUserFeignService.pwdReset(hrmsEmployeeResp.getEmployeeId(),resetPasswordReq.getPassWord());
        }
       
        
        //更新登录记录表
    	CommLoginLogs record = new CommLoginLogs();
    	record.setUserCode(hrmsEmployeeResp.getEmployeeNo());
    	record.setRectificationDate(new Date());
    	record.setRectificationType("2");
    	//弱密码规则校验  至少8个字符，至少1个字母，1个数字和1个特殊字符
    	//String regex = "^(?=.*[a-z])(?=.*\\d)(?=.*[@#.~$!%*^?&_+-])[A-Za-z\\d@#.~$!%*^?&_+-]{8,50}$";
    	String regex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";
        if(!resetPasswordReq.getPassWord().matches(regex)){
            record.setPasswordType("2");
            record.setRectification("0");
        }else{
        	record.setPasswordType("1");
        	record.setRectification("1");
        }
        
        
		commLoginLogsService.updateByUserCode(record);
        
        
        if(StringUtils.isNotBlank(changePwdByUserCodePlatformUrl)) {
        	Map<String,String> requestJson = new HashMap<>();
            requestJson.put("userCode", hrmsEmployeeResp.getEmployeeNo());
            String newpassword = new String(Base64.encode(resetPasswordReq.getPassWord().trim().getBytes()));
            requestJson.put("password", newpassword);
            String result2 = HttpRequest.post(changePwdByUserCodePlatformUrl)
            .body(JSON.toJSONString(requestJson))
            .execute().body();
            
            log.info("密码统计集成平台返回的数据：" + result2);
        }
        
        return PlatformResult.success();
    }
    
    @ApiOperation(value = "重置工号", notes = "重置工号")
    @RequestMapping(value = "/employee/resetJobNumber", method = {RequestMethod.POST})
    public PlatformResult resetJobNumber(@RequestBody ResetJobNumberReq resetJobNumberReq) throws NoSuchAlgorithmException, InvalidKeySpecException {

       /* if (UserLoginService.ISADMIN() == false) {
            throw new BusinessException("无权限！");
        }*/
    	String msg = "操作失败";
        try {
        	 hrmsEmployeeService.updateJobNumber(resetJobNumberReq);
        	  return PlatformResult.success();
        } catch (Exception e) {
        	msg = e.getMessage();
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure(msg);
        
    }


    //查询科室人员树数据
    @ApiOperation(value = "查询员工科室机构树", notes = "查询员工科室机构树")
    @GetMapping(value = "/employee/getTreeOrgAndEmp")
    public PlatformResult<String> getTreeOrgAndEmp() {
        try {
            return PlatformResult.success(hrmsEmployeeService.getTreeOrgAndEmp());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    //修改个人用户信息
    @ApiOperation(value = "修改个人用户信息", notes = "修改个人用户信息")
    @PostMapping(value = "/employee/updateMyUser")
    public PlatformResult<String> updateMyUser(@RequestBody HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        try {

            if (!StringUtils.isBlank(hrmsEmployeeSaveReq.getEmpPhone())) {
                hrmsEmployeeSaveReq.setPhoneNumber(hrmsEmployeeSaveReq.getEmpPhone());
            }

            if (!StringUtils.isBlank(hrmsEmployeeSaveReq.getEmpEmail())) {
                hrmsEmployeeSaveReq.setEmail(hrmsEmployeeSaveReq.getEmpEmail());
            }


            //hrmsEmployeeService.updateMyUser(hrmsEmployeeSaveReq);
            
            CustomEmployeeInfo customEmployeeInfo = new CustomEmployeeInfo();
            
            BeanUtil.copyProperties(hrmsEmployeeSaveReq, customEmployeeInfo);
            
            customEmployeeInfo.setEmployeeNo(UserInfoHolder.getCurrentUserCode());
            customEmployeeInfo.setEmployeeId(UserInfoHolder.getCurrentUserId());
            
            customEmployeeInfoService.updateUserConfig(customEmployeeInfo);
            
            return PlatformResult.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }


    /**
     * @description: 新增员工个人信息
     * @param: hrmsEmployeeSaveReq
     * @return: java.lang.Integer
     * @author: liyuan
     * @createTime: 2021/6/23 10:50
     */
    @ApiOperation(value = "新增个人用户信息", notes = "新增个人用户信息")
    @PostMapping(value = "/employee/addEmployee")
    public PlatformResult<HrmsEmployeeResp> addEmployee(@RequestBody HrmsEmployeeSaveReq hrmsEmployeeSaveReq) {
        return hrmsEmployeeService.addEmployee(hrmsEmployeeSaveReq);
    }


    @ApiOperation(value = "启用禁用", notes = "启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String")
            , @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
    })
    @RequestMapping(value = "/employee/enable", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult enable(@RequestParam("id") String id, @RequestParam("enable") String enable) {
        hrmsEmployeeService.enable(id, enable);
        return PlatformResult.success();
    }

    /**
     * @Title: getEmployeeListByEqualPay
     * @Description: 同工同酬选择人员列表
     * @Params: @param page
     * @Params: @param record
     * @Params: @return
     * @Return: DataSet<HrmsEmployeeResp>
     * <AUTHOR>
     * @date:2021年9月14日
     * @Throws
     */
    @ApiOperation(value = "同工同酬选择人员列表", notes = "同工同酬选择人员列表")
    @PostMapping(value = "/employee/getEmployeeListByEqualPay")
    public DataSet<HrmsEmployeeResp> getEmployeeListByEqualPay(Page page, HrmsEmployee record) {
        List<HrmsEmployeeResp> list = hrmsEmployeeService.getEmployeeListByEqualPay(page, record);
        return new DataSet<HrmsEmployeeResp>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }


    @GetMapping(value = "/employee/download/template/importEmployee")
    @ApiOperation(value = "下载员工模板导入模板", notes = "下载员工模板导入模板")
    public void importEmployee(HttpServletResponse response) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "员工导入模板.xlsx";
            String template = "template/importEmployee.xlsx";
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入", notes = "导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "type 1:增量 2全量", required = true, dataType = "String")
    })
    @PostMapping(value = "/import")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file) {
        List<EmployeeImport> employeeImportList = (List<EmployeeImport>) ImportExcelUtil.getExcelDatas(file, EmployeeImport.class);
        hrmsEmployeeService.importEmployee(employeeImportList);
        return PlatformResult.success();
    }


    /**
     * @description: 获取员工选择器列表
     * @param: record
     * @return: java.util.List<cn.trasen.basicsbottom.bean.HrmsEmployeeResp>
     * @author: liyuan
     * @createTime: 2021/6/23 14:41
     */
    @PostMapping(value = "/employee/pageSelect")
    @ApiOperation(value = "分页获取人员信息（选择人）", notes = "分页获取人员信息（选择人）")
    public DataSet<EmployeeSelectListRes> pageSelect(Page page, @RequestBody EmployeeSelectListReq employeeSelectListReq) {
        EmployeeListInBO employeeListInBO = new EmployeeListInBO();
        employeeListInBO.setOrgId(employeeSelectListReq.getOrgId());
        employeeListInBO.setLikeAllName(employeeSelectListReq.getLikeAllName());
        List<EmployeeListOutBO> employeeListOutBOList = hrmsEmployeeService.pageSelect(page, employeeListInBO);
        List<EmployeeSelectListRes> employeeSelectListResList = new ArrayList<>();
        employeeListOutBOList.forEach(emp -> {
            EmployeeSelectListRes employeeSelectListRes = new EmployeeSelectListRes();
            employeeSelectListRes.setEmpId(emp.getEmployeeId());
            employeeSelectListRes.setEmpCode(emp.getEmployeeNo());
            employeeSelectListRes.setEmpName(emp.getEmployeeName());
            employeeSelectListRes.setGender(emp.getGender());
            employeeSelectListRes.setGenderText(emp.getGenderText());
            employeeSelectListRes.setIsEnable(emp.getIsEnable());
            employeeSelectListRes.setEnableText(emp.getEnableText());
            employeeSelectListRes.setOrgId(emp.getOrgId());
            employeeSelectListRes.setOrgName(emp.getOrgName());
            employeeSelectListRes.setEmpMobile(emp.getPhoneNumber());
            employeeSelectListRes.setAvatar(emp.getAvatar());
            employeeSelectListResList.add(employeeSelectListRes);
        });
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), employeeSelectListResList);
    }
    
    @ApiOperation(value = "同步平台用户信息", notes = "同步平台用户信息")
    @PostMapping(value = "/employee/syncEmployeeByPlatform")
    public PlatformResult<String> syncEmployeeByPlatform(@RequestBody PlatformBody platformBody) {
    	
    	try {
    		
	    	Map<String, Object> bodyData = platformBody.getBody();
	    	
	    	log.info("集成平台获取到用户的body：" + bodyData);
	    	
	    	UserLoginService.loginContext("admin");
	    	
	    	//Map<String, Object> bodyData = (Map<String, Object>) body.get("body");
	    	
	    	//JSONObject jsonObject =	JSONObject.parseObject(body);
	    	
	    	log.info("============================" + (String) bodyData.get("code"));
	    	
	    	HrmsEmployeeResp hrmsEmployeeResp = hrmsEmployeeService.findByEmployeeNo((String) bodyData.get("code"));
	    	//先判断获取数据字典，平台机构类型，1-单机构，2-紧密型，平台院区对应OA的机构，3-松散型，平台机构对应OA的机构，默认按单机构处理
		    String platformOrgType = "1";
		    List<DictItem> itemList = dictItemService.getDictItemByTypeCode("PLATFORM_ORG_TYPE", "*PUBLIC*");
		    if(CollUtil.isNotEmpty(itemList)){
		    	for(DictItem item : itemList){
		    		if(item.getItemCode().equals("ORG_TYPE")){
		    			platformOrgType = item.getItemNameValue();
		    		}
		    	}
		    }
		    
		    String orgCode = (String) bodyData.get("orgCode");//机构编码
		    String orgName = (String) bodyData.get("orgName");//机构名称
	    	String hospCode = (String) bodyData.get("hospCode");//院区编码
	    	String hospName = (String) bodyData.get("hospName");//院区名称
	    	if(null != hrmsEmployeeResp) {
	    		CustomEmployeeBase hrmsEmployeeSaveReq = new CustomEmployeeBase();
	    		if(platformOrgType.equals("2")){ //紧密型(益阳三)，获取院区编码当做机构编码
	    			hrmsEmployeeSaveReq.setSsoOrgCode(hospCode);
	    			hrmsEmployeeSaveReq.setSsoOrgName(hospName);
	    		} else {
	    			hrmsEmployeeSaveReq.setSsoOrgCode(orgCode);
	    			hrmsEmployeeSaveReq.setSsoOrgName(orgCode);
	    		}
	    		hrmsEmployeeSaveReq.setEmployeeId((String) bodyData.get("memberId"));
	    		hrmsEmployeeSaveReq.setEmployeeNo((String)bodyData.get("code"));
	    		hrmsEmployeeSaveReq.setEmployeeName((String)bodyData.get("name"));
	    		hrmsEmployeeSaveReq.setOrgId((String)bodyData.get("office"));
	    		hrmsEmployeeSaveReq.setOrgName((String)bodyData.get("officeName"));
	    		hrmsEmployeeSaveReq.setOpenId((String)bodyData.get("code"));
	    		if("1".equals(bodyData.get("sex"))){
	    			hrmsEmployeeSaveReq.setGender("0");
	    		}
				if("2".equals(bodyData.get("sex"))) {
					hrmsEmployeeSaveReq.setGender("1");
				}
				if(StringUtils.isNotBlank((String)bodyData.get("birthday"))) {
					hrmsEmployeeSaveReq.setBirthday(DateUtil.parse((String)bodyData.get("birthday"), "yyyy-MM-dd"));
				}
				hrmsEmployeeSaveReq.setIdentityNumber((String)bodyData.get("idCard"));
				hrmsEmployeeSaveReq.setPhoneNumber((String)bodyData.get("phone"));
				hrmsEmployeeSaveReq.setEmployeeStatus("1");
				hrmsEmployeeSaveReq.setIsEnable((String)bodyData.get("status"));
				hrmsEmployeeSaveReq.setEmpPayroll((String)bodyData.get("code"));
				
//				updateEmployee(hrmsEmployeeSaveReq);
				log.info("更新用户信息customEmployeeBaseService.update(hrmsEmployeeSaveReq)--------------------------");
				customEmployeeBaseService.update(hrmsEmployeeSaveReq);
	    	}else {
	    		CustomEmployeeInfo hrmsEmployeeSaveReq = new CustomEmployeeInfo();
	    		if(platformOrgType.equals("2")){ //紧密型(益阳三)，获取院区编码当做机构编码
	    			hrmsEmployeeSaveReq.setSsoOrgCode(hospCode);
	    			hrmsEmployeeSaveReq.setSsoOrgName(hospName);
	    		} else {
	    			hrmsEmployeeSaveReq.setSsoOrgCode(orgCode);
	    			hrmsEmployeeSaveReq.setSsoOrgName(orgCode);
	    		}
	    		hrmsEmployeeSaveReq.setEmployeeId((String) bodyData.get("memberId"));
	    		hrmsEmployeeSaveReq.setEmployeeNo((String)bodyData.get("code"));
	    		hrmsEmployeeSaveReq.setEmployeeName((String)bodyData.get("name"));
	    		hrmsEmployeeSaveReq.setOrgId((String)bodyData.get("office"));
	    		hrmsEmployeeSaveReq.setOrgName((String)bodyData.get("officeName"));
	    		hrmsEmployeeSaveReq.setOpenId((String)bodyData.get("code"));
	    		if("1".equals(bodyData.get("sex"))){
	    			hrmsEmployeeSaveReq.setGender("0");
	    		}
				if("2".equals(bodyData.get("sex"))) {
					hrmsEmployeeSaveReq.setGender("1");
				}
				if(StringUtils.isNotBlank((String)bodyData.get("birthday"))) {
					hrmsEmployeeSaveReq.setBirthday(DateUtil.parse((String)bodyData.get("birthday"), "yyyy-MM-dd"));
				}
				hrmsEmployeeSaveReq.setIdentityNumber((String)bodyData.get("idCard"));
				hrmsEmployeeSaveReq.setPhoneNumber((String)bodyData.get("phone"));
				hrmsEmployeeSaveReq.setEmployeeStatus("1");
				hrmsEmployeeSaveReq.setIsEnable((String)bodyData.get("status"));
				hrmsEmployeeSaveReq.setEmpPayroll((String)bodyData.get("code"));
	    		
	    		//hrmsEmployeeService.addEmployee(hrmsEmployeeSaveReq);
	    		
	    		customEmployeeBaseService.add(hrmsEmployeeSaveReq);
	    	}
	    	return PlatformResult.success();
    	}catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("同步人员数据失败,失败原因：" + e.getMessage());
        }
    }
}
