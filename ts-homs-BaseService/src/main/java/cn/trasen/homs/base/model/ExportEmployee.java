package cn.trasen.homs.base.model;


import javax.persistence.Column;

import cn.trasen.BootComm.excel.ExportFieldsAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
* @ClassName: ExportEmployee
* @Description: 导出时查询字段 和映射需要导出的字段(页面选中的导出属性 格式为:{code:'code',name:;'name'}) 都是string类型 ,此对象仅作为获取导出字段使用
* <AUTHOR>
* @date 2018年4月25日 上午9:26:21
*
 */
@Getter
@Setter
public class ExportEmployee {
	
	@ExportFieldsAnnotation(value="员工工号")
    @ApiModelProperty(value = "员工工号")
    private String employee_no;

    @ExportFieldsAnnotation(value="员工姓名")
    private String employee_name;

    @ExportFieldsAnnotation(value="组织机构")
    private String orgName;

    @ExportFieldsAnnotation(value="编制类型")
    private String establishment_type;



    @ExportFieldsAnnotation(value="性别")
    private String gender;

    @ExportFieldsAnnotation(value="身份证号")
    private String identity_number;


    @ExportFieldsAnnotation(value="年龄")
    private String emp_age;

    @ExportFieldsAnnotation(value="出生日期")
    private String birthday;

    @ExportFieldsAnnotation(value="参加工作时间")
    private String work_start_date;

    @ExportFieldsAnnotation(value="入院时间")
    private String entry_date;

    @ExportFieldsAnnotation(value="政治面貌")
    private String political_status;

    @ExportFieldsAnnotation(value="入党时间")
    private String party_date;

    
    
    @ExportFieldsAnnotation(value="民族")
    private String nationality;

    @ExportFieldsAnnotation(value="籍贯")
    private String birthplace;

    @ExportFieldsAnnotation(value="最高学历")
    private String education_type_name;

    @ExportFieldsAnnotation(value="学校")
    private String school_name;

    @ExportFieldsAnnotation(value="专业")
    private String professional;

    @ExportFieldsAnnotation(value="毕业时间")
    private String edEndTime;

    @ExportFieldsAnnotation(value="最高职称")
    private String jobtitleCategoryName;

    @ExportFieldsAnnotation(value="职称名称")
    private String jobtitleName;

    @ExportFieldsAnnotation(value="获得时间")
    private String assessment_date;

    @ExportFieldsAnnotation(value="职务")
    private String positionName;

    @ExportFieldsAnnotation(value="任职时间")
    private String job_deion_type_time;

    @ExportFieldsAnnotation(value="是否专业英才")
    private String zhuanyeyingcai;

    @ExportFieldsAnnotation(value="是否中层干部")
    private String shifouzhongcengganbu;

    @ExportFieldsAnnotation(value="是否杏林人才")
    private String shifouxinglinrencai;

    @ExportFieldsAnnotation(value="婚姻状况")
    private String marriage_status;

    @ExportFieldsAnnotation(value="本单位连续工龄")
    private String year_work;

    @ExportFieldsAnnotation(value="连续工龄")
    private String bdwlxgl;

    @ExportFieldsAnnotation(value="岗位名称")
    private String plgw;

    @ExportFieldsAnnotation(value="岗位等级")
    private String postName;

    @ExportFieldsAnnotation(value="执业类别")
    private String operation_type;

    @ExportFieldsAnnotation(value="执业范围")
    private String operation_scope;

    @ExportFieldsAnnotation(value="岗位类别")
    private String personal_identity;

    @ExportFieldsAnnotation(value="是否规培")
    private String shifouguipeirenyuan;

    @ExportFieldsAnnotation(value="出生地")
    private String born_address;

    @ExportFieldsAnnotation(value="户籍")
    private String residence_address;

    @ExportFieldsAnnotation(value="现居住地")
    private String address;

    @ExportFieldsAnnotation(value="档案归属地")
    private String danganguishu;

    @ExportFieldsAnnotation(value="手机号码")
    private String phone_number;

    @ExportFieldsAnnotation(value="员工状态")
    private String employee_status;
    
    /**
     * 编制类别
     */
    @ExportFieldsAnnotation(value="编制类别")
    private String employee_category;
    
    /**
     * 执业证编号
     */
    @ExportFieldsAnnotation(value="执业证编号")
    private String operation_number;

}
