package cn.trasen.homs.base.customEmployee.service.impl;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.IdcardUtil;
import cn.trasen.homs.feign.base.HrmsAnnualLeaveSettingFeignService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.x.protobuf.MysqlxDatatypes.Array;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.BootComm.utils.IDCardUtil;
import cn.trasen.homs.base.bean.CustomEmployeeResp;
import cn.trasen.homs.base.bean.EmployeeImport;
import cn.trasen.homs.base.bean.HrmsEducation;
import cn.trasen.homs.base.bean.HrmsFamilyInfo;
import cn.trasen.homs.base.bean.HrmsWorkRecord;
import cn.trasen.homs.base.contants.CommonContants;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeBaseMapper;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeFieldMapper;
import cn.trasen.homs.base.customEmployee.model.CommContact;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeDetailsModel;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeField;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeGroup;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeInfo;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeModel;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateDetail;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateOperation;
import cn.trasen.homs.base.customEmployee.model.WxCpUser;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeAuthorityService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeBaseService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeFieldService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeGroupService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeInfoService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeLeaderService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeUpdateDetailService;
import cn.trasen.homs.base.customEmployee.service.CustomEmployeeUpdateOperationService;
import cn.trasen.homs.base.customEmployee.utils.Hl7v3Utils;
import cn.trasen.homs.base.customEmployee.utils.SerialNumberGeneratorUtils;
import cn.trasen.homs.base.customEmployee.utils.SqlGenerationUtils;
import cn.trasen.homs.base.dao.CommCityMapper;
import cn.trasen.homs.base.dao.CommEmployeeFieldGroupMapper;
import cn.trasen.homs.base.model.CommCity;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.model.HrmsPersonnelTransaction;
import cn.trasen.homs.base.saasOrg.model.EmployeeOrgMap;
import cn.trasen.homs.base.saasOrg.service.EmployeeOrgMapService;
import cn.trasen.homs.base.service.GlobalSettingsService;
import cn.trasen.homs.base.service.HrmsOrganizationService;
import cn.trasen.homs.base.service.HrmsPersonnelTransactionService;
import cn.trasen.homs.base.service.IDictItemService;
import cn.trasen.homs.base.utils.DateUtils;
import cn.trasen.homs.base.utils.JsonUtil;
import cn.trasen.homs.bean.base.HrmsAdvancementIncidentReq;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.hrms.HrmsIncidentFeignService;
import cn.trasen.homs.feign.sso.RightFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import lombok.extern.log4j.Log4j2;
import me.chanjar.weixin.common.exception.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.WxCpInMemoryConfigStorage;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

/**
 * @ClassName CustomEmployeeBaseServiceImpl
 * @Description TODO
 * @date 2024��9��10�� ����6:54:06
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Log4j2
public class CustomEmployeeBaseServiceImpl implements CustomEmployeeBaseService {

	@Resource
	private CustomEmployeeBaseMapper mapper;
	
	@Autowired
	private CustomEmployeeInfoService customEmployeeInfoService;
	
	@Autowired
	private HrmsOrganizationService hrmsOrganizationService;
	
	@Autowired
	private CustomEmployeeLeaderService customEmployeeLeaderService;
	
	@Autowired
	private CustomEmployeeFieldService customEmployeeFieldService;
	
	@Autowired
	private CustomEmployeeGroupService customEmployeeGroupService;
	
	@Autowired
	private GlobalSettingsService globalSettingsService;
	
	@Autowired
	private IDictItemService dictItemService;
	
	@Autowired
	private CommCityMapper commCityMapper;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;
	
	@Autowired
	private RightFeignService rightService;
	
	@Autowired
	private HrmsIncidentFeignService incidentFeignService;
	
	@Autowired
	private WorkflowInstanceService workflowInstanceService;
	
	@Autowired
	private CustomEmployeeUpdateOperationService customEmployeeUpdateOperationService;
	
	@Autowired
	private CustomEmployeeUpdateDetailService customEmployeeUpdateDetailService;
	
	@Autowired
	private HrmsPersonnelTransactionService hrmsPersonnelTransactionService;
	
	@Autowired
	private CustomEmployeeAuthorityService customEmployeeAuthorityService;
	
	@Autowired
	private WorkflowTaskService workflowTaskService;
	
	@Autowired
    private CommEmployeeFieldGroupMapper commEmployeeFieldGroupMapper;
	
	@Autowired
	private CustomEmployeeFieldMapper customEmployeeFieldMapper;
	
	@Autowired
	private EmployeeOrgMapService employeeOrgMapService;

	@Autowired
	private HrmsAnnualLeaveSettingFeignService hrmsAnnualLeaveSettingFeignService;
	
	@Autowired
    private JdbcTemplate jdbcTemplate;
	
	@Value("${syncWeixinSwitch:0}")
    private String syncWeixinSwitch;
	
	@Value("${weixinDepartment:}")
    private String weixinDepartment;
	
	@Value("${syncWeixinSwitchSecret:}")
	private String syncWeixinSwitchSecret;
	
	@Value("${wechat.cp.corpId:}")
	private String corpId;
	
	@Value("${roadGateUrl:}")
    private String roadGateUrl;
	
	@Transactional(readOnly = false)
	@Override
	public String save(CustomEmployeeModel record) {
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		String employeeId = "";
		String archivesType = null;
		if (StringUtils.isBlank(record.getEmployeeId())) { // 新增

			employeeId = insert(record.getCustomFileds());

			if(CollUtil.isNotEmpty(record.getCustomFileds())) {
				List<CustomEmployeeField> list = record.getCustomFileds().get(0).getFields().stream().filter(vo -> "archives_type".equals(vo.getFieldName())).collect(Collectors.toList());
				if(CollUtil.isNotEmpty(list)){
					archivesType = list.get(0).getValue();
				}
			}
			//同步用户数据
			syncUserInfo(employeeId,"insert",archivesType, null, null);
			//异步生成员工年假
			String finalEmployeeId = employeeId;
			new Thread(() -> {
				hrmsAnnualLeaveSettingFeignService.createEmployeeAnnualLeaveByYear(finalEmployeeId, user.getToken());
			}).start();
		} else { // 修改
			log.error("----------更新员工档案开始,params:"+JSONObject.toJSONString(record));
			employeeId = record.getEmployeeId();
			//待修改列表
			CustomEmployeeInfo customEmployeeInfo = customEmployeeInfoService.findByEmployeeId(employeeId);
			
			List<CustomEmployeeUpdateDetail> emplist = checkNeedStartProcess(record,customEmployeeInfo);

			if(CollUtil.isNotEmpty(record.getCustomFileds())) {
				List<CustomEmployeeField> list = record.getCustomFileds().get(0).getFields().stream().filter(vo -> "archives_type".equals(vo.getFieldName())).collect(Collectors.toList());
				if(CollUtil.isNotEmpty(list)){
					archivesType = list.get(0).getValue();
				}
			}
			//同步用户数据
			syncUserInfo(employeeId, "update",archivesType, null, null);

			// 判断是否需要走流程
			if (CollectionUtils.isNotEmpty(emplist)) {
				sendBusiFlow(emplist, employeeId,customEmployeeInfo,record.getGroupId(),record.getArchivesType());
				log.error("----------生成员工档案审批流程:"+employeeId);
			}
			log.error("----------更新员工档案结束:"+employeeId);
			//异步生成员工年假
			new Thread(() -> {
				hrmsAnnualLeaveSettingFeignService.createEmployeeAnnualLeaveByYear(record.getEmployeeId(), user.getToken());
			}).start();
		}

		return employeeId;
		
	}
	
	private String insert(List<CustomEmployeeDetailsModel> records) {
        
        String employeeId = ApplicationUtils.GUID32();
        
        List<CustomEmployeeField> allEmployeeFields = new ArrayList<CustomEmployeeField>();
        
        for(CustomEmployeeDetailsModel customEmployee : records) {
        	
        	String groupId = customEmployee.getGroupId();
        	
        	//个人信息
        	if(CollectionUtils.isNotEmpty(customEmployee.getFields())) {
                
                allEmployeeFields.addAll(customEmployee.getFields());
        	}
        	
        	//明细表
        	List<List<CustomEmployeeField>> detailFields = customEmployee.getDetailFields();
        	
        	if(CollectionUtils.isNotEmpty(detailFields)) {//增加明细信息
        		
        		CustomEmployeeField commEmployeeField = new CustomEmployeeField();
        		
        		commEmployeeField.setFieldName("employee_id");
        		
        		commEmployeeField.setValue(employeeId);
        		
        		for(List<CustomEmployeeField> detailFieldList : detailFields) {
        			
        			if(CollectionUtils.isNotEmpty(detailFieldList)) {
        				
        				CustomEmployeeGroup commEmployeeFieldGroup  = customEmployeeGroupService.selectById(groupId);
        				
        				String tableName = commEmployeeFieldGroup.getTableName();
        				 
        				detailFieldList.add(commEmployeeField);
            			
            			String sql  = SqlGenerationUtils.insertEmployeeDetail(detailFieldList,tableName);
            			
            			if(StringUtils.isNotBlank(sql)) {
                            
            				customEmployeeGroupService.executeSql(sql);
                        }
        			}
        		}
        	}
        }
        
        if(CollectionUtils.isNotEmpty(allEmployeeFields)) { //个人信息
    		
    		List<CustomEmployeeField> baseField = new ArrayList<CustomEmployeeField>();
    		List<CustomEmployeeField> infoField = new ArrayList<CustomEmployeeField>();
    		
    		for (CustomEmployeeField customEmployeeField : allEmployeeFields) {
    			//校验工号是否自动生成，如果是则按照流水号规则，信息是否必填
    			String fieldName = customEmployeeField.getFieldName();
    			if(!ObjectUtils.isEmpty(fieldName) && fieldName.equals("employee_no")){
    				updateInitialValue(customEmployeeField);
    			}
    			//排序字段-如果排序字段为空，则获取最大排序值+1
    			if(!ObjectUtils.isEmpty(fieldName) && fieldName.equals("emp_sort") && ObjectUtils.isEmpty(customEmployeeField.getValue())){
    				Example example = new Example(CustomEmployeeBase.class);
    		        example.and().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
    		        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
    		        example.orderBy(" empSort ").desc();
    		        List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);
    		        if(CollUtil.isNotEmpty(employeeList) && employeeList.get(0).getEmpSort() != null){
    		        	customEmployeeField.setValue(String.valueOf(employeeList.get(0).getEmpSort() + 1));
    		        } else {
    		        	customEmployeeField.setValue("1");
    		        }
    			}
				if("1".equals(customEmployeeField.getFieldClass())) { //基础表字段
					baseField.add(customEmployeeField);
				}else { //业务表字段
					infoField.add(customEmployeeField);
				}
			}
    		
    		String baseSql = SqlGenerationUtils.insertEmployee(baseField,"cust_emp_base",employeeId);
    		String infoSql = SqlGenerationUtils.insertEmployee(infoField,"cust_emp_info",employeeId);
    		
    		customEmployeeGroupService.executeSql(baseSql);
    		
    		customEmployeeGroupService.executeSql(infoSql);
    	}

        return employeeId;
    }
	
	public void syncUserInfo(String employeeId, String operateType,String archivesType, String ssoOrgCode, String ssoOrgName) {
		
		GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
		
		CustomEmployeeBase customEmployeeBase = mapper.selectByPrimaryKey(employeeId);
		
        ThpsUserReq thpsUser = new ThpsUserReq();
        thpsUser.setId(employeeId);
        thpsUser.setUsercode(customEmployeeBase.getEmployeeNo());
        
        if(!"hnsrmyy".equals(globalSetting.getOrgCode())){
        	thpsUser.setOldusercode(customEmployeeBase.getEmployeeNo());
        }
        
        thpsUser.setUsername(customEmployeeBase.getEmployeeName());
        
        if("0".equals(customEmployeeBase.getIsEnable())){
			thpsUser.setStatus(0);
		}else{
			thpsUser.setStatus(1);
		}
        
        if("4".equals(customEmployeeBase.getEmployeeStatus()) || "7".equals(customEmployeeBase.getEmployeeStatus())
				|| "8".equals(customEmployeeBase.getEmployeeStatus()))  {
        	thpsUser.setStatus(0);
        	ThpsUserReq user = new ThpsUserReq();
        	user.setId(employeeId);
        	user.setStatus(0);
        	systemUserFeignService.newDisable(user);
        	
        	customEmployeeBase.setIsEnable("0");
        	mapper.updateByPrimaryKeySelective(customEmployeeBase);
        }else {
        	thpsUser.setStatus(1);
        	ThpsUserReq user = new ThpsUserReq();
        	user.setId(employeeId);
        	user.setStatus(1);
        	systemUserFeignService.newDisable(user);
        	
        	customEmployeeBase.setIsEnable("1");
        	mapper.updateByPrimaryKeySelective(customEmployeeBase);
        }
        
        
        thpsUser.setMobileNo(customEmployeeBase.getPhoneNumber());
        thpsUser.setSex(customEmployeeBase.getGender());
        thpsUser.setDeptcode(customEmployeeBase.getOrgId());

        if ("insert".equals(operateType)) {

            String passWord = "111111";
            if (StringUtils.isNotBlank(globalSetting.getPasswordPreset())) {
                passWord = globalSetting.getPasswordPreset();
            } else {
                if (IDCardUtil.isIdentity(customEmployeeBase.getIdentityNumber())) {
                    passWord = customEmployeeBase.getIdentityNumber().substring(customEmployeeBase.getIdentityNumber().length() - 6);
                }
            }
            
            thpsUser.setPassword(passWord);
            thpsUser.setOldpassword(passWord);

        } else if ("delete".equals(operateType)) {
            thpsUser.setStatus(0);
        }
        if(ObjectUtil.isEmpty(ssoOrgCode)){
        	thpsUser.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
        } else {
        	thpsUser.setCorpcode(ssoOrgCode);
        }

        log.info("---同步用户：" + JSON.toJSONString(thpsUser));

        // 权限系统同步
        PlatformResult<String> saveOrUpdate = systemUserFeignService.saveOrUpdate(thpsUser);
        if(saveOrUpdate.isSuccess()){
            log.info("----同步用户返回信息" + saveOrUpdate.getMessage() + "---同步用户返回：" + saveOrUpdate.getObject());
        }
        
        /**
         * 同步用户数据到多机构映射表中，并且所属机构是默认的
         * <AUTHOR>
         * @update 2025-07-12 14:30:00
         */
        EmployeeOrgMap employeeOrgMap = new EmployeeOrgMap();
        employeeOrgMap.setEmployeeId(employeeId);
        if(ObjectUtil.isEmpty(ssoOrgCode)){
        	employeeOrgMap.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        	employeeOrgMap.setSsoOrgName(UserInfoHolder.getCurrentUserInfo().getOrgName());
        } else {
        	employeeOrgMap.setSsoOrgCode(ssoOrgCode);
        	employeeOrgMap.setSsoOrgName(ssoOrgName);
        }
        employeeOrgMap.setStatus(thpsUser.getStatus());
        employeeOrgMapService.saveOrUpdate(employeeOrgMap);
        
        //授权普通用户角色权限
        if ("insert".equals(operateType)) {
//        	 rightService.roleUserSave(employeeId, "00E8EFF2705D46A18D4251C5961744F6","", "");
			customEmployeeGroupService.insertSSOUSerRole(ApplicationUtils.GUID32(),employeeId, "00E8EFF2705D46A18D4251C5961744F6");
        	 if(StringUtils.isNotBlank(archivesType) && "yw".equalsIgnoreCase(archivesType)){
//				 rightService.roleUserSave(employeeId, "A4762094A41745FBBE6D418F2EA8AB0B","", "");
                 customEmployeeGroupService.insertSSOUSerRole(ApplicationUtils.GUID32(),employeeId, "A4762094A41745FBBE6D418F2EA8AB0B");
			 }
        }else if("update".equalsIgnoreCase(operateType) ){
			if(!"-1".equalsIgnoreCase(archivesType)) {
//				customEmployeeGroupService.insertSSOUSerRole(ApplicationUtils.GUID32(),employeeId, "00E8EFF2705D46A18D4251C5961744F6");
				//更新员工档案 ，如果当前用户不是医务人员则删除员工对应的医生角色
				customEmployeeGroupService.delSSOUSerRole(employeeId, "A4762094A41745FBBE6D418F2EA8AB0B");
			}
			if("yw".equalsIgnoreCase(archivesType)) {
				//更新员工档案 ，如果当前用户不是医务人员则删除员工对应的医生角色
//				rightService.roleUserSave(employeeId, "A4762094A41745FBBE6D418F2EA8AB0B","", "");
                customEmployeeGroupService.insertSSOUSerRole(ApplicationUtils.GUID32(),employeeId, "A4762094A41745FBBE" +
						"" +
						"" +
						"6D418F2EA8AB0B");
			}
		}
        
        //用户同步至企业微信
        if("1".equals(syncWeixinSwitch)) {
			syncWeixinUser(operateType, customEmployeeBase);
		}
        
        //北海市第二人民医院定制需求
        if("bhsdermyy".equals(globalSetting.getOrgCode())){
        	bhsdermyySync(customEmployeeBase,operateType);
        }
    }

	private void syncWeixinUser(String operateType, CustomEmployeeBase customEmployeeBase) {

		WxCpInMemoryConfigStorage configStorage = new WxCpInMemoryConfigStorage();
		configStorage.setCorpId(corpId);
		configStorage.setCorpSecret(syncWeixinSwitchSecret);
		
		WxCpServiceImpl wxCpService = new WxCpServiceImpl();
		wxCpService.setWxCpConfigStorage(configStorage);
		
		WxCpUser wxCpUser = new WxCpUser();
		wxCpUser.setUserid(customEmployeeBase.getEmployeeNo());
		wxCpUser.setName(customEmployeeBase.getEmployeeName());
		wxCpUser.setMobile(customEmployeeBase.getPhoneNumber());
		
		if(StringUtils.isNotBlank(weixinDepartment)) {
			Long[] department = {Long.valueOf(weixinDepartment)};
			wxCpUser.setDepartment(department);
		}else {
			Long[] department = {Long.valueOf(customEmployeeBase.getOrgId())};
			wxCpUser.setDepartment(department);
		}
		
		if(StringUtils.isNotBlank(customEmployeeBase.getGender()) && "0".equals(customEmployeeBase.getGender())) {
			wxCpUser.setGender("1");
		}else {
			wxCpUser.setGender("2");
		}
		
		wxCpUser.setEmail(customEmployeeBase.getEmail());
		
		if("2".equals(customEmployeeBase.getIsEnable()) || "4".equals(customEmployeeBase.getEmployeeStatus()) 
				|| "7".equals(customEmployeeBase.getEmployeeStatus()))  {
			wxCpUser.setEnable(0);
		}else {
			wxCpUser.setEnable(1);
		}
		try {
			if(Contants.IS_DELETED_TURE.equals(customEmployeeBase.getIsDeleted())) {
				String url = "https://qyapi.weixin.qq.com/cgi-bin/user/delete?userid=" + customEmployeeBase.getEmployeeNo();
			    String msg = wxCpService.get(url, null);
				log.info("删除企业微信用户返回数据：" + msg);
			}else {
				String url = "";
				if ("insert".equals(operateType)) {
					url = "https://qyapi.weixin.qq.com/cgi-bin/user/create";
				}
				if("update".equals(operateType)) {
					url = "https://qyapi.weixin.qq.com/cgi-bin/user/update";
				}
				String msg = wxCpService.post(url, JSON.toJSONString(wxCpUser));
				log.info("用户同步企业微信返回数据：" + msg);
			}
		} catch (WxErrorException e) {
			log.error("用户同步企业微信失败，失败原因：" + e.getMessage());
			e.printStackTrace();
		}
	}
	
	private void bhsdermyySync(CustomEmployeeBase customEmployeeBase,String operateType) {
		
		HrmsOrganization hrmsOrganization = hrmsOrganizationService.findById(customEmployeeBase.getOrgId());
		customEmployeeBase.setOrgName(hrmsOrganization.getName());
		
		//离职、死亡、退休员工数据推送给道闸系统
		if("4".equals(customEmployeeBase.getEmployeeStatus()) || "7".equals(customEmployeeBase.getEmployeeStatus())
				|| "8".equals(customEmployeeBase.getEmployeeStatus()))  {
			Map<String,Object> params = new HashMap<>();
			params.put("name", customEmployeeBase.getEmployeeName());
			params.put("idcard", customEmployeeBase.getIdentityNumber());
			params.put("userCode", customEmployeeBase.getEmployeeNo());
			params.put("userAccounts", customEmployeeBase.getEmployeeNo());
			if(StringUtils.isNotBlank(customEmployeeBase.getGender()) && "0".equals(customEmployeeBase.getGender())) {
				params.put("sex", 1);
			}else{
				params.put("sex", 2);
			}
			
			params.put("deptName", hrmsOrganization.getName());
			params.put("userType", "正式人员");
			params.put("outTime", DateUtil.format(new Date(), "yyyy-MM-dd"));
			
			String requestParams = JSON.toJSONString(params);
			log.info("道闸接口请求的数据：" + requestParams);
			
			String resultJson = HttpUtil.post("http://192.168.100.100:8888/ts-platform-work/http/OA/LZ", requestParams);
			
			log.info("道闸接口返回的数据：" + resultJson);
		}
		
		//集成平台员工信息同步接口
		Map<String,String> responseMap = new HashMap<>();
     	responseMap.put("id", customEmployeeBase.getEmployeeId());
     	responseMap.put("usercode", customEmployeeBase.getEmployeeNo());
     	responseMap.put("username", customEmployeeBase.getEmployeeName());
     	responseMap.put("status",customEmployeeBase.getEmployeeStatus());
     	responseMap.put("mobile_no", customEmployeeBase.getPhoneNumber());
     	responseMap.put("cardno", customEmployeeBase.getIdentityNumber());
     	responseMap.put("appuser", customEmployeeBase.getEmployeeNo());
     	responseMap.put("sex",customEmployeeBase.getGender());
     	responseMap.put("role_id","13B74043E7B3402092033BE2B4FB66D3");
     	
     	String resulst = HttpUtil.post("http://192.168.100.100:8888/ts-platform-work/http/jcpt/oa/user_oa",
     			JSONObject.toJSONString(responseMap));
     	log.info("调用集成平台人员更新接口返回数据:" + resulst);
		 
     	//平台Hl7v3数据标准化
		if("insert".equals(operateType)) {
			Hl7v3Utils.Hl7v3UserInfo("P5311",customEmployeeBase);
     	}else{
     		Hl7v3Utils.Hl7v3UserInfo("P5312",customEmployeeBase);
     	}
	}
	
	private List<CustomEmployeeUpdateDetail> checkNeedStartProcess(CustomEmployeeModel record,CustomEmployeeInfo customEmployeeInfo) {

		CustomEmployeeDetailsModel customEmployeeFieldModel = new CustomEmployeeDetailsModel();
		
		HrmsAdvancementIncidentReq entity = new HrmsAdvancementIncidentReq();

		customEmployeeFieldModel.setUserCode(UserInfoHolder.getCurrentUserCode());

		String employeeId = record.getEmployeeId();

		customEmployeeFieldModel.setEmployeeId(employeeId);

		Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
		Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
		boolean isInfoAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST");// 档案管理员
		boolean isYsAdmin = UserInfoHolder.getRight("SYS_ARCHIVIST_YS"); //医师档案管理

		//流程防重复校验
		Map<String, Object> empFlowMap = mapper.getEmpFlowStatus(employeeId, record.getArchivesType(), UserInfoHolder.getCurrentUserCorpCode());
		if(null != empFlowMap){
			Integer STATUS = (Integer) empFlowMap.get("STATUS");
			//不是人事系统管理员
			if(!((isInfoAdmin && StringUtils.isBlank(record.getArchivesType())
					|| (isYsAdmin && StringUtils.isNotBlank(record.getArchivesType()))))
					&& 1 == STATUS) {
				throw new BusinessException("流程已发起,请勿重复提交! 发起时间:"+MapUtil.getStr(empFlowMap,"createDate"));
			}
		}
		
		// 修改的数据列表
		List<CustomEmployeeUpdateDetail> operationList = new ArrayList<>();

		if (CollectionUtils.isNotEmpty(record.getCustomFileds())) {

			for (CustomEmployeeDetailsModel item : record.getCustomFileds()) {

				String groupId = item.getGroupId();

				CustomEmployeeGroup commEmployeeFieldGroup  = customEmployeeGroupService.selectGroupById(groupId);

				String tableName = commEmployeeFieldGroup.getTableName();

				customEmployeeFieldModel.setTableName(tableName);

				customEmployeeFieldModel.setGroupId(groupId);

				customEmployeeFieldModel.setGroupName(commEmployeeFieldGroup.getGroupName());
				
				// 个人信息
				if(CollectionUtils.isNotEmpty(item.getFields()) && "cust_emp_info".equals(commEmployeeFieldGroup.getTableName())) {
					
					List<CustomEmployeeField> fields = item.getFields();
					
					if (!isAdmin && !isALL && !((isInfoAdmin && StringUtils.isBlank(record.getArchivesType())
							|| (isYsAdmin && StringUtils.isNotBlank(record.getArchivesType()))))) {

						//查询需要走流程的字段
						List<CustomEmployeeField> processFields = customEmployeeAuthorityService.selectProcessFieldByGroupId(item.getGroupId(),tableName);
						
						if (CollectionUtils.isNotEmpty(processFields)) {

							List<Map<String, Object>> oldDetailList = getEmployeeInfoByNeedStartProcess(employeeId,processFields, "cust_emp_info left join cust_emp_base on employee_id = info_id");

							checkEmployeeInfoIsChange(oldDetailList, fields, customEmployeeFieldModel,processFields, operationList);

						} else {
							updateEmployeeInfo(fields, employeeId);
						}
					} else {
						updateEmployeeInfo(fields, employeeId);
					}

					fields.forEach(commEmployeeField -> {
						boolean b = (StringUtils.isNotEmpty(commEmployeeField.getValue())
								&& Objects.nonNull(customEmployeeInfo.getPlgw()));
						//调整岗位信息的异动明细保存
						//岗位类别
						if (Objects.equals(commEmployeeField.getFieldName(),"plgw")
								&& b
								&& !Objects.equals(commEmployeeField.getValue(),customEmployeeInfo.getPlgw())){
							entity.setNewPlgw(commEmployeeField.getValue());
							entity.setOldPlgw(customEmployeeInfo.getPlgw());
							entity.setType(1);
						}
						//岗位等级
						if (Objects.equals(commEmployeeField.getFieldName(),"gwdj")
								&& b
								&& !Objects.equals(commEmployeeField.getValue(),customEmployeeInfo.getGwdj())){
							entity.setNewGwdj(commEmployeeField.getValue());
							entity.setOldGwdj(customEmployeeInfo.getGwdj());
							entity.setType(1);
						}
						//薪级等级
						if (Objects.equals(commEmployeeField.getFieldName(),"salary_level_id")
								&& b
								&& !Objects.equals(commEmployeeField.getValue(),customEmployeeInfo.getSalaryLevelId())){
							entity.setNewSalaryLevelId(commEmployeeField.getValue());
							entity.setOldSalaryLevelId(customEmployeeInfo.getSalaryLevelId());
							entity.setType(1);
						}
						//薪级类别
						if (Objects.equals(commEmployeeField.getFieldName(),"salary_level_type")
								&& b
								&& !Objects.equals(commEmployeeField.getValue(),customEmployeeInfo.getSalaryLevelType())){
							entity.setNewSalaryLevelType(commEmployeeField.getValue());
							entity.setOldSalaryLevelType(customEmployeeInfo.getSalaryLevelType());
							entity.setType(1);
						}
						
					});
					entity.setApprovalStatus(4);
					entity.setEmployeeId(customEmployeeInfo.getEmployeeId());
					entity.setEmployeeName(customEmployeeInfo.getEmployeeName());
					entity.setEmployeeNo(customEmployeeInfo.getEmployeeNo());
					entity.setIsDeleted(Contants.IS_DELETED_FALSE);
					entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
					entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
					entity.setCreateDate(new Date());
					entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				}

				//其他分组
				if (CollectionUtils.isNotEmpty(item.getDetailFields())) {

					// 要修改的明细数据
					List<List<CustomEmployeeField>> detailFields = item.getDetailFields();

					if (!isAdmin && !isALL && !((isInfoAdmin && StringUtils.isBlank(record.getArchivesType())
							|| (isYsAdmin && StringUtils.isNotBlank(record.getArchivesType()))))) {// 不是管理员和档案管理员修改需要走流程

						// 查询需要走权限的分组
						//List<CustomEmployeeField> jurisdictionFields = customEmployeeFieldService.getAuthorityFieldByGroupId(groupId);
						
						//查询需要走流程的分组
						List<CustomEmployeeField> processFields = customEmployeeAuthorityService.selectProcessFieldByGroupId(item.getGroupId(),tableName);

						if (CollectionUtils.isEmpty(processFields)) { // 修改明细表,不需要走流程

							updateEmpDetail(employeeId, tableName, detailFields);

						} else {

							// 判断是否需要走流程,走流程前的保存修改前和修改后数据
							List<Map<String, Object>> oldDetailList = getEmployeeDetailInfoByShow(employeeId, groupId,tableName);

							checkDetailIsChange(oldDetailList, detailFields, customEmployeeFieldModel, operationList,commEmployeeFieldGroup);
							
						}

					} else {
						
						updateEmpDetail(employeeId, tableName, detailFields);
					}

				}else {
					
					updateEmpDetail(employeeId, tableName, item.getDetailFields());
				}
			}
			
			incidentFeignService.empSave(entity);
		}
		
		return operationList;
	}

	private void updateEmpDetail(String employeeId, String tableName, List<List<CustomEmployeeField>> detailFields) {
		
		if(!"cust_emp_info".equals(tableName)){ //是主表 不执行删除操作
			String deleteSql = SqlGenerationUtils.deleteEmployeeDetailByEmployeeId(employeeId, tableName);
			log.info("删除人员明细信息执行的sql：" + deleteSql);
			customEmployeeGroupService.executeSql(deleteSql);
		}
		
		if(CollectionUtils.isNotEmpty(detailFields)) {
			
			CustomEmployeeField commEmployeeField = new CustomEmployeeField();
			commEmployeeField.setFieldName("employee_id");
			commEmployeeField.setValue(employeeId);
			
			for(List<CustomEmployeeField> detailFieldList : detailFields) {
				
				if(CollectionUtils.isNotEmpty(detailFieldList)) {
					 
					detailFieldList.add(commEmployeeField);
					
					String sql  = SqlGenerationUtils.insertEmployeeDetail(detailFieldList,tableName);
					
					log.info("更新人员信息执行的sql：" + sql);
					
					customEmployeeGroupService.executeSql(sql);
					
				}
			}
		}
	}
	
	private List<Map<String, Object>> getEmployeeDetailInfoByShow(String employeeId, String groupId, String tableName) {

		CustomEmployeeField commEmployeeField = new CustomEmployeeField();

		commEmployeeField.setGroupId(groupId);

		commEmployeeField.setIsDisabled(0);

		List<CustomEmployeeField> fields = customEmployeeFieldService.getFieldsListByCondition(commEmployeeField);

		String sql = SqlGenerationUtils.getEmployeeDetailInfoByShow(employeeId, fields, tableName);
		
		log.info("查询人员明细表执行的sql：" + sql);
		
		List<Map<String, Object>> list = customEmployeeGroupService.querySql(sql);

		return list;
	}
	
	private boolean checkDetailIsChange(List<Map<String, Object>> oldDetailList,
			List<List<CustomEmployeeField>> detailFields, CustomEmployeeDetailsModel customEmployeeFieldModel,
			List<CustomEmployeeUpdateDetail> operationList, CustomEmployeeGroup commEmployeeFieldGroup) {

		boolean flag = false;

		List<Map<String, String>> newDetailList = new ArrayList<Map<String, String>>();

		for (List<CustomEmployeeField> detailField : detailFields) {

			Map<String, String> newDetailMap = new HashMap<>();

			for (CustomEmployeeField commEmployeeField : detailField) {

				if (StringUtils.isNotBlank(commEmployeeField.getValue()) && !"null".equals(commEmployeeField.getValue())) {

					newDetailMap.put(commEmployeeField.getFieldName(), commEmployeeField.getValue());

				}
			}

			newDetailList.add(newDetailMap);

		}

		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

		if (CollectionUtils.isNotEmpty(oldDetailList)) {

			for (Map<String, Object> dataMap : oldDetailList) {

				for (String key : dataMap.keySet()) {

					Object object = dataMap.get(key);

					boolean obgflag = ObjectUtils.allNotNull(object);

					if (null != object && obgflag) {

						if (object instanceof Date) {

							dataMap.put(key, sf.format(object));

						} else {

							dataMap.put(key, object);
						}
					}
				}
			}

		}

		String beforeData = JsonUtil.formatObject(oldDetailList);

		String afterData = JsonUtil.formatObject(newDetailList);

		// 先判断长度,如果长度不对,就表示已经修改过了
		if (oldDetailList.size() == detailFields.size()) {
			flag = JsonUtil.sameEmplpyeeMessage(beforeData, afterData);
		}

		if (flag) {
			
			updateEmpDetail(customEmployeeFieldModel.getEmployeeId(), customEmployeeFieldModel.getTableName(), detailFields);

		} else {

			ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
			// 记录修改的操作 ,前后数据
			CustomEmployeeUpdateDetail commEmployeeUpdateDetail = new CustomEmployeeUpdateDetail();

			commEmployeeUpdateDetail.setAfterData(afterData);

			commEmployeeUpdateDetail.setBeforeData(beforeData);

			commEmployeeUpdateDetail.setEmployeeId(customEmployeeFieldModel.getEmployeeId());

			commEmployeeUpdateDetail.setGroupId(customEmployeeFieldModel.getGroupId());

			commEmployeeUpdateDetail.setFieldName(customEmployeeFieldModel.getGroupName());

			commEmployeeUpdateDetail.setShowName(customEmployeeFieldModel.getGroupName());

			commEmployeeUpdateDetail.setTableName(customEmployeeFieldModel.getTableName());

			commEmployeeUpdateDetail.setUpdateType(2);

			commEmployeeUpdateDetail.setSsoOrgCode(userInfo.getCorpcode());

			operationList.add(commEmployeeUpdateDetail);
		}

		return flag;
	}
	
	private List<Map<String, Object>> getEmployeeInfoByNeedStartProcess(String employeeId,List<CustomEmployeeField> jurisdictionFields, String tabelName) {

		String sql = SqlGenerationUtils.getEmployeeDetailInfoByShow(employeeId, jurisdictionFields,tabelName);

		//List<Map<String, Object>> list = customEmployeeGroupService.querySql(sql);

		List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
		
		return list;
	}
	
	private boolean checkEmployeeInfoIsChange(List<Map<String, Object>> oldDetailList, List<CustomEmployeeField> fields,
			CustomEmployeeDetailsModel customEmployeeFieldModel, List<CustomEmployeeField> jurisdictionFields,
			List<CustomEmployeeUpdateDetail> operationList) {

		boolean flag = false;

		Map<String, Object> oldEmployeeInfoMap = oldDetailList.get(0);
		
		List<CustomEmployeeField> updateFields = new ArrayList<>();

		Map<String, String> fieldMap = new HashMap<>();

		for (CustomEmployeeField item : jurisdictionFields) {

			fieldMap.put(item.getFieldName(), item.getShowName());
		}

		for (CustomEmployeeField detailField : fields) {

			for (String fieldName : oldEmployeeInfoMap.keySet()) {

				String oldValue = oldEmployeeInfoMap.get(fieldName) != null
						? oldEmployeeInfoMap.get(fieldName).toString() : "";

				if (fieldName.equals(detailField.getFieldName())) {

					String newValue = detailField.getValue();

					if (!oldValue.equals(newValue)) {
						
						ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();

						CustomEmployeeUpdateDetail commEmployeeUpdateDetail = new CustomEmployeeUpdateDetail();

						commEmployeeUpdateDetail.setAfterData(newValue);

						commEmployeeUpdateDetail.setBeforeData(oldValue.toString());

						commEmployeeUpdateDetail.setEmployeeId(customEmployeeFieldModel.getEmployeeId());

						commEmployeeUpdateDetail.setGroupId(customEmployeeFieldModel.getGroupId());

						commEmployeeUpdateDetail.setFieldName(fieldName);

						commEmployeeUpdateDetail.setShowName(fieldMap.get(fieldName));

						commEmployeeUpdateDetail.setUpdateType(1);

						commEmployeeUpdateDetail.setSsoOrgCode(userInfo.getCorpcode());

						operationList.add(commEmployeeUpdateDetail);

						updateFields.add(detailField);

					}
				}
			}
		}

		fields.removeAll(updateFields);

		if (CollectionUtils.isNotEmpty(fields)) {
			
			updateEmployeeInfo(fields, customEmployeeFieldModel.getEmployeeId());
		}

		return flag;
	}

	private void updateEmployeeInfo(List<CustomEmployeeField> fields,String employeeId) {
		
		List<CustomEmployeeField> baseField = new ArrayList<CustomEmployeeField>();
		List<CustomEmployeeField> infoField = new ArrayList<CustomEmployeeField>();
		
		for (CustomEmployeeField customEmployeeField : fields) {
			
			if("1".equals(customEmployeeField.getFieldClass())) { //基础表字段
				baseField.add(customEmployeeField);
			}else { //业务表字段
				infoField.add(customEmployeeField);
			}
		}
		
		if(CollectionUtils.isNotEmpty(baseField)) {
			String baseSql = SqlGenerationUtils.updateEmployeeInfo(baseField, "cust_emp_base",employeeId);
			customEmployeeGroupService.executeSql(baseSql);
		}
		
		if(CollectionUtils.isNotEmpty(infoField)) {
			String infoSql = SqlGenerationUtils.updateEmployeeInfo(infoField, "cust_emp_info",employeeId);
			customEmployeeGroupService.executeSql(infoSql);
		}
		
	}
	
	private void sendBusiFlow(List<CustomEmployeeUpdateDetail> operationList, String employeeId,CustomEmployeeInfo customEmployeeInfo,String groupId,String archivesType) {
		
		if (CollectionUtils.isNotEmpty(operationList)) {
			//获取本次编辑的分组id
//			List<String> groupIdsList = operationList.stream().map(CustomEmployeeUpdateDetail::getGroupId).distinct().collect(Collectors.toList());
			//获取所有分组集合
			List<CustomEmployeeGroup>  groupList = customEmployeeGroupService.getGroupList(null);
			//根据编辑内容区分是否为人事档案分组还是医务档案分组编辑
			List<CustomEmployeeUpdateDetail> infoUpDetailList = new ArrayList<>(); //人事档案
			List<CustomEmployeeUpdateDetail> ysUpDetailList = new ArrayList<>();   //医务档案
			List<CustomEmployeeUpdateDetail> tmp = null;
			for(CustomEmployeeGroup group : groupList){
				tmp = operationList.stream().filter(vo-> vo.getGroupId().equals(group.getId())).distinct().collect(Collectors.toList());
				if(StringUtils.isNotBlank(group.getArchivesType())){
					ysUpDetailList.addAll(tmp);
				}else{
					infoUpDetailList.addAll(tmp);
				}
			}
			infoUpDetailList = infoUpDetailList.stream().distinct().collect(Collectors.toList());
			ysUpDetailList = ysUpDetailList.stream().distinct().collect(Collectors.toList());
            if(CollUtil.isNotEmpty(infoUpDetailList)) {
				String operationId = ApplicationUtils.GUID32();
				Map<String, Object> map = new HashMap<>();
				map.put("L_BusinessId", operationId); // 业务id
				map.put("L_personalIdentity", customEmployeeInfo.getPersonalIdentityName()); // 人员岗位名称 中文 审批流程判断使用的

				String workflowId = workflowInstanceService.doStartProcessInstance("L_00001", map); //发起人事档案编辑流程;
				if (StringUtils.isNotBlank(workflowId)) {
					insertUpdateOperation(infoUpDetailList, workflowId, operationId, employeeId, groupId);
				}
			}
			if(CollUtil.isNotEmpty(ysUpDetailList)){
				String operationId = ApplicationUtils.GUID32();
				Map<String, Object> map = new HashMap<>();
				map.put("L_BusinessId", operationId); // 业务id
				map.put("L_personalIdentity", customEmployeeInfo.getPersonalIdentityName()); // 人员岗位名称 中文 审批流程判断使用的

				String workflowId = workflowInstanceService.doStartProcessInstance("L_00002", map); //发起医务档案编辑流程;
				if (StringUtils.isNotBlank(workflowId)) {
					insertUpdateOperation(ysUpDetailList, workflowId, operationId, employeeId, groupId);
				}
			}
		}
	}
	
	private void insertUpdateOperation(List<CustomEmployeeUpdateDetail> operationList, String workflowId,String operationId, String employeeId,String groupId) {

		customEmployeeUpdateOperationService.deleteByGroupId(groupId,employeeId);
		
		CustomEmployeeUpdateOperation commEmployeeUpdateOperation = new CustomEmployeeUpdateOperation();

		commEmployeeUpdateOperation.setId(operationId);
		commEmployeeUpdateOperation.setWorkflowId(workflowId);
		commEmployeeUpdateOperation.setAuditStatus(1);
		commEmployeeUpdateOperation.setEmployeeId(employeeId);
		commEmployeeUpdateOperation.setWorkflowId(workflowId);
		commEmployeeUpdateOperation.setCreateUserDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		commEmployeeUpdateOperation.setCreateUserDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		commEmployeeUpdateOperation.setGroupId(groupId);		
		
		for (CustomEmployeeUpdateDetail record : operationList) {
			record.setOperationId(operationId);
			record.setWorkflowId(workflowId);
			customEmployeeUpdateDetailService.save(record);
		}
		
		if("all".equals(groupId)) {
			commEmployeeUpdateOperation.setOperationTitle("全部信息-" + UserInfoHolder.getCurrentUserName());
		}else {
			
			CustomEmployeeGroup customEmployeeGroup = customEmployeeGroupService.selectGroupById(groupId);
			
			commEmployeeUpdateOperation.setOperationTitle(customEmployeeGroup.getGroupName() + "-" + UserInfoHolder.getCurrentUserName());
		}
		
		
		customEmployeeUpdateOperationService.save(commEmployeeUpdateOperation);
	}
	
	@Transactional(readOnly = false)
	@Override
	public Integer update(CustomEmployeeBase record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//异步生成员工年假
		new Thread(() -> {
			hrmsAnnualLeaveSettingFeignService.createEmployeeAnnualLeaveByYear(record.getEmployeeId(),
					user.getToken());
		}).start();
		mapper.updateByPrimaryKeySelective(record);
		
		syncUserInfo(record.getEmployeeId(), "update","-1", null, null);
		
		return 1;
	}
	
	@Transactional(readOnly = false)
    public void add(CustomEmployeeInfo customEmployeeInfo) {
        if (StringUtils.isBlank(customEmployeeInfo.getEmployeeId())) {
        	customEmployeeInfo.setEmployeeId(String.valueOf(IdWork.id.nextId()));
        }
        customEmployeeInfo.setCreateDate(new Date());
        customEmployeeInfo.setUpdateDate(new Date());
        customEmployeeInfo.setCreateUser(UserInfoHolder.getCurrentUserId());
        customEmployeeInfo.setIsEnable(CommonContants.IS_ENABLE_TRUE);
        customEmployeeInfo.setIsDeleted(Contants.IS_DELETED_FALSE);
        if(StringUtils.isBlank(customEmployeeInfo.getEmployeeStatus())){
        	customEmployeeInfo.setEmployeeStatus("1");
        }
        if (StringUtils.isBlank(customEmployeeInfo.getEmpPayroll())) {
        	customEmployeeInfo.setEmpPayroll(customEmployeeInfo.getEmployeeNo());
        }

        Example example = new Example(CustomEmployeeBase.class);
        example.and().andEqualTo("employeeNo", customEmployeeInfo.getEmployeeNo());
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);
        
        if (employeeList.size() <= 0) {
        	if(ObjectUtils.isEmpty(customEmployeeInfo.getSsoOrgCode())){
        		customEmployeeInfo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        		customEmployeeInfo.setSsoOrgName(UserInfoHolder.getCurrentUserInfo().getOrgName());
        	}
        	mapper.insertSelective(customEmployeeInfo);
			ThpsUser userInfo = UserInfoHolder.getCurrentUserInfo();
			//异步生成员工年假
			new Thread(() -> {
				hrmsAnnualLeaveSettingFeignService.createEmployeeAnnualLeaveByYear(customEmployeeInfo.getEmployeeId(),
																					userInfo.getToken());
			}).start();
        	//根据需求导入时也需生成cust_emp_info数据
			customEmployeeInfoService.save(new CustomEmployeeInfo().setInfoId(customEmployeeInfo.getEmployeeId()));

        	 //同步
            syncUserInfo(customEmployeeInfo.getEmployeeId(),"insert",customEmployeeInfo.getArchivesType(), customEmployeeInfo.getSsoOrgCode(), customEmployeeInfo.getSsoOrgName());

            //招聘管理数据入职归档操作同步（经开医院需求）
            GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
            if("csjkyy".equals(globalSetting.getOrgCode())){
            	syncZpgl(customEmployeeInfo);
            }
        }
    }

	private void syncZpgl(CustomEmployeeInfo customEmployeeInfo) {
		Map<String, Object> dataMap = customEmployeeInfo.getDataMap();
        if(null != dataMap){
            StringBuffer sbSql = new StringBuffer();
            if(null != dataMap.get("hrms_child")){
                sbSql.append("update cust_emp_info set hrms_child='").append(dataMap.get("hrms_child")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("account_nature2")){
                sbSql.append("update cust_emp_info set account_nature2='").append(dataMap.get("account_nature2")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("dy_xueli")){
                sbSql.append("update cust_emp_info set dy_xueli='").append(dataMap.get("dy_xueli")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work1")){
                sbSql.append("update cust_emp_info set jkhrms_work1='").append(dataMap.get("jkhrms_work1")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work2")){
                sbSql.append("update cust_emp_info set jkhrms_work2='").append(dataMap.get("jkhrms_work2")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("jkhrms_work3")){
                sbSql.append("update cust_emp_info set jkhrms_work3='").append(dataMap.get("jkhrms_work3")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zhicheng")){
                sbSql.append("update cust_emp_info set zhicheng='").append(dataMap.get("zhicheng")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zhichengzhuangye")){
                sbSql.append("update cust_emp_info set zhichengzhuangye='").append(dataMap.get("zhichengzhuangye")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zcqudeshijian")){
                sbSql.append("update cust_emp_info set zcqudeshijian='").append(dataMap.get("zcqudeshijian")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            if(null != dataMap.get("zcqudedidian")){
                sbSql.append("update cust_emp_info set zcqudedidian='").append(dataMap.get("zcqudedidian")).append("' where employee_id='").append(customEmployeeInfo.getEmployeeId()).append("';");
            }
            
            log.info("执行的sql：" + sbSql.toString());
            
            customEmployeeGroupService.executeSql(sbSql.toString());
            
        }
        
        //学历信息
        if(CollectionUtils.isNotEmpty(customEmployeeInfo.getHrmsEducationList())){
            for (HrmsEducation hrmsEducation : customEmployeeInfo.getHrmsEducationList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_education_info (id,employee_id,start_time,end_time,school_name,education_type,professional,learn_way,highest_level,xlfj,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(customEmployeeInfo.getEmployeeId()).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsEducation.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsEducation.getEndTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(hrmsEducation.getSchoolName()).append("',");
                sbSql.append("'").append(hrmsEducation.getEducationType()).append("',");
                sbSql.append("'").append(hrmsEducation.getProfessional()).append("',");
                sbSql.append("'").append(hrmsEducation.getLearnWay()).append("',");
                sbSql.append("'").append(hrmsEducation.getHighestLevel()).append("',");
                sbSql.append("'").append(hrmsEducation.getXlfj()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                customEmployeeGroupService.executeSql(sbSql.toString());
            }
        }
        
        //院外工作经历
        if(CollectionUtils.isNotEmpty(customEmployeeInfo.getHrmsWorkRecordList())){
            for (HrmsWorkRecord hrmsWorkRecord : customEmployeeInfo.getHrmsWorkRecordList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_work_experience (id,employee_id,start_time,end_time,work_unit,dept_name,post,witness,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(customEmployeeInfo.getEmployeeId()).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsWorkRecord.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(DateUtil.format(hrmsWorkRecord.getStartTime(), "yyyy-MM-dd")).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getWorkUnit()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getDeptName()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getPost()).append("',");
                sbSql.append("'").append(hrmsWorkRecord.getWitness()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                customEmployeeGroupService.executeSql(sbSql.toString());
            }
        }
        
        //家庭关系
        if(CollectionUtils.isNotEmpty(customEmployeeInfo.getFamilyInfoList())){
            for (HrmsFamilyInfo hrmsFamilyInfo : customEmployeeInfo.getFamilyInfoList()) {
                StringBuffer sbSql = new StringBuffer();
                sbSql.append("insert into hrms_family_info (id,employee_id,member_name,relationship,work_unit,contact_number,is_deleted,create_date)");
                sbSql.append(" values('").append(String.valueOf(IdWork.id.nextId())).append("',");
                sbSql.append("'").append(customEmployeeInfo.getEmployeeId()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getMemberName()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getRelationship()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getWorkUnit()).append("',");
                sbSql.append("'").append(hrmsFamilyInfo.getContactNumber()).append("',");
                sbSql.append("'N',");
                sbSql.append("'").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("')");
                customEmployeeGroupService.executeSql(sbSql.toString());
            }
        }
        
        //插入人事异动记录
        HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction(customEmployeeInfo.getEmployeeNo(),
        		customEmployeeInfo.getEmployeeName(), customEmployeeInfo.getEmployeeId(), customEmployeeInfo.getOrgId(), customEmployeeInfo.getOrgName(), null,
                null, DateUtils.getStringDateShort(new Date()), "员工入职", "是", hrmsPersonnelTransactionService.getBatchNumber(),
                null, null, null,UserInfoHolder.getCurrentUserCorpCode());
        hrmsPersonnelTransactionService.insert(hpt);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CustomEmployeeBase record = new CustomEmployeeBase();
		record.setEmployeeId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		mapper.updateByPrimaryKeySelective(record);
		
		syncUserInfo(id,"delete",null, null, null);
		
		return 1;
	}

	@Override
	public DataSet<Map<String, String>> getDataSetList(Page page, CustomEmployeeInfo record) {
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		Boolean isAdmin = UserInfoHolder.ISADMIN();// 是否管理员
		Boolean isALL = UserInfoHolder.ISALL();// 是否所有权限
		boolean sysArchivist = UserInfoHolder.getRight("SYS_ARCHIVIST"); //员工档案管理员

		if (!isAdmin && !isALL && !sysArchivist) {

			boolean isSub = UserInfoHolder.getRight("IS_SUB");

			if (isSub) {
				
				String orgRang = user.getOrgRang();
				
				List<String> orgCodeList = new ArrayList<>();
				
				if (StringUtils.isNotBlank(orgRang)) {
					
					String[] orgList = (orgRang.substring(1, orgRang.length() - 1).replaceAll("'", "").split(","));
					
					for (String orgCode : orgList) {
						if (StringUtils.isNotBlank(orgCode)) {
							orgCodeList.add(StringUtils.deleteWhitespace(orgCode));
						}
					}
				}
				
				// 直接查询本部门 或者 下属部门权限  本人 SELF 本部门 SELF_DEPT SELF_SUB_DEPT 本部门以及下属部门
				List<String> hrmsOrganizationAndNextList = hrmsOrganizationService.getHrmsOrganizationAndNextList(user.getDeptId());

				orgCodeList.addAll(hrmsOrganizationAndNextList);
				
				record.setOrgCodeList(orgCodeList);
				
			} else {
				
				List<String> orgIdList = mapper.selectManageDept(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());//科主任可以查看自己科室的所有员工
				
				if(CollectionUtils.isNotEmpty(orgIdList)) {  
					
					orgIdList.add("23405568"); //省人医需求 后续去掉
					
					record.setOrgCodeList(orgIdList);
				}else {
					record.setCreateUser(user.getUsercode());
				}
			}
		}
		
		GlobalSetting globalSetting = globalSettingsService.getGlobalSetting("Y");
		
		String orgCode = globalSetting.getOrgCode();
		
		//数据管理权限
		List<String> identityCode = customEmployeeLeaderService.getIdentityCodeByUserCode(user.getUsercode());
		
		record.setIdentityCode(identityCode);
		//根据当前登录账号机构编码过滤
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		//只查询头部显示的字段数据
		List<CustomEmployeeField> headField = customEmployeeFieldService.getHeadFieldList().stream()
				.filter(h -> h.getSetValue() == null || h.getSetValue().equals(1)).collect(Collectors.toList());
		StringBuffer headFieldSql = new StringBuffer();
		//字段重复去重
		headField.stream().map(CustomEmployeeField::getFieldName).distinct().forEach(name ->{
			if ("remark".equalsIgnoreCase(name)) {
				headFieldSql.append("s." + name).append(",");
			}else if("position_id".equalsIgnoreCase(name)) {
				headFieldSql.append("s." + name).append(",");
			}else if("jobtitle_name".equalsIgnoreCase(name)) {
				headFieldSql.append("a2.jobtitleName").append(",");
			}else if("HOSP_CODE".equalsIgnoreCase(name)) {
				headFieldSql.append("s." + name).append(",");
			}else {
				headFieldSql.append(name).append(",");
			}
		});
		record.setHeadFieldSql(headFieldSql.toString());
		
		//自定义查询条件
		Map<String, String> queryMap = record.getQueryMap();
        if (null != queryMap && !queryMap.isEmpty()) {
            
            String startFieldName = "";
            String startFieldValue = "";
            String endFieldName = "";
            String endFieldValue = "";
            
//            String startNumFieldName = "";
//            String startNumFieldValue = "";
//            String endNumFieldName = "";
//            String endNumFieldValue = "";

            StringBuffer sql = new StringBuffer();
            log.info("=======请求参数："+JSONObject.toJSONString(queryMap));
            for (String key : queryMap.keySet()) {
            	
                String value = Convert.toStr(queryMap.get(key));
                if("HOSP_CODE".equalsIgnoreCase(key)){
                	key ="s." + key;
				}
				if("jobtitle_name".equalsIgnoreCase(key)){
					key ="a2.jobtitleName";
				}
				if("jobtitle_level".equalsIgnoreCase(key)){
					key ="a2.jobtitleCategoryLevel";
				}
				if("jobtitle_category".equalsIgnoreCase(key)){
					key ="a3.jobtitleCategoryName";
				}

                if (StringUtils.isNotBlank(value)) {
                    
                    if (key.startsWith("start_")) {
                    	//字段名称可以能存在start_的关键字，将获取字段名称字段截图
//                        String[] arr = key.split("start_");
//                        String fieldName = arr[1];

						String fieldName = key.substring(6);


						startFieldName = fieldName;
                        startFieldValue = value.split(" ")[0] + " 00:00:00";

						if(StringUtils.isNotBlank(startFieldName) && StringUtils.isNotBlank(startFieldValue)) {
							sql.append(" AND ").append(startFieldName).append(" >= '").append(startFieldValue).append("'");
						}

                    } else if (key.startsWith("end_")) {

						//字段名称可以能存在start_的关键字，将获取字段名称字段截图
//                        String[] arr = key.split("end_");
//                        String fieldName = arr[1];
						String fieldName =key.substring(4);
                        
                        endFieldName = fieldName;
                        endFieldValue = value.split(" ")[0] + " 23:59:59";
						if(StringUtils.isNotBlank(endFieldName) && StringUtils.isNotBlank(endFieldValue)) {
							sql.append(" AND ").append(endFieldName).append(" <= '").append(endFieldValue).append("'");
						}

                    } else if (key.startsWith("numStart_")) {

						//字段名称可以能存在start_的关键字，将获取字段名称字段截图
//                        String[] arr = key.split("numStart_");
//                        String fieldName = arr[1];
//						  String startNumFieldName = fieldName;
						String startNumFieldName = key.substring(9);


						String  startNumFieldValue = value.split(" ")[0];

						if(StringUtils.isNotBlank(startNumFieldName) && StringUtils.isNotBlank(startNumFieldValue)){
							sql.append(" AND ").append(startNumFieldName).append(" >= ").append(startNumFieldValue);
						}

                    } else if (key.startsWith("numEnd_")) {

						//字段名称可以能存在start_的关键字，将获取字段名称字段截图
//                        String[] arr = key.split("numEnd_");
//                        String fieldName = arr[1];
//						String endNumFieldName = fieldName;
						String endNumFieldName = key.substring(7);
						String  endNumFieldValue = value.split(" ")[0];

						//工龄只存在最大值且没有最小值或最小值为空时，则需要把为空的数据也查询出来
						if("year_work".equals(endNumFieldName) && StringUtils.isNotBlank(endNumFieldValue) &&
								(!queryMap.containsKey("numStart_year_work") || StringUtils.isBlank(queryMap.get("numStart_year_work")))){
							sql.append(" AND (").append(endNumFieldName).append(" <= ").append(endNumFieldValue).append(" or year_work is null )");
						}else if(StringUtils.isNotBlank(endNumFieldName) && StringUtils.isNotBlank(endNumFieldValue)){
							sql.append(" AND ").append(endNumFieldName).append(" <= ").append(endNumFieldValue);
						}

                    } else {
                    	if(value.contains("isnull")){
							sql.append(" AND ").append(key).append(" is null ");
						}else if(value.contains(",")) {
                    		
                    		String[] values = value.split(",");
                    		
                    		StringBuffer valStr = new StringBuffer();
                    		for (String val : values) {
                    			valStr.append("'").append(val).append("',");
							}
                    		valStr.deleteCharAt(valStr.length() - 1);
                    		sql.append(" AND ").append(key).append("  in (").append(valStr).append(")");
                    	}else {
                    		sql.append(" AND ").append(key).append("  like '%").append(value).append("%'");
                    	}
                    }
                }
            }

            if(StringUtils.isNotBlank(startFieldName) && StringUtils.isNotBlank(endFieldName)) {
				sql.append(" AND (").append(startFieldName).append(" BETWEEN '").append(startFieldValue).append("' AND '").append(endFieldValue).append("'");
				sql.append(" OR ").append(endFieldName).append(" BETWEEN '").append(startFieldValue).append("' AND '").append(endFieldValue).append("'");
				sql.append(" OR '").append(startFieldValue).append("' BETWEEN ").append(startFieldName).append(" AND ").append(endFieldName);
				sql.append(" OR '").append(endFieldValue).append("' BETWEEN ").append(startFieldName).append(" AND ").append(endFieldName).append(")");
			}
//            if(StringUtils.isNotBlank(startNumFieldName) && StringUtils.isNotBlank(endNumFieldName)){
//            	sql.append(" AND ").append(startNumFieldName).append(" BETWEEN ").append(startNumFieldValue).append(" and ").append(endNumFieldValue);
//            }
            record.setSearchSql(sql.toString());
        }
        if(StringUtils.isNotBlank(record.getArchivesType())){ //如果档案类型不为空，则根据档案类型进行数据过滤
            String archivesTypeSql = "AND s.archives_type = '"+record.getArchivesType()+"' ";
            if(StringUtils.isNotBlank(record.getSearchSql())){
                record.setSearchSql(record.getSearchSql() + " "+ archivesTypeSql);
            }else{
                record.setSearchSql(archivesTypeSql);
            }
        }
        
        //排序字段需要加别名
        if(StringUtils.isNotBlank(page.getSidx())) {
        	
        	String sidx = page.getSidx();
        	
        	String tablename = mapper.selectTableNameByFieldName(sidx);
        	if("jobtitleCategoryName".equals(sidx)){
				sidx = "a3." + sidx;
			}else if("jobtitleCategoryLevel".equals(sidx)){
				sidx = "a2." + sidx;
			}else if("jobtitleName".equals(sidx)){
				sidx = "a2." + sidx;
			}else if("assessment_date".equals(sidx)){
				sidx = "a1." + sidx;
			}else if( "school_name".equals(sidx) ||  "education_type_name".equals(sidx)
					|| "professional".equals(sidx)){
				sidx = "a4." + sidx;
			}else if("edEndTime".equals(sidx)){
				sidx = "a4.end_time";
			}else if("cust_emp_base".equals(tablename)) {
        		sidx = "s." + sidx;
        	}else {
        		if(ObjectUtils.isEmpty(tablename) && sidx.indexOf(".") == 0){
        			sidx = "i." + sidx;
        		}
        	}
        	
        	page.setSidx(sidx);
        	
        	//二福定制化需求  人员按左边机构排序
    		if ("cssdeshfly".equals(orgCode)) {
				if ("create_date".equals(page.getSidx())) {
					page.setSidx(" o.ksfb_no");
					page.setSord("ASC");
				}
    		}
        }else {
        	page.setSidx(" s.emp_sort ");
			page.setSord(" asc");
        }   
        
		List<Map<String, String>> list = mapper.getDataSetList(page, record);

		setDictValue(list, headField);

		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	/**
	 * 设置字典值
	 * @param list
	 * @param fieldList
	 */
	private void setDictValue(List<Map<String, String>> list, List<CustomEmployeeField> fieldList) {

		Map<String, Map<String, String>> dictMap = dictItemService.getDictMap();
		
	    List<Map<String,String>> jobMap = commEmployeeFieldGroupMapper.getJobMap();
		List<CommCity> commCityList = commCityMapper.selectAll();
		Map<String,String> cityMap= commCityList.stream().collect(Collectors.toMap(CommCity::getId,CommCity::getName));
	    Map<String, String> baseJobMap = jobMap.stream().collect(Collectors.toMap(item -> item.get("jobtitle_basic_id"), item -> item.get("jobtitle_basic_name")));

		for (CustomEmployeeField field : fieldList) {

			if (StringUtils.isNotBlank(field.getDictSource())) {

				String diceSource = field.getDictSource();

				Map<String, String> itemMap = dictMap.get(diceSource);

				for (Map<String, String> dataMap : list) {

					for (String key : dataMap.keySet()) {

						if (key.equals(field.getFieldName())) {

							if (null != itemMap && null != dataMap.get(key)) {

								dataMap.put(key, itemMap.get(dataMap.get(key)));
							}
						}
					}
				}
			} else {
				if (field.getFieldName().equals("born_address") || field.getFieldName().equals("birthplace")) {
					for (Map<String, String> dataMap : list) {
						for (String key : dataMap.keySet()) {
							if (key.equals(field.getFieldName())) {
								String value = dataMap.get(key);
								if (StringUtils.isNoneBlank(value)) {
									String[] arrCodes = value.split("-");
									StringBuffer cityNames = new StringBuffer();
									for (String cityCode : arrCodes) {
										if(cityMap!=null && cityMap.containsKey(cityCode) &&  null != cityMap.get(cityCode)) {
											cityNames.append(cityMap.get(cityCode)).append("-") ;
										}
									}
									if (StringUtils.isNotBlank(cityNames)) {
										dataMap.put(key, cityNames.substring(0, cityNames.length() - 1));
									}
								}
							}
						}
					}
				}
				
				if(field.getFieldName().equals("jobtitle_category") || field.getFieldName().equals("jobtitle_level") || field.getFieldName().equals("jobtitle_name")) {
                    for (Map<String, String> dataMap : list) {
                        for (String key : dataMap.keySet()) {
                            if (key.equals(field.getFieldName())) {
                                dataMap.put(key, baseJobMap.get(dataMap.get(key)));
                            }
                        }
                    }
            }

			}
		}
	}

	@Override
	public List<CustomEmployeeBase> getEmployeeBasePageList(Page page, CustomEmployeeBase record) {
			boolean isAllData = false;
			if(StringUtils.isNotBlank(record.getIsAll()) && "Y".equals(record.getIsAll())){
				isAllData = true;
			}
			if(!isAllData){
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			}
		 	
		 	if(StringUtils.isNotBlank(record.getOrgId())) {
		 		List<String> deptChildsList = hrmsOrganizationService.getChildOrgIdsList(record.getOrgId());
		    	record.setChildOrgListIds(deptChildsList);	
		 	}
		 	
		 	if(StringUtils.isNotBlank(record.getChildOrgIds())) {
		 		List<String> deptChildsList = Arrays.asList(record.getChildOrgIds().split(","));
		    	record.setChildOrgListIds(deptChildsList);	
		 	}
		 	
		 	if(StringUtils.isNotBlank(record.getEmployeeStatus())) {
		 		List<String> employeeStatusList = Arrays.asList(record.getEmployeeStatus().split(","));
		    	record.setEmployeeStatusList(employeeStatusList);	
		 	}
	    	
		 	List<CustomEmployeeBase> list = mapper.getEmployeeBasePageList(page,record);
		 	
	        // 增加本人
		 	if("benren".equals(record.getAuthority())) {
		 		CustomEmployeeBase resp = new CustomEmployeeBase();
		        resp.setEmployeeNo(CommonContants.BEN_REN_EMP);
		        resp.setEmployeeId(CommonContants.BEN_REN_EMP);
		        resp.setEmployeeName("本人");
		        list.add(0,resp);
		 	}
		 	
		 	//TODO 
	        return list;
	}

	@Override
	public Map<String, List<Map<String, Object>>> findDetailsById(String employeeId,String groupId,String archivesType) {

		CustomEmployeeGroup record = new CustomEmployeeGroup();
		record.setId(groupId);
		record.setArchivesType(archivesType);
		List<CustomEmployeeGroup> groupList = customEmployeeGroupService.getGroupListBy(record);

		Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>();

		for (CustomEmployeeGroup group : groupList) {

			String tableName = group.getTableName();
			
			if (StringUtils.isNotBlank(tableName)) {
				
				List<CustomEmployeeField> fields = customEmployeeFieldService.selectByGroupId(group.getId());
				
				if(CollectionUtils.isNotEmpty(fields)) {
					
					StringBuffer fieldStr = new StringBuffer();
					
					for (CustomEmployeeField customEmployeeField : fields) {
						fieldStr.append(customEmployeeField.getFieldName()).append(",");
					}
					
					fieldStr.deleteCharAt(fieldStr.length()-1);
					
					if("cust_emp_info".equals(tableName)) {
						
						StringBuffer sql = new StringBuffer();
						sql.append("select ").append(fieldStr.toString()).append("  from ")
						.append("cust_emp_base t1 LEFT JOIN cust_emp_info t2 on t1.employee_id = t2.info_id ")
						.append("where is_deleted = 'N' and employee_id = '").append(employeeId).append("'");
						
						if(StringUtils.isNotBlank(group.getSortField())) {
							sql.append(" ORDER BY ").append(group.getSortField()).append(" desc ");
						}else {
							sql.append(" ORDER BY CREATE_DATE desc");
						}
						
						List<Map<String, Object>> list = jdbcTemplate.queryForList(sql.toString());
						
						if("聘任情况".equals(group.getGroupName()) || "执业情况".equals(group.getGroupName())) {
							
//							boolean areAllValuesNull = areAllValuesNull(list.get(0));
							
							//所有值为null 要给一个空数组
//							if(areAllValuesNull) {
//								
//								Map<String,String> nullMap = new HashMap<>();
//								List<Map<String,String>> nullList = new ArrayList<>();
//								nullList.add(nullMap);
//								map.put(group.getId(), nullList);
//							}else {
//								map.put(group.getId(), list);
//							}
							
							map.put(group.getId(), list);
							
						}else {
							map.put(group.getId(), list);
						}
					}else {
//						customEmployeeModel.setTableName(tableName);
//						customEmployeeModel.setSortField(group.getSortField());
						
						//List<Map<String, String>> list = mapper.findByEmployeeId(customEmployeeModel);
						
						StringBuffer sql = new StringBuffer();
						sql.append("select ").append(fieldStr.toString()).append("  from ").append(tableName)
						.append(" where is_deleted = 'N' and employee_id = '").append(employeeId).append("'");
						
						if(StringUtils.isNotBlank(group.getSortField())) {
							sql.append(" ORDER BY ").append(group.getSortField()).append(" desc ");
						}else {
							sql.append(" ORDER BY CREATE_DATE desc");
						}
						
						List<Map<String, Object>> list = jdbcTemplate.queryForList(sql.toString());
						
						map.put(group.getId(), list);
					}
				}
			}
		}
		
		return map;
	}
	

//	private boolean areAllValuesNull(Map<?, ?> map) {
//        if (map == null) {
//            return true; // Map本身就是null，直接返回true
//        }
//        for (Object value : map.values()) {
//            if (!Objects.isNull(value)) {
//                return false; // 发现非null的值，直接返回false
//            }
//        }
//        return true; // 所有的值都是null，返回true
//    }
	

	@Override
	public String uniqueCheck(CustomEmployeeModel record) {
		
		String message = "";

        String employeeId = record.getEmployeeId();

        List<CustomEmployeeDetailsModel> records = record.getCustomFileds();

        List<CustomEmployeeField> allEmployeeFields = new ArrayList<CustomEmployeeField>();
        
        for (CustomEmployeeDetailsModel customEmployee : records) {
            // 个人信息
            List<CustomEmployeeField> fields = customEmployee.getFields();

            if (CollectionUtils.isNotEmpty(fields)) {

                allEmployeeFields.addAll(fields);
            }
        }

        if (CollectionUtils.isNotEmpty(allEmployeeFields)) {
        	
            String employeeNo = "";
            String employeeName = "";
            String orgId = "";
            String employeeStatus = "";
            String empPayroll = "";
            String identityNumber = "";
            String phoneNumber = "";
            String email = "";

            for (CustomEmployeeField field : allEmployeeFields) {

                if (field.getFieldName().equals("employee_no")) {

                    employeeNo = field.getValue();
                }
                
                if (field.getFieldName().equals("employee_name")) {

                	employeeName = field.getValue();
                }
                
                if (field.getFieldName().equals("org_id")) {

                	orgId = field.getValue();
                }
                
                if (field.getFieldName().equals("employee_status")) {

                	employeeStatus = field.getValue();
                }
                
                if (field.getFieldName().equals("emp_payroll")) {

                    empPayroll = field.getValue();
                }
                
                Integer isRemoveDuplicate = field.getIsRemoveDuplicate();
                
                if (field.getFieldName().equals("identity_number") || null == isRemoveDuplicate || 0 == isRemoveDuplicate) {

                    identityNumber = field.getValue();
                }
                
                if (field.getFieldName().equals("phone_number") || null == isRemoveDuplicate || 0 == isRemoveDuplicate) {

                	phoneNumber = field.getValue();
                }
                
                if (field.getFieldName().equals("email") || null == isRemoveDuplicate || 0 == isRemoveDuplicate) {

                	email = field.getValue();
                }
               
            }
            
            if(StringUtils.isBlank(employeeNo) || StringUtils.isBlank(employeeName) 
            		|| StringUtils.isBlank(orgId) || StringUtils.isBlank(employeeStatus)) {
            	  message = "工号、姓名、组织机构、员工状态不允许为空";
            }
            
            if (StringUtils.isNotBlank(employeeNo)) {
            	
            	 Example example = new Example(CustomEmployeeBase.class);
                 example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                 example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                 example.and().andEqualTo("employeeNo", employeeNo);
                 if (StringUtils.isNotBlank(employeeId)) {
                     example.and().andNotEqualTo("employeeId", employeeId);
                 }
                 List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);

                 if (CollectionUtils.isNotEmpty(employeeList)) {
                     message = "工号:" + employeeNo + "已存在,请勿重复添加";
                 }
            }
            
            if (StringUtils.isNotBlank(identityNumber)) {

                Example example = new Example(CustomEmployeeBase.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("identityNumber", identityNumber);
                if (StringUtils.isNotBlank(employeeId)) {
                    example.and().andNotEqualTo("employeeId", employeeId);
                }
                List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);

                if (CollectionUtils.isNotEmpty(employeeList)) {

                    message = "身份证:" + identityNumber + "已存在,请勿重复添加";
                }
           }

           if (StringUtils.isNotBlank(empPayroll)) {

                Example example = new Example(CustomEmployeeBase.class);
                example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
                example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
                example.and().andEqualTo("empPayroll", empPayroll);
                if (StringUtils.isNotBlank(employeeId)) {
                    example.and().andNotEqualTo("employeeId", employeeId);
                }
                List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);

                if (CollectionUtils.isNotEmpty(employeeList)) {

                    message = "发薪号:" + empPayroll + "已存在,请勿重复添加";
                }
            }
           
           if (StringUtils.isNotBlank(phoneNumber)) {

               Example example = new Example(CustomEmployeeBase.class);
               example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
               example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
               example.and().andEqualTo("phoneNumber", phoneNumber);
               if (StringUtils.isNotBlank(employeeId)) {
                   example.and().andNotEqualTo("employeeId", employeeId);
               }
               List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);

               if (CollectionUtils.isNotEmpty(employeeList)) {

                   message = "手机号:" + phoneNumber + "已存在,请勿重复添加";
               }
           }
           
           if (StringUtils.isNotBlank(email)) {

               Example example = new Example(CustomEmployeeBase.class);
               example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
               example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
               example.and().andEqualTo("email", email);
               if (StringUtils.isNotBlank(employeeId)) {
                   example.and().andNotEqualTo("employeeId", employeeId);
               }
               List<CustomEmployeeBase> employeeList = mapper.selectByExample(example);

               if (CollectionUtils.isNotEmpty(employeeList)) {

                   message = "手机号:" + email + "已存在,请勿重复添加";
               }
           }
           
        }

        return message;
	}

	@Override
	@Transactional(readOnly = false)
	public void importEmployee(List<EmployeeImport> employeeImportList,String employeeStatus) {
		for (int i = 0; i < employeeImportList.size(); i++) {
			EmployeeImport employeeImport = employeeImportList.get(i);
			if (StringUtils.isNotEmpty(employeeImport.getEntryDate())){
				employeeImport.setEntryDateDate(DateUtils.getStringToDate(employeeImport.getEntryDate()));
			}
        	CustomEmployeeInfo customEmployeeInfo = BeanUtil.copyProperties(employeeImport, CustomEmployeeInfo.class);

        	HrmsOrganization organization = hrmsOrganizationService.getOrganizationByName(employeeImport.getOrgName());
            
            if (organization != null) {
            	customEmployeeInfo.setOrgId(organization.getOrganizationId());
            }
            if(StringUtils.isNotBlank(employeeImport.getGender())) {
            	customEmployeeInfo.setGender(employeeImport.getGender().equals("男") ? "0" : "1");
            }
            if(StringUtils.isNotBlank(employeeImport.getEstablishmentType())) {
            	customEmployeeInfo.setEstablishmentType(employeeImport.getEstablishmentType().equals("是") ? "1" : "2");
            }
            if(StringUtils.isNotBlank(employeeImport.getPoliticalStatus())) {
            	customEmployeeInfo.setPoliticalStatus(employeeImport.getPoliticalStatus().equals("是") ? "1" : "4");
            }
            
            if(StringUtils.isNotBlank(employeeStatus)) {
        		customEmployeeInfo.setEmployeeStatus(employeeStatus);
        	}
            
            Example example = new Example(CustomEmployeeBase.class);
            example.and().andEqualTo("employeeNo", employeeImport.getEmployeeNo());
            example.setOrderByClause("  employee_id LIMIT 1");
            CustomEmployeeBase emp = mapper.selectOneByExample(example);
            
            if (emp != null) {
            	emp.setEmployeeName(employeeImport.getEmployeeName());
            	emp.setOrgId(organization.getOrganizationId());
            	emp.setGender(employeeImport.getGender().equals("男") ? "0" : "1");
            	emp.setIdentityNumber(employeeImport.getIdentityNumber());
            	emp.setBirthday(employeeImport.getBirthdayDate());
            	emp.setEntryDate(DateUtil.format(employeeImport.getEntryDateDate(), "yyyy-MM-dd"));
            	emp.setPhoneNumber(employeeImport.getPhoneNumber());
                update(emp);
            } else {
				//验证身份证号码是否合法
				if (StringUtils.isNotBlank(employeeImport.getIdentityNumber()) && IdcardUtil.isValidCard18(employeeImport.getIdentityNumber())){
					//根据身份证号码截取出生年月日
					String birthday = employeeImport.getIdentityNumber().substring(6, 10) + "-" + employeeImport.getIdentityNumber().substring(10, 12) + "-" + employeeImport.getIdentityNumber().substring(12, 14);
					employeeImport.setBirthdayDate(DateUtils.getStringToDate(birthday));
					//计算年龄
					int years = Period.between(DateUtils.turnLocalDate(employeeImport.getBirthdayDate()),
							DateUtils.turnLocalDate(new Date())).getYears();
					customEmployeeInfo.setEmpAge(String.valueOf(years));
				}
				//根据来院时间计算工龄
				if (employeeImport.getEntryDateDate() != null){
					int years = Period.between(DateUtils.turnLocalDate(employeeImport.getEntryDateDate()),
							DateUtils.turnLocalDate(new Date())).getYears();
					customEmployeeInfo.setYearWork(String.valueOf(years));
				}
                add(customEmployeeInfo);
            }
        }
	}

	@Override
	@Transactional(readOnly = false)
	public void audit(CustomEmployeeResp record) {
		
		Map<String, Object> map = new HashMap<>();

        if ("1".equals(record.getAuditStatus())) {

            workflowTaskService.completeTask(record.getTaskId(), map); 

        } else {
           
        	CustomEmployeeUpdateOperation op = new CustomEmployeeUpdateOperation();
            op.setId(record.getOperationId());
            op.setRemark(record.getRemark());
            customEmployeeUpdateOperationService.update(op);

            map.put("handleMarkedWords", record.getRemark());
            
            workflowTaskService.doRejectTask(record.getTaskId(), map);
        }
	}

	@Override
	@Transactional(readOnly = false)
	public void auditFinish(String operationId) {
		
		CustomEmployeeUpdateOperation updateOperation = customEmployeeUpdateOperationService.selectById(operationId);

        if (null != updateOperation) {
        	
        	String employeeId = updateOperation.getEmployeeId();
            
        	//查询合格数据
            List<CustomEmployeeUpdateDetail> details = customEmployeeUpdateDetailService.selectByAuditStatus(operationId,"1");

            if (CollectionUtils.isNotEmpty(details)) {

                Map<Integer, List<CustomEmployeeUpdateDetail>> empFieldMap = details.stream()
                        .collect(Collectors.groupingBy(CustomEmployeeUpdateDetail::getUpdateType));

                for (Integer updateType : empFieldMap.keySet()) {

                    List<CustomEmployeeUpdateDetail> empBasicsFields = empFieldMap.get(updateType);

                    if (1 == updateType) { // 员工基础信息修改
                       
                        if (CollectionUtils.isNotEmpty(empBasicsFields)) {

                            List<CustomEmployeeField> fields = new ArrayList<>();

                            for (CustomEmployeeUpdateDetail detail : empBasicsFields) {

                            	CustomEmployeeField commEmployeeField = new CustomEmployeeField();

                                commEmployeeField.setFieldName(detail.getFieldName());

                                commEmployeeField.setValue(detail.getAfterData());
                                
                                commEmployeeField.setFieldClass(detail.getFieldClass());

                                fields.add(commEmployeeField);
                            }

                            if (CollectionUtils.isNotEmpty(fields)) {
                            	
                            	List<CustomEmployeeField> baseField = new ArrayList<CustomEmployeeField>();
                        		List<CustomEmployeeField> infoField = new ArrayList<CustomEmployeeField>();
                        		
                        		for (CustomEmployeeField customEmployeeField : fields) {
                        			
                    				if("1".equals(customEmployeeField.getFieldClass())) { //基础表字段
                    					baseField.add(customEmployeeField);
                    				}else { //业务表字段
                    					infoField.add(customEmployeeField);
                    				}
                    			}
                        		
                        		if(CollectionUtils.isNotEmpty(baseField)) {
                        			String updateBaseSql = SqlGenerationUtils.updateEmployeeInfo(baseField, "cust_emp_base", employeeId);
                        			customEmployeeGroupService.executeSql(updateBaseSql);
                        		}
                        		
                        		if(CollectionUtils.isNotEmpty(infoField)) {
                        			String updateInfoSql = SqlGenerationUtils.updateEmployeeInfo(infoField, "cust_emp_info", employeeId);
                        			customEmployeeGroupService.executeSql(updateInfoSql);
                        		}
                            }

                        }
                    } else {// 员工明细表信息修改

                        if (CollectionUtils.isNotEmpty(empBasicsFields)) {

                            for (CustomEmployeeUpdateDetail detail : empBasicsFields) {

                            	CustomEmployeeGroup commEmployeeFieldGroup = customEmployeeGroupService.selectGroupById(detail.getGroupId());

                                String tableName = commEmployeeFieldGroup.getTableName();

                                String afterData = detail.getAfterData();

                                List<List<CustomEmployeeField>> detailFields = new ArrayList<>();

                                if (StringUtils.isNotBlank(afterData)) {

                                    detailFields = JsonUtil.formatJsonStrToBean2(afterData);

                                }

                                if (CollectionUtils.isNotEmpty(detailFields)) {

                                	updateEmpDetail(employeeId, tableName, detailFields);

                                } else {// 如果修改的数据为空，则表示全部删除
                                	
                                	String deleteSql = SqlGenerationUtils.deleteEmployeeDetailByEmployeeId(employeeId, tableName);
                                	
                        			log.info("删除人员明细信息执行的sql：" + deleteSql);
                        			
                        			customEmployeeGroupService.executeSql(deleteSql);
                                }
                            }

                        }

                    }
                }
            }
            
            updateOperation.setAuditStatus(2);
            
            customEmployeeUpdateOperationService.update(updateOperation);
        }
	}

	@Override
	public Map<String, Object> inApproval(String archivesType) {
		
		Map<String, Object> empFlowStatus = mapper.getEmpFlowStatus(UserInfoHolder.getCurrentUserId(), archivesType, UserInfoHolder.getCurrentUserCorpCode());
		
        if (empFlowStatus != null && !empFlowStatus.isEmpty()) {
        	
            // 查询最后一次提交的数据
            String businessId = (String) empFlowStatus.get("BUSINESS_ID");
            
            List<CustomEmployeeUpdateDetail> commEmployeeUpdateDetails = customEmployeeUpdateDetailService.selectByOperationId(businessId);

//            CustomEmployeeUpdateOperation commEmployeeUpdateOperation = customEmployeeUpdateOperationService.selectById(businessId);
            
            Map<String, Object> returnMap = null;
            
            String status = String.valueOf(empFlowStatus.get("STATUS"));
            
            if (!StringUtil.isEmpty(status)) {
            	
                returnMap = new HashMap<>();
                
                if ("1".equals(status)) {
                    returnMap.put("status", "1"); // 1 审核中
                    returnMap.put("data", commEmployeeUpdateDetails);
                } else if ("2".equals(status)) {
                    returnMap.put("status", "2"); // 2 通过
                    returnMap.put("data", commEmployeeUpdateDetails);
                } else {
                    returnMap.put("status", "3"); // 3 驳回
                    returnMap.put("data", commEmployeeUpdateDetails);
                }
            }
            
//            if (commEmployeeUpdateOperation != null) {
//                returnMap.put("remark", commEmployeeUpdateOperation.getRemark());
//            } else {
//                returnMap.put("remark", null);
//            }

            return returnMap;
        }
	        
		return null;
	}

	@Override
	public Map<String, Object> getEmpFlowStatus(String employeeId,String archivesType) {
		return mapper.getEmpFlowStatus(employeeId, archivesType, UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	@Transactional(readOnly = false)
	public void calculationEmployeeMessage(CustomEmployeeInfo employee) {
		mapper.calculationEmployeeMessage(employee);
	}

	@Override
	public List<CustomEmployeeInfo> getCalculationEmployeeMessageInfo() {
		return mapper.getCalculationEmployeeMessageInfo();
	}
	
	@Override
	@Transactional(readOnly = false)
	public void updateEmployee(CustomEmployeeBase customEmployeeBase) {
        
		mapper.updateByPrimaryKeySelective(customEmployeeBase);
            
	}

	@Override
	public Map<String, String> getEmployeeTask(String employeeNo) {
		
		Assert.hasText(employeeNo, "人员工号不能为空");
		
        Map<String, String> employeeTask = mapper.getEmployeeTask(employeeNo);

        Boolean sysArchivist = UserLoginService.getRight("SYS_ARCHIVIST");
        
        Boolean isadmin = UserLoginService.ISADMIN();

        if (null != employeeTask) {
            if (sysArchivist || isadmin) {
                employeeTask.put("isAdmin", "true");
            } else {
                employeeTask.put("isAdmin", "false");
            }
        } else {
            employeeTask = new HashMap<>();
            if (sysArchivist || isadmin) {
                employeeTask.put("isAdmin", "true");
            } else {
                employeeTask.put("isAdmin", "false");
            }
        }
        return employeeTask;
	}

	@Override
	@Transactional(readOnly = false)
	public void saveContact(List<CommContact> commContact) {
		for (CommContact contact : commContact) {
			contact.setId(ApplicationUtils.GUID32());
			contact.setUserCode(UserInfoHolder.getCurrentUserCode());
			mapper.saveContact(contact);
		}
	}

	@Override
	public DataSet<Map<String, Object>> getCommContact(Page page,CommContact commContact) {
		commContact.setUserCode(UserInfoHolder.getCurrentUserCode());
		//当前账号机构编码
		commContact.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> list = mapper.getCommContact(page,commContact);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	@Override
	public String generateEmployeeNo(String establishmentType) {
		if(null == establishmentType){
			establishmentType = "";
		}
		//查询工号字段的设置信息
		Example example = new Example(CustomEmployeeField.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
	    example.and().andEqualTo("fieldName", "employee_no");
	    example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
	    List<CustomEmployeeField> result = customEmployeeFieldMapper.selectByExample(example);
	    if(CollUtil.isNotEmpty(result)){
	    	CustomEmployeeField employeeNoField = result.get(0);
	    	Integer dataSource = employeeNoField.getDataSource();
	    	String serialNumberRule = employeeNoField.getSerialNumberRule();//流水号规则(1-编制类型编码+流水号)
	    	Integer serialNumberLength = employeeNoField.getSerialNumberLength();//流水号长度
	    	Long serialNumberInitialValue = employeeNoField.getSerialNumberInitialValue();//初始值
	    	String reservedNumber = employeeNoField.getReservedNumber();//预留号码
	    	String disabledNumber = employeeNoField.getDisabledNumber();//禁用号码
	    	//判断当前工号是否设置了自动生成  数据来源(1-手动录入 2-常用字段 3-公式编辑 4-数据字典 5-自动生成)
	    	if(dataSource != null && dataSource == 5){
	    		if(!ObjectUtils.isEmpty(serialNumberRule) && serialNumberLength != null && serialNumberInitialValue != null){
	    			//按照规则生成一个--优先根据下一个值生成
	    			String employNo = null;
	    			boolean exist = false;//工号存在的标记
	    			while(!exist){
	    				employNo = SerialNumberGeneratorUtils.generateSerialNumbereExclude(serialNumberLength, serialNumberInitialValue, reservedNumber, disabledNumber);
	    				//校验工号是否存在，存在则生成下一个
	    				Example exampleBase = new Example(CustomEmployeeBase.class);
	    				exampleBase.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
	    				exampleBase.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
	    				exampleBase.and().andEqualTo("employeeNo", establishmentType + employNo);
	                    List<CustomEmployeeBase> employeeList = mapper.selectByExample(exampleBase);
	                    if (CollectionUtils.isEmpty(employeeList)) {
	                    	exist = true;
	                    }
	                    serialNumberInitialValue ++;
	    			}
	    			return establishmentType + employNo;
	    		} else {
	    			throw new BusinessException("请先完善工号自动生成规则!");
	    		}
	    	}
	    }
	    return null;
	}
	
	/**
	 * 保存下一个流水号-当保存人员成功时调用
	 * <AUTHOR>
	 * @update 2025-06-21
	 */
	void updateInitialValue(CustomEmployeeField employeeNoField){
		employeeNoField = customEmployeeFieldMapper.selectByPrimaryKey(employeeNoField.getId());
		
		Integer dataSource = employeeNoField.getDataSource();
    	String serialNumberRule = employeeNoField.getSerialNumberRule();//流水号规则(1-编制类型编码+流水号)
    	Integer serialNumberLength = employeeNoField.getSerialNumberLength();//流水号长度
    	Long serialNumberInitialValue = employeeNoField.getSerialNumberInitialValue();//初始值
    	String reservedNumber = employeeNoField.getReservedNumber();//预留号码
    	String disabledNumber = employeeNoField.getDisabledNumber();//禁用号码
    	//判断当前工号是否设置了自动生成  数据来源(1-手动录入 2-常用字段 3-公式编辑 4-数据字典 5-自动生成)
    	if(dataSource != null && dataSource == 5){
    		if(!ObjectUtils.isEmpty(serialNumberRule)
	    			&& serialNumberLength != null && serialNumberInitialValue != null){
    			//按照规则生成一个--优先根据下一个值生成
    			String employNo = null;
    			boolean exist = false;//工号存在的标记
    			while(!exist){
    				employNo = SerialNumberGeneratorUtils.generateSerialNumbereExclude(serialNumberLength, serialNumberInitialValue, reservedNumber, disabledNumber);
    				//校验工号是否存在，存在则生成下一个
    				Example exampleBase = new Example(CustomEmployeeBase.class);
    				exampleBase.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
    				exampleBase.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
    				exampleBase.and().andEqualTo("employeeNo", employNo);
                    List<CustomEmployeeBase> employeeList = mapper.selectByExample(exampleBase);
                    if (CollectionUtils.isEmpty(employeeList)) {
                    	exist = true;
                    }
                    serialNumberInitialValue++;
    			}
    			if(employeeNoField.getSerialNumberInitialValue().equals(Long.valueOf(employNo))){
    				//判断serialNumberInitialValue是否已经存在
    				String nextEmployNo = serialNumberInitialValue + "";
    				exist = false;
    				while(!exist){
    					nextEmployNo = SerialNumberGeneratorUtils.generateSerialNumbereExclude(serialNumberLength, serialNumberInitialValue, reservedNumber, disabledNumber);
        				//校验工号是否存在，存在则生成下一个
        				Example exampleBase = new Example(CustomEmployeeBase.class);
        				exampleBase.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
        				exampleBase.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        				exampleBase.and().andEqualTo("employeeNo", nextEmployNo);
                        List<CustomEmployeeBase> employeeList = mapper.selectByExample(exampleBase);
                        if (CollectionUtils.isEmpty(employeeList)) {
                        	exist = true;
                        }
                        serialNumberInitialValue++;
        			}
    				employeeNoField.setSerialNumberInitialValue(Long.valueOf(nextEmployNo));
    			} else {
    				employeeNoField.setSerialNumberInitialValue(Long.valueOf(employNo));
    			}
//    			employeeNoField.setSerialNumberInitialValue(Long.parseLong(employNo) + 1);
    			customEmployeeFieldMapper.updateByPrimaryKeySelective(employeeNoField);
    		} else {
    			log.error("工号自动生成规则不完善");
    		}
    	}
	}
	
}
