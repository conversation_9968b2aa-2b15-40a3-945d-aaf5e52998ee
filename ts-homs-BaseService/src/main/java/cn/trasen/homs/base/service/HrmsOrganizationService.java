package cn.trasen.homs.base.service;

import java.util.List;

import cn.trasen.homs.base.bean.HrmsEmployeeResp;
import cn.trasen.homs.base.model.HrmsOrganization;
import cn.trasen.homs.base.model.Organization;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.MyTreeModel;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;

/**   
 * @Title: HrmsOrganizationService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 组织机构 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月9日 下午2:34:48 
 * @version V1.0   
 */
public interface HrmsOrganizationService {

    /**
     * @Title: insert
     * @Description: 新增组织机构
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年4月28日 上午11:55:17
     */
    PlatformResult<String> insert(HrmsOrganization entity);

    /**
     * @Title: update
     * @Description: 修改组织机构
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年4月28日 下午4:42:32
     */
    PlatformResult<String> update(HrmsOrganization entity);
    
    /**
     * @Title: updateEnableStatus
     * @Description: 修改组织机构启用/停用状态
     * @param entity
     * @Return int
     * <AUTHOR>
     * @date 2020年4月30日 下午5:18:51
     */
    int updateEnableStatus(HrmsOrganization entity);
    
    /**
     * @Title: mergeOrganization
     * @Description: 组织机构合并
     * @param entity
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年6月23日 下午2:05:19
     */
    PlatformResult<String> mergeOrganization(HrmsOrganization entity);

    /**
     * @Title: findById
     * @Description: 根据机构ID查询组织机构
     * @param organizationId
     * @Return HrmsOrganization
     * <AUTHOR>
     * @date 2020年4月13日 下午3:41:18
     */
    HrmsOrganization findById(String organizationId);

    /**
     * @Title: getDataSetList
     * @Description: 分页查询组织机构列表
     * @param page
     * @param entity
     * @Return DataSet<HrmsOrganization>
     * <AUTHOR>
     * @date 2020年4月13日 下午3:40:56
     */
    DataSet<HrmsOrganization> getDataSetList(Page page, HrmsOrganization entity);

    /**
     * @Title: getOrgTree
     * @Description: 获取组织机构树
     * @Return List<TreeModel>
     * <AUTHOR>
     * @date 2020年4月13日 下午4:14:25
     */
    List<TreeModel> getOrgTree();

    
    /**
     * @Title: getOrgTree
     * @Description: 获取组织机构树
     * @param level 查询层级
     * @Return List<TreeModel>
     * <AUTHOR>
     * @date 2020年4月13日 下午4:14:25
     */
     List<TreeModel> getOrgTree(Integer level);
    
    /**
     * @Title: getOrgAllList
     * @Description: 获取所有组织机构
     * @Return List<HrmsOrganization>
     * <AUTHOR>
     * @date 2020年4月13日 下午5:27:57
     */
    List<HrmsOrganization> getOrgAllList();
    
    /**
     * @Title: excelImportOrganization
     * @Description: excel导入组织机构
     * @param list
     * @Return PlatformResult<String>
     * <AUTHOR>
     * @date 2020年6月16日 下午3:13:33
     */
    PlatformResult<String> excelImportOrganization(List<HrmsOrganization> list);
    

    List<TreeModel> getOrgTreeAndEmployeeAll();

    /**  
     * <p> @Title: getOrgTree2</p>
     * <p> @Description: 查询组织机构树（不包含禁用的）</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年1月18日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */  
    List<TreeModel>  getOrgTree2(Integer level);
    
    List<TreeModel> getTreeWithMapping(String syscode);
    
    List<TreeModel>  getTreeScduDept(Integer level);
    
    /**  
     * <p> @Title: getOrgTree3</p>
     * <p> @Description: 查询组织机构树（不包含禁用的）</p>
     * <p> @Param: isTmpEmp 是否临时员工 true-是 false - 否</p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年1月18日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */  
    List<MyTreeModel>  getOrgTree3(Integer level,boolean isTmpEmp,String employeeStatus,String archivesType, boolean isAll);

    
    /**  
     * <p> @Title: getHrmsOrganizationList</p>
     * <p> @Description:获取指定组织以及下层所有组织</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年3月22日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */  
     List<HrmsOrganization> getHrmsOrganizationAndNextList(List<String> orgIdList);
     

     List<HrmsOrganization> getHrmsOrganizationList(List<String> orgIdList);
     
    /**
     * <p> @Title: getHrmsOrganizationList</p>
     * <p> @Description:获取指定组织以及下层所有组织</p>
     * <p> @Param: </p>
     * <p> @Return: List<TreeModel></p>
     * <P> @Date: 2021年3月22日  上午11:36:51 </p>
     * <p> <AUTHOR>
     */
     List<String> getHrmsOrganizationAndNextList(String orgIds);

     /**
      * 
      * @MethodName: selectUpOrganizationTree
      * @Description: TODO
      * <AUTHOR>
      * @param organizationId
      * @return List<TreeModel>
      * @date 2022-11-10 05:13:33
      */
    List<TreeModel> selectUpOrganizationTree(String organizationId);

    /**
     * 
     * @MethodName: selectDownOrganizationTree
     * @Description: TODO
     * <AUTHOR>
     * @param organizationId
     * @return List<TreeModel>
     * @date 2022-11-10 05:13:48
     */
    List<TreeModel> selectDownOrganizationTree(String organizationId);

    /**
     * 
     * @MethodName: selectOrganizationTree
     * @Description: TODO
     * <AUTHOR>
     * @param organizationId
     * @return List<TreeModel>
     * @date 2022-11-12 03:37:13
     */
    List<TreeModel> selectOrganizationTree(String organizationId);

    List<ThpsUser> selectUserListByDeptCode(List<String> organizationIdList);

    List<HrmsEmployeeResp> selectEmpListByDeptCode(List<String> organizationIdList);

    List<TreeModel> selectOrgOrganizationTree(String ssoOrgCode);

    /**
     * 
     * @param orgName
     * @return
     */
    HrmsOrganization getOrganizationByName(String orgName);

    /**
     * 
     * @param orgId
     * @return
     */
	List<String> getChildOrgIdsList(String orgId);
}
