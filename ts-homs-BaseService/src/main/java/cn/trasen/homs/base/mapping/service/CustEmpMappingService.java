package cn.trasen.homs.base.mapping.service;

import cn.trasen.homs.base.mapping.model.CustEmpMapping;
import cn.trasen.homs.base.mapping.vo.CustEmpMappingReqVo;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;

/**
 * @ClassName CustEmpMappingService
 * @Description 人员映射服务类
 * @date 2025-04-28 16:23:40
 * <AUTHOR>
 * @version 1.0
 */
public interface CustEmpMappingService {

	/**
	 * @Title initMapping
	 * @Description 初始化映射表
	 * @param record
	 * @return Integer
	 * @date 2025-04-28 16:23:40
	 * <AUTHOR>
	 */
	Integer initMapping(CustEmpMappingReqVo record);
	
	/**
	 * 同步his人员
	 */
	void syncHisUser(String syscode);

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	Integer save(CustEmpMapping record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	Integer update(CustEmpMapping record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CustEmpStorage
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	CustEmpMapping selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CustEmpMapping>
	 * @date 2025-04-29 14:23:40
	 * <AUTHOR>
	 */
	DataSet<CustEmpMapping> getDataSetList(Page page, CustEmpMapping record);

}
