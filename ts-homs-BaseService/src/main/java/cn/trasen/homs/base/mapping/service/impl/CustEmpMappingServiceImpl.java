package cn.trasen.homs.base.mapping.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.base.customEmployee.dao.CustomEmployeeBaseMapper;
import cn.trasen.homs.base.customEmployee.model.CustomEmployeeBase;
import cn.trasen.homs.base.mapping.dao.CustEmpMappingMapper;
import cn.trasen.homs.base.mapping.dao.CustEmpSyncMapper;
import cn.trasen.homs.base.mapping.model.CustEmpMapping;
import cn.trasen.homs.base.mapping.model.CustEmpSync;
import cn.trasen.homs.base.mapping.service.CustEmpMappingService;
import cn.trasen.homs.base.mapping.vo.CustEmpMappingReqVo;
import cn.trasen.homs.bean.hrms.CommInterfaceRegisterResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.hrms.CommInterfaceRegisterFeignService;
import cn.trasen.homs.utils.HttpClient;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CustEmpMappingServiceImpl
 * @Description 人员映射服务实现类
 * @date 2025-04-28 16:23:40
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CustEmpMappingServiceImpl implements CustEmpMappingService {
	
	@Value("${hisRequestVersion:}")
	private String hisRequestVersion;

	@Autowired
	private CustEmpMappingMapper mapper;
	
	@Autowired
	private CustomEmployeeBaseMapper customEmployeeBaseMapper;
	
	@Autowired
	private CustEmpSyncMapper custEmpSyncMapper;
	
	@Autowired
	private CommInterfaceRegisterFeignService commInterfaceRegisterFeignService;
	
	/**
	 * 人员映射唯一标识，1-身份证，2-手机号，3-工号；默认身份证
	 */
	@Value("${emp-mapping-unique-identifier:1}")
	private String empMappingUniqueIdentifier;
	
	/**
	 * 同步his的人员
	 */
	@Transactional(readOnly = false)
	@Override
	public void syncHisUser(String syscode){
		if(ObjectUtils.isEmpty(syscode)){
			syscode = "HIS";
		}
		// 从his查询科室列表，同步到  jc_bas_org_department 中 id存在则更新，否则就新增
		DataSet<CommInterfaceRegisterResp> dataSet = commInterfaceRegisterFeignService.selectCommInterfaceRegisterList("2", "集成平台-查询医师处方权限", "1");
		List<CommInterfaceRegisterResp> list = dataSet.getRows();
		if(CollUtil.isNotEmpty(list)){
			CommInterfaceRegisterResp commInterfaceRegister = list.get(0);

        	JSONArray jsonArray = queryMember(commInterfaceRegister);
        	log.info("获取人员的数据：" + jsonArray.toJSONString());
        	
        	if(null != jsonArray && jsonArray.size() > 0) {
        		for (int i = 0; i < jsonArray.size(); i++) {
        			CustEmpSync custEmpSync = new CustEmpSync();
        			JSONObject obj = jsonArray.getJSONObject(i);
        			String busEmployeeId = obj.getString("id");
        			custEmpSync.setBusEmployeeId(busEmployeeId);
        			custEmpSync.setSyscode(syscode);
        			custEmpSync.setIdentityNumber(obj.getString("idCard"));//身份证号
        			custEmpSync.setPhoneNumber(obj.getString("phone"));//手机号
        			custEmpSync.setBusEmployeeNo(obj.getString("code"));//工号
        			custEmpSync.setBusEmployeeName(obj.getString("name"));//员工姓名
        			custEmpSync.setBusOrgCode(obj.getString("orgCode"));//机构编码
        			
        			String memberDeptsStr = obj.getString("memberDepts");
        			if(!ObjectUtils.isEmpty(memberDeptsStr)){
        				JSONObject memberDepts = JSON.parseObject(memberDeptsStr);
        				for(int j = 0; j < memberDepts.size(); j++){
        					JSONObject o = jsonArray.getJSONObject(i);
        					String masterFlag = o.getString("masterFlag");//是否主工作科室(默认科室)  N-否 Y-是
        					if(!ObjectUtils.isEmpty(masterFlag) && "Y".equals(masterFlag)){
			        			custEmpSync.setBusDeptId(obj.getString("deptId"));//员工所属科室ID
			        			custEmpSync.setBusDeptCode(obj.getString("deptCode"));//员工所属科室编码
			        			custEmpSync.setBusDeptName(obj.getString("deptName"));//员工所属科室名称
        						break;
        					}
        				}
        			}
        			custEmpSync.setIsEnable(obj.getString("status"));//1-有效 2-无效
        			custEmpSync.setIsDeleted(obj.getString("isDelete"));//删除标示  N-否 Y-是
        			if(custEmpSyncMapper.selectByPrimaryKey(busEmployeeId) != null){ //更新
        				custEmpSyncMapper.updateByPrimaryKeySelective(custEmpSync);
        			} else { //新增
        				custEmpSyncMapper.insertSelective(custEmpSync);
        			}
        		}
        	}
		}
	}
	
	/**
	 * 查询his人员数据
	 * @param commInterfaceRegister
	 * @return
	 */
	private JSONArray queryMember(CommInterfaceRegisterResp commInterfaceRegister){
		Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
		log.info("===========开始获取人员数据================");
		Map<String,Object> requestParams = new HashMap<>();
		//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
		if(StringUtils.isNotBlank(hisRequestVersion) && "2".equals(hisRequestVersion)) {
			requestParams.put("IsCompress", "false");
			requestParams.put("ServiceName", "OuterPatPayService");//服务名
			requestParams.put("InterfaceName", "QueryOutPatient");//接口名
			requestParams.put("TimeOut", 15000);//超时时间
			
			Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	       	paramsMap.put("pageIndex", "1");
	       	paramsMap.put("pageSize", "1000000");
	    	paramsMap.put("sortOrder", "0");
	       	
	       	requestParams.put("Parameter",paramsMap);
	       	
		}else {
			requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
			requestParams.put("pageIndex", "1");
			requestParams.put("pageSize", "1000000");
			requestParams.put("sortOrder", "0");
		}
		
		String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
		
		String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
		
		String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
		
		JSONObject reuslt = JSON.parseObject(bodyStr);
		
		JSONArray jsonArray = new JSONArray();
		if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
			JSONObject data = reuslt.getJSONObject("data");
			JSONObject Value = data.getJSONObject("Value");
			jsonArray = Value.getJSONArray("list");
		}else {
			jsonArray = reuslt.getJSONArray("list");
		}
		log.info("===========获取人员数据结束================");
		return jsonArray;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer initMapping(CustEmpMappingReqVo record) {
		//同步his人员数据
		syncHisUser(record.getSyscode());
		//根据查询条件查询业务系统人员同步表数据
		List<CustEmpSync> custEmpSyncList = mapper.getCustEmpSyncList(record);
		//将人员根据系统编码及唯一标识整理成map
		Map<String, CustEmpSync> custEmpSyncMap = new HashMap<String, CustEmpSync>();
		if(!custEmpSyncList.isEmpty()){
			for(CustEmpSync sync : custEmpSyncList){
				String uniqueIdentifier = "";
				if(empMappingUniqueIdentifier.equals("1")){
					uniqueIdentifier = sync.getIdentityNumber();
				} else if(empMappingUniqueIdentifier.equals("2")){
					uniqueIdentifier = sync.getPhoneNumber();
				} else if(empMappingUniqueIdentifier.equals("3")){
					uniqueIdentifier = sync.getBusEmployeeNo();
				} 
				if(!ObjectUtils.isEmpty(uniqueIdentifier)){
					custEmpSyncMap.put(sync.getSyscode() + "&" + uniqueIdentifier, sync);
				}
			}
		} else {
			throw new BusinessException("业务系统人员为空！");
		}
		//查询所有的人员表
		CustomEmployeeBase custEmpRecord = new CustomEmployeeBase();
		//根据当前登录账号机构编码过滤
		custEmpRecord.setSsoOrgCode(record.getBusOrgCode());
		List<CustomEmployeeBase> custEmpBaseList = customEmployeeBaseMapper.getEmployeeBaseList(custEmpRecord);
		Map<String, CustomEmployeeBase> custEmpBaseMap = new HashMap<String, CustomEmployeeBase>();
		if(!custEmpBaseList.isEmpty()){
			for(CustomEmployeeBase custEmpBase : custEmpBaseList){
				String uniqueIdentifier = "";
				if(empMappingUniqueIdentifier.equals("1")){
					uniqueIdentifier = custEmpBase.getIdentityNumber();
				} else if(empMappingUniqueIdentifier.equals("2")){
					uniqueIdentifier = custEmpBase.getPhoneNumber();
				} else if(empMappingUniqueIdentifier.equals("3")){
					uniqueIdentifier = custEmpBase.getEmployeeNo();
				} 
				if(!ObjectUtils.isEmpty(uniqueIdentifier)){
					custEmpBaseMap.put(uniqueIdentifier, custEmpBase);
				}
			}
		}
		//查询所有的人员映射表
		List<CustEmpMapping> custEmpMappingList = mapper.selectAll();
		Map<String, CustEmpMapping> custEmpMappingMap = new HashMap<String, CustEmpMapping>();
		if(!custEmpMappingList.isEmpty()){
			for(CustEmpMapping custEmpMapping : custEmpMappingList){
				String key = custEmpMapping.getSyscode() + "&" + custEmpMapping.getBaseEmployeeId() + "&" + custEmpMapping.getMapEmployeeId();
				custEmpMappingMap.put(key, custEmpMapping);
			}
		}
		//遍历人员同步表数据初始化到人员映射表中
		if(custEmpSyncMap.size() > 0 && custEmpSyncMap.size() > 0){
			for(String key : custEmpSyncMap.keySet()){
				String identityNumber = key.split("&")[1];
				if(custEmpBaseMap.containsKey(identityNumber)){
					CustomEmployeeBase customEmployeeBase = custEmpBaseMap.get(identityNumber);
					CustEmpSync custEmpSync = custEmpSyncMap.get(key);
					//将映射数据插入到映射表中，先判断映射表中是否有数据，有则更新，否则新增，删除怎么操作？是否传过来删除标识？
					String mappingKey = custEmpSync.getSyscode() + "&" + customEmployeeBase.getEmployeeId() + "&" + custEmpSync.getBusEmployeeId();
					if(!custEmpMappingMap.containsKey(mappingKey)){ //新增
						CustEmpMapping mapping = new CustEmpMapping();
						mapping = generateMapping(customEmployeeBase, custEmpSync, mapping, true);
						mapper.insertSelective(mapping);
					} else { //更新
						CustEmpMapping mapping = custEmpMappingMap.get(mappingKey);
						mapping = generateMapping(customEmployeeBase, custEmpSync, mapping, false);
						mapper.updateByPrimaryKeySelective(mapping);
					}
					
				}
			}
		}
		return 1;
	}
	
	/**
	 * 组装渲染映射对象
	 * 
	 * @param customEmployeeBase 医务系统人员对象
	 * @param custEmpSync 业务系统人员对象
	 * @param mapping 映射对象
	 * @param isAdd 是否新增，true-新增，false-更新
	 * @return
	 */
	private CustEmpMapping generateMapping(CustomEmployeeBase customEmployeeBase, CustEmpSync custEmpSync, CustEmpMapping mapping, boolean isAdd){
		mapping.setPhoneNumber(custEmpSync.getPhoneNumber());
		mapping.setBaseEmployeeNo(customEmployeeBase.getEmployeeNo());
		mapping.setBaseEmployeeName(customEmployeeBase.getEmployeeName());
		mapping.setBaseDeptId(customEmployeeBase.getOrgId());
		mapping.setBaseDeptCode(customEmployeeBase.getOrgCode());
		mapping.setBaseDeptName(customEmployeeBase.getOrgName());
		mapping.setBaseOrgCode(customEmployeeBase.getSsoOrgCode());
		
		mapping.setMapEmployeeNo(custEmpSync.getBusEmployeeNo());
		mapping.setMapEmployeeName(custEmpSync.getBusEmployeeName());
		mapping.setMapDeptId(custEmpSync.getBusDeptId());
		mapping.setMapDeptCode(custEmpSync.getBusDeptCode());
		mapping.setMapDeptName(custEmpSync.getBusDeptName());
		
		mapping.setUpdateDate(new Date());
		mapping.setIsEnable(custEmpSync.getIsEnable());
		mapping.setIsDeleted(custEmpSync.getIsDeleted());
		if(isAdd){
			mapping.setId(IdGeneraterUtils.nextId());
			mapping.setSyscode(custEmpSync.getSyscode());
			mapping.setIdentityNumber(customEmployeeBase.getIdentityNumber());
			mapping.setBaseEmployeeId(customEmployeeBase.getEmployeeId());
			mapping.setMapEmployeeId(custEmpSync.getBusEmployeeId());
			mapping.setCreateDate(new Date());
		}
		return mapping;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer save(CustEmpMapping record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setUpdateDate(new Date());
		record.setCreateDate(new Date());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CustEmpMapping record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CustEmpMapping record = new CustEmpMapping();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
//		return mapper.deleteByPrimaryKey(id);
	}

	@Override
	public CustEmpMapping selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CustEmpMapping> getDataSetList(Page page, CustEmpMapping record) {
		Example example = new Example(CustEmpMapping.class);
		Example.Criteria criteria = example.createCriteria();
		//应用编码
		String syscode = record.getSyscode();
		if(!ObjectUtils.isEmpty(syscode)){
//			criteria.andEqualTo("syscode", syscode);
			criteria.andLike("syscode", "%" + syscode + "%");
		}
		//oa系统员工编码
		String baseEmployeeNo = record.getBaseEmployeeNo();
		if(!ObjectUtils.isEmpty(baseEmployeeNo)){
			criteria.andLike("baseEmployeeNo", "%" + baseEmployeeNo + "%");
		}
		//业务系统员工编码
		String mapEmployeeNo = record.getMapEmployeeNo();
		if(!ObjectUtils.isEmpty(mapEmployeeNo)){
			criteria.andLike("mapEmployeeNo", "%" + mapEmployeeNo + "%");
		}
		//身份证号
		String identityNumber = record.getIdentityNumber();
		if(!ObjectUtils.isEmpty(identityNumber)){
			criteria.andLike("identityNumber", "%" + identityNumber + "%");
		}
		//所属机构编码
		String baseOrgCode = record.getBaseOrgCode();
		if(!ObjectUtils.isEmpty(baseOrgCode)){
			criteria.andLike("baseOrgCode", "%" + baseOrgCode + "%");
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CustEmpMapping> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

}
