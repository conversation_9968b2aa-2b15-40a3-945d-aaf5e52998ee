package cn.trasen.homs.base.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.base.bean.ModulePermissionsListRes;
import cn.trasen.homs.base.service.ModuleMenuService;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:19
 */

@RestController
@Api(tags = "模块菜单")
@RequestMapping("/moduleMenu")
public class ModuleMenuController {

    @Autowired
    ModuleMenuService moduleMenuService;

    @ApiOperation(value = "", notes = "读取我的显示菜单")
    @RequestMapping(value = "/getMyMenu", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<ModulePermissionsListRes> getMyMenu() {
        return PlatformResult.success(moduleMenuService.getMyMenu());
    }


    @ApiOperation(value = "", notes = "读取个人设置显示菜单")
    @RequestMapping(value = "/getMyConfigMenu", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<ModulePermissionsListRes> getMyConfigMenu() {
        return PlatformResult.success(moduleMenuService.getMyConfigMenu());
    }


    @ApiOperation(value = "", notes = "读取模块显示菜单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "module", value = "模块ID", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getMenu", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<ModulePermissionsListRes> getMenu(@RequestParam("module") String module) {
        return PlatformResult.success(moduleMenuService.getMenu(module));
    }
}