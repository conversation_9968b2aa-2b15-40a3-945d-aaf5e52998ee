<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapping.dao.CustEmpMappingMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.base.mapping.model.CustEmpMapping">
    <id column="id" jdbcType="VARCHAR" property="id" />
  </resultMap>
  
  <select id="getCustEmpSyncList"  resultType="cn.trasen.homs.base.mapping.model.CustEmpSync" parameterType="cn.trasen.homs.base.mapping.vo.CustEmpMappingReqVo">
		select syscode, identity_number, phone_number, bus_employee_id, bus_employee_no, bus_employee_name, bus_org_code, bus_dept_id, 
		bus_dept_code, bus_dept_name, sync_date, is_enable, is_deleted 
		from cust_emp_sync where 1=1
		<if test="syscode != null and syscode != ''">
			and syscode =#{syscode}
		</if>
		<if test="busOrgCode != null and busOrgCode != ''">
			and bus_org_code =#{busOrgCode}
		</if>
		<if test="startSyncDate != null and startSyncDate != ''">
	  		and sync_date &gt;= TO_DATE(#{startSyncDate}, 'yyyy-MM-dd')
	  	</if>
	  	<if test="endSyncDate != null and endSyncDate != ''">
	  		and sync_date &lt;= TO_DATE(#{endSyncDate}, 'yyyy-MM-dd')
	  	</if> 
	</select>
</mapper>