UPDATE hrms_employee t1
JOIN toa_employee t2 ON t1.employee_id = t2.id and t2.open_id != '' and t2.open_id is not null
SET t1.open_id = t2.open_id;

update toa_employee set open_id = null;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'organization_parttime_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `organization_parttime_id` VARCHAR(50)  NULL COMMENT ''兼职科室id'' ',
                   'select ''INFO: organization_parttime_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'organization_parttime_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `organization_parttime_name` VARCHAR(50)  NULL  COMMENT ''兼职科室名称'' ',
                   'select ''INFO: organization_parttime_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

UPDATE hrms_employee t1
JOIN toa_employee t2 ON t1.employee_id = t2.id and t2.organization_parttime_id != '' and t2.organization_parttime_id is not null
SET t1.organization_parttime_id = t2.organization_parttime_id;

UPDATE hrms_employee t1
JOIN toa_employee t2 ON t1.employee_id = t2.id and t2.organization_parttime_name != '' and t2.organization_parttime_name is not null
SET t1.organization_parttime_name = t2.organization_parttime_name;

update toa_employee set organization_parttime_id = null;

update toa_employee set organization_parttime_name = null;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_task_his' and COLUMN_NAME = 'approval_duration' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_task_his` ADD `approval_duration` int(11)  DEFAULT 0 COMMENT ''审批时长（分）'' ',
                   'select ''INFO: approval_duration 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_instance_info' and COLUMN_NAME = 'total_duration' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_instance_info` ADD `total_duration` int(11)  DEFAULT 0 COMMENT ''总审批时长（分）'' ',
                   'select ''INFO: total_duration 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'comm_organization' and COLUMN_NAME = 'platform_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_organization` ADD `platform_id` VARCHAR(50)  COMMENT ''集成平台id'' ',
                   'select ''INFO: platform_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'comm_organization' and COLUMN_NAME = 'org_type' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_organization` ADD `org_type` VARCHAR(50)  COMMENT ''科室类别'' ',
                   'select ''INFO: org_type 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS comm_interface_register
(
   id                   varchar(50) not null comment '主键',
   module_name          varchar(50) comment '应用模块',
   call_type            varchar(1) comment '数据调用方式 1主动推送  2第三方拉取',
   interface_name       varchar(50) comment '接口名称',
   interface_ip         varchar(50) comment '服务器ip端口',
   interface_address    varchar(200) comment '接口地址',
   post_type            varchar(10) comment '请求方式',
   params_example       text comment '入参示例',
   response_example     text comment '出差示例',
   header_params        varchar(1000) comment '请求头参数',
   status               varchar(1) comment '状态  1启用  2停用',
   cipher               varchar(50) DEFAULT 'khel3@hkzlv8!' comment '密钥',
   is_sys          		varchar(1) DEFAULT '0' comment '是否系统级 0否 1是',
   platform_org_code    varchar(50) comment '集成平台医院编码',
   platform_org_name    varchar(50) comment '集成平台医院名称',
   platform_hosp_code   varchar(50) comment '集成平台院区编码',
   platform_hosp_name   varchar(50) comment '集成平台院区名称',
   platform_app_id      varchar(50) comment '集成平台appid',
   platform_sign        varchar(50) comment '集成平台sign',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '接口登记';

CREATE TABLE IF NOT EXISTS comm_interface_logs
(
   id                   varchar(50) not null comment '主键',
   register_id          varchar(50) comment '登记id',
   interface_name       varchar(50) comment '调用接口名称',
   interwork_platform   varchar(50) comment '互通平台',
   request_url          varchar(200) comment '请求路径',
   request_params       text comment '请求参数',
   response_params      longtext comment '响应参数',
   take_time            varchar(10) comment '调用耗时',
   response_status      varchar(1) comment '调用状态',
   create_date          datetime comment '调用时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '接口请求日志';

CREATE TABLE IF NOT EXISTS `wf_back_stepinfo`  (
  `id` varchar(50)  NOT NULL,
  `wf_instance_id` varchar(50)  NULL DEFAULT NULL,
  `back_step_id` varchar(50)  NULL DEFAULT NULL,
  `back_step_name` varchar(100)  NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);


set @exist := (select count(1) from information_schema.columns
               where table_name = 'comm_employee_field_group' and COLUMN_NAME = 'show_delete' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_employee_field_group` ADD `show_delete` int(1) DEFAULT NULL COMMENT ''明细保留一组必填数据，删除按钮不显示 0 可删除 1不可删除''',
                   'select ''INFO: show_delete 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'TOA_INFORMATION' and COLUMN_NAME = 'TITLE_ALIGN' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`TOA_INFORMATION` ADD `TITLE_ALIGN` VARCHAR(10) DEFAULT NULL COMMENT ''标题位置'' ',
                   'select ''INFO: TITLE_ALIGN 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


create table IF NOT EXISTS comm_holiday_year
(
   id                   varchar(50) not null,
   name                 varchar(50) comment '节假日名称',
   vacation             varchar(1000) comment '节假日数组',
   remark               varchar(1000) comment '调休日数组',
   create_date          datetime comment '创建时间',
   primary key (id)
)COMMENT = '节假日数据';


CREATE TABLE IF NOT EXISTS `comm_contact`  (
  `id` varchar(50)  NOT NULL,
  `user_code` varchar(50)  NULL DEFAULT NULL,
  `contact_code` varchar(50)  NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
)COMMENT = '选人组件常用联系人';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_document_channel' and COLUMN_NAME = 'scope' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document_channel` ADD `scope` VARCHAR(20) DEFAULT NULL COMMENT ''范围'' ',
                   'select ''INFO: scope 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_document_channel' and COLUMN_NAME = 'CHANNEL_SIZE' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document_channel` ADD `CHANNEL_SIZE` INT DEFAULT 0 COMMENT ''栏目允许上传大小(M)'' ',
                   'select ''INFO: CHANNEL_SIZE 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_document_channel' and COLUMN_NAME = 'tree_ids' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document_channel` ADD `tree_ids` VARCHAR(2000) DEFAULT NULL COMMENT ''关联树id'' ',
                   'select ''INFO: tree_ids 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'TOA_DOCUMENT' and COLUMN_NAME = 'CREATE_SHARE_NAME' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`TOA_DOCUMENT` ADD `CREATE_SHARE_NAME` VARCHAR(50) DEFAULT NULL COMMENT ''分享人名称'' ',
                   'select ''INFO: CREATE_SHARE_NAME 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'TOA_DOCUMENT' and COLUMN_NAME = 'CREATE_SHARE_USER' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`TOA_DOCUMENT` ADD `CREATE_SHARE_USER` VARCHAR(50) DEFAULT NULL COMMENT ''分享人编码'' ',
                   'select ''INFO: CREATE_SHARE_USER 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


create table IF NOT EXISTS  toa_document_operate
(
   id                   varchar(50) not null comment '主键',
   opt_id				varchar(50) comment '操作业务id',
   opt_content          varchar(500) comment '操作内容',
   opt_remark           varchar(500) comment '操作备注',
   create_date          datetime default NULL comment '创建时间',
   create_user          varchar(50) default NULL comment '创建人',
   create_user_name     varchar(50) default NULL comment '创建人名称',
   create_dept          varchar(50) comment '创建部门编码',
   create_dept_name     varchar(50) comment '创建部门名称',
   update_user          varchar(50) default NULL comment '更新人',
   update_user_name     varchar(50) default NULL comment '更新人名称',
   update_date          datetime default NULL comment '更新时间',
   is_deleted           varchar(1) default NULL comment '删除标示',
   sso_org_code         varchar(50) comment '机构编码',
   sso_org_name         varchar(50) comment '机构名称',
   primary key (id)
)COMMENT = '文档管理操作日志表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_callback_parameter_info' and COLUMN_NAME = 'callback_type' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_callback_parameter_info` ADD `callback_type` VARCHAR(20) DEFAULT NULL COMMENT ''回调类型'' ',
                   'select ''INFO: callback_type 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_definition_info' and COLUMN_NAME = 'submit_url' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_definition_info` ADD `submit_url` VARCHAR(200) DEFAULT NULL COMMENT ''发起前校验url'' ',
                   'select ''INFO: submit_url 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_step_info' and COLUMN_NAME = 'organization_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_step_info` ADD `organization_name` VARCHAR(50) DEFAULT NULL COMMENT ''组织名称'' ',
                   'select ''INFO: organization_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_step_info' and COLUMN_NAME = 'organization_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_step_info` ADD `organization_id` VARCHAR(50) DEFAULT NULL COMMENT ''组织id'' ',
                   'select ''INFO: organization_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'wf_instance_info' and COLUMN_NAME = 'APPROVAL_DURATION' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_instance_info` ADD `APPROVAL_DURATION` int(11)  DEFAULT 0 COMMENT ''是否计算过审批时长 0否 1是'' ',
                   'select ''INFO: APPROVAL_DURATION 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

create table IF NOT EXISTS `ts_base_oa`.comm_modes_uses (
	ID varchar(36) not null comment '唯一标识',
	model_id varchar(50) comment '模块id',
	model_name varchar(50) comment '模块名称',
	model_urls	varchar(100) comment '模块url地址',
	user_id	varchar(50) comment '用户id',
	user_name varchar(50) comment '用户名称',
	dept_id varchar(50) comment '科室id',
	dept_name varchar(50) comment '科室名称',
	access_time datetime comment '访问时间',
	teram_type int comment '终端类型，1为PC端，2为移动端',
	primary key(id)
)
engine=innodb comment='用户访问功能模块清单';


CREATE TABLE IF NOT EXISTS    toa_leader_daily_read
(
	id VARCHAR(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
	daily_id VARCHAR(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '日报id',
	read_number INT COLLATE utf8mb4_general_ci COMMENT '阅读次数',
	create_date DATETIME COLLATE utf8mb4_general_ci COMMENT '创建日期',
	create_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人',
	create_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人姓名',
	update_date DATETIME COLLATE utf8mb4_general_ci COMMENT '更新日期',
	update_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人',
	update_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人姓名',
	is_deleted CHAR(1) COLLATE utf8mb4_general_ci COMMENT '是否删除',
	sso_org_code VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '机构编码',
	PRIMARY KEY (id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='领导日报表阅读记录表';

CREATE TABLE IF NOT EXISTS toa_leader_daily
(
	id VARCHAR(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
	daily_begin_date VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '日报开始日期',
	daily_end_date VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '日报结束日期',
	data_storage text COLLATE utf8mb4_general_ci COMMENT '数据存储',
	type VARCHAR(10) COLLATE utf8mb4_general_ci COMMENT '类型:1日,2周,3月',
	files VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '附件id',
	create_date DATETIME COLLATE utf8mb4_general_ci COMMENT '创建日期',
	create_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人',
	create_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人姓名',
	update_date DATETIME COLLATE utf8mb4_general_ci COMMENT '更新日期',
	update_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人',
	update_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人姓名',
	is_deleted CHAR(1) COLLATE utf8mb4_general_ci COMMENT '是否删除',
	sso_org_code VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '机构编码',
	remarks VARCHAR(1000) COLLATE utf8mb4_general_ci,
	PRIMARY KEY (id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='领导日、周、月报表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_sys_setting' and COLUMN_NAME = 'leader_daily_user_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD `leader_daily_user_code` VARCHAR(1000) COLLATE utf8mb4_general_ci COMMENT ''领导日报推送人code'' ',
                   'select ''INFO: leader_daily_user_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_sys_setting' and COLUMN_NAME = 'leader_daily_user_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD `leader_daily_user_name` VARCHAR(1000) COLLATE utf8mb4_general_ci COMMENT ''领导日报推送人姓名'' ',
                   'select ''INFO: leader_daily_user_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'civil_affairs_change_expenditure' and COLUMN_NAME = 'remarks' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_expenditure` ADD `remarks` VARCHAR(1000) COLLATE utf8mb4_general_ci COMMENT ''备注'' ',
                   'select ''INFO: remarks 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'civil_affairs_change_return' and COLUMN_NAME = 'serial_number' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_return` ADD `serial_number` VARCHAR(100) COLLATE utf8mb4_general_ci COMMENT ''退还流水号'' ',
                   'select ''INFO: serial_number 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS  civil_admission_transfer_ward
    (
        id VARCHAR(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
        admission_id VARCHAR(36) COLLATE utf8mb4_general_ci COMMENT '入院id',
        transfer_date VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '转科日期',
		before_ward VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '变更前病区',
        after_ward VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '变更后病区',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人',
        create_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人',
        update_user_name VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '更新人姓名',
        is_deleted CHAR(1) COLLATE utf8mb4_general_ci COMMENT '是否删除',
        sso_org_code VARCHAR(50) COLLATE utf8mb4_general_ci COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='入院变更病区记录';