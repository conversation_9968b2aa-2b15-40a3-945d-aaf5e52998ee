CREATE TABLE IF NOT EXISTS `comm_post` (
  `post_id` varchar(36) NOT NULL COMMENT '主键ID',
  `post_name` varchar(100) DEFAULT NULL COMMENT '岗位名称',
  `post_category` varchar(36) DEFAULT NULL COMMENT '岗位类别',
  `is_enable` char(1) DEFAULT '1' COMMENT '是否启用: 1=是; 0=否;',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `enterprise_id` varchar(36) DEFAULT NULL COMMENT '企业ID',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `upgrade_time` int(11) DEFAULT NULL COMMENT '升级时间',
  `upgrade_system` varchar(10) DEFAULT NULL COMMENT '是否在升级体系（1是 0否）',
  `upgrade_no` int(11) DEFAULT NULL COMMENT '排序号（确定下一级）',
  `upgrade_post_id` varchar(36) DEFAULT NULL COMMENT '升级到的级别ID',
  `sort_no` int(11) DEFAULT NULL,
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='岗位表';

CREATE TABLE IF NOT EXISTS `comm_post_wage` (
  `post_wage_id` varchar(36) NOT NULL COMMENT '主键ID',
  `post_id` varchar(36) DEFAULT NULL COMMENT '岗位ID',
  `post_wage` decimal(18,2) DEFAULT NULL COMMENT '岗位工资',
  `performance_wage` decimal(18,2) DEFAULT NULL COMMENT '绩效工资',
  `award_wage` decimal(18,2) DEFAULT NULL COMMENT '奖励性绩效',
  `is_enable` char(1) DEFAULT NULL COMMENT '是否启用: 1=是; 2=否;',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `enterprise_id` varchar(36) DEFAULT NULL COMMENT '企业ID',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`post_wage_id`) USING BTREE,
  KEY `FK_POST_POSTWAGE` (`post_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='岗位工资表';

CREATE TABLE IF NOT EXISTS `hrms_salary_level` (
  `salary_level_id` varchar(36) NOT NULL COMMENT '主键ID',
  `salary_level_name` varchar(100) DEFAULT NULL COMMENT '薪级名称',
  `salary_level_category` char(2) DEFAULT NULL COMMENT '薪级类别',
  `grade` int(11) DEFAULT NULL COMMENT '等级',
  `is_enable` char(1) DEFAULT NULL COMMENT '是否启用: 1=是; 2=否;',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `enterprise_id` varchar(36) DEFAULT NULL COMMENT '企业ID',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(64) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(64) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`salary_level_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪级表';

CREATE TABLE IF NOT EXISTS `hrms_salary_level_wage` (
  `salary_level_wage_id` varchar(36) NOT NULL COMMENT '主键ID',
  `salary_level_id` varchar(36) DEFAULT NULL COMMENT '薪级ID',
  `salary_level_wage` decimal(18,2) DEFAULT NULL COMMENT '薪级工资',
  `is_enable` char(1) DEFAULT NULL COMMENT '是否启用: 1=是; 2=否;',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `enterprise_id` varchar(36) DEFAULT NULL COMMENT '企业ID',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(64) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(64) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`salary_level_wage_id`) USING BTREE,
  KEY `FK_SALARYLEVEL_SALARYLEVELWAGE` (`salary_level_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪级工资表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'preparation_no' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `preparation_no` varchar(64) NULL DEFAULT NULL COMMENT ''编制号(平江同步用友)'' ',
                   'select ''INFO: preparation_no 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'organization' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `organization` varchar(64) NULL DEFAULT NULL COMMENT ''组织号(平江同步奖金系统)'' ',
                   'select ''INFO: organization 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_level_wage' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_level_wage` ADD `sso_org_code` varchar(64) NULL DEFAULT NULL COMMENT ''机构编码'' ',
                   'select ''INFO: sso_org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_level_wage' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_level_wage` ADD `sso_org_name` varchar(64) NULL DEFAULT NULL COMMENT ''机构名称'' ',
                   'select ''INFO: sso_org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_basic_column` (
    `id` varchar(50) NOT NULL COMMENT '主键id',
    `basic_item_name` varchar(50) DEFAULT NULL COMMENT '基本定薪项名称',
    `basic_item_type` char(1) DEFAULT NULL COMMENT '项目类型1基础信息2工资项3其他',
    `emp_field` varchar(100) DEFAULT NULL COMMENT '员工档案字段',
    `POSITION` char(1) DEFAULT NULL COMMENT '小数位',
    `remark` varchar(200) DEFAULT NULL COMMENT '备注',
    `number_sort` int(11) DEFAULT NULL COMMENT '排序',
    `compare` char(1) DEFAULT NULL COMMENT '1 需要对比',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬档案基本列表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_basic_item` (
     `id` varchar(50) NOT NULL COMMENT 'id',
     `group_id` varchar(50) DEFAULT NULL COMMENT '项分组id',
     `item_name` varchar(50) DEFAULT NULL COMMENT '薪酬项目名称',
     `item_code` varchar(50) DEFAULT NULL COMMENT '薪酬项目编码',
     `item_type` char(1) DEFAULT NULL COMMENT '1基础信息2工资项',
     `item_digit` char(2) DEFAULT NULL COMMENT '小数位',
     `count_type` char(1) DEFAULT NULL COMMENT '1加项 2减项3不参与计算',
     `item_rule` char(1) DEFAULT NULL COMMENT '1手工录入2固定值3从薪资档案取4自定义公式',
     `count_formula` varchar(500) DEFAULT NULL COMMENT '计算公式',
     `count_formula_text` varchar(500) DEFAULT NULL COMMENT '计算公式文本',
     `salary_item_amount` decimal(18,2) DEFAULT NULL COMMENT '固定金额',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬项目基础库';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_basicitem_emp` (
     `id` varchar(50) NOT NULL COMMENT '主键id',
     `employee_id` varchar(50) DEFAULT NULL COMMENT '人员id',
     `basic_item_id` varchar(50) DEFAULT NULL COMMENT '基本定薪项id',
     `basic_item_type` char(1) DEFAULT NULL COMMENT '类型',
     `emp_field` varchar(50) DEFAULT NULL COMMENT '字段名称',
     `emp_field_value` varchar(50) DEFAULT NULL COMMENT '定薪字段值',
     `salary_amount` decimal(18,2) DEFAULT NULL COMMENT '定薪金额',
     `reason` varchar(50) DEFAULT NULL COMMENT '调薪原因',
     `effective_date` varchar(50) DEFAULT NULL COMMENT '生效日期',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     `emp_field_value_text` varchar(50) DEFAULT NULL,
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='人员定薪表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_basicitem_emp_history` (
     `id` varchar(50) NOT NULL COMMENT '主键id',
     `employee_id` varchar(50) DEFAULT NULL COMMENT '人员id',
     `basic_item_id` varchar(50) DEFAULT NULL COMMENT '基本定薪项id',
     `emp_field` varchar(50) DEFAULT NULL COMMENT '字段名称',
     `salary_amount` decimal(18,2) DEFAULT NULL COMMENT '定薪金额',
     `reason` varchar(50) DEFAULT NULL COMMENT '调薪原因',
     `effective_date` varchar(50) DEFAULT NULL COMMENT '生效日期',
     `v_number` varchar(50) DEFAULT NULL COMMENT '历史版本号从1开始 自增长',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `data_status` char(1) DEFAULT NULL COMMENT '0 未生效1已生效 2已过期',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     `emp_field_value` varchar(50) DEFAULT NULL COMMENT '字段值',
     `emp_field_value_text` varchar(50) DEFAULT NULL,
     `basic_item_type` char(1) DEFAULT NULL COMMENT '类型',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='人员定薪历史表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_controls` (
    `id` varchar(50) NOT NULL COMMENT '主键id',
    `send_month` varchar(50) DEFAULT NULL COMMENT '月份',
    `option_id` varchar(50) DEFAULT NULL COMMENT '薪酬组id',
    `accounting_cycle` varchar(50) DEFAULT NULL COMMENT '算薪周期',
    `accounting_status` varchar(50) DEFAULT NULL COMMENT '1未启动2已计算3已完成4已锁定5解除锁定',
    `is_send` varchar(50) DEFAULT NULL COMMENT '是否发送工资条 0 未发送 1已发送',
    `remark` varchar(200) DEFAULT NULL COMMENT '备注',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬控制表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_firststep_history` (
    `id` varchar(50) NOT NULL COMMENT 'id',
    `payroll_date` varchar(50) DEFAULT NULL COMMENT '发放月份',
    `option_id` varchar(50) DEFAULT NULL COMMENT '薪酬方案id',
    `ydqk` varchar(50) DEFAULT NULL COMMENT '异动情况',
    `employee_no` varchar(50) DEFAULT NULL COMMENT '工号',
    `employee_name` varchar(50) DEFAULT NULL COMMENT '姓名',
    `employee_status` varchar(50) DEFAULT NULL COMMENT '员工状态',
    `org_name` varchar(50) DEFAULT NULL COMMENT '科室',
    `val` text COMMENT '数据内容',
    `remark` varchar(200) DEFAULT NULL COMMENT '备注',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='核对人员历史记录';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_item_basic` (
     `id` varchar(50) NOT NULL COMMENT '薪酬项目id',
     `group_id` varchar(50) DEFAULT NULL COMMENT '项分组id',
     `item_name` varchar(50) DEFAULT NULL COMMENT '薪酬项目名称',
     `item_code` varchar(50) DEFAULT NULL COMMENT '薪酬项目编码',
     `item_type` varchar(50) DEFAULT NULL COMMENT '1基础信息2工资项',
     `item_digit` char(1) DEFAULT NULL COMMENT '小数位',
     `count_type` char(1) DEFAULT NULL COMMENT '1加项 2减项3不参与计算',
     `item_rule` char(1) DEFAULT NULL COMMENT '1手工录入2固定值3从薪资档案取4自定义公式',
     `count_formula` varchar(500) DEFAULT NULL COMMENT '计算公式',
     `count_formula_text` varchar(500) DEFAULT NULL COMMENT '计算公式文本',
     `salary_item_amount` decimal(18,2) DEFAULT NULL COMMENT '固定金额',
     `salary_remark` varchar(50) DEFAULT NULL COMMENT '工资条备注',
     `is_hidden` varchar(50) DEFAULT NULL COMMENT '1空值隐藏',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬基础项目表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_item_group` (
	`id` varchar(50) NOT NULL,
	`item_group` varchar(50) DEFAULT NULL COMMENT '组名称',
	`remark` varchar(200) DEFAULT NULL COMMENT '备注',
	`create_date` datetime DEFAULT NULL COMMENT '创建时间',
	`create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
	`create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
	`update_date` datetime DEFAULT NULL COMMENT '修改时间',
	`update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
	`update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
	`is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
	`sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
	`sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
	`option_id` varchar(36) DEFAULT NULL COMMENT '薪酬组id',
	`seq_no` int(2) DEFAULT NULL COMMENT '排序号',
	`is_article` char(2) DEFAULT NULL COMMENT '工资条展示 1是,2否',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬项分组';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_item_group_basic` (
     `id` varchar(50) NOT NULL,
     `item_group` varchar(50) DEFAULT NULL COMMENT '组名称',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬基础项分组';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_item_option` (
     `id` varchar(50) NOT NULL,
     `option_id` varchar(50) DEFAULT NULL COMMENT '薪酬方案id',
     `item_id` varchar(50) DEFAULT NULL COMMENT '薪酬项目id',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬方案项目中间表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_option` (
     `id` varchar(50) NOT NULL,
     `option_name` varchar(50) DEFAULT NULL COMMENT '方案名称',
     `option_code` varchar(50) DEFAULT NULL COMMENT '方案编码',
     `is_enable` varchar(50) DEFAULT NULL COMMENT '是否启用: 1=是; 2=否;  ',
     `option_cycle` varchar(50) DEFAULT NULL COMMENT '算薪周期',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     `head_count` varchar(10) DEFAULT NULL COMMENT '算薪人数',
     `item_digit` int(2) DEFAULT NULL COMMENT '小数位',
     `carry_rule` varchar(16) DEFAULT NULL COMMENT '进位规则 1四舍五入,2向下取数,3向上取数',
     `pay_slip` varchar(16) DEFAULT NULL COMMENT '是否创建工资条 1是,2否',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬方案表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_option_emp` (
    `id` varchar(50) NOT NULL,
    `employee_id` varchar(50) DEFAULT NULL COMMENT '人员id',
    `employee_name` varchar(50) DEFAULT NULL COMMENT '人员名字',
    `option_id` varchar(50) DEFAULT NULL COMMENT '方案id',
    `remark` varchar(200) DEFAULT NULL COMMENT '备注',
    `effective_date` varchar(30) DEFAULT NULL COMMENT '生效时间',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬方案人员关联表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_option_emp_history` (
     `id` varchar(36) NOT NULL COMMENT '主键id',
     `employee_id` varchar(36) DEFAULT NULL COMMENT '员工id',
     `option_id` varchar(36) DEFAULT NULL COMMENT '方案id',
     `v_number` varchar(36) DEFAULT NULL COMMENT '历史版本号',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬人员-方案关联表历史表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_option_payroll` (
     `id` varchar(50) NOT NULL COMMENT '核算记录id',
     `option_id` varchar(50) DEFAULT NULL COMMENT '方案id',
     `option_name` varchar(50) DEFAULT NULL COMMENT '方案名称',
     `compute_date` varchar(50) DEFAULT NULL COMMENT '核算月份',
     `compute_status` char(1) DEFAULT NULL COMMENT '核算状态 0 未计算 1已计算 2已完成 3已锁定',
     `pay_slip` char(1) DEFAULT NULL COMMENT '1 发放工资条',
     `head_count` int(5) DEFAULT NULL COMMENT '算薪人数',
     `set_count` int(5) DEFAULT NULL COMMENT '定薪人数',
     `update_count` int(5) DEFAULT NULL COMMENT '调薪人数',
     `compute_time` varchar(50) DEFAULT NULL COMMENT '核算时间',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬核算记录表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_payroll` (
     `id` varchar(50) NOT NULL COMMENT '薪酬发放id',
     `employee_id` varchar(50) DEFAULT NULL COMMENT '员工id',
     `option_id` varchar(50) DEFAULT NULL COMMENT '方案id',
     `payroll_date` varchar(50) DEFAULT NULL COMMENT '发放月份',
     `send_status` char(1) DEFAULT NULL COMMENT '工资条发送状态',
     `send_time` varchar(50) DEFAULT NULL COMMENT '发送时间',
     `send_method` char(1) DEFAULT NULL COMMENT '发送方式',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     `option_payroll_id` varchar(36) DEFAULT NULL COMMENT '发放记录id',
     `is_view` char(1) DEFAULT NULL COMMENT '1 已查看',
     `revocation_time` varchar(36) DEFAULT NULL COMMENT '撤回时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='发放记录表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_payroll_detail` (
     `id` varchar(50) NOT NULL COMMENT '发放明细id',
     `payroll_id` varchar(50) DEFAULT NULL COMMENT '薪酬发放id',
     `item_id` varchar(50) DEFAULT NULL COMMENT '薪酬醒目id',
     `option_id` varchar(50) DEFAULT NULL COMMENT '方案id',
     `payroll_date` varchar(36) DEFAULT NULL COMMENT '发放月份',
     `item_name` varchar(50) DEFAULT NULL COMMENT '薪酬项目名称',
     `salary` decimal(18,2) DEFAULT NULL COMMENT '工资金额',
     `is_hidden` char(1) DEFAULT NULL COMMENT '工资条空值是否隐藏',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     `item_group_name` varchar(36) DEFAULT NULL COMMENT '薪酬组名称',
     `item_group_id` varchar(36) DEFAULT NULL COMMENT '薪酬组id',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬发放明细表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_payroll_detail_import` (
      `id` varchar(50) NOT NULL COMMENT '发放明细id',
      `employee_no` varchar(50) DEFAULT NULL COMMENT '人员id',
      `item_id` varchar(50) DEFAULT NULL COMMENT '薪酬醒目id',
      `option_id` varchar(50) DEFAULT NULL COMMENT '薪酬醒目id',
      `item_code` varchar(50) DEFAULT NULL COMMENT '薪酬项目code',
      `item_name` varchar(50) DEFAULT NULL COMMENT '薪酬项目名称',
      `salary` decimal(18,2) DEFAULT NULL COMMENT '工资金额',
      `import_date` varchar(36) DEFAULT NULL COMMENT '导入月份',
      `remark` varchar(200) DEFAULT NULL COMMENT '备注',
      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
      `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
      `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
      `update_date` datetime DEFAULT NULL COMMENT '修改时间',
      `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
      `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
      `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
      `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
      `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬发放明细手工导入表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_secondstep_history` (
     `id` varchar(50) NOT NULL COMMENT 'id',
     `payroll_date` varchar(50) DEFAULT NULL COMMENT '发放月份',
     `option_id` varchar(50) DEFAULT NULL COMMENT '薪酬方案id',
     `ydqk` varchar(50) DEFAULT NULL COMMENT '异动情况',
     `employee_no` varchar(50) DEFAULT NULL COMMENT '工号',
     `employee_name` varchar(50) DEFAULT NULL COMMENT '姓名',
     `employee_status` varchar(50) DEFAULT NULL COMMENT '员工状态',
     `org_name` varchar(50) DEFAULT NULL COMMENT '科室',
     `val` text COMMENT '数据内容',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='定薪调薪历史数据';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_secondstep_title_history` (
	`id` varchar(50) NOT NULL COMMENT 'id',
	`payroll_date` varchar(50) DEFAULT NULL COMMENT '发放月份',
	`option_id` varchar(50) DEFAULT NULL COMMENT '薪酬方案id',
	`val` text COMMENT '数据内容',
	`remark` varchar(200) DEFAULT NULL COMMENT '备注',
	`create_date` datetime DEFAULT NULL COMMENT '创建时间',
	`create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
	`create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
	`update_date` datetime DEFAULT NULL COMMENT '修改时间',
	`update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
	`update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
	`is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
	`sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
	`sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='定薪调薪历史记录表头';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_seniority_wage` (
     `id` varchar(50) NOT NULL COMMENT 'id',
     `employee_id` varchar(50) DEFAULT NULL COMMENT '员工id',
     `working_year` varchar(50) DEFAULT NULL COMMENT '工作年限',
     `salary` decimal(18,2) DEFAULT NULL COMMENT '工资金额',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='工龄工资结果表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_seniority_wage_disposition` (
      `id` varchar(50) NOT NULL COMMENT 'id',
      `years` varchar(200) DEFAULT NULL COMMENT '规则',
      `salary` decimal(18,2) DEFAULT NULL COMMENT '工资金额',
      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
      `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
      `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
      `update_date` datetime DEFAULT NULL COMMENT '修改时间',
      `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
      `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
      `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
      `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
      `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
      `is_enable` varchar(16) DEFAULT NULL COMMENT '启用状态',
      `remark` varchar(128) DEFAULT NULL COMMENT '备注',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='工龄工资配置表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_payslip` (
      `id` varchar(64) NOT NULL COMMENT '主键id',
      `option_id` varchar(36) DEFAULT NULL COMMENT '薪酬组id',
      `option_name` varchar(128) DEFAULT NULL COMMENT '薪酬方案名称',
      `slip_name` varchar(50) DEFAULT NULL COMMENT '工资条名称',
      `hint` varchar(255) DEFAULT NULL COMMENT '提示语',
      `is_enable` char(1) DEFAULT NULL COMMENT '是否启用 1是,2否',
      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
      `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
      `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
      `update_date` datetime DEFAULT NULL COMMENT '修改时间',
      `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
      `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
      `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
      `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
      `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬工资条表';


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_newsalary_item_library` (
     `id` varchar(64) NOT NULL COMMENT '主键id',
     `library_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
     `item_code` varchar(50) DEFAULT NULL COMMENT '薪酬项编码',
     `item_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
     `basic_item_id` varchar(64) DEFAULT NULL COMMENT '薪酬档案定薪id',
     `item_type` char(2) DEFAULT NULL COMMENT '1基础信息2工资项',
     `next_month` char(2) DEFAULT NULL COMMENT '下月引用当月数据',
     `library_type` varchar(128) DEFAULT NULL COMMENT '项目类型  1基本工资,2绩效工资,3考勤工资,4社保公积金,5个税,6福利补贴,7其他,8自定义',
     `carry_rule` char(1) DEFAULT NULL COMMENT '进位规则',
     `item_digit` char(1) DEFAULT NULL COMMENT '小数点位数',
     `item_rule` char(1) DEFAULT NULL COMMENT '计算规则 1手工录入2固定值3从薪资档案取4自定义公式',
     `item_source` char(1) DEFAULT NULL COMMENT '来源 1引用,2自定义',
     `count_type` char(1) DEFAULT NULL COMMENT '加减项 1加项 2减项3不参与计算',
     `remark` varchar(200) DEFAULT NULL COMMENT '备注',
     `status` char(1) DEFAULT NULL COMMENT '状态 1启用,2停用',
     `sort_no` int(5) DEFAULT NULL COMMENT '排序',
     `count_formula` varchar(500) DEFAULT NULL COMMENT '计算公式',
     `count_formula_text` varchar(500) DEFAULT NULL COMMENT '计算公式文本',
     `salary_item_amount` decimal(18,2) DEFAULT NULL COMMENT '固定金额',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
     `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
     `update_date` datetime DEFAULT NULL COMMENT '修改时间',
     `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
     `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
     `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
     `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬项目库表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_item' and COLUMN_NAME = 'is_enable' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_item` ADD `is_enable` char(1)  NULL DEFAULT null  COMMENT ''状态 1启用,2禁用'' ',
                   'select ''INFO: is_enable 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
CREATE TABLE IF NOT EXISTS `hrms_newsalary_item` (
    `id` varchar(50)  NOT NULL COMMENT '薪酬项目id',
    `group_id` varchar(50)  DEFAULT NULL COMMENT '项分组id',
    `option_id` varchar(36)  DEFAULT NULL COMMENT '薪酬组id',
    `item_name` varchar(50)  DEFAULT NULL COMMENT '薪酬项目名称',
    `item_code` varchar(50)  DEFAULT NULL COMMENT '薪酬项目编码',
    `item_digit` char(1)  DEFAULT NULL COMMENT '小数位',
    `count_type` char(1)  DEFAULT NULL COMMENT '1加项 2减项3不参与计算',
    `item_rule` char(1)  DEFAULT NULL COMMENT '1手工录入2固定值3从薪资档案取4自定义公式',
    `count_formula` varchar(500)  DEFAULT NULL COMMENT '计算公式',
    `count_formula_text` varchar(500)  DEFAULT NULL COMMENT '计算公式文本',
    `salary_item_amount` decimal(18,2) DEFAULT NULL COMMENT '固定金额',
    `salary_remark` varchar(50)  DEFAULT NULL COMMENT '工资条备注',
    `is_hidden` char(50)  DEFAULT NULL COMMENT '1空值隐藏',
    `is_article` char(1)  DEFAULT NULL COMMENT '1 工资条展示',
    `remark` varchar(200) DEFAULT NULL COMMENT '备注',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36)  DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30)  DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36)  DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30)  DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1)  DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36)  DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36)  DEFAULT NULL COMMENT '机构名称',
    `basic_item_id` varchar(36)  DEFAULT NULL COMMENT '薪酬档案定薪id',
    `next_month` char(1)  DEFAULT NULL COMMENT '1 下月引用当月数据',
    `actual_salary` char(1)  DEFAULT NULL COMMENT '1 实发工资',
    `warm_reminder` varchar(50)  DEFAULT NULL COMMENT '温馨提示',
    `sort_no` int DEFAULT NULL COMMENT '排序号',
    `item_type` char(1)  DEFAULT NULL COMMENT '类型',
    `sh_salary` char(1)  DEFAULT NULL COMMENT '应发工资',
    `personal_tax` char(1)  DEFAULT NULL COMMENT '是否个税',
    `carry_rule` char(1)  DEFAULT NULL COMMENT '进位规则',
    `library_type` varchar(32)  DEFAULT NULL COMMENT '项目类型',
    `status` char(1)  DEFAULT NULL COMMENT '启用状态',
    `uid` varchar(64)  NOT NULL COMMENT '主键id',
    PRIMARY KEY (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪酬项目表';


set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_item_library' and COLUMN_NAME = 'custom_rule' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_item_library` ADD `custom_rule` varchar(64)  NULL DEFAULT null  COMMENT ''自定义项目'' ',
                   'select ''INFO: custom_rule 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_item' and COLUMN_NAME = 'custom_rule' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_item` ADD `custom_rule` varchar(64)  NULL DEFAULT null  COMMENT ''自定义项目'' ',
                   'select ''INFO: custom_rule 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_basic_column' and COLUMN_NAME = 'custom_rule' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_basic_column` ADD `custom_rule` varchar(500)  NULL DEFAULT null  COMMENT ''自定义项目'' ',
                   'select ''INFO: custom_rule 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_train_plan' and COLUMN_NAME = 'train_start_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_train_plan` ADD `train_start_time` datetime  NULL DEFAULT null  COMMENT ''培训开始时间'' ',
                   'select ''INFO: train_start_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
				   
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_train_plan' and COLUMN_NAME = 'train_end_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_train_plan` ADD `train_end_time` datetime  NULL DEFAULT null  COMMENT ''培训开始时间'' ',
                   'select ''INFO: train_end_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `comm_table_snapshot` (
    `id` varchar(50) NOT NULL DEFAULT '',
    `table_name` varchar(50) NOT NULL DEFAULT '' COMMENT '表名称',
    `row_pk_value` varchar(50) DEFAULT NULL COMMENT '行主键值',
    `row_json_old` text COMMENT '行记录旧',
    `row_json_new` text COMMENT '行记录新',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`),
    KEY `table_name` (`table_name`,`row_pk_value`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_committee` (
     `id` varchar(36)  NOT NULL COMMENT '主键ID',
     `employee_id` varchar(36)  DEFAULT NULL COMMENT '员工ID',
     `employee_name` varchar(36)  DEFAULT NULL COMMENT '员工姓名',
     `post_id` varchar(36)  DEFAULT NULL COMMENT '党内职务id',
     `post` varchar(36)  DEFAULT NULL COMMENT '党内职务',
     `forhowlong` varchar(200)  DEFAULT NULL COMMENT '任期',
     `files_id` varchar(2000)  DEFAULT NULL COMMENT '附件id',
     `remark` varchar(300)  DEFAULT NULL COMMENT '备注',
     `enterprise_id` varchar(36)  DEFAULT NULL COMMENT '企业ID',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(30)  DEFAULT NULL COMMENT '创建者ID',
     `create_user_name` varchar(30)  DEFAULT NULL COMMENT '创建者姓名',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `update_user` varchar(30)  DEFAULT NULL COMMENT '更新者ID',
     `update_user_name` varchar(30)  DEFAULT NULL COMMENT '更新者姓名',
     `org_id` varchar(36)  DEFAULT NULL COMMENT '组织机构ID',
     `org_name` varchar(50)  DEFAULT NULL COMMENT '组织机构名称',
     `is_deleted` char(1)  DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
     `sso_org_code` varchar(64)  DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(64)  DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='党委班子表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_committee_employee` (
     `id` varchar(36)  NOT NULL COMMENT '主键ID',
     `committee_id` varchar(36)  NOT NULL COMMENT '党委班子表id',
     `employee_id` varchar(36)  DEFAULT NULL COMMENT '员工ID',
     `employee_name` varchar(36)  DEFAULT NULL COMMENT '员工姓名',
     `post_id` varchar(36)  DEFAULT NULL COMMENT '党内职务id',
     `post` varchar(36)  DEFAULT NULL COMMENT '党内职务',
     `input_date` varchar(36)  DEFAULT NULL COMMENT '任职日期',
     `output_date` varchar(36)  DEFAULT NULL COMMENT '离任日期',
     `files_id` varchar(2000)  DEFAULT NULL COMMENT '附件id',
     `remark` varchar(300)  DEFAULT NULL COMMENT '备注',
     `enterprise_id` varchar(36) DEFAULT NULL COMMENT '企业ID',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(30)  DEFAULT NULL COMMENT '创建者ID',
     `create_user_name` varchar(30)  DEFAULT NULL COMMENT '创建者姓名',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `update_user` varchar(30)  DEFAULT NULL COMMENT '更新者ID',
     `update_user_name` varchar(30)  DEFAULT NULL COMMENT '更新者姓名',
     `org_id` varchar(36)  DEFAULT NULL COMMENT '组织机构ID',
     `org_name` varchar(50)  DEFAULT NULL COMMENT '组织机构名称',
     `is_deleted` char(1)  DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
     `sso_org_code` varchar(64)  DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(64)  DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='党委成员表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'salary_appoint' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `salary_appoint` int COMMENT ''定薪标识 1是,2否'' ',
                   'select ''INFO: salary_appoint 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'bankcardno' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `bankcardno` varchar(64) null default null COMMENT ''开户行号'' ',
                   'select ''INFO: bankcardno 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'bankcardname' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `bankcardname` varchar(64) null default null COMMENT ''开户行名称'' ',
                   'select ''INFO: bankcardname 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_personnel_incident' and COLUMN_NAME = 'subsidy_money' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_personnel_incident` ADD `subsidy_money` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''津贴补助'' ',
                   'select ''INFO: subsidy_money 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_personnel_incident' and COLUMN_NAME = 'subsidy_money' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_personnel_incident` ADD `retired_money` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''退休工资'' ',
                   'select ''INFO: subsidy_money 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_level' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_level` ADD `sso_org_code` varchar(64) NULL DEFAULT NULL COMMENT ''机构编码'' ',
                   'select ''INFO: sso_org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_level' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_level` ADD `sso_org_name` varchar(64) NULL DEFAULT NULL COMMENT ''机构名称'' ',
                   'select ''INFO: sso_org_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- set @exist := (select count(1) from information_schema.columns
--               where table_name = 'hrms_train_plan' and COLUMN_NAME = 'train_numbers' and table_schema = database());
-- set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_train_plan` ADD `train_numbers` varchar(10) COMMENT ''培训人数'' ',
--                   'select ''INFO: train_numbers 字段已存在.''');
-- PREPARE stmt FROM @sqlstmt;
-- EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_train_plan' and COLUMN_NAME = 'credit' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_train_plan` ADD `credit` varchar(10) COMMENT ''学分'' ',
                   'select ''INFO: credit 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_train_plan' and COLUMN_NAME = 'org_attributes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_train_plan` ADD `org_attributes` varchar(10) COMMENT ''人员类别'' ',
                   'select ''INFO: credit 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_report_total' and COLUMN_NAME = 'sort_no' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_report_total` ADD `sort_no` int null default null COMMENT ''排序号'' ',
                   'select ''INFO: bankcardno 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_scheduling_grouping' and COLUMN_NAME = 'auto_scheduling' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_scheduling_grouping` ADD `auto_scheduling` varchar(10) COMMENT ''开启自动排班'' ',
                   'select ''INFO: auto_scheduling 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_scheduling_grouping' and COLUMN_NAME = 'auto_jurisdiction_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_scheduling_grouping` ADD `auto_jurisdiction_id` varchar(50) COMMENT ''自动排班班次id'' ',
                   'select ''INFO: auto_jurisdiction_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_scheduling_grouping' and COLUMN_NAME = 'auto_jurisdiction_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_scheduling_grouping` ADD `auto_jurisdiction_name` varchar(50) COMMENT ''自动排班班次名称'' ',
                   'select ''INFO: auto_jurisdiction_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_scheduling_grouping' and COLUMN_NAME = 'auto_start_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_scheduling_grouping` ADD `auto_start_date` varchar(20) COMMENT ''自动排班开始日期'' ',
                   'select ''INFO: auto_start_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_scheduling_grouping' and COLUMN_NAME = 'auto_end_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_scheduling_grouping` ADD `auto_end_date` varchar(20) COMMENT ''自动排班结束日期'' ',
                   'select ''INFO: auto_end_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;