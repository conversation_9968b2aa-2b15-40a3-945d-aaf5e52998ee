/**
薪级等级提醒设置
 */
CREATE TABLE IF NOT EXISTS `hrms_newsalary_level_remind_setting`  (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
	`remind_id` varchar(36) NOT NULL COMMENT '薪酬提醒ID',
  `employee_status` varchar(2) DEFAULT NULL COMMENT '员工状态',
	`establishment_type` varchar(1) DEFAULT NULL COMMENT '编制类型',
  `assess_year` varchar(1) DEFAULT NULL COMMENT '0-本年 1-上年',
  `assess_result` varchar(100) DEFAULT NULL COMMENT '考核结果,多个值之间用逗号分割',
  `year_num` varchar(5) DEFAULT NULL COMMENT '入院时间满(年)',
  `auto_adjust_date` varchar(50) DEFAULT NULL COMMENT '自动调整时间',
  `is_enabled` varchar(1) DEFAULT NULL COMMENT '是否启用  1正常  0停用',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT 'N' COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_remind_id` (`remind_id`),
  KEY `index_status_establishment` (`employee_status`,`establishment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪级等级提醒设置';

/**
薪级等级提醒记录
 */
CREATE TABLE IF NOT EXISTS `hrms_newsalary_level_remind_record`  (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
	`remind_id` varchar(36) NOT NULL COMMENT '薪酬提醒ID',
	`adjust_date` varchar(50) DEFAULT NULL COMMENT '调整时间',
	`employee_id` varchar(36) DEFAULT NULL COMMENT '员工id',
	`employee_status` varchar(2) DEFAULT NULL COMMENT '员工状态',
	`establishment_type` varchar(1) DEFAULT NULL COMMENT '编制类型',
  `option_id` varchar(36) DEFAULT NULL COMMENT '方案id',
	`before_salary_level` varchar(36) DEFAULT NULL COMMENT '调整前薪级等级id',
	`after_salary_level` varchar(36) DEFAULT NULL COMMENT '调整后薪级等级id',
	`before_amount` decimal(18,2) DEFAULT NULL COMMENT '调整前薪级等级金额',
	`after_amount` decimal(18,2) DEFAULT NULL COMMENT '调整后薪级等级金额',
	`is_adjust` varchar(1) DEFAULT NULL COMMENT '是否调整 1-是 0-否',
	`handle_status` varchar(1) DEFAULT '0' COMMENT '处理状态 0-未处理 1-已处理',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT 'N' COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_remind_id` (`remind_id`),
	KEY `idx_employee_id` (`employee_id`),
	KEY `idx_empid_adjustdate` (`employee_id`,`adjust_date`),
  KEY `index_adjust_date` (`adjust_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='薪级等级提醒记录';

/**
修改薪级表
 */
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_level' and COLUMN_NAME in ('parent_level_id') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_salary_level` ADD COLUMN `parent_level_id`  varchar(36) NULL COMMENT ''上级薪级等级id'' AFTER `is_enable`;',
                   'select ''INFO: parent_level_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
修改薪酬汇总报表
 */
ALTER TABLE `hrms_newsalary_reports`
MODIFY COLUMN `reports_type`  char(1)  NULL DEFAULT '1' COMMENT '报表类型 1-薪酬汇总 2-薪酬方案类 3-保险缴费类' AFTER `sso_org_name`;

/**
修改报表字段设置
 */
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_report_total' and COLUMN_NAME in ('count_type') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_report_total` ADD COLUMN `count_type`  char(1) NULL COMMENT ''计算类型 1-增项 2-减项'' AFTER `sort_no`;',
                   'select ''INFO: count_type 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
修改薪酬报表字段映射表
 */
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_report_map' and COLUMN_NAME in ('item_rule','count_formula','count_formula_text') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_report_map` ADD COLUMN `item_rule`  char(1) DEFAULT ''1'' COMMENT ''计算规则 1-关联薪酬项目 2-自定义公式'' AFTER `sso_org_name`,
ADD COLUMN `count_formula` varchar(500) DEFAULT NULL COMMENT ''计算公式'' AFTER `item_rule`,
ADD COLUMN `count_formula_text` varchar(500) DEFAULT NULL COMMENT ''计算公式文本'' AFTER `count_formula`;',
                   'select ''INFO: item_rule、count_formula、count_formula_text 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
修改人事事件预警提醒记录表
 */
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_warning_record' and COLUMN_NAME in ('overdue_days','warning_status','remark') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_warning_record`
ADD COLUMN `overdue_days`  varchar(10) NULL COMMENT ''逾期天数'' AFTER `surplus_date`,
ADD COLUMN `warning_status`  char(1) NULL COMMENT ''状态 0-提醒中 1-已提醒 2-已处理'' AFTER `overdue_days`,
ADD COLUMN `remark`  varchar(100) NULL COMMENT ''备注'' AFTER `warning_status`;',
                   'select ''INFO: overdue_days、warning_status、remark 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
/**
更新历史数据字段默认值
 */
update hrms_warning_record set overdue_days = '0' ,warning_status = '0' where overdue_days is null or overdue_days = '';


