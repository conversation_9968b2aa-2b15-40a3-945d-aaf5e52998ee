/*=============================================================*/
/* Table: med_schedule_template_extend                                       */
/* 功能模块:  排班模板拓展表				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-07-03                                         */
/*=============================================================*/ 
CREATE TABLE IF NOT EXISTS `ts_base_oa`.`med_schedule_template_extend` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `employee_no` varchar(36) DEFAULT NULL COMMENT '模板设置人工号',
  `status`  varchar(50) DEFAULT NULL COMMENT '状态：0-禁用，1-启用',
  `sso_org_code`  varchar(50) DEFAULT NULL COMMENT '机构编码',
  PRIMARY KEY (`id`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排班模板拓展表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_compose_classes' and COLUMN_NAME = 'is_hospital' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_compose_classes` ADD `is_hospital` CHAR(1) DEFAULT ''N'' COMMENT ''是否全院-Y是，N否，默认“否”'' ',
                   'select ''INFO: is_hospital 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_compose_classes' and COLUMN_NAME = 'classes_use_names' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_compose_classes` ADD `classes_use_names` VARCHAR(500) DEFAULT NULL COMMENT ''使用范围名称'' ',
                   'select ''INFO: classes_use_names 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_compose_classes' and COLUMN_NAME = 'classes_use_org' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_compose_classes` ADD `classes_use_org` VARCHAR(500) DEFAULT NULL COMMENT ''使用范围科室'' ',
                   'select ''INFO: classes_use_org 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_compose_classes' and COLUMN_NAME = 'classes_use_user' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_compose_classes` ADD `classes_use_user` VARCHAR(500) DEFAULT NULL COMMENT ''使用范围人员'' ',
                   'select ''INFO: classes_use_user 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: med_schedule_classes                                       */
/* 功能模块:  排班班次				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-08-11                                         */
/*=============================================================*/ 
set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_classes' and COLUMN_NAME = 'schedule_statistics' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_classes` ADD `schedule_statistics` VARCHAR(100) DEFAULT ''0'' COMMENT ''排班统计：0-禁用，1-开启，默认禁用'' ',
                   'select ''INFO: schedule_statistics 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: hrms_leave_report                                       */
/* 功能模块:  请假单				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-08-12                                         */
/*=============================================================*/ 
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_leave_report' and COLUMN_NAME = 'start_date_value' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_leave_report` ADD `start_date_value` VARCHAR(2) DEFAULT ''1'' COMMENT ''请假具体开始时间:1-上午开始，2-下午开始'' ',
                   'select ''INFO: start_date_value 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_leave_report' and COLUMN_NAME = 'end_date_value' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_leave_report` ADD `end_date_value` VARCHAR(2) DEFAULT ''2'' COMMENT ''请假具体结束时间:1-上午结束，2-下午结束'' ',
                   'select ''INFO: end_date_value 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: hrms_cancel_leave_report                                       */
/* 功能模块:  销假单				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-08-12                                         */
/*=============================================================*/ 
set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_cancel_leave_report' and COLUMN_NAME = 'start_date_value' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_cancel_leave_report` ADD `start_date_value` VARCHAR(2) DEFAULT ''1'' COMMENT ''销假具体开始时间:1-上午开始，2-下午开始'' ',
                   'select ''INFO: start_date_value 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_cancel_leave_report' and COLUMN_NAME = 'end_date_value' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_cancel_leave_report` ADD `end_date_value` VARCHAR(2) DEFAULT ''2'' COMMENT ''销假具体结束时间:1-上午结束，2-下午结束'' ',
                   'select ''INFO: end_date_value 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: med_custom_statistics_title                                       */
/* 功能模块:  自定义统计表头				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-8-13                                         */
/*=============================================================*/ 
CREATE TABLE IF NOT EXISTS `ts_base_oa`.`med_custom_statistics_title` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `classes_id` varchar(36) DEFAULT NULL COMMENT '班次id',
  `classes_name`  varchar(128) DEFAULT NULL COMMENT '班次名称',
  `create_date` datetime DEFAULT NULL,
  `create_user` varchar(50) DEFAULT NULL,
  `create_user_name` varchar(50) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `update_user` varchar(50) DEFAULT NULL,
  `update_user_name` varchar(50) DEFAULT NULL,
  `is_deleted` char(1) DEFAULT 'N',
  `sso_org_code`  varchar(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name`  varchar(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`)
) COMMENT='自定义统计表头';

/*=============================================================*/
/* Table: med_custom_statistics_title                                       */
/* 功能模块:  自定义统计导出				                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-8-20                                         */
/*=============================================================*/ 
CREATE TABLE IF NOT EXISTS `ts_base_oa`.`med_custom_statistics_export` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `label` varchar(255) DEFAULT NULL COMMENT '字段中文名',
  `prop`  varchar(255) DEFAULT NULL COMMENT '字段名',
  `type` varchar(2) DEFAULT NULL COMMENT '类型：1-个人，2-科室',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `create_date` datetime DEFAULT NULL,
  `create_user` varchar(50) DEFAULT NULL,
  `create_user_name` varchar(50) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `update_user` varchar(50) DEFAULT NULL,
  `update_user_name` varchar(50) DEFAULT NULL,
  `is_deleted` char(1) DEFAULT 'N',
  `sso_org_code`  varchar(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name`  varchar(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`)
) COMMENT='自定义统计导出';

