/*=============================================================*/
/* Table: med_schedule_classes                */
/* 功能模块:  排班班次			                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-06-25                                         */
/*=============================================================*/
set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_classes' and COLUMN_NAME = 'class_attributes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_classes` ADD `class_attributes` CHAR(1) DEFAULT NULL COMMENT ''班次属性 0全院  1科室'' ',
                   'select ''INFO: class_attributes 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: med_schedule_template                */
/* 功能模块:  排班模板			                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-06-27                                         */
/*=============================================================*/
set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_template' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_template` ADD `sso_org_code` varchar(50) DEFAULT NULL COMMENT ''机构编码'' ',
                   'select ''INFO: sso_org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_template' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_template` ADD `sso_org_name` varchar(100) DEFAULT NULL COMMENT ''机构名称'' ',
                   'select ''INFO: sso_org_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_template' and COLUMN_NAME = 'status' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_template` ADD `status` CHAR(1) DEFAULT NULL COMMENT ''状态：0-禁用，1-启用'' ',
                   'select ''INFO: status 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_template' and COLUMN_NAME = 'remark' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_template` ADD `remark` varchar(500) DEFAULT NULL COMMENT ''备注'' ',
                   'select ''INFO: remark 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: med_schedule_record                */
/* 功能模块:  排班记录			                               */
/* 提交人: zouj                                                  */
/* 提交时间:2025-08-01                                         */
/*=============================================================*/
set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_record' and COLUMN_NAME = 'classes_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_record` ADD `classes_name` varchar(100) DEFAULT NULL COMMENT ''班次名称'' ',
                   'select ''INFO: classes_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_record' and COLUMN_NAME = 'classes_worktime' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_record` ADD `classes_worktime` varchar(1000) DEFAULT NULL COMMENT ''考勤时间 多个逗号隔开'' ',
                   'select ''INFO: classes_worktime 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

