<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsEvaluationResultMapper">

	<insert id="batchInsert">

	INSERT INTO hrms_evaluation_result
		( id,
		employee_id,
		assess_year,
		assess_org,
		assess_result,
		remark,
		create_date,
		create_user,
		create_user_name,
		org_id,
		org_name,
		is_deleted,
        sso_org_code
		 )
	VALUES 
		
		<foreach collection="list" item="item" index="index" separator=",">
		
			(
			#{item.id},
			#{item.employeeId},
			#{item.assessYear},
			#{item.assessOrg},
			#{item.assessResult},
			#{item.remark},
			#{item.createDate},
			#{item.createUser},
			#{item.createUserName},
			#{item.orgId},
			#{item.orgName},
			#{item.isDeleted},
            #{item.ssoOrgCode}
			)         
			    			
		</foreach>
	</insert>
 	
 	<select id="getDataList" parameterType="cn.trasen.hrms.model.HrmsEvaluationResult" 
 		resultType="cn.trasen.hrms.model.HrmsEvaluationResult">
 		
 		SELECT 
 			t1.ID,
 			t1.employee_id,
 			t1.assess_year,
 			t1.assess_org,
 			t1.assess_result,
			t1.remark,
			t1.khfj,
			t1.org_id,
			t2.employee_no,
			t2.employee_name,
			t3.name AS orgName,
			t2.identity_number ,
			t4.item_name AS establishmentType
		FROM hrms_evaluation_result t1
		INNER JOIN cust_emp_base t2 ON t1.employee_id=t2.employee_id
		left join cust_emp_info i on t2.employee_id = i.info_id
		INNER JOIN comm_organization t3 ON t2.org_id=t3.organization_id
		LEFT JOIN (
			SELECT
			A.*
			FROM
			COMM_DICT_ITEM A
			LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
			WHERE
			B.TYPE_CODE = 'establishment_type'
			AND B.IS_DELETED = 'N'
			AND A.IS_DELETED = 'N'
			AND A.IS_ENABLE = '1'
			AND A.sso_org_code = #{ssoOrgCode}
		) t4 ON i.establishment_Type =t4.item_code
		where t1.is_deleted='N' and t1.sso_org_code=#{ssoOrgCode}
		<if test="employeeName != null and employeeName != ''">
			and ((t2.employee_no LIKE concat('%',#{employeeName},'%')) or (t2.employee_name LIKE concat('%',#{employeeName},'%'))) 
		</if>
		
		<if test="establishmentType != null and establishmentType !=''">
			and i.establishment_type=#{establishmentType}
		</if>
		
		<if test="yearList != null and yearList.size() > 0">
			AND t1.assess_year IN
			<foreach collection="yearList" item="year" open="(" separator="," close=")">
				#{year}
			</foreach>
		</if>
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND t2.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		<if test="assessOrg != null and assessOrg != ''">
			and t1.assess_org = #{assessOrg}
		</if>
		<if test="assessResult != null and assessResult != ''">
			and t1.assess_result = #{assessResult}
		</if>
		order by t1.create_date desc
 	</select>
</mapper>