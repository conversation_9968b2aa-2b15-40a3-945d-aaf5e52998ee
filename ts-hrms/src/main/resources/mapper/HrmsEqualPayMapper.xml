<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsEqualPayMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsEqualPay">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_code" jdbcType="VARCHAR" property="employeeCode" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="job_category" jdbcType="VARCHAR" property="jobCategory" />
    <result column="entry_date" jdbcType="VARCHAR" property="entryDate" />
    <result column="highest_professional" jdbcType="VARCHAR" property="highestProfessional" />
    <result column="education" jdbcType="VARCHAR" property="education" />
    <result column="education_file" jdbcType="VARCHAR" property="educationFile" />
    <result column="id_card_file" jdbcType="VARCHAR" property="idCardFile" />
    <result column="professional_file" jdbcType="VARCHAR" property="professionalFile" />
    <result column="zyzs_file" jdbcType="VARCHAR" property="zyzsFile" />
    <result column="gp_file" jdbcType="VARCHAR" property="gpFile" />
    <result column="xuexin_net_file" jdbcType="VARCHAR" property="xuexinNetFile" />
    <result column="first_year_appraise" jdbcType="VARCHAR" property="firstYearAppraise" />
    <result column="two_year_appraise" jdbcType="VARCHAR" property="twoYearAppraise" />
    <result column="three_year_appraise" jdbcType="VARCHAR" property="threeYearAppraise" />
    <result column="four_year_appraise" jdbcType="VARCHAR" property="fourYearAppraise" />
    <result column="five_year_appraise" jdbcType="VARCHAR" property="fiveYearAppraise" />
    <result column="befor_first_year_attendance" jdbcType="VARCHAR" property="beforFirstYearAttendance" />
    <result column="first_year_sick_leave" jdbcType="VARCHAR" property="firstYearSickLeave" />
    <result column="first_year_personal_leave" jdbcType="VARCHAR" property="firstYearPersonalLeave" />
    <result column="current_first_year_attendance" jdbcType="VARCHAR" property="currentFirstYearAttendance" />
    <result column="current_year_sick_leave" jdbcType="VARCHAR" property="currentYearSickLeave" />
    <result column="current_year_personal_leave" jdbcType="VARCHAR" property="currentYearPersonalLeave" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>