<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsRewardPenaltyMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsRewardPenalty">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="reward_penalty_type" jdbcType="CHAR" property="rewardPenaltyType" />
    <result column="reward_penalty_title" jdbcType="VARCHAR" property="rewardPenaltyTitle" />
    <result column="reward_penalty_date" jdbcType="TIMESTAMP" property="rewardPenaltyDate" />
    <result column="reward_penalty_reason" jdbcType="VARCHAR" property="rewardPenaltyReason" />
    <result column="reward_penalty_unit" jdbcType="VARCHAR" property="rewardPenaltyUnit" />
    <result column="release_date" jdbcType="TIMESTAMP" property="releaseDate" />
    <result column="release_reason" jdbcType="VARCHAR" property="releaseReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>