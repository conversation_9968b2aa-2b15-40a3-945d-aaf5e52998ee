<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsJobtitleAppointMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsJobtitleAppoint">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="jobtitle_appoint_id" jdbcType="VARCHAR" property="jobtitleAppointId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="jobtitle_category" jdbcType="VARCHAR" property="jobtitleCategory" />
    <result column="jobtitle_level" jdbcType="VARCHAR" property="jobtitleLevel" />
    <result column="jobtitle_name" jdbcType="VARCHAR" property="jobtitleName" />
    <result column="assess_time" jdbcType="TIMESTAMP" property="assessTime" />
    <result column="mechanism_name" jdbcType="VARCHAR" property="mechanismName" />
    <result column="accept_method" jdbcType="CHAR" property="acceptMethod" />
    <result column="certificate_number" jdbcType="VARCHAR" property="certificateNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="approval_status" jdbcType="CHAR" property="approvalStatus" />
    <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="highest_level" jdbcType="CHAR" property="highestLevel" />
  </resultMap>

  <select id="getDataSetList" parameterType="cn.trasen.hrms.model.HrmsJobtitleAppoint"
          resultType="cn.trasen.hrms.model.HrmsJobtitleAppoint">
    SELECT
	    t1.jobtitle_appoint_id, 
	    t1.employee_id,
	    t1.employee_no,
	    t1.employee_name, 
	    t1.jobtitle_category, 
	    t1.jobtitle_level,
	    t1.jobtitle_name,
	    t1.approval_status,
	    t1.assess_time, 
	    t1.mechanism_name,
	    t1.accept_method, 
	    t1.certificate_number,
	    t1.remark, 
	    t1.is_deleted,
	    t1.highest_level,
	    t2.jobtitle_basic_name AS jobtitleCategoryText,
	    t3.jobtitle_basic_name AS jobtitleLevelText,
	    t4.jobtitle_basic_name AS jobtitleNameText,
	    t6.name as orgName
    FROM hrms_jobtitle_appoint t1
    LEFT JOIN comm_jobtitle_basic t2 ON t2.jobtitle_basic_id = t1.jobtitle_category
    LEFT JOIN comm_jobtitle_basic t3 ON t3.jobtitle_basic_id = t1.jobtitle_level
    LEFT JOIN comm_jobtitle_basic t4 ON t4.jobtitle_basic_id = t1.jobtitle_name
    LEFT JOIN cust_emp_base t5 on t1.employee_id = t5.employee_id
    LEFT JOIN comm_organization t6 on t5.org_id = t6.organization_id
    WHERE t1.is_deleted = 'N'
    <if test="employeeName != null and employeeName != ''">
   	 AND (( t1.employee_Name LIKE CONCAT('%', #{employeeName}, '%')) or (t1.employee_no LIKE CONCAT('%', #{employeeName}, '%')))
    </if>
    <if test="createUser != null and createUser != ''">
      AND t1.employee_no = ${createUser}
    </if>
    <if test="jobtitleNameText != null and jobtitleNameText != ''">
      AND (
      t2.jobtitle_basic_name like '%${jobtitleNameText}%'
      OR t3.jobtitle_basic_name like '%${jobtitleNameText}%'
      OR t4.jobtitle_basic_name like '%${jobtitleNameText}%'
      )
    </if>
    <if test="orgName != null and orgName != ''">
      AND t6.name like '%${orgName}%'
    </if>
    <if test="approvalStatus != null and approvalStatus != ''">
      AND t1.approval_status like #{approvalStatus}
    </if> 
	<if test="htOrgIdList != null and htOrgIdList != ''">
			AND t5.org_id in ${htOrgIdList}
	</if>
	<if test="ssoOrgCode != null and ssoOrgCode != ''">
        AND t1.sso_org_code = #{ssoOrgCode}
    </if>
  </select>

  <select id="getList" parameterType="cn.trasen.hrms.model.HrmsJobtitleAppoint"
          resultType="cn.trasen.hrms.model.HrmsJobtitleAppoint">

    SELECT
    t1.jobtitle_appoint_id, t1.employee_id,t1.employee_no,t1.employee_name, t1.jobtitle_category, t1.jobtitle_level,
    t1.jobtitle_name,t1.approval_status,
    t1.assess_time, t1.mechanism_name,t1.accept_method, t1.certificate_number,t1.remark, t1.is_deleted,
    t2.jobtitle_basic_name AS jobtitleCategoryText,
    t3.jobtitle_basic_name AS jobtitleLevelText,
    t4.jobtitle_basic_name AS jobtitleNameText
    FROM hrms_jobtitle_appoint t1
    LEFT JOIN comm_jobtitle_basic t2 ON t2.jobtitle_basic_id = t1.jobtitle_category
    LEFT JOIN comm_jobtitle_basic t3 ON t3.jobtitle_basic_id = t1.jobtitle_level
    LEFT JOIN comm_jobtitle_basic t4 ON t4.jobtitle_basic_id = t1.jobtitle_name
    LEFT JOIN cust_emp_base t5 on t1.employee_id = t5.employee_id
    WHERE t1.is_deleted = 'N'
    <if test="employeeName != null and employeeName != ''">
      AND t1.employee_name like '%${employeeName}%'
    </if>
    <if test="employeeNo != null and employeeNo != ''">
      AND t1.employee_no like '%${employeeNo}%'
    </if>
    <if test="jobtitleNameText != null and jobtitleNameText != ''">
      AND (
      t2.jobtitle_basic_name like '%${jobtitleNameText}%'
      OR t3.jobtitle_basic_name like '%${jobtitleNameText}%'
      OR t4.jobtitle_basic_name like '%${jobtitleNameText}%'
      )
    </if>
    <if test="htOrgIdList != null and htOrgIdList != ''">
		AND t5.org_id in ${htOrgIdList}
	</if>
  </select>
  
  <update id="updateHighestLevel" parameterType="java.lang.String">
    	update hrms_jobtitle_appoint set highest_level='2'
	  	where employee_id = #{employeeId}
	  	and is_deleted = 'N'
  </update>
</mapper>