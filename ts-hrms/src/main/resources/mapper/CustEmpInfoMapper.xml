<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.CustEmpInfoMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.CustEmpInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="nationality" jdbcType="VARCHAR" property="nationality" />
    <result column="political_status" jdbcType="VARCHAR" property="politicalStatus" />
    <result column="party_date" jdbcType="VARCHAR" property="partyDate" />
    <result column="birthplace" jdbcType="VARCHAR" property="birthplace" />
    <result column="blood_group" jdbcType="VARCHAR" property="bloodGroup" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="born_address" jdbcType="VARCHAR" property="bornAddress" />
    <result column="born_address_name" jdbcType="VARCHAR" property="bornAddressName" />
    <result column="residence_address" jdbcType="VARCHAR" property="residenceAddress" />
    <result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact" />
    <result column="emergency_tel" jdbcType="VARCHAR" property="emergencyTel" />
    <result column="emp_title_id" jdbcType="VARCHAR" property="empTitleId" />
    <result column="emp_title_name" jdbcType="VARCHAR" property="empTitleName" />
    <result column="health_status" jdbcType="VARCHAR" property="healthStatus" />
    <result column="id_card_file" jdbcType="VARCHAR" property="idCardFile" />
    <result column="marriage_status" jdbcType="VARCHAR" property="marriageStatus" />
    <result column="postcode" jdbcType="VARCHAR" property="postcode" />
    <result column="first_education_type" jdbcType="VARCHAR" property="firstEducationType" />
    <result column="rxsj" jdbcType="VARCHAR" property="rxsj" />
    <result column="bysj" jdbcType="VARCHAR" property="bysj" />
    <result column="prepare_party_date" jdbcType="VARCHAR" property="preparePartyDate" />
    <result column="used_name" jdbcType="VARCHAR" property="usedName" />
    <result column="work_start_date" jdbcType="VARCHAR" property="workStartDate" />
    <result column="xuexin_net_file" jdbcType="VARCHAR" property="xuexinNetFile" />
    <result column="employee_category" jdbcType="VARCHAR" property="employeeCategory" />
    <result column="establishment_type" jdbcType="VARCHAR" property="establishmentType" />
    <result column="authorized_org" jdbcType="VARCHAR" property="authorizedOrg" />
    <result column="bdwlxgl" jdbcType="VARCHAR" property="bdwlxgl" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="doctor_qualification_certificate" jdbcType="VARCHAR" property="doctorQualificationCertificate" />
    <result column="check_work_depart" jdbcType="VARCHAR" property="checkWorkDepart" />
    <result column="concurrent_position" jdbcType="VARCHAR" property="concurrentPosition" />
    <result column="concurrent_position_time" jdbcType="VARCHAR" property="concurrentPositionTime" />
    <result column="employ_duty" jdbcType="VARCHAR" property="employDuty" />
    <result column="employ_duty_date" jdbcType="DATE" property="employDutyDate" />
    <result column="employ_duty_duration" jdbcType="INTEGER" property="employDutyDuration" />
    <result column="employ_duty_equally_date" jdbcType="DATE" property="employDutyEquallyDate" />
    <result column="job_deion_type_time" jdbcType="DATE" property="jobDeionTypeTime" />
    <result column="jrzwsj" jdbcType="VARCHAR" property="jrzwsj" />
    <result column="prfj" jdbcType="VARCHAR" property="prfj" />
    <result column="prlx" jdbcType="VARCHAR" property="prlx" />
    <result column="fire_reason" jdbcType="VARCHAR" property="fireReason" />
    <result column="good_at" jdbcType="VARCHAR" property="goodAt" />
    <result column="is_retire" jdbcType="VARCHAR" property="isRetire" />
    <result column="is_duplicate_entry" jdbcType="VARCHAR" property="isDuplicateEntry" />
    <result column="is_leader" jdbcType="VARCHAR" property="isLeader" />
    <result column="is_veteran" jdbcType="VARCHAR" property="isVeteran" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="job_deion_type" jdbcType="VARCHAR" property="jobDeionType" />
    <result column="midwife" jdbcType="VARCHAR" property="midwife" />
    <result column="post_id" jdbcType="VARCHAR" property="postId" />
    <result column="preparation_no" jdbcType="VARCHAR" property="preparationNo" />
    <result column="quit_date" jdbcType="DATE" property="quitDate" />
    <result column="reemployment_date" jdbcType="DATE" property="reemploymentDate" />
    <result column="retire_date" jdbcType="DATE" property="retireDate" />
    <result column="retirement_time" jdbcType="VARCHAR" property="retirementTime" />
    <result column="review_depart" jdbcType="VARCHAR" property="reviewDepart" />
    <result column="rtjnx" jdbcType="VARCHAR" property="rtjnx" />
    <result column="rtzsj" jdbcType="VARCHAR" property="rtzsj" />
    <result column="shifouguipeirenyuan" jdbcType="VARCHAR" property="shifouguipeirenyuan" />
    <result column="compliance_training" jdbcType="VARCHAR" property="complianceTraining" />
    <result column="shifouxinglinrencai" jdbcType="VARCHAR" property="shifouxinglinrencai" />
    <result column="shifouzhongcengganbu" jdbcType="VARCHAR" property="shifouzhongcengganbu" />
    <result column="work_nature" jdbcType="VARCHAR" property="workNature" />
    <result column="zhuanyeyingcai" jdbcType="VARCHAR" property="zhuanyeyingcai" />
    <result column="start_employ_date" jdbcType="VARCHAR" property="startEmployDate" />
    <result column="end_employ_date" jdbcType="VARCHAR" property="endEmployDate" />
    <result column="is_hetongdaoqi" jdbcType="VARCHAR" property="isHetongdaoqi" />
    <result column="work_status" jdbcType="VARCHAR" property="workStatus" />
    <result column="archive_address" jdbcType="VARCHAR" property="archiveAddress" />
    <result column="coefficient" jdbcType="VARCHAR" property="coefficient" />
    <result column="personal_profile" jdbcType="VARCHAR" property="personalProfile" />
    <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="GWZZ" jdbcType="VARCHAR" property="gwzz" />
    <result column="landline_number" jdbcType="VARCHAR" property="landlineNumber" />
    <result column="lianxiren" jdbcType="VARCHAR" property="lianxiren" />
    <result column="zcgbscrzsj" jdbcType="VARCHAR" property="zcgbscrzsj" />
    <result column="operation_date" jdbcType="TIMESTAMP" property="operationDate" />
    <result column="operation_number" jdbcType="VARCHAR" property="operationNumber" />
    <result column="operation_org" jdbcType="VARCHAR" property="operationOrg" />
    <result column="operation_scope" jdbcType="VARCHAR" property="operationScope" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="zyfj" jdbcType="VARCHAR" property="zyfj" />
    <result column="unit_start_date" jdbcType="TIMESTAMP" property="unitStartDate" />
    <result column="bankcardname" jdbcType="VARCHAR" property="bankcardname" />
    <result column="bankcardno" jdbcType="VARCHAR" property="bankcardno" />
    <result column="buy_provident_date" jdbcType="VARCHAR" property="buyProvidentDate" />
    <result column="buy_social_date" jdbcType="VARCHAR" property="buySocialDate" />
    <result column="gwdj" jdbcType="VARCHAR" property="gwdj" />
    <result column="improve_flag" jdbcType="VARCHAR" property="improveFlag" />
    <result column="plgw" jdbcType="VARCHAR" property="plgw" />
    <result column="post_wage" jdbcType="VARCHAR" property="postWage" />
    <result column="probation_salary" jdbcType="VARCHAR" property="probationSalary" />
    <result column="regular_salary" jdbcType="VARCHAR" property="regularSalary" />
    <result column="salary_appoint" jdbcType="INTEGER" property="salaryAppoint" />
    <result column="salary_level_id" jdbcType="VARCHAR" property="salaryLevelId" />
    <result column="salary_level_type" jdbcType="VARCHAR" property="salaryLevelType" />
    <result column="salary_level_wage" jdbcType="VARCHAR" property="salaryLevelWage" />
    <result column="salary_remark" jdbcType="VARCHAR" property="salaryRemark" />
    <result column="upgrade_flag" jdbcType="VARCHAR" property="upgradeFlag" />
    <result column="cssj" jdbcType="VARCHAR" property="cssj" />
    <result column="wydz" jdbcType="VARCHAR" property="wydz" />
    <result column="csgrzd" jdbcType="VARCHAR" property="csgrzd" />
    <result column="test123" jdbcType="VARCHAR" property="test123" />
    <result column="cs1" jdbcType="VARCHAR" property="cs1" />
    <result column="css" jdbcType="VARCHAR" property="css" />
  </resultMap>
</mapper>