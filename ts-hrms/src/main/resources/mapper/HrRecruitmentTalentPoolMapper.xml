<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.zpgl.dao.HrRecruitmentTalentPoolMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="hr_recruitment_talent_pool_id" jdbcType="VARCHAR" property="hrRecruitmentTalentPoolId" />
    <result column="talent_pool_no" jdbcType="VARCHAR" property="talentPoolNo" />
    <result column="talent_pool_name" jdbcType="VARCHAR" property="talentPoolName" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="CHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
    <select id="selectByName" resultType="cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool">
        select * from hr_recruitment_talent_pool
        where talent_pool_name = #{name} and is_deleted = 'N' and status = 'Y' AND parent_id is null
    </select>
    <select id="selectPageList" resultType="cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool">
     SELECT
      a.hr_recruitment_talent_pool_id,
      a.talent_pool_no,
      a.talent_pool_name,
      a.parent_id,
      a.parent_name,
      a.parent_talent_pool_no,
      a.status,
      a.create_date,
      a.create_user_name create_user,
      a.update_date,
      a.update_user,
      a.update_user_name,
      a.is_deleted,
      a.remark,
      a.seq_no,
      a.fullpath
      FROM hr_recruitment_talent_pool a
     <where>
        and  a.is_deleted = 'N'
       <if test=" null != talentPoolName and '' != talentPoolName">
         and a.talent_pool_name like concat('%',#{talentPoolName},'%')
       </if>
       <if test=" null != createUser and '' != createUser">
         and a.create_user_name like concat('%',#{createUser},'%')
       </if>
       <if test="parentIds != null ">
         and a.hr_recruitment_talent_pool_id in (<foreach collection="parentIds" index="index" item="item" separator=",">#{item}</foreach>)
       </if>
       <if test="ssoOrgCode != null and ssoOrgCode != ''">
           and a.sso_org_code = #{ssoOrgCode}
       </if>
     </where>

    </select>
    
    <select id="treeSecondLevel" resultType="cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool" parameterType="cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolListInputVo">
    	
    	SELECT 
      a.hr_recruitment_talent_pool_id,
      a.talent_pool_no,
      a.talent_pool_name,
      a.parent_id,
      a.parent_name,
      a.parent_talent_pool_no,
      a.status,
      a.create_date,
      a.create_user_name create_user,
      a.update_date,
      a.update_user,
      a.update_user_name,
      a.is_deleted,
      a.remark,
      a.seq_no
      FROM hr_recruitment_talent_pool a

		WHERE a.is_deleted ='N' and a.status='Y' and ( (a.parent_id IS NULL)  OR (
		a.parent_id IN 
		(SELECT hr_recruitment_talent_pool_id FROM hr_recruitment_talent_pool  
			WHERE parent_id IS NULL  AND STATUS='Y' AND is_deleted='N'
		 )))
    	
    	
    </select>
    
</mapper>