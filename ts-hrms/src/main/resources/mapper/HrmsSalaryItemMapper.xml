<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsSalaryItemMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsSalaryItem">
		<!-- WARNING - @mbg.generated -->
		<id column="salary_item_id" jdbcType="VARCHAR" property="salaryItemId" />
		<result column="salary_item_name" jdbcType="VARCHAR" property="salaryItemName" />
		<result column="salary_item_code" jdbcType="VARCHAR" property="salaryItemCode" />
		<result column="data_category" jdbcType="VARCHAR" property="dataCategory" />
		<result column="salary_item_type" jdbcType="CHAR" property="salaryItemType" />
		<result column="count_type" jdbcType="CHAR" property="countType" />
		<result column="count_formula" jdbcType="VARCHAR" property="countFormula" />
		<result column="count_formula_text" jdbcType="VARCHAR" property="countFormulaText" />
		<result column="salary_item_amount" jdbcType="DECIMAL" property="salaryItemAmount" />
		<result column="is_real" jdbcType="CHAR" property="isReal" />
		<result column="serial_number" jdbcType="INTEGER" property="serialNumber" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="org_name" jdbcType="VARCHAR" property="orgName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
		<result column="attendance_type" jdbcType="VARCHAR" property="attendanceType" />
	</resultMap>
</mapper>