<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsPersonnelIncidentMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsPersonnelIncident">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="personnel_incident_id" jdbcType="VARCHAR" property="personnelIncidentId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="establishment_type" jdbcType="VARCHAR" property="establishmentType" />
    <result column="incident_type" jdbcType="CHAR" property="incidentType" />
    <result column="incident_category" jdbcType="CHAR" property="incidentCategory" />
    <result column="approval_status" jdbcType="CHAR" property="approvalStatus" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="incident_time" jdbcType="TIMESTAMP" property="incidentTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="related_user_id" jdbcType="VARCHAR" property="relatedUserId" />
    <result column="related_user_name" jdbcType="VARCHAR" property="relatedUserName" />
    <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <update id="update"
  		parameterType="cn.trasen.hrms.model.HrmsPersonnelIncident">
  		UPDATE
		  hrms_personnel_incident
		SET
		  <if test="employeeId != null and employeeId!= ''">
			  employee_id = #{employeeId},
		  </if>
		  <if test="employeeNo != null and employeeNo!= ''">
			  employee_no = #{employeeNo},
		  </if>
		  <if test="employeeName != null and employeeName!= ''">
			  employee_name = #{employeeName},
		  </if>
		  <if test="incidentType != null and incidentType!= ''">
			  incident_type = #{incidentType},
		  </if>
		  <if test="incidentCategory != null and incidentCategory!= ''">
			  incident_category = #{incidentCategory},
		  </if>
		  <if test="startTime != null">
			  start_time = #{startTime},
		  </if>
		  <if test="endTime != null">
			  end_time = #{endTime},
		  </if>
		  <if test="incidentTime != null">
			  incident_time =#{incidentTime},
		  </if>
		  <if test="remark != null and remark!= ''">
			  remark = #{remark},
		  </if>
		  <if test="relatedUserId != null and relatedUserId!= ''">
			  related_user_id = #{relatedUserId},
		  </if>
		  <if test="relatedUserName != null and relatedUserName!= ''">
			  related_user_name = #{relatedUserName},
		  </if>
		  <if test="enterpriseId != null and enterpriseId!= ''">
			  enterprise_id = #{enterpriseId},
		  </if>
		  <if test="updateUser != null and updateUser!= ''">
			  update_user = #{updateUser},
		  </if>
		  <if test="updateUserName != null and updateUserName!= ''">
			  update_user_name = #{updateUserName},
		  </if>
		  <if test="orgId != null and orgId!= ''">
			  org_id = #{orgId},
		  </if>
		  <if test="orgName != null and orgName!= ''">
			  org_name = #{orgName},
		  </if>
		  <if test="newOrgId != null and newOrgId!= ''">
			  new_org_id = #{newOrgId},
		  </if>
		  <if test="positionId != null and positionId!= ''">
			  position_id = #{positionId},
		  </if>
		  <if test="personalIdentity != null and personalIdentity!= ''">
			  personal_identity=#{personalIdentity},
		  </if>
		  <if test="txmoney != null and txmoney!= ''">
			  txmoney = #{txmoney},
		  </if>
		  <if test="txRecordTime != null ">
			  txRecordTime = #{txRecordTime},
		  </if>
		  <if test="establishmentType != null and establishmentType!= ''">
			  establishment_type = #{establishmentType},
		  </if>
		  <if test="cause != null and cause!= ''">
			  cause =#{cause},
		  </if>
		  <if test="fileId != null and fileId!= ''">
			  file_id = #{fileId},
		  </if>
		  <if test="workflowId != null and workflowId!= ''">
			  workflow_id = #{workflowId},
		  </if>
		  <if test="ssoOrgCode != null and ssoOrgCode!= ''">
			  sso_org_code = #{ssoOrgCode},
		  </if>
		  <if test="subsidyMoney != null and subsidyMoney!= ''">
			  subsidy_money = #{subsidyMoney},
		  </if>
		  <if test="retiredMoney != null and retiredMoney!= ''">
			  retired_money = #{retiredMoney},
		  </if>
	  		update_date = #{updateDate}
		WHERE personnel_incident_id = #{personnelIncidentId}

  </update>

  <select id="getDataSetList" resultType="cn.trasen.hrms.model.HrmsPersonnelIncident" parameterType="cn.trasen.hrms.model.HrmsPersonnelIncident">
	SELECT
		inc.*,
		org.organization_id AS employeeOrgId,
		org.name AS employeeOrgName,
		pos.position_id AS positionId,
		pos.position_name AS positionName,
		emp.identity_number as identityNumber,
		inc.stop_event AS stopEvent,
		dic.ITEM_NAME AS establishmentType,
		dic3.ITEM_NAME AS  employeeStatus,
		IFNULL(inc.update_user_name,inc.create_user_name) as operatorUserName,
		dic4.ITEM_NAME AS  politicalStatus,
		i.work_start_date as workStartDate
	FROM
	hrms_personnel_incident inc
	LEFT JOIN cust_emp_base emp ON emp.employee_id = inc.employee_id
	left join cust_emp_info i on emp.employee_id = i.info_id
	LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
	LEFT JOIN comm_position pos ON pos.position_id = inc.position_id
	LEFT JOIN (
	  SELECT
	  A.*
	  FROM
	  COMM_DICT_ITEM A
	  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
	  WHERE
	  B.TYPE_CODE = 'establishment_type'
	  AND B.IS_DELETED = 'N'
	  AND A.IS_DELETED = 'N'
	  AND A.IS_ENABLE = '1'
	  AND A.sso_org_code = #{ssoOrgCode}
    ) dic ON inc.establishment_type = dic.ITEM_CODE
	LEFT JOIN (
	  SELECT
	  A.*
	  FROM
	  COMM_DICT_ITEM A
	  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
	  WHERE
	  B.TYPE_CODE = 'employee_status'
	  AND B.IS_DELETED = 'N'
	  AND A.IS_DELETED = 'N'
	  AND A.IS_ENABLE = '1'
	  AND A.sso_org_code = #{ssoOrgCode}
    ) dic3 ON emp.employee_status = dic3.ITEM_CODE
	LEFT JOIN (
	  SELECT
	  A.*
	  FROM
	  COMM_DICT_ITEM A
	  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
	  WHERE
	  B.TYPE_CODE = 'political_status'
	  AND B.IS_DELETED = 'N'
	  AND A.IS_DELETED = 'N'
	  AND A.IS_ENABLE = '1'
	  AND A.sso_org_code = #{ssoOrgCode}
    ) dic4 ON i.political_status = dic4.ITEM_CODE
	WHERE inc.is_deleted = 'N'
	<if test="ssoOrgCode != null and ssoOrgCode != ''">
	  and inc.sso_org_code=#{ssoOrgCode}
	</if>
    <if test="employeeName != null and employeeName != ''">
      and inc.employee_name like '%${employeeName}%'
    </if>
    <if test="employeeNo != null and employeeNo != ''">
      and inc.employee_no like '%${employeeNo}%'
    </if>
    <if test="incidentCategory != null and incidentCategory != ''">
      and inc.incident_category = #{incidentCategory}
    </if>
    <if test="incidentType != null and incidentType != ''">
      and inc.incident_type = #{incidentType}
    </if>
    <if test="startDate !=null and startDate !=''">
      and DATE_FORMAT(incident_time,'%Y-%m-%d') &gt;= #{startDate}
    </if>
    <if test="endDate !=null and endDate !=''">
      and DATE_FORMAT(incident_time,'%Y-%m-%d') &lt;= #{endDate}
    </if>
    
    <if test="establishmentType != null and establishmentType != ''">
      and inc.establishment_type = #{establishmentType}
    </if>
    
    <if test="approvalStatus != null and approvalStatus != ''">
      and inc.approval_status = #{approvalStatus}
    </if>
    		
	<if test="htOrgIdList != null and htOrgIdList != ''">
		AND emp.org_id in ${htOrgIdList}
	</if>
	
	<if test="createUserName != null and createUserName != ''">
		and inc.create_user_name  LIKE CONCAT('%',#{createUserName},'%')
	</if>
	
	<if test="createStartDate !=null and createStartDate !=''">
		and DATE_FORMAT(inc.create_date,'%Y-%m-%d') &gt;= #{createStartDate}
	</if>
	<if test="createEndDate !=null and createEndDate !=''">
		and DATE_FORMAT(inc.create_date,'%Y-%m-%d') &lt;= #{createEndDate}
	</if>
	
	<if test="establishmentTypes != null and establishmentTypes.size() > 0">
		and (inc.establishment_type in
		<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
	</if>
	<if test="politicalStatusTypes != null and politicalStatusTypes.size() > 0">
		  and (i.political_status in
		  <foreach collection="politicalStatusTypes" index="index" item="item" open="(" separator="," close=")">
			  #{item}
		  </foreach>
		  )
    </if>
	order by inc.create_date desc
		
  </select>

  <select id="getList" resultType="cn.trasen.hrms.model.HrmsPersonnelIncident" parameterType="cn.trasen.hrms.model.HrmsPersonnelIncident">
    select 
    	inc.*,
    	emp.org_id AS employeeOrgId,
    	org.name AS employeeOrgName,
    	i.post_id AS positionId, 
    	po.post_name AS postName, 
    	pos.position_name AS positionName,
    	dic.ITEM_NAME AS establishmentType
    from hrms_personnel_incident inc
    left join cust_emp_base emp on inc.employee_id = emp.employee_id
    left join cust_emp_info i on i.info_id = emp.employee_id
    LEFT JOIN comm_position pos ON pos.position_id = inc.position_id
    left join comm_organization org on org.organization_id = emp.org_id
    left join hrms_post po ON po.post_id = i.post_id
    LEFT JOIN (
		  SELECT
		  A.*
		  FROM
		  COMM_DICT_ITEM A
		  LEFT JOIN COMM_DICT_TYPE B ON A.DIC_TYPE_ID = B.ID
		  WHERE
		  B.TYPE_CODE = 'establishment_type'
		  AND B.IS_DELETED = 'N'
		  AND A.IS_DELETED = 'N'
		  AND A.IS_ENABLE = '1'
	  	  AND A.sso_org_code = #{ssoOrgCode}
    ) dic ON inc.establishment_type = dic.ITEM_CODE
    where inc.is_deleted = 'N' and inc.sso_org_code=#{ssoOrgCode}
    <if test="employeeName != null and employeeName != ''">
      and inc.employee_name like '%${employeeName}%'
    </if>
    <if test="employeeNo != null and employeeNo != ''">
      and inc.employee_no like '%${employeeNo}%'
    </if>
    <if test="incidentCategory != null and incidentCategory != ''">
      and inc.incident_category = #{incidentCategory}
    </if>
    <if test="incidentType != null and incidentType != ''">
      and inc.incident_type = #{incidentType}
    </if>
    <if test="startDate !=null and startDate !=''">
      and DATE_FORMAT(incident_time,'%Y-%m-%d') &gt;= #{startDate}
    </if>
    <if test="endDate !=null and endDate !=''">
      and DATE_FORMAT(incident_time,'%Y-%m-%d') &lt;= #{endDate}
    </if>
    <if test="htOrgIdList != null and htOrgIdList != ''">
		AND org.organization_id in ${htOrgIdList}
	</if>
	<if test="establishmentType != null and establishmentType !='' ">
		 and inc.establishment_type = #{establishmentType}
	</if>
	order by create_date desc
  </select>

    
    <select id="getWork" resultType="java.lang.Integer">
    			SELECT COUNT(t1.employee_id) AS WORK FROM cust_emp_base t1 
			LEFT JOIN hrms_employee_extend t2 ON  t1.employee_id=t2.employee_id AND t1.is_deleted='N' AND t2.is_deleted='N' 
			WHERE t1.employee_status IN ('1','5','6','9','10','11','12','13') and t1.sso_org_code=#{ssoOrgCode}
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
			
    </select>
    <!-- 上月入职 -->
    <select id="getMonthJoinkork" resultType="java.lang.Integer">
		SELECT COUNT(*) AS monthJoinkork
			
			FROM hrms_employee_extend as t1
			 JOIN cust_emp_base as t2 ON t1.employee_id=t2.employee_id
			
			WHERE
		t1.is_deleted = 'N' and t1.sso_org_code=#{ssoOrgCode}	
		AND DATE_FORMAT(t1.entry_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH),'%Y-%m')
				<if test="orgIdList != null and orgIdList.size() > 0">
			AND t2.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		
    </select>
    <!-- 离职 -->
    <select id="getMonthResignation" resultType="java.lang.Integer">
       SELECT COUNT(*) FROM  hrms_personnel_incident  t1
       
        JOIN cust_emp_base t2 ON t1.employee_id=t2.employee_id
       
       WHERE t1.approval_status='4'  and t1.sso_org_code=#{ssoOrgCode}
       and t1.incident_category='1'
       AND  DATE_FORMAT(t1.incident_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH),'%Y-%m')
       <if test="orgIdList != null and orgIdList.size() > 0">
			AND t2.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
       
    </select>
     <!-- 退休 -->
    <select id="getRetirement" resultType="java.lang.Integer">
    		SELECT COUNT(*)
			FROM cust_emp_base t1
			LEFT JOIN hrms_employee_extend t2 ON t1.employee_id = t2.employee_id
			WHERE t1.is_deleted = 'N' 
	        AND ((t1.employee_status IN('3','8')) OR (t1.is_retire = '1') )  AND t1.employee_status !='7'
	         <if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
	        
    </select>
    <!-- 延聘 -->
    <select id="getPostpone" resultType="java.lang.Integer">
        	 SELECT COUNT(*) FROM  hrms_personnel_incident t1 
        	 
        	  JOIN cust_emp_base t2 ON t1.employee_id=t2.employee_id
        	 
       WHERE t1.approval_status='4' 
       and t1.incident_category='4' and t1.sso_org_code=#{ssoOrgCode}
         <if test="orgIdList != null and orgIdList.size() > 0">
			AND t2.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		
		
    </select>
    <!-- 返聘 -->
    <select id="getReengage" resultType="java.lang.Integer">
            	 SELECT COUNT(*) FROM  hrms_personnel_incident t1 
            	   JOIN cust_emp_base t2 ON t1.employee_id=t2.employee_id
       WHERE t1.approval_status='4'  and t1.sso_org_code=#{ssoOrgCode}
       and t1.incident_category='5'
              
         <if test="orgIdList != null and orgIdList.size() > 0">
			AND t2.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
       
    </select>
    <!-- 职称聘任总人数 -->
    <select id="getTitleAppoint" resultType="java.lang.Integer">
	SELECT COUNT(t1.employee_id) FROM cust_emp_base t1
	INNER JOIN (
	SELECT employee_id,jobtitle_info_id FROM hrms_jobtitle_info WHERE
	is_deleted='N'
		GROUP BY employee_id
	) t2 ON t1.employee_id=t2.employee_id
	WHERE t1.is_deleted='N' AND t1.employee_status IN ('1','5','6','9','10','11','12','13') and t1.sso_org_code=#{ssoOrgCode}
         <if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		
		
    </select>
    
    <select id="findReviewDepart" resultType="java.lang.String" parameterType="java.lang.String">
	    SELECT t3.name FROM cust_emp_base  t1
		LEFT JOIN hrms_employee_extend t2 ON t1.employee_id = t2.employee_id
		LEFT JOIN comm_organization t3 ON t2.review_depart = t3.organization_id
		WHERE t1.employee_no=#{empNo}
    </select>
</mapper>