<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.zpgl.dao.HrmsZpglAddTalentpoolMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="zpglempid" jdbcType="VARCHAR" property="zpglempid" />
    <result column="applicant_post" jdbcType="VARCHAR" property="applicantPost" />
    <result column="applicant_posttext" jdbcType="VARCHAR" property="applicantPosttext" />
    <result column="applicant_dept" jdbcType="VARCHAR" property="applicantDept" />
    <result column="applicant_depttext" jdbcType="VARCHAR" property="applicantDepttext" />
    <result column="affiliation_dept" jdbcType="VARCHAR" property="affiliationDept" />
    <result column="affiliation_depttext" jdbcType="VARCHAR" property="affiliationDepttext" />
    <result column="affiliation_job" jdbcType="VARCHAR" property="affiliationJob" />
    <result column="affiliation_jobtext" jdbcType="VARCHAR" property="affiliationJobtext" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  
  <select id="getPageList" parameterType="cn.trasen.hrms.zpgl.model.HrmsZpglAddTalentpool" resultType="cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglAddTalentpoolOutListVo">
		SELECT DISTINCT
		t1.id AS zpglempid,
	    ts.id AS messageId, <!-- 面试信息id -->
	    ir.id as resultId, <!-- 面试结果id -->
	    ts.conform,
	    ir.interview_result_status,
	    t1.zpgl_employee_status,
	    t1.add_interview,
		tt.add_type,
		tt.id ,
		t1.employee_name,
		t1.gender,
		t1.birthplace,
		t1.age,
		t3.zhichengmingcheng,
		t5.jobtitle_basic_name AS zhichengmingchengtext,
		tt.affiliation_job ,
		tt.affiliation_jobtext,
		tt.affiliation_dept ,
		tt.affiliation_depttext,
		t1.iphone,
		tt.create_date,
		t3.zhuenye,
		t2.xueli,
		t2.biyeyuanxiao,
		t1.entry_date,
		t1.email ,
		t1.add_talent_pool,
		t1.add_source,
		re.fullpath,
		tt.interview_pool,
	    t1.update_date
		FROM hrms_zpgl_add_talentpool tt
		LEFT JOIN hrms_zpgl_employee t1 ON tt.zpglempid = t1.id
		LEFT JOIN hr_recruitment_talent_pool re ON tt.affiliation_job = re.hr_recruitment_talent_pool_id
		LEFT JOIN hr_recruitment_talent_pool re2 ON re.parent_id = re2.hr_recruitment_talent_pool_id
		LEFT JOIN (
		SELECT id,zpglempid,jieduan,xueli,zhuanye,biyeyuanxiao,qizhishijian FROM
		hrms_zpgl_education a WHERE a.rank =(
		SELECT MIN(b.rank) FROM
		hrms_zpgl_education b WHERE a.zpglempid= b.zpglempid )
		) t2 ON t1.`id`= t2.`zpglempid`
		LEFT JOIN (
		SELECT * FROM hrms_zpgl_professional a WHERE a.rank =(
		SELECT MIN(b.rank) FROM
		hrms_zpgl_professional b WHERE a.zpglempid= b.zpglempid )
		) t3 ON t1.`id`= t3.`zpglempid`
		LEFT JOIN comm_jobtitle_basic t5 ON t3.zhichengmingcheng =t5.jobtitle_basic_id
		LEFT JOIN hr_recruitment_talent_pool_label_talent_library pol ON t1.id = pol.talent_library_id
		LEFT JOIN hr_recruitment_talent_pool_label poll ON pol.hr_recruitment_talent_pool_label_id =poll.hr_recruitment_talent_pool_label_id
	    LEFT JOIN hrms_zpgl_interview_message  ts ON t1.id = ts.zpglempid AND ts.is_deleted = 'N'
	    LEFT JOIN hrms_zpgl_interview_result  ir ON t1.id = ir.zpglempid AND ts.is_deleted = 'N'
	<where>
			tt.is_deleted = 'N' 
			and t1.is_deleted = 'N' 
			and t1.add_talent_pool ='1'
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			and tt.sso_org_code = #{ssoOrgCode}
		</if>
		<if test="treeLevel !=null and treeLevel !='' and affiliationJob != null and affiliationJob !=''">
		
			<choose>
				<!-- 第一级  -->
				<when test="treeLevel == '1'.toString()">
					and (
					   (tt.affiliation_job = #{affiliationJob}) 
					or (re.parent_id=#{affiliationJob}) 
					or (re2.parent_id=#{affiliationJob})
					)
				</when>
				<!-- 第二级  -->
				<when test="treeLevel == '2'.toString()">
					and (tt.affiliation_job = #{affiliationJob} or re.parent_id=#{affiliationJob})
				</when>
				<otherwise>
					and tt.affiliation_job = #{affiliationJob}
				</otherwise>
			</choose>
		</if>

		<if test="employeeName !=null and employeeName !=''">
			and t1.employee_name like concat('%',#{employeeName},'%')
		</if>

		<!-- 时间查询 -->
		<if test="searchStartDate != null and searchEndDate != null">
			and tt.create_date BETWEEN #{searchStartDate} AND #{searchEndDate}
		</if>

		<if test="searchEntryDate !=null and searchEntryDate !=''">
			and TIMESTAMPDIFF(YEAR,t1.entry_date,NOW()) &gt;= #{searchEntryDate}
		</if>

		<if test="zhichengmingcheng !=null and zhichengmingcheng !=''">
			and t3.zhichengmingcheng =#{zhichengmingcheng}
		</if>

		<if test="gender !=null and gender !=''">
			and t1.gender =#{gender}
		</if>
		<if test="poolLabel !=null and poolLabel !=''">
			AND  poll.label_name LIKE  CONCAT('%',#{poolLabel},'%')
		</if>
			<!-- 面试管理岗位模糊搜索 -->
		<if test="allPath !=null and allPath !=''">
			and (
			t1.interview_path  like concat('%',#{allPath},'%')
			or tt.interview_pool  like concat('%',#{allPath},'%')
			)
		</if>
	</where>
  </select>
</mapper>