<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsSalaryAuditMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsSalaryAudit">
		<!-- WARNING - @mbg.generated -->
		<id column="salary_audit_id" jdbcType="VARCHAR" property="salaryAuditId" />
		<result column="audit_date" jdbcType="VARCHAR" property="audit_date" />
		<result column="salary_plan_id" jdbcType="VARCHAR" property="salaryPlanId" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="org_name" jdbcType="VARCHAR" property="orgName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>
</mapper>