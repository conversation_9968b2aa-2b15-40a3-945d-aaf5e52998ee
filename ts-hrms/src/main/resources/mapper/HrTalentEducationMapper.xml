<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.zpgl.dao.HrTalentEducationMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.zpgl.model.HrTalentEducation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="education_id" jdbcType="VARCHAR" property="educationId" />
    <result column="talent_library_id" jdbcType="VARCHAR" property="talentLibraryId" />
    <result column="education_start_date" jdbcType="VARCHAR" property="educationStartDate" />
    <result column="education_end_date" jdbcType="VARCHAR" property="educationEndDate" />
    <result column="school_name" jdbcType="VARCHAR" property="schoolName" />
    <result column="professional" jdbcType="VARCHAR" property="professional" />
    <result column="academic_info" jdbcType="VARCHAR" property="academicInfo" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
  </resultMap>
</mapper>