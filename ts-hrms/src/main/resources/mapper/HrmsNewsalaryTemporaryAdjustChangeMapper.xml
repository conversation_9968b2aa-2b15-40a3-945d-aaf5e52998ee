<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryTemporaryAdjustChangeMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjustChange">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="adjust_id" jdbcType="VARCHAR" property="adjustId" />
    <result column="op_type" jdbcType="CHAR" property="opType" />
    <result column="op_before_value" jdbcType="VARCHAR" property="opBeforeValue" />
    <result column="op_after_value" jdbcType="VARCHAR" property="opAfterValue" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
</mapper>