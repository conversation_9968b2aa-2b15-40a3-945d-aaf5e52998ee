<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsSalaryLevelWageMapper">

	<resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsSalaryLevelWage">
		<!-- WARNING - @mbg.generated -->
		<id column="salary_level_wage_id" jdbcType="VARCHAR" property="salaryLevelWageId" />
		<result column="salary_level_id" jdbcType="VARCHAR" property="salaryLevelId" />
		<result column="salary_level_wage" jdbcType="DECIMAL" property="salaryLevelWage" />
		<result column="is_enable" jdbcType="CHAR" property="isEnable" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>

	<select id="getList" parameterType="cn.trasen.hrms.model.HrmsSalaryLevelWage" resultType="cn.trasen.hrms.model.HrmsSalaryLevelWage">
		<![CDATA[
			SELECT 
				wage.salary_level_wage_id, wage.salary_level_id, wage.salary_level_wage, wage.is_enable,
				sl.salary_level_name, sl.salary_level_category,wage.policy_standard_id
			FROM hrms_salary_level_wage wage
			LEFT JOIN hrms_salary_level sl ON sl.salary_level_id = wage.salary_level_id
			WHERE wage.salary_level_id is not null and wage.is_deleted = #{isDeleted}
		]]>
		<if test="salaryLevelName != null and salaryLevelName != ''">
			<![CDATA[ AND sl.salary_level_name LIKE CONCAT(#{salaryLevelName}, '%') ]]>
		</if>
		<if test="salaryLevelCategory != null and salaryLevelCategory != ''">
			 AND sl.salary_level_category  = #{salaryLevelCategory} 
		</if>
		group by sl.salary_level_category ,wage.salary_level_id
		order by sl.salary_level_category ,sl.grade
	</select>

	<select id="getSalaryLevelWagesSettingList" parameterType="cn.trasen.hrms.model.HrmsSalaryLevelWage" resultType="cn.trasen.hrms.model.HrmsSalaryLevelWage">
		SELECT
			wage.salary_level_wage_id, sl.salary_level_id, wage.salary_level_wage, wage.is_enable,
			sl.salary_level_name, sl.salary_level_category,wage.policy_standard_id
		FROM hrms_salary_level sl
		LEFT JOIN hrms_salary_level_wage wage ON sl.salary_level_id = wage.salary_level_id  and sl.is_deleted = wage.is_deleted
		<if test="policyStandardId != null and policyStandardId != ''">
			and (wage.policy_standard_id =#{policyStandardId})
		</if>
		<if test="policyStandardId == null or policyStandardId == ''">
			and (wage.policy_standard_id is null)
		</if>
		WHERE sl.is_deleted = #{isDeleted}
		<if test="salaryLevelName != null and salaryLevelName != ''">
			<![CDATA[ AND sl.salary_level_name LIKE CONCAT(#{salaryLevelName}, '%') ]]>
		</if>
		<if test="salaryLevelCategory != null and salaryLevelCategory != ''">
			AND sl.salary_level_category  = #{salaryLevelCategory}
		</if>
		group by sl.salary_level_category ,sl.salary_level_id
		order by sl.salary_level_category ,sl.grade
	</select>
	
	<select id="getDataByLevelId"  parameterType="cn.trasen.hrms.model.HrmsSalaryLevelWage"  resultType="cn.trasen.hrms.model.HrmsSalaryLevelWage">
		<![CDATA[
			SELECT
				wage.salary_level_wage_id, wage.salary_level_id, wage.salary_level_wage, wage.is_enable,
				sl.salary_level_name, sl.salary_level_category
			FROM hrms_salary_level_wage wage
			LEFT JOIN hrms_salary_level sl ON sl.salary_level_id = wage.salary_level_id
			WHERE wage.is_deleted = #{isDeleted}
		]]>
		<if test="salaryLevelName != null and salaryLevelName != ''">
			<![CDATA[ AND sl.salary_level_name LIKE CONCAT(#{salaryLevelName}, '%') ]]>
		</if>
		<if test="salaryLevelCategory != null and salaryLevelCategory != ''">
			AND sl.salary_level_category  = #{salaryLevelCategory}
		</if>
		<if test="salaryLevelId != null and salaryLevelId !=''">
			AND wage.salary_level_id  = #{salaryLevelId}
		</if>
		<if test="policyStandardId != null and policyStandardId !=''">
			AND wage.policy_standard_id  = #{policyStandardId}
		</if>

	</select>
	
	<insert id="batchInsert">
		<![CDATA[
			INSERT INTO hrms_salary_level_wage 
			(
				salary_level_wage_id, 
				salary_level_id, 
				salary_level_wage,
				is_enable,
				remark, 
				create_date, 
				create_user, 
				is_deleted 
			) 
			VALUES 
		]]>
		<foreach collection="list" item="item" index="index" separator=",">
			<![CDATA[
			(
				#{item.salaryLevelWageId}, 
				#{item.salaryLevelId}, 
				#{item.salaryLevelWage},
				#{item.isEnable},
				#{item.remark}, 
				#{item.createDate}, 
				#{item.createUser}, 
				#{item.isDeleted}
			)
			]]>
		</foreach>
	</insert>

</mapper>