package cn.trasen.hrms.zpgl.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_zpgl_trace_setting")
@Setter
@Getter
public class HrmsZpglTraceSetting {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * id
     */
	@Id
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer no;

    /**
     * 跟踪开始天数
     */
    @Column(name = "start_day")
    @ApiModelProperty(value = "跟踪开始天数")
    private Integer startDay;

    /**
     * 跟踪结束天数
     */
    @Column(name = "end_day")
    @ApiModelProperty(value = "跟踪结束天数")
    private Integer endDay;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    /**
     * 删除标识(N存在，Y删除)
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识(N存在，Y删除)")
    private String isDeleted;
}