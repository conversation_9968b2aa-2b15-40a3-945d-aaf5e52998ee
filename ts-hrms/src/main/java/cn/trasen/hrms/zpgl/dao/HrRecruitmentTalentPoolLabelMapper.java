package cn.trasen.hrms.zpgl.dao;

import tk.mybatis.mapper.common.Mapper;

import java.util.List;

import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPoolLabel;

/** 
* @ClassName: HrRecruitmentTalentPoolLabelMapper 
* @Description:人才库标签接口
* <AUTHOR>  
* @date 2023年2月6日 下午3:15:21 
*  
*/
public interface HrRecruitmentTalentPoolLabelMapper extends Mapper<HrRecruitmentTalentPoolLabel> {

    List<HrRecruitmentTalentPoolLabel> getTalentLibraryLableById(String talentLibraryId);
}