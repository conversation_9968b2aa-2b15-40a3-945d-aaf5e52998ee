package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrContactInformation;

/**
 * @ClassName HrContactInformationService
 * @Description TODO
 * @date 2021年6月19日 下午4:48:35
 * <AUTHOR>
 * @version 1.0
 */
public interface HrContactInformationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:48:35
	 * <AUTHOR>
	 */
	Integer save(HrContactInformation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:48:35
	 * <AUTHOR>
	 */
	Integer update(HrContactInformation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021年6月19日 下午4:48:35
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrContactInformation
	 * @date 2021年6月19日 下午4:48:35
	 * <AUTHOR>
	 */
	HrContactInformation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrContactInformation>
	 * @date 2021年6月19日 下午4:48:35
	 * <AUTHOR>
	 */
	DataSet<HrContactInformation> getDataSetList(Page page, HrContactInformation record);
 /**
   * @Title findByTalentId
   * @Description 根据id查询所有联系人信息（人才库或简历）
   * @param id
   * @return List<HrContactInformation>
   * @date 2021/6/19 17:01
   * <AUTHOR>
   */
	List<HrContactInformation>findByTalentId(String id);
}
