package cn.trasen.hrms.zpgl.dao;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrmsZpglTraceResult;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglTraceResultOutListVo;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsZpglTraceResultMapper extends Mapper<HrmsZpglTraceResult> {

	List<HrmsZpglTraceResult> getAllList(Page page, HrmsZpglTraceResult record);

	List<HrmsZpglTraceResultOutListVo> getPageList(Page page, HrmsZpglTraceResult record);

}