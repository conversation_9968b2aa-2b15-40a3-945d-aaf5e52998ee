package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglEducation;
import cn.trasen.hrms.zpgl.service.HrmsZpglEducationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglEducationController
 * @Description TODO
 * @date 2023��2��8�� ����4:10:23
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglEducationController")
public class HrmsZpglEducationController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglEducationController.class);

	@Autowired
	private HrmsZpglEducationService hrmsZpglEducationService;

	/**
	 * @Title saveHrmsZpglEducation
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglEducation/save")
	public PlatformResult<String> saveHrmsZpglEducation(@RequestBody HrmsZpglEducation record) {
		try {
			hrmsZpglEducationService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglEducation
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglEducation/update")
	public PlatformResult<String> updateHrmsZpglEducation(@RequestBody HrmsZpglEducation record) {
		try {
			hrmsZpglEducationService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglEducationById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglEducation>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglEducation/{id}")
	public PlatformResult<HrmsZpglEducation> selectHrmsZpglEducationById(@PathVariable String id) {
		try {
			HrmsZpglEducation record = hrmsZpglEducationService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglEducationById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglEducation/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglEducationById(@PathVariable String id) {
		try {
			hrmsZpglEducationService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglEducationList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglEducation>
	 * @date 2023��2��8�� ����4:10:23
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglEducation/list")
	public DataSet<HrmsZpglEducation> selectHrmsZpglEducationList(Page page, HrmsZpglEducation record) {
		return hrmsZpglEducationService.getDataSetList(page, record);
	}
}
