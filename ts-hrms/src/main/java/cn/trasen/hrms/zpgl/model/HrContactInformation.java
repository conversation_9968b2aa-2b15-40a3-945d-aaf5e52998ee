package cn.trasen.hrms.zpgl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hr_contact_information")
@Setter
@Getter
public class HrContactInformation {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    @Column(name = "contact_information_id")
    private String contactInformationId;

    /**
     * 对应人员ID
     */
    @Column(name = "talent_library_id")
    @ApiModelProperty(value = "对应人员ID")
    private String talentLibraryId;

    /**
     * 联系人
     */
    @Column(name = "contact_name")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /**
     * 与员工关系
     */
    @Column(name = "relationship_with_employee")
    @ApiModelProperty(value = "与员工关系")
    private String relationshipWithEmployee;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 工作单位及任职部门
     */
    @Column(name = "work_and_dept")
    @ApiModelProperty(value = "工作单位及任职部门")
    private String workAndDept;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "create_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_date")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 0 紧急 1 非紧急 2 主管或同事
     */
    @ApiModelProperty(value = "0 紧急 1 非紧急 2 主管或同事")
    private Integer status;
}