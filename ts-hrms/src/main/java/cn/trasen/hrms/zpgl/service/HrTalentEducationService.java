package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrTalentEducation;

/**
 * @ClassName HrTalentEducationService
 * @Description TODO
 * @date 2021年6月19日 下午4:34:41
 * <AUTHOR>
 * @version 1.0
 */
public interface HrTalentEducationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:34:41
	 * <AUTHOR>
	 */
	Integer save(HrTalentEducation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021年6月19日 下午4:34:41
	 * <AUTHOR>
	 */
	Integer update(HrTalentEducation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021年6月19日 下午4:34:41
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrTalentEducation
	 * @date 2021年6月19日 下午4:34:41
	 * <AUTHOR>
	 */
	HrTalentEducation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrTalentEducation>
	 * @date 2021年6月19日 下午4:34:41
	 * <AUTHOR>
	 */
	DataSet<HrTalentEducation> getDataSetList(Page page, HrTalentEducation record);

	/**
	 * @Title findByTalentId
	 * @Description 根据id查询所有联系人信息（人才库或简历）
	 * @param id
	 * @return List<HrTalentEducation>
	 * @date 2021/6/19 17:01
	 * <AUTHOR>
	 */
	List<HrTalentEducation> findByTalentId(String id);
}
