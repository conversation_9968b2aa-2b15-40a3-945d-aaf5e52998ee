package cn.trasen.hrms.zpgl.vo.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;


@Setter
@Getter
public class HrRecruitmentTalentPoolListInputVo {

    @ApiModelProperty(value = "招聘-人才库名称")
    private String talentPoolName;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "招聘-上级机构人才库id")
    private String parentId;
    
    @ApiModelProperty(hidden = true)
    private List<String> parentIds;

    @ApiModelProperty(value = "机构编号")
    private String ssoOrgCode;
}