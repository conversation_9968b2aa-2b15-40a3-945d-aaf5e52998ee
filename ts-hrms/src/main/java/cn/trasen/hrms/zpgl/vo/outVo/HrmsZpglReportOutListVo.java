package cn.trasen.hrms.zpgl.vo.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: HrmsZpglEmployeeOutVo
 * @Description: 列表
 * <AUTHOR>
 * @date 2023年2月15日 上午10:02:56
 * 
 */
@Setter
@Getter
public class HrmsZpglReportOutListVo {

	@ApiModelProperty(value = "名称")
	private String name;

	@ApiModelProperty(value = "一月")
	private String january;

	@ApiModelProperty(value = "二月")
	private String february;

	@ApiModelProperty(value = "三月")
	private String march;

	@ApiModelProperty(value = "四月")
	private String april;
	
	@ApiModelProperty(value = "五月")
	private String may;

	@ApiModelProperty(value = "六月")
	private String june;
	
	@ApiModelProperty(value = "七月")
	private String july;
	
	@ApiModelProperty(value = "八月")
	private String august;
	
	@ApiModelProperty(value = "九月")
	private String september;

	@ApiModelProperty(value = "十月")
	private String october;

	@ApiModelProperty(value = "十一月")
	private String november;

	@ApiModelProperty(value = "十二月")
	private String december;
	
	@ApiModelProperty(value = "第一季度")
	private String firstQuarter;
	
	@ApiModelProperty(value = "第二季度")
	private String twoQuarter;

	@ApiModelProperty(value = "第三季度")
	private String threeQuarter;

	@ApiModelProperty(value = "第四季度")
	private String fourQuarter;
	
	@ApiModelProperty(value = "年度统计")
	private String year;

}
