package cn.trasen.hrms.zpgl.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrInterviewPhrases;

/**
 * @ClassName HrInterviewPhrasesService
 * @Description TODO
 * @date 2021年7月1日 下午1:49:09
 * <AUTHOR>
 * @version 1.0
 */
public interface HrInterviewPhrasesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	Integer save(HrInterviewPhrases record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	Integer update(HrInterviewPhrases record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrInterviewPhrases
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	HrInterviewPhrases selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrInterviewPhrases>
	 * @date 2021年7月1日 下午1:49:09
	 * <AUTHOR>
	 */
	DataSet<String> getDataSetList(Page page, HrInterviewPhrases record);
	/**
	 * @Title cancelLabel
	 * @Description X去掉当前阶段的常用语
	 * @param label
	 * @return
	 * @date 2021/7/1 15:06
	 * <AUTHOR>
	 */
	void cancelLabel(HrInterviewPhrases label);
}
