package cn.trasen.hrms.zpgl.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zpgl.model.HrmsZpglAcademy;
import cn.trasen.hrms.zpgl.service.HrmsZpglAcademyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsZpglAcademyController
 * @Description TODO
 * @date 2023��2��8�� ����4:12:18
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsZpglAcademyController")
public class HrmsZpglAcademyController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsZpglAcademyController.class);

	@Autowired
	private HrmsZpglAcademyService hrmsZpglAcademyService;

	/**
	 * @Title saveHrmsZpglAcademy
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/zpglAcademy/save")
	public PlatformResult<String> saveHrmsZpglAcademy(@RequestBody HrmsZpglAcademy record) {
		try {
			hrmsZpglAcademyService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsZpglAcademy
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/zpglAcademy/update")
	public PlatformResult<String> updateHrmsZpglAcademy(@RequestBody HrmsZpglAcademy record) {
		try {
			hrmsZpglAcademyService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsZpglAcademyById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsZpglAcademy>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/zpglAcademy/{id}")
	public PlatformResult<HrmsZpglAcademy> selectHrmsZpglAcademyById(@PathVariable String id) {
		try {
			HrmsZpglAcademy record = hrmsZpglAcademyService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsZpglAcademyById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/zpglAcademy/delete/{id}")
	public PlatformResult<String> deleteHrmsZpglAcademyById(@PathVariable String id) {
		try {
			hrmsZpglAcademyService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsZpglAcademyList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglAcademy>
	 * @date 2023��2��8�� ����4:12:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/zpglAcademy/list")
	public DataSet<HrmsZpglAcademy> selectHrmsZpglAcademyList(Page page, HrmsZpglAcademy record) {
		return hrmsZpglAcademyService.getDataSetList(page, record);
	}
}
