package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.zpgl.model.HrRecruitmentTalentPool;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolInputVo;
import cn.trasen.hrms.zpgl.vo.inputVo.HrRecruitmentTalentPoolListInputVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolOutVo;
import cn.trasen.hrms.zpgl.vo.outVo.HrRecruitmentTalentPoolTreeOutVo;



public interface HrRecruitmentTalentPoolService {
	
	/**
	 * 保存、编辑
	 * @param recruitmentTalentPoolInputVo
	 * @return
	 */
	Integer saveOrUpdate(HrRecruitmentTalentPoolInputVo recruitmentTalentPoolInputVo);


	/**
	 * 启用禁用
	 * @param hrEnterpriseEmailId 主键
	 * @param status 状态（N停用Y启用）
	 * @return
	 */
	Integer enableDisable(String hrEnterpriseEmailId,String status);



	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2022��8��5�� ����11:35:15
	 * <AUTHOR>
	 */
	Integer save(HrRecruitmentTalentPool record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2022��8��5�� ����11:35:15
	 * <AUTHOR>
	 */
	Integer update(HrRecruitmentTalentPool record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2022��8��5�� ����11:35:15
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrRecruitmentTalentPool
	 * @date 2022��8��5�� ����11:35:15
	 * <AUTHOR>
	 */
	HrRecruitmentTalentPool selectById(String id);


	/**
	 * 根据人才库管理名称查询
	 * @param name 人才库管理名称
	 * @return
	 */
	HrRecruitmentTalentPool selectByName(String name);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrRecruitmentTalentPool>
	 * @date 2022��8��5�� ����11:35:15
	 * <AUTHOR>
	 */
	List<HrRecruitmentTalentPoolOutVo> getDataSetList(Page page, HrRecruitmentTalentPoolListInputVo record);


	/**
	 * 树结构
	 * @param record
	 * @return
	 */
	List<HrRecruitmentTalentPoolTreeOutVo> getTree(HrRecruitmentTalentPoolListInputVo record);

	/**
	 * 查询人才库类型下所有子类型
	 * @param pid 父id
	 * @return
	 */
	List<String> queryQllSubNodes(String pid);


	/** 
	* @Title: treeSecondLevel 
	* @Description: 树一二级
	* @param @param recruitmentTalentPoolListInputVo
	* @param @return    设定文件 
	* @return List<HrRecruitmentTalentPoolTreeOutVo>    返回类型 
	* @throws 
	*/
	List<HrRecruitmentTalentPoolTreeOutVo> treeSecondLevel(HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo);
	
	/** 
	* @Title: treeSecondLevel 
	* @Description: 树单三级
	* @param @param recruitmentTalentPoolListInputVo
	* @param @return    设定文件 
	* @return List<HrRecruitmentTalentPoolTreeOutVo>    返回类型 
	* @throws 
	*/
	List<HrRecruitmentTalentPoolTreeOutVo> treeThreeLevel(HrRecruitmentTalentPoolListInputVo recruitmentTalentPoolListInputVo);
}
