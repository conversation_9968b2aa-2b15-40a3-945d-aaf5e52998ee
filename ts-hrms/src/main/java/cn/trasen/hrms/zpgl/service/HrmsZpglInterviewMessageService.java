package cn.trasen.hrms.zpgl.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zpgl.model.HrmsZpglInterviewMessage;
import cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglInterviewMessageResultOutListVo;

/**
 * @ClassName HrmsZpglInterviewMessageService
 * @Description TODO
 * @date 2023��2��9�� ����4:20:25
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsZpglInterviewMessageService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	Integer save(HrmsZpglInterviewMessage record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	Integer update(HrmsZpglInterviewMessage record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	Integer deleteByEmpId(String zpglempid);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsZpglInterviewMessage
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	HrmsZpglInterviewMessage selectById(String id);
	
	
	/** 
	* @Title: selectByEmpId 
	* @Description: 根据员工id查询最新数据
	* @param @param id
	* @param @return    设定文件 
	* @return HrmsZpglInterviewMessage    返回类型 
	* @throws 
	*/
	HrmsZpglInterviewMessage selectByEmpId(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsZpglInterviewMessage>
	 * @date 2023��2��9�� ����4:20:25
	 * <AUTHOR>
	 */
	DataSet<HrmsZpglInterviewMessageResultOutListVo> getDataSetList(Page page, HrmsZpglInterviewMessage record);

	/** 
	* @Title: getHrmsZpglEntryList 
	* @Description: 入职管理列表
	* @param @param page
	* @param @param record
	* @param @return    设定文件 
	* @return DataSet<HrmsZpglInterviewMessageResultOutListVo>    返回类型 
	* @throws 
	*/
	DataSet<HrmsZpglInterviewMessageResultOutListVo> getHrmsZpglEntryList(Page page, HrmsZpglInterviewMessage record);

	List<HrmsZpglInterviewMessageResultOutListVo> export(HrmsZpglInterviewMessage record);

	/** 
	* @Title: signIn 
	* @Description: 二维码签到
	* @param @param record
	* @param @return    设定文件 
	* @return Integer    返回类型 
	* @throws 
	*/
	Integer signIn(HrmsZpglInterviewMessage record);

	/** 
	* @Title: batchSave 
	* @Description:批量提交面试信息
	* @param @param record
	* @param @return    设定文件 
	* @return Intger    返回类型 
	* @throws 
	*/
	Integer batchSave(HrmsZpglInterviewMessage record);


	Integer deleteAllByEmpId(String zpglempid);
}
