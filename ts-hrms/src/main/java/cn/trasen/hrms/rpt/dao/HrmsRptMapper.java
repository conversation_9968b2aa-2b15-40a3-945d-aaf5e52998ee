package cn.trasen.hrms.rpt.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.model.HrmsEmployee;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsRptMapper extends Mapper<HrmsEmployee> {
	/**
	 * 获取人力资源分布
	 */
	List<Map<String,Object>> getLabrResuDistr(@Param("param") Map<String,Object> param);

	/**
	 * 获取员工分布按所属机构分组
	 */
	List<Map<String,Object>> getEmpDistrGroupbyOrg(@Param("param") Map<String,Object> param);
}