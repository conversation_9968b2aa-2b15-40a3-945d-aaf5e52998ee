package cn.trasen.hrms.utils;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.IoUtil;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;

/**   
 * @Title: ExportExcelUtil.java 
 * @Package cn.trasen.hrms.utils 
 * @Description: Excel导出工具类(泛型)
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月13日 下午4:28:07 
 * @version V1.0   
 */
public class ExportExcelUtil<T> {

	/**
	 * @Title: exportExcel
	 * @Description: 导出Excel(泛型)
	 * @param list 泛型数据集合
	 * @param exportHeaders 导出头部数据
	 * @param exportFields 导出的字段数据
	 * @param sheetName 表格名称
	 * @param outputStream
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:21:10
	 */
	public void exportExcel(List<T> list, String exportHeaders, String exportFields, String sheetName, OutputStream outputStream) {
		HSSFWorkbook workbook = createWorkBook(list, exportHeaders, exportFields, sheetName, "");
		if (workbook != null) {
			try {
				workbook.write(outputStream);
				outputStream.flush();
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * @Title: exportExcel
	 * @Description: 导出Excel(泛型)、可以设置单元格默认值
	 * @param list 泛型数据集合
	 * @param exportHeaders 导出头部数据
	 * @param exportFields 导出的字段数据
	 * @param sheetName 表格名称
	 * @param cellDefaultValue 单元格默认值
	 * @param outputStream
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月29日 下午1:45:46
	 */
	public void exportExcel(List<T> list, String exportHeaders, String exportFields, String sheetName, String cellDefaultValue, OutputStream outputStream) {
		HSSFWorkbook workbook = createWorkBook(list, exportHeaders, exportFields, sheetName, cellDefaultValue);
		if (workbook != null) {
			try {
				workbook.write(outputStream);
				outputStream.flush();
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * @Title: exportMapExcel
	 * @Description: 导出Excel(List<Map<String, Object>>)
	 * @param list
	 * @param exportHeaders
	 * @param exportFields
	 * @param sheetName
	 * @param outputStream
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月19日 下午4:06:45
	 */
	public void exportMapExcel(List<Map<String, Object>> list, String exportHeaders, String exportFields, String sheetName, OutputStream outputStream) {
		HSSFWorkbook workbook = createWorkBookToMap(list, exportHeaders, exportFields, sheetName);
		if (workbook != null) {
			try {
				workbook.write(outputStream);
				outputStream.flush();
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * @Title: createWorkBook
	 * @Description: 创建工作簿
	 * @param wwb
	 * @param list 泛型数据集合
	 * @param exportHeaders 导出头部数据
	 * @param exportFields 导出的字段数据
	 * @param sheetName 表格名称
	 * @param cellDefaultValue 单元格默认值
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:24:40
	 */
	private HSSFWorkbook createWorkBook(List<T> list, String exportHeaders, String exportFields, String sheetName, String cellDefaultValue) {
		HSSFWorkbook wwb = new HSSFWorkbook();
		// 创建工作薄
		HSSFSheet sheet = null;
		// 创建行
		HSSFRow row = null;

		// 表头单元格样式
		HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 12);
		// 表体单元格样式
		HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 10);

		// 报表头
		int defaultWidth = 18 * 256 + 184;
		String[] headerStrings = exportHeaders.split(",");
		if (headerStrings != null && headerStrings.length > 0) {
			sheet = wwb.createSheet(sheetName);
			for (int i = 0; i < headerStrings.length; i++) {
				sheet.setColumnWidth(i, defaultWidth);
			}
		}
		HSSFCell cell = null;
		row = sheet.createRow(0);
		for (int j = 0; j < headerStrings.length; j++) {
			cell = row.createCell(j);
			cell.setCellValue(headerStrings[j]);
			cell.setCellStyle(headerCellStyle);
		}
		createCell(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportHeaders, exportFields, cellDefaultValue);
		return wwb;
	}

	/**
	 * @Title: createWorkBookToMap
	 * @param list Map<String, Object>数据集合
	 * @param exportHeaders 导出头部数据
	 * @param exportFields 导出的字段数据
	 * @param sheetName 表格名称
	 * @Return HSSFWorkbook
	 * <AUTHOR>
	 * @date 2020年5月19日 下午4:07:34
	 */
	private HSSFWorkbook createWorkBookToMap(List<Map<String, Object>> list, String exportHeaders, String exportFields, String sheetName) {
		HSSFWorkbook wwb = new HSSFWorkbook();
		// 创建工作薄
		HSSFSheet sheet = null;
		// 创建行
		HSSFRow row = null;

		// 表头单元格样式
		HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 12);
		// 表体单元格样式
		HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 10);

		// 报表头
		int defaultWidth = 13 * 256 + 184;
		String[] headerStrings = exportHeaders.split(",");
		if (headerStrings != null && headerStrings.length > 0) {
			sheet = wwb.createSheet(sheetName);
			for (int i = 0; i < headerStrings.length; i++) {
				sheet.setColumnWidth(i, defaultWidth);
			}
		}
		HSSFCell cell = null;
		row = sheet.createRow(0);
		for (int j = 0; j < headerStrings.length; j++) {
			cell = row.createCell(j);
			cell.setCellValue(headerStrings[j]);
			cell.setCellStyle(headerCellStyle);
		}
		createCellToMap(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportFields);
		return wwb;
	}

	/**
	 * @Title: createHeaderStyle
	 * @Description: 设置表头样式
	 * @param wwb
	 * @param fontSize
	 * @Return HSSFCellStyle
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:24:16
	 */
	private HSSFCellStyle createHeaderStyle(HSSFWorkbook wwb, short fontSize) {
		HSSFFont headerFont = wwb.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints(fontSize);

		HSSFCellStyle headerCellStyle = wwb.createCellStyle();
		headerCellStyle.setWrapText(true);
		headerCellStyle.setFont(headerFont);
		headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
		headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerCellStyle.setBorderLeft(BorderStyle.THIN);
		headerCellStyle.setBorderTop(BorderStyle.THIN);
		headerCellStyle.setBorderRight(BorderStyle.THIN);
		headerCellStyle.setBorderBottom(BorderStyle.THIN);
		return headerCellStyle;
	}

	/**
	 * @Title: createColStyle
	 * @Description: 设置列内容样式
	 * @param wwb
	 * @param fontSize
	 * @Return HSSFCellStyle
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:25:24
	 */
	private HSSFCellStyle createColStyle(HSSFWorkbook wwb, short fontSize) {
		HSSFFont colFont = wwb.createFont();
		colFont.setFontHeightInPoints(fontSize);

		HSSFCellStyle colCellStyle = wwb.createCellStyle();
		colCellStyle.setWrapText(true);
		colCellStyle.setFont(colFont);
		colCellStyle.setAlignment(HorizontalAlignment.CENTER);
		colCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		colCellStyle.setBorderLeft(BorderStyle.THIN);
		colCellStyle.setBorderTop(BorderStyle.THIN);
		colCellStyle.setBorderRight(BorderStyle.THIN);
		colCellStyle.setBorderBottom(BorderStyle.THIN);
		return colCellStyle;
	}

	/**
	 * @Title: createCell
	 * @Description: excel列赋值
	 * @param list 泛型数据集合
	 * @param row
	 * @param cell
	 * @param sheet
	 * @param headerCellStyle
	 * @param bodyCellStyle
	 * @param exportFields
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:25:57
	 */
	private void createCell(List<T> list, HSSFRow row, HSSFCell cell, HSSFSheet sheet, HSSFCellStyle headerCellStyle, HSSFCellStyle bodyCellStyle, String exportHeaders, String exportFields, String cellDefaultValue) {
		try {
			for (int i = 0; i < list.size(); i++) {
				row = sheet.createRow(i + 1);
				cell = row.createCell(0);
				cell.setCellValue(i + 1);
				cell.setCellStyle(bodyCellStyle);

				T dto = list.get(i);
				String[] headerStrings = exportHeaders.split(","); // 导出的头部数组
				String[] fieldStrings = exportFields.split(","); // 选中的导出的字段数组
				for (int j = 0; j < headerStrings.length; j++) {
					int fieldArrayLen = fieldStrings.length - 1;
					cell = row.createCell(j);
					cell.setCellStyle(bodyCellStyle);
					if (j <= fieldArrayLen) {
						String name = fieldStrings[j];
						String getMethodName = "get" + name.substring(0, 1).toUpperCase() + name.substring(1); // 获取方法名
						Method method = dto.getClass().getMethod(getMethodName); // 获取dto对象中的方法名
						Object obj = method.invoke(dto); // 获取方法名下面的值
						cell = getCellValueByObject(cell, obj);
					} else {
						cell.setCellValue(cellDefaultValue);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @Title: createCellToMap
	 * @Description: excel列赋值
	 * @param list Map<String, Object>数据集合
	 * @param row
	 * @param cell
	 * @param sheet
	 * @param headerCellStyle
	 * @param bodyCellStyle
	 * @param exportFields
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年5月19日 下午4:08:17
	 */
	private void createCellToMap(List<Map<String, Object>> list, HSSFRow row, HSSFCell cell, HSSFSheet sheet, HSSFCellStyle headerCellStyle, HSSFCellStyle bodyCellStyle, String exportFields) {
		try {
			for (int i = 0; i < list.size(); i++) {
				row = sheet.createRow(i + 1);
				String[] fieldStrings = exportFields.split(","); // 选中的导出的字段数组
				for (int j = 0; j < fieldStrings.length; j++) {
					String name = fieldStrings[j];
					Object obj = list.get(i).get(name);
					Boolean isNum = false;// data是否为数值型
					if (obj != null || "".equals(obj)) {
						// 判断obj是否为数值型
						isNum = obj.toString().matches("^(-?\\d+)(\\.\\d+)?$");
					}
					if (isNum) {
						obj = Double.parseDouble(obj.toString());
					}
					cell = row.createCell(j);
					cell = getCellValueByObject(cell, obj);
					cell.setCellStyle(bodyCellStyle);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @Title: getCellValueByObject
	 * @Description: 获取单元格的值
	 * @param cell
	 * @param param
	 * @Return HSSFCell
	 * <AUTHOR>
	 * @date 2020年5月19日 上午11:26:51
	 */
	private HSSFCell getCellValueByObject(HSSFCell cell, Object param) {
		if (param instanceof Integer) {
			int value = ((Integer) param).intValue();
			cell.setCellValue(value);
		} else if (param instanceof String) {
			String s = (String) param;
			cell.setCellValue(s);
		} else if (param instanceof BigDecimal) {
			double doubleVal = ((BigDecimal) param).doubleValue();
			cell.setCellValue(doubleVal);
		} else if (param instanceof Double) {
			double d = ((Double) param).doubleValue();
			cell.setCellValue(d);
		} else if (param instanceof Float) {
			float f = ((Float) param).floatValue();
			cell.setCellValue(f);
		} else if (param instanceof Long) {
			long l = ((Long) param).longValue();
			cell.setCellValue(l);
		} else if (param instanceof Boolean) {
			boolean b = ((Boolean) param).booleanValue();
			cell.setCellValue(b);
		} else if (param instanceof Date) {
			Date d = (Date) param;
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String time = sdf.format(d);
			cell.setCellValue(time);
		}
		return cell;
	}
	
	 public void downloadExportExcel(String filename, String path, HttpServletResponse response, ResourceLoader resourceLoader) {
	        InputStream inputStream = null;
	        ServletOutputStream servletOutputStream = null;
	        try {
	            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:" + path);
	            response.setContentType("application/vnd.ms-excel");
	            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
	            response.addHeader("charset", "utf-8");
	            response.addHeader("Pragma", "no-cache");
	            String encodeName = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
	            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
	            inputStream = resource.getInputStream();
	            servletOutputStream = response.getOutputStream();
	            IOUtils.copy(inputStream, servletOutputStream);
	            response.flushBuffer();
	        } catch (Exception e) {
	            e.printStackTrace();
	        } finally {
	            try {
	                if (servletOutputStream != null) {
	                    servletOutputStream.close();
	                    servletOutputStream = null;
	                }
	                if (inputStream != null) {
	                    inputStream.close();
	                    inputStream = null;
	                }
	                // 召唤jvm的垃圾回收器
	                System.gc();
	            } catch (Exception e) {
	                e.printStackTrace();
	            }
	        }
	    }


	/**
	 * @description:
	 * @param: filename
	 * @param: path
	 * @param: response
	 * @param: resourceLoader
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/7/29 13:52
	 */
	public void downloadExportExcel(String filename, HttpServletResponse response, ClassPathResource resourceLoader) {



		OutputStream stream =null;

		try {

			if (resourceLoader.exists()) {
				response.setContentType("application/vnd.ms-excel");
				response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
				response.addHeader("charset", "utf-8");
				response.addHeader("Pragma", "no-cache");
				String encodeName = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
				response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
			} else {
				response.setStatus(404);
			}


			stream = new BufferedOutputStream(response.getOutputStream());
			stream.write(IoUtil.readBytes(resourceLoader.getInputStream()));
			stream.flush();
		} catch (IOException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		finally {
			if(stream!=null)
			{
				try {
					stream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

}
