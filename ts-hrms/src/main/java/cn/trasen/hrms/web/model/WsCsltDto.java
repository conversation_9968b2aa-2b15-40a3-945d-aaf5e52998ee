package cn.trasen.hrms.web.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class WsCsltDto {
    
    @ApiModelProperty(value = "会诊类别")
    private String csltTypeName;

    @ApiModelProperty(value = "会诊类型")
    private String csltMoldName;

    @ApiModelProperty(value = "会诊级别名称")
    private String csltLvName;
    
    @ApiModelProperty(value = "会诊级别")
    private String csltLv;

    @ApiModelProperty(value = "院区")
    private String hospAreaName;

    @ApiModelProperty(value = "科室ID")
    private String orgId;

    @ApiModelProperty(value = "科室名称")
    private String orgName;
    
    @ApiModelProperty(value = "医生ID")
    private String employeeId;
    
    @ApiModelProperty(value = "医生姓名")
    private String employeeName;

    @ApiModelProperty(value = "医生工号")
    private String employeeNo;
    
    @ApiModelProperty(value = "医生联系电话")
    private String employeeTel;
    
    @ApiModelProperty(value = "会诊范围")
    private String csltScp;
}
