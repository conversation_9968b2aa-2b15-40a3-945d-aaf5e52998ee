package cn.trasen.hrms.web.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltFnsVo {
	
	@ApiModelProperty(value = "会诊申请ID")
	@NotEmpty(message="会诊申请ID不能为空！")
	@Size(min = 1, max = 50, message = "会诊申请ID长度必须在1-50个字符之间！")
	private String csltAppyId;
	
	@ApiModelProperty(value = "操作类型：0取消 1会诊")
	@NotEmpty(message="操作类型不能为空！")
	@Pattern(regexp = "0|1", message = "操作类型必须是 '0' 或 '1' ！")
	private String type;
	
	@ApiModelProperty(value = "完成会诊")
	@NotEmpty(message="完成会诊时间不能为空！")
	@Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "完成会诊格式必须为 YYYY-MM-DD HH:MM:SS")
	private String fnsTime;
	
	@ApiModelProperty(value = "完成会诊医生ID")
	@Size(min = 1, max = 50, message = "完成会诊医生ID长度必须在1-50个字符之间！")
    private String fnsEmployeeId;

    @ApiModelProperty(value = "完成会诊医生姓名")
    @NotEmpty(message="完成会诊医生姓名不能为空！")
	@Size(min = 1, max = 50, message = "完成会诊医生姓名长度必须在1-50个字符之间！")
    private String fnsEmployeeName;
    
    @ApiModelProperty(value = "完成会诊医生工号")
    @NotEmpty(message="完成会诊医生工号不能为空！")
    @Size(min = 1, max = 50, message = "完成会诊医生工号长度必须在1-50个字符之间！")
    private String fnsEmployeeNo;
    
    @ApiModelProperty(value = "完成医生技术职称")
    @Size(max = 50, message = "完成医生技术职称不能超过50个字符！")
    private String fnsJobtitle;
    
    @ApiModelProperty(value = "完成会诊医生手机号码")
    @Size(max = 50, message = "完成会诊医生手机号码不能超过50个字符！")
    private String fnsTel;

    @ApiModelProperty(value = "会诊意见(申请科室)")
    @Size( max = 1000, message = "会诊意见(申请科室)不能超过1000个字符！")
    private String appyOrgDscr;

    @ApiModelProperty(value = "会诊意见(会诊科室)")
    @Size( max = 1000, message = "会诊意见(会诊科室)不能超过1000个字符！")
    private String csltOrgDscr;
    
	@ApiModelProperty(value = "操作人账号")
	@NotEmpty(message="操作人账号不能为空！")
	@Size(min = 1, max = 50, message = "操作人账号长度必须在1-50个字符之间！")
	private String oprtUser;
	
	@ApiModelProperty(value = "操作人姓名")
	@NotEmpty(message="操作人姓名不能为空！")
	@Size(min = 1, max = 50, message = "操作人姓名长度必须在1-50个字符之间！")
	private String oprtUserName;
	
	@ApiModelProperty(value = "会诊目的答复评分")
    @Size( max = 20, message = "会诊目的答复评分不能超过20个字符！")
    private String hzmddfScore;
	
	@ApiModelProperty(value = "会诊意见评分")
    @Size( max = 20, message = "会诊意见评分不能超过20个字符！")
    private String hzyjScore;
	
    @ApiModelProperty(value = "会诊目的评分")
    @Size( max = 20, message = "会诊目的评分不能超过20个字符！")
    private String hzmdScore;
    
    @ApiModelProperty(value = "会诊资料评分")
    @Size( max = 20, message = "会诊资料评分不能超过20个字符！")
    private String hzzlScore;
    
    @ApiModelProperty(value = "会诊必要性评分")
    @Size( max = 20, message = "会诊必要性评分不能超过20个字符！")
    private String hzbyxScore;
}
