package cn.trasen.hrms.med.qua.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

@Table(name = "med_pharmacist_role")
@Setter
@Getter
public class MedPharmacistRole {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 员工工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 流程id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程id")
    private String workflowId;

    /**
     * 类型 11处方调剂权限  12临床药学技术权限 21 高风险操作权限
     */
    @Column(name = "role_type")
    @ApiModelProperty(value = "类型")
    private String roleType;
    
    @Column(name = "risk_cfg_type")
    @ApiModelProperty(value = "高风险操作权限类型")
    private String riskCfgType;
    
    @Column(name = "risk_cfg_code")
    @ApiModelProperty(value = "高风险操作权限编码")
    private String riskCfgCode;

    /**
     * 
     */
    @Column(name = "role_value")
    @ApiModelProperty(value = "1-普通药品、1-麻醉精神药品、1-抗菌药物 1-抗肿瘤药物 1-放射性药品 1-中药饮片 2-处方审核 2-药学门诊 2-药学会诊 2-药学巡诊 2-药学检测")
    private String roleValue;

    /**
     * 授权状态  0停止授权 2继续授权
     */
    @Column(name = "auth_status")
    @ApiModelProperty(value = "授权状态 0停止授权 1继续授权")
    private String authStatus;

    /**
     * 操作备注
     */
    @Column(name = "opt_remark")
    @ApiModelProperty(value = "操作备注")
    private String optRemark;

    /**
     * 操作附件
     */
    @Column(name = "opt_file")
    @ApiModelProperty(value = "操作附件")
    private String optFile;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    
    @Transient
    private String employeeName;
    
    @Transient
    private String jobtitle;
    
    @Transient
    private String hospCode;
    
    @Transient
    private String orgName;
    
    @Transient
    private String identityNumber;
    
    @Transient
    private String tel;
    
    @Transient
    private String hospAreaName; 
    
    @Transient
    private String startDate;
    
    @Transient
    private String endDate;
    
    @Transient
    private String orgId;
    
    @Transient
    private List<String> orgIds;
    
    @Transient
    private List<String> orgIdList;
    
     //11处方调剂权限  12临床药学技术权限
    @Transient
    private String index;  
    
    @Transient
    private String cfqName; 
    
    @Transient
    private String cfqLevel; 
    
    @Transient
    private Date endtime;
    
    @Transient
    private Date appyTime;
    
    @Transient
    private String optType; //1继续授权  2停止授权
    
    @Transient
    private String auditFlag;
    
    @Transient
    private int orderNumber;
    
    @Transient
    private String authStatusText;
}