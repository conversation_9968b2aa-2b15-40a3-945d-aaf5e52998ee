package cn.trasen.hrms.med.newTech.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 新技术新项目表
 * @date 2025-05-12 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "med_new_tech_info")
@Setter
@Getter
public class NewTechInfo {
	
	//既是主键ID也是流程实例ID
    @Id
    @ApiModelProperty(value = "主键-转入流程实例ID")
    private String id;
    
    @Column(name = "business_id")
    @ApiModelProperty(value = "准入流程业务ID")
    private String businessId;
    
    @Column(name = "normal_workflow_inst_id")
    @ApiModelProperty(value = "常规流程实例ID")
    private String normalWorkflowInstId;
    
    @Column(name = "normal_business_id")
    @ApiModelProperty(value = "常规流程业务ID")
    private String normalBusinessId;

    /****************项目信息 *************/
    
    @Column(name = "tech_name")
    @ApiModelProperty(value = "项目名称")
    private String techName;

    @Column(name = "tech_code")
    @ApiModelProperty(value = "伦理号")
    private String techCode;

    @Column(name = "is_new_tech")
    @ApiModelProperty(value = "是否新技术：0-否，1-是")
    private String isNewTech;

    @Column(name = "is_rstd_tech")
    @ApiModelProperty(value = "是否限制性类技术：0-否，1-是")
    private String isRstdTech;

    @Column(name = "tech_status")
    @ApiModelProperty(value = "状态：1-开展，2-中止，3-转常规")
    private String techStatus;

    @Column(name = "reason")
    @ApiModelProperty(value = "原因")
    private String reason;

    @Column(name = "tech_class")
    @ApiModelProperty(value = "项目分类：1-Ⅰ类，2-Ⅱ类，3-Ⅲ类")
    private String techClass;

    @Column(name = "tech_type")
    @ApiModelProperty(value = "技术类型：1-手术类，2-治疗操作类，3-检验检查类，4-其他类")
    private String techType;

    @Column(name = "item_op_name")
    @ApiModelProperty(value = "手术/操作 名称-新技术名称")
    private String itemOpName;

    @Column(name = "item_op_code")
    @ApiModelProperty(value = "手术/操作 编码")
    private String itemOpCode;

    @Column(name = "auth_lv_hosp")
    @ApiModelProperty(value = "院内分级")
    private String authLvHosp;
    
    @Transient
    @ApiModelProperty(value = "院内分级文本")
    private String authLvHospText;

    @Column(name = "auth_lv_nat")
    @ApiModelProperty(value = "国家分级")
    private String authLvNat;

    @Transient
    @ApiModelProperty(value = "国家分级文本")
    private String authLvNatText;

//    @Column(name = "treat_item_name")
//    @ApiModelProperty(value = "治疗操作名称")
//    private String treatItemName;
//
//    @Column(name = "treat_item_code")
//    @ApiModelProperty(value = "治疗操作编码")
//    private String treatItemCode;

    @Column(name = "tech_catg")
    @ApiModelProperty(value = "项目类别")
    private String techCatg;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "apply_date")
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "start_date")
    @ApiModelProperty(value = "开展日期")
    private Date startDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "end_date")
    @ApiModelProperty(value = "中止日期")
    private Date endDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "transform_date")
    @ApiModelProperty(value = "转常规日期")
    private Date transformDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "cycle_start_date")
    @ApiModelProperty(value = "项目周期开始日期")
    private Date cycleStartDate;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "cycle_end_date")
    @ApiModelProperty(value = "项目周期结束日期")
    private Date cycleEndDate;

    
    /****************项目负责人 *************/

    @Column(name = "pro_employee_id")
    @ApiModelProperty(value = "项目负责人")
    private String proEmployeeId;

    @Column(name = "pro_employee_name")
    @ApiModelProperty(value = "项目负责人姓名")
    private String proEmployeeName;
    
    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @Transient
    @ApiModelProperty(value = "项目负责人工号")
    private String proEmployeeNo;

    @Transient
    @ApiModelProperty(value = "项目负责人性别")
    private String proSex;

    @Transient
    @ApiModelProperty(value = "项目负责人出生年月")
    private Date proBirthDate;

    @Transient
    @ApiModelProperty(value = "项目负责人所属机构编码")
    private String proSsoOrgCode;

    @Transient
    @ApiModelProperty(value = "项目负责人所属机构名称")
    private String proSsoOrgName;

    @Transient
    @ApiModelProperty(value = "项目负责人所属院区编码")
    private String proHospCode;

    @Transient
    @ApiModelProperty(value = "项目负责人所属院区名称")
    private String proHospArea;

    @Transient
    @ApiModelProperty(value = "项目负责人科室ID")
    private String proOrgId;

    @Transient
    @ApiModelProperty(value = "项目负责人科室名称")
    private String proOrgName;

    @Transient
    @ApiModelProperty(value = "项目负责人身份证号")
    private String proIdentityNumber;

    @Transient
    @ApiModelProperty(value = "项目负责人手机号")
    private String proTel;

    @Transient
    @ApiModelProperty(value = "职务")
    private String dutyTitle;

    @Transient
    @ApiModelProperty(value = "职称")
    private String jobtitle;
    
    @Transient
    @ApiModelProperty(value = "获取职称时间")
    private Date jobTitleDate;

    @Transient
    @ApiModelProperty(value = "最高学历")
    private String educationType;
    
    @Transient
    @ApiModelProperty(value = "项目参与人员ID集合")
    private List<String> participantIdList;
    
    /****************评估信息*************/
    
    @Column(name = "evaluation_count")
    @ApiModelProperty(value = "评估次数")
    private Integer evaluationCount;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "evaluation_date")
    @ApiModelProperty(value = "最新评估日期")
    private Date evaluationDate;

    @Column(name = "evaluation_result")
    @ApiModelProperty(value = "最新评估结果")
    private String evaluationResult;
    
    /****************审计信息 *************/

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识：N-未删除，Y-已删除")
    private String isDeleted;
    
    @Transient
    @ApiModelProperty(value = "参与人员列表")
    private List<NewTechParticipants> participants;
    
    @Transient
    @ApiModelProperty(value = "开展病例列表")
    private List<NewTechPatientInfo> patients;
    
    @Transient
    @ApiModelProperty(value = "开展病例数")
    private Integer patientCount;
    
    @Transient
    @ApiModelProperty(value = "不良事件例数")
    private Integer adverseEventCount;
    
    @Transient
    @ApiModelProperty(value = "并发症例数")
    private Integer complicationsCount;
    
    @Transient
    @ApiModelProperty(value = "医疗投诉纠纷例数")
    private Integer complaintOrDisputeCount;
    
    @Transient
    @ApiModelProperty(value = "非计划再次手术")
    private Integer unplannedReoperationCount;
    
    @Transient
    @ApiModelProperty(value = "参与人数")
    private Integer participantCount;
    
    @Transient
    @ApiModelProperty(value = "值域-用于流程中下拉选择")
    private String itemValue;
    
    @Transient
    @ApiModelProperty(value = "显示域-用于流程中下拉选择")
    private String itemName;
    
    @Transient
    @ApiModelProperty(value = "所属科室ID集合，多个用逗号隔开")
    private String orgIds;
    
    @Transient
    @ApiModelProperty(value = "所属科室ID集合，多个用逗号隔开")
    private List<String> orgIdList;
    
    @Transient
    @ApiModelProperty(value = "查询关键字")
    private String condition;

    @Transient
    @ApiModelProperty(value = "开展日期")
    private String startDateStr;
    
    @Transient
    @ApiModelProperty(value = "中止日期")
    private String endDateStr;
    
    @Transient
    @ApiModelProperty(value = "转常规日期")
    private String transformDateStr;

}
