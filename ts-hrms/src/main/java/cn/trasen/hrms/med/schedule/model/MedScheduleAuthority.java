package cn.trasen.hrms.med.schedule.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;
import lombok.*;

/**
 * 排班权限
 *
 */
@Table(name = "med_schedule_authority")
@Setter
@Getter
public class MedScheduleAuthority {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 排班管理员名称
     */
    @Column(name = "manage_name")
    @ApiModelProperty(value = "排班管理员名称")
    private String manageName;

    /**
     * 排班管理员code
     */
    @Column(name = "manage_user_code")
    @ApiModelProperty(value = "排班管理员code")
    private String manageUserCode;

    /**
     * 考勤组范围名称
     */
    @Column(name = "schedule_name")
    @ApiModelProperty(value = "考勤组范围名称")
    private String scheduleName;

    /**
     * 考勤组范围机构id
     */
    @Column(name = "schedule_org")
    @ApiModelProperty(value = "考勤组范围机构id")
    private String scheduleOrg;

    /**
     * 考勤组范围用户编码
     */
    @Column(name = "schedule_user")
    @ApiModelProperty(value = "考勤组范围用户id")
    private String scheduleUser;

    /**
     * 备注
     */
    @Column(name = "authority_remark")
    @ApiModelProperty(value = "备注")
    private String authorityRemark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    private String autoGenerate;
    
    @Transient
    @ApiModelProperty(value = "是否强制更新使用范围,默认否")
    private boolean forceUpdate;
   
}