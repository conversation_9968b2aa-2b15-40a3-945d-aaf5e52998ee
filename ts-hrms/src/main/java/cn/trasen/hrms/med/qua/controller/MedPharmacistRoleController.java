package cn.trasen.hrms.med.qua.controller;

import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.qua.model.MedDoctorRole;
import cn.trasen.hrms.med.qua.model.MedPharmacistRole;
import cn.trasen.hrms.med.qua.service.MedPharmacistRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedPharmacistRoleController
 * @Description TODO
 * @date 2025��3��7�� ����4:27:56
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "药师权限申请")
public class MedPharmacistRoleController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedPharmacistRoleController.class);

	@Autowired
	private MedPharmacistRoleService medPharmacistRoleService;

	/**
	 * @Title saveMedPharmacistRole
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:27:56
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/pharmacistRole/save")
	public PlatformResult<String> saveMedPharmacistRole(@RequestBody MedPharmacistRole record) {
		try {
			medPharmacistRoleService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedPharmacistRole
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:27:56
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/pharmacistRole/update")
	public PlatformResult<String> updateMedPharmacistRole(@RequestBody MedPharmacistRole record) {
		try {
			medPharmacistRoleService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedPharmacistRoleById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedPharmacistRole>
	 * @date 2025��3��7�� ����4:27:56
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/pharmacistRole/{id}")
	public PlatformResult<MedPharmacistRole> selectMedPharmacistRoleById(@PathVariable String id) {
		try {
			MedPharmacistRole record = medPharmacistRoleService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedPharmacistRoleById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:27:56
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/pharmacistRole/delete/{id}")
	public PlatformResult<String> deleteMedPharmacistRoleById(@PathVariable String id) {
		try {
			medPharmacistRoleService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedPharmacistRoleList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedPharmacistRole>
	 * @date 2025��3��7�� ����4:27:56
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/pharmacistRole/list")
	public DataSet<MedPharmacistRole> selectMedPharmacistRoleList(Page page, MedPharmacistRole record) {
		return medPharmacistRoleService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "继续授权/停止授权", notes = "继续授权/停止授权")
	@PostMapping("/api/pharmacistRole/authorize")
	public PlatformResult<String> authorize(@RequestBody MedPharmacistRole record) {
		try {
			medPharmacistRoleService.authorize(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "流程回调", notes = "流程回调")
	@PostMapping("/api/pharmacistRole/finishExamine")
	public void finishExamine(HttpServletRequest request) {
		try {
			
			Map<String, Object> formData = new HashMap<>();
			Enumeration<String> enu = request.getParameterNames();
			while (enu.hasMoreElements()) {
				String key = (String) enu.nextElement();
				formData.put(key, request.getParameter(key));
			}
			// 业务id
			String L_BusinessId = formData.get("L_BusinessId").toString(); //业务id
		    String L_LaunchUserCode = (String) formData.get("L_LaunchUserCode");//流程发起人编码
		    
		    String roleTypeDjs = (String) formData.get("roleTypeDj");//处方调剂权限
		    
		    if(StringUtils.isNotBlank(roleTypeDjs)) {
		    	String[] roleTypeDjArray = roleTypeDjs.split(",");
		    	for (String roleTypeDj : roleTypeDjArray) {
		    		if(!"无".equals(roleTypeDj)) {
		    			MedPharmacistRole medPharmacistRole =  new MedPharmacistRole();
			 		    medPharmacistRole.setWorkflowId(L_BusinessId);
			 		    medPharmacistRole.setRoleType("11");
			 		    medPharmacistRole.setEmployeeId(L_LaunchUserCode);
			 		    medPharmacistRole.setEmployeeNo(L_LaunchUserCode);
			 		    medPharmacistRole.setRoleValue(roleTypeDj);
			 		    medPharmacistRole.setAuthStatus("1");
			 		    
			 		    medPharmacistRoleService.savePharmacistRole(medPharmacistRole);
		    		}
				}
		    }
		    
		    String roleTypeJss = (String) formData.get("roleTypeJs");//临床药学技术权限
		    if(StringUtils.isNotBlank(roleTypeJss)) {
		    	String[] roleTypeJsArray = roleTypeJss.split(",");
		    	for (String roleTypeJs : roleTypeJsArray) {
		    		if(!"无".equals(roleTypeJs)) {
		    			MedPharmacistRole medPharmacistRole =  new MedPharmacistRole();
			 		    medPharmacistRole.setWorkflowId(L_BusinessId);
			 		    medPharmacistRole.setRoleType("12");
			 		    medPharmacistRole.setEmployeeId(L_LaunchUserCode);
			 		    medPharmacistRole.setEmployeeNo(L_LaunchUserCode);
			 		    medPharmacistRole.setRoleValue(roleTypeJs);
			 		    medPharmacistRole.setAuthStatus("1");
			 		    
			 		    medPharmacistRoleService.savePharmacistRole(medPharmacistRole);
		    		}
				}
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@ApiOperation(value = "高风险操作流程回调", notes = "高风险操作流程回调")
	@PostMapping("/api/pharmacistRole/highRisk/finishExamine")
	public void highRiskFinishExamine(HttpServletRequest request) {
		try {
			
			Map<String, Object> formData = new HashMap<>();
			Enumeration<String> enu = request.getParameterNames();
			while (enu.hasMoreElements()) {
				String key = (String) enu.nextElement();
				formData.put(key, request.getParameter(key));
			}
			// 业务id
			String L_BusinessId = formData.get("L_BusinessId").toString(); //业务id
		    String L_LaunchUserCode = (String) formData.get("L_LaunchUserCode");//流程发起人编码
		    String L_LaunchUserName = (String) formData.get("L_LaunchUserName");//流程发起人名称
		    String L_LaunchDeptCode = (String) formData.get("L_LaunchDeptCode");//流程发起人部门编码
		    String L_LaunchDeptName = (String) formData.get("L_LaunchDeptName");//流程发起人部门名称
		    
		    String L_name = (String) formData.get("L_name");
		    logger.info("========高风险操作回调参数==========" + L_name);
		    
		    medPharmacistRoleService.saveOrUpdate(L_name,L_BusinessId,L_LaunchUserCode,L_LaunchUserName,L_LaunchDeptCode,L_LaunchDeptName);
		    
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/pharmacistRole/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,MedPharmacistRole record) {
		try {
			page.setPageNo(1);
			page.setPageSize(100000);
			
			DataSet<MedPharmacistRole> dataSet = medPharmacistRoleService.getDataSetList(page, record);
			List<MedPharmacistRole> list = dataSet.getRows();
			
			int i = 1;
			for (MedPharmacistRole medPharmacistRole : list) {
				medPharmacistRole.setOrderNumber(i);
				if("1".equals(medPharmacistRole.getAuthStatus())) {
					medPharmacistRole.setAuthStatusText("已授权");
				}else {
					medPharmacistRole.setAuthStatusText("未授权");
				}
				
				i++;
			}
			
			String title = "处方调剂权";
			String templateUrl = "template/medDoctorRole.xlsx";
			
			if("11".equals(record.getIndex())) {
				title = "处方调剂权";
			}
			if("12".equals(record.getIndex())) {
				title = "临床药师技术权";
			}
			if("21".equals(record.getIndex())) {
				title = "高风险项目操作权限";
				templateUrl = "template/medPharmacistRole.xlsx";
			}
			
			//表头标题
            String name = title + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
		    map.put("title", title);
		    
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
