package cn.trasen.hrms.med.risk.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.dao.PatientInfoMapper;
import cn.trasen.hrms.med.risk.model.RiskPatientOperation;
import cn.trasen.hrms.med.risk.service.RiskPatientOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RiskPatientOperationController
 * @Description TODO
 * @date 2025��4��29�� ����9:43:03
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "RiskPatientOperationController")
public class RiskPatientOperationController {

	private transient static final Logger logger = LoggerFactory.getLogger(RiskPatientOperationController.class);

	@Autowired
	private RiskPatientOperationService riskPatientOperationService;
	
	@Autowired
	private PatientInfoMapper patientInfoMapper;

	/**
	 * @Title saveRiskPatientOperation
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medRiskPatientOperation/save")
	public PlatformResult<String> saveRiskPatientOperation(@RequestBody RiskPatientOperation record) {
		try {
			riskPatientOperationService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRiskPatientOperation
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medRiskPatientOperation/update")
	public PlatformResult<String> updateRiskPatientOperation(@RequestBody RiskPatientOperation record) {
		try {
			riskPatientOperationService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRiskPatientOperationById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RiskPatientOperation>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medRiskPatientOperation/{id}")
	public PlatformResult<RiskPatientOperation> selectRiskPatientOperationById(@PathVariable String id) {
		try {
			RiskPatientOperation record = riskPatientOperationService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRiskPatientOperationById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medRiskPatientOperation/delete/{id}")
	public PlatformResult<String> deleteRiskPatientOperationById(@PathVariable String id) {
		try {
			riskPatientOperationService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRiskPatientOperationList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RiskPatientOperation>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medRiskPatientOperation/list")
	public DataSet<RiskPatientOperation> selectRiskPatientOperationList(Page page, RiskPatientOperation record) {
		return riskPatientOperationService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Title selectPageList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RiskPatientOperation>
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/medRiskPatientOperation/pageList")
	public DataSet<RiskPatientOperation> selectPageList(Page page, RiskPatientOperation record) {
		if(!StringUtils.isEmpty(record.getDeptId())) {
			//List<String> deptList = patientInfoMapper.getHisDeptIds(Arrays.asList(record.getDeptId().split(",")));//查询对应的HIS科室id
			List<String> deptList = patientInfoMapper.getHisDeptIdsByksys(Arrays.asList(record.getDeptId().split(",")), "HIS");//查询对应的HIS科室id
			record.setDeptList(deptList);
		}
		return riskPatientOperationService.selectPageList(page, record);
	}
	
	
	/**
	 * @Title exportPageListAll
	 * @Description 导出列表接口
	 * @date 2025��4��29�� ����9:43:03
	 * <AUTHOR>
	 */
	@ApiOperation(value = "导出列表", notes = "导出列表")
	@GetMapping("/api/medRiskPatientOperation/exportPageListAll")
	public void exportPageListAll(HttpServletRequest request, HttpServletResponse response, Page page, RiskPatientOperation record) {
		
		if(!StringUtils.isEmpty(record.getDeptId())) {
			//List<String> deptList = patientInfoMapper.getHisDeptIds(Arrays.asList(record.getDeptId().split(",")));//查询对应的HIS科室id
			List<String> deptList = patientInfoMapper.getHisDeptIdsByksys(Arrays.asList(record.getDeptId().split(",")), "HIS");//查询对应的HIS科室id
			record.setDeptList(deptList);
		}
		
		DataSet<RiskPatientOperation> records = riskPatientOperationService.selectPageList(page, record);
		List<RiskPatientOperation>   riskPatientOperationList=  records.getRows();
		if (CollectionUtils.isNotEmpty(riskPatientOperationList)) {
			for(RiskPatientOperation  riskPatientOperation : riskPatientOperationList) {
				if(!StringUtils.isEmpty(riskPatientOperation.getNationalLevel()) && "1".equals(riskPatientOperation.getNationalLevel())) {
					riskPatientOperation.setNationalLevel("四级");
				}else {
					riskPatientOperation.setNationalLevel("非四级");
				}
				if("1".equals(riskPatientOperation.getStatus())) {
					riskPatientOperation.setStatus("按时完成");
				}else if("2".equals(riskPatientOperation.getStatus())) {
					riskPatientOperation.setStatus("术后完成");
				}else {
					riskPatientOperation.setStatus("未完成");
				}
			}
		}
	// 导出文件名称
	String name = "四级手术术前多学科讨论.xls";
	
	// 模板位置
	String templateUrl = "template/riskPatientOperation.xls";
	// 导出数据列表
	try {
			if (CollectionUtils.isNotEmpty(riskPatientOperationList)) {
				ExportUtil.export(request, response, riskPatientOperationList, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
