<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.shift.dao.MedShiftRecordMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.shift.model.MedShiftRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="handover_doctor_id" jdbcType="VARCHAR" property="handoverDoctorId" />
    <result column="handover_doctor_name" jdbcType="VARCHAR" property="handoverDoctorName" />
    <result column="handover_date" jdbcType="TIMESTAMP" property="handoverDate" />
    <result column="takeover_doctor_id" jdbcType="VARCHAR" property="takeoverDoctorId" />
    <result column="takeover_doctor_name" jdbcType="VARCHAR" property="takeoverDoctorName" />
    <result column="takeover_date" jdbcType="TIMESTAMP" property="takeoverDate" />
    <result column="schedule_start_date" jdbcType="TIMESTAMP" property="scheduleStartDate" />
    <result column="schedule_end_date" jdbcType="TIMESTAMP" property="scheduleEndDate" />
    <result column="original_num" jdbcType="INTEGER" property="originalNum" />
    <result column="now_num" jdbcType="INTEGER" property="nowNum" />
    <result column="handover_num" jdbcType="INTEGER" property="handoverNum" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <select id="selectOriginalNum" parameterType="Map" resultType="cn.trasen.hrms.med.shift.model.MedShiftRecord">
  		SELECT * FROM med_shift_record
		WHERE is_deleted = 'N' AND takeover_doctor_id = #{takeoverDoctorId} AND status = '1'
		ORDER BY schedule_end_date DESC 
		LIMIT 1
  </select>
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.shift.model.MedShiftPatient" parameterType="cn.trasen.hrms.med.shift.model.MedShiftRecord">
  		SELECT 
  			t1.*,
  			t2.handover_doctor_name,
  			t2.handover_org_name,
  			t2.schedule_start_date,
  			t2.schedule_end_date,
  			t3.hosp_code,
  			t2.status as recordStatus,
  			t2.compensate_status
  		FROM med_shift_patient t1
		LEFT JOIN med_shift_record t2 ON t1.record_id  = t2.id
		left join cust_emp_base t3 on t3.employee_no = t2.handover_doctor_id
		WHERE t1.is_deleted = 'N' and t2.is_deleted = 'N' 
		<if test="id == null or id == ''">
		  	and t2.handover_doctor_id = #{handoverDoctorId}
		  	AND t2.status = '0'
	 	 </if>
		 <if test="id != null and id != ''">
		  	and t1.record_id = #{id}
	 	 </if>
		 <if test="searchKey != null and searchKey != ''">
			and (
					t1.patn_no like concat('%',#{searchKey},'%')  
					or t1.patn_name like concat('%',#{searchKey},'%')
					or t1.bed_no like concat('%',#{searchKey},'%')
				) 
		 </if>
		 <if test="patientType != null and patientType != '' and patientType == '1'.toString()">
		  	and t1.is_new_patient = '1'
	 	 </if>
	 	 <if test="patientType != null and patientType != '' and patientType == '2'.toString()">
	  		and t1.is_in_transfer = '1'
	 	 </if>
	 	 <if test="patientType != null and patientType != '' and patientType == '3'.toString()">
	  		and t1.is_out_transfer = '1'
	 	 </if>
	 	 <if test="patientType != null and patientType !='' and patientType == '4'.toString()">
	  		and t1.is_bw = '1'
	 	 </if>
	 	 <if test="patientType !=null and patientType != '' and patientType == '5'.toString()">
	  		and t1.is_bz = '1'
	 	 </if>
	 	 <if test="patientType !=null and patientType !='' and patientType == '6'.toString()">
	  		and t1.is_operation = '1'
	 	 </if>
	 	 <if test="patientType != null and patientType != '' and patientType == '7'.toString()">
	  		and t1.is_birth = '1'
	 	 </if>
	 	 <if test="patientType != null and patientType != '' and patientType == '8'.toString()">
	  		and t1.is_death = '1'
	 	 </if>
	 	  <if test="patientType != null and patientType != '' and patientType == '9'.toString()">
	  		and t1.is_special = '1'
	 	 </if>
  </select>
  
  <select id="getMedShiftRecordList" resultType="cn.trasen.hrms.med.shift.model.MedShiftRecord" parameterType="cn.trasen.hrms.med.shift.model.MedShiftRecord">
  			SELECT 
			    t1.*,
			    t3.hosp_code,
			    COUNT(t2.id) AS patientNums
			FROM med_shift_record t1
			LEFT JOIN med_shift_patient t2 ON t1.id = t2.record_id AND t2.is_deleted = 'N'
			left join cust_emp_base t3 on t3.employee_no = t1.handover_doctor_id
			WHERE 
			    t1.is_deleted = 'N' 
			 <if test="index != null and index != '' and index == '1'.toString()">
			 	 AND t1.status IN ('1', '2')
			   	 AND handover_doctor_id = #{handoverDoctorId}
			 </if>
			 <if test="index != null and index != '' and index == '2'.toString()">
			 	 AND t1.status = '1'
			   	 AND takeover_doctor_id = #{takeoverDoctorId}
			 </if>
			 <if test="index != null and index != '' and index == '3'.toString()">
			 	 AND t1.status = '2'
			   	 AND takeover_doctor_id = #{takeoverDoctorId}
			 </if>
			 <if test="handoverDateStart != null and handoverDateStart != '' and handoverDateEnd != null and handoverDateEnd != ''">
			 	and handover_date between concat(#{handoverDateStart},' 00:00') and concat(#{handoverDateEnd}, ' 23:59')
			 </if>
			 <if test="handoverDoctorName != null and handoverDoctorName != '' ">
			 	and handover_doctor_name like concat('%',#{handoverDoctorName},'%')
			 </if>
			 <if test="takeoverDoctorName != null and takeoverDoctorName != '' ">
			 	and takeover_doctor_name like concat('%',#{takeoverDoctorName},'%')
			 </if>
			 <if test="takeoverDateStart != null and takeoverDateStart != '' and takeoverDateEnd != null and takeoverDateEnd != ''">
			 	and takeover_date between concat(#{takeoverDateStart},' 00:00') and concat(#{takeoverDateEnd}, ' 23:59')
			 </if>
			 <if test="scheduleDateStart != null and scheduleDateStart != '' and scheduleDateEnd != null and scheduleDateEnd != ''">
			 	and schedule_start_date &lt;= concat(#{scheduleDateEnd},' 23:59') and schedule_end_date >= concat(#{scheduleDateStart}, ' 00:00')
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '1'.toString()">
			 	and t1.status = '0' and compensate_status = '0'
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '2'.toString()">
			 	and t1.status = '0' and compensate_status = '1'
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '3'.toString()">
			 	and t1.status = '1' and compensate_status = '0'
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '4'.toString()">
			 	and t1.status = '1' and compensate_status = '1'
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '5'.toString()">
			 	and t1.status = '2' and compensate_status = '0'
			 </if>
			 <if test="searchStatus != null and searchStatus != '' and searchStatus == '6'.toString()">
			 	and t1.status = '2' and compensate_status = '1'
			 </if>
			 <if test="deptIdList != null and deptIdList.size() > 0">
				AND t1.handover_org in
				<foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
					#{item}
			 	</foreach>
			 </if>
			 <if test="orgIdList != null and orgIdList.size() > 0">
	        	 and t1.handover_org in
		        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	         </if>
			GROUP BY t1.id
  </select>
  
  
  <select id="getMedShiftRecordDetails" resultType="cn.trasen.hrms.med.shift.model.MedShiftData" parameterType="String">
  			SELECT
				t1.*,
				t2.in_diagnosis,
				t2.diagnosi,
				t2.transfer_in_date,
				t2.transfer_remark,
				t2.diagnosi_plan,
				t2.crisis_value,
				t2.out_remark,
				t2.out_date,
				t2.operation_name,
				t2.temperature,
				t2.pulse,
				t2.respiratory_rate,
				t2.blood_pressure,
				t2.awareness,
				t2.bos,
				t2.dgw,
				t2.rescue_process,
				t2.death_remrk,
				t2.death_date,
				t2.special_remark_xr,
				t2.special_remark_wz,
				t2.special_remark_ss,
				t2.special_remark_fm,
				t2.special_remark_sw,
				t2.special_remark_ts,
				t2.special_remark_zc 
			FROM
				med_shift_patient t1
			LEFT JOIN med_shift_indices t2 ON t1.id = t2.patient_id
			WHERE
				t1.record_id = #{recordId}
  </select>
  
  <select id="selectOutPatient" resultType="Long" parameterType="Map">
  		SELECT count(1) FROM med_patient_info
		WHERE is_deleted = 'N' AND cancel_bit = '0' AND out_mode IN ('1','2','3','5') 
		AND out_date BETWEEN #{startDate} and #{endDate}
		<if test="hisOrgIds != null and hisOrgIds.size() > 0">
			AND dept_id  in
			<foreach collection="hisOrgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
  </select>
  
  <select id="getHandoverDoctorArea" parameterType="String" resultType="String">
  		SELECT hosp_code FROM cust_emp_base
		WHERE employee_no = #{handoverDoctorId}
  </select>
</mapper>