package cn.trasen.hrms.med.qua.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.qua.model.QuaAuthAuditDetl;

/**
 * @ClassName MedQuaAuthAuditDetlService
 * @Description TODO
 * @date 2024��12��13�� ����9:26:21
 * <AUTHOR>
 * @version 1.0
 */
public interface QuaAuthAuditDetlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��12��13�� ����9:26:21
	 * <AUTHOR>
	 */
	Integer save(QuaAuthAuditDetl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��12��13�� ����9:26:21
	 * <AUTHOR>
	 */
	Integer update(QuaAuthAuditDetl record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��12��13�� ����9:26:21
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedQuaAuthAuditDetl
	 * @date 2024��12��13�� ����9:26:21
	 * <AUTHOR>
	 */
	QuaAuthAuditDetl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedQuaAuthAuditDetl>
	 * @date 2024��12��13�� ����9:26:21
	 * <AUTHOR>
	 */
	DataSet<QuaAuthAuditDetl> getDataSetList(Page page, QuaAuthAuditDetl record);
	
	QuaAuthAuditDetl getByItemIdAndSeqno(String itemDetlId,String auditSeqNo);

	void updateByPrimaryKey(QuaAuthAuditDetl auditDetl);

	List<QuaAuthAuditDetl> getByItemIdsAndSeqno(List<String> itemIds, String auditStatus);

	void deleteByItemId(String itemDetlId,String auditStatus);
}
