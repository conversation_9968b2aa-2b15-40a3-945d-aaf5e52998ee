package cn.trasen.hrms.med.workplan.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.med.workplan.dao.MedWorkplanRecordMapper;
import cn.trasen.hrms.med.workplan.model.MedWorkplanPlayer;
import cn.trasen.hrms.med.workplan.model.MedWorkplanRecord;
import cn.trasen.hrms.med.workplan.service.MedWorkplanPlayerService;
import cn.trasen.hrms.med.workplan.service.MedWorkplanRecordService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedWorkplanRecordServiceImpl
 * @Description TODO
 * @date 2024��11��12�� ����11:52:20
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedWorkplanRecordServiceImpl implements MedWorkplanRecordService {

	@Autowired
	private MedWorkplanRecordMapper mapper;
	
	@Autowired
	private MedWorkplanPlayerService medWorkplanPlayerService;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedWorkplanRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		record.setStatus("0");
		
		if(CollectionUtil.isNotEmpty(record.getPlayerList())) {
			for (MedWorkplanPlayer medWorkplanPlayer : record.getPlayerList()) {
				medWorkplanPlayer.setWorkplanId(record.getId());
				medWorkplanPlayerService.save(medWorkplanPlayer);
			}
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedWorkplanRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		medWorkplanPlayerService.deleteByWorkPlanId(record.getId());
		
		if(CollectionUtil.isNotEmpty(record.getPlayerList())) {
			
			for (MedWorkplanPlayer medWorkplanPlayer : record.getPlayerList()) {
				medWorkplanPlayer.setWorkplanId(record.getId());
				medWorkplanPlayerService.save(medWorkplanPlayer);
			}
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void examine(List<MedWorkplanRecord> record) {
		
		for (MedWorkplanRecord medWorkplanRecord : record) {
			medWorkplanRecord.setUpdateDate(new Date());
			medWorkplanRecord.setExamineDate(new Date());
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				medWorkplanRecord.setUpdateUser(user.getUsercode());
				medWorkplanRecord.setUpdateUserName(user.getUsername());
				medWorkplanRecord.setExamineUser(user.getUsercode());
				medWorkplanRecord.setExamineUserName(user.getUsername());
			}
			
			mapper.updateByPrimaryKeySelective(medWorkplanRecord);
		}
		
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedWorkplanRecord record = new MedWorkplanRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedWorkplanRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedWorkplanRecord medWorkplanRecord = mapper.selectByPrimaryKey(id);
		
		List<MedWorkplanPlayer> playerList = medWorkplanPlayerService.selectByWorkplanId(id);
		medWorkplanRecord.setPlayerList(playerList);
		
		return medWorkplanRecord;
	}

	@Override
	public DataSet<MedWorkplanRecord> getDataSetList(Page page, MedWorkplanRecord record) {
		
		
		PlatformResult<List<String>> hrmsOrganizationAndNextList = hrmsOrganizationFeignService.getHrmsOrganizationAndNextList(record.getWorkplanOrg());
		if(hrmsOrganizationAndNextList.isSuccess()){
			record.setOrgIdList(hrmsOrganizationAndNextList.getObject());
		}
		
		List<MedWorkplanRecord> records = mapper.getDataSetList(record, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
