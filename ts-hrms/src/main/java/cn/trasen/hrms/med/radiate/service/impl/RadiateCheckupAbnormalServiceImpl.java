package cn.trasen.hrms.med.radiate.service.impl;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.radiate.dao.RadiateCheckupAbnormalMapper;
import cn.trasen.hrms.med.radiate.model.RadiateCheckupAbnormal;
import cn.trasen.hrms.med.radiate.service.RadiateCheckupAbnormalService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedRadiateCheckupAbnormalServiceImpl
 * @Description TODO
 * @date 2025��2��12�� ����9:50:28
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RadiateCheckupAbnormalServiceImpl implements RadiateCheckupAbnormalService {

	@Autowired
	private RadiateCheckupAbnormalMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(RadiateCheckupAbnormal record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RadiateCheckupAbnormal record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RadiateCheckupAbnormal record = new RadiateCheckupAbnormal();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RadiateCheckupAbnormal selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RadiateCheckupAbnormal> getDataSetList(Page page, RadiateCheckupAbnormal record) {
		Example example = new Example(RadiateCheckupAbnormal.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(!StringUtils.isEmpty(record.getCheckupId())) {
			criteria.andEqualTo("checkupId", record.getCheckupId());
		}
		List<RadiateCheckupAbnormal> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<RadiateCheckupAbnormal> getListByCheckupId(String checkupId) {
		Assert.hasText(checkupId, "体检ID不能为空.");
		Example example = new Example(RadiateCheckupAbnormal.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("checkupId", checkupId);
		return mapper.selectByExample(example);
	}


	@Transactional(readOnly = false)
	@Override
	public Integer batchSave(List<RadiateCheckupAbnormal> records) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		//判断关联体检ID是否为空
		if(ObjectUtils.isEmpty(records.get(0).getCheckupId())){
			throw new BusinessException("体检ID不能为空");
		}
		
		updateRadiateCheckupAbnormal(records);
		return records.size();
	}
	
	private void updateRadiateCheckupAbnormal(List<RadiateCheckupAbnormal> records){
		//先查询该用户一共有几条数据
		List<RadiateCheckupAbnormal> oldRecords = getListByCheckupId(records.get(0).getCheckupId());
		//将更新的数据记录，用于后续排除删除
		Set<String> updateIds = new HashSet<>();
		//遍历新增的数据
		for (RadiateCheckupAbnormal record : records){
			//判断该用户是否已经存在该数据
			boolean flag = false;
			for (RadiateCheckupAbnormal oldRecord : oldRecords){
				if(record.getId().equals(oldRecord.getId())){
					flag = true;
					record.setId(oldRecord.getId());
					update(record);
					updateIds.add(record.getId());
				}
			}
			if(!flag){
				save(record);
			}
		}
		//删除多余的数据
		if(CollUtil.isNotEmpty(records) && records.size() != updateIds.size()){
			for(RadiateCheckupAbnormal oldRecord : oldRecords){
				if(!updateIds.contains(oldRecord.getId())){
					deleteById(oldRecord.getId());
				}
			}
		}
	}

	@Override
	public Integer saveOrUpdateList(List<RadiateCheckupAbnormal> records, boolean isAdd) {
		if(CollUtil.isEmpty(records)){
			return 0;
		}
		//新增
		if(isAdd){
			for (RadiateCheckupAbnormal record : records){
				save(record);
			}
		} else {
			updateRadiateCheckupAbnormal(records);
		}
		return records.size();
	}
}
