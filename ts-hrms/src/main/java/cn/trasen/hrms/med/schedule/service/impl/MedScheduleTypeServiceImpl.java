package cn.trasen.hrms.med.schedule.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.schedule.dao.MedScheduleTypeMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleType;
import cn.trasen.hrms.med.schedule.service.MedScheduleClassesService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.med.schedule.service.MedScheduleTypeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleTypeServiceImpl
 * @Description TODO
 * @date 2025��3��29�� ����2:57:10
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleTypeServiceImpl implements MedScheduleTypeService {

	@Autowired
	private MedScheduleTypeMapper mapper;
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;
	
	@Autowired
	private MedScheduleClassesService medScheduleClassesService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleType record) {
		//名称唯一性校验
		Example example = new Example(MedScheduleType.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("typeName", record.getTypeName());
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleType> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			throw new BusinessException("该类型名称已存在！");
		}
		
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		
		//排序设置为最大
		Example example1 = new Example(MedScheduleType.class);
		Example.Criteria criteria1 = example1.createCriteria();
		criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example1.orderBy("sort").desc();
		list = mapper.selectByExample(example1);
		if(CollUtil.isNotEmpty(list) && list.get(0).getSort() != null){
			Integer sort = list.get(0).getSort();
			if(sort != null){
				record.setSort(sort + 1);
			}
		} else {
			record.setSort(0);
		}
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleType record) {
		//名称唯一性校验
		Example example = new Example(MedScheduleType.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("typeName", record.getTypeName());
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andNotEqualTo("id", record.getId());
		List<MedScheduleType> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			throw new BusinessException("该类型名称已存在！");
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		
		Assert.hasText(id, "ID不能为空.");
		
		int countType = medScheduleClassesService.selectByTypeId(id);
		if(countType > 0) {
			Assert.isTrue(false, "此类别已被班次引用，不允许删除");
		}
		
		//需要判断是否有排班数据 有的话就不允许删除
		int count = medScheduleRecordService.selectCountByTypeId(id);
		
		if(count > 0) {
			Assert.isTrue(false, "已存在排班数据，不允许删除");
		}
		
		MedScheduleType record = new MedScheduleType();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
		
	}

	@Override
	public MedScheduleType selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleType> getDataSetList(Page page, MedScheduleType record) {
		Example example = new Example(MedScheduleType.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleType> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateSeq(List<MedScheduleType> records) {
		
		if(CollectionUtils.isNotEmpty(records)) {
			for (MedScheduleType medScheduleType : records) {
				mapper.updateByPrimaryKeySelective(medScheduleType);
			}
		}
	}

	@Override
	public List<MedScheduleType> getScheduleTypeList() {
		Example example = new Example(MedScheduleType.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		
		example.setOrderByClause("sort");
		
		return mapper.selectByExample(example);
	}
	
	
	
	
}
