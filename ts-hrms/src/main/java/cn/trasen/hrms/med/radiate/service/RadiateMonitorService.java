package cn.trasen.hrms.med.radiate.service;

import java.util.List;

import cn.trasen.hrms.med.radiate.model.RadiateMonitor;
import cn.trasen.hrms.med.radiate.model.RadiatePersonnelRegister;

/**
 * @ClassName RadiateMonitorService
 * @Description 
 * @date 2025-05-21 15:15:27
 * <AUTHOR>
 * @version 1.0
 */
public interface RadiateMonitorService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer save(RadiateMonitor record);

	/**
	 * @Title update
	 * @Description 更新
	 * @param record
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer update(RadiateMonitor record);

	/**
	 *
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title saveOrUpdateList
	 * @Description 批量信息
	 * @param records
	 * @param isAdd 新增标识
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer saveOrUpdateList(List<RadiateMonitor> records, boolean isAdd, RadiatePersonnelRegister personnel);

	/**
	 * @Title selectByEmployeeId
	 * @Description 根据员工ID查询
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	List<RadiateMonitor> selectByEmployeeId(String employeeId);
	List<RadiateMonitor> selectAll();
	
	/**
	 * @Title deleteByEmployeeId
	 * @Description 根据员工ID删除
	 * @param employeeId
	 * @return Integer
	 * @date 2025-05-21 15:15:27
	 * <AUTHOR>
	 */
	Integer deleteByEmployeeId(String employeeId);

}
