package cn.trasen.hrms.med.schedule.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.OrganizationListRes;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.oa.ScheduleFeignService;
import cn.trasen.hrms.med.schedule.dao.MedCustomStatisticsExportMapper;
import cn.trasen.hrms.med.schedule.dao.MedCustomStatisticsTitleMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleAuthorityMapper;
import cn.trasen.hrms.med.schedule.model.LeaveRecordVo;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsExport;
import cn.trasen.hrms.med.schedule.model.MedCustomStatisticsTitle;
import cn.trasen.hrms.med.schedule.model.MedRecordStatisticsVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsDetailVo;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsReq;
import cn.trasen.hrms.med.schedule.model.MedScheduleStatisticsTitleVo;
import cn.trasen.hrms.med.schedule.model.ScheduleDept;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.med.schedule.service.MedScheduleStatisticsService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleStatisticsServiceImpl
 * @Description 排班统计实现类
 * @date 2025-08-13 16:48:40
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleStatisticsServiceImpl implements MedScheduleStatisticsService {
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Autowired
	private MedCustomStatisticsTitleMapper medCustomStatisticsTitleMapper;
	
	@Autowired
	private MedScheduleAuthorityService medScheduleAuthorityService;
	
	@Autowired
	private MedScheduleRecordService medScheduleRecordService;

	@Autowired
	private MedScheduleAuthorityMapper medScheduleAuthorityMapper;
	
	@Autowired
	private ScheduleFeignService scheduleFeignService;
	
	@Autowired
	private MedCustomStatisticsExportMapper medCustomStatisticsExportMapper;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;

	/**
	 * 请假数据的类型，用于组装统计返回数据
	 */
	private static Map<String, BigDecimal> leaveItemCodeMap = new HashMap<>();
	
	/**
	 * 进修、规培、下乡等数据的类型，用于组装统计返回数据
	 */
	private static Map<String, BigDecimal> outItemCodeMap = new HashMap<>();
	
	/**
	 * 排班等数据的类型，用于组装统计返回数据
	 */
	private static Map<String, BigDecimal> scheduleItemCodeMap = new HashMap<>();

	@Override
	public List<MedScheduleStatisticsTitleVo> getStatisticsTitleList(MedScheduleStatisticsReq record) {
		//时间区间为空-则默认 开始时间上个月1号，结束时间 上个月最后一天
		if(ObjectUtils.isEmpty(record.getStartDate()) || ObjectUtils.isEmpty(record.getEndDate())){
			record.setStartDate(DateUtils.getlastMonthfirstDay(new Date()));//获取上个月第一天
			record.setEndDate(DateUtils.getlastMonthLastDay(new Date()));//获取上个月最后一天
		}
		String type = record.getType();
		List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
		if(type.equals("3")){//排班分类统计
//			titleList.addAll(MedScheduleStatisticsTitleVo.getCategoryStatisticsTitleList());
			
			//先判断是否自定义班次，否则取所有启用班次数据
			List<MedCustomStatisticsTitle> list = selectCustomStatisticsTitle();
			if(list.size() > 0){
				for(MedCustomStatisticsTitle title : list){
					titleList.add(new MedScheduleStatisticsTitleVo(title.getClassesName(), title.getClassesId(), "center", 120, null));
				}
			} else {
				/**
				 * 如果是admin，超级管理员，排班设置员查询时间范围内的有排班的数据班次
				 * 如果是排班管理员查询时间范围内，班次权限范围内以及有排班数据的班次
				 */
				ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
				scheduleEmployee.setStartDate(record.getStartDate());
				scheduleEmployee.setEndDate(record.getEndDate());
				if(!medScheduleAuthorityService.setQueryData(scheduleEmployee)){
					return titleList;
				}
				//排班数据
//				scheduleEmployee.setHolidayClasses("0");
				List<MedScheduleRecord> recordList = medScheduleRecordService.selectMedScheduleRecordList(scheduleEmployee);
				Set<String> classesIds = new HashSet<>();
				for(MedScheduleRecord scheduleRecord : recordList){
					if(!classesIds.contains(scheduleRecord.getClassesId())){
						titleList.add(new MedScheduleStatisticsTitleVo(scheduleRecord.getClassesName() + "(天)", scheduleRecord.getClassesId(), "center", 80, null));
						classesIds.add(scheduleRecord.getClassesId());
					}
				}
			}
		} else {//个人排班明细统计
			//获取请假类型数据字典-HOLIDAY_TYPE
			List<DictItemResp> holidayTypeList = dictItemFeignService.getDictItemByTypeCode("HOLIDAY_TYPE").getObject();
			for(DictItemResp holidayType : holidayTypeList){
				titleList.add(new MedScheduleStatisticsTitleVo(holidayType.getItemName() + "(天)", holidayType.getItemName() + "_leave", "center", 110, null));
			}
			
			//获取规培，下乡，进修等类型
			titleList.add(new MedScheduleStatisticsTitleVo("进修(天)", "进修_out", "center", 80, null));
			
			titleList.add(new MedScheduleStatisticsTitleVo("规培(天)", "规培_out", "center", 80, null));
			
			titleList.add(new MedScheduleStatisticsTitleVo("外出学习(天)", "外出学习_out", "center", 120, null));
			titleList.add(new MedScheduleStatisticsTitleVo("外出会议(天)", "外出会议_out", "center", 120, null));
			titleList.add(new MedScheduleStatisticsTitleVo("公务外出(天)", "公务外出_out", "center", 120, null));
			
			titleList.add(new MedScheduleStatisticsTitleVo("下乡(天)", "下乡_out", "center", 80, null));
			
			titleList.addAll(getTitelClassesIds(record.getStartDate(), record.getEndDate()));
			
		}
		return titleList;
	}
	
	/**
	 * 获取时间范围内的开启排班统计的数据
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	private List<MedScheduleStatisticsTitleVo> getTitelClassesIds(String startDate, String endDate){
		List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
		//获取有数据的班次且排班统计开启的班次
		ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
		scheduleEmployee.setStartDate(startDate);
		scheduleEmployee.setEndDate(endDate);
		if(!medScheduleAuthorityService.setQueryData(scheduleEmployee)){
			return titleList;
		}
		scheduleEmployee.setScheduleStatistics("1");
//		scheduleEmployee.setHolidayClasses("0");
		List<MedScheduleRecord> recordList = medScheduleRecordService.selectMedScheduleRecordList(scheduleEmployee);
		Set<String> classesIds = new HashSet<>();
		for(MedScheduleRecord scheduleRecord : recordList){
			if(!classesIds.contains(scheduleRecord.getClassesId())){
				titleList.add(new MedScheduleStatisticsTitleVo(scheduleRecord.getClassesName() + "(天)", scheduleRecord.getClassesId(), "center", 120, null));
				classesIds.add(scheduleRecord.getClassesId());
			}
		}
		return titleList;
	}

	@Override
	public List<MedScheduleStatisticsTitleVo> selectAaTypeList() {
		List<MedScheduleStatisticsTitleVo> titleList = new ArrayList<>();
		List<DictItemResp> holidayTypeList = dictItemFeignService.getDictItemByTypeCode("HOLIDAY_TYPE").getObject();
		for(DictItemResp holidayType : holidayTypeList){
			titleList.add(new MedScheduleStatisticsTitleVo(holidayType.getItemName(), holidayType.getItemName() + "_leave", "center", 110, null));
		}
		for(DictItemResp holidayType : holidayTypeList){
			titleList.add(new MedScheduleStatisticsTitleVo("销假-" + holidayType.getItemName(), holidayType.getItemName() + "_cancelLeave", "center", 80, null));
		}
		
		//获取规培，下乡，进修等类型
		titleList.add(new MedScheduleStatisticsTitleVo("进修", "进修_out", "center", 80, null));
		
		titleList.add(new MedScheduleStatisticsTitleVo("规培", "规培_out", "center", 80, null));
		
		titleList.add(new MedScheduleStatisticsTitleVo("外出学习", "外出学习_out", "center", 120, null));
		titleList.add(new MedScheduleStatisticsTitleVo("外出会议", "外出会议_out", "center", 120, null));
		titleList.add(new MedScheduleStatisticsTitleVo("公务外出", "公务外出_out", "center", 120, null));
		
		titleList.add(new MedScheduleStatisticsTitleVo("下乡", "下乡_out", "center", 80, null));
		return titleList;
	}
	
	@Transactional(readOnly = false)
	@Override
	public void saveCustomStatisticsTitle(List<MedCustomStatisticsTitle> records) {
		if(records.size() == 0){
			return;
		}
		//先全部删除，再根据传递的数据更新数据的删除标识
		Example example = new Example(MedCustomStatisticsTitle.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		MedCustomStatisticsTitle updateEntity = new MedCustomStatisticsTitle();
		updateEntity.setIsDeleted(Contants.IS_DELETED_TURE);
		medCustomStatisticsTitleMapper.updateByExampleSelective(updateEntity, example);
		
		for(MedCustomStatisticsTitle record : records){
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if(!ObjectUtils.isEmpty(record.getId())){
				record.setIsDeleted(Contants.IS_DELETED_FALSE);
				record.setUpdateDate(new Date());
				if (user != null) {
					record.setUpdateUser(user.getUsercode());
					record.setUpdateUserName(user.getUsername());
				}
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				medCustomStatisticsTitleMapper.updateByPrimaryKeySelective(record);
				continue;
			}
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted(Contants.IS_DELETED_FALSE);
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setSsoOrgName(user.getOrgName());
			medCustomStatisticsTitleMapper.insertSelective(record);
		}
	}

	@Override
	public List<MedCustomStatisticsTitle> selectCustomStatisticsTitle() {
		return medCustomStatisticsTitleMapper.selectCustomStatisticsTitle(UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	public Map<String, Object> statisticsTotalTableDataList(MedScheduleStatisticsReq record) {
		Map<String, Object>  resultMap = new HashMap<>();
		//获取全部科室的数据
		record.setType("2");
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		List<Map<String, Object>> mapDeptList = statisticsTableDataList(page, record);
		//出勤人数
		BigDecimal attendanceUserCount = BigDecimal.ZERO;
		//应出勤天数
		BigDecimal scheduledAttendance = BigDecimal.ZERO;
		//实际出勤(天)=已排班的班次（不算休息班次）
		BigDecimal actualAttendance = BigDecimal.ZERO;
		//平均出勤天数
		BigDecimal averageAttendance = BigDecimal.ZERO;
		if(mapDeptList.size() > 0){
			for(String key : leaveItemCodeMap.keySet()){
				BigDecimal keyValue = BigDecimal.ZERO;
				for(Map<String, Object> deptMap : mapDeptList){
					for(String deptKey : deptMap.keySet()){
						if(deptKey.equals(key) && deptMap.get(deptKey) != null){
							keyValue = keyValue.add(new BigDecimal((String) deptMap.get(deptKey)));
						}
					}
					
				}
				//如果是0，则不显示，否则保留一位小数
				if(keyValue.compareTo(BigDecimal.ZERO) == 0){
					resultMap.put(key, BigDecimal.ZERO);
				} else {
					resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
				}
//				resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
			}
			for(String key : outItemCodeMap.keySet()){
				BigDecimal keyValue = BigDecimal.ZERO;
				for(Map<String, Object> deptMap : mapDeptList){
					for(String deptKey : deptMap.keySet()){
						if(deptKey.equals(key) && deptMap.get(deptKey) != null){
							keyValue = keyValue.add(new BigDecimal((String) deptMap.get(deptKey)));
						}
					}
					
				}
				//如果是0，则不显示，否则保留一位小数
				if(keyValue.compareTo(BigDecimal.ZERO) == 0){
					resultMap.put(key, BigDecimal.ZERO);
				} else {
					resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
				}
//				resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
			}
			for(String key : scheduleItemCodeMap.keySet()){
				BigDecimal keyValue = BigDecimal.ZERO;
				for(Map<String, Object> deptMap : mapDeptList){
					for(String deptKey : deptMap.keySet()){
						if(deptKey.equals(key) && deptMap.get(deptKey) != null){
							keyValue = keyValue.add(new BigDecimal((String) deptMap.get(deptKey)));
						}
					}
					
				}
				//如果是0，则不显示，否则保留一位小数
				if(keyValue.compareTo(BigDecimal.ZERO) == 0){
					resultMap.put(key, BigDecimal.ZERO);
				} else {
					resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
				}
//				resultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
			}
			for(Map<String, Object> deptMap : mapDeptList){
				for(String deptKey : deptMap.keySet()){
					if(deptKey.equals("attendanceUserCount") && deptMap.get(deptKey) != null){
						attendanceUserCount = attendanceUserCount.add((BigDecimal) deptMap.get(deptKey));
					}
					if(deptKey.equals("scheduledAttendance") && deptMap.get(deptKey) != null){
						if (deptMap.get(deptKey) instanceof BigDecimal){
							scheduledAttendance = scheduledAttendance.add((BigDecimal) deptMap.get(deptKey));
						} else {
							scheduledAttendance = scheduledAttendance.add(new BigDecimal((String) deptMap.get(deptKey)));
						}
					}
					if(deptKey.equals("actualAttendance") && deptMap.get(deptKey) != null){
						if (deptMap.get(deptKey) instanceof BigDecimal){
							actualAttendance = actualAttendance.add((BigDecimal) deptMap.get(deptKey));
						} else {
							actualAttendance = actualAttendance.add(new BigDecimal((String) deptMap.get(deptKey)));
						}
					}
				}
			}
			resultMap.put("attendanceUserCount", attendanceUserCount);
			
			if(scheduledAttendance.compareTo(BigDecimal.ZERO) != 0){
				resultMap.put("scheduledAttendance", scheduledAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
			} else {
				resultMap.put("scheduledAttendance", BigDecimal.ZERO);
			}

			if(actualAttendance.compareTo(BigDecimal.ZERO) != 0){
				resultMap.put("actualAttendance", actualAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
			} else {
				resultMap.put("actualAttendance", BigDecimal.ZERO);
			}
			//平均实际出勤=实际出勤/出勤人数
			if(attendanceUserCount.compareTo(BigDecimal.ZERO) != 0){
				averageAttendance = actualAttendance.divide(attendanceUserCount, 2, RoundingMode.HALF_UP);
				resultMap.put("averageAttendance", averageAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
			} else {
				resultMap.put("averageAttendance", BigDecimal.ZERO);
			}
		}
		resultMap.put("deptName", "合计");
		return resultMap;
	}

	@Override
	public List<Map<String, Object>> statisticsTableDataList(Page page, MedScheduleStatisticsReq record) {
		List<Map<String, Object>> resultData = new ArrayList<>();//最终返回的结果
		//时间区间为空-则默认 开始时间上个月1号，结束时间 上个月最后一天
		if(ObjectUtils.isEmpty(record.getStartDate()) || ObjectUtils.isEmpty(record.getEndDate())){
			record.setStartDate(DateUtils.getlastMonthfirstDay(new Date()));//获取上个月第一天
			record.setEndDate(DateUtils.getlastMonthLastDay(new Date()));//获取上个月最后一天
		}
		//获取当前页的人员
		String startDate = record.getStartDate();
		String endDate = record.getEndDate();
		ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
		scheduleEmployee.setStartDate(startDate);
		scheduleEmployee.setEndDate(endDate);
		scheduleEmployee.setTechnical(record.getTechnical());
		scheduleEmployee.setHospCode(record.getHospCode());
		scheduleEmployee.setKeywords(record.getKeywords());
		scheduleEmployee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		scheduleEmployee.setEmployeeStatus(record.getEmployeeStatus());
		if(!ObjectUtils.isEmpty(record.getDeptIds())){
			scheduleEmployee.setOrgIdList(Arrays.asList(record.getDeptIds().split(",")));
		}
		if(!medScheduleAuthorityService.setQueryData(scheduleEmployee)){
			return ListUtil.empty();
		}
		//统计类型：1-个人，2-科室，3-班次分类统计，默认个人
		String type = record.getType();
		if(type.equals("1")){
			List<ScheduleEmployee> employeeRecords = medScheduleAuthorityMapper.selectEmployeeByAuthorit(scheduleEmployee, page);
			if(employeeRecords.size() == 0){
				return ListUtil.empty();
			} 
			
			List<String> employeeIdList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeId).collect(Collectors.toList());
			List<String> employeeNoList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeNo).collect(Collectors.toList());
			//请假类型统计天数,key为工号
			Map<String, Map<String, Object>> leaveRecordMap = getLeaveRecordStatistics(employeeNoList, startDate, endDate, "employee");
			
			//进修/规培/学习/下乡等统计天数,key为工号
			Map<String, Map<String, Object>> outRecordMap = getOutRecordStatistics(employeeIdList, startDate, endDate, "employee");
			
			//按排班统计统计天数,key为工号
			Map<String, Map<String, Object>> scheduleRecordMap = getScheduleRecordStatistics(employeeIdList, startDate, endDate, "employee");
			
			//获取班次数据-用于统计应出勤日期
			Page newPage = new Page();
			newPage.setPageSize(Integer.MAX_VALUE);
			//排除休息班次
			scheduleEmployee.setHolidayClasses("0");
			scheduleEmployee.setShowActualAttendance(true);//返回实际出勤天数
			DataSet<ScheduleEmployee> scheduleDataSet = medScheduleAuthorityService.getScheduleRecordList(newPage, scheduleEmployee, employeeRecords);
			
			//将各部分的数据填充到result中
			renderPersonResultData(employeeRecords, resultData, leaveRecordMap, outRecordMap, scheduleRecordMap, startDate, endDate, scheduleDataSet.getRows(), "employee");
			
		} else if(type.equals("2")){
			//先获取所有权限范围的科室，再根据科室查询科室下的人，再一次根据人员统计再叠加统计到科室数据
			List<ScheduleDept> deptRecords = medScheduleAuthorityMapper.selectDeptByAuthorit(scheduleEmployee, page);
			if(deptRecords.size() == 0){
				return ListUtil.empty();
			}
			//计算科室人员，将scheduleEmployee重新赋值
			scheduleEmployee = new ScheduleEmployee();
			scheduleEmployee.setStartDate(startDate);
			scheduleEmployee.setEndDate(endDate);
			scheduleEmployee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			scheduleEmployee.setEmployeeStatus(record.getEmployeeStatus());
			scheduleEmployee.setOrgIdList(deptRecords.stream().map(ScheduleDept::getDeptId).collect(Collectors.toList()));
			
			//查询科室的统计人员列表-排除没有排班的人员
			List<ScheduleEmployee> employeeRecords = medScheduleAuthorityMapper.selectEmployeeByOrgId(scheduleEmployee);
			List<String> employeeIdList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeId).collect(Collectors.toList());
			List<String> employeeNoList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeNo).collect(Collectors.toList());
			
			//请假类型统计天数,key为 工号&科室ID
			Map<String, Map<String, Object>> leaveRecordMap = getLeaveRecordStatistics(employeeNoList, startDate, endDate, "dept");
//			
			//进修/规培/学习/下乡等统计天数,key为 工号&科室ID
			Map<String, Map<String, Object>> outRecordMap = getOutRecordStatistics(employeeIdList, startDate, endDate, "dept");
			
			//按班次统计天数,key为 工号&科室ID
			Map<String, Map<String, Object>> scheduleRecordMap = getScheduleRecordStatistics(employeeIdList, startDate, endDate, "dept");
			
			//获取班次数据-用于统计应出勤日期
			Page newPage = new Page();
			newPage.setPageSize(Integer.MAX_VALUE);
			//获取班次数据-用于统计应出勤日期，排除休息班次
			scheduleEmployee.setHolidayClasses("0");
			scheduleEmployee.setShowActualAttendance(true);//返回实际出勤天数
			DataSet<ScheduleEmployee> scheduleDataSet = medScheduleAuthorityService.getScheduleRecordList(newPage, scheduleEmployee, employeeRecords);
			
			//将各部分的数据填充到result中
			renderDeptResultData(deptRecords, resultData, leaveRecordMap, outRecordMap, scheduleRecordMap, startDate, endDate, scheduleDataSet.getRows());
			
		} else if(type.equals("3")){
			//获取当前页的人员
			List<ScheduleEmployee> employeeRecords = medScheduleAuthorityMapper.selectEmployeeByAuthorit(scheduleEmployee, page);
			if(employeeRecords.size() == 0){
				return ListUtil.empty();
			} 
			List<String> employeeIdList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeId).collect(Collectors.toList());
			List<String> employeeNoList = employeeRecords.stream().map(ScheduleEmployee::getEmployeeNo).collect(Collectors.toList());
			//请假类型统计天数,key为工号
			Map<String, Map<String, Object>> leaveRecordMap = getLeaveRecordStatistics(employeeNoList, startDate, endDate, "employee");
			
			//进修/规培/学习/下乡等统计天数,key为工号
			Map<String, Map<String, Object>> outRecordMap = getOutRecordStatistics(employeeIdList, startDate, endDate, "employee");
			
			//按排班统计统计天数,key为工号
			Map<String, Map<String, Object>> scheduleRecordMap = getScheduleRecordStatistics(employeeIdList, startDate, endDate, "category");
			
			Page newPage = new Page();
			newPage.setPageSize(Integer.MAX_VALUE);
			//获取班次数据-用于统计应出勤日期，排除休息班次
			scheduleEmployee.setHolidayClasses("0");
			scheduleEmployee.setShowActualAttendance(true);//返回实际出勤天数
			DataSet<ScheduleEmployee> scheduleDataSet = medScheduleAuthorityService.getScheduleRecordList(newPage, scheduleEmployee, employeeRecords);
			
			//将各部分的数据填充到result中
			renderPersonResultData(employeeRecords, resultData, leaveRecordMap, outRecordMap, scheduleRecordMap, startDate, endDate, scheduleDataSet.getRows(), "category");
			
		}
		return resultData;
	}
	
	/**
	 * 计算应出勤天数=每月天数-周末-法定节假日
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	private BigDecimal getScheduledAttendance(String startDate, String endDate){
		List<String> dateList = DateUtils.getDays(startDate, endDate);//相隔日期列表
		List<String> weekendDates = DateUtils.getWeekendDates(startDate, endDate);//获取周末日期列表
		PlatformResult<Map<String, List<String>>> resp = scheduleFeignService.getHolidayDateList(startDate, endDate, UserInfoHolder.getCurrentUserCorpCode());
		BigDecimal scheduledAttendance = BigDecimal.ZERO;//应出勤天数
		if(resp.isSuccess()){
			//holiday -节假日， compensatoryDay - 调休日
			Map<String, List<String>> holidayDateMap = resp.getObject();
			List<String> holidayList = holidayDateMap.get("holiday");
			List<String> compensatoryDayList = holidayDateMap.get("compensatoryDay");
			for(String date : dateList){
				//当前天是节假日,或者是周末且非调休日则计算应出勤日
				if(holidayList.contains(date) || (weekendDates.contains(date) && !compensatoryDayList.contains(date))){
					
				} else {
					scheduledAttendance = scheduledAttendance.add(new BigDecimal(1));
				}
			}
		} else {
			for(String date : dateList){
				//当前非周末
				if(!weekendDates.contains(date)){
					scheduledAttendance = scheduledAttendance.add(new BigDecimal(1));
				}
			}
		}
		return scheduledAttendance;
	}
	
	/**
	 * 渲染个人统计数据
	 * @param employeeRecords
	 * @param resultData
	 * @param leaveRecordMap
	 * @param outRecordMap
	 * @param scheduleRecordMap
	 * @param startDate
	 * @param endDate
	 * @param records
	 * @param type 个人-employee，分类统计-category
	 */
	private void renderPersonResultData(List<ScheduleEmployee> employeeRecords, List<Map<String, Object>> resultData,
			Map<String, Map<String, Object>> leaveRecordMap, Map<String, Map<String, Object>> outRecordMap, 
			Map<String, Map<String, Object>> scheduleRecordMap, String startDate, String endDate,
			List<ScheduleEmployee> records, String type){
		Map<String, ScheduleEmployee> empScheduleMap = new HashMap<>();
		if (records != null && records.size() > 0) {
			empScheduleMap = records.stream().collect(Collectors.toMap(ScheduleEmployee::getEmployeeNo, a -> a, (k1, k2) -> k1));
        }
		
		//应出勤天数
		BigDecimal scheduledAttendance = getScheduledAttendance(startDate, endDate);
		
		//编制类型
		List<DictItemResp> establishmentTypeList =  dictItemFeignService.getDictItemByTypeCode("establishment_type").getObject();
		List<DictItemResp> orgAttributesList = dictItemFeignService.getDictItemByTypeCode("ORG_ATTRIBUTES").getObject();
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		int index = 0;
		for(ScheduleEmployee emp : employeeRecords){
			String employeeCode = emp.getEmployeeNo();
			index++;
			Map<String, Object> empMap = new HashMap<>();
			empMap.put("index", index);//序号
			//归属院区
			DictItemResp area = areas.stream().filter(j -> StrUtil.equals(emp.getHospCode(), j.getItemCode())).findFirst().orElse(null);
			empMap.put("hospName", null == area ? emp.getHospCode() : area.getItemName());
			empMap.put("deptName", emp.getOrgName());
			empMap.put("employeeName", emp.getEmployeeName());
			empMap.put("employeeCode", emp.getEmployeeNo());
			empMap.put("employeeId", emp.getEmployeeId());
			//人员类型
			DictItemResp orgAttributes = orgAttributesList.stream().filter(j -> StrUtil.equals(emp.getOrgAttributes(), j.getItemCode())).findFirst().orElse(null);
			empMap.put("orgAttributes", null == orgAttributes ? emp.getOrgAttributes() : orgAttributes.getItemName());
			
			//应出勤(天)=每月天数-周末-法定节假日
			if(scheduledAttendance.compareTo(BigDecimal.ZERO) == 0){
				empMap.put("scheduledAttendance", 0);
			} else {
				empMap.put("scheduledAttendance", scheduledAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
			}
			//实际出勤(天)=已排班的班次（不算休息班次）
			BigDecimal actualAttendance = empScheduleMap.get(employeeCode).getActualAttendance();
			if(actualAttendance.compareTo(BigDecimal.ZERO) == 0){
				empMap.put("actualAttendance", 0);
			} else {
				empMap.put("actualAttendance", actualAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
			}
			
			if(type.equals("employee")){
				empMap.put("positionName", emp.getPositionName());
				//编制类型
				DictItemResp establishmentType = establishmentTypeList.stream().filter(j -> StrUtil.equals(emp.getEstablishmentType(), j.getItemNameValue())).findFirst().orElse(null);
				empMap.put("establishmentType", null == establishmentType ? emp.getEstablishmentType() : establishmentType.getItemName());
				
				empMap.put("technical", emp.getTechnical());
				empMap.put("phoneNumber", emp.getPhoneNumber());
				
				//填充请假统计数据
				List<String> remarks = new ArrayList<>();//请假类型集合，便于非全勤备注去重 
				if(leaveRecordMap.containsKey(employeeCode)){
					Map<String, Object> leaveEmpRecordMap = leaveRecordMap.get(employeeCode);
					//请假天数
					for(String leaveType : leaveEmpRecordMap.keySet()){
						if(!leaveType.equals("employeeCode") && !leaveType.equals("orgId")){
							BigDecimal holidayTypeLeaveValue = (BigDecimal) leaveEmpRecordMap.get(leaveType);
							if(holidayTypeLeaveValue.compareTo(BigDecimal.ZERO) > 0){
								if(!remarks.contains(leaveType)){
									remarks.add(leaveType);
								}
							}
							//如果是0，则不显示，否则保留一位小数
							if(holidayTypeLeaveValue == null || holidayTypeLeaveValue.compareTo(BigDecimal.ZERO) == 0){
								empMap.put(leaveType + "_leave", null);
							} else {
								empMap.put(leaveType + "_leave", holidayTypeLeaveValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
							}
						}
					}
				} else {
					empMap.putAll(leaveItemCodeMap);
				}
				//填充进修、规培/会议等统计数据
				if(outRecordMap.containsKey(employeeCode)){
					Map<String, Object> leaveEmpRecordMap = outRecordMap.get(employeeCode);
					for(String outType : leaveEmpRecordMap.keySet()){
						if(!outType.equals("employeeCode") && !outType.equals("orgId")){
							BigDecimal outTypeLeaveValue = (BigDecimal) leaveEmpRecordMap.get(outType);
							if(outTypeLeaveValue.compareTo(BigDecimal.ZERO) > 0){
								if(!remarks.contains(outType)){
									remarks.add(outType);
								}
							}
							//如果是0，则不显示，否则保留一位小数
							if(outTypeLeaveValue == null || outTypeLeaveValue.compareTo(BigDecimal.ZERO) == 0){
								empMap.put(outType + "_out", null);
							} else {
								empMap.put(outType + "_out", outTypeLeaveValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
							}
						}
					}
				} else {
					empMap.putAll(outItemCodeMap);
				}
				
				/**
				 * 出勤情况
				 * 全勤（实际出勤天数大于等于应出勤天数）
				 * 非全勤（有请假类型，备注请假,进修、规培、会议，下乡类型）
				 */
				if(actualAttendance.compareTo(scheduledAttendance) >= 0){
					empMap.put("attendance", "全勤");
				} else {
					String remark = StringUtils.join(remarks, ",");
					if(ObjectUtils.isEmpty(remark)){
						empMap.put("attendance", "非全勤");
					} else {
						remark = remark.substring(1);
						empMap.put("attendance", "非全勤(" + remark + ")");
					}
				}
			}
			
			//填充排班统计数据
			if(scheduleRecordMap.containsKey(employeeCode)){
				scheduleRecordMap.get(employeeCode).remove("orgId");
				Map<String, Object> scheduleEmpRecordMap = scheduleRecordMap.get(employeeCode);
				for(String scheduleType : scheduleEmpRecordMap.keySet()){
					if(!scheduleType.equals("employeeCode") && !scheduleType.equals("orgId")){
						BigDecimal scheduleTypeValue = (BigDecimal) scheduleEmpRecordMap.get(scheduleType);
						//如果是0，则不显示，否则保留一位小数
						if(scheduleTypeValue == null || scheduleTypeValue.compareTo(BigDecimal.ZERO) == 0){
							empMap.put(scheduleType, null);
						} else {
							empMap.put(scheduleType, scheduleTypeValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
						}
					}
				}
//				empMap.putAll(scheduleRecordMap.get(employeeCode));
			} else {
				empMap.putAll(scheduleItemCodeMap);
			}
			resultData.add(empMap);
		}
	}
	
	/**
	 * 渲染科室统计数据
	 * @param deptRecords
	 * @param resultData
	 * @param leaveRecordMap
	 * @param outRecordMap
	 * @param scheduleRecordMap
	 * @param startDate
	 * @param endDate
	 * @param records
	 */
	private void renderDeptResultData(List<ScheduleDept> deptRecords, List<Map<String, Object>> resultData,
			Map<String, Map<String, Object>> leaveRecordMap, Map<String, Map<String, Object>> outRecordMap, 
			Map<String, Map<String, Object>> scheduleRecordMap, String startDate, String endDate,
			List<ScheduleEmployee> records){
		//获取请假列表-leaveRecordMap第一条的 Map<String, Object> 对应的 keys
		Set<String> leaveKeySet = new HashSet<>();
		if(leaveRecordMap.size() > 0){
			for(String key : leaveRecordMap.keySet()){
				leaveKeySet = leaveRecordMap.get(key).keySet();
				break;
			}
		}
		//获取进修列表-leaveRecordMap第一条的 Map<String, Object> 对应的 keys
		Set<String> outKeySet = new HashSet<>();
		if(outRecordMap.size() > 0){
			for(String key : outRecordMap.keySet()){
				outKeySet = outRecordMap.get(key).keySet();
				break;
			}
		}
		//获取统计的班次列表-scheduleRecordMap第一条的 Map<String, Object> 对应的 keys
		Set<String> scheduleKeySet = new HashSet<>();
		if(scheduleRecordMap.size() > 0){
			for(String key : scheduleRecordMap.keySet()){
				scheduleKeySet = scheduleRecordMap.get(key).keySet();
				break;
			}
		}
		
		//把排班数据按照科室ID分组
		Map<String, List<ScheduleEmployee>> deptEmpRecordsMap = records.stream().collect(Collectors.groupingBy(ScheduleEmployee::getOrgId));
		
		//应出勤天数
		BigDecimal scheduledAttendance = getScheduledAttendance(startDate, endDate);
		//遍历科室，组装科室统计数据
		int index = 0;
		for(ScheduleDept scheduleDept : deptRecords){
			String deptId = scheduleDept.getDeptId();
			index++;
			Map<String, Object> deptMap = new HashMap<>();
			//序号
			deptMap.put("index", index);
			//归属科室
			deptMap.put("deptName", scheduleDept.getDeptName());
			deptMap.put("deptId", scheduleDept.getDeptId());

			if(deptEmpRecordsMap.containsKey(deptId)){
				BigDecimal attendanceUserCount = new BigDecimal(deptEmpRecordsMap.get(deptId).size());
				//出勤人数
				deptMap.put("attendanceUserCount", attendanceUserCount);
				BigDecimal deptScheduledAttendance = scheduledAttendance.multiply(attendanceUserCount);
				//应出勤天数
				if(deptScheduledAttendance.compareTo(BigDecimal.ZERO) == 0){
					deptMap.put("scheduledAttendance", 0);
				} else {
					deptMap.put("scheduledAttendance", deptScheduledAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
				}
				//实际出勤天数
				BigDecimal actualAttendance = BigDecimal.ZERO;
				//计算当前科室的人员出勤统计
				for(ScheduleEmployee emp : deptEmpRecordsMap.get(deptId)){
					actualAttendance = actualAttendance.add(emp.getActualAttendance());//把科室人员的实际出勤天数累加
				} 
				if(actualAttendance.compareTo(BigDecimal.ZERO) == 0){
					deptMap.put("actualAttendance", 0);
				} else {
					deptMap.put("actualAttendance", actualAttendance.setScale(1, RoundingMode.HALF_UP).toPlainString());
				}
				
				Map<String, Object> deptResultMap = new HashMap<>();//用于统计科室的数据
				//填充请假数据
				if(leaveKeySet.size() > 0){
					for(String key : leaveKeySet){
						BigDecimal keyValue = BigDecimal.ZERO;
						if(!key.equals("employeeCode") && !key.equals("orgId")){
							for(String empMapKey : leaveRecordMap.keySet()){
								String orgId = empMapKey.split("&")[1];
								if(deptId.equals(orgId)){
									Map<String, Object> empMap = leaveRecordMap.get(empMapKey);
									for(String empKey : empMap.keySet()){
										if(empKey.equals(key) && empMap.get(empKey) != null){
											keyValue = keyValue.add((BigDecimal) empMap.get(empKey));
										}
									}
								}
							}
						}
						//如果是0，则不显示，否则保留一位小数
						if(keyValue.compareTo(BigDecimal.ZERO) == 0){
							deptResultMap.put(key + "_leave", null);
						} else {
							deptResultMap.put(key + "_leave", keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
						}
					}
				} else {
					deptResultMap.putAll(leaveItemCodeMap);
				}
				
				//填充进修数据
				if(outKeySet.size() > 0){
					for(String key : outKeySet){
						BigDecimal keyValue = BigDecimal.ZERO;
						if(!key.equals("employeeCode") && !key.equals("orgId")){
							for(String empMapKey : outRecordMap.keySet()){
								String orgId = empMapKey.split("&")[1];
								if(deptId.equals(orgId)){
									Map<String, Object> empMap = outRecordMap.get(empMapKey);
									for(String empKey : empMap.keySet()){
										if(empKey.equals(key) && empMap.get(empKey) != null){
											keyValue = keyValue.add((BigDecimal) empMap.get(empKey));
										}
									}
								}
							}
						}
						//如果是0，则不显示，否则保留一位小数
						if(keyValue.compareTo(BigDecimal.ZERO) == 0){
							deptResultMap.put(key + "_out", null);
						} else {
							deptResultMap.put(key + "_out", keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
						}
					}
				} else {
					deptResultMap.putAll(outItemCodeMap);
				}
				
				//填充班次数据
				for(String key : scheduleKeySet){
					BigDecimal keyValue = BigDecimal.ZERO;
					if(!key.equals("employeeCode") && !key.equals("orgId")){
						for(String empMapKey : scheduleRecordMap.keySet()){
							String orgId = empMapKey.split("&")[1];
							if(deptId.equals(orgId)){
								Map<String, Object> empMap = scheduleRecordMap.get(empMapKey);
								for(String empKey : empMap.keySet()){
									if(empKey.equals(key) && empMap.get(empKey) != null){
										keyValue = keyValue.add((BigDecimal) empMap.get(empKey));
									}
								}
							}
						}
					}
					//如果是0，则不显示，否则保留一位小数
					if(keyValue.compareTo(BigDecimal.ZERO) == 0){
						deptResultMap.put(key, null);
					} else {
						deptResultMap.put(key, keyValue.setScale(1, RoundingMode.HALF_UP).toPlainString());
					}
				}
				
				deptMap.putAll(deptResultMap);
				
				//平均实际出勤=实际出勤/出勤人数
				if(attendanceUserCount.compareTo(BigDecimal.ZERO) != 0){
					BigDecimal averageAttendance = actualAttendance.divide(attendanceUserCount, 1, RoundingMode.HALF_UP);
					deptMap.put("averageAttendance", averageAttendance.toPlainString());
				} else {
					deptMap.put("averageAttendance", BigDecimal.ZERO);
				}
			} else {
				//出勤人数
				deptMap.put("attendanceUserCount", BigDecimal.ZERO);
				//应出勤天数
				deptMap.put("scheduledAttendance", BigDecimal.ZERO);
				//实际出勤天数
				deptMap.put("actualAttendance", BigDecimal.ZERO);
				//平均实际出勤
				deptMap.put("averageAttendance", BigDecimal.ZERO);
				deptMap.putAll(leaveItemCodeMap);
				deptMap.putAll(outItemCodeMap);
				deptMap.putAll(scheduleItemCodeMap);
			}
			resultData.add(deptMap);
		}
		
	}
	
	
	/**
	 * 请假类型天数统计
	 * @param employeeNoList
	 * @param startDate
	 * @param endDate
	 * @param type  个人-employee，科室-dept
	 * @return
	 */
	private Map<String, Map<String, Object>> getLeaveRecordStatistics(List<String> employeeNoList, String startDate, String endDate, String type){
		Map<String, Map<String, Object>> leaveRecordMap= new HashMap<>();
		List<DictItemResp> holidayTypeList = dictItemFeignService.getDictItemByTypeCode("HOLIDAY_TYPE").getObject();
		if(CollUtil.isNotEmpty(holidayTypeList)){
			List<String> itemCodeList = holidayTypeList.stream().map(DictItemResp::getItemName).collect(Collectors.toList());
			for(String key : itemCodeList){
				leaveItemCodeMap.put(key + "_leave", null);
			}
			MedRecordStatisticsVo vo = new MedRecordStatisticsVo();
			vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			vo.setItemCodeList(itemCodeList);
			vo.setStartDate(startDate);
			vo.setEndDate(endDate);
			vo.setEmployeeNoList(employeeNoList);
			List<Map<String, Object>> leaveRecordList = medCustomStatisticsTitleMapper.selectLeaveRecordStatistics(vo);//请假的数据
			List<Map<String, Object>> cancelLeaveRecordMap = medCustomStatisticsTitleMapper.selectCancelLeaveRecordStatistics(vo);//销假的数据
			//请假实际天数= 请假天数- 销假天数
			for(Map<String, Object> leaveRecord : leaveRecordList){
				Map<String, Object> reLeaveRecord = new HashMap<>();//用于存储key值带后缀的集合
				String employeeCode = (String) leaveRecord.get("employeeCode");
				String orgId = (String) leaveRecord.get("orgId");
				for(Map<String, Object> cancelLeaveMap : cancelLeaveRecordMap){
					if(employeeCode.equals(cancelLeaveMap.get("employeeCode"))){
						//遍历计算各类型的实际请假天数
						for(String holidayType : leaveRecord.keySet()){
							if(holidayType.equals("employeeCode")){
								reLeaveRecord.put("employeeCode", leaveRecord.get("employeeCode"));
								continue;
							}
							if(holidayType.equals("orgId")){
								reLeaveRecord.put("orgId", leaveRecord.get("orgId"));
								continue;
							}
							BigDecimal holidayTypeLeaveValue = (BigDecimal) leaveRecord.get(holidayType);
							//请假数据大于0才计算销假数据
							if(holidayTypeLeaveValue.compareTo(BigDecimal.ZERO) > 0){
								BigDecimal holidayTypeCancelLeaveValue = (BigDecimal) cancelLeaveMap.get(holidayType);
								leaveRecord.put(holidayType, holidayTypeLeaveValue.subtract(holidayTypeCancelLeaveValue));
							}
							reLeaveRecord.put(holidayType, leaveRecord.get(holidayType));
						}
						break;
					}
				}
				if(type.equals("employee")){
					if(cancelLeaveRecordMap.size() > 0){
						leaveRecordMap.put(employeeCode, reLeaveRecord);
					} else {
						leaveRecordMap.put(employeeCode, leaveRecord);
					}
				} else {
					if(cancelLeaveRecordMap.size() > 0){
						leaveRecordMap.put(employeeCode + "&" + orgId, reLeaveRecord);
					} else {
						leaveRecordMap.put(employeeCode + "&" + orgId, leaveRecord);
					}
				}
			}
		}
		return leaveRecordMap;
	}

	/**
	 * 进修/规培/学习/下乡等天数统计
	 * @param employeeIdList
	 * @param startDate
	 * @param endDate
	 * @param type 个人-employee，科室-dept
	 * @return
	 */
	private Map<String, Map<String, Object>> getOutRecordStatistics(List<String> employeeIdList, String startDate, String endDate, String type){
		Map<String, Map<String, Object>> outRecordMap = new HashMap<>();
		List<String> outTypeitemCodeList = Arrays.asList("进修,规培,外出学习,外出会议,公务外出,下乡".split(","));
		for(String key : outTypeitemCodeList){
			outItemCodeMap.put(key + "_out", null);
		}
		MedRecordStatisticsVo vo = new MedRecordStatisticsVo();
		vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		vo.setItemCodeList(outTypeitemCodeList);
		vo.setStartDate(startDate);
		vo.setEndDate(endDate);
		vo.setEmployeeIdList(employeeIdList);
		List<Map<String, Object>> outRecordList = medCustomStatisticsTitleMapper.selectOutRecordStatistics(vo);
		for(Map<String, Object> outRecord : outRecordList){
			Map<String, Object> reOutRecord = new HashMap<>();//用于存储key值带后缀的集合
			String employeeCode = (String) outRecord.get("employeeCode");
			String orgId = (String) outRecord.get("orgId");
			for(String outType : outRecord.keySet()){
				if(outType.equals("employeeCode")){
					reOutRecord.put("employeeCode", outRecord.get("employeeCode"));
					continue;
				}
				if(outType.equals("orgId")){
					reOutRecord.put("orgId", outRecord.get("orgId"));
					continue;
				}
				//将值转为String再转成BigDecimal
				if(!ObjectUtils.isEmpty(outRecord.get(outType))){
					reOutRecord.put(outType, new BigDecimal(String.valueOf(outRecord.get(outType))));
				} else {
					reOutRecord.put(outType, BigDecimal.ZERO);
				}
			}
			if(type.equals("employee")){
				outRecordMap.put(employeeCode, reOutRecord);
			} else {
				outRecordMap.put(employeeCode + "&" + orgId, reOutRecord);
			}
		}
		
		return outRecordMap;
	}
	
	/**
	 * 排班统计天数统计
	 * @param employeeIdList
	 * @param startDate
	 * @param endDate
	 * @param type 人员-employee，科室-dept
	 * @return
	 */
	private  Map<String, Map<String, Object>> getScheduleRecordStatistics(List<String> employeeIdList, String startDate, String endDate, String type){
		Map<String, Map<String, Object>> scheduleRecordMap = new HashMap<>();
		if(type.equals("category")){
			List<String> itemCodeList = new ArrayList<>();
			//先判断是否自定义班次，否则取所有启用班次数据
			List<MedCustomStatisticsTitle> list = selectCustomStatisticsTitle();
			if(list.size() > 0){
				for(MedCustomStatisticsTitle title : list){
					itemCodeList.add(title.getClassesId());
				}
			} else {
				/**
				 * 如果是admin，超级管理员，排班设置员查询时间范围内的有排班的数据班次
				 * 如果是排班管理员查询时间范围内，班次权限范围内以及有排班数据的班次
				 */
				ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
				scheduleEmployee.setStartDate(startDate);
				scheduleEmployee.setEndDate(endDate);
				if(!medScheduleAuthorityService.setQueryData(scheduleEmployee)){
					return scheduleRecordMap;
				}
				//排班数据
				List<MedScheduleRecord> recordList = medScheduleRecordService.selectMedScheduleRecordList(scheduleEmployee);
				Set<String> classesIds = new HashSet<>();
				for(MedScheduleRecord scheduleRecord : recordList){
					if(!classesIds.contains(scheduleRecord.getClassesId())){
						itemCodeList.add(scheduleRecord.getClassesId());
					}
				}
			}
			if(CollUtil.isEmpty(itemCodeList)){
				return scheduleRecordMap;
			}
			for(String key : itemCodeList){
				scheduleItemCodeMap.put(key, null);
			}
			MedRecordStatisticsVo vo = new MedRecordStatisticsVo();
			vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			vo.setItemCodeList(itemCodeList);
			vo.setStartDate(startDate);
			vo.setEndDate(endDate);
			vo.setEmployeeNoList(employeeIdList);
			List<Map<String, Object>> scheduleRecordList = medCustomStatisticsTitleMapper.selectScheduleRecordStatistics(vo);
			for(Map<String, Object> scheduleRecord : scheduleRecordList){
				scheduleRecordMap.put((String) scheduleRecord.get("employeeCode"), scheduleRecord);
			}
		} else {
			List<MedScheduleStatisticsTitleVo> scheduleStatisticsList = getTitelClassesIds(startDate, endDate);
			if(CollUtil.isNotEmpty(scheduleStatisticsList)){
				List<String> itemCodeList = scheduleStatisticsList.stream().map(MedScheduleStatisticsTitleVo::getProp).collect(Collectors.toList());
				for(String key : itemCodeList){
					scheduleItemCodeMap.put(key, null);
				}
				MedRecordStatisticsVo vo = new MedRecordStatisticsVo();
				vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				vo.setItemCodeList(itemCodeList);
				vo.setStartDate(startDate);
				vo.setEndDate(endDate);
				vo.setEmployeeNoList(employeeIdList);
				List<Map<String, Object>> scheduleRecordList = medCustomStatisticsTitleMapper.selectScheduleRecordStatistics(vo);
				for(Map<String, Object> scheduleRecord : scheduleRecordList){
					String employeeCode = (String) scheduleRecord.get("employeeCode");
					String orgId = (String) scheduleRecord.get("orgId");
					if(type.equals("employee")){
						scheduleRecordMap.put(employeeCode, scheduleRecord);
					} else {
						scheduleRecordMap.put(employeeCode + "&" + orgId, scheduleRecord);
					}
				}
			} else {
				return scheduleRecordMap;
			}
		}
		return scheduleRecordMap;
	}

	@Override
	public MedScheduleStatisticsDetailVo selectDetail(MedScheduleStatisticsReq record) {
		MedScheduleStatisticsDetailVo vo = new MedScheduleStatisticsDetailVo();
		//时间区间为空-则默认 开始时间上个月1号，结束时间 上个月最后一天
		if(ObjectUtils.isEmpty(record.getStartDate()) || ObjectUtils.isEmpty(record.getEndDate())){
			record.setStartDate(DateUtils.getlastMonthfirstDay(new Date()));//获取上个月第一天
			record.setEndDate(DateUtils.getlastMonthLastDay(new Date()));//获取上个月最后一天
		}
		String employeeId = record.getEmployeeId();
		String deptId = record.getDeptId();
		List<MedScheduleRecord> scheduleRecords = new ArrayList<>();
		List<LeaveRecordVo> oaRecords = new ArrayList<>();
		if(!ObjectUtils.isEmpty(employeeId)){//查看个人统计明细
			ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
			scheduleEmployee.setEmployeeId(record.getEmployeeId());
			List<ScheduleEmployee> empList = medScheduleAuthorityMapper.selectEmployeeByAuthorit(scheduleEmployee);
			if(CollUtil.isEmpty(empList)){
				return vo;
			}
			
			ScheduleEmployee emp = empList.get(0);
			BeanUtils.copyProperties(emp, vo);
			
			scheduleEmployee.setStartDate(record.getStartDate());
			scheduleEmployee.setEndDate(record.getEndDate());
			scheduleEmployee.setEmployeeNo(emp.getEmployeeNo());
			//查询请假数据 填充进去
			List<LeaveRecordVo> leaveDataList = medScheduleAuthorityMapper.selectLeaveData(scheduleEmployee);
			oaRecords.addAll(leaveDataList);
			
			//销假数据填充进去
			List<LeaveRecordVo> cancelLeaveDataList = medScheduleAuthorityMapper.selectCancelLeaveData(scheduleEmployee);
			for(LeaveRecordVo cancel : cancelLeaveDataList){
				cancel.setLeaveType("销假-" + cancel.getLeaveType());
			}
			oaRecords.addAll(cancelLeaveDataList);
			
			//查询进修、规培、学习会议、下乡等数据 填充进去
			List<LeaveRecordVo> outRecordDataList = medScheduleAuthorityMapper.selectOutRecordData(scheduleEmployee);
			oaRecords.addAll(outRecordDataList);
			
			//OA信息数据按开始时间升序排列
			if(CollUtil.isNotEmpty(oaRecords)){
				oaRecords.forEach(record1 -> {
	                // 格式化数字字段（如保留1位小数）
	                BigDecimal days = record1.getDays();
	                if (days != null) {
	                    // 保留1位小数
	                    record1.setDaysText(days.setScale(1, RoundingMode.HALF_UP).toPlainString());
	                }
	            });
				Collections.sort(oaRecords, (p1, p2) -> p1.getStartDate().compareTo(p2.getStartDate()));
			}
			vo.setOaRecords(oaRecords);
			
			//排班数据-排除掉请假时长 > 1 的班次，和班次管理页面数据保持一致
			scheduleEmployee.setHolidayClasses("0");
			List<MedScheduleRecord> recordList = medScheduleAuthorityService.getScheduleRecordLists(scheduleEmployee);//单个人的排班数据
			for(MedScheduleRecord re : recordList){
				if(!ObjectUtil.isEmpty(re.getId())){
					scheduleRecords.add(re);
				}
			}
			//排班数据按开始时间升序排列
			if(CollUtil.isNotEmpty(scheduleRecords)){
				scheduleRecords.forEach(record1 -> {
	                // 格式化数字字段（如保留1位小数）
	                BigDecimal classesDays = record1.getClassesDays();
	                if (classesDays != null) {
	                    // 保留1位小数
	                    record1.setClassesDaysText(classesDays.setScale(1, RoundingMode.HALF_UP).toPlainString());
	                }
	            });
				Collections.sort(scheduleRecords, (p1, p2) -> p1.getScheduleDate().compareTo(p2.getScheduleDate()));
			}
			vo.setScheduleRecords(scheduleRecords);
		} else if(!ObjectUtils.isEmpty(deptId)){//查看科室统计明细
			ScheduleEmployee scheduleEmployee = new ScheduleEmployee();
			scheduleEmployee.setStartDate(record.getStartDate());
			scheduleEmployee.setEndDate(record.getEndDate());
			scheduleEmployee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			scheduleEmployee.setEmployeeStatus(record.getEmployeeStatus());
			List<String> orgIdList = new ArrayList<>();
			orgIdList.add(deptId);
			scheduleEmployee.setOrgIdList(orgIdList);
			
			//获取科室名称
			List<String> deptIds = new ArrayList<>();
			deptIds.add(deptId);
			PlatformResult<List<OrganizationListRes>> orgResp = hrmsOrganizationFeignService.getListByIds(deptIds);
			if(orgResp.isSuccess() && null != orgResp.getObject() && orgResp.getObject().size() > 0){
				vo.setOrgName(orgResp.getObject().get(0).getName());
			}
			
			//查询科室的统计人员列表-排除没有排班的人员
			List<ScheduleEmployee> employeeRecords = medScheduleAuthorityMapper.selectEmployeeByOrgId(scheduleEmployee);
			vo.setUserCount(employeeRecords.size());
			if(CollUtil.isNotEmpty(employeeRecords)){
				List<String> employeeIds = employeeRecords.stream().map(ScheduleEmployee::getEmployeeId).collect(Collectors.toList());
				String deptExportType = record.getDeptExportType();//科室明细类型：1-班次数据，2-OA数据，默认班次数据
				String keywords = record.getKeywords();//姓名搜索
				if(!ObjectUtil.isEmpty(deptExportType) && deptExportType.equals("2")){
					scheduleEmployee.setEmployeeIds(employeeIds);
					//查询请假数据 填充进去
					List<LeaveRecordVo> leaveDataList = medScheduleAuthorityMapper.selectLeaveData(scheduleEmployee);
					oaRecords.addAll(leaveDataList);
					
					//销假数据填充进去
					List<LeaveRecordVo> cancelLeaveDataList = medScheduleAuthorityMapper.selectCancelLeaveData(scheduleEmployee);
					for(LeaveRecordVo cancel : cancelLeaveDataList){
						cancel.setLeaveType("销假-" + cancel.getLeaveType());
					}
					oaRecords.addAll(cancelLeaveDataList);
					
					//查询进修、规培、学习会议、下乡等数据 填充进去
					List<LeaveRecordVo> outRecordDataList = medScheduleAuthorityMapper.selectOutRecordData(scheduleEmployee);
					oaRecords.addAll(outRecordDataList);
					
					if(!ObjectUtils.isEmpty(record.getOaType()) || !ObjectUtils.isEmpty(keywords)){
						List<LeaveRecordVo> oaRecordVos = new ArrayList<>();
						//类型过滤
						if(!ObjectUtils.isEmpty(record.getOaType())){
							String oaType = record.getOaType();
							for(String oaTypeItem : oaType.split(",")){
								String typeValue = oaTypeItem.split("_")[0];
								String typeName = oaTypeItem.split("_")[1];
								if(typeName.equals("leave")){//请假
									for(LeaveRecordVo lrv : oaRecords){
										if(lrv.getType().equals("2") && typeValue.equals(lrv.getLeaveType())){
											oaRecordVos.add(lrv);
										}
									}
								} else if(typeName.equals("cancelLeave")){//销假
									for(LeaveRecordVo lrv : oaRecords){
										if(lrv.getType().equals("4") && ("销假-" + typeValue).equals(lrv.getLeaveType())){
											oaRecordVos.add(lrv);
										}
									}
								} else if(typeName.equals("out")){//进修、下乡等
									for(LeaveRecordVo lrv : oaRecords){
										if(lrv.getType().equals("3") && typeValue.equals(lrv.getLeaveType())){
											oaRecordVos.add(lrv);
										}
									}
								}
							}
						} else {
							oaRecordVos.addAll(oaRecords);
						}
						
						List<LeaveRecordVo> oaRecordVos1 = new ArrayList<>();
						//姓名过滤
						if(!ObjectUtils.isEmpty(keywords)){
							for(LeaveRecordVo v : oaRecordVos){
								if(v.getEmployeeName().contains(keywords)){
									oaRecordVos1.add(v);
								}
							}
						} else {
							oaRecordVos1.addAll(oaRecordVos);
						}
						//OA信息数据按开始时间升序排列
						if(CollUtil.isNotEmpty(oaRecordVos1)){
							oaRecordVos1.forEach(record1 -> {
				                // 格式化数字字段（如保留1位小数）
				                BigDecimal days = record1.getDays();
				                if (days != null) {
				                    // 保留1位小数
				                    record1.setDaysText(days.setScale(1, RoundingMode.HALF_UP).toPlainString());
				                }
				            });
							Collections.sort(oaRecordVos1, (p1, p2) -> p1.getStartDate().compareTo(p2.getStartDate()));
						}
						vo.setOaRecords(oaRecordVos1);
					} else {
						//OA信息数据按开始时间升序排列
						if(CollUtil.isNotEmpty(oaRecords)){
							oaRecords.forEach(record1 -> {
				                // 格式化数字字段（如保留1位小数）
				                BigDecimal days = record1.getDays();
				                if (days != null) {
				                    // 保留1位小数
				                    record1.setDaysText(days.setScale(1, RoundingMode.HALF_UP).toPlainString());
				                }
				            });
							Collections.sort(oaRecords, (p1, p2) -> p1.getStartDate().compareTo(p2.getStartDate()));
						}
						vo.setOaRecords(oaRecords);
					}
				} else {
					//依次获取员工的排班数据
					scheduleEmployee.setEmployeeIds(null);
					
					//按照班次过滤
					List<String> classesIdList = new ArrayList<>();
					if(!ObjectUtils.isEmpty(record.getClassesIds())){
						classesIdList = Arrays.asList(record.getClassesIds().split(","));
					}
					
					for(String empId : employeeIds){
						scheduleEmployee.setEmployeeId(empId);
						//排班数据-排除掉请假时长 > 1 的班次，和班次管理页面数据保持一致
						scheduleEmployee.setHolidayClasses("0");
						List<MedScheduleRecord> recordList = medScheduleAuthorityService.getScheduleRecordLists(scheduleEmployee);
						for(MedScheduleRecord re : recordList){
							if(!ObjectUtil.isEmpty(re.getId())){//排除请假，进修等数据
								scheduleRecords.add(re);
							}
						}
					}
					
					if(classesIdList.size() > 0 || !ObjectUtils.isEmpty(keywords)){//根据班次过滤
						List<MedScheduleRecord> scheduleRecord1 = new ArrayList<MedScheduleRecord>();
						//班次过滤
						if(classesIdList.size() > 0){
							for(MedScheduleRecord v : scheduleRecords){
								if(classesIdList.size() > 0 && classesIdList.contains(v.getClassesId())){
									scheduleRecord1.add(v);
								}
							}
						} else {
							scheduleRecord1.addAll(scheduleRecords);
						}
						//姓名过滤
						List<MedScheduleRecord> scheduleRecord2 = new ArrayList<MedScheduleRecord>();
						if(!ObjectUtils.isEmpty(keywords)){
							for(MedScheduleRecord v : scheduleRecord1){
								if(v.getEmployeeName().contains(keywords)){
									scheduleRecord2.add(v);
								}
							}
						} else {
							scheduleRecord2.addAll(scheduleRecord1);
						}
						//排班数据按开始时间升序排列
						if(CollUtil.isNotEmpty(scheduleRecord2)){
							scheduleRecord2.forEach(record1 -> {
				                // 格式化数字字段（如保留1位小数）
				                BigDecimal classesDays = record1.getClassesDays();
				                if (classesDays != null) {
				                    // 保留1位小数
				                    record1.setClassesDaysText(classesDays.setScale(1, RoundingMode.HALF_UP).toPlainString());
				                }
				            });
							Collections.sort(scheduleRecord2, (p1, p2) -> p1.getScheduleDate().compareTo(p2.getScheduleDate()));
						}
						vo.setScheduleRecords(scheduleRecord2);
					} else if(classesIdList.size() == 0) {
						//排班数据按开始时间升序排列
						if(CollUtil.isNotEmpty(scheduleRecords)){
							scheduleRecords.forEach(record1 -> {
				                // 格式化数字字段（如保留1位小数）
				                BigDecimal classesDays = record1.getClassesDays();
				                if (classesDays != null) {
				                    // 保留1位小数
				                    record1.setClassesDaysText(classesDays.setScale(1, RoundingMode.HALF_UP).toPlainString());
				                }
				            });
							Collections.sort(scheduleRecords, (p1, p2) -> p1.getScheduleDate().compareTo(p2.getScheduleDate()));
						}
						vo.setScheduleRecords(scheduleRecords);
					}
				}
				
			}
		}
		return vo;
	}

	@Transactional(readOnly = false)
	@Override
	public void saveMedCustomStatisticsExport(List<MedCustomStatisticsExport> records) {
		if(records.size() == 0){
			return;
		}
		//先全部删除，再根据传递的数据更新数据的删除标识
		Example example = new Example(MedCustomStatisticsExport.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		MedCustomStatisticsExport updateEntity = new MedCustomStatisticsExport();
		updateEntity.setIsDeleted(Contants.IS_DELETED_TURE);
		medCustomStatisticsExportMapper.updateByExampleSelective(updateEntity, example);
		int index = 1;
		for(MedCustomStatisticsExport record : records){
			record.setSort(index++);
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if(!ObjectUtils.isEmpty(record.getId())){
				record.setIsDeleted(Contants.IS_DELETED_FALSE);
				record.setUpdateDate(new Date());
				if (user != null) {
					record.setUpdateUser(user.getUsercode());
					record.setUpdateUserName(user.getUsername());
				}
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				medCustomStatisticsExportMapper.updateByPrimaryKeySelective(record);
				continue;
			}
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted(Contants.IS_DELETED_FALSE);
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setSsoOrgName(user.getOrgName());
			medCustomStatisticsExportMapper.insertSelective(record);
		}
		
	}

	@Override
	public List<MedCustomStatisticsExport> selectMedCustomStatisticsExport(String type) {
		if(ObjectUtils.isEmpty(type)){
			type = "1";
		}
		Example example = new Example(MedCustomStatisticsExport.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("type", type);
		example.orderBy("sort").asc();
		return medCustomStatisticsExportMapper.selectByExample(example);
	}
}
