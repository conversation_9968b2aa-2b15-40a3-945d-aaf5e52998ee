package cn.trasen.hrms.med.kpi.controller;

import java.text.DecimalFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.kpi.model.OfYpZbk;
import cn.trasen.hrms.med.kpi.service.OfYpZbkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName OfYpZbkController
 * @Description TODO
 * @date 2025��5��22�� ����10:32:51
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "OfYpZbkController")
public class OfYpZbkController {

	private transient static final Logger logger = LoggerFactory.getLogger(OfYpZbkController.class);

	@Autowired
	private OfYpZbkService ofYpZbkService;

	/**
	 * @Title saveOfYpZbk
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/ofYpZbk/save")
	public PlatformResult<String> saveOfYpZbk(@RequestBody OfYpZbk record) {
		try {
			ofYpZbkService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateOfYpZbk
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/ofYpZbk/update")
	public PlatformResult<String> updateOfYpZbk(@RequestBody OfYpZbk record) {
		try {
			ofYpZbkService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectOfYpZbkById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<OfYpZbk>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/ofYpZbk/{id}")
	public PlatformResult<OfYpZbk> selectOfYpZbkById(@PathVariable String id) {
		try {
			OfYpZbk record = ofYpZbkService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteOfYpZbkById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/ofYpZbk/delete/{id}")
	public PlatformResult<String> deleteOfYpZbkById(@PathVariable String id) {
		try {
			ofYpZbkService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectOfYpZbkList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<OfYpZbk>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/ofYpZbk/list")
	public DataSet<OfYpZbk> selectOfYpZbkList(Page page, OfYpZbk record) {
		return ofYpZbkService.getDataSetList(page, record);
	}
	
	
	
	/**
	 * @Title getYsTopByCode
	 * @Description 获取医生单个指标Top
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取医生单个指标Top", notes = "获取医生单个指标Top")
	@GetMapping("/api/ofYpZbk/getYsTopByCode")
	public PlatformResult<List<Map<String,Object>>> getYsTopByCode(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getYsTopByCode(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title getKsTopByCode
	 * @Description 获取科室单个指标Top
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取科室单个指标Top", notes = "获取科室单个指标Top")
	@GetMapping("/api/ofYpZbk/getKsTopByCode")
	public PlatformResult<List<Map<String,Object>>> getKsTopByCode(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getKsTopByCode(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title getYsTopByFzFmCode
	 * @Description 获取医生(分子/分母)指标Top
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取医生单个占比类型指标Top", notes = "获取医生单个占比类型指标Top")
	@GetMapping("/api/ofYpZbk/getYsTopByFzFmCode")
	public PlatformResult<List<Map<String,Object>>> getYsTopByFzFmCode(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getYsTopByFzFmCode(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title getKsTopByFzFmCode
	 * @Description 获取科室(分子/分母)指标Top
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取科室单个占比类型指标Top", notes = "获取科室单个占比类型指标Top")
	@GetMapping("/api/ofYpZbk/getKsTopByFzFmCode")
	public PlatformResult<List<Map<String,Object>>> getKsTopByFzFmCode(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getKsTopByFzFmCode(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title getKsYpZb
	 * @Description 获取科室药品指标_多个指标(药品看板)
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取科室药品指标_药品看板多个指标", notes = "获取科室药品指标_药品看板多个指标")
	@GetMapping("/api/ofYpZbk/getKsYpZb")
	public PlatformResult<List<Map<String,Object>>> getKsYpZb(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getKsYpZb(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title getYsYpZb
	 * @Description 获取医生药品指标_多个指标(药品看板)
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取科医生药品指标_药品看板多个指标", notes = "获取医生药品指标_药品看板多个指标")
	@GetMapping("/api/ofYpZbk/getYsYpZb")
	public PlatformResult<List<Map<String,Object>>> getYsYpZb(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getYsYpZb(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title getYpZbSumAll
	 * @Description 获取药品指标_多个指标汇总(药品看板)
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取药品指标_多个指标汇总(药品看板)", notes = "获取药品指标_多个指标汇总(药品看板)")
	@GetMapping("/api/ofYpZbk/getYpZbSumAll")
	public PlatformResult<List<Map<String,Object>>> getYpZbSumAll(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getYpZbSumAll(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title getKsYpZb
	 * @Description 获取科室排名第一的指标——多个指标(药品看板)
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取科室排名第一的指标值——药品看板多个指标", notes = "获取科室排名第一的指标值——药品看板多个指标")
	@GetMapping("/api/ofYpZbk/getKsTopOne")
	public PlatformResult<List<Map<String,Object>>> getKsTopOne(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getKsTopOne(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title getYsYpZb
	 * @Description 获取医生排名第一的指标——多个指标(药品看板)
	 * @param record
	 * @return PlatformResult<List<Map<String,Object>>>
	 * @date 2025��5��22�� ����10:32:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取医生排名第一的指标值——药品看板多个指标", notes = "获取医生排名第一的指标值——药品看板多个指标")
	@GetMapping("/api/ofYpZbk/getYsTopOne")
	public PlatformResult<List<Map<String,Object>>> getYsTopOne(OfYpZbk record) {
		try {
			return PlatformResult.success(ofYpZbkService.getYsTopOne(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	
	/**
	* @date 2025年06月09日
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出药品看板科室列表", notes = "导出药品看板科室列表")
	@GetMapping("/api/ofYpZbk/exportGetKsYpZb")
	public void exportGetKsYpZb(HttpServletRequest request, HttpServletResponse response,OfYpZbk record) {
	List<Map<String,Object>> records = ofYpZbkService.getKsYpZb(record);
	DecimalFormat  df = new DecimalFormat("0.##");
	DecimalFormat  df2 = new DecimalFormat("0");
	for (Map<String, Object> dataMap : records) {
		for (String key : dataMap.keySet()) {
			if (key.equals("CF_HGL") || key.equals("DLYP_CGL") || key.equals("DLYP_SYL") || key.equals("ZDYP_XHJE_ZF") || key.equals("YP_KCZZL") || key.equals("YP_ZSR_ZB") || key.equals("MZ_YP_ZSR_ZB") || key.equals("ZY_YP_ZSR_ZB") || key.equals("GZHZ_SYFW_FHL") ) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df.format(Double.valueOf(dataMap.get(key).toString()) * 100) +"%");
				}
			}
			if (key.equals("ZY_GEYCFYSJ") || key.equals("CYPSMS_SYZBL") || key.equals("MZ_DCFS") || key.equals("MZ_CYPCF")   ) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df2.format(Double.valueOf(dataMap.get(key).toString()) ));
				}
			}
			if (key.equals("KJYW_SYQD") || key.equals("KZLYWXH")  || key.equals("CSYZYY")) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df.format(Double.valueOf(dataMap.get(key).toString())));
				}
			}
		}
	}	
	// 导出文件名称
	String name = "科室药品指标.xls";
	
	// 模板位置
	String templateUrl = "template/exportGetKsYpZb.xls";
	// 导出数据列表
	try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	/**
	* @date 2025年06月09日
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出药品看板医生列表", notes = "导出药品看板医生列表")
	@GetMapping("/api/ofYpZbk/exportGetYsYpZb")
	public void exportGetYsYpZb(HttpServletRequest request, HttpServletResponse response,OfYpZbk record) {
	List<Map<String,Object>> records = ofYpZbkService.getYsYpZb(record);
	DecimalFormat  df = new DecimalFormat("0.##");
	DecimalFormat  df2 = new DecimalFormat("0");
	for (Map<String, Object> dataMap : records) {
		for (String key : dataMap.keySet()) {
			if (key.equals("CF_HGL") || key.equals("DLYP_CGL") || key.equals("DLYP_SYL") || key.equals("ZDYP_XHJE_ZF") || key.equals("YP_KCZZL") || key.equals("YP_ZSR_ZB") || key.equals("MZ_YP_ZSR_ZB") || key.equals("ZY_YP_ZSR_ZB") || key.equals("GZHZ_SYFW_FHL") ) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df.format(Double.valueOf(dataMap.get(key).toString()) * 100) +"%");
				}
			}
			if (key.equals("ZY_GEYCFYSJ") || key.equals("CYPSMS_SYZBL") || key.equals("MZ_DCFS") || key.equals("MZ_CYPCF")   ) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df2.format(Double.valueOf(dataMap.get(key).toString()) ));
				}
			}
			if (key.equals("KJYW_SYQD") || key.equals("KZLYWXH") || key.equals("CSYZYY")) {
				if (null != dataMap.get(key)) {
					dataMap.put(key, df.format(Double.valueOf(dataMap.get(key).toString())));
				}
			}
		}
	}	
	// 导出文件名称
	String name = "医生药品指标.xls";
	
	// 模板位置
	String templateUrl = "template/exportGetYsYpZb.xls";
	// 导出数据列表
	try {
			if (CollectionUtils.isNotEmpty(records)) {
				ExportUtil.export(request, response, records, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
