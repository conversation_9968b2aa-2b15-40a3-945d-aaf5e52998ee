package cn.trasen.hrms.med.comm.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.comm.dao.CategoryDictMapper;
import cn.trasen.hrms.med.comm.model.CategoryDict;
import cn.trasen.hrms.med.comm.service.CategoryDictService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CommCategoryDictServiceImpl
 * @Description TODO
 * @date 2024��11��29�� ����11:39:00
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CategoryDictServiceImpl implements CategoryDictService {
	
	@Autowired
	private CategoryDictMapper mapper;
	
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(CategoryDict record) {
		//暂时只支持加子节点
		Assert.hasText(record.getPid(), "父级编码不能为空！");
		CategoryDict parent = mapper.selectByPrimaryKey(record.getPid());
		if(null == parent){
			throw new RuntimeException(StrUtil.format("父级编码{}不存在", record.getPid()));
		}
		if(!isAuth("2",parent.getCode())){
			throw new RuntimeException("对不起，您无操作权限！");
		}
		Example example = new Example(CategoryDict.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("pid", record.getPid());
		List<CategoryDict> items = mapper.selectByExample(example);
		int num = 0;
		if(CollUtil.isNotEmpty(items)){
			if(items.size() >= 99){
				//暂时只支持两位
				throw new RuntimeException(StrUtil.format("父节点{}下已有{}个子节点，不允许再添加！", record.getName(),items.size()));
			}
			List<Integer> codes = new ArrayList<Integer>();
			items.forEach(i -> {
				List<String> numbers = ReUtil.findAllGroup0("\\d+", i.getCode());
				if(CollUtil.isNotEmpty(numbers)){
					codes.add(Convert.toInt(numbers.get(numbers.size() - 1)));
				}
			});
			num = Collections.max(codes);
		}
		record.setId(IdGeneraterUtils.nextId());
		record.setCode(StrUtil.format("{}{}{}", parent.getCode(),StrUtil.sub(parent.getCode(), 0, 1),StrUtil.padPre(Convert.toStr(num + 1),2,"0")));
		record.setHasChild("0");
		record.setLevel(parent.getLevel() + 1);
		record.setSortOrder(num);
		record.setIsEnable("1");
		List<String> seeEmpIds = new ArrayList<String>();
		List<String> oprtEmpIds = new ArrayList<String>();
		if(StrUtil.isNotEmpty(parent.getSeeEmpIds())){
			seeEmpIds.addAll(ListUtil.of(parent.getSeeEmpIds().split(",")));
		}
		if(StrUtil.isNotEmpty(record.getSeeEmpIds())){
			seeEmpIds.addAll(ListUtil.of(record.getSeeEmpIds().split(",")));
		}
		if(StrUtil.isNotEmpty(parent.getOprtEmpIds())){
			oprtEmpIds.addAll(ListUtil.of(parent.getOprtEmpIds().split(",")));
		}
		if(StrUtil.isNotEmpty(record.getOprtEmpIds())){
			oprtEmpIds.addAll(ListUtil.of(record.getOprtEmpIds().split(",")));
		}
		record.setSeeEmpIds(CollUtil.join(seeEmpIds.stream().distinct().collect(Collectors.toList()), ","));
		record.setOprtEmpIds(CollUtil.join(oprtEmpIds.stream().distinct().collect(Collectors.toList()), ","));
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		int result = mapper.insertSelective(record);
		if(result > 0 && StrUtil.equals(parent.getHasChild(), "0")){
			//更新父节点的 hasChild
			parent.setHasChild("1");
			mapper.updateByPrimaryKeySelective(parent);
		}
		return result;
	}
	
	@Transactional(readOnly = false)
	@Override
	public Integer update(CategoryDict record) {
		CategoryDict dic = mapper.selectByPrimaryKey(record.getId());
		if(null != dic){
			if(!isAuth("2",dic.getCode())){
				throw new RuntimeException("对不起，您无操作权限！");
			}
			// 如果修改了权限，子节点的 权限不做处理
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			record.setUpdateDate(new Date());
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			return mapper.updateByPrimaryKeySelective(record);
			
		}
		return 0;
	}
	
	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		CategoryDict dic = mapper.selectByPrimaryKey(id);
		if(null != dic){
			if(!isAuth("2",dic.getCode())){
				throw new RuntimeException("对不起，您无操作权限！");
			}
			//删除当前节点 以及下级所有的节点
			Example example = new Example(CategoryDict.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andLike("code", dic.getCode() + "%");
			CategoryDict record = new CategoryDict();
			record.setUpdateDate(new Date());
			record.setIsDeleted("Y");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			int cnts = mapper.updateByExampleSelective(record, example);
			//如果删除当前节点后，父节点再没有子节点，则更新父节点的 hasChild
			if(cnts > 0){
				example.clear();
				criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria.andEqualTo("pid",dic.getPid());
				criteria.andNotEqualTo("id", dic.getId());
				if(mapper.selectCountByExample(example) <= 0){
					record = new CategoryDict();
					record.setId(dic.getPid());
					record.setHasChild("0");
					mapper.updateByPrimaryKeySelective(record);
				}
			}
			return cnts;
		}
		return 0;
	}
	
	@Override
	public CategoryDict selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CategoryDict record = mapper.selectByPrimaryKey(id);
		if(null != record && !isAuth("1",record.getCode())){
			throw new RuntimeException("对不起，您无查看权限！");
		}
		return record;
	}
	@Override
	public DataSet<CategoryDict> getDataSetList(Page page, CategoryDict record) {
		Example example = new Example(CategoryDict.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("isEnable", "1");
		if(record.getLevel() > 0){
			criteria.andEqualTo("level",record.getLevel());
		}
		if(StrUtil.isNotEmpty(record.getCode())){
			criteria.andLike("code", record.getCode() + "%");
		}
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if(null != user && !UserInfoHolder.ISADMIN() && !UserInfoHolder.ISALL()){//判断可查看权限
			criteria.andCondition(StrUtil.format("(emp_ids is null or concat(',',see_emp_ids,',') like '%,{},%' )", user.getId()));
		}
		example.orderBy("sortOrder");
		List<CategoryDict> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public List<CategoryDict> getTreeBycodeAndLv(CategoryDict record) {
		Example example = new Example(CategoryDict.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("isEnable", "1");
		criteria.andLike("code", record.getCode() + "%");
		criteria.andGreaterThanOrEqualTo("level", record.getLevel());
		if(StrUtil.isNotEmpty(record.getAuthType())){
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (null != user && !UserInfoHolder.ISADMIN() && !UserInfoHolder.ISALL()) {
				if("1".equals(record.getAuthType())){//判断可查看权限
					criteria.andCondition(StrUtil.format("(see_emp_ids is null or see_emp_ids = '' or concat(',',see_emp_ids,',') like '%,{},%' )", user.getId()));
				}else if("2".equals(record.getAuthType())){//判断可操作权限
					criteria.andCondition(StrUtil.format("(oprt_emp_ids is null or oprt_emp_ids = '' or concat(',',oprt_emp_ids,',') like '%,{},%' )", user.getId()));
				}
			}
		}
		List<CategoryDict> list = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(list)){
			Map<String, CategoryDict> map = new HashMap<>();// 创建一个映射，方便快速查找
			list.forEach(i -> {
				map.put(i.getId(), i);
			});
			list.forEach(i -> {
				if (StrUtil.isNotEmpty(i.getPid()) && map.containsKey(i.getPid())) {
					CategoryDict parent = map.get(i.getPid());
					if (parent.getChildren() == null) {
						parent.setChildren(new ArrayList<>());
						parent.setChildCtn(0);
					}
					parent.getChildren().add(i);
					parent.setChildCtn(parent.getChildCtn() + 1);
				}
			});
		}
		return list.stream().filter(i -> i.getLevel() == record.getLevel()).collect(Collectors.toList());
	}
	
	@Override
	public CategoryDict getOneNormalByCode(String code) {
		if(StrUtil.isNotEmpty(code)){
			Example example = new Example(CategoryDict.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
			criteria.andEqualTo("code", code);
			List<CategoryDict> ls = mapper.selectByExample(example);
			if(CollUtil.isNotEmpty(ls)){
				return ls.get(0);
			}
		}
		return null;
	}
	
	@Override
	public List<CategoryDict> getNormalsByCode(String code) {
		if(StrUtil.isNotEmpty(code)){
			Example example = new Example(CategoryDict.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
			criteria.andEqualTo("isEnable", "1");
			criteria.andLike("code", code + "%");
			return mapper.selectByExample(example);
		}
		return null;
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 判断是否有权限
	  -- 作者: GW
	  -- 创建时间: 2024年12月20日
	  -- @param authType  1查看权限2操作权限
	  -- @param code  字典编码
	  -- @return
	  -- =============================================
	 */
	@Override
	public boolean isAuth(String authType, String code) {
		if(!ReUtil.isMatch("1|2", authType)) return true;
		if(StrUtil.isEmpty(code)) return true;
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if(null == user) return true;
		if(UserInfoHolder.ISADMIN()) return true;
		if(UserInfoHolder.ISALL()) return true;
		CategoryDict dic = getOneNormalByCode(code);
		if(null == dic) return true;
		if("1".equals(authType)){
			if(StrUtil.isEmpty(dic.getSeeEmpIds())) return true;
			if(StrUtil.contains("," + dic.getOprtEmpIds() + ",", user.getId())) return true;
		}else{
			if(StrUtil.isEmpty(dic.getOprtEmpIds())) return true;
			if(StrUtil.contains("," + dic.getOprtEmpIds() + ",", user.getId())) return true;
		}
		return false;
	}
}
