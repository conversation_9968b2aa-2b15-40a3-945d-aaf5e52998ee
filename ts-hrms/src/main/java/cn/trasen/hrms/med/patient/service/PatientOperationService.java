package cn.trasen.hrms.med.patient.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientOperation;

/**
 * @ClassName PatientOperationService
 * @Description TODO
 * @date 2025��4��8�� ����4:38:18
 * <AUTHOR>
 * @version 1.0
 */
public interface PatientOperationService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	Integer save(PatientOperation record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	Integer update(PatientOperation record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientOperation
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	PatientOperation selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientOperation>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	DataSet<PatientOperation> getDataSetList(Page page, PatientOperation record);
	
	void updateOrSavePatientOperation();
}
