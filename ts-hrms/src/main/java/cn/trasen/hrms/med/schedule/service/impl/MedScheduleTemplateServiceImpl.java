package cn.trasen.hrms.med.schedule.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.schedule.dao.MedScheduleTemplateExtendMapper;
import cn.trasen.hrms.med.schedule.dao.MedScheduleTemplateMapper;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplate;
import cn.trasen.hrms.med.schedule.model.MedScheduleTemplateExtend;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleTemplateService;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedScheduleTemplateServiceImpl
 * @Description TODO
 * @date 2025��4��1�� ����6:48:40
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedScheduleTemplateServiceImpl implements MedScheduleTemplateService {

	@Autowired
	private MedScheduleTemplateMapper mapper;
	
	@Autowired
	private MedScheduleTemplateExtendMapper medScheduleTemplateExtendMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedScheduleTemplate record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedScheduleTemplate record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedScheduleTemplate record = new MedScheduleTemplate();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedScheduleTemplate selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedScheduleTemplate> getDataSetList(Page page, MedScheduleTemplate record) {
		Example example = new Example(MedScheduleTemplate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<MedScheduleTemplate> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void batchInsert(List<MedScheduleTemplate> list) {
		
		List<MedScheduleTemplate> insertList = new ArrayList<MedScheduleTemplate>();
		
		for (MedScheduleTemplate medScheduleTemplate : list) {
			
			MedScheduleTemplate record = new MedScheduleTemplate();
			if(StringUtil.isEmpty(medScheduleTemplate.getEmpOrgId())) {
				medScheduleTemplate.setEmpOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			}
			
			record.setCreateUser(UserInfoHolder.getCurrentUserCode());			
			mapper.delete(record);
			
			if(StringUtils.isNotBlank(medScheduleTemplate.getClassesId())) {
				medScheduleTemplate.setId(IdUtil.getId());
				medScheduleTemplate.setIsDeleted(Contants.IS_DELETED_FALSE);
				medScheduleTemplate.setCreateUser(UserInfoHolder.getCurrentUserCode());
				medScheduleTemplate.setCreateUserName(UserInfoHolder.getCurrentUserName());
				medScheduleTemplate.setCreateDate(new Date());
				record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				insertList.add(medScheduleTemplate);
			}
		}
		
		if(CollectionUtils.isNotEmpty(insertList)) {
			
			if(insertList.size() > 1000) {
				List<List<MedScheduleTemplate>> saveList = CommonUtils.averageAssign(insertList,1000);
				
				saveList.stream().forEach(ls ->{
					mapper.batchInsert(ls);	
		        });
			}else {
				mapper.batchInsert(insertList);	
			}
			
		}
	}

	@Deprecated
	@Override
	public List<MedScheduleTemplate> getScheduleTemplateList() {
		Example example = new Example(MedScheduleTemplate.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
		return mapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveMedScheduleTemplate(ScheduleEmployee record) {
		//删除排班
		clearScheduleTemplateRecord(record);
		
		String status = record.getStatus();
		
		//根据模板设置人和所属机构查询模板拓展表-用于更新状态
//		MedScheduleTemplateExtend templateExtend = mapper.selectTemplateExtendByEmp(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());
//		if(null == templateExtend){
//			templateExtend = new MedScheduleTemplateExtend();
//			templateExtend.setId(IdUtil.getId());
//			templateExtend.setEmployeeNo(UserInfoHolder.getCurrentUserCode());
//			templateExtend.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
//			templateExtend.setStatus(status);
//			medScheduleTemplateExtendMapper.insertSelective(templateExtend);
//		} else {
//			templateExtend.setStatus(status);
//			medScheduleTemplateExtendMapper.updateByPrimaryKeySelective(templateExtend);
//		}
		
		if(CollectionUtils.isNotEmpty(record.getScheduleRecords())) {
			List<MedScheduleTemplate> insertList = new ArrayList<MedScheduleTemplate>();
			for (MedScheduleRecord medScheduleRecord : record.getScheduleRecords()) {
				MedScheduleTemplate scheduleTemplate = new MedScheduleTemplate();
				if(StringUtil.isEmpty(medScheduleRecord.getEmpOrgId())) {
					scheduleTemplate.setEmpOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				}
				if(StringUtils.isNotBlank(medScheduleRecord.getClassesId())) {
		            BeanUtils.copyProperties(medScheduleRecord, scheduleTemplate);
					scheduleTemplate.setId(IdUtil.getId());
					scheduleTemplate.setIsDeleted(Contants.IS_DELETED_FALSE);
					scheduleTemplate.setCreateUser(UserInfoHolder.getCurrentUserCode());
					scheduleTemplate.setCreateUserName(UserInfoHolder.getCurrentUserName());
					scheduleTemplate.setCreateDate(new Date());
					scheduleTemplate.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					insertList.add(scheduleTemplate);
				}
			}
			if(CollectionUtils.isNotEmpty(insertList)) {
				
				if(insertList.size() > 1000) {
					List<List<MedScheduleTemplate>> saveList = CommonUtils.averageAssign(insertList, 1000);
					
					saveList.stream().forEach(ls ->{
						mapper.batchInsert(ls);	
			        });
				}else {
					mapper.batchInsert(insertList);	
				}
				
			}
		}
	}
	
	/**
	 * 删除人员ID在 employeeIds 中且主键ID 不在 ids 中
	 * @param record
	 */
	private void clearScheduleTemplateRecord(ScheduleEmployee record){
		if(CollUtil.isNotEmpty(record.getEmployeeIds())){
			record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
			mapper.deleteMedScheduleTemplateRecord(record);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public Integer updateStatus(String status) {
		//根据模板设置人和所属机构查询模板拓展表-用于更新状态
		MedScheduleTemplateExtend templateExtend = mapper.selectTemplateExtendByEmp(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());
		if(null == templateExtend){
			templateExtend = new MedScheduleTemplateExtend();
			templateExtend.setId(IdUtil.getId());
			templateExtend.setEmployeeNo(UserInfoHolder.getCurrentUserCode());
			templateExtend.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			templateExtend.setStatus(status);
			medScheduleTemplateExtendMapper.insert(templateExtend);
		} else {
			templateExtend.setStatus(status);
			medScheduleTemplateExtendMapper.updateByPrimaryKeySelective(templateExtend);
		}
		return 1;
	}

	@Override
	public MedScheduleTemplateExtend getScheduleTemplateExtend() {
		return mapper.selectTemplateExtendByEmp(UserInfoHolder.getCurrentUserCode(), UserInfoHolder.getCurrentUserCorpCode());
	}
	
}
