package cn.trasen.hrms.med.crisisValue.service;

import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.crisisValue.model.MedCrisisValue;

/**
 * @ClassName MedCrisisValueService
 * @Description TODO
 * @date 2025��5��24�� ����4:18:04
 * <AUTHOR>
 * @version 1.0
 */
public interface MedCrisisValueService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	Integer save(MedCrisisValue record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	Integer update(MedCrisisValue record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedCrisisValue
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	MedCrisisValue selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedCrisisValue>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	DataSet<MedCrisisValue> getDataSetList(Page page, MedCrisisValue record);

	void syncMedCrisisValue();

	void handleMedCrisisValue(MedCrisisValue record);

	Map<String,Object> indexStatistics(MedCrisisValue record);

	Map<String,Object> tableStatistics(MedCrisisValue record);

	MedCrisisValue selectByRepNo(String repNo);
}
