package cn.trasen.hrms.med.schedule.model;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 校验班次是否能够复制入参
 * @update 2025-07-11 14:30:00
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class MedScheduleClassesValidateCopyVo {
	
    @ApiModelProperty(value = "被复制的班次ID")
    private List<String> scheduleClassesIdList;
	
	@ApiModelProperty(value = "目标人员ID")
    private String employeeId;
	
}
