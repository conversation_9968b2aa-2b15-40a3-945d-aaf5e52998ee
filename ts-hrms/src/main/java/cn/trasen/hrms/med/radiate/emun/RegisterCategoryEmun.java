package cn.trasen.hrms.med.radiate.emun;

/**
 * 放射登记类别
 * <AUTHOR>
 *
 */
public enum RegisterCategoryEmun {
	
	CHECKUP("1", "体检状况登记"),  
	TRAINING("2", "培训状况登记"),  
	DOSE("3", "剂量监测登记");
	
	private String name;

	private String code;

	RegisterCategoryEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (RegisterCategoryEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}
