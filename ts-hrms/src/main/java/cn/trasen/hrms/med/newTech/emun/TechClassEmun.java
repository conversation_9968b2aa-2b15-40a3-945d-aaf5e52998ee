package cn.trasen.hrms.med.newTech.emun;

/**
 * 项目分类
 * <AUTHOR>
 *
 */
public enum TechClassEmun {
	
	I("1", "I类临床新技术(成熟技术，院内首次开展)"),  
	II ("2", "II类临床新技术(国内首创)"),  
	III("3", "III类临床新技术(国际首创)");
	private String name;

	private String code;

	TechClassEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (TechClassEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}
