<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.shift.dao.MedShiftPatientMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.shift.model.MedShiftPatient">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="patn_name" jdbcType="VARCHAR" property="patnName" />
    <result column="bed_no" jdbcType="VARCHAR" property="bedNo" />
    <result column="patn_sex" jdbcType="VARCHAR" property="patnSex" />
    <result column="patn_age" jdbcType="VARCHAR" property="patnAge" />
    <result column="patient_type" jdbcType="VARCHAR" property="patientType" />
    <result column="in_dete" jdbcType="TIMESTAMP" property="inDete" />
    <result column="in_diagnosis" jdbcType="VARCHAR" property="inDiagnosis" />
    <result column="diagnosi" jdbcType="VARCHAR" property="diagnosi" />
    <result column="transfer_in_date" jdbcType="TIMESTAMP" property="transferInDate" />
    <result column="transfer_remark" jdbcType="VARCHAR" property="transferRemark" />
    <result column="diagnosi_plan" jdbcType="VARCHAR" property="diagnosiPlan" />
    <result column="crisis_value" jdbcType="VARCHAR" property="crisisValue" />
    <result column="special_remark" jdbcType="VARCHAR" property="specialRemark" />
    <result column="out_remark" jdbcType="VARCHAR" property="outRemark" />
    <result column="out_date" jdbcType="TIMESTAMP" property="outDate" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="temperature" jdbcType="VARCHAR" property="temperature" />
    <result column="pulse" jdbcType="VARCHAR" property="pulse" />
    <result column="respiratory_rate" jdbcType="VARCHAR" property="respiratoryRate" />
    <result column="blood_pressure" jdbcType="VARCHAR" property="bloodPressure" />
    <result column="awareness" jdbcType="VARCHAR" property="awareness" />
    <result column="bos" jdbcType="VARCHAR" property="bos" />
    <result column="dgw" jdbcType="VARCHAR" property="dgw" />
    <result column="rescue_process" jdbcType="VARCHAR" property="rescueProcess" />
    <result column="death_remrk" jdbcType="VARCHAR" property="deathRemrk" />
    <result column="death_date" jdbcType="TIMESTAMP" property="deathDate" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
  </resultMap>
  
  <delete id="deleteUnCommit" parameterType="String">
  		DELETE FROM med_shift_patient 
			WHERE record_id IN (
			    SELECT id FROM med_shift_record WHERE status = '0' AND handover_doctor_id = #{currentUserCode}
			)
  </delete>
</mapper>