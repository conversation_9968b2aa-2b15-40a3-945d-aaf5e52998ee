package cn.trasen.hrms.med.crisisValue.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.crisisValue.model.MedCrisisValue;
import cn.trasen.hrms.med.crisisValue.service.MedCrisisValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedCrisisValueController
 * @Description TODO
 * @date 2025��5��24�� ����4:18:04
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "危急值记录")
public class MedCrisisValueController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedCrisisValueController.class);

	@Autowired
	private MedCrisisValueService medCrisisValueService;

	/**
	 * @Title saveMedCrisisValue
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medCrisisValue/save")
	public PlatformResult<String> saveMedCrisisValue(@RequestBody MedCrisisValue record) {
		try {
			medCrisisValueService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedCrisisValue
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medCrisisValue/update")
	public PlatformResult<String> updateMedCrisisValue(@RequestBody MedCrisisValue record) {
		try {
			medCrisisValueService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedCrisisValueById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedCrisisValue>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medCrisisValue/{id}")
	public PlatformResult<MedCrisisValue> selectMedCrisisValueById(@PathVariable String id) {
		try {
			MedCrisisValue record = medCrisisValueService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedCrisisValueById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medCrisisValue/delete/{id}")
	public PlatformResult<String> deleteMedCrisisValueById(@PathVariable String id) {
		try {
			medCrisisValueService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	@ApiOperation(value = "列表数量统计", notes = "列表数量统计")
    @PostMapping("/api/medCrisisValue/tableStatistics")
	public PlatformResult<Map<String,Object>> tableStatistics(@RequestBody MedCrisisValue record) {
        try {
        	return PlatformResult.success(medCrisisValueService.tableStatistics(record));
        } catch (Exception e) {
        	logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }

	/**
	 * @Title selectMedCrisisValueList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedCrisisValue>
	 * @date 2025��5��24�� ����4:18:04
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medCrisisValue/list")
	public DataSet<MedCrisisValue> selectMedCrisisValueList(Page page, MedCrisisValue record) {
		return medCrisisValueService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "工作台统计", notes = "工作台统计")
    @PostMapping("/api/medCrisisValue/indexStatistics")
	public PlatformResult<Map<String,Object>> indexStatistics(@RequestBody MedCrisisValue record) {
        try {
        	return PlatformResult.success(medCrisisValueService.indexStatistics(record));
        } catch (Exception e) {
        	logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
	
	/**
	 * 
	 * @param record
	 * @return
	 */
	@ApiOperation(value = "处理危急值并回传给HIS", notes = "处理危急值并回传给HIS")
    @PostMapping("/api/medCrisisValue/handleMedCrisisValue")
	public PlatformResult<String> handleMedCrisisValue(@RequestBody MedCrisisValue record) {
        try {
        	medCrisisValueService.handleMedCrisisValue(record);
        	return PlatformResult.success("更新成功");
        } catch (Exception e) {
        	logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
	
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/medCrisisValue/export")
	public void export(Page page,HttpServletResponse response, HttpServletRequest request,MedCrisisValue record) {
		try {
			page.setPageNo(1);
			page.setPageSize(Integer.MAX_VALUE);
			
			DataSet<MedCrisisValue> dataSet = medCrisisValueService.getDataSetList(page, record);
			List<MedCrisisValue> list = dataSet.getRows();
			
			int i = 1;
			for (MedCrisisValue medCrisisValue : list) {
				medCrisisValue.setOrderNumber(i);
				if("1".equals(medCrisisValue.getVisitType())){
					medCrisisValue.setVisitType("门诊");
				}
				if("2".equals(medCrisisValue.getVisitType())){
					medCrisisValue.setVisitType("住院");
				}
				if("-1".equals(medCrisisValue.getStatus())){
					medCrisisValue.setStatus("已通知");
				}
				if("0".equals(medCrisisValue.getStatus())){
					medCrisisValue.setStatus("未处理");
				}
				if("1".equals(medCrisisValue.getStatus())){
					medCrisisValue.setStatus("已处理");
				}
				
				i++;
			}
			
			//表头标题
            String name = "危急值信息" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/medCrisisValueExport.xlsx";
            
            Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list);
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
