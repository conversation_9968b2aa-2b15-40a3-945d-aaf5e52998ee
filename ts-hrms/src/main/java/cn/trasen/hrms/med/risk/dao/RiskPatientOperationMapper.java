package cn.trasen.hrms.med.risk.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.risk.model.RiskPatientOperation;
import tk.mybatis.mapper.common.Mapper;

public interface RiskPatientOperationMapper extends Mapper<RiskPatientOperation> {
	
	List<RiskPatientOperation>  selectOperationMessageData();
	
	Map<String,Object> selectOperationLeaderMessageData();
	
	List<RiskPatientOperation> selectPageList(Page page, RiskPatientOperation record);
}