package cn.trasen.hrms.med.crisisValue.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_his_employee")
@Setter
@Getter
public class MedHisEmployee {
    @Id
    @Column(name = "employee_id")
    private String employeeId;

    @Column(name = "employee_no")
    private String employeeNo;

    @Column(name = "employee_name")
    private String employeeName;

    @Column(name = "org_id")
    private String orgId;
    
    @Column(name = "org_name")
    private String orgName;
    
    @Column(name = "org_code")
    private String orgCode;
    
    @Column(name = "hosp_code")
    private String hospCode;
    
    @Transient
    private String oaEmployeeNo;
    
    @Transient
    private String technical;
    
    @Transient
    private String phoneNumber;
    
}