package cn.trasen.hrms.med.rounds.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.rounds.model.RoundsGroupRules;

/**
 * @ClassName RoundsGroupRulesService
 * @Description TODO
 * @date 2025��3��6�� ����10:58:52
 * <AUTHOR>
 * @version 1.0
 */
public interface RoundsGroupRulesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��6�� ����10:58:52
	 * <AUTHOR>
	 */
	Integer save(RoundsGroupRules record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��6�� ����10:58:52
	 * <AUTHOR>
	 */
	Integer update(RoundsGroupRules record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��6�� ����10:58:52
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return RoundsGroupRules
	 * @date 2025��3��6�� ����10:58:52
	 * <AUTHOR>
	 */
	RoundsGroupRules selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<RoundsGroupRules>
	 * @date 2025��3��6�� ����10:58:52
	 * <AUTHOR>
	 */
	DataSet<RoundsGroupRules> getDataSetList(Page page, RoundsGroupRules record);
}
