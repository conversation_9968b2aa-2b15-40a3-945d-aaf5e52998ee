package cn.trasen.hrms.med.rounds.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_rounds_task")
@Setter
@Getter
public class RoundsTask {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 排班id
     */
    @Column(name = "scheduling_id")
    @ApiModelProperty(value = "排班id")
    private String schedulingId;

    /**
     * 科主任
     */
    @Column(name = "director_user_code")
    @ApiModelProperty(value = "科主任")
    private String directorUserCode;

    /**
     * 科主任名称
     */
    @Column(name = "director_user_name")
    @ApiModelProperty(value = "科主任名称")
    private String directorUserName;

    /**
     * 护士长
     */
    @Column(name = "nurse_user_code")
    @ApiModelProperty(value = "护士长")
    private String nurseUserCode;

    /**
     * 护士长名称
     */
    @Column(name = "nurse_user_name")
    @ApiModelProperty(value = "护士长名称")
    private String nurseUserName;

    /**
     * 整改截止日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @Column(name = "rectification_deadline")
    @ApiModelProperty(value = "整改截止日期")
    private Date rectificationDeadline;

    /**
     * 状态;1待扣分,2待审核,3待整改,4待复检,5已办结
     */
    @ApiModelProperty(value = "状态;1待扣分,2待审核,3待整改,4待复检,5已办结")
    private String status;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 整改要求
     */
    @Column(name = "rectification_requirements")
    @ApiModelProperty(value = "整改要求")
    private String rectificationRequirements;
    
    @Transient
    @ApiModelProperty(value = "任务小组信息")
    private List<RoundsTaskGroup> roundsTaskGroupList;
    
    @Transient
    @ApiModelProperty(value = "巡查时间")
    private String roundsTime;

    @Transient
    @ApiModelProperty(value = "周次")
    private String weekNumber;

    @Transient
    @ApiModelProperty(value = "周几")
    private String weekValue;

    @Transient
    @ApiModelProperty(value = "院区")
    private String hospArea;
    
    @Transient
    @ApiModelProperty(value = "院区文本")
    private String hospAreaText;

    @Transient
    @ApiModelProperty(value = "查房科室")
    private String roundsOrg;

    @Transient
    @ApiModelProperty(value = "查房科室名称")
    private String roundsOrgName;
    
    @Transient
    @ApiModelProperty(value = "创建开始日期")
    private String createDateBegin;
    
    @Transient
    @ApiModelProperty(value = "创建结束日期")
    private String createDateEnd;
    
    @Transient
    @ApiModelProperty(value = "查房开始日期")
    private String roundsTimeBegin;
    
    @Transient
    @ApiModelProperty(value = "查房结束日期")
    private String roundsTimeEnd;
    
    @Transient
    @ApiModelProperty(value = "完成开始日期")
    private String completionDateBegin;
    
    @Transient
    @ApiModelProperty(value = "完成结束日期")
    private String completionDateEnd;
    
    @Transient
    @ApiModelProperty(value = "排班所有小组用户code")
    private String schedulingGroupUserCode;
    
    @Transient
    @ApiModelProperty(value = "排班所有小组用户名称")
    private String schedulingGroupUserName;
    
    @Transient
    @ApiModelProperty(value = "查询条件list:机构id")
    private List<String> orgIdList;
    
    @Transient
    @ApiModelProperty(value = "查询条件机构orgIds")
    private String orgIds;
    
    @Transient
    @ApiModelProperty(value = "整改前总得分")
    private BigDecimal score;
    
    @Transient
    @ApiModelProperty(value = "整改后总得分")
    private BigDecimal rectificationScore;
    
    /**
     * 办结日期
     */
    @Column(name = "completion_date")
    @ApiModelProperty(value = "办结日期")
    private Date completionDate;
    
    @Transient
    @ApiModelProperty(value = "查房秘书")
    private String secretaryUser;
    
    @Transient
    @ApiModelProperty(value = "查房秘书姓名")
    private String secretaryUserName;
    
    @Transient
    @ApiModelProperty(value = "查房科室地址")
    private String roundsOrgAddress;
    
    @Transient
    @ApiModelProperty(value = "编辑状态:传1(暂存或者全量编辑,不改变状态)")
    private String editStatus;
    
    @Transient
    @ApiModelProperty(value = "查询年月")
    private String queryYearMonth;
    
    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String files;
    
    @Transient
    @ApiModelProperty(value = "任务名称")
    private String taskName;
}