package cn.trasen.hrms.med.rounds.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroup;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RoundsTaskGroupController
 * @Description TODO
 * @date 2025��3��7�� ����3:27:11
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "查房任务分组表")
public class RoundsTaskGroupController {

	private transient static final Logger logger = LoggerFactory.getLogger(RoundsTaskGroupController.class);

	@Autowired
	private RoundsTaskGroupService roundsTaskGroupService;

	/**
	 * @Title saveRoundsTaskGroup
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����3:27:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/roundsTaskGroup/save")
	public PlatformResult<String> saveRoundsTaskGroup(@RequestBody RoundsTaskGroup record) {
		try {
			roundsTaskGroupService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRoundsTaskGroup
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����3:27:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/roundsTaskGroup/update")
	public PlatformResult<String> updateRoundsTaskGroup(@RequestBody RoundsTaskGroup record) {
		try {
			roundsTaskGroupService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRoundsTaskGroupById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RoundsTaskGroup>
	 * @date 2025��3��7�� ����3:27:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/roundsTaskGroup/{id}")
	public PlatformResult<RoundsTaskGroup> selectRoundsTaskGroupById(@PathVariable String id) {
		try {
			RoundsTaskGroup record = roundsTaskGroupService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRoundsTaskGroupById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����3:27:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/roundsTaskGroup/delete/{id}")
	public PlatformResult<String> deleteRoundsTaskGroupById(@PathVariable String id) {
		try {
			roundsTaskGroupService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRoundsTaskGroupList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RoundsTaskGroup>
	 * @date 2025��3��7�� ����3:27:11
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/roundsTaskGroup/list")
	public DataSet<RoundsTaskGroup> selectRoundsTaskGroupList(Page page, RoundsTaskGroup record) {
		return roundsTaskGroupService.getDataSetList(page, record);
	}
	
}
