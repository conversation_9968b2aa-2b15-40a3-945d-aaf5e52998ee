package cn.trasen.hrms.med.qua.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_qua_auth_mgt")
@Setter
@Getter
public class QuaAuthMgt {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 资质授权类型
     */
    @Column(name = "qua_auth_type")
    @ApiModelProperty(value = "资质授权类型")
    private String quaAuthType;

    /**
     * 医生ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "医生ID")
    private String employeeId;

    /**
     * 医生工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "医生工号")
    private String employeeNo;

    /**
     * 医生姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "医生姓名")
    private String employeeName;
    
    /**
     * 身份证号
     */
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;
    /**
     * 技术职称
     */
    @Column(name = "jobtitle")
    @ApiModelProperty(value = "技术职称")
    private String jobtitle;
    /**
     * 联系电话
     */
    @Column(name = "tel")
    @ApiModelProperty(value = "联系电话")
    private String tel;

    /**
     * 所在科室ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "所在科室ID")
    private String orgId;

    /**
     * 所在科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "所在科室名称")
    private String orgName;

    /**
     * 所在院区
     */
    @Column(name = "hosp_area")
    @ApiModelProperty(value = "所在院区")
    private String hospArea;

    /**
     * 流程实例id
     */
    @Column(name = "workflow_id")
    @ApiModelProperty(value = "流程实例id")
    private String workflowId;
    
    /**
     * 审批状态
     */
    @Column(name = "audit_status")
    @ApiModelProperty(value = "审批状态")
    private String auditStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    private String quaAuthTypeName;
    @Transient
    private String hospAreaName;
    @Transient
    private String auditStatusName;
    
    @Transient
    private String appyTimeStart;
    @Transient
    private String appyTimeEnd;
    @Transient
    private String begntimeStart;
    @Transient
    private String begntimeEnd;
    @Transient
    private String endtimeStart;
    @Transient
    private String endtimeEnd;
    @Transient
    private String authLvHosp;
    
    @Transient
    private List<QuaAuthItemDetl> itemDetls;
    @Transient
    private List<QuaAuthFile> files;
}