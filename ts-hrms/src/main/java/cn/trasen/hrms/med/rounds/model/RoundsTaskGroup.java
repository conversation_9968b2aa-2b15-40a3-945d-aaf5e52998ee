package cn.trasen.hrms.med.rounds.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_rounds_task_group")
@Setter
@Getter
public class RoundsTaskGroup {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty(value = "任务id")
    private String taskId;

    /**
     * 排班id
     */
    @Column(name = "scheduling_id")
    @ApiModelProperty(value = "排班id")
    private String schedulingId;

    /**
     * 小组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "小组id")
    private String groupId;

    /**
     * 选择的小组用户
     */
    @Column(name = "group_user_code")
    @ApiModelProperty(value = "选择的小组用户")
    private String groupUserCode;

    /**
     * 选择的小组用户名称
     */
    @Column(name = "group_user_name")
    @ApiModelProperty(value = "选择的小组用户名称")
    private String groupUserName;

    /**
     * 得分
     */
    @ApiModelProperty(value = "得分")
    private BigDecimal score;

    /**
     * 整改附件
     */
    @Column(name = "rectification_files")
    @ApiModelProperty(value = "整改附件")
    private String rectificationFiles;

    /**
     * 整改后得分
     */
    @Column(name = "rectification_score")
    @ApiModelProperty(value = "整改后得分")
    private BigDecimal rectificationScore;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 评估时间
     */
    @Column(name = "evaluation_time")
    @ApiModelProperty(value = "评估时间")
    private String evaluationTime;

    /**
     * 评估人
     */
    @ApiModelProperty(value = "评估人")
    private String evaluator;
    
    /**
     * 评估人code
     */
    @Column(name = "evaluator_code")
    @ApiModelProperty(value = "评估人code")
    private String evaluatorCode;

    /**
     * 评估附件
     */
    @Column(name = "evaluation_files")
    @ApiModelProperty(value = "评估附件")
    private String evaluationFiles;

    /**
     * 状态;1扣分待提交,2扣分已提交,0扣分打回,3整改结果已提交,4复检结果已提交
     */
    @ApiModelProperty(value = "状态;1扣分待提交,2扣分已提交,0扣分打回,3整改结果已提交,4复检结果已提交")
    private String status;

    /**
     * 打回原因
     */
    @Column(name = "return_reason")
    @ApiModelProperty(value = "打回原因")
    private String returnReason;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 存在的问题
     */
    @Column(name = "existing_problems")
    @ApiModelProperty(value = "存在的问题")
    private String existingProblems;

    /**
     * 原因分析
     */
    @Column(name = "cause_analysis")
    @ApiModelProperty(value = "原因分析")
    private String causeAnalysis;

    /**
     * 整改措施
     */
    @Column(name = "rectification_measures")
    @ApiModelProperty(value = "整改措施")
    private String rectificationMeasures;

    /**
     * 整改结果
     */
    @Column(name = "rectification_results")
    @ApiModelProperty(value = "整改结果")
    private String rectificationResults;
    
    @Transient
    @ApiModelProperty(value = "任务小组评分细则")
    private List<RoundsTaskGroupRules> roundsTaskGroupRulesList;
    
    @Transient
    @ApiModelProperty(value = "小组名称")
    private String groupName;
    
    @Transient
    @ApiModelProperty(value = "状态参数:0查询打回数据,1查询未提交和打回数据，2查询扣分已提交数据，3查询未整改数据,4查询整改完成数据,5查询未复检数据,6查询已复检数据")
    private String statusParam;
    
    @Transient
    @ApiModelProperty(value = "1科主任,2护士长")
    private String replyPerson;
    
    @Transient
    @ApiModelProperty(value = "排序字段")
    private String sort;
    
    @Transient
    @ApiModelProperty(value = "细则得分合计")
    private BigDecimal hjScore;
    
    @Transient
    @ApiModelProperty(value = "细则扣分合计")
    private BigDecimal hjDeductPoints;
    
}