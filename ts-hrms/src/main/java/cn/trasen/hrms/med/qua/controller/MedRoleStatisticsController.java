package cn.trasen.hrms.med.qua.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.qua.model.MedRoleStatistics;
import cn.trasen.hrms.med.qua.service.MedRoleStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedRoleStatisticsController
 * @Description TODO
 * @date 2025��6��4�� ����5:58:40
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "MedRoleStatisticsController")
public class MedRoleStatisticsController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedRoleStatisticsController.class);

	@Autowired
	private MedRoleStatisticsService medRoleStatisticsService;

	/**
	 * @Title saveMedRoleStatistics
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��6��4�� ����5:58:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medRoleStatistics/save")
	public PlatformResult<String> saveMedRoleStatistics(@RequestBody MedRoleStatistics record) {
		try {
			medRoleStatisticsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedRoleStatistics
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��6��4�� ����5:58:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medRoleStatistics/update")
	public PlatformResult<String> updateMedRoleStatistics(@RequestBody MedRoleStatistics record) {
		try {
			medRoleStatisticsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedRoleStatisticsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedRoleStatistics>
	 * @date 2025��6��4�� ����5:58:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medRoleStatistics/{id}")
	public PlatformResult<MedRoleStatistics> selectMedRoleStatisticsById(@PathVariable String id) {
		try {
			MedRoleStatistics record = medRoleStatisticsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedRoleStatisticsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��6��4�� ����5:58:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medRoleStatistics/delete/{id}")
	public PlatformResult<String> deleteMedRoleStatisticsById(@PathVariable String id) {
		try {
			medRoleStatisticsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedRoleStatisticsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedRoleStatistics>
	 * @date 2025��6��4�� ����5:58:40
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medRoleStatistics/list")
	public DataSet<MedRoleStatistics> selectMedRoleStatisticsList(Page page, MedRoleStatistics record) {
		return medRoleStatisticsService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "重点监控权限", notes = "重点监控权限")
	@GetMapping("/api/medRoleStatistics/getWarnRoleList")
	public PlatformResult<List<MedRoleStatistics>> getWarnRoleList() {
		try {
			List<MedRoleStatistics> record = medRoleStatisticsService.getWarnRoleList();
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
