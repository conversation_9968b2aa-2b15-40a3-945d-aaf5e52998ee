package cn.trasen.hrms.med.radiate.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 放射诊疗培训查询参照
 * @date 2025-05-22 11:52:53
 * <AUTHOR> @version 1.0
 */
@Setter
@Getter
public class RadiateTrainingReqVo {
    
    @ApiModelProperty(value = "状态")
    private String employeeStatus;

    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    @ApiModelProperty(value = "人员id")
    private String employeeId;

    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;
    
    @ApiModelProperty(value = "培训类型")
    private String trainingType;
    
    @ApiModelProperty(value = "培训开始时间")
    private String trainingDateBegin;
    
    @ApiModelProperty(value = "培训结束时间")
    private String trainingDateEnd;
    
    @ApiModelProperty(value = "培训到期开始时间")
    private String expirationDateBegin;
    
    @ApiModelProperty(value = "培训到期结束时间")
    private String expirationDateEnd;
    
    @ApiModelProperty(value = "所属科室ID")
    private String orgId;

    @ApiModelProperty(value = "所属科室ID多个逗号隔开")
    private String orgIds;

    @ApiModelProperty(value = "所属科室ID集合")
    private List<String> orgIdList;
    
    @ApiModelProperty(value = "搜索关键字-工号/姓名")
    private String condition;
    
    @ApiModelProperty(value = "是否查询到期，0-未到期，1-已到期，不查询")
    private String expired;
    
    @ApiModelProperty(value = "系统当前时间：yyyy-MM-dd")
    private String today;

}
