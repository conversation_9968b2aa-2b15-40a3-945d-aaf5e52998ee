package cn.trasen.hrms.med.newTech.vo;

import java.util.Date;

import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EmployeeRespVo {
	
    @ApiModelProperty(value = "员工ID ")
    private String employeeId;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工性别")
    private String sex;

    @ApiModelProperty(value = "员工出生年月")
    private Date birthDate;

    @ApiModelProperty(value = "员工所属机构编码")
    private String ssoOrgCode;

    @ApiModelProperty(value = "员工所属机构名称")
    private String ssoOrgName;

    @ApiModelProperty(value = "员工所属院区编码")
    private String hospCode;

    @ApiModelProperty(value = "员工所属院区名称")
    private String hospArea;

    @ApiModelProperty(value = "员工科室ID")
    private String orgId;

    @ApiModelProperty(value = "员工科室名称")
    private String orgName;

    @ApiModelProperty(value = "员工身份证号")
    private String identityNumber;

    @ApiModelProperty(value = "员工手机号")
    private String tel;

    @ApiModelProperty(value = "职务")
    private String dutyTitle;

    @Transient
    @ApiModelProperty(value = "职称")
    private String jobtitle;
    
    @ApiModelProperty(value = "获取职称时间")
    private Date jobTitleDate;

    @ApiModelProperty(value = "最高学历")
    private String degree;
}
