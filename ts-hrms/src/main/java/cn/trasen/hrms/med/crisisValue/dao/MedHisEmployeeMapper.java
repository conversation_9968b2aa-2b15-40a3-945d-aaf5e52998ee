package cn.trasen.hrms.med.crisisValue.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.crisisValue.model.MedHisEmployee;
import tk.mybatis.mapper.common.Mapper;

public interface MedHisEmployeeMapper extends Mapper<MedHisEmployee> {

	void batchInsert(List<MedHisEmployee> list);

	MedHisEmployee selectHisEmployee(@Param("currentUserCode") String currentUserCode,@Param("hisEmployeeId") String hisEmployeeId);

	String selectPlatformOrgType();
}