package cn.trasen.hrms.voluntaries.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.voluntaries.model.HrmsVoluntariesActivityMaster;
import cn.trasen.hrms.voluntaries.service.HrmsVoluntariesActivityMasterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsVoluntariesActivityMasterController
 * @Description TODO
 * @date 2023��9��7�� ����3:34:06
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "志愿者活动主表")
public class HrmsVoluntariesActivityMasterController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsVoluntariesActivityMasterController.class);

	@Autowired
	private HrmsVoluntariesActivityMasterService hrmsVoluntariesActivityMasterService;

	/**
	 * @Title saveHrmsVoluntariesActivityMaster
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����3:34:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/voluntariesActivityMaster/save")
	public PlatformResult<String> saveHrmsVoluntariesActivityMaster(@RequestBody HrmsVoluntariesActivityMaster record) {
		try {
			hrmsVoluntariesActivityMasterService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsVoluntariesActivityMaster
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����3:34:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/voluntariesActivityMaster/update")
	public PlatformResult<String> updateHrmsVoluntariesActivityMaster(@RequestBody HrmsVoluntariesActivityMaster record) {
		try {
			hrmsVoluntariesActivityMasterService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsVoluntariesActivityMasterById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsVoluntariesActivityMaster>
	 * @date 2023��9��7�� ����3:34:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/voluntariesActivityMaster/{id}")
	public PlatformResult<HrmsVoluntariesActivityMaster> selectHrmsVoluntariesActivityMasterById(@PathVariable String id) {
		try {
			HrmsVoluntariesActivityMaster record = hrmsVoluntariesActivityMasterService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsVoluntariesActivityMasterById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����3:34:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/voluntariesActivityMaster/delete/{id}")
	public PlatformResult<String> deleteHrmsVoluntariesActivityMasterById(@PathVariable String id) {
		try {
			hrmsVoluntariesActivityMasterService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsVoluntariesActivityMasterList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsVoluntariesActivityMaster>
	 * @date 2023��9��7�� ����3:34:06
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/voluntariesActivityMaster/list")
	public DataSet<HrmsVoluntariesActivityMaster> selectHrmsVoluntariesActivityMasterList(Page page, HrmsVoluntariesActivityMaster record) {
		return hrmsVoluntariesActivityMasterService.getDataSetList(page, record);
	}
}
