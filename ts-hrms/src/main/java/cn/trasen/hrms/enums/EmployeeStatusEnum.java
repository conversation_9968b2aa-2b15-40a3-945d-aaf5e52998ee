package cn.trasen.hrms.enums;

import lombok.Getter;

/**   
 * @Title: EmployeeStatusEnum.java 
 * @Package cn.trasen.hrms.enums 
 * @Description: 员工状态枚举
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月17日 下午6:06:08 
 * @version V1.0   
 */
@Getter
public enum EmployeeStatusEnum {
	
	EMPLOYEE_STATUS_1("1", "在职"),
	
	EMPLOYEE_STATUS_4("4", "离职"),

	EMPLOYEE_STATUS_5("5", "延聘"),

	EMPLOYEE_STATUS_6("6", "院内返聘"),
	
	EMPLOYEE_STATUS_7("7", "死亡"),
	
	EMPLOYEE_STATUS_8("8", "退休"),
	
	EMPLOYEE_STATUS_9("9", " 借调"),

	EMPLOYEE_STATUS_10("10", "长期病休"),

	EMPLOYEE_STATUS_11("11", "助勤"),
	
	EMPLOYEE_STATUS_12("12", "院外返聘"),
	
	EMPLOYEE_STATUS_13("13", "转岗");

	private final String key;
	private final String val;

	private EmployeeStatusEnum(String key, String val) {
		this.key = key;
		this.val = val;
	}

	/**
	 * @Title: getValByKey
	 * @Description: 根据Key获得val值
	 * @Param: key
	 * @Return: String
	 * <AUTHOR>
	 */
	public static String getValByKey(String key) {
		for (EmployeeStatusEnum item : EmployeeStatusEnum.values()) {
			if (item.key.equals(key)) {
				return item.val;
			}
		}
		return "";
	}
}
