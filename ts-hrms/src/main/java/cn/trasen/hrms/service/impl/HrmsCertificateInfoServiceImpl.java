package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsCertificateInfoMapper;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.model.HrmsCertificateInfo;
import cn.trasen.hrms.service.HrmsCertificateInfoService;
import cn.trasen.hrms.service.HrmsDictInfoService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsCertificateInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工证书信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月12日 下午5:28:07 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsCertificateInfoServiceImpl implements HrmsCertificateInfoService {

	@Autowired
	HrmsCertificateInfoMapper hrmsCertificateInfoMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	/**
	 * @Title: insert
	 * @Description: 新增证书信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsCertificateInfo entity) {
		entity.setCertificateInfoId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if(StringUtil.isEmpty(entity.getApprovalStatus())) {
			entity.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
		}
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		int row = hrmsCertificateInfoMapper.insert(entity);
//		if(row > 0) {
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileService.updateTempBusinessId(entity.getCertificateInfoId(), entity.getBusinessId());
//			}
//		}
		
		return row;
	}

	/**
	 * @Title: update
	 * @Description: 更新证书信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsCertificateInfo entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		
		int row = hrmsCertificateInfoMapper.updateByPrimaryKeySelective(entity);
//		if(row > 0) {
//			if(StringUtils.isNotBlank(entity.getBusinessId())) {
//				fileService.updateTempBusinessId(entity.getCertificateInfoId(), entity.getBusinessId());
//			}
//		}
		return row;
	}

	/**
	 * @Title: deleted
	 * @Description: 删除证书信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsCertificateInfo certificateInfo = hrmsCertificateInfoMapper.selectByPrimaryKey(id);
		if (certificateInfo != null) {
			certificateInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsCertificateInfoMapper.updateByPrimaryKeySelective(certificateInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取证书信息列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsCertificateInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsCertificateInfo> getDataList(Page page, HrmsCertificateInfo entity) {

/*		Example example = new Example(HrmsCertificateInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		if (StringUtils.isNotBlank(entity.getCertificateName())) { // 证书名称
			example.and().andLike("certificateName", "%" + entity.getCertificateName() + "%");
		}
		if (StringUtils.isNotBlank(entity.getCertificateNumber())) { // 证书编号
			example.and().andEqualTo("certificateNumber", entity.getCertificateNumber());
		}
		if (StringUtils.isNotBlank(entity.getCertificateType())) { // 证书类型
			example.and().andEqualTo("certificateType", entity.getCertificateType());
		}
		if (StringUtils.isNotBlank(entity.getCreateUser())) { //创建人
			example.and().andEqualTo("createUser", entity.getCreateUser());
		}
		if (StringUtils.isNotBlank(entity.getApprovalStatus())) { 
			example.and().andEqualTo("approvalStatus", entity.getApprovalStatus());
		}*/
		//数据权限
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if(!UserInfoHolder.ISADMIN()) {	// 是否管理员   
			if(!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				 entity.setHtOrgIdList(orgRang);
			}
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsCertificateInfo> list = hrmsCertificateInfoMapper.getPageList(page,entity);

//		List<HrmsCertificateInfo> list = hrmsCertificateInfoMapper.selectByExampleAndRowBounds(example, page);
		
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> acceptMethodMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_OBTAIN_TYPE); // 获取途径字典
			Map<String, String> certificateTypeMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_TYPE);
			for (HrmsCertificateInfo hrmsCertificateInfo : list) {
				hrmsCertificateInfo.setAcceptMethodText(acceptMethodMap.get(hrmsCertificateInfo.getAcceptMethod())); // 获取途径
				hrmsCertificateInfo.setCertificateTypeText(certificateTypeMap.get(hrmsCertificateInfo.getCertificateType())); 
				hrmsCertificateInfo.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(hrmsCertificateInfo.getApprovalStatus()));//审批状态
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 查询证书信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsCertificateInfo>
	 * <AUTHOR>
	 * @date 2020年4月21日 下午6:21:17
	 */
	@Override
	public List<HrmsCertificateInfo> getList(HrmsCertificateInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsCertificateInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getCertificateInfoId())) {
			example.and().andEqualTo("certificateInfoId", entity.getCertificateInfoId());
		}
		if (StringUtils.isNotBlank(entity.getApprovalStatus())) { 
			example.and().andEqualTo("approvalStatus", entity.getApprovalStatus());
		}

		List<HrmsCertificateInfo> list = hrmsCertificateInfoMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> certificateTypeMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_TYPE); // 证书类型字典
			Map<String, String> acceptMethodMap = hrmsDictInfoService.convertDictMap(DictContants.CERTIFICATE_OBTAIN_TYPE); // 证书获取方式字典
			for (HrmsCertificateInfo certificateInfo : list) {
				certificateInfo.setCertificateTypeText(certificateTypeMap.get(certificateInfo.getCertificateType())); // 证书类型文本值
				certificateInfo.setAcceptMethodText(acceptMethodMap.get(certificateInfo.getAcceptMethod())); // 证书获取方式文本值
			}
		}
		return list;
	}

	@Override
	@Transactional(readOnly = false)
	public void incidentAudit(String certificateInfoId,String status) {
		HrmsCertificateInfo hrmsCertificateInfo = new HrmsCertificateInfo();
		hrmsCertificateInfo.setCertificateInfoId(certificateInfoId);
		hrmsCertificateInfo.setApprovalStatus(status);
		hrmsCertificateInfoMapper.updateByPrimaryKeySelective(hrmsCertificateInfo);
	}

}
