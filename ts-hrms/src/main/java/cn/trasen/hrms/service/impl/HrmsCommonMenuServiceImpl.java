package cn.trasen.hrms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsCommonMenuMapper;
import cn.trasen.hrms.model.HrmsCommonMenu;
import cn.trasen.hrms.service.HrmsCommonMenuService;
import tk.mybatis.mapper.entity.Example;


/**    
  * <P> @Description: 设置快捷菜单实现类</p>
  * <P> @Date: 2020年6月29日  下午3:18:41 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
@Service
public class HrmsCommonMenuServiceImpl implements HrmsCommonMenuService {

	@Autowired
	private HrmsCommonMenuMapper hrmsCommonMenuMapper;

	@Override
	public int insert(HrmsCommonMenu entity) {
		//只允许添加一次
		HrmsCommonMenu record = new HrmsCommonMenu();
		record.setUserId(UserInfoHolder.getCurrentUserCode());
		record.setMenuName(entity.getMenuName());
		List<HrmsCommonMenu> beans = hrmsCommonMenuMapper.select(record);
		if(beans != null && beans.size() > 0) {
			return 0;
		}
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setUserId(UserInfoHolder.getCurrentUserCode());
		return hrmsCommonMenuMapper.insert(entity);
	}

	@Override
	public int deleted(String id) {
		return hrmsCommonMenuMapper.deleteByPrimaryKey(id);
	}

	@Override
	public List<HrmsCommonMenu> getList() {
		Example example = new Example(HrmsCommonMenu.class);
		example.createCriteria().andEqualTo("userId",UserInfoHolder.getCurrentUserCode());
		example.setOrderByClause("sequence");
		return hrmsCommonMenuMapper.selectByExample(example);
	}

	@Override
	public int update(HrmsCommonMenu entity) {
		return hrmsCommonMenuMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public List<HrmsCommonMenu> getDataList(Page page, HrmsCommonMenu entity) {
		Example example = new Example(HrmsCommonMenu.class);
		example.createCriteria().andEqualTo("userId",UserInfoHolder.getCurrentUserCode());
		example.setOrderByClause("sequence");
		return hrmsCommonMenuMapper.selectByExampleAndRowBounds(example,page);
	}

	@Override
	public int deleteAll() {
		Example example = new Example(HrmsCommonMenu.class);
		example.createCriteria().andEqualTo("userId",UserInfoHolder.getCurrentUserCode());
		return hrmsCommonMenuMapper.deleteByExample(example);
	}
	
}
