package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.hrms.model.ThpsUser;

/**
 * @ClassName: ThpsUserService
 * @Description: 权限系统用户Service
 * <AUTHOR>
 * @date 2018年5月21日 上午9:34:22
 *
 */
public interface ThpsUserService {

//	int addThpsUser(ThpsUser record);
//
//	/**
//	 * @Title: setUserAndRoleRelation
//	 * @Description: 设置用户和角色关系
//	 * @param userCode
//	 * @param roleCode
//	 * @return
//	 * @date 2019年12月4日 上午9:15:55
//	 * <AUTHOR>
//	 */
//	String setUserAndRoleRelation(String userCode, String roleCode);
//
//	int updateThpsUserInfoForSys(String userId, String userNo, String name);

}
