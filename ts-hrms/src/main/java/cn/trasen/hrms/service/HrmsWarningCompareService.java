package cn.trasen.hrms.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.HrmsWarningCompare;

/**
 * @ClassName HrmsWarningCompareService
 * @Description TODO
 * @date 2021��10��11�� ����5:24:56
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsWarningCompareService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021��10��11�� ����5:24:56
	 * <AUTHOR>
	 */
	Integer save(HrmsWarningCompare record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021��10��11�� ����5:24:56
	 * <AUTHOR>
	 */
	Integer update(HrmsWarningCompare record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021��10��11�� ����5:24:56
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsWarningCompare
	 * @date 2021��10��11�� ����5:24:56
	 * <AUTHOR>
	 */
	HrmsWarningCompare selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsWarningCompare>
	 * @date 2021��10��11�� ����5:24:56
	 * <AUTHOR>
	 */
	DataSet<HrmsWarningCompare> getDataSetList(Page page, HrmsWarningCompare record);
}
