package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.dingtalk.model.DingAttendancePo;
import cn.trasen.hrms.model.HrmsSchedulingGroupingEmp;
import cn.trasen.hrms.model.HrmsSchedulingManage;
import cn.trasen.hrms.model.HrmsSchedulingSort;

/**   
 * @ClassName:  HrmsSchedulingManageService   
 * @Description:人员排班接口
 * @author: WZH
 * @date:   2021年7月15日 上午10:05:04      
 * @Copyright:  
 */
public interface HrmsSchedulingManageService {
	
	

	
	/**   
	 * @Title: batchInsert   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param list
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int batchInsert(List<HrmsSchedulingManage> list,boolean autoScheduling);
	
	public int syncBatchInsert(List<HrmsSchedulingManage> list);
	
	

	public int delete(String id);
	
	

	public List<HrmsSchedulingManage> getDataList(Page page, HrmsSchedulingManage entity);


	

	public int updateByGroupingId(String id);
	
	

	public List<HrmsSchedulingGroupingEmp> getDetailByGroupinId(String id);



	
	/**   
	 * @Title: getDataAllList   
	 * @Description: 查询排班信息
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getDataAllList(HrmsSchedulingManage entity);



	
	/**   
	 * @Title: getEmpSchduing   
	 * @Description: 获取月考勤记录  
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getEmpSchduing(HrmsSchedulingManage entity);



	
	/**   
	 * @Title: getWorkingtablePageList   
	 * @Description: 获取排班数据统计
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingGrouping>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getWorkingtablePageList(Page page, HrmsSchedulingManage entity);



	
	/**   
	 * @Title: getOvertimeWorkingtablePageList   
	 * @Description: 获取节假日排班统计
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<Map<String, Object>> getOvertimeWorkingtablePageList(Page page, HrmsSchedulingManage entity);



	
	/**
	 * @param request    
	 * @Title: getWorkingtableExport   
	 * @Description: 导出加班统计  
	 * @param: @param page
	 * @param: @param entity
	 * @param: @param response      
	 * @return: void      
	 * @throws   
	 */
	public void getWorkingtableExport(Page page, HrmsSchedulingManage entity, HttpServletResponse response, HttpServletRequest request);



	
	/**   
	 * @Title: getOvertimeWorkingtableExport   
	 * @Description: 导出节假日加班统计
	 * @param: @param page
	 * @param: @param entity
	 * @param: @param response      
	 * @return: void      
	 * @throws   
	 */
	public void getOvertimeWorkingtableExport(Page page, HrmsSchedulingManage entity, HttpServletResponse response);



	
	/**   
	 * @Title: getNightClassTablePageList   
	 * @Description:排班晚夜班统计导出
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getNightClassTablePageList(Page page, HrmsSchedulingManage entity);



	
	/**   
	 * @Title: getnightclasstableexport   
	 * @Description: 晚夜班导出
	 * @param: @param page
	 * @param: @param entity
	 * @param: @param response      
	 * @return: void      
	 * @throws   
	 */
	public void getnightclasstableexport(Page page, HrmsSchedulingManage entity, HttpServletResponse response);



	
	/**   
	 * @Title: deleteBySync   
	 * @Description: 删除同步来的数据  
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int deleteBySync(Map<String, String> parMap);

	
	/**
	 * @param parMap    
	 * @Title: deleteBySyncYw   
	 * @Description: 删除护理同步来的数据  
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int deleteBySyncYw(Map<String, String> parMap);

	
	/**   
	 * @Title: getScheduleShow   
	 * @Description: 排班查看
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getScheduleShow(HrmsSchedulingManage entity);

	
	/**   
	 * @Title: getSchedulingByDateAndOrg   
	 * @Description: 根据科室id和日期查询排班信息   
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getSchedulingByDateAndOrg(HrmsSchedulingManage entity);

	
	/**   
	 * @Title: getLastWeekSchedule   
	 * @Description: 查询上周排班  
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getLastWeekSchedule(HrmsSchedulingManage entity);


	
	/**   
	 * @Title: getDataByEmpIdAndDate   
	 * @Description: 根日期和用工id查询排班信息
	 * @param: @param parEmpMap
	 * @param: @return      
	 * @return: List<HrmsSchedulingManage>      
	 * @throws   
	 */
	public List<HrmsSchedulingManage> getDataByEmpIdAndDate(Map<String, String> parEmpMap);

	/**
	 * 
	 * @param entity
	 * @return
	 */
	public List<HrmsSchedulingManage> getRestStatisticsPageList(Page page,HrmsSchedulingManage entity);

	/**
	 * 请假天数 到处
	 * @param page
	 * @param entity
	 * @param response
	 */
	public void getRestStatisticsExport(Page page, HrmsSchedulingManage entity, HttpServletResponse response,HttpServletRequest request);

	/**
	 * 保存排班顺序
	 * @param list
	 */
	public int savesort(List<HrmsSchedulingSort> list);

	/**
	 * 获取排序号
	 * @param orgId
	 * @return
	 */
	public List<HrmsSchedulingSort> getsort(String orgId);

	/** 
	* @Title: getWorkingtableJkyyList 
	* @Description: 经开医院排班统计
	* @param @param page
	* @param @param entity
	* @param @param response
	* @param @param request
	* @param @return    设定文件 
	* @return List<Object>    返回类型 
	* @throws 
	*/
	public  List<DingAttendancePo> getWorkingtableJkyyList(Page page, HrmsSchedulingManage entity, HttpServletResponse response,
			HttpServletRequest request);

	/** 
	* @Title: getWorkingtableJkyyTitle 
	* @Description: 排班报表表头
	* @param @return    设定文件 
	* @return Map<String,Object>    返回类型 
	* @throws 
	*/
	public DingAttendancePo getWorkingtableJkyyTitle(HrmsSchedulingManage entity);

	/** 
	* @Title: getWorkingtableMonthExport 
	* @Description: 排班统计月导出 
	* @param @param page
	* @param @param entity
	* @param @param response
	* @param @param request    设定文件 
	* @return void    返回类型 
	* @throws 
	*/
	public void getWorkingtableMonthExport(Page page, HrmsSchedulingManage entity, HttpServletResponse response,
			HttpServletRequest request);

	/**
	 * 班时数统计
	 * @param entity
	 * @return
	 */
	public Map<String, Object> getScheduleShiftHour(HrmsSchedulingManage entity);
	//班次数统计
	public Map<String, Object> getScheduleQquantity(HrmsSchedulingManage entity);
	//门诊派班表
	public List<TreeMap<String, Object>> getScheduleSendShift(Page page, HrmsSchedulingManage entity);
	//临床科室派班表
	public List<TreeMap<String, Object>> getScheduleClinical(Page page, HrmsSchedulingManage entity);
	//行政后勤科室
	public List<TreeMap<String, Object>> getScheduleLogistics(Page page, HrmsSchedulingManage entity);
	//新排班查看
	public List<Map<String, Object>> getNewScheduleShow(HrmsSchedulingManage entity);

	public List<Map<String, Object>> getleaveData(HrmsSchedulingManage entity);

	public List<Map<String, Object>> getCancelLeaveData(HrmsSchedulingManage entity);

}
