package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.hrms.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.bean.document.Attachment;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.oa.DocumentFeignClient;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsPersonnelIncidentMapper;
import cn.trasen.hrms.enums.EmployeeStatusEnum;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsPersonnelExecutionService;
import cn.trasen.hrms.service.HrmsPersonnelIncidentService;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;
import cn.trasen.hrms.service.HrmsTimekeeperService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.UserPermissionManager;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @Title: HrmsPositionServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 人事事件 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月26日 上午11:11:39
 * @version V1.0
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPersonnelIncidentServiceImpl implements HrmsPersonnelIncidentService {

	@Autowired
	HrmsPersonnelIncidentMapper hrmsPersonnelIncidentMapper;
	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Autowired
	private HrmsPersonnelTransactionService hrmsPersonnelTransactionService;

	@Autowired
	private SystemUserFeignService thpsUserService;

	@Autowired
	private HrmsTimekeeperService hrmsTimekeeperService;

	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationService;
	
	@Autowired
	private HrmsPersonnelExecutionService hrmsPersonnelExecutionService;
	
    @Autowired
    DictItemFeignService dictItemFeignService;
    
    @Autowired
	private DocumentFeignClient documentFeignClient;
    
    @Autowired
	private FileAttachmentFeignService fileAttachmentFeignService;
	
	/**
	 * @Title: validate
	 * @Description: 数据校验
	 * @param entity
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年5月25日 下午2:01:00
	 */
	@Override
	public PlatformResult<String> validate(HrmsPersonnelIncident entity) {
		Example example = new Example(HrmsPersonnelIncident.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return PlatformResult.success();
	}

	/**
	 * @Title: insert
	 * @Description: 新增
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsPersonnelIncident entity) {
		entity.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//添加编制类型
		//第一条退休记录的数据
		HrmsPersonnelIncident record = new HrmsPersonnelIncident();
		record.setEmployeeId(entity.getEmployeeId());
		record.setIncidentCategory("2");
		List<HrmsPersonnelIncident> select = hrmsPersonnelIncidentMapper.select(record);
		
		//取员工档案的数据
		HrmsEmployee findDetailById = hrmsEmployeeService.findDetailById(entity.getEmployeeId());
		
		if (select != null && select.size() > 0) {
			entity.setEstablishmentType(select.get(0).getEstablishmentType());
		}else {
			
			entity.setEstablishmentType(findDetailById.getEstablishmentType());
		}
		int insert = hrmsPersonnelIncidentMapper.insert(entity);
		

		return insert;
	}

	/**
	 * @Title: update
	 * @Description: 更新
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsPersonnelIncident entity) throws Exception {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		if(StringUtils.isNotBlank(entity.getApprovalStatus()) && IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey().equals(entity.getApprovalStatus())){
			dealWith(entity.getPersonnelIncidentId());
		}
		return hrmsPersonnelIncidentMapper.update(entity);
	}



	
	/**
	 * @Title: deleted
	 * @Description: 删除
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsPersonnelIncident personnelIncident = hrmsPersonnelIncidentMapper.selectByPrimaryKey(id);
		if (personnelIncident != null) {
			personnelIncident.setIsDeleted(Contants.IS_DELETED_TURE);
		}

		return hrmsPersonnelIncidentMapper.updateByPrimaryKeySelective(personnelIncident);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取人员列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsPosition>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsPersonnelIncident> getDataList(Page page, HrmsPersonnelIncident entity) {

//		// 数据权限
//		cn.trasen.BootComm.utils.ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
//		String orgRang = thpsUser.getOrgRang();
//		if (!UserInfoHolder.ISADMIN()) { // 是否管理员
//			if (!StringUtil.isEmpty(orgRang)) {// 查询组织范围数据
//				entity.setHtOrgIdList(orgRang);
//			}
//		}
		
		UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getHrmsUserDataPermission();
		
		if(null!=userDataPermissionVo) {
			
			if(CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())){
				
				entity.setOrgIds(userDataPermissionVo.getOrgCodeList());
			}
			if(StringUtils.isNotBlank(userDataPermissionVo.getUserCode())){
				
				entity.setCreateUser(userDataPermissionVo.getUserCode());
			}
			
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPersonnelIncident> list = hrmsPersonnelIncidentMapper.getDataSetList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> incidentResignationDictMap = convertDictMap("incident_resignation_type");   //离职
			Map<String, String> incidentRetirementDictMap = convertDictMap("incident_retirement_type");   //退休
			Map<String, String> incidentDeathDictMap = convertDictMap("incident_death_type");   //死亡
			Map<String, String> lizhiTypeDictMap = convertDictMap("DIMISSION_TYPE");   //离职原因
			Map<String, String> personalIdentityDictMap = convertDictMap("PERSONAL_IDENTITY");   //岗位
			
			for (HrmsPersonnelIncident item : list) {
				item.setPersonnelIncidentId(item.getPersonnelIncidentId());

				if (CommonContants.INCIDENT_TYPE_RESIGNATION.equals(entity.getIncidentCategory())) { // 离职
					item.setIncidentTypeText(incidentResignationDictMap.get(item.getIncidentType()));
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
				} else if (CommonContants.INCIDENT_TYPE_RETIREMENT.equals(entity.getIncidentCategory())) { // 退休
					item.setIncidentTypeText(incidentRetirementDictMap.get(item.getIncidentType()));
//					item.setIncidentTypeText(IncidentRetirementTypeEnum.getValByKey(item.getIncidentType()));
				} else if (CommonContants.INCIDENT_TYPE_DEATH.equals(entity.getIncidentCategory())) { // 死亡
//					item.setIncidentTypeText(IncidentDeathTypeEnum.getValByKey(item.getIncidentType()));
					item.setIncidentTypeText(incidentDeathDictMap.get(item.getIncidentType()));
				}
				if(!StringUtil.isEmpty(item.getCause())) {
					item.setCauseText(lizhiTypeDictMap.get(item.getCause()));
				}
				if(StringUtil.isEmpty(item.getCauseText())) {
					item.setCauseText(item.getCause());
				}
//				else if (CommonContants.INCIDENT_TYPE_POSTPONE.equals(entity.getIncidentCategory())) { // 延聘
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
//				}else if (CommonContants.INCIDENT_TYPE_REENGAGE.equals(entity.getIncidentCategory())) { // 返聘
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
//				}
				item.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(item.getApprovalStatus()));// 审批状态
				if(StringUtils.isNotBlank(item.getPersonalIdentity())){
					item.setPersonalIdentityText(personalIdentityDictMap.get(item.getPersonalIdentity()));
				}
			}
		}
		return list;
	}

	/**
	 * @Title: getList
	 * @Description: 查询人员列表
	 * @param entity
	 * @Return List<HrmsPosition>
	 * <AUTHOR>
	 * @date 2020年4月16日 下午2:56:02
	 */
	@Override
	public List<HrmsPersonnelIncident> getList(HrmsPersonnelIncident entity) {

		// 数据权限
		//cn.trasen.BootComm.utils.ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		//String orgRang = thpsUser.getOrgRang();
		//if (!UserInfoHolder.ISADMIN()) { // 是否管理员
		//	if (!StringUtil.isEmpty(orgRang)) {// 查询组织范围数据
		//		entity.setHtOrgIdList(orgRang);
		//	}
		//}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPersonnelIncident> list = hrmsPersonnelIncidentMapper.getList(entity);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, String> incidentResignationDictMap = convertDictMap("incident_resignation_type");   //离职
		Map<String, String> incidentRetirementDictMap = convertDictMap("incident_retirement_type");   //退休
		Map<String, String> incidentDeathDictMap = convertDictMap("incident_death_type");   //死亡
		Map<String, String> personalIdentityDictMap = convertDictMap("PERSONAL_IDENTITY");   //岗位
		
		if (CollectionUtils.isNotEmpty(list)) {
			for (HrmsPersonnelIncident item : list) {

				if (item.getStartTime() != null) {
					item.setStartTimeExport(sdf.format(item.getStartTime()));
				}
				if (item.getEndTime() != null) {
					item.setEndTimeExport(sdf.format(item.getEndTime()));
				}
				if (item.getIncidentTime() != null) {
					item.setIncidentTimeExport(sdf.format(item.getIncidentTime()));
				}
				if (item.getCreateDate() != null) {
					item.setCreateDateExport(sdf2.format(item.getCreateDate()));
				}

				if (CommonContants.INCIDENT_TYPE_RESIGNATION.equals(entity.getIncidentCategory())) { // 离职
					item.setIncidentTypeText(incidentResignationDictMap.get(item.getIncidentType()));
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
					if(StringUtil.isEmpty(item.getCauseText())) {
						item.setCauseText(item.getCause());
					}
					
				} else if (CommonContants.INCIDENT_TYPE_RETIREMENT.equals(entity.getIncidentCategory())) { // 退休
					item.setIncidentTypeText(incidentRetirementDictMap.get(item.getIncidentType()));
//					item.setIncidentTypeText(IncidentRetirementTypeEnum.getValByKey(item.getIncidentType()));
				} else if (CommonContants.INCIDENT_TYPE_DEATH.equals(entity.getIncidentCategory())) { // 死亡
//					item.setIncidentTypeText(IncidentDeathTypeEnum.getValByKey(item.getIncidentType()));
					item.setIncidentTypeText(incidentDeathDictMap.get(item.getIncidentType()));
				}
//				else if (CommonContants.INCIDENT_TYPE_POSTPONE.equals(entity.getIncidentCategory())) { // 延聘
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
//				}else if (CommonContants.INCIDENT_TYPE_REENGAGE.equals(entity.getIncidentCategory())) { // 返聘
//					item.setIncidentTypeText(IncidentResignationTypeEnum.getValByKey(item.getIncidentType()));
//				}
				item.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(item.getApprovalStatus()));// 审批状态
				if(StringUtils.isNotBlank(item.getPersonalIdentity())){
					item.setPersonalIdentityText(personalIdentityDictMap.get(item.getPersonalIdentity()));
				}
			}
		}
		return list;
	}
	
	

	

	/**
	 * @throws Exception
	 * @Title: incidentAudit
	 * @Description: 审核
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int incidentAudit(String id) throws Exception {
		if (!StringUtil.isEmpty(id)) {
			List<Object> asList = Arrays.asList(id.split(","));
			for (int i = 0; i < asList.size(); i++) {
				dealWith(asList.get(i).toString());
			}
		}
		return 1;
	}
	
	
	private int dealWith(String id)  throws Exception   {
		HrmsPersonnelIncident hrmsPersonnelIncident = hrmsPersonnelIncidentMapper.selectByPrimaryKey(id);
		if (hrmsPersonnelIncident != null) {
			hrmsPersonnelIncident.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey());
		}

		// 更改员工状态
		String employeeId = hrmsPersonnelIncident.getEmployeeId();
		HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
		String incidentCategory = hrmsPersonnelIncident.getIncidentCategory();
		String msg = "";
		if(!CommonContants.INCIDENT_TYPE_RETIREMENT.equals(incidentCategory)) {
			if (CommonContants.INCIDENT_TYPE_RESIGNATION.equals(incidentCategory)) { // 离职
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_4.getKey());
				employee.setIsEnable("0"); //停用
				msg = EmployeeStatusEnum.EMPLOYEE_STATUS_4.getVal();
			} else if (CommonContants.INCIDENT_TYPE_DEATH.equals(incidentCategory)) { // 死亡
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_7.getKey());
				msg = EmployeeStatusEnum.EMPLOYEE_STATUS_7.getVal();
			} else if (CommonContants.INCIDENT_TYPE_POSTPONE.equals(incidentCategory)) { // 延聘
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_1.getKey());
				msg = EmployeeStatusEnum.EMPLOYEE_STATUS_1.getVal();
				employee.setIsRetire("1"); // 设置为退休过
			} else if (CommonContants.INCIDENT_TYPE_REENGAGE.equals(incidentCategory)) { // 返聘
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_6.getKey());
				employee.setIsEnable("1");
				msg = "返聘";
				employee.setIsRetire("1"); // 设置为退休过

				// 修改员工科室和职务
				if (!StringUtil.isEmpty(hrmsPersonnelIncident.getNewOrgId())) {
					employee.setOrgId(hrmsPersonnelIncident.getNewOrgId());
				}
				employee.setPositionId(hrmsPersonnelIncident.getPositionId());
			}
			hrmsEmployeeService.update(employee);

		}else {
			//退休时间小于等于当前时间 马上修改员工状态 否则到期修改
			msg = "退休";
			Date date1 = hrmsPersonnelIncident.getIncidentTime();
			Date date2 = new Date();
			if((date1.compareTo(date2) < 0) || (date1.compareTo(date2) == 0)) {
				employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_8.getKey());
				employee.setIsEnable("0"); //停用
				hrmsEmployeeService.update(employee);
			}else {
				HrmsPersonnelExecution executionEntity = new HrmsPersonnelExecution();
				executionEntity.setEmployeeId(employeeId);
				executionEntity.setNewEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_8.getKey());
				executionEntity.setExecutionTime(DateUtils.getStringDateShort(hrmsPersonnelIncident.getEndTime()));
				executionEntity.setRemark("退休");
				executionEntity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsPersonnelExecutionService.insert(executionEntity);
			}
		}

		Date date = hrmsPersonnelIncident.getIncidentTime();
		if (date == null) {
			date = new Date();
		}

		// 人员异动记录
		HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction(employee.getEmployeeNo(),
				employee.getEmployeeName(), employee.getEmployeeId(), employee.getOrgId(), employee.getOrgName(), null,
				null, DateUtils.getStringDateShort(date), msg, "是", hrmsPersonnelTransactionService.getBatchNumber(),
				null, null, null);
		hpt.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsPersonnelTransactionService.insert(hpt); // 添加人事事件

		return hrmsPersonnelIncidentMapper.updateByPrimaryKeySelective(hrmsPersonnelIncident);
		
	}
	
	//退休人员死亡流转到死亡名单
	@Override
	@Transactional(readOnly = false)
	public int passAway(String id) throws Exception {
		
		HrmsPersonnelIncident hrmsPersonnelIncident = hrmsPersonnelIncidentMapper.selectByPrimaryKey(id);
		
		String employeeId = hrmsPersonnelIncident.getEmployeeId();
		HrmsEmployee employee = hrmsEmployeeService.findDetailById(employeeId);
		employee.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_7.getKey());

		HrmsPersonnelIncident passAway = new HrmsPersonnelIncident();
		passAway.setApprovalStatus(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey());
		passAway.setIsDeleted(Contants.IS_DELETED_FALSE);
		passAway.setCreateUser(UserInfoHolder.getCurrentUserCode());
		passAway.setCreateUserName(UserInfoHolder.getCurrentUserName());
		passAway.setCreateDate(new Date());
		passAway.setIncidentCategory(CommonContants.INCIDENT_TYPE_DEATH);  //死亡事件
		passAway.setPersonnelIncidentId(String.valueOf(IdWork.id.nextId()));
		passAway.setEmployeeId(employeeId);
		passAway.setEmployeeName(employee.getEmployeeName());
		passAway.setEmployeeNo(employee.getEmployeeNo());
		passAway.setEstablishmentType(employee.getEstablishmentType());
		passAway.setPersonalIdentity(employee.getPersonalIdentity());
		passAway.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsPersonnelIncidentMapper.insertSelective(passAway);
		
		hrmsEmployeeService.update(employee);
		
		hrmsPersonnelIncident.setIsDeleted(Contants.IS_DELETED_TURE);
		return hrmsPersonnelIncidentMapper.updateByPrimaryKeySelective(hrmsPersonnelIncident);
	}

	@Override
	public Map<String, Object> incidentCount() {

		List<String> orgIdList = new ArrayList<String>();
		if (UserInfoHolder.ISADMIN()) {
			orgIdList = new ArrayList<String>();
		} else {
			for (String s : UserInfoHolder.getOrgRang().replace("'", "").replace("(", "").replace(")", "").split(",")) {
				if (StringUtils.isBlank(s) == false && (!s.equals("ZZSFYBJY"))) {
					orgIdList.add(s);
				}
			}
		}

		List<String> orgIdNewList = new ArrayList<String>();
		if (orgIdList.size() > 0) {
			List<HrmsOrganizationResp> hrmsOrganizations = hrmsOrganizationService
					.getHrmsOrganizationBeanAndNextList(orgIdList).getObject();

			for (HrmsOrganizationResp hrmsOrganization : hrmsOrganizations) {
				orgIdNewList.add(hrmsOrganization.getOrganizationId());
			}
			if (orgIdNewList.size() < 1) {
				orgIdNewList.add("0");
			}
		}

		orgIdNewList = orgIdNewList.stream().distinct().collect(Collectors.toList());

		
		Map<String, Object> map = new HashMap<>();
		/*
		 * 当前在职人数：work； 本月入职人数：monthJoinkork； 本月离职人数：monthResignation 退休总人数： retirement
		 * 延聘总人数：postpone 返聘总人数；reengage 职称聘任总数：titleAppoint
		 */
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		Integer work = hrmsPersonnelIncidentMapper.getWork(orgIdNewList,ssoOrgCode);
		Integer monthJoinkork = hrmsPersonnelIncidentMapper.getMonthJoinkork(orgIdNewList,ssoOrgCode);
		Integer monthResignation = hrmsPersonnelIncidentMapper.getMonthResignation(orgIdNewList,ssoOrgCode);
		Integer retirement = hrmsPersonnelIncidentMapper.getRetirement(orgIdNewList,ssoOrgCode);
		Integer postpone = hrmsPersonnelIncidentMapper.getPostpone(orgIdNewList,ssoOrgCode);
		Integer reengage = hrmsPersonnelIncidentMapper.getReengage(orgIdNewList,ssoOrgCode);
		Integer titleAppoint = hrmsPersonnelIncidentMapper.getTitleAppoint(orgIdNewList,ssoOrgCode);
		map.put("work", work);
		map.put("monthJoinkork", monthJoinkork);
		map.put("monthResignation", monthResignation);
		map.put("retirement", retirement);
		map.put("postpone", postpone);
		map.put("reengage", reengage);
		map.put("titleAppoint", titleAppoint);
		return map;
	}

	@Override
	public String findReviewDepart(String empNo) {
		return hrmsPersonnelIncidentMapper.findReviewDepart(empNo);
	}

	@Override
	@Transactional(readOnly = false)
	public int stop(HrmsPersonnelIncident entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
//		entity.setEndTime(new Date());
		entity.setStopEvent("1"); // 停止事件
    	if(!StringUtil.isEmpty(entity.getEndDate())) {
    		try {
				entity.setEndTime(DateUtils.getStringToDate(entity.getEndDate()));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			entity.setEndTime(new Date());
		}
		hrmsPersonnelIncidentMapper.updateByPrimaryKeySelective(entity); // 修改结束时间
		/*HrmsEmployee empBean = new HrmsEmployee();
		empBean.setEmployeeId(entity.getEmployeeId());
		empBean.setEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_8.getKey());
		hrmsEmployeeService.updateByPrimaryKeySelective(empBean);*/
		// 修改员工状态改为定时任务去执行
		HrmsPersonnelExecution executionEntity = new HrmsPersonnelExecution();
		executionEntity.setEmployeeId(entity.getEmployeeId());
		executionEntity.setNewEmployeeStatus(EmployeeStatusEnum.EMPLOYEE_STATUS_8.getKey());
		executionEntity.setExecutionTime(entity.getEndDate());
		executionEntity.setRemark("停止返聘");
		executionEntity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsPersonnelExecutionService.insert(executionEntity);
		return 1;
	}

	/* (non-Javadoc)
	 * 查询未审批的数据
	 */
	@Override
	public List<HrmsPersonnelIncident> findNoApproval(HrmsPersonnelIncident entity) {
		HrmsPersonnelIncident record = new HrmsPersonnelIncident();
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		record.setApprovalStatus("1");
		record.setEmployeeId(entity.getEmployeeId());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsPersonnelIncident> beanList = hrmsPersonnelIncidentMapper.select(record);
		return beanList;
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

	@Override
	@Transactional(readOnly = false)
	public void saveHrmsPersonnelIncident(HrmsPersonnelIncident record) {
		
		record.setPersonnelIncidentId(String.valueOf(IdWork.id.nextId()));
		
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(record.getEmployeeNo());
		
		if(StringUtils.isNotBlank(record.getFileId())) {
        	PlatformResult<List<Attachment>> attList = documentFeignClient.selectByIds(record.getFileId(), UserInfoHolder.getToken());
        	if(attList.isSuccess()) {
	        	List<FileAttachmentResp> fileAttachmentRespList = new ArrayList<>();
	        	for (Attachment attachmentReq : attList.getObject()) {
	        		FileAttachmentResp fileAttachmentResp = new FileAttachmentResp();
	        		fileAttachmentResp.setId(attachmentReq.getId());
	        		fileAttachmentResp.setOriginalName(attachmentReq.getOriginalName());
	        		fileAttachmentResp.setModuleName(attachmentReq.getModuleName());
	        		fileAttachmentResp.setFilePath(attachmentReq.getRealPath());
	        		fileAttachmentResp.setFileSize(Long.valueOf(attachmentReq.getFileSize()));
	        		fileAttachmentResp.setRealPath(attachmentReq.getFilePath());
	        		fileAttachmentResp.setFileExtension(attachmentReq.getFileExtension());
	        		fileAttachmentResp.setBusinessId(record.getPersonnelIncidentId());
	        		fileAttachmentResp.setCreateDate(new Date());
	        		fileAttachmentResp.setUpdateUser(hrmsEmployee.getEmployeeNo());
	        		fileAttachmentResp.setUpdateDate(new Date());
	        		fileAttachmentResp.setCreateUser(hrmsEmployee.getEmployeeNo());
	        		fileAttachmentResp.setCreateUserName(hrmsEmployee.getEmployeeName());
	        		fileAttachmentResp.setIsDeleted("N");
	        		fileAttachmentRespList.add(fileAttachmentResp);
				}
	        	fileAttachmentFeignService.saveAttachmentList(JSON.toJSONString(fileAttachmentRespList));
	        }
        }
		
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		record.setCreateUser(hrmsEmployee.getEmployeeNo());
		record.setCreateUserName(hrmsEmployee.getEmployeeName());
		record.setEmployeeId(hrmsEmployee.getEmployeeId());
		record.setEmployeeName(hrmsEmployee.getEmployeeName());
		
		record.setEstablishmentType(hrmsEmployee.getEstablishmentType());
		record.setPersonalIdentity(hrmsEmployee.getPersonalIdentity());
		record.setPositionId(hrmsEmployee.getPositionId());
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsPersonnelIncidentMapper.insert(record);
		
	/*	try {
			//更新员工状态为离职
			hrmsEmployee.setEmployeeStatus("4");
			hrmsEmployeeService.update(hrmsEmployee);
		} catch (Exception e) {
			e.printStackTrace();
		}*/
		
	}

	/**
	 * @Title: 导入退休人员
	 * @Description: 导入退休人员
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Map<String,Object> importRetirement(List<HrmsPersonnelRetirementImportDto> datas){
		log.info("导入的数据datas:" + datas);
		if(CollectionUtil.isEmpty(datas)){
			throw new BusinessException("请至少添加一条数据");
		}
		List<String> employeeStatus = Arrays.asList("1","5","6","9","10","11","12","13");
		Map<String, String> incidentRetirementType = convertDictKeyValMap("incident_retirement_type");  //退休类型
		Map<String, String> personalIdentityName = convertDictMap("personal_identity");  //岗位名称

		List<Map<String,String>> errorList = new ArrayList<>(); //错误信息记录
		HrmsEmployee employee = null;
		HrmsPersonnelIncident entity = null;
		HrmsPersonnelRetirementImportDto dto = null;
		Map<String,String> errData = null;
		int successCount = 0;
		for (int i=0 ; i< datas.size();i++) {
			errData = new HashMap<>();
			dto = datas.get(i);
			if(dto == null || dto.getEmployeeNo().indexOf("填写说明")>-1){
				continue;
			}
			if(StringUtils.isEmpty(dto.getEmployeeNo())){
				errData.put("data","第"+(i+1)+"行的工号为空");
				errorList.add(errData);
				continue;
			}
			if(StringUtils.isEmpty(dto.getIncidentType())){
				errData.put("data","第"+(i+1)+"行的退休类型为空");
				errorList.add(errData);
				continue;
			}else{
				dto.setIncidentType(incidentRetirementType.get(dto.getIncidentType()));
			}
			if(StringUtils.isEmpty(dto.getIncidentTimeStr())){
				errData.put("data","第"+(i+1)+"行的退休日期为空");
				errorList.add(errData);
				continue;
			}
			//根据员工工号获取员工信息
			employee = hrmsEmployeeService.findByEmployeeNo(dto.getEmployeeNo());
			if(employee == null || !employeeStatus.contains(employee.getEmployeeStatus())){
				errData.put("data","工号["+dto.getEmployeeNo()+"]不存在或状态错误");
				errorList.add(errData);
				continue;
			}
			try{
				entity = setImportData(dto,employee);
				entity.setTxmoney(personalIdentityName.get(employee.getPersonalIdentity()));//设置岗位名称
				//验证是否有未审批的数据
				List listBean = findNoApproval(entity);
				if(CollectionUtil.isNotEmpty(listBean)){
					errData.put("data","工号["+dto.getEmployeeNo()+"]存在未审批的事件");
					errorList.add(errData);
					continue;
				}else {
					insert(entity);
					successCount ++ ;
				}
			}catch (Exception e){
				errData.put("data","工号["+dto.getEmployeeNo()+"]导入数据保存异常："+e.getMessage());
				errorList.add(errData);
			}
		}
		Map<String,Object> errorMap = new HashMap<>();
		errorMap.put("total",datas.size());
		errorMap.put("success",successCount);
		errorMap.put("error",(datas.size()  - successCount)+"");
		errorMap.put("data",errorList);
		return errorMap;
	}

	private HrmsPersonnelIncident setImportData(HrmsPersonnelRetirementImportDto dto,HrmsEmployee employee){
		HrmsPersonnelIncident incident = new HrmsPersonnelIncident();
		BeanUtils.copyProperties(dto,incident);
		String id = String.valueOf(IdWork.id.nextId());
		try {
			incident.setIncidentTime(DateUtils.getStringToDate(dto.getIncidentTimeStr()));
		}catch (Exception e){}
		incident.setPersonnelIncidentId(id);
		incident.setEmployeeId(employee.getEmployeeId());
		incident.setEmployeeNo(employee.getEmployeeNo());
		incident.setEmployeeName(employee.getEmployeeName());
		incident.setIncidentCategory("2");
		incident.setApprovalStatus("1");
		incident.setPersonalIdentity(employee.getPersonalIdentity());
		incident.setTxmoney(employee.getPersonalIdentity());
		incident.setEstablishmentType(employee.getEstablishmentType());
		return incident;
	}

	/**
	 * 获取字典名称、值的map对象
	 * @param dictType
	 * @return
	 */
	private Map<String, String> convertDictKeyValMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemName(),d.getItemNameValue());
			}
		}
		return map;
	}
}
