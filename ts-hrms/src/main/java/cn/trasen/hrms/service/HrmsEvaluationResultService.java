package cn.trasen.hrms.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsEvaluationResult;

/**
 * 考核结果接口类
 * <AUTHOR>
 *
 */
public interface HrmsEvaluationResultService {
	
	/**
	 * 添加考核记录
	 * @param entity
	 * @return
	 * zhihua
	 */
	int insert(HrmsEvaluationResult entity);

	/**
	 * 修改考核记录
	 * @param entity
	 * @return
	 * zhihua
	 */
	int update(HrmsEvaluationResult entity);

	/**
	 * 删除考核记录
	 * @param id
	 * @return
	 * zhihua
	 */
	int deleted(String id);

	/**
	 * 查询考核记录分页
	 * @param page
	 * @param entity
	 * @return
	 * zhihua
	 */
	List<HrmsEvaluationResult> getDataList(Page page, HrmsEvaluationResult entity);

	/**
	 * 查询考核记录不分页
	 * @param entity
	 * @return
	 * zhihua
	 */
	List<HrmsEvaluationResult> getList(HrmsEvaluationResult entity);

	/**导入年度考核
	 * @param list
	 * @return
	 * zhihua
	 */
	PlatformResult<Map<String, Object>> excelImportAssess(List<HrmsEvaluationResult> list);
	
}
