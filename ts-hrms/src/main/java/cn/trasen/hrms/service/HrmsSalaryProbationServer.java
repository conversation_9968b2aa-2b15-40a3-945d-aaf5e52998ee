package cn.trasen.hrms.service;

import java.math.BigDecimal;
import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsSalaryProbation;

/**   
 * @ClassName:  HrmsSalaryProbationServer   
 * @Description:试用期员工工资设置接口  
 * @author: WZH
 * @date:   2021年11月2日 上午10:23:30      
 * @Copyright:  
 */
public  interface HrmsSalaryProbationServer {

	public int insert(HrmsSalaryProbation entity);

	public int update(HrmsSalaryProbation entity);

	public int delete(String id); 

	public List<HrmsSalaryProbation> getDataList(Page page, HrmsSalaryProbation entity);

	
	/**   
	 * @Title: getSalary   
	 * @Description: 根据员工id获取多少岗位工资 
	 * @param: @param employeeId
	 * @param: @return      
	 * @return: BigDecimal      
	 * @throws   
	 */
	public BigDecimal getSalary(String employeeId);
}
