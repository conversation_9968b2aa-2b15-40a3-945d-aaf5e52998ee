package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsSchedulingGrouping;

/**   
 * @ClassName:  HrmsSchedulingGroupingService   
 * @Description:考勤组实现类   
 * @author: WZH
 * @date:   2021年7月14日 下午3:55:49      
 * @Copyright:  
 */
public interface HrmsSchedulingGroupingService {

	
	/**   
	 * @Title: insert   
	 * @Description: 添加考勤组
	 * @param: @param entity
	 * @param: @return      
	 * @return: boolean      
	 * @throws   
	 */
	void insert(HrmsSchedulingGrouping entity);

	
	/**   
	 * @Title: update   
	 * @Description: 修改考勤组   
	 * @param: @param entity
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	void update(HrmsSchedulingGrouping entity);

	
	/**   
	 * @Title: deleteById   
	 * @Description: 删除考勤组  
	 * @param: @param id
	 * @param: @return      
	 * @return: boolean      
	 * @throws   
	 */
	boolean deleteById(String id);

	
	/**   
	 * @Title: getDataList   
	 * @Description:考勤组列表  
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingGrouping>      
	 * @throws   
	 */
	List<HrmsSchedulingGrouping> getDataList(Page page, HrmsSchedulingGrouping entity);

	
	/**   
	 * @Title: getList   
	 * @Description:查询所有考勤组
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingGrouping>      
	 * @throws   
	 */
	List<HrmsSchedulingGrouping> getList(HrmsSchedulingGrouping entity);

	
	/**   
	 * @Title: findDetailById   
	 * @Description: 查询考勤组所有人员 
	 * @param: @param id
	 * @param: @return      
	 * @return: HrmsSchedulingGrouping      
	 * @throws   
	 */
	HrmsSchedulingGrouping findDetailById(String id);


	
	/**   
	 * @Title: getPageAllList   
	 * @Description: 查询科室所有人员信息
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingGrouping>      
	 * @throws   
	 */
	List<HrmsSchedulingGrouping> getPageAllList(Page page, HrmsSchedulingGrouping entity);


	List<HrmsSchedulingGrouping> getDataListByGroup(HrmsSchedulingGrouping entity);

}
