package cn.trasen.hrms.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.HrmsResearchTopicUrge;

/**
 * @ClassName HrmsResearchTopicUrgeService
 * @Description TODO
 * @date 2023��11��14�� ����10:23:33
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsResearchTopicUrgeService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��11��14�� ����10:23:33
	 * <AUTHOR>
	 */
	Integer save(HrmsResearchTopicUrge record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��11��14�� ����10:23:33
	 * <AUTHOR>
	 */
	Integer update(HrmsResearchTopicUrge record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��11��14�� ����10:23:33
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsResearchTopicUrge
	 * @date 2023��11��14�� ����10:23:33
	 * <AUTHOR>
	 */
	HrmsResearchTopicUrge selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsResearchTopicUrge>
	 * @date 2023��11��14�� ����10:23:33
	 * <AUTHOR>
	 */
	DataSet<HrmsResearchTopicUrge> getDataSetList(Page page, HrmsResearchTopicUrge record);
	
}
