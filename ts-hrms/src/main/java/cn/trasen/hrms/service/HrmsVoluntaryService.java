package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsVoluntary;

public interface HrmsVoluntaryService {

	int deleted(String id);

	List<HrmsVoluntary> getDataList(Page page, HrmsVoluntary entity);

	String excelImport(List<HrmsVoluntary> list);

	List<HrmsVoluntary> getDataCountList(Page page, HrmsVoluntary entity);

	int insert(HrmsVoluntary entity);

	int update(HrmsVoluntary entity);

	


}
