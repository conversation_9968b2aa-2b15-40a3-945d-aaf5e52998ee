package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.hrms.model.HrmsSchedulingGrouping;
import cn.trasen.hrms.model.HrmsSchedulingGroupingEmp;

/**   
 * @ClassName:  HrmsSchedulingGroupingEmpService   
 * @Description:考勤组员工接口   
 * @author: WZH
 * @date:   2021年7月15日 上午10:05:04      
 * @Copyright:  
 */
public interface HrmsSchedulingGroupingEmpService {
	
	
	/**   
	 * @Title: batchInsert   
	 * @Description:批量添加员工信息
	 * @param: @param entity
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int batchInsert(List<HrmsSchedulingGroupingEmp> list);
	
	
	/**   
	 * @Title: delete   
	 * @Description: 根据外键id删除数据 
	 * @param: @param id
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int delete(String id);
	
	
	/**   
	 * @Title: getDataList   
	 * @Description: 根据外键获取所有人员数据  
	 * @param: @param id
	 * @param: @return      
	 * @return: List<HrmsSchedulingGroupingEmp>      
	 * @throws   
	 */
	public List<HrmsSchedulingGroupingEmp> getDataList(String id);


	
	/**   
	 * @Title: updateByGroupingId   
	 * @Description: 根据外键修改id
	 * @param: @param id
	 * @param: @return      
	 * @return: int      
	 * @throws   
	 */
	public int updateByGroupingId(String id);
	
	
	/**   
	 * @Title: getDetailByGroupinId   
	 * @Description: 根据外键获取员工信息
	 * @param: @return      
	 * @return: List<HrmsSchedulingGroupingEmp>      
	 * @throws   
	 */
	public List<HrmsSchedulingGroupingEmp> getDetailByGroupinId(String id);


	
	/**   
	 * @Title: getPageAllList   
	 * @Description: 获取科室所有人员
	 * @param: @param page
	 * @param: @param entity
	 * @param: @return      
	 * @return: List<HrmsSchedulingGroupingEmp>      
	 * @throws   
	 */
	public List<HrmsSchedulingGroupingEmp> getPageAllList( HrmsSchedulingGrouping entity);


	
	/**   
	 * @Title: updateGroup   
	 * @Description: 改变考勤组
	 * @param: @param entity
	 * @param: @return      
	 * @return: boolean      
	 * @throws   
	 */
	public void updateGroup(HrmsSchedulingGroupingEmp entity);


	
}
