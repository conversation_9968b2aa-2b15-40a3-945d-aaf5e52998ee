package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsRecruitmentDemand;

/**   
 * @Title: HrmsRecruitmentDemandService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 招聘需求 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月21日 下午3:24:13 
 * @version V1.0   
 */
public interface HrmsRecruitmentDemandService {

	/**
	 * @Title: insert
	 * @Description: 新增招聘需求
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 */
	int insert(HrmsRecruitmentDemand entity);

	/**
	 * @Title: update
	 * @Description: 更新招聘需求
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int update(HrmsRecruitmentDemand entity);

	/**
	 * @Title: deleted
	 * @Description: 删除招聘需求
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	int deleted(String id);

	/**
	 * @Title: getDataList
	 * @Description: 查询招聘需求列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsRecruitmentDemand>
	 * <AUTHOR>
	 */
	List<HrmsRecruitmentDemand> getDataList(Page page, HrmsRecruitmentDemand entity);

	/**审核接口
	 * @param recruitmentDemandId
	 * @return
	 */
	int update(String recruitmentDemandId);

}