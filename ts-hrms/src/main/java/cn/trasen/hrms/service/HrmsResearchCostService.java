package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.model.HrmsResearchCost;

/**
 * @ClassName HrmsResearchCostService
 * @Description TODO
 * @date 2021��11��6�� ����3:44:54
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsResearchCostService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2021��11��6�� ����3:44:54
	 * <AUTHOR>
	 */
	Integer save(HrmsResearchCost record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2021��11��6�� ����3:44:54
	 * <AUTHOR>
	 */
	Integer update(HrmsResearchCost record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2021��11��6�� ����3:44:54
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsResearchCost
	 * @date 2021��11��6�� ����3:44:54
	 * <AUTHOR>
	 */
	HrmsResearchCost selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsResearchCost>
	 * @date 2021��11��6�� ����3:44:54
	 * <AUTHOR>
	 */
	DataSet<HrmsResearchCost> getDataSetList(Page page, HrmsResearchCost record);

	/**
	 * 
	 * @Title: saveHrmsResearchCostList
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param list 参数
	 * @return void 返回类型
	 * 2021年11月10日
	 * ADMIN
	 * @throws
	 */
	void saveHrmsResearchCostList(List<HrmsResearchCost> list,String delIds);

	/**
	 * 
	 * @Title: selectHrmsResearchCostList
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param record
	 * @param @return 参数
	 * @return List<HrmsResearchCost> 返回类型
	 * 2021年11月10日
	 * ADMIN
	 * @throws
	 */
	List<HrmsResearchCost> selectHrmsResearchCostList(HrmsResearchCost record);
}
