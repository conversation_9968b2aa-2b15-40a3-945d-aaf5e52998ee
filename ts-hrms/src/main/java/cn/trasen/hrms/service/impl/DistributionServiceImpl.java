package cn.trasen.hrms.service.impl;

import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.bean.TreeListResp;
import cn.trasen.hrms.dao.DistributionServiceMapper;
import cn.trasen.hrms.dao.HrmsOrganizationMapper;
import cn.trasen.hrms.service.DistributionService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DistributionServiceImpl implements DistributionService {

    @Autowired
    private DistributionServiceMapper distributionServiceMapper;
    @Autowired
    HrmsOrganizationMapper hrmsOrganizationMapper;

    @Override
    public List<TreeListResp> orgDistribution(List<String> orgIds) {
        //根据当前登录账号机构编码过滤查询数据
        String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
        List<TreeListResp> respsList = distributionServiceMapper.orgDistribution(orgIds, ssoOrgCode);
        Map<String, List<TreeListResp>> parentMap = respsList.stream().filter(treeListResp -> Objects.nonNull(treeListResp) && Objects.nonNull(treeListResp.getTreeIds()))
                .collect(Collectors.groupingBy(TreeListResp::getTreeIds));

        if (CollectionUtils.isNotEmpty(respsList)){
            int total = hrmsOrganizationMapper.getTotalNum(orgIds,ssoOrgCode);
            for (TreeListResp resp: respsList){
                String treeIds = resp.getTreeIds();
                resp.setNums(String.valueOf(total));
                //树节点
                if (StringUtils.isNotEmpty(treeIds)){
                    List<String> numberList = Arrays.stream(treeIds.split(","))
                            .map(String::trim)
                            .collect(Collectors.toList());
                    List<String> orgLists = hrmsOrganizationMapper.getOrgName(numberList);
                    if (CollectionUtils.isNotEmpty(orgLists)){
                        resp.setOrgNames(String.join(",",orgLists));
                    }
                    //统计个数
                    for (int i = 1; i < numberList.size(); i++){
                        List<TreeListResp> treeListResps = new ArrayList<>();
                        for (Map.Entry<String, List<TreeListResp>> entry : parentMap.entrySet()) {
                            if (entry.getKey().contains(numberList.get(i))){
                               treeListResps.addAll(entry.getValue());
                            }
                        }
                        List<TreeListResp> collect = treeListResps.stream().distinct().collect(Collectors.toList());
                        long toSum = collect.stream().mapToLong(TreeListResp::getJobtitleNum).sum();
                        resp.setNums(resp.getNums() +","+ toSum);
                    }
                }
            }
        }
        respsList.sort(Comparator.comparing(TreeListResp::getOrgNames, Comparator.nullsLast(Comparator.naturalOrder())));
        return respsList;
    }

    @Override
    public List<TreeListResp> getOrgEmployeeList(List<String> orgIds,List<String> employeeStatus,String employeeName) {
        //根据当前登录账号机构编码过滤查询数据
        String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
        List<TreeListResp> respsList = distributionServiceMapper.getOrgEmployeeList(orgIds,employeeStatus,employeeName, ssoOrgCode);
       if(CollectionUtils.isNotEmpty(respsList)){
           int totalNum = 0;
           for(int i=0;i<respsList.size();i++){
               respsList.get(i).setNo(i+1);
               totalNum += respsList.get(i).getEmployeeNum();
           }
           //汇总数据
           TreeListResp totalData = new TreeListResp();
           totalData.setEmployeeNum(totalNum);
           totalData.setName("合计");
           respsList.add(totalData);
       }
        return respsList;
    }

	@Override
	public List<Map<String, String>> getCoefficient(List<String> list) {
        //根据当前登录账号机构编码过滤查询数据
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return distributionServiceMapper.getCoefficient(list,ssoOrgCode);
	}

}
