package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsResumeStorehouse;

/**   
 * @Title: HrmsResumeStorehouseService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 简历库 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年5月22日 上午9:31:33 
 * @version V1.0   
 */
public interface HrmsResumeStorehouseService {

	/**
	 * @Title: insert
	 * @Description: 新增简历库
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 */
	int insert(HrmsResumeStorehouse entity);

	/**
	 * @Title: update
	 * @Description: 更新简历库
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int update(HrmsResumeStorehouse entity);
	
	/**
	 * @Title: updateById
	 * @Description: 根据ID更新简历库(无事务,供其他Service调用)
	 * @param id
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月25日 下午2:10:40
	 */
	int updateById(String id);

	/**
	 * @Title: deleted
	 * @Description: 删除简历库
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	int deleted(String id);

	/**
	 * @Title: getDataList
	 * @Description: 查询简历库列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsResumeStorehouse>
	 * <AUTHOR>
	 */
	List<HrmsResumeStorehouse> getDataList(Page page, HrmsResumeStorehouse entity);
	
	/**
	 * @Title: selectByPrimaryKey
	 * @Description: 根据主键ID查询
	 * @param id
	 * @Return HrmsResumeStorehouse
	 * <AUTHOR>
	 * @date 2020年5月22日 上午11:46:28
	 */
	HrmsResumeStorehouse selectByPrimaryKey(String id);

}
