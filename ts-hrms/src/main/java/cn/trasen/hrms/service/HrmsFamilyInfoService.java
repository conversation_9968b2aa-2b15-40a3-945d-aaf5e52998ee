package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsFamilyInfo;

/**   
 * @Title: HrmsFamilyInfoService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 员工家庭信息 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月12日 下午5:39:08 
 * @version V1.0   
 */
public interface HrmsFamilyInfoService {
	
	/**
	 * @Title: insert
	 * @Description: 新增家庭信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int insert(HrmsFamilyInfo entity);

	/**
	 * @Title: update
	 * @Description: 更新家庭信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	int update(HrmsFamilyInfo entity);

	/**
	 * @Title: deleted
	 * @Description: 删除家庭信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	int deleted(String id);

	/**
	 * @Title: getDataList
	 * @Description: 查询家庭信息列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsFamilyInfo>
	 * <AUTHOR>
	 */
	List<HrmsFamilyInfo> getDataList(Page page, HrmsFamilyInfo entity);
	
	/**
	 * @Title: getList
	 * @Description: 查询家庭信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsFamilyInfo>
	 * <AUTHOR>
	 * @date 2020年4月21日 下午2:40:30
	 */
	List<HrmsFamilyInfo> getList(HrmsFamilyInfo entity);
	
}
