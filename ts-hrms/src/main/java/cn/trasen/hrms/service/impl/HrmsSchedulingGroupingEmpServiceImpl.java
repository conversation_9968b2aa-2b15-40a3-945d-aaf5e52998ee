package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSchedulingGroupingEmpMapper;
import cn.trasen.hrms.model.HrmsSchedulingGrouping;
import cn.trasen.hrms.model.HrmsSchedulingGroupingEmp;
import cn.trasen.hrms.service.HrmsSchedulingGroupingEmpService;
import cn.trasen.hrms.service.HrmsSchedulingGroupingService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName: HrmsSchedulingGroupingEmpServiceImpl
 * @Description:TODO(描述这个类的作用)
 * @author: WZH
 * @date: 2021年7月15日 上午10:09:53
 * @Copyright:
 */
@Service
public class HrmsSchedulingGroupingEmpServiceImpl implements HrmsSchedulingGroupingEmpService {

	@Autowired
	private HrmsSchedulingGroupingEmpMapper hrmsSchedulingGroupingEmpMapper;
	
	@Autowired
	private HrmsSchedulingGroupingService hrmsSchedulingGroupingService;
	
	@Override
	public int batchInsert(List<HrmsSchedulingGroupingEmp> list) {
		int count = 0;
		if (list.size() > 500) {
			int toIndex = 500;
			int listSize = list.size();
			for (int i = 0; i < list.size(); i += 500) {
				// 作用为toIndex最后没有500条数据则剩余几条list中就装几条
				if (i + 500 > listSize) {
					toIndex = listSize - i;
				}
				List<HrmsSchedulingGroupingEmp> sepaList = list.subList(i, i + toIndex);
				count += hrmsSchedulingGroupingEmpMapper.batchInsert(sepaList);
			}
		} else {
			count = hrmsSchedulingGroupingEmpMapper.batchInsert(list);
		}
		return count;
	}

	@Override
	public int delete(String id) {
		HrmsSchedulingGroupingEmp record = new HrmsSchedulingGroupingEmp();
		record.setFrequencyGroupingId(id);
		return hrmsSchedulingGroupingEmpMapper.delete(record);
	}

	@Override
	public List<HrmsSchedulingGroupingEmp> getDataList(String id) {
		HrmsSchedulingGroupingEmp record = new HrmsSchedulingGroupingEmp();
		record.setFrequencyGroupingId(id);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingEmpMapper.select(record);
	}
	
	//根据外键修改数据
	@Override
	public int updateByGroupingId(String id) {
		HrmsSchedulingGroupingEmp record = new HrmsSchedulingGroupingEmp();
		record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		record.setUpdateDate(new Date());
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		Example example = new Example(HrmsSchedulingGroupingEmp.class);
		example.createCriteria().andEqualTo("frequencyGroupingId", id);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingEmpMapper.updateByExampleSelective(record, example);
	}

	@Override
	public List<HrmsSchedulingGroupingEmp> getDetailByGroupinId(String id) {
		HrmsSchedulingGroupingEmp record = new HrmsSchedulingGroupingEmp();
		record.setFrequencyGroupingId(id);
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsSchedulingGroupingEmpMapper.select(record);
	}

	@Override
	public List<HrmsSchedulingGroupingEmp> getPageAllList(HrmsSchedulingGrouping entity) {
		HrmsSchedulingGroupingEmp recode = new HrmsSchedulingGroupingEmp();
		if(StringUtil.isEmpty(entity.getOrgId())) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		}
		
		//加条件 排班 只查询在当前科室的人 
		
		//如果是查看 就要查询时间段内在本科室有排班数据的人员
		
		//根据时间查询某段时间在科室的人员
		if(!StringUtil.isEmpty(entity.getSearchEndDate()) && !StringUtil.isEmpty(entity.getSearchStartDate())) {
			recode.setSearchStartDate(entity.getSearchStartDate());
			recode.setSearchEndDate(entity.getSearchEndDate());
		}else {
			//获取本月第一天
			//获取本月最后一天
			recode.setSearchStartDate(DateUtils.getFirstDay(new Date()));
			recode.setSearchEndDate(DateUtils.getStringDateShort(new Date()));
		}
		recode.setOrgId(entity.getOrgId());
		recode.setMovementType(entity.getMovementType());
		List<HrmsSchedulingGroupingEmp> returnList = new ArrayList<HrmsSchedulingGroupingEmp>();
		//根据当前登录账号机构编码过滤查询数据
		recode.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//科室所有人员 (包含曾经在这个科室打过考勤的人)
		List<HrmsSchedulingGroupingEmp> allList = hrmsSchedulingGroupingEmpMapper.getPageAllList(recode);
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		//科室已设置分组人员
		List<HrmsSchedulingGrouping> dataList = hrmsSchedulingGroupingService.getDataListByGroup(entity);
		//判断分组集合
		if(dataList != null && dataList.size() > 0) {
			for (HrmsSchedulingGrouping hrmsSchedulingGrouping : dataList) {
				//遍历每一个分组
				List<HrmsSchedulingGroupingEmp> dataEmpList = hrmsSchedulingGrouping.getSchedulingGroupingEmp();
				if (dataEmpList != null && dataEmpList.size() > 0) {
					//遍历单个分组
					for (HrmsSchedulingGroupingEmp hrmsSchedulingGroupingEmp : dataEmpList) {
						hrmsSchedulingGroupingEmp.setFrequencyGroupingId(hrmsSchedulingGrouping.getFrequencyGroupingId());
						hrmsSchedulingGroupingEmp.setFrequencyGroupingName(hrmsSchedulingGrouping.getFrequencyGroupingName());
						returnList.add(hrmsSchedulingGroupingEmp);
					}
				}
			}
		}
		if(allList != null && allList.size() > 0) {
			//遍历所有人员
			for (HrmsSchedulingGroupingEmp hrmsSchedulingGroupingEmp : allList) {
				String employeeId = hrmsSchedulingGroupingEmp.getEmployeeId();
				boolean isNo = returnList.stream().anyMatch(m -> employeeId.equals(m.getEmployeeId()));
				if (!isNo) {
					returnList.add(0, hrmsSchedulingGroupingEmp);
				}
			}
		}
		
		
		returnList = returnList.stream().distinct().collect(Collectors.toList());
		Collections.sort(returnList, Comparator.comparing(HrmsSchedulingGroupingEmp::getSort));
	/*	if(returnList != null && returnList.size() > 0) {
			for(int i=0; i < returnList.size(); i++) {
				if(null == returnList.get(i).getSort()) {
					returnList.get(i).setSort(99999);
				}
			}
		
		}*/
		return returnList;
	}

	/**   
	 * <p>Title: updateGroup</p>   
	 * <p>Description: 拖拽修改员工组</p>   
	 * @param entity
	 * @return   
	 */
	@Override
	@Transactional
	public void updateGroup(HrmsSchedulingGroupingEmp entity) {
		
		if(StringUtil.isEmpty(entity.getEmployeeId())) {
			throw new BusinessException("不能操作空数据");
		}
		
		//先删除再本考勤组的数据 
		if(!StringUtil.isEmpty(entity.getOldGroupingId())) {
			entity.setFrequencyGroupingId(entity.getOldGroupingId());
		}
		hrmsSchedulingGroupingEmpMapper.delete(entity);
		//重新保存考勤组数据
		if(!StringUtil.isEmpty(entity.getNewGroupingId())) {
			entity.setFrequencyGroupingEmpId(IdUtil.getId());
			entity.setFrequencyGroupingId(entity.getNewGroupingId());
			entity.setIsDeleted(Contants.IS_DELETED_FALSE);
			entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
			entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
			entity.setCreateDate(new Date());
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			hrmsSchedulingGroupingEmpMapper.insertSelective(entity);
		}
	}
	
}
