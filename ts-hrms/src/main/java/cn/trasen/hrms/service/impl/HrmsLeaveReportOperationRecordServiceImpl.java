package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsLeaveReportOperationRecordMapper;
import cn.trasen.hrms.model.HrmsLeaveReportOperationRecord;
import cn.trasen.hrms.service.HrmsLeaveReportOperationRecordService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsLeaveReportOperationRecordServiceImpl
 * @Description TODO
 * @date 2021��11��3�� ����3:00:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsLeaveReportOperationRecordServiceImpl implements HrmsLeaveReportOperationRecordService {

	@Autowired
	private HrmsLeaveReportOperationRecordMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsLeaveReportOperationRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsLeaveReportOperationRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsLeaveReportOperationRecord record = new HrmsLeaveReportOperationRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsLeaveReportOperationRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsLeaveReportOperationRecord> getDataSetList(Page page, HrmsLeaveReportOperationRecord record) {
		
		if(StringUtils.isNotBlank(record.getLeaveMonth()) && StringUtils.isNotBlank(record.getEmployeeCode())) {
			
			Example example = new Example(HrmsLeaveReportOperationRecord.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			criteria.andEqualTo("employeeCode",record.getEmployeeCode());
			
			criteria.andEqualTo("leaveMonth",record.getLeaveMonth());

			
			List<HrmsLeaveReportOperationRecord> records = mapper.selectByExampleAndRowBounds(example, page);
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
		}
		
		return null;
	}
}
