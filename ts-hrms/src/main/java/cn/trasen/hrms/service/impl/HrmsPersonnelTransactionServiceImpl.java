package cn.trasen.hrms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.dao.HrmsPersonnelTransactionMapper;
import cn.trasen.hrms.model.HrmsPersonnelTransaction;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;
import cn.trasen.hrms.utils.UserPermissionManager;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsPersonnelTransactionServiceImpl implements HrmsPersonnelTransactionService {
	
	@Autowired
	private HrmsPersonnelTransactionMapper hrmsPersonnelTransactionMapper;
	
	@Override
	public int batchInsert(List<HrmsPersonnelTransaction> list) {
		
		return hrmsPersonnelTransactionMapper.batchInsert(list);
	}

	@Override
	public int insert(HrmsPersonnelTransaction bean) {
//    	if (bean.getId() == null) {
//    		bean.setId(IdWork.id.nextId());
//    	}
		return hrmsPersonnelTransactionMapper.insertSelective(bean);
	}

	@Override
	public List<HrmsPersonnelTransaction> getDataList(Page page, HrmsPersonnelTransaction entity) {
		
		UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getHrmsUserDataPermission();
		
		if(null!=userDataPermissionVo) {
			
			if(CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())){
				
				entity.setOrgIds(userDataPermissionVo.getOrgCodeList());
			}
			if(StringUtils.isNotBlank(userDataPermissionVo.getUserCode())){
				
				entity.setCreateUser(userDataPermissionVo.getUserCode());
			}
			
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsPersonnelTransactionMapper.getDataList(page,entity);
	}
	
	@Override
	public List<HrmsPersonnelTransaction> getList(HrmsPersonnelTransaction entity) {
		return hrmsPersonnelTransactionMapper.getList(entity);
	}

	@Override
	public List<HrmsPersonnelTransaction> getCycleList(HrmsPersonnelTransaction personnelTransaction) {
		return hrmsPersonnelTransactionMapper.getCycleList(personnelTransaction);
	}


	//	获取通知单编号
	@Override
	public String getBatchNumber() {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
		String ym = formatter.format(new Date());
		String newYm = hrmsPersonnelTransactionMapper.getBatchNumber(ym);
		if(StringUtil.isEmpty(newYm)) {
			newYm = ym + "01";
		}else {
			String substring = newYm.substring(6);
			Integer re = Integer.valueOf(substring);
			if(re < 10) {
				newYm = ym + "0" + (re +1); 
			}else {
				newYm  = ym + (re + 1);
			}
		}
		return newYm;
	}

	@Override
	public List<String> loadChangeSelect() {
		
		return hrmsPersonnelTransactionMapper.loadChangeSelect();
	}
	
	//根据时间和id删除数据
	@Override
	public int deleteByEmployeeNoAndDate(String employeeNo, String date) {
		return hrmsPersonnelTransactionMapper.deleteByEmployeeNoAndDate(employeeNo, date);
	}

}
