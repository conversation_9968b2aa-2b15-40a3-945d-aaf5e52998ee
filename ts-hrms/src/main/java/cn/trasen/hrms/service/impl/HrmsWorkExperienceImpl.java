package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsWorkExperienceMapper;
import cn.trasen.hrms.model.HrmsWorkExperience;
import cn.trasen.hrms.service.HrmsWorkExperienceService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsWorkExperienceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工工作经历 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月13日 上午9:50:15 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsWorkExperienceImpl implements HrmsWorkExperienceService {

	@Autowired
	HrmsWorkExperienceMapper hrmsWorkExperienceMapper;

	/**
	 * @Title: insert
	 * @Description: 新增工作经历
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsWorkExperience entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsWorkExperienceMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新工作经历
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsWorkExperience entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsWorkExperienceMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除工作经历
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsWorkExperience workExperience = hrmsWorkExperienceMapper.selectByPrimaryKey(id);
		if (workExperience != null) {
			workExperience.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsWorkExperienceMapper.updateByPrimaryKeySelective(workExperience);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取工作经历列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsWorkExperience>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsWorkExperience> getDataList(Page page, HrmsWorkExperience entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsWorkExperience.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());

		return hrmsWorkExperienceMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询工作经历列表(不分页)
	 * @param entity
	 * @Return List<HrmsWorkExperience>
	 * <AUTHOR>
	 * @date 2020年4月21日 上午10:56:56
	 */
	@Override
	public List<HrmsWorkExperience> getList(HrmsWorkExperience entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsWorkExperience.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		
		if (StringUtils.isNotBlank(entity.getId())) {
			example.and().andEqualTo("id", entity.getId());
		}
		return hrmsWorkExperienceMapper.selectByExample(example);
	}

}
