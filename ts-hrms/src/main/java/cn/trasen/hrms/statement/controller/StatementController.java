package cn.trasen.hrms.statement.controller;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.statement.service.StatementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 党委班子控制器
 * <AUTHOR>
 *
 */

@Slf4j
@Api(tags = "报表Controller")
@RestController
public class StatementController {
	
	@Autowired
	StatementService statementService;

	@ApiOperation(value = "人员类别表", notes = "人员类别表")
	@GetMapping(value = "/statement/getRylbtj")
	public PlatformResult<Object> getRylbtj() {
		try {
			Map<String,Object> map = statementService.getRylbtj();
			return PlatformResult.success(map);
			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	@ApiOperation(value = "科室类别统计", notes = "科室类别统计")
	@GetMapping(value = "/statement/getKslbtj")
	public PlatformResult<Object> getKslbtj() {
		try {
			LinkedHashMap<String,Object> val = new LinkedHashMap<String,Object>();
			List<LinkedHashMap<String,String>> data = statementService.getKslbtj();
			val.put("data", data);
			LinkedHashMap<String,Object> _title = new LinkedHashMap<String,Object>();
			List<String> _st1 = new ArrayList<String>();
			_st1.add("高级");
			_st1.add("副高级");
			_st1.add("中级");
			_st1.add("初级");
			_st1.add("其他/无");
			_title.put("当月科室人员职称结构", _st1);
			
			List<String> _st2 = new ArrayList<String>();
			_st2.add("博士");
			_st2.add("研究生");
			_st2.add("本科");
			_st2.add("大专");
			_st2.add("大专以下/无");
			_title.put("当月科室人员学历结构", _st2);
			
			List<String> _st3 = new ArrayList<String>();
			_st3.add("25岁以下");
			_st3.add("25-35");
			_st3.add("36-45");
			_st3.add("46-55");
			_st3.add("55以上");
			_title.put("当月科室人员年龄结构", _st3);
			
			
			List<String> _st4 = new ArrayList<String>();
			_st4.add("院长级");
			_st4.add("副院长级");
			_st4.add("总监级");
			_st4.add("副经理级");
			_st4.add("院长助理级");
			
			_st4.add("主任级");
			_st4.add("副主任级");
			_st4.add("负责人级");
			_st4.add("主管级");
			_st4.add("医师高级");
			_st4.add("医师副高级");
			_st4.add("医师中级");
			_st4.add("医师初级");
			_st4.add("助理医师");
			_st4.add("护士长级");
			_st4.add("护理中级");
			_st4.add("护理师级");
			_st4.add("护理士级");
			_st4.add("助理护士级");
			_st4.add("药师中级");
			_st4.add("药剂师级");
			_st4.add("药剂士级");
			_st4.add("医技中级");
			_st4.add("医技师级");
			_st4.add("医技士级");
			_st4.add("技术员级");
			_st4.add("文员级");
			_st4.add("专员级");
			_title.put("当月科室人员职级结构", _st4);
			val.put("title", _title);
			return PlatformResult.success(val);
			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
}
