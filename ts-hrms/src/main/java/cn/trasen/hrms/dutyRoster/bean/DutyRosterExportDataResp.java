package cn.trasen.hrms.dutyRoster.bean;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 值班数据导出bean
 */
@Data
public class DutyRosterExportDataResp {
	
	 @ApiModelProperty(value = "值班日期")
	 private String reportDate;
	 @ApiModelProperty(value = "星期几")
	 private String weekdayame;
	 @ApiModelProperty(value = "值班领导")
	 private String zbld;
	 @ApiModelProperty(value = "行政值班")
	 private String xzzb;
	 @ApiModelProperty(value = "水电值班")
	 private String sdzb;
	 @ApiModelProperty(value = "精神科值班")
	 private String jskzb;
	 @ApiModelProperty(value = "精四值班")
	 private String jszb;
	 @ApiModelProperty(value = "老一值班")
	 private String lyzb;
	 @ApiModelProperty(value = "老三值班")
	 private String lszb;
	 @ApiModelProperty(value = "医生二线值班")
	 private String ysexzb;
	 @ApiModelProperty(value = "护士长二线值班")
	 private String hszexzb;
	 @ApiModelProperty(value = "值班司机")
	 private String zbsj;

}


   