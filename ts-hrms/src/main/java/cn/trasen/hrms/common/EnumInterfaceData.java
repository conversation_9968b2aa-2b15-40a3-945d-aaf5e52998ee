package cn.trasen.hrms.common;

import java.util.List;

import com.google.common.collect.Lists;

import cn.trasen.hrms.enums.AttendanceItemTypeEnum;
import cn.trasen.hrms.enums.ContractOperationStatusEnum;
import cn.trasen.hrms.enums.ContractStatusEnum;
import cn.trasen.hrms.enums.GenderTypeEnum;
import cn.trasen.hrms.enums.HireStatusEnum;
import cn.trasen.hrms.enums.InterviewStatusEnum;
import cn.trasen.hrms.enums.LaborContractStatusEnum;
import cn.trasen.hrms.enums.NumberConfigEnum;
import cn.trasen.hrms.enums.PriorityTypeEnum;
import cn.trasen.hrms.enums.SalaryCountTypeEnum;
import cn.trasen.hrms.enums.SalaryItemTypeEnum;

/**   
 * @Title: EnumInterfaceData.java 
 * @Package cn.trasen.hrms.common 
 * @Description: 提供枚举接口的数据列表
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月27日 上午11:27:08 
 * @version V1.0   
 */
public class EnumInterfaceData {

	/**
	 * @Title: getSalaryItemTypeList
	 * @Description: 获取薪酬项目类型列表
	 * @Param: 
	 * @Return: List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年3月27日 上午11:32:32
	 */
	public static List<KeyValue> getSalaryItemTypeList() {
		List<KeyValue> list = Lists.newArrayList();
		for (SalaryItemTypeEnum item : SalaryItemTypeEnum.values()) {
			if (!SalaryItemTypeEnum.ITEM_TYPE_1.getKey().equals(item.getKey())) {
				KeyValue kv = new KeyValue();
				kv.setCode(item.getKey());
				kv.setText(item.getVal());
				list.add(kv);
			}
		}
		return list;
	}

	/**
	 * @Title: getSalaryItemTypeList
	 * @Description: 获取薪酬计算类型列表
	 * @Param: 
	 * @Return: List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年3月27日 上午11:32:32
	 */
	public static List<KeyValue> getSalaryCountTypeList() {
		List<KeyValue> list = Lists.newArrayList();
		for (SalaryCountTypeEnum item : SalaryCountTypeEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

	/**
	 * @Title: getGenderTypeList
	 * @Description: 获取性别类型列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年4月10日 上午11:07:44
	 */
	public static List<KeyValue> getGenderTypeList() {
		List<KeyValue> list = Lists.newArrayList();
		for (GenderTypeEnum item : GenderTypeEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

	/**
	 * @Title: getNumberConfigCategoryList
	 * @Description: 获取编号配置类别列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年4月10日 上午11:07:44
	 */
	public static List<KeyValue> getNumberConfigCategoryList() {
		List<KeyValue> list = Lists.newArrayList();
		for (NumberConfigEnum item : NumberConfigEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

	/**
	 * @Title: getAttendanceItemTypeList
	 * @Description: 获取考勤项目类型列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年4月27日 上午9:07:59
	 */
	public static List<KeyValue> getAttendanceItemTypeList() {
		List<KeyValue> list = Lists.newArrayList();
		for (AttendanceItemTypeEnum item : AttendanceItemTypeEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

	/**
	 * @Title: getLaborContractStatusList
	 * @Description: 获取劳动合同状态列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年5月9日 上午12:39:52
	 */
	public static List<KeyValue> getLaborContractStatusList() {
		List<KeyValue> list = Lists.newArrayList();
		for (LaborContractStatusEnum item : LaborContractStatusEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

	/**
	 * @Title: getPriorityTypeList
	 * @Description: 获取优先级类型列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年5月21日 下午4:07:10
	 */
	public static List<KeyValue> getPriorityTypeList() {
		List<KeyValue> list = Lists.newArrayList();
		for (PriorityTypeEnum item : PriorityTypeEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}
	
	/**
	 * @Title: getPriorityTypeList
	 * @Description: 面试状态
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年5月24日 下午11:22:17
	 */
	public static List<KeyValue> getInterviewStatusList() {
		List<KeyValue> list = Lists.newArrayList();
		for (InterviewStatusEnum item : InterviewStatusEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}
	
	/**
	 * @Title: getHireStatusList
	 * @Description: 录用状态
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年5月24日 下午11:22:17
	 */
	public static List<KeyValue> getHireStatusList() {
		List<KeyValue> list = Lists.newArrayList();
		for (HireStatusEnum item : HireStatusEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}
	
	/**
	 * @Title: getHireStatusList
	 * @Description: 获取合同状态列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 * @date 2020年5月24日 下午11:22:17
	 */
	public static List<KeyValue> getContractStatusList() {
		List<KeyValue> list = Lists.newArrayList();
		for (ContractStatusEnum item : ContractStatusEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}
	
	/**
	 * @Title: getHireStatusList
	 * @Description: 获取合同状态列表
	 * @Return List<KeyValue>
	 * <AUTHOR>
	 */
	public static List<KeyValue> getContractOperationStatusList() {
		List<KeyValue> list = Lists.newArrayList();
		for (ContractOperationStatusEnum item : ContractOperationStatusEnum.values()) {
			KeyValue kv = new KeyValue();
			kv.setCode(item.getKey());
			kv.setText(item.getVal());
			list.add(kv);
		}
		return list;
	}

}
