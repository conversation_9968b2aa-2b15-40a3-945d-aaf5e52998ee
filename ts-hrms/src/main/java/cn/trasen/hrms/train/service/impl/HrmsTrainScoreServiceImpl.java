package cn.trasen.hrms.train.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.train.dao.HrmsTrainScoreMapper;
import cn.trasen.hrms.train.model.HrmsTrainScore;
import cn.trasen.hrms.train.service.HrmsTrainScoreService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsTrainScoreServiceImpl
 * @Description TODO
 * @date 2023��5��18�� ����5:37:24
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsTrainScoreServiceImpl implements HrmsTrainScoreService {

	@Autowired
	private HrmsTrainScoreMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsTrainScore record) {
		Assert.hasText(record.getTrainPlanId(), "培训计划id不能为空");
		//  有就新增 没有就修改
		
		//根据员工id和培训计划id
		Example example = new Example(HrmsTrainScore.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
		criteria.andEqualTo("trainPlanId", record.getTrainPlanId());
		List<HrmsTrainScore> score = mapper.selectByExample(example);
		if(score != null && score.size() > 0) {  //有就修改
			HrmsTrainScore hrmsTrainScore = score.get(0);
			hrmsTrainScore.setScore(record.getScore());
			return update(hrmsTrainScore);
		}else {  //没有评价过就新增
			record.setTrainScoreId(String.valueOf(IdWork.id.nextId()));
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			return mapper.insertSelective(record);
		}
		
		
	
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsTrainScore record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsTrainScore record = new HrmsTrainScore();
		record.setTrainScoreId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsTrainScore selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		Example example = new Example(HrmsTrainScore.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("createUser", UserInfoHolder.getCurrentUserCode());
		criteria.andEqualTo("trainPlanId", id);
		List<HrmsTrainScore> score = mapper.selectByExample(example);
		if(score != null && score.size() > 0) {
			return score.get(0);
		}
		return null;
	}

	@Override
	public DataSet<HrmsTrainScore> getDataSetList(Page page, HrmsTrainScore record) {
		Example example = new Example(HrmsTrainScore.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainScore> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<HrmsTrainScore> scoreboard(Page page, HrmsTrainScore record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainScore> records = mapper.scoreboard( page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
