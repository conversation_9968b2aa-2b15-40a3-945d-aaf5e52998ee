package cn.trasen.hrms.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsPartyMembers;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsPartyMembersMapper extends Mapper<HrmsPartyMembers> {
	
	Map<String,Object> selectEmployeeById(String empId);
	
	
	List<HrmsEmployee> selectSyncEmployee(@Param("ssoOrgCode") String ssoOrgCode);


	void updateEmployeePoliticalStatus(HrmsPartyMembers hrmsPartyMembers);
	
	List<HrmsPartyMembers> selectList(Page page, HrmsPartyMembers record);
}