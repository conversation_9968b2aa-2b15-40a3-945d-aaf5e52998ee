package cn.trasen.hrms.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.model.HrmsDictInfo;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsDictInfoMapper extends Mapper<HrmsDictInfo> {

	/**
	 * @Title: getDictInfoListByDictType
	 * @Description: 根据字典类型获取字典列表
	 * @Param: dictType 字典类型
	 * @Return: List<HrmsDictInfo>
	 * <AUTHOR> 
	 */
	List<HrmsDictInfo> getDictInfoListByDictType(@Param("dictType") String dictType);
	
//	/**
//	 * @Title: batchInsert
//	 * @Description: 批量新增
//	 * @param list
//	 * @Return int
//	 * <AUTHOR>
//	 * @date 2020年4月14日 下午6:01:00
//	 */
//	int batchInsert(List<HrmsDictInfo> list);

}