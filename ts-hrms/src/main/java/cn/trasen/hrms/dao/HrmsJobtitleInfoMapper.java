package cn.trasen.hrms.dao;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.bean.EmployeeJobtitleListResp;
import cn.trasen.hrms.model.HrmsJobtitleAppoint;
import cn.trasen.hrms.model.HrmsJobtitleInfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsJobtitleInfoMapper extends Mapper<HrmsJobtitleInfo> {

	/**
	 * @param entity
	 * @Title: getList
	 * @Description: 查询职称信息列表(不分页)
	 * @Return List<HrmsJobtitleInfo>
	 * <AUTHOR>
	 * @date 2020年4月23日 下午4:59:51
	 */
	List<HrmsJobtitleInfo> getList(HrmsJobtitleInfo entity);

	/**
	 * <p> @Title: batchInsert</p>
	 * <p> @Description: 批量添加职称信息</p>
	 * <p> @Param: </p>
	 * <p> @Return: int</p>
	 * <P> @Date: 2021年2月26日  上午10:02:39 </p>
	 * <p> <AUTHOR>
	 */
	int batchInsert(List<HrmsJobtitleInfo> list);

	int updateHighestLevel(String employeeId);

	int deleteByAppoint(HrmsJobtitleInfo jobtitleInfo);

	int deleteByApointBean(HrmsJobtitleAppoint hrmsJobtitleAppoint);

	List<HrmsJobtitleInfo> getPromotionJobDataList(Page page, HrmsJobtitleInfo entity);

	int updateTreated(String employeeId);

	List<Map<String, Object>> getWarningPromotionJobData(Map<String, Object> params);


	/**
	 * 获取员工职称列表
	 *
	 * @return java.util.List<cn.trasen.hrms.bean.GetEmployeeJobtitleListResp>
	 * <AUTHOR>
	 * @date 2021/11/17 17:09
	 */
	List<EmployeeJobtitleListResp> getEmployeejobtitleList(@Param("ssoOrgCode") String ssoOrgCode);
}