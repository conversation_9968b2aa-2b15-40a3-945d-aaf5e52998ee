package cn.trasen.hrms.salary.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CheckPersonnel {

    @ApiModelProperty(value = "工号")
    private Integer pm;
    @ApiModelProperty(value = "员工id")
    private String employeeId;
    @ApiModelProperty(value = "工号")
    private String employeeNo;
    @ApiModelProperty(value = "姓名")
    private String employeeName;
    @ApiModelProperty(value = "科室")
    private String orgName;
    @ApiModelProperty(value = "岗位")
    private String personalIdentity;
    @ApiModelProperty(value = "岗位中文")
    private String personalIdentityText;
    @ApiModelProperty(value = "编制")
    private String establishmentType;
    @ApiModelProperty(value = "编制中未能")
    private String establishmentTypeText;
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;
    @ApiModelProperty(value = "员工状态中文")
    private String employeeStatusText;
    @ApiModelProperty(value = "入职时间")
    private String entryDate;
    @ApiModelProperty(value = "转正时间")
    private String positiveTime;
    @ApiModelProperty(value = "离退休时间")
    private String retirementTime;
    @ApiModelProperty(value = "方案id")
    private String optionId;
    @ApiModelProperty(value = "方案名称")
    private String optionName;
    @ApiModelProperty(value = "是否临时员工")
    private String isTemp;
    @ApiModelProperty(value = "岗位类别")
    private String plgw;
    @ApiModelProperty(value = "岗位等级")
    private String gwdj;
    @ApiModelProperty(value = "薪级等级")
    private String salaryLevel;
}
