package cn.trasen.hrms.salary.controller;

import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportOfTemplateUtil;
import cn.trasen.hrms.utils.ExportExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustController
 * @Description TODO
 * @date 2024��10��8�� ����3:13:22
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@RestController
@Api(tags = "薪酬月度调整Controller")
public class HrmsNewsalaryTemporaryAdjustController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryTemporaryAdjustController.class);

	@Autowired
	private HrmsNewsalaryTemporaryAdjustService hrmsNewsalaryTemporaryAdjustService;

	/**
	 * @Title saveHrmsNewsalaryTemporaryAdjust
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalaryTemporaryAdjust/save")
	public PlatformResult<String> saveHrmsNewsalaryTemporaryAdjust(@RequestBody HrmsNewsalaryTemporaryAdjust record) {
		try {
			hrmsNewsalaryTemporaryAdjustService.save(record,NewsalaryTemporaryAdjustOpTypeEnum.ADD);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryTemporaryAdjust
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalaryTemporaryAdjust/update")
	public PlatformResult<String> updateHrmsNewsalaryTemporaryAdjust(@RequestBody HrmsNewsalaryTemporaryAdjust record) {
		try {
			hrmsNewsalaryTemporaryAdjustService.update(record,NewsalaryTemporaryAdjustOpTypeEnum.UPDATE);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryTemporaryAdjustById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryTemporaryAdjust>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalaryTemporaryAdjust/{id}")
	public PlatformResult<HrmsNewsalaryTemporaryAdjust> selectHrmsNewsalaryTemporaryAdjustById(@PathVariable String id) {
		try {
			HrmsNewsalaryTemporaryAdjust record = hrmsNewsalaryTemporaryAdjustService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryTemporaryAdjustById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalaryTemporaryAdjust/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryTemporaryAdjustById(@PathVariable String id) {
		try {
			hrmsNewsalaryTemporaryAdjustService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryTemporaryAdjustList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryTemporaryAdjust>
	 * @date 2024��10��8�� ����3:13:22
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalaryTemporaryAdjust/list")
	public DataSet<HrmsNewsalaryTemporaryAdjust> selectHrmsNewsalaryTemporaryAdjustList(Page page, HrmsNewsalaryTemporaryAdjust record) {
		List<HrmsNewsalaryTemporaryAdjust> records = hrmsNewsalaryTemporaryAdjustService.getDataSetList(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@GetMapping(value = "/api/newsalaryTemporaryAdjust/downloadImportTemplate")
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	public void downloadImportTemplate(HttpServletResponse response) {
		try {
			ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
			String filename = "薪酬调整导入模板.xlsx";
			String template = "template/newsalaryadjust/import.xlsx";
			ClassPathResource resource = new ClassPathResource(template);
			exportExcelUtil.downloadExportExcel(filename, response, resource);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@ApiOperation(value = "导入", notes = "导入")
	@PostMapping(value = "/api/newsalaryTemporaryAdjust/import")
	public PlatformResult importData(@RequestParam("file") MultipartFile file) {
		List<HrmsNewsalaryTemporaryAdjust> list = (List<HrmsNewsalaryTemporaryAdjust>) ImportExcelUtil.getExcelDatas(file, HrmsNewsalaryTemporaryAdjust.class);
		List<HrmsNewsalaryTemporaryAdjust> datas = list.stream().filter(vo-> StringUtils.isNotBlank(vo.getEmployeeNo()) && StringUtils.isNotBlank(vo.getOptionCycle())).collect(Collectors.toList());
		Map<String,Integer> map = hrmsNewsalaryTemporaryAdjustService.importData(datas);
		return PlatformResult.success(map);
	}

	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping(value = "/api/newsalaryTemporaryAdjust/downLoad")
	public void downLoad(HrmsNewsalaryTemporaryAdjust entity,HttpServletRequest request,
						 HttpServletResponse response) {
		try {
			TemplateExportParams params = new TemplateExportParams("template/newsalaryadjust/export.xls");
			String name = "薪酬调整记录.xls";
			String _val = entity.getOptionCycle();
			if(!StringUtil.isEmpty(_val)) {
				name = _val + " 薪酬调整记录.xls";
			}
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");


			List<HrmsNewsalaryTemporaryAdjust> list = hrmsNewsalaryTemporaryAdjustService.getList(entity);
			if(list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i).setNo(i+1);
					list.get(i).setCreateDateStr(DateUtils.getPresentTimeStr(list.get(i).getCreateDate()));
					list.get(i).setUpdateDateStr(DateUtils.getPresentTimeStr(list.get(i).getUpdateDate()));
					list.get(i).setCountType(list.get(i).getCountType().equals("1")?"加项":"减项");
					list.get(i).setIsUse(list.get(i).getIsUse().equals("1")?"已锁定":"未锁定");
				}
			}
			Map<String,Object> resultMap = new HashMap<String, Object>();
			resultMap.put("list", list);
			resultMap.put("date", _val);
			Workbook workbook = new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, resultMap);
			response.setContentType("application/msword");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} catch (Exception e) {
			log.error("薪酬调整记录导出异常"+e.getMessage(),e);
		}
	}
}
