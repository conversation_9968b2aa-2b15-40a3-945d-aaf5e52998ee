package cn.trasen.hrms.salary.service.impl;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporaryChange;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryTemporaryAdjustChangeMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjustChange;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustChangeService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.persistence.Transient;

/**
 * @ClassName HrmsNewsalaryTemporaryAdjustChangeServiceImpl
 * @Description TODO
 * @date 2024��10��8�� ����3:13:33
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryTemporaryAdjustChangeServiceImpl implements HrmsNewsalaryTemporaryAdjustChangeService {

	@Resource
	private HrmsNewsalaryTemporaryAdjustChangeMapper mapper;
	@Resource
	private DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer saveChanges(HrmsNewsalaryTemporaryAdjust oldObj, HrmsNewsalaryTemporaryAdjust newObj, NewsalaryTemporaryAdjustOpTypeEnum opTypeEnum){
		if(opTypeEnum.getKey().equals(NewsalaryTemporaryAdjustOpTypeEnum.ADD.getKey())
				|| opTypeEnum.getKey().equals(NewsalaryTemporaryAdjustOpTypeEnum.IMPORT.getKey())){
			HrmsNewsalaryTemporaryAdjustChange record = new HrmsNewsalaryTemporaryAdjustChange();
			record.setOpType(opTypeEnum.getKey());
			record.setEmployeeId(newObj.getEmployeeId());
			record.setEmployeeName(newObj.getEmployeeName());
			record.setEmployeeNo(newObj.getEmployeeNo());
			record.setAdjustId(newObj.getId());
			return save(record);
		}else {
			//对比两个对象的属性差值，保存操作
			Map<String, Object> oldMap = BeanUtil.beanToMap(oldObj);
			Map<String, Object> newMap = BeanUtil.beanToMap(newObj);
			Field[] fields = HrmsNewsalaryTemporaryAdjust.class.getDeclaredFields();
			StringBuffer beforeValues = new StringBuffer();
			StringBuffer afterValues = new StringBuffer();
			Map<String, String> tmpAdjustItemDictMap = convertDictMap("tmp_adjust_item"); // 项目类型

			String oldValue = "";
			String newalue = "";
			for (Field f: fields){
				if("createDate".equals(f.getName())
						||"createUser".equals(f.getName())
						||"createUserName".equals(f.getName())
						||"updateDate".equals(f.getName())
						||"updateUser".equals(f.getName())
						||"updateUserName".equals(f.getName())
						|| f.getAnnotatedType().equals(Transient.class)){
					continue;
				}
				if("tmpItem".equals(f.getName())){
					if(oldMap.containsKey(f.getName()) && oldMap.get(f.getName())!=null) {
						oldValue = tmpAdjustItemDictMap.get(oldMap.get(f.getName()).toString());
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName())!=null) {
						newalue = tmpAdjustItemDictMap.get(newMap.get(f.getName()).toString());
					}
				}else if("countType".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName())!=null) {
						oldValue = "1".equals(oldMap.get(f.getName()).toString()) ? "加项" : "减项";
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName())!=null) {
						newalue = "1".equals(newMap.get(f.getName()).toString()) ? "加项" : "减项";
					}
				}else if("isUse".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName())!=null) {
						oldValue = "1".equals(oldMap.get(f.getName()).toString()) ? "已锁定" : "未锁定";
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName())!=null) {
						newalue = "1".equals(newMap.get(f.getName()).toString()) ? "已锁定" : "未锁定";
					}
				}else if("isDeleted".equals(f.getName())){
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName())!=null) {
						oldValue = "N".equals(oldMap.get(f.getName()).toString()) ? "否" : "是";
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName())!=null) {
						newalue = "N".equals(newMap.get(f.getName()).toString()) ? "否" : "是";
					}
				}else{
					if(oldMap.containsKey(f.getName())&& oldMap.get(f.getName())!=null) {
						oldValue =oldMap.get(f.getName()).toString();
					}
					if(newMap.containsKey(f.getName())&& newMap.get(f.getName())!=null) {
						newalue = newMap.get(f.getName()).toString();
					}
				}

				if(oldMap.containsKey(f.getName()) && oldMap.get(f.getName()) != null && !newMap.containsKey(f.getName()) ){
					beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(oldValue).append(";  ");
					afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append("空 ;   ");
				}else if(!oldMap.containsKey(f.getName()) && newMap.containsKey(f.getName()) && newMap.get(f.getName()) != null){
					beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append("空 ;  ");
					afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(newalue).append(";   ");
				}else if(oldMap.containsKey(f.getName()) && newMap.containsKey(f.getName())
						&& oldMap.get(f.getName()) != null && newMap.get(f.getName()) != null
						&& !oldMap.get(f.getName()).equals(newMap.get(f.getName()))){
					beforeValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(oldValue).append(";  ");
					afterValues.append(f.getAnnotation(ApiModelProperty.class).value()).append(":").append(newalue).append(";   ");
				}
			}

			HrmsNewsalaryTemporaryAdjustChange record = new HrmsNewsalaryTemporaryAdjustChange();
			record.setOpType(opTypeEnum.getKey());
			record.setEmployeeId(newObj.getEmployeeId());
			record.setEmployeeNo(newObj.getEmployeeNo());
			record.setEmployeeName(newObj.getEmployeeName());
			record.setAdjustId(newObj.getId());
			record.setOpBeforeValue(beforeValues.toString());
			record.setOpAfterValue(afterValues.toString());
			return save(record);
		}
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryTemporaryAdjustChange record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryTemporaryAdjustChange record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryTemporaryAdjustChange record = new HrmsNewsalaryTemporaryAdjustChange();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryTemporaryAdjustChange selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryTemporaryAdjustChange> getDataSetList(Page page, HrmsNewsalaryTemporaryAdjustChange record) {
		Example example = new Example(HrmsNewsalaryTemporaryAdjustChange.class);
		Example.Criteria criteria = example.createCriteria();
		Example.Criteria employeeCriteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(record!=null){
			if(StringUtils.isNotBlank(record.getEmployeeNo())){
				employeeCriteria.andLike("employeeNo","%"+record.getEmployeeNo()+"%").orLike("employeeName","%"+record.getEmployeeNo()+"%");
			}
			if(StringUtils.isNotBlank(record.getSearchStartTime())){
				criteria.andCondition("DATE_FORMAT(create_date, '%Y-%m-%d') >= DATE_FORMAT('"+record.getSearchStartTime()+"','%Y-%m-%d')");
			}
			if(StringUtils.isNotBlank(record.getSearchEndTime())){
				criteria.andCondition("DATE_FORMAT(create_date, '%Y-%m-%d') <= DATE_FORMAT('"+record.getSearchEndTime()+"','%Y-%m-%d')");
			}
			if(StringUtils.isNotBlank(record.getOpType())){
				criteria.andEqualTo("opType",record.getOpType());
			}
			example.and(employeeCriteria);
		}
		example.setOrderByClause("create_date desc");
		List<HrmsNewsalaryTemporaryAdjustChange> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
