package cn.trasen.hrms.salary.DTO;

import lombok.Data;

import java.util.List;

//薪酬汇总统计查询参数模型
@Data
public class SalaryCountSearchReq {

    private String payrollDate;  // 发放月份
    private String optionId;  //方案id
    private String employeeName; //名称
    private String orgName;  //名称id

    private String phoneNumber; //电话

    private String reportId;  //报表id


    private List<String> employeeStatuses ;

    private String birthdayStartTime;  //出生日期开始
    private String birthdayEndTime;  //出生日期结束
    private String identityNumber;  //身份证
    private String empPayroll;// 发薪号
    private List<String> employeeCategorys;
    private List<String> jobtitleCategory;
    private List<String> jobtitleName;
    private String positiveTimeStartTime;  //转正开始时间
    private String  positiveTimeEndTime;  //转正结束时间
    private String  startAge;  //开始年龄
    private String endAge;   //结束年龄
    private String entryDateStartTime;  //入院开始时间
    private String  entryDateEndTime;    //入院结束时间
    private List<String> establishmentTypes;   //编制类型
    private List<String>  orgAttributestypes;  //人员类别
    private List<String> politicalStatuses;   //政治面貌
    private List<String> nationalityes;   //民族
    private List<String> marriageStatuses;  //婚姻状况
    private List<String> positionNames;
    private List<String>  personalIdentitys;  //岗位名称
    private List<String> operationTypes;  //执业类别
    private List<String>  educationTypes;  //学历类型
    private List<String> plgws;   // 岗位类别
    private List<String> gwdjs;  //岗位等级
    private List<String> optionIds;  //薪酬方案组id

    private String exportJson;  //导出的列

    private String startDate;  // 开始发放月份
    private String endDate;  // 结束发放月份
    private String optionName;  //方案名称

}
