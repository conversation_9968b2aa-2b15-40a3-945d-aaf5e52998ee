package cn.trasen.hrms.salary.task;

import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindRecordService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionsRemindRecordService;
import cn.trasen.hrms.service.HrmsWarningSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 薪酬提示定时任务
 */
@Slf4j
@Component
public class NewsalaryRemindJob {
	
	@Autowired
	private HrmsNewsalaryOptionsRemindRecordService newsalaryOptionsRemindRecordService;

	@Autowired
	private HrmsNewsalaryLevelRemindRecordService newsalaryLevelRemindRecordService;
	
	/**
	 * 
	 * @Title: newsalaryOptionsRemindData
	 * @Description: 薪酬方案异动提醒  每分钟执行一次
	 * @param
	 * @return void 返回类型
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "30 0/1 * * * ? ")
	public void newsalaryOptionsRemindData() {
		try {
			newsalaryOptionsRemindRecordService.newsalaryOptionsChange();
		} catch (Exception e) {
			log.error("薪酬方案变动提醒执行失败：" + e.getMessage(),e);
		}
	}

	/**
	 *
	 * @Title: newsalaryOptionsRemindData
	 * @Description: 薪级等级异动提醒  每分钟执行一次
	 * @param
	 * @return void 返回类型
	 * ADMIN
	 * @throws
	 */
	@Scheduled(cron = "30 0/1 * * * ? ")
	public void newsalaryLevelRemindData() {
		try {
			newsalaryLevelRemindRecordService.newsalaryLevelChange();
		} catch (Exception e) {
			log.error("薪级等级异动提醒执行失败：" + e.getMessage(),e);
		}
	}
	
	
	
}
