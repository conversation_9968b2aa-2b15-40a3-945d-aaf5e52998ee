package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportMapEo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Entity generator.domain.HrmsNewsalaryOptionTotalType
 */
public interface HrmsNewsalaryReportMapMapper extends Mapper<HrmsNewsalaryReportMapEo> {

    List<HrmsNewsalaryReportMapVo> getDataSetList(@Param("reportMapVo") HrmsNewsalaryReportMapVo reportMapVo, Page page);

    /**
     * 获取报表设置中的关联薪酬项id
     * @param reportId
     * @return
     */
    List<String> queryItem(@Param("reportId") String reportId);

    /**
     * 获取报表设置中的计算公式id
     * @param reportId
     * @return
     */
    List<Map<String,String>> queryCountFormulaItem(@Param("reportId") String reportId);
}




