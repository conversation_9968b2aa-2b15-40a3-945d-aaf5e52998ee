package cn.trasen.hrms.salary.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryRemindSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsNewsalaryLevelRemindRecordMapper;
import cn.trasen.hrms.salary.service.HrmsNewsalaryLevelRemindRecordService;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryLevelRemindRecordServiceImpl
 * @Description TODO
 * @date 2024��11��6�� ����10:33:38
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryLevelRemindRecordServiceImpl implements HrmsNewsalaryLevelRemindRecordService {

	@Resource
	private HrmsNewsalaryLevelRemindRecordMapper mapper;

	@Autowired
	private HrmsNewsalaryRemindSettingService remindSettingService;

	@Resource
	private InformationFeignService informationFeignService;

	@Autowired
	private HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	//注入本身，保证数据库事务的正常使用
	@Autowired
	private HrmsNewsalaryLevelRemindRecordService newsalaryLevelRemindRecordService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalaryLevelRemindRecord record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryLevelRemindRecord record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryLevelRemindRecord record = new HrmsNewsalaryLevelRemindRecord();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryLevelRemindRecord selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalaryLevelRemindRecord> getDataSetList(Page page, HrmsNewsalaryLevelRemindRecord record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsNewsalaryLevelRemindRecord> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * 薪级等级调整
	 * @param vo 提醒记录
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public Integer saveSalaryLevelAdjust(HrmsNewsalaryLevelRemindRecord vo){
		Assert.notNull(vo,"调整对象不能为空");
		int count = 0;
		List<HrmsNewsalaryBasicitemEmp> list = null;
		HrmsNewsalaryBasicColumn record = null;
		List<HrmsNewsalaryBasicColumn> basicColumnsList = null;
		vo.setHandleStatus("1");
		count = update(vo);

		if("1".equals(vo.getIsAdjust())){ //同步更新到薪酬档案
			//获取员工定薪表数据
			list = hrmsNewsalaryBasicitemEmpService.getDataByEmployeeId(vo.getEmployeeId());
			//获取薪酬档案字段
			record = new HrmsNewsalaryBasicColumn();
			record.setEmployeeId(vo.getEmployeeId());
			HrmsNewsalaryBasicitemEmp emp = null;
			basicColumnsList = hrmsNewsalaryBasicitemEmpService.getAllData(record);
			if(CollectionUtils.isEmpty(list)){
				for(HrmsNewsalaryBasicColumn ba: basicColumnsList) {
					emp = new HrmsNewsalaryBasicitemEmp();
					BeanUtil.copyProperties(ba,emp);
					emp.setEmployeeId(vo.getEmployeeId());
					if(StringUtils.isNotEmpty(vo.getUpdateUser())){
						emp.setUpdateUser(vo.getUpdateUser());
						emp.setUpdateUserName(vo.getUpdateUserName());
						emp.setCreateUser(vo.getUpdateUser());
						emp.setCreateUserName(vo.getUpdateUserName());
						emp.setSsoOrgCode(vo.getSsoOrgCode());
					}
					list.add(emp);
				}
			}
			for(HrmsNewsalaryBasicitemEmp entity : list){
				for(HrmsNewsalaryBasicColumn ba: basicColumnsList) {
					if (ba.getEmpField().equals(entity.getEmpField())) {
						entity.setCustomRule(ba.getCustomRule());
						entity.setItemRule(ba.getItemRule());
					}
				}
				//设置为当月生效
				entity.setEffectiveDate(DateUtil.formatDate(DateUtil.beginOfMonth(DateUtil.date())));
				entity.setReason("薪级等级自动调整");
				if ("salary_level_id".equals(entity.getEmpField())) {
					entity.setEmpFieldValue(vo.getAfterSalaryLevel());
					entity.setEmpFieldValueText(vo.getAfterSalaryLevelName());
				}
				if("A03".equalsIgnoreCase(entity.getCustomRule())){
					entity.setSalaryAmount(vo.getAfterAmount());
				}
			}
			hrmsNewsalaryBasicitemEmpService.adjustSave(list);
		}
		return count;
	}

	/**
	 * 薪级等级批量调整(异步)
	 * @param isAdjust 调整类型
	 * @param ids id
	 * @return
	 */
	@Override
	public SseEmitter batchSalaryLevelAdjust(String isAdjust,String ids ){
		Assert.hasText(isAdjust,"调整类型不能为空");
		Assert.hasText(ids,"调整对象id不能为空");
		SseEmitter emitter = new SseEmitter(-1L);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		// 在新线程中模拟进度更新
		new Thread(() -> {
			int count = 0;
			try{
				String[] idArr = ids.split(",");
				if(idArr==null && idArr.length<1){
					throw new BusinessException("调整对象id不能为空");
				}
				int size = idArr.length;
				if(idArr!=null && idArr.length > 0){
					List<HrmsNewsalaryBasicitemEmp> list = null;
					HrmsNewsalaryBasicColumn record = null;
					List<HrmsNewsalaryBasicColumn> basicColumnsList = null;
					for(String id : idArr){
						count ++;
						HrmsNewsalaryLevelRemindRecord vo = mapper.selectByPrimaryKey(id);
						if(vo == null){
							throw new BusinessException("未找到薪级等级调整对象数据");
						}
						if(StringUtils.isNotBlank(vo.getHandleStatus()) && "1".equals(vo.getHandleStatus())){
							continue;
						}
						log.error("==========薪级等级开始处理:"+ vo.getEmployeeId());
						if (user != null) {
							vo.setUpdateUser(user.getUsercode());
							vo.setUpdateUserName(user.getUsername());
							vo.setSsoOrgCode(user.getCorpcode());
						}
						vo.setIsAdjust(isAdjust);
						newsalaryLevelRemindRecordService.saveSalaryLevelAdjust(vo);
						// 发送进度数据
						BigDecimal proccess = Convert.toBigDecimal(count).divide(Convert.toBigDecimal(size),3,RoundingMode.HALF_UP ).multiply(Convert.toBigDecimal(100)).setScale(0,RoundingMode.DOWN);
						if(proccess.compareTo(new BigDecimal(100))<0) {
							emitter.send(proccess);
						}
						log.error("==========薪级等级结束处理:"+ vo.getEmployeeId());
					}
					log.error("==========薪级等级批量处理完成");
					// 发送进度数据
					emitter.send(new BigDecimal(100));
					emitter.complete();
				}
			} catch (Exception e) {
				try {
					emitter.send("message:" + e.getMessage());
				}catch (Exception e1){
					emitter.completeWithError(e);
				}
				if(e instanceof IOException) {
					emitter.completeWithError(e);
				}
				log.error("==========薪级等级批量处理失败");
				e.printStackTrace();
			}
		}).start();
		return emitter;
	}

	/**
	 * 定时查询薪级等级异动数据
	 */
	@Transactional(readOnly = false)
	@Override
	public void newsalaryLevelChange(){
		//获取方案的提醒设置配置
		HrmsNewsalaryRemindSetting setting = remindSettingService.selectByType(3);
		if(setting!=null && "1".equals(setting.getIsEnabled())){
			String currentTime = DateUtil.format(DateUtil.date(),"HH:mm");
			if(StringUtils.isBlank(setting.getRemindTime()) || !currentTime.equals(setting.getRemindTime())){
				return;
			}
			if(CollUtil.isNotEmpty(setting.getLevelRemindSettingList())){
				Example example = null;
				Example.Criteria criteria = null;

				//删除本次调整时间未处理的提醒记录
				for(HrmsNewsalaryLevelRemindSetting vo:setting.getLevelRemindSettingList()){

					example = new Example(HrmsNewsalaryLevelRemindRecord.class);
					criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andEqualTo("adjustDate", vo.getNextAdjustDate());
					criteria.andEqualTo("handleStatus", "0");
					mapper.deleteByExample(example);
				}
			}
			List<HrmsNewsalaryLevelRemindRecord> list = mapper.getNewsalaryLevelChange(setting.getRemindCycle(),setting.getAdvanceDays()+"",setting.getRemindDate(),setting.getRemindTime());
			if(CollectionUtils.isNotEmpty(list)){
				for(HrmsNewsalaryLevelRemindRecord vo : list){
					//新增方案异动记录
					vo.setRemindId(setting.getId());
					save(vo);
				}

				try {
					String content = "本月有" + list.size() + "个员工的薪级等级需要进行调整，请前往薪酬提醒-薪级等级调整进行处理";
					//发送通知
					NoticeReq notice = NoticeReq.builder()
							.content(content)
							.noticeType("4")
							.receiver(setting.getNoticeUser())
							.sender("admin")
							.senderName("系统管理员")
							.subject("薪级等级调整异动提醒通知")
							.toUrl("/ts-web-hrm/pay-manager/newsalary-remind")
							.wxSendType("2")
							.source("异动提醒")
							.build();
					informationFeignService.sendNotice(notice);
				}catch (Exception e){
					e.printStackTrace();
					System.out.println("发送通知异常:"+e.getMessage());
				}

				//如果是单次提醒，则需要修改启用状态为停用
				if("1".equals(setting.getRemindCycle())){
					setting.setIsEnabled("0");
					setting.setRemark("已完成提醒");
					remindSettingService.update(setting);
				}
			}
		}
	}
}
