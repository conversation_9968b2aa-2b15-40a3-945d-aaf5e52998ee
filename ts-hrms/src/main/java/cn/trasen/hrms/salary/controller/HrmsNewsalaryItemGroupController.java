package cn.trasen.hrms.salary.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemGroup;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryItemGroupController
 * @Description 薪酬项分组
 * @date 2023��11��11�� ����4:34:59
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬项分组Controller")
public class HrmsNewsalaryItemGroupController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemGroupController.class);

	//zhushi
	@Autowired
	private HrmsNewsalaryItemGroupService hrmsNewsalaryItemGroupService;

	/**
	 * @Title saveHrmsNewsalaryItemGroup
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryItemGroup/save")
	public PlatformResult<String> saveHrmsNewsalaryItemGroup(@RequestBody HrmsNewsalaryItemGroup record) {
		try {
			hrmsNewsalaryItemGroupService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItemGroup
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryItemGroup/update")
	public PlatformResult<String> updateHrmsNewsalaryItemGroup(@RequestBody HrmsNewsalaryItemGroup record) {
		try {
			hrmsNewsalaryItemGroupService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryItemGroupById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItemGroup>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryItemGroup/{id}")
	public PlatformResult<HrmsNewsalaryItemGroup> selectHrmsNewsalaryItemGroupById(@PathVariable String id) {
		try {
			HrmsNewsalaryItemGroup record = hrmsNewsalaryItemGroupService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryItemGroupById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryItemGroup/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryItemGroupById(@PathVariable String id) {
		try {
			hrmsNewsalaryItemGroupService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "从其它薪资组导入", notes = "从其它薪资组导入")
	@PostMapping("/api/salaryItemGroup/copy")
	public PlatformResult<String> copy(@RequestBody HrmsNewsalaryItemGroup record) {
		try {
			hrmsNewsalaryItemGroupService.copy(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "导入工资项", notes = "导入工资项")
	@PostMapping("/api/salaryItemGroup/importItem/{groupId}")
	public PlatformResult<String> importItem(@PathVariable String groupId,@RequestBody List<HrmsNewsalaryItem> record) {
		try {
			hrmsNewsalaryItemGroupService.importItem(groupId,record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	//根据薪酬组id查询分组 和分组里面的项目
	@ApiOperation(value = "根据薪酬组查询所有薪酬项分组", notes = "根据薪酬组查询所有薪酬项分组")
	@PostMapping("/api/salaryItemGroup/listByOptionId")
	public PlatformResult<List<HrmsNewsalaryItemGroup>> listByOptionId(@RequestBody HrmsNewsalaryItemGroup record) {
		try {
			List<HrmsNewsalaryItemGroup> list= hrmsNewsalaryItemGroupService.listByOptionId(record);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据薪酬组查询所有薪酬项分组", notes = "根据薪酬组查询所有薪酬项分组")
	@PostMapping("/api/salaryItemGroup/listByOptionIdSel")
	public PlatformResult<List<HrmsNewsalaryItemGroup>> listByOptionIdSel(@RequestBody HrmsNewsalaryItemGroup record) {
		try {
			List<HrmsNewsalaryItemGroup> list= hrmsNewsalaryItemGroupService.listByOptionIdSel(record);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}



	/**
	 * @Title selectHrmsNewsalaryItemGroupList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItemGroup>
	 * @date 2023��11��11�� ����4:34:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryItemGroup/list")
	public DataSet<HrmsNewsalaryItemGroup> selectHrmsNewsalaryItemGroupList(Page page, HrmsNewsalaryItemGroup record) {
		return hrmsNewsalaryItemGroupService.getDataSetList(page, record);
	}

}
