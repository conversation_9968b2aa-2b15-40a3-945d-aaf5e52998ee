package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpHistoryMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper;
import cn.trasen.hrms.salary.enums.CarryRuleEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicColumnService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpHistoryService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpHistoryServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:33:03
 * <AUTHOR>
 * @version 1.0
 */
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryBasicitemEmpHistoryServiceImpl implements HrmsNewsalaryBasicitemEmpHistoryService {

	@Resource
	private HrmsNewsalaryBasicitemEmpHistoryMapper mapper;

	@Resource
	private HrmsNewsalaryBasicitemEmpMapper basicitemEmpMapper;

	@Autowired
	HrmsNewsalaryBasicColumnService hrmsNewsalaryBasicColumnService;

	@Autowired
	private HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	@Resource
	DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(List<HrmsNewsalaryBasicitemEmpHistory> records) {
		if(!records.isEmpty() && records.size() >0){
			for (int i = 0; i < records.size(); i++) {
				records.get(i).setId(IdUtil.getId());
				records.get(i).setCreateDate(new Date());
				records.get(i).setUpdateDate(new Date());
				records.get(i).setIsDeleted("N");
				records.get(i).setVNumber(DateUtils.getNoByDateStr());  //年月日作为版本号
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					records.get(i).setCreateUser(user.getUsercode());
					records.get(i).setCreateUserName(user.getUsername());
					records.get(i).setUpdateUser(user.getUsercode());
					records.get(i).setUpdateUserName(user.getUsername());
				}
				mapper.insertSelective(records.get(i));
			}
		}
		return records.size();
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalaryBasicitemEmpHistory record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryBasicitemEmpHistory record = new HrmsNewsalaryBasicitemEmpHistory();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer cancel(HrmsNewsalaryBasicitemEmpHistory record) {
		//根据调薪生效日期 和人员id删除数据
		Assert.hasText(record.getEmployeeId(), "员工id不能为空");
		Assert.hasText(record.getEffectiveDate(), "生效时间不能为空");
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		criteria.andEqualTo("dataStatus", "0");
		criteria.andEqualTo("effectiveDate", record.getEffectiveDate());
		return mapper.deleteByExample(example);
	}

	@Override
	public HrmsNewsalaryBasicitemEmpHistory selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<Map<String,Object>> getDataSetList( HrmsNewsalaryBasicitemEmpHistory record) {
		//传入分组 key
		List<HrmsNewsalaryBasicColumn> allTypeList = hrmsNewsalaryBasicColumnService.getAllListBaType();
		List<String> listStr = allTypeList.stream().map(HrmsNewsalaryBasicColumn::getId).collect(Collectors.toList());
		record.setAllTypeList(listStr);
		List<Map<String,Object>>  data= mapper.getDataSetList(record);
		return data;
	}


	@Override
	public List<HrmsNewsalaryBasicitemEmpHistory> getEmployeeIdAndNo(String employeeId, String no) {
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("vNumber", no);
		return mapper.selectByExample(example);
	}

	@Override
	public List<HrmsNewsalaryBasicitemEmpHistory> getEmployeeIdAndEffectiveDate(String employeeId, String effectiveDate) {
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("effectiveDate", effectiveDate);
		List<HrmsNewsalaryBasicitemEmpHistory> list = mapper.selectByExample(example);

		Map<String, String> postCategoryDictMap = convertDictMap("post_category");  //岗位类别
		Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category");  //薪级类别

		if(list != null && list.size() > 0 ){
			for (int i = 0; i < list.size(); i++) {
				if("plgw".equals(list.get(i).getEmpField())){  //岗位类别
					list.get(i).setEmpFieldText(list.get(i).getEmpFieldValueText());
				}
				if("salary_level_type".equals(list.get(i).getEmpField())){  //薪级类别
					list.get(i).setEmpFieldText(list.get(i).getEmpFieldValueText());
				}
				//处理岗位等级
				if("gwdj".equals(list.get(i).getEmpField())){  //薪级类别
					list.get(i).setEmpFieldText(list.get(i).getEmpFieldValueText());
				}
				//处理薪级等级
				if("salary_level_id".equals(list.get(i).getEmpField())){  //薪级类别
					list.get(i).setEmpFieldText(list.get(i).getEmpFieldValueText());
				}
			}
		}

		return list;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteByEmployeeIdAndDate(String employeeId, String effectiveDate) {
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("effectiveDate", effectiveDate);
		return mapper.deleteByExample(example);
	}
	@Transactional(readOnly = false)
	@Override
	public Integer updateDate(List<HrmsNewsalaryBasicitemEmpHistory> records) {
		HrmsNewsalaryBasicitemEmpHistory bean = records.get(0);
		HrmsNewsalaryBasicitemEmpHistory basicItemHistory = this.getFinallYByEmployeeId(bean.getEmployeeId());
		records.forEach(item -> {
			item.setReason(basicItemHistory.getReason());
		});
		deleteByEmployeeIdAndDate(bean.getEmployeeId(),bean.getEffectiveDate());
		List<HrmsNewsalaryBasicitemEmp> basicEmpList = BeanUtil.copyToList(records, HrmsNewsalaryBasicitemEmp.class);
		hrmsNewsalaryBasicitemEmpService.adjustSave(basicEmpList);
		return records.size();
	}



	@Override
	public List<VueTableEntity> headlist(String employeeId) {
		int width= 20;
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		retVueTableEntity.add(new VueTableEntity("调薪原因","reason",null,null,null));
		retVueTableEntity.add(new VueTableEntity("生效日期","effective_date",null,100,null));
		retVueTableEntity.add(new VueTableEntity("生效状态","data_status",null,null,null));
		//查询 基本列
		List<HrmsNewsalaryBasicColumn> allList = hrmsNewsalaryBasicColumnService.getAllListBaType();
		if(!allList.isEmpty() && allList.size() > 0){
			allList.forEach(item->{
				retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(),item.getId(),null,item.getBasicItemName().length() == 2 ? 80 : item.getBasicItemName().length() * width,null));
			});
		}
		retVueTableEntity.add(new VueTableEntity("备注","remark",null,null,null));
		retVueTableEntity.add(new VueTableEntity("最近处理人","update_user_name",null,null,null));
		retVueTableEntity.add(new VueTableEntity("最近处理日期","update_date",null,180,null));
		return retVueTableEntity;
	}



	@Override
	public Integer updateExpired(String employeeId,String basicItemId) {
		HrmsNewsalaryBasicitemEmpHistory record = new HrmsNewsalaryBasicitemEmpHistory();
		record.setDataStatus("2");  //设置为已过期
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("dataStatus", "1");
		if(StringUtils.isNotBlank(basicItemId)){
			criteria.andEqualTo("basicItemId", basicItemId);
		}
		return mapper.updateByExampleSelective(record,example);

	}

	@Override
	public HrmsNewsalaryBasicitemEmpHistory getFinallYByEmployeeId(String id) {

		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", id);
		example.setOrderByClause(" create_date desc ");
		List<HrmsNewsalaryBasicitemEmpHistory> list = mapper.selectByExample(example);
		if(!list.isEmpty()){
			return list.get(0);
		}
		return new HrmsNewsalaryBasicitemEmpHistory();
	}

	//查询当前定薪信息
	@Override
	public Map<String,Object> currentSalary(HrmsNewsalaryBasicitemEmpHistory record) {
		//查询当前定薪 名称
		List<HrmsNewsalaryBasicitemEmp>  list= mapper.currentSalary(record);

		Map<String, String> postCategoryDictMap = convertDictMap("post_category");  //岗位类别
		Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category");  //薪级类别
		Map<String,Object> retInfo = new LinkedHashMap<>();
		Map<String,Object> baseInfo = new LinkedHashMap<>();
		Map<String,Object> salaryInfo = new LinkedHashMap<>();
		if(list != null && list.size() > 0 ){
			for (int i = 0; i < list.size(); i++) {
				if("plgw".equals(list.get(i).getEmpField())){  //岗位类别
					list.get(i).setEmpFieldText(postCategoryDictMap.get(list.get(i).getEmpFieldValue()));
					baseInfo.put(list.get(i).getBasicItemName(),list.get(i).getEmpFieldText());
				}
				if("salary_level_type".equals(list.get(i).getEmpField())){  //薪级类别
					list.get(i).setEmpFieldText(salaryLevelCategoryDictMap.get(list.get(i).getEmpFieldValue()));
					baseInfo.put(list.get(i).getBasicItemName(),list.get(i).getEmpFieldText());
				}
				//处理岗位等级
				if("gwdj".equals(list.get(i).getEmpField())){  //薪级类别
					String _text = basicitemEmpMapper.getGwdjText(list.get(i).getEmpFieldValue());
					list.get(i).setEmpFieldText(_text);
					baseInfo.put(list.get(i).getBasicItemName(),list.get(i).getEmpFieldText());
				}
				//处理薪级等级
				if("salary_level_id".equals(list.get(i).getEmpField())){  //薪级类别
					String _text = basicitemEmpMapper.getSalaryLeavelText(list.get(i).getEmpFieldValue());
					list.get(i).setEmpFieldText(_text);
					baseInfo.put(list.get(i).getBasicItemName(),list.get(i).getEmpFieldText());
				}

				//政策标准
				if("policy_standard_id".equals(list.get(i).getEmpField())){
					list.get(i).setEmpFieldText(list.get(i).getEmpFieldValueText());
					baseInfo.put(list.get(i).getBasicItemName(),list.get(i).getEmpFieldText());
				}

				if("薪级年度正常晋升".equals(list.get(i).getBasicItemName())){
					baseInfo.put(list.get(i).getBasicItemName(),"1".equals(list.get(i).getEmpFieldValue())?'是':'否');
				}
				if("月度提高标识".equals(list.get(i).getBasicItemName())){
					baseInfo.put(list.get(i).getBasicItemName(),"1".equals(list.get(i).getEmpFieldValue())?'是':'否');
				}

				if(StringUtil.isEmpty(list.get(i).getEmpFieldText())){
					if(null != list.get(i).getSalaryAmount() && !"1".equals(list.get(i).getBasicItemType())){
						BigDecimal amount = list.get(i).getSalaryAmount();
						if(StringUtils.isNotBlank(list.get(i).getEmpFieldValue())){
							amount = amount.multiply(Convert.toBigDecimal(list.get(i).getEmpFieldValue())).divide(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP);
						}
						if(StringUtils.isNotBlank(list.get(i).getItemDigit()) && StringUtils.isNotBlank(list.get(i).getItemDigit())){
							int scale = Integer.parseInt(list.get(i).getItemDigit());
							int roundingMode = CarryRuleEnum.getValByKey(list.get(i).getCarryRule());
							amount = amount.setScale(scale,roundingMode);
						}
						salaryInfo.put(list.get(i).getBasicItemName(),amount);
					}
				}
			}
		}
		retInfo.put("baseInfo",baseInfo);
		retInfo.put("salaryInfo",salaryInfo);
		return retInfo;
	}

	//获取指定日期需要生效的人员id
	@Override
	public List<HrmsNewsalaryBasicitemEmpHistory> getEmpTakeEffect(String takeEffect) {
		return mapper.getEmpTakeEffect(takeEffect);
	}

	//把数据改为生效状态
	@Transactional(readOnly = false)
	@Override
	public Integer updateSetTakeEffect(List<HrmsNewsalaryBasicitemEmpHistory> historyList) {
		historyList.forEach(item->{
			item.setDataStatus("1");
			mapper.updateByPrimaryKeySelective(item);
		});
		return historyList.size();
	}

	/**
	 * 获取员工未生效定薪历史数据
	 * @param employeeId
	 * @param effectiveDate
	 * @return
	 */
	public List<HrmsNewsalaryBasicitemEmpHistory> getEmpIneffectiveItemByEmpIdAndEffect(String employeeId, String effectiveDate){
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("dataStatus","0");
		criteria.andEqualTo("effectiveDate", effectiveDate);
		return mapper.selectByExample(example);
	}

	//更新本次人员定薪调薪人员表历史表数据
	@Transactional(readOnly = false)
	@Override
	public Integer updateEmpHisttorySalaryItemByEmpIdAndEffect(String employeeId, String basicItemId, String effectiveDate, BigDecimal salaryAmount){
		Example example = new Example(HrmsNewsalaryBasicitemEmpHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andEqualTo("basicItemId",basicItemId);
		criteria.andEqualTo("effectiveDate", effectiveDate);
		HrmsNewsalaryBasicitemEmpHistory hrmsNewsalaryBasicitemEmpHistory = new HrmsNewsalaryBasicitemEmpHistory();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			hrmsNewsalaryBasicitemEmpHistory.setUpdateUser(user.getUsercode());
			hrmsNewsalaryBasicitemEmpHistory.setUpdateUserName(user.getUsername());
		}
		hrmsNewsalaryBasicitemEmpHistory.setSalaryAmount(salaryAmount);
		return mapper.updateByExampleSelective(hrmsNewsalaryBasicitemEmpHistory,example);
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}
}
