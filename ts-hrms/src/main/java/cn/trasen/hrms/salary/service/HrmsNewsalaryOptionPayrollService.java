package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.CheckPersonnel;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.DTO.SearchListTable;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionPayroll;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HrmsNewsalaryOptionPayrollService
 * @Description TODO
 * @date 2024��2��23�� ����11:39:46
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsNewsalaryOptionPayrollService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��2��23�� ����11:39:46
	 * <AUTHOR>
	 */
	Integer save(HrmsNewsalaryOptionPayroll record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��2��23�� ����11:39:46
	 * <AUTHOR>
	 */
	Integer update(HrmsNewsalaryOptionPayroll record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��2��23�� ����11:39:46
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	Integer deleteByExample(String optionId, String date);



	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsNewsalaryOptionPayroll
	 * @date 2024��2��23�� ����11:39:46
	 * <AUTHOR>
	 */
	HrmsNewsalaryOptionPayroll selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionPayroll>
	 * @date 2024��2��23�� ����11:39:46
	 * <AUTHOR>
	 */
	DataSet<HrmsNewsalaryOptionPayroll> getDataSetList(Page page, HrmsNewsalaryOptionPayroll record);


	//薪酬核算列表
    DataSet<HrmsNewsalaryOptionPayroll> calculateList(Page page,HrmsNewsalaryOptionPayroll record);
    
	//核对人员接口
	DataSet<CheckPersonnel> checkPersonnel(Page page, SearchListTable record);

	//薪酬核算定薪调薪列表表头
	List<VueTableEntity> makePayTableTitle(HrmsNewsalaryOptionPayroll record);

	//薪酬核算定薪调薪数据接口
	DataSet<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record);

	//薪酬核算-核算表头
	List<VueTableEntity> calculateWagesTitle(HrmsNewsalaryOptionPayroll record);

	//薪酬核算-核算数据
	DataSet<Map<String, String>> calculateWagesData(Page page, HrmsNewsalaryOptionPayroll record);

	//薪酬核算--开始计算
	SseEmitter startCalculation(HrmsNewsalaryOptionPayroll record);

//	void executeThread(List<HrmsNewsalaryOptionEmp> empList, String computeDate, String optionId,
//					   List<HrmsNewsalaryPayroll> payrollRecords, List<HrmsNewsalaryPayrollDetail> payrollDetails,
//					   Map<String, Map<String, BigDecimal>> empBasicSalaryMap, List<HrmsNewsalaryItem> itemList,
//					   Map<String, Map<String, Object>> handWorkSalary,String hnopId,int itemDigit,Map<String, BigDecimal> senWageMap);

	/**
	 * 根据方案id和算薪周期查询是否存在未处理薪酬项提醒数据
	 * @param optionId
	 * @param computeDate
	 * @return
	 */
	Integer checkIsExistsUnprocessedRemindData(String optionId,String computeDate);

	//完成核算
	void complete(HrmsNewsalaryOptionPayroll record);

	//根据方案id和月份查询薪酬核算状态
	HrmsNewsalaryOptionPayroll getCompleteInfo(HrmsNewsalaryOptionPayroll record);

	//根据月份和薪酬方案id查询计算到哪里了
	HrmsNewsalaryOptionPayroll getCalculationStatus(HrmsNewsalaryOptionPayroll record);

	List<VueTableEntity> makePayTableTitleCount(HrmsNewsalaryOptionPayroll record);

	DataSet<Map<String, Object>> makePayTableDataCount(Page page, HrmsNewsalaryOptionPayroll record);

	DataSet<Map<String, Object>> salaryCountData(Page page, SalaryCountSearchReq record);

	List<HrmsNewsalaryOptionPayroll> getDataByOptionId(String id);

	Integer reloadStartCalculation(HrmsNewsalaryOptionPayroll record);

    Map<String, Object> getSalaryChangesData(HrmsNewsalaryOptionPayroll record);

	void hsryExport(Page page, SearchListTable record, HttpServletRequest request, HttpServletResponse response);

	Map<String, Integer> checkPersonnelCount(SearchListTable record);

	Map<String,Map<String, Object>> makePayTableDataStatis(HrmsNewsalaryOptionPayroll record);

	/**
	 * 批量锁定工资
	 * @param ids
	 */
	void batchLockSalary(List<String> ids);

	/**
	 * 批量发送工资条
	 * @param ids
	 */
	void batchPayslip(List<String> ids);

	/**
	 * 人员确认添加人员列表
	 * @param page
	 * @param record
	 * @return
	 */
	DataSet<CheckPersonnel> getCheperList(Page page, SearchListTable record);

	/**
	 * 薪酬核算-薪酬数据确认表头
	 * @param record
	 * @return
	 */
	List<VueTableEntity> salaryConfirmTitle(HrmsNewsalaryOptionPayroll record);

	/**
	 * 薪酬核算-薪酬数据确认数据
	 * @param page
	 * @param record
	 * @return
	 */
	DataSet<Map<String, String>> salaryConfirmData(Page page, HrmsNewsalaryOptionPayroll record);

	/**
	 * 薪酬计算-预览
	 * @param employeeId
	 * @param computeDate
	 * @return
	 */
	Map<String,Object> preview(String optionId,String employeeId,String computeDate);

	DataSet<Map<String, Object>> salaryTotalCountData(Page page, SalaryCountSearchReq record);

	/**
	 * 判断员工所在方案在薪酬方案是否已锁定 1-已锁定 0-未锁定 -1 -未绑定方案
	 * @param computeDate
	 * @param employeeId
	 * @return
	 */
	Integer getIsLockByEmpComputeDate(String computeDate, String employeeId);
}
