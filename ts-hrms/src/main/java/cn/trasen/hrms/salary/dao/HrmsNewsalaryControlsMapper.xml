<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryControlsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryControls">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="send_month" jdbcType="VARCHAR" property="sendMonth" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="accounting_cycle" jdbcType="VARCHAR" property="accountingCycle" />
    <result column="accounting_status" jdbcType="VARCHAR" property="accountingStatus" />
    <result column="is_send" jdbcType="VARCHAR" property="isSend" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
</mapper>