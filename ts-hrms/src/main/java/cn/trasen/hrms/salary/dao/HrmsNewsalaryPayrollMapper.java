package cn.trasen.hrms.salary.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryPayrollVo;
import cn.trasen.hrms.salary.DTO.SalarySendOut;
import cn.trasen.hrms.salary.model.HrmsNewsalaryPayroll;
import tk.mybatis.mapper.common.Mapper;

public interface HrmsNewsalaryPayrollMapper extends Mapper<HrmsNewsalaryPayroll> {
    Integer batchInsert(List<HrmsNewsalaryPayroll> hrmsNewsalaryPayrolls);

    List<SalarySendOut> getCount(@Param("computeDate") String computeDate);

    List<Map<String, String>> getCountList(@Param("computeDate") String computeDate);


    List<HrmsNewsalaryPayroll> getRecordslist(Page page, HrmsNewsalaryPayroll record);

    Integer revocation(HrmsNewsalaryPayroll record);

    Integer singleRevocation(HrmsNewsalaryPayroll record);

    Integer singleSend(HrmsNewsalaryPayroll record);

    List<HrmsNewsalaryPayroll> getCountTitleDetails(Page page, HrmsNewsalaryPayroll record);

	List<HrmsNewsalaryPayrollVo> selectNewsalaryPayrollList(Page page, HrmsNewsalaryPayrollVo record);

//	Map<String, Object> summaryData(HrmsNewsalaryPayrollVo record);

    /**
     * 薪酬方案汇总合计统计（新） add ni.jiang
     * @param record
     * @return
     */
	List<Map<String,Object>> summaryData(HrmsNewsalaryPayrollVo record);

	List<String> selectEmpPayroll(@Param("optionId")String optionId, @Param("computeDate")String computeDate, @Param("employeeId")String employeeId, @Param("employeeIds")List<String> employeeIds);

	Map<String, Object> sendSalaryDeatilsSummaryData(HrmsNewsalaryPayrollVo record);

    List<Map<String, String>> getPayrollByEmployeeId(@Param("employeeId") String employeeId,@Param("computeDate") String computeDate);
    
    HrmsNewsalaryPayroll getPayrollByEmpId(@Param("employeeId") String employeeId, @Param("payrollDate") String computeDate, @Param("optionId")String optionIdq);
}