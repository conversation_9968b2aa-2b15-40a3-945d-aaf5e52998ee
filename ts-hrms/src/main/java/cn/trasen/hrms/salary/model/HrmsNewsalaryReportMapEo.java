package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
* 薪酬方案人员关联表
* @TableName hrms_newsalary_option_total_type
*/
@Table(name = "hrms_newsalary_report_map")
@Setter
@Getter
public class HrmsNewsalaryReportMapEo {

    /**
    * 
    */
    @Id
    private String id;
    /**
    * 薪酬项目id
    */
    @Column(name = "item_id")
    @ApiModelProperty(value = "薪酬项目id")
    private String itemId;
    /**
    * 方案id
    */
    @Column(name = "option_id")
    @ApiModelProperty(value = "方案id")
    private String optionId;

    /**
     * 薪酬汇总类型配置表ID
     */
    @Column(name = "col_id")
    @ApiModelProperty(value = "汇总报表字段ID")
    private String colId;

    /**
     * 薪酬项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;

    /**
     * 汇总报表字段编码
     */
    @Column(name = "col_code")
    @ApiModelProperty(value = "汇总报表字段编码")
    private String colCode;

    /**
     * 汇总报表字段名称
     */
    @Column(name = "col_name")
    @ApiModelProperty(value = "汇总报表字段名称")
    private String colName;
    /**
    * 创建时间
    */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    /**
    * 创建人
    */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
    * 创建人名称
    */
    private String createUserName;
    /**
    * 修改时间
    */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;
    /**
    * 修改人
    */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
    * 修改人名称
    */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;
    /**
    * 删除标识
    */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    /**
    * 机构编码
    */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
    /**
    * 机构名称
    */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 计算规则 1-关联薪酬项目 2-自定义公式
     */
    @Column(name = "item_rule")
    @ApiModelProperty(value = "计算规则 1-关联薪酬项目 2-自定义公式")
    private String itemRule;

    /**
     * 计算公式
     */
    @Column(name = "count_formula")
    @ApiModelProperty(value = "计算公式")
    private String countFormula;

    /**
     * 计算公式文本
     */
    @Column(name = "count_formula_text")
    @ApiModelProperty(value = "计算公式文本")
    private String countFormulaText;

    /**
     * 排序
     */
    @Transient
    @ApiModelProperty(value = "排序")
    private Integer sortNo;
}
