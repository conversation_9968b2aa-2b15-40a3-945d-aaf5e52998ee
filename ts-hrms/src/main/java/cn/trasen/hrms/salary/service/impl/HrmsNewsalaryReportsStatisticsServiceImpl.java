package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.salary.DTO.SalaryCountSearchReq;
import cn.trasen.hrms.salary.dao.*;
import cn.trasen.hrms.salary.model.HrmsNewsalaryReportTotalEo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionPayrollService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportsStatisticsService;
import cn.trasen.hrms.salary.utils.FormulaParse;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import com.google.common.collect.Maps;
import com.udojava.evalex.Expression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 薪酬报表统计
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsNewsalaryOptionTotalTypeServiceImpl
 * @Description TODO
 * @date 2024  0723
 */
@Slf4j
@Service
public class HrmsNewsalaryReportsStatisticsServiceImpl implements HrmsNewsalaryReportsStatisticsService {

    @Resource
    private HrmsNewsalaryItemMapper newsalaryItemMapper;

    @Autowired
    private HrmsNewsalaryReportMapMapper newsalaryReportMapMapper;

    @Resource
    private HrmsNewsalaryOptionPayrollMapper newsalaryOptionPayrollMapper;

    @Autowired
    DictItemFeignService dictItemFeignService;

    @Autowired
    private HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;

    @Override
    public List<VueTableEntity> salaryOptionTotalTitle(String reportId) {
        Assert.hasText(reportId, "reportId不能为空!");
        int width = 20;
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        List<HrmsNewsalaryReportTotalEo> totalList = newsalaryItemMapper.salaryTotalCountTitle(reportId);
        retVueTableEntity.add(new VueTableEntity("算薪周期", "payroll_date", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("方案名称", "option_name", null, 140, null));
        retVueTableEntity.add(new VueTableEntity("人员类型", "establishment_types", null, 140, null));
        retVueTableEntity.add(new VueTableEntity("算薪人数", "head_count", null, 80, null));
        if (!totalList.isEmpty() && totalList.size() > 0) {
            totalList.forEach(item -> {
                retVueTableEntity.add(new VueTableEntity(item.getColName(), item.getColCode(), null,
                        item.getColName().length() == 2 ? 80 : item.getColName().length() * width, null));
            });
        }
        return retVueTableEntity;
    }
    @Override
    public DataSet<Map<String, Object>> salaryOptionStatisticsData(Page page, SalaryCountSearchReq record) {
        if (StringUtil.isEmpty(record.getPayrollDate())) {
            record.setPayrollDate(record.getStartDate());
        }
        page.setPageNo(1);
        page.setPageSize(Integer.MAX_VALUE);

        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类型

        //获取报表配置的所有薪酬项目id
        List<String> itemIdList = new ArrayList<>();
        itemIdList.addAll(newsalaryReportMapMapper.queryItem(record.getReportId()));
        List<Map<String,String>> countFormulaItemList = newsalaryReportMapMapper.queryCountFormulaItem(record.getReportId());
        if (CollectionUtils.isNotEmpty(countFormulaItemList)){
            countFormulaItemList.forEach(map->{
                String countFormula = map.get("count_formula");
                List<String> _item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(_item)) {
                    _item.forEach(item-> {
                        Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item);
                        if (null != countFormulaCode && StringUtils.isNotEmpty(countFormulaCode.get("code"))){
                            itemIdList.add(countFormulaCode.get("code"));
                        }
                    });
                }
            });
        }

        List<HrmsNewsalaryReportTotalEo> totalList = newsalaryItemMapper.salaryTotalCountTitle(record.getReportId());
        //查询薪酬方案下的薪酬项数据
        List<Map<String, Object>> allList = new ArrayList<>();
        List<Map<String, Object>> setList = null;
        if(CollectionUtils.isNotEmpty(itemIdList)) {
            allList = newsalaryOptionPayrollMapper.salaryOptionTotalCountData(page, record, totalList, CollectionUtil.distinct(itemIdList));
            if (CollectionUtils.isNotEmpty(allList)) {
                allList.stream().forEach(vo -> {
                    //根据方案id过滤所有计算公式
                    List<Map<String, String>> optionCountFormulaList = countFormulaItemList.stream()
                            .filter(cfi -> cfi.get("option_id").equalsIgnoreCase(vo.get("option_id").toString()))
                            .collect(Collectors.toList());
                    //公式类数据计算
                    reportFormulaCalculation(vo,optionCountFormulaList);
                    //编制类型转换
                    if(vo.get("establishment_types")!= null && StringUtils.isNotEmpty(vo.get("establishment_types").toString())){
                        String[] arrs = vo.get("establishment_types").toString().split(",");
                        List<String> typeNames = new ArrayList<>(arrs.length);
                        for(String type : arrs){
                            typeNames.add(establishmentTypeDictMap.get(type));
                        }
                        vo.put("establishment_types",typeNames.stream().collect(Collectors.joining(",")));
                    }
                    vo.keySet().removeIf(key -> key.indexOf("temp_")>-1);
                });
                //汇总数据
                Map<String, Object> totalData = new HashMap<>();
//                for(String key : allList.get(0).keySet()){
//                    if(allList.get(0).get(key) instanceof Number){
//                       totalData.put(key,allList.stream().map(vo -> new BigDecimal(String.valueOf(vo.get(key)==null ? "0": vo.get(key)))).reduce(BigDecimal.ZERO,BigDecimal::add));
//                    }
//                }
                for(HrmsNewsalaryReportTotalEo en : totalList){
                    totalData.put(en.getColCode(),allList.stream().map(vo -> new BigDecimal(String.valueOf(vo.get(en.getColCode())==null ? "0": vo.get(en.getColCode())))).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                //算薪人数
                totalData.put("head_count",allList.stream().map(vo -> new BigDecimal(String.valueOf(vo.get("head_count")==null ? "0": vo.get("head_count")))).reduce(BigDecimal.ZERO,BigDecimal::add));
                totalData.put("payroll_date","合计");
                allList.add(totalData);
            }
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), allList);
    }

    @Override
    public List<VueTableEntity> salaryInsuranceTotalTitle(String reportId) {
        Assert.hasText(reportId, "reportId不能为空!");
        int width = 20;
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        List<HrmsNewsalaryReportTotalEo> totalList = newsalaryItemMapper.salaryTotalCountTitle(reportId);
        retVueTableEntity.add(new VueTableEntity("薪酬月份", "payroll_date", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("身份证号", "identity_number", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, 80, null));
        retVueTableEntity.add(new VueTableEntity("薪酬方案组", "optionName", null, 120, null));
        if (!totalList.isEmpty() && totalList.size() > 0) {
            totalList.forEach(item -> {
                retVueTableEntity.add(new VueTableEntity(item.getColName(), item.getColCode(), null,
                        item.getColName().length() == 2 ? 80 : item.getColName().length() * width, null));
            });
        }
        return retVueTableEntity;
    }
    @Override
    public DataSet<Map<String, Object>> salaryInsuranceStatisticsData(Page page, SalaryCountSearchReq record) {
       return hrmsNewsalaryOptionPayrollService.salaryTotalCountData(page,record);
    }

    /**
     * 汇总类报表公式计算
     * @param data
     * @param optionCountFormulaList
     */
    @Override
    public void reportFormulaCalculation(Map<String, Object> data, List<Map<String, String>> optionCountFormulaList){
        //公式解析
        optionCountFormulaList.forEach(map -> {
            String countFormula = map.get("count_formula");
            List<String> _item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目;
            if (CollectionUtils.isNotEmpty(_item)) {
                boolean resume = true; // 继续标识
                for (String item : _item) {
                    Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item);
                    if (null != countFormulaCode) {
                        if ("2".equals(countFormulaCode.get("type"))) {
                            if (null == data.get("temp_"+countFormulaCode.get("code"))) {
                                resume = false;
                                break;
                            }
                            countFormula = countFormula.replace(item, data.get("temp_"+countFormulaCode.get("code")).toString());
                        } else if ("4".equals(countFormulaCode.get("type"))) { // 常数
                            String formula = item;
                            int index = item.indexOf("[");
                            countFormula = countFormula.replace(item, formula.substring(0, index));
                        }
                    }
                }
                if (resume) {
                    // 用函数库拿到结果
                    countFormula = countFormula.replace("\\[\\d+\\]", "");
                    // 匹配薪酬项目
                    log.error("匹配替换前===========" + countFormula);
                    // 去掉大括号
                    countFormula = countFormula.replaceAll("[{}]", "");
                    log.error("匹配替换后 计算算式===========" + countFormula);
                    Expression expression = new Expression(countFormula);
                    // 设置小数位及取整规则
                    BigDecimal itemVal = expression.eval().setScale(2, BigDecimal.ROUND_HALF_UP);
                    data.put(map.get("col_code"), itemVal);
                }
            }
        });
    }

    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(),d.getItemName());
            }
        }
        return map;
    }
}
