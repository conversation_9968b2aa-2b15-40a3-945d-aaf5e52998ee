<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryPayrollDetailMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryPayrollDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="payroll_id" jdbcType="VARCHAR" property="payrollId" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="salary" jdbcType="DECIMAL" property="salary" />
    <result column="is_hidden" jdbcType="CHAR" property="isHidden" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>

  <!-- 批量保存  -->
  <insert id="batchInsert">
    <![CDATA[
			INSERT INTO hrms_newsalary_payroll_detail
			(
				id,
				payroll_id,
				item_id,
				item_name,
				salary,
			    option_id,
			    create_user,
			    create_user_name,
				create_date,
			    update_date,
			    update_user,
				update_user_name,
				is_deleted,
			    payroll_date
			)
			VALUES
		]]>
    <foreach collection="list" index="index" item="item" separator=",">
      <![CDATA[
			(
				#{item.id},
				#{item.payrollId},
				#{item.itemId},
				#{item.itemName},
				#{item.salary},
				#{item.optionId},
				#{item.createUser},
				#{item.createUserName},
				#{item.createDate},
				#{item.updateDate},
				#{item.updateUser},
				#{item.updateUserName},
				#{item.isDeleted},
				#{item.payrollDate}
			)
			]]>
    </foreach>
  </insert>

    <select id="getSalaryChangesData" resultType="cn.trasen.hrms.salary.DTO.UpdateSalaryVo">
        SELECT
          t1.employee_id,
          t1.payroll_date,
          t2.salary,
          t3.id AS itemId,
          t3.item_name,
          t3.item_rule,
          t4.item_group,t4.seq_no,
          <!-- CONCAT(t4.seq_no,"、",t4.item_group) as item_group, -->
          t1.option_id
        FROM hrms_newsalary_payroll t1
               LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id = t2.payroll_id
               LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id and t1.option_id = t3.option_id 
               LEFT JOIN hrms_newsalary_item_group t4 ON t3.group_id = t4.id
        WHERE t1.is_deleted='N'
          AND t1.employee_id=#{employeeId}
          AND t3.is_deleted='N'
          AND t1.option_id = #{optionId}
          AND t1.payroll_date = #{computeDate}
          ORDER BY t4.item_group, t4.seq_no,t3.sort_no
    </select>

    <select id="getCalculateWagesHistoryTitle" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
      SELECT t2.item_id,t2.item_name,t3.custom_rule
      FROM hrms_newsalary_payroll t1
      inner JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
      LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id
      LEFT JOIN hrms_newsalary_item_group t4 ON t3.group_id = t4.id
      WHERE t1.is_deleted = 'N' AND t2.is_deleted='N'
        AND t1.payroll_date = #{computeDate}
        AND t1.option_id=#{optionId}
      GROUP BY t2.item_id ,t2.item_name
      ORDER BY t4.seq_no,t3.sort_no,t3.id
    </select>

  <select id="getsalaryConfirmHistoryTitle" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryItem">
      SELECT t2.item_id,t3.item_name
      FROM hrms_newsalary_payroll t1
      LEFT JOIN hrms_newsalary_payroll_detail t2 ON t1.id=t2.payroll_id
      LEFT JOIN hrms_newsalary_item t3 ON t2.item_id = t3.id
      LEFT JOIN hrms_newsalary_item_group t4 ON t3.group_id = t4.id
      WHERE t1.is_deleted = 'N' AND t2.is_deleted='N'
        and t3.item_rule = '1'
        AND t1.payroll_date = #{computeDate}
        AND t1.option_id=#{optionId}
      GROUP BY t2.item_id,t3.item_name
      ORDER BY t4.seq_no,t3.sort_no,t3.id
    </select>
    <delete id="deleteByPayrollId" parameterType="java.lang.String">
    	delete from hrms_newsalary_payroll_detail where payroll_id = #{payrollId}
    </delete>

</mapper>