package cn.trasen.hrms.salary.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工定薪项目表
 *
 */
@Table(name = "hrms_newsalary_basicitem_emp")
@Setter
@Getter
public class HrmsNewsalaryBasicitemEmp {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;
    /**
     * 基本定薪项id
     */
    @Column(name = "basic_item_id")
    @ApiModelProperty(value = "基本定薪项id")
    private String basicItemId;
    /**
     * 字段名称- 薪酬项目基本库 hrms_newsalary_item_library 表的ID值
     */
    @Column(name = "emp_field")
    @ApiModelProperty(value = "字段名称")
    private String empField;

    /**
     * TODO 1表示开启，其它全部为关闭
     */
    @Column(name = "emp_field_value")
    @ApiModelProperty(value = "字段名称值")
    private String empFieldValue;

    @Column(name = "emp_field_value_text")
    @ApiModelProperty(value = "字段名称值中文")
    private String empFieldValueText;

    /**
     * TODO  
     */
    @Column(name = "basic_item_type")
    @ApiModelProperty(value = "类型")
    private String basicItemType;


    @Transient
    @ApiModelProperty(value = "字段名称值 字典对应的中文")
    private String empFieldText;
    /**
     * 定薪金额
     */
    @Column(name = "salary_amount")
    @ApiModelProperty(value = "定薪金额")
    private BigDecimal salaryAmount;
    /**
     * 调薪原因
     */
    @ApiModelProperty(value = "调薪原因")
    private String reason;
    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;
    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "字段名称")
    private String basicItemName;

    @Transient
    @ApiModelProperty(value = "定薪数据状态")
    private String dataStatus;
    
    @Transient
    @ApiModelProperty(value = "计算公式")
    private String countFormula;
    
    @Transient
    @ApiModelProperty(value = "自定义规则")
    private String customRule;
    
    @Transient
    @ApiModelProperty(value = "薪酬项目类型")
    private String itemRule;
    
    @Transient
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;
    
    @Transient
    @ApiModelProperty(value = "薪酬项目名称")
    private String itemName;
    
    @Transient
    @ApiModelProperty(value = "计算公式名称")
    private String countFormulaText;
    @Transient
    @ApiModelProperty(value = "下月引用当月数据")
    private String nextMonth;
    @Transient
    @ApiModelProperty(value = "加减项")
    private String countType;
    @Transient
    @ApiModelProperty(value = "取值规则：1四舍五入，2向下取整，3向上取整")
    private String carryRule;
    @Transient
    @ApiModelProperty(value = "小数点位数")
    private String itemDigit;

    /**
     * 现任聘时间
     */
    @Transient
    private String jobDeionTypeTime;
}