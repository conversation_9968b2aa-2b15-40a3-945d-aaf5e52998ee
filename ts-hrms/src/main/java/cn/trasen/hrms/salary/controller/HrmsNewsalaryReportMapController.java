package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;
import cn.trasen.hrms.salary.service.HrmsNewsalaryReportMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName HrmsNewsalaryOptionTotalTypeController
 * @Description 薪酬方案汇总类型关联Controller
 * @date 2024 0723
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬方案汇总类型关联Controller")
public class HrmsNewsalaryReportMapController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryReportMapController.class);

	@Autowired
	private HrmsNewsalaryReportMapService hrmsNewsalaryReportMapService;


	/**
	 * @Title saveOptionTotalType
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/reportMap/save")
	public PlatformResult<String> saveOptionTotalType(@RequestBody HrmsNewsalaryReportMapVo record) {
		try {
			hrmsNewsalaryReportMapService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateOptionTotalType
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/reportMap/update")
	public PlatformResult<String> updateOptionTotalType(@RequestBody HrmsNewsalaryReportMapVo record) {
		try {
			hrmsNewsalaryReportMapService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectOptionTotalTypeById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItem>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/reportMap/{id}")
	public PlatformResult<HrmsNewsalaryReportMapVo> selectOptionTotalTypeById(@PathVariable String id) {
		try {
			HrmsNewsalaryReportMapVo record = hrmsNewsalaryReportMapService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	/**
	 *
	 * @Title deleteHrmsOptionTotalType
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024 0723
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/reportMap/delete/{id}")
	public PlatformResult<String> deleteOptionTotalTypeById(@PathVariable String id) {
		try {
			hrmsNewsalaryReportMapService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectOptionTotalTypeList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<selectNewsalaryList>
	 * @date 2024 0723
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/reportMap/list")
	public DataSet<HrmsNewsalaryReportMapVo> selectOptionTotalTypeList(Page page, HrmsNewsalaryReportMapVo record) {
		return hrmsNewsalaryReportMapService.getDataSetList(page, record);
	}

	@ApiOperation(value = "查询薪酬项目列表", notes = "查询薪酬项目列表")
	@GetMapping("/api/reportMap/queryItem/{reportId}")
	public PlatformResult<List<String>> queryItem(@PathVariable String reportId) {
		try {
			List<String> list = hrmsNewsalaryReportMapService.queryItem(reportId);
			return PlatformResult.success(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
