package cn.trasen.hrms.salary.dao;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemRemindRecord;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HrmsNewsalaryItemRemindRecordMapper extends Mapper<HrmsNewsalaryItemRemindRecord> {
    /**
     * 批量保存薪酬薪资提醒
     * @param list
     * @return
     */
    Integer batchInsert(List<HrmsNewsalaryItemRemindRecord> list);
    /**
     * @Title: getList
     * @Description: 查询薪酬薪资提醒列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryItemRemindRecord> getList(Page page, HrmsNewsalaryItemRemindRecord entity);

    /**
     * @Title: getList
     * @Description: 查询薪酬薪资提醒列表
     * @param entity
     * @Return List<HrmsEmployeeTemporary>
     * <AUTHOR>
     * @date 2020年4月15日 下午2:45:21
     */
    List<HrmsNewsalaryItemRemindRecord> getList(HrmsNewsalaryItemRemindRecord entity);

    /**
     * 汇总统计员工薪酬异动数据
     * @param entity
     * @return
     */
    List<HrmsNewsalaryItemRemindRecord> getEmployeeSalaryRemindStatistics(Page page,HrmsNewsalaryItemRemindRecord entity);
}