package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryFirststepHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalaryFirststepHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryFirststepHistoryController
 * @Description TODO
 * @date 2024��3��11�� ����5:03:18
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsNewsalaryFirststepHistoryController")
public class HrmsNewsalaryFirststepHistoryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryFirststepHistoryController.class);

	@Autowired
	private HrmsNewsalaryFirststepHistoryService hrmsNewsalaryFirststepHistoryService;

	/**
	 * @Title saveHrmsNewsalaryFirststepHistory
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newsalaryfirststephistory/save")
	public PlatformResult<String> saveHrmsNewsalaryFirststepHistory(@RequestBody HrmsNewsalaryFirststepHistory record) {
		try {
			hrmsNewsalaryFirststepHistoryService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryFirststepHistory
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newsalaryfirststephistory/update")
	public PlatformResult<String> updateHrmsNewsalaryFirststepHistory(@RequestBody HrmsNewsalaryFirststepHistory record) {
		try {
			hrmsNewsalaryFirststepHistoryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryFirststepHistoryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryFirststepHistory>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newsalaryfirststephistory/{id}")
	public PlatformResult<HrmsNewsalaryFirststepHistory> selectHrmsNewsalaryFirststepHistoryById(@PathVariable String id) {
		try {
			HrmsNewsalaryFirststepHistory record = hrmsNewsalaryFirststepHistoryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryFirststepHistoryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newsalaryfirststephistory/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryFirststepHistoryById(@PathVariable String id) {
		try {
			hrmsNewsalaryFirststepHistoryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryFirststepHistoryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryFirststepHistory>
	 * @date 2024��3��11�� ����5:03:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newsalaryfirststephistory/list")
	public DataSet<HrmsNewsalaryFirststepHistory> selectHrmsNewsalaryFirststepHistoryList(Page page, HrmsNewsalaryFirststepHistory record) {
		return hrmsNewsalaryFirststepHistoryService.getDataSetList(page, record);
	}
}
