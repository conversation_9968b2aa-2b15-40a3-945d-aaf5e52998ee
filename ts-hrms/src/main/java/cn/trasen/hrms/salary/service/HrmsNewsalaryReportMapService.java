package cn.trasen.hrms.salary.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.salary.DTO.HrmsNewsalaryReportMapVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HrmsNewsalaryReportMapService {

    Integer save(HrmsNewsalaryReportMapVo record);

    Integer update(HrmsNewsalaryReportMapVo record) ;

    HrmsNewsalaryReportMapVo selectById(String id) ;

    Integer deleteById(String id);

    DataSet<HrmsNewsalaryReportMapVo> getDataSetList(Page page, HrmsNewsalaryReportMapVo record) ;

    List<String> queryItem(String reportId);
}
