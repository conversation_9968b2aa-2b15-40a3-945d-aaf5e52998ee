package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * TODO 薪酬控制，已废弃？
 *
 */
@Table(name = "hrms_newsalary_controls")
@Setter
@Getter
public class HrmsNewsalaryControls {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 月份
     */
    @Column(name = "send_month")
    @ApiModelProperty(value = "月份")
    private String sendMonth;

    /**
     * 薪酬组id
     */
    @Column(name = "option_id")
    @ApiModelProperty(value = "薪酬组id")
    private String optionId;

    /**
     * 算薪周期
     */
    @Column(name = "accounting_cycle")
    @ApiModelProperty(value = "算薪周期")
    private String accountingCycle;

    /**
     * 1未启动2已计算3已完成4已锁定5解除锁定
     */
    @Column(name = "accounting_status")
    @ApiModelProperty(value = "1未启动2已计算3已完成4已锁定5解除锁定")
    private String accountingStatus;

    /**
     * 是否发送工资条 0 未发送 1已发送
     */
    @Column(name = "is_send")
    @ApiModelProperty(value = "是否发送工资条 0 未发送 1已发送")
    private String isSend;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}