package cn.trasen.hrms.salary.enums;

import lombok.Getter;

/**
 * 启用禁用枚举类
 */
@Getter
public enum CarryRuleEnum {

    //四舍五入
    ROUND("1", 4),

    //向下取数
    DOWN("2", 3),

    //向上取数
    UP("3", 2);

    private final String key;
    private final int val;

    private CarryRuleEnum(String key, int val) {
        this.key = key;
        this.val = val;
    }

    /**
     * @Title: getValByKey
     * @Description: 根据key获得val值
     * @Param: key
     * @Return: String
     * <AUTHOR>
     */
    public static int getValByKey(String key) {
        for (CarryRuleEnum item : CarryRuleEnum.values()) {
            if (item.key.equals(key)) {
                return item.val;
            }
        }
        return 0;
    }

    /**
     * @Title: getKeyByVal
     * @Description: 根据val获得key值
     * @param val
     * @Return String
     * <AUTHOR>
     * @date 2020年6月17日 下午5:30:51
     */
    public static String getKeyByVal(int val) {
        for (CarryRuleEnum item : CarryRuleEnum.values()) {
            if (item.val == val) {
                return item.key;
            }
        }
        return "";
    }
}
