package cn.trasen.hrms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.model.HrmsPersonnelTransaction;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExcelExportOfTemplateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**    
  * <P> @Description: 人员异动控制器</p>
  * <P> @Date: 2020年9月3日  上午10:45:12 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Slf4j
@Api(tags = "人员异动Controller ")
@RestController
public class HrmsPersonnelTransactionController {

	@Autowired
	HrmsPersonnelTransactionService hrmsPersonnelTransactionService;
	
	@Autowired
	private HrmsDictInfoService hrmsDictInfoService;




	/**
	 * @Title: getDataList
	 * @Description: 查询异动记录列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: DataSet<HrmsPersonnelTransaction>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询异动记录列表(分页)", notes = "查询异动记录列表(分页)")
	@PostMapping(value = "/personnelTransaction/list")
	public DataSet<HrmsPersonnelTransaction> getDataList(Page page, HrmsPersonnelTransaction entity) {
		List<HrmsPersonnelTransaction> list = hrmsPersonnelTransactionService.getDataList(page, entity);
		return new DataSet<HrmsPersonnelTransaction>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	//异动人员导出
	
	@ApiOperation(value = "异动人员导出数据", notes = "异动人员导出数据")
	@GetMapping(value = "/personnelTransaction/downLoad")
	public void downLoad(Page page, HrmsPersonnelTransaction entity,HttpServletRequest request,
            HttpServletResponse response) {
	        try {
	        	/*page.setPageSize(Integer.MAX_VALUE);
	        	String name = "异动人员数据数据.xls";
	        	String _val = entity.getSarchDate();
				if(!StringUtil.isEmpty(_val)) {
					_val.replace("-", "年");
					name = _val + "月异动人员数据数据.xls";
				}
				List<HrmsPersonnelTransaction> list = hrmsPersonnelTransactionService.getDataList(page, entity);
			
				if(list != null && list.size() > 0) {
					for (int i = 0; i < list.size(); i++) {
						list.get(i).setNo(i+1);
						list.get(i).setCreateDateStr(DateUtils.getStringDateShort(list.get(i).getCreateDate()));
					}
				}
				String templateUrl = "template/ryydtzd.xls"; // 模板位置
	            if (CollectionUtils.isNotEmpty(list)) {
	                ExportUtil.export(request, response, list, name, templateUrl);
	            }else {
	            	ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
	            }*/
	        	   TemplateExportParams params = new TemplateExportParams("template/ryydtzd.xls");
	        	   
	        	   page.setPageSize(Integer.MAX_VALUE);
	        	   
	        	   String name = "异动人员数据数据.xls";
		           String _val = entity.getSarchDate();
					if(!StringUtil.isEmpty(_val)) {
						 String[] split = _val.split("~");
						 entity.setEffectiveStartDate(split[0].trim());
		        		 entity.setEffectiveEndDate(split[1].trim());
						name = _val + " 异动人员数据数据.xls";
					}else{
						_val = "";
					}
	        	   name = new String(name.getBytes("UTF-8"), "ISO8859-1");
	        	   
	        	   
	        	   List<HrmsPersonnelTransaction> list = hrmsPersonnelTransactionService.getDataList(page, entity);
	        		if(list != null && list.size() > 0) {
						for (int i = 0; i < list.size(); i++) {
							list.get(i).setNo(i+1);
							list.get(i).setCreateDateStr(DateUtils.getStringDateShort(list.get(i).getCreateDate()));
						}
					}
	        	   
	        	   Map<String,Object> resultMap = new HashMap<String, Object>();
	        	   resultMap.put("list", list);
	        	   resultMap.put("date", _val);
	        	   Workbook workbook = exportExcel(params, resultMap);
	        	   response.setContentType("application/msword");
	    		   response.setCharacterEncoding("UTF-8");
	    		   response.setHeader("Content-disposition", "attachment; filename=" + name);
	    		   ServletOutputStream fos = response.getOutputStream();
	    		   workbook.write(fos);
	    		   fos.flush();
	        } catch (Exception e) {
	        	log.error("异动人员导出异常"+e.getMessage(),e);
	        }
	}
	
	private static Workbook exportExcel(TemplateExportParams params, Map<String, Object> map) {
        return new ExcelExportOfTemplateUtil().createExcleByTemplate(params, null, null, map);
    }

	
	
	
    @ApiOperation(value = "异动记录查询下拉框", notes = "异动记录查询下拉框")
    @PostMapping("/personnelTransaction/loadchangeselect")
    public DataSet<DictItemResp> getDataList(Page page) {
    	page.setPageSize(Integer.MAX_VALUE);
    	page.setTotalCount(1);
    	List<DictItemResp> list = new ArrayList<DictItemResp>();
    	try {
    		
    		List<String> loadChangeSelect = hrmsPersonnelTransactionService.loadChangeSelect();
			if(!loadChangeSelect.contains("员工入职")) {
				loadChangeSelect.add("员工入职");
			}
			//加上调动记录的 值
			Map<String, String> employeeTransferTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_TRANSFER_TYPE); // 调动类型
			for(String key:employeeTransferTypeMap.keySet()){
				if(!loadChangeSelect.contains(employeeTransferTypeMap.get(key))) {
					loadChangeSelect.add(employeeTransferTypeMap.get(key));
				}
			}
    		
			loadChangeSelect.forEach(item ->{
				DictItemResp dr = new DictItemResp();
				dr.setItemName(item);
				dr.setItemCode(item);
				list.add(dr);
			});
    	}catch(Exception e) {
    		e.printStackTrace();
    	}
    	return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }
	
	
	@ApiOperation(value = "添加人员异动接口", notes = "添加人员异动接口")
	@GetMapping(value = "/personnelTransaction/save")
	public PlatformResult<String> save(@RequestBody HrmsPersonnelTransaction entity) {
		try {
			hrmsPersonnelTransactionService.insert(entity);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
}
