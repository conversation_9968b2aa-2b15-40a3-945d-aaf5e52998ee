package cn.trasen.hrms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsDictInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**   
 * @Title: HrmsDictInfoController.java 
 * @Package cn.trasen.hrms.controller 
 * @Description: 数据字典 Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月23日 下午5:19:23 
 * @version V1.0   
 */
@Slf4j
@Api(tags = "数据字典Controller")
@RestController
public class HrmsDictInfoController {

	@Autowired
	private HrmsDictInfoService hrmsDictInfoService;
//
//	/**
//	 * @Title: insert
//	 * @Description: 新增数据字典
//	 * @Param: entity
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "新增数据字典", notes = "新增数据字典")
//	@PostMapping(value = "/dictInfo/save")
//	public PlatformResult<String> insert(@RequestBody HrmsDictInfo entity) {
//		try {
//			PlatformResult<String> result = hrmsDictInfoService.validate(entity);
//			if (!result.isSuccess()) {
//				return result;
//			}
//			if (hrmsDictInfoService.insert(entity) > 0) {
//				return PlatformResult.success();
//			}
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}

//	/**
//	 * @Title: update
//	 * @Description: 修改数据字典
//	 * @Param: entity
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "修改数据字典", notes = "修改数据字典")
//	@PostMapping(value = "/dictInfo/update")
//	public PlatformResult<String> update(@RequestBody HrmsDictInfo entity) {
//		try {
//			PlatformResult<String> result = hrmsDictInfoService.validate(entity);
//			if (!result.isSuccess()) {
//				return result;
//			}
//			if (hrmsDictInfoService.update(entity) > 0) {
//				return PlatformResult.success();
//			}
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//		}
//		return PlatformResult.failure();
//	}

//	/**
//	 * @Title: deleteById
//	 * @Description: 删除数据字典
//	 * @Param: dictInfoId 数据字典主键ID
//	 * @Return: PlatformResult<String>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "删除数据字典", notes = "删除数据字典")
//	@PostMapping(value = "/dictInfo/deletedById/{dictInfoId}")
//	public PlatformResult<String> deleteById(@PathVariable String dictInfoId) {
//		try {
//			hrmsDictInfoService.deleted(dictInfoId);
//			return PlatformResult.success();
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//			return PlatformResult.failure();
//		}
//	}

//	/**
//	 * @Title: getDataList
//	 * @Description: 查询数据字典列表
//	 * @Param: page
//	 * @param entity
//	 * @Return: DataSet<HrmsDictInfo>
//	 * <AUTHOR>
//	 */
//	@ApiOperation(value = "数据字典列表", notes = "数据字典列表")
//	@PostMapping(value = "/dictInfo/list")
//	public DataSet<HrmsDictInfo> getDataList(Page page, HrmsDictInfo entity) {
//		List<HrmsDictInfo> list = hrmsDictInfoService.getDataList(page, entity);
//		return new DataSet<HrmsDictInfo>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
//	}

	/**
	 * @Title: getDictInfoCombobox
	 * @Description: 数据字典列表(下拉列表)
	 * @Param: dictType 数据字典类型
	 * @Return: PlatformResult<Object>
	 * <AUTHOR>
	 * @date 2020年3月26日 下午2:33:10
	 */
	@ApiOperation(value = "数据字典列表(下拉列表)", notes = "数据字典列表(下拉列表)")
	@PostMapping(value = "/dict/combobox/{dictType}")
	public PlatformResult<List<HrmsDictInfo>> getDictInfoCombobox(@PathVariable String dictType) {
		try {
			List<HrmsDictInfo> list = hrmsDictInfoService.getDictInfoListByDictType(dictType);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
//
//	/**
//	 * @Title: excelImportDictInfo
//	 * @Description: excel导入数据字典
//	 * @param file
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月14日 下午12:00:03
//	 */
//	@ApiOperation(value = "excel导入数据字典", notes = "excel导入数据字典")
//	@PostMapping(value = "/dict/excelImportDictInfo")
//	public PlatformResult<String> excelImportDictInfo(@RequestParam("file") MultipartFile file) {
//		try {
//			@SuppressWarnings("unchecked")
//			List<HrmsDictInfo> list = (List<HrmsDictInfo>) ImportExcelUtil.getExcelDatas(file, HrmsDictInfo.class);
//			return hrmsDictInfoService.excelImportDictInfo(list);
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//			return PlatformResult.failure();
//		}
//	}

//	/**
//	 * @Title: initDictCache
//	 * @Description: 初始化缓存
//	 * @return
//	 * @Return PlatformResult<String>
//	 * <AUTHOR>
//	 * @date 2020年4月16日 下午2:06:12
//	 */
//	@ApiOperation(value = "初始化缓存", notes = "初始化缓存")
//	@PostMapping(value = "/dict/cache/init")
//	public PlatformResult<String> initDictCache() {
//		try {
//			hrmsDictInfoService.initCache();
//			return PlatformResult.success();
//		} catch (Exception e) {
//			log.error(e.getMessage(), e);
//			return PlatformResult.failure();
//		}
//	}

}
