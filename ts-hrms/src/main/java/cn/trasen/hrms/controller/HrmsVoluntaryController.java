package cn.trasen.hrms.controller;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.model.HrmsVoluntary;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsVoluntaryService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExportExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Title: HrmsVoluntaryController.java
 * @Package cn.trasen.hrms.controller
 * @Description: 志愿者Controller
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年3月13日 上午9:59:26
 * @version V1.0
 */
@Api(tags = "志愿者Controller")
@RestController
public class HrmsVoluntaryController {

	private static final Logger logger = LoggerFactory.getLogger(HrmsVoluntaryController.class);

	@Autowired
	private HrmsVoluntaryService hrmsVoluntaryService;

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	/*
	 * @SuppressWarnings("unchecked")
	 * 
	 * @ApiOperation(value = "志愿者导入", notes = "志愿者导入")
	 * 
	 * @PostMapping(value = "/voluntary/excelImport") public PlatformResult<String>
	 * excelImportEmployee(@RequestParam("file") MultipartFile file) { try {
	 * List<HrmsVoluntary> list = (List<HrmsVoluntary>)
	 * ImportExcelUtil.getExcelDatas(file, HrmsVoluntary.class); if(list != null &&
	 * list.size() >0) { //获取所有人员的工号 HrmsEmployee re = new HrmsEmployee();
	 * List<HrmsEmployee> beanList = hrmsEmployeeService.getList(re); List<String>
	 * empList =
	 * beanList.stream().map(HrmsEmployee::getEmployeeNo).collect(Collectors.toList(
	 * )); for(int i = 0; i<list.size(); i++) {
	 * if(StringUtil.isEmpty(list.get(i).getEmployeeNo())) { throw new Exception(""
	 * + list.get(i).getEmployeeName() + "的工号为空！"); }
	 * if(!empList.contains(list.get(i).getEmployeeNo())) { throw new Exception("" +
	 * list.get(i).getEmployeeName() + "的工号在系统中没有找到"); }
	 * if(list.get(i).getVoluntaryDate() == null) {
	 * if(!StringUtil.isEmpty(list.get(i).getVoluntaryDateText())) { try {
	 * SimpleDateFormat simpleDateFormat1 = new
	 * SimpleDateFormat("yyyy-MM-dd");//注意月份是MM Date date =
	 * simpleDateFormat1.parse(list.get(i).getVoluntaryDateText());
	 * list.get(i).setVoluntaryDate(date); } catch (Exception e) { try {
	 * SimpleDateFormat simpleDateFormat2 = new
	 * SimpleDateFormat("yyyy.MM.dd");//注意月份是MM Date date =
	 * simpleDateFormat2.parse(list.get(i).getVoluntaryDateText());
	 * list.get(i).setVoluntaryDate(date); } catch (Exception e1) { try {
	 * SimpleDateFormat simpleDateFormat2 = new
	 * SimpleDateFormat("yyyy/MM/dd");//注意月份是MM Date date =
	 * simpleDateFormat2.parse(list.get(i).getVoluntaryDateText());
	 * list.get(i).setVoluntaryDate(date); } catch (Exception e2) { throw new
	 * Exception("" + list.get(i).getEmployeeName() + "的志愿者时间格式不对"); }
	 * 
	 * }
	 * 
	 * } } } }
	 * 
	 * String excelImport = hrmsVoluntaryService.excelImport(list); return
	 * PlatformResult.success(excelImport); } } catch (Exception e) {
	 * logger.error(e.getMessage(), e); return
	 * PlatformResult.failure(e.getMessage()); } return PlatformResult.failure(); }
	 */

	@ApiOperation(value = "删除志愿者信息", notes = "删除志愿者")
	@PostMapping(value = "/voluntary/deletedById/{id}")
	@ResponseBody
	public PlatformResult<String> deleteById(@PathVariable String id) {
		try {
			hrmsVoluntaryService.deleted(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询账号信息列表
	 * @Param: page
	 * @param entity
	 * @Return: DataSet<HrmsVoluntary>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@PostMapping(value = "/voluntary/list")
	public DataSet<HrmsVoluntary> getDataList(Page page, HrmsVoluntary entity) {

		/*
		 * if(!StringUtil.isEmpty(entity.getOwn()) && "1".equals(entity.getOwn())) {
		 * //查自己 entity.setEmployeeNo(UserInfoHolder.getCurrentUserCode()); }
		 */

		List<HrmsVoluntary> list = hrmsVoluntaryService.getDataList(page, entity);
		return new DataSet<HrmsVoluntary>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	@ApiOperation(value = "志愿者导出模板", notes = "志愿者导出模板")
	@GetMapping(value = "/voluntary/improtTemplate")
	public void improtTemplate(HttpServletRequest request, HttpServletResponse response) {
		String name = "志愿者导入数据模板.xls";
		String templateUrl = "template/zyzTemplate.xls"; // 模板位置
		try {
			ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
		} catch (Exception e) {
			logger.error("导出志愿者模板异常" + e.getMessage(), e);
		}
	}

	@ApiOperation(value = "志愿者导出数据", notes = "志愿者导出数据")
	@GetMapping(value = "/voluntary/downLoad")
	public void downLoad(Page page, HrmsVoluntary entity, HttpServletRequest request, HttpServletResponse response) {
		page.setPageSize(Integer.MAX_VALUE);

		try {
			String name = "志愿者活动数据.xls";
			List<HrmsVoluntary> list = hrmsVoluntaryService.getDataList(page, entity);
			Double count = 0d;
			if (list != null && list.size() > 0) {
				Integer no = 1;
				for (HrmsVoluntary bean : list) {
					bean.setNo(no);
					no++;
					count += bean.getVoluntaryDuration();
					bean.setVoluntaryDateStr(bean.getVoluntaryDate());
				}
			}
			HrmsVoluntary countBean = new HrmsVoluntary();
			countBean.setVoluntaryDate("时长合计:");
			countBean.setVoluntaryDuration(count);
			list.add(countBean);
			String templateUrl = "template/zyzDataTemplate.xls"; // 模板位置
			if (CollectionUtils.isNotEmpty(list)) {
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			logger.error("志愿者填报导出异常" + e.getMessage(), e);
		}
	}

	@ApiOperation(value = "志愿者导出数据", notes = "志愿者导出数据")
	@GetMapping(value = "/voluntary/downLoad2")
	public void downLoad2(Page page, HrmsVoluntary entity, HttpServletRequest request, HttpServletResponse response) {
		page.setPageSize(Integer.MAX_VALUE);

		try {
			List<HrmsVoluntary> list = new ArrayList<>();
			list = hrmsVoluntaryService.getDataList(page, entity);
			Map<String, List<HrmsVoluntary>> maps = list.stream()
					.collect(Collectors.groupingBy(HrmsVoluntary::getEmployeeId));
			// ExportExcel
			// excel标题
			String title = "志愿者活动统计";
			// excel列头信息
			String[] rowsName = new String[] { "序号", "姓名", "手机号", "工号", "性别", "邮箱", "生日", "政治面貌", "身份证", "科室","总时长"};
			List<Object[]> dataList = new ArrayList<Object[]>();
			Object[] objs = null;
			List<String> listTitile = new ArrayList<>();
			List<String> minTitle = new ArrayList<>();
			minTitle.add("活动时间");
			minTitle.add("活动时长");
			minTitle.add("活动名称");
			listTitile.addAll(minTitle);
			int no = 1;
			 for(List<HrmsVoluntary> obj: maps.values()){
				if(obj != null && obj.size() > 0) {
					objs = new Object[rowsName.length];
					objs[0] = no;
					objs[1] = !StringUtil.isEmptyObj(obj.get(0).getEmployeeName())? obj.get(0).getEmployeeName() : " ";
					objs[2] = !StringUtil.isEmptyObj(obj.get(0).getPhoneNumber())? obj.get(0).getPhoneNumber() : " ";
					objs[3] = !StringUtil.isEmptyObj(obj.get(0).getEmployeeNo())? obj.get(0).getEmployeeNo() : " ";
					objs[4] = !StringUtil.isEmptyObj(obj.get(0).getGender())? obj.get(0).getGender() : " ";
					objs[5] = !StringUtil.isEmptyObj(obj.get(0).getEmail())? obj.get(0).getEmail() : " ";
					objs[6] = !StringUtil.isEmptyObj(obj.get(0).getBirthday())? obj.get(0).getBirthday() : " ";
					objs[7] = !StringUtil.isEmptyObj(obj.get(0).getPoliticalStatus())? obj.get(0).getPoliticalStatus() : " ";
					objs[8] = !StringUtil.isEmptyObj(obj.get(0).getIdentityNumber())? obj.get(0).getIdentityNumber() : " ";
					objs[9] = !StringUtil.isEmptyObj(obj.get(0).getEmployeeDept())? obj.get(0).getEmployeeDept() : " ";
					objs[10] = " ";
					List<Object> strs = new ArrayList<>();
					Double count = 0d;
					for(int k=0;k< obj.size();k++) {
						count += obj.get(k).getVoluntaryDuration();
						String A = !StringUtil.isEmptyObj(obj.get(k).getVoluntaryDate())? obj.get(k).getVoluntaryDate() : " ";
						Double B = !StringUtil.isEmptyObj(obj.get(k).getVoluntaryDuration())? obj.get(k).getVoluntaryDuration() : 0d;
						String C = !StringUtil.isEmptyObj(obj.get(k).getRemark())? obj.get(k).getRemark() : " ";
						strs.add(A);
						strs.add(B);
						strs.add(C);
						if(listTitile.size() / 3  < obj.size()) {  // 添加表头
							listTitile.addAll(minTitle);
						}
					}
					objs[10] = count;
					List<Object> asList = new ArrayList<>(Arrays.asList(objs));
					asList.addAll(strs);  //固定列合并
					dataList.add(asList.toArray());
				}
				no++;
			}
			 List<String> rowsNameList =new ArrayList<>( Arrays.asList(rowsName));
			 rowsNameList.addAll(listTitile);
			 String[] strTitleArr = new String[rowsNameList.size()];
			 rowsNameList.toArray(strTitleArr);
			String fileName = "志愿者活动统计" + DateUtils.getStringDateShort(new Date()) + ".xls";
			// 告诉浏览器数据格式，将头和数据传到前台
			String headStr = "attachment; filename=\"" + new String(fileName.getBytes("UTF-8"), "ISO8859-1") + "\"";
			response.setContentType("APPLICATION/OCTET-STREAM");
			response.setHeader("Content-Disposition", headStr);
			OutputStream out = response.getOutputStream();

			// 调用poi的工具类
			ExportExcel ex = new ExportExcel(title, strTitleArr, dataList);
			try {
				ex.export(out);
			} catch (Exception e) {
				e.printStackTrace();
			}
			out.flush();
			out.close();
			return;
		} catch (Exception e) {
			logger.error("志愿者填报导出异常" + e.getMessage(), e);
		}
	}

	@ApiOperation(value = "志愿者导出统计数据", notes = "志愿者导出统计数据")
	@GetMapping(value = "/voluntary/downLoadCount")
	public void downLoadCount(Page page, HrmsVoluntary entity, HttpServletRequest request,
			HttpServletResponse response) {
		try {
			String name = "志愿者统计数据.xls";
			if (!StringUtil.isEmpty(entity.getEmployeeName())) {
				entity.setEmployeeName(entity.getEmployeeName().trim());
			}
			if (!StringUtil.isEmpty(entity.getEmployeeDept())) {
				entity.setEmployeeDept(entity.getEmployeeDept().trim());
			}
			page.setPageSize(Integer.MAX_VALUE);
			List<HrmsVoluntary> list = hrmsVoluntaryService.getDataCountList(page, entity);

			Double count = 0d;
			Integer no = 1;
			if (list != null && list.size() > 0) {
				for (HrmsVoluntary bean : list) {
					bean.setNo(no);
					no++;
					count += bean.getVoluntaryDuration();
					bean.setVoluntaryDateStr(bean.getVoluntaryDate());
				}
			}
			HrmsVoluntary countBean = new HrmsVoluntary();
			countBean.setEmployeeDept("时长合计:");
			countBean.setVoluntaryDuration(count);
			list.add(countBean);
			String templateUrl = "template/zyzDataTemplate.xls"; // 模板位置
			if (CollectionUtils.isNotEmpty(list)) {
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			logger.error("个人工资导出异常" + e.getMessage(), e);
		}
	}

	@ApiOperation(value = "列表", notes = "列表")
	@PostMapping(value = "/voluntary/countList")
	public DataSet<HrmsVoluntary> getDataCountList(Page page, HrmsVoluntary entity) {
		if (!StringUtil.isEmpty(entity.getEmployeeName())) {
			entity.setEmployeeName(entity.getEmployeeName().trim());
		}
		if (!StringUtil.isEmpty(entity.getEmployeeDept())) {
			entity.setEmployeeDept(entity.getEmployeeDept().trim());
		}

		List<HrmsVoluntary> list = hrmsVoluntaryService.getDataCountList(page, entity);
		return new DataSet<HrmsVoluntary>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	// 添加志愿者
	@ApiOperation(value = "新增志愿者信息", notes = "新增志愿者信息")
	@PostMapping(value = "/voluntary/save")
	public PlatformResult<String> insert(@RequestBody HrmsVoluntary entity) {
		try {
			if (hrmsVoluntaryService.insert(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	// 编辑志愿者
	// 添加志愿者
	@ApiOperation(value = "新增志愿者信息", notes = "新增志愿者信息")
	@PostMapping(value = "/voluntary/update")
	public PlatformResult<String> update(@RequestBody HrmsVoluntary entity) {
		try {
			if (hrmsVoluntaryService.update(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

}
