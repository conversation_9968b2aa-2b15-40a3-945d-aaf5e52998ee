package cn.trasen.hrms.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.model.HrmsWarningRecord;
import cn.trasen.hrms.service.HrmsWarningRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsWarningRecordController
 * @Description TODO
 * @date 2021��10��14�� ����3:25:27
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsWarningRecordController")
public class HrmsWarningRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsWarningRecordController.class);

	@Autowired
	private HrmsWarningRecordService hrmsWarningRecordService;

	/**
	 * @Title saveHrmsWarningRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��10��14�� ����3:25:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/warningRecord/save")
	public PlatformResult<String> saveHrmsWarningRecord(@RequestBody HrmsWarningRecord record) {
		try {
			hrmsWarningRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsWarningRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��10��14�� ����3:25:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/warningRecord/update")
	public PlatformResult<String> updateHrmsWarningRecord(@RequestBody HrmsWarningRecord record) {
		try {
			hrmsWarningRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsWarningRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsWarningRecord>
	 * @date 2021��10��14�� ����3:25:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/warningRecord/{id}")
	public PlatformResult<HrmsWarningRecord> selectHrmsWarningRecordById(@PathVariable String id) {
		try {
			HrmsWarningRecord record = hrmsWarningRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title: selectCountRecordByUserCode
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return PlatformResult<HrmsWarningRecord> 返回类型
	 * 2021年11月15日
	 * ADMIN
	 * @throws
	 */
	@ApiOperation(value = "首页统计接口", notes = "首页统计接口")
	@GetMapping("/api/warningRecord/selectCountRecordByUserCode")
	public PlatformResult<Long> selectCountRecordByUserCode() {
		try {
			return PlatformResult.success(hrmsWarningRecordService.selectCountRecordByUserCode(UserInfoHolder.getCurrentUserCode()));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title deleteHrmsWarningRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2021��10��14�� ����3:25:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/warningRecord/delete/{id}")
	public PlatformResult<String> deleteHrmsWarningRecordById(@PathVariable String id) {
		try {
			hrmsWarningRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsWarningRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsWarningRecord>
	 * @date 2021��10��14�� ����3:25:27
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/warningRecord/list")
	public DataSet<HrmsWarningRecord> selectHrmsWarningRecordList(Page page, HrmsWarningRecord record) {
		return hrmsWarningRecordService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @Title: exportHrmsWarningRecord
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param page
	 * @param @param record
	 * @param @param response
	 * @param @param request 参数
	 * @return void 返回类型
	 * 2021年10月18日
	 * ADMIN
	 * @throws
	 */
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	@GetMapping("/api/warningRecord/exportHrmsWarningRecord")
	public void exportHrmsWarningRecord(Page page, HrmsWarningRecord record,HttpServletResponse response, HttpServletRequest request) {
		try {
			page.setPageNo(1);
			page.setPageSize(10000);
			page.setSidx("create_date");
			page.setSord("desc");
			DataSet<HrmsWarningRecord> list = hrmsWarningRecordService.getDataSetList(page,record);
			
			for (int i = 0; i < list.getRows().size(); i++) {
				HrmsWarningRecord hrmsWarningRecord = list.getRows().get(i);
				hrmsWarningRecord.setPm(i + 1);
				if("0".equals(hrmsWarningRecord.getEmpSex())) {
					hrmsWarningRecord.setEmpSex("男");
				}else if("1".equals(hrmsWarningRecord.getEmpSex())) {
					hrmsWarningRecord.setEmpSex("女");
				}else {
					hrmsWarningRecord.setEmpSex("未知");
				}
				if("2".equals(hrmsWarningRecord.getWarningStatus())) {
					hrmsWarningRecord.setWarningStatus("已处理");
				}else if("1".equals(hrmsWarningRecord.getWarningStatus())) {
					hrmsWarningRecord.setWarningStatus("已提醒");
				}else {
					hrmsWarningRecord.setWarningStatus("提醒中");
				}
			}
			//表头标题
            String name = "预警提醒" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
            // 模板位置
            String templateUrl = "template/hrmsWarningRecord.xlsx";
            
		    Map<String, Object> map = new HashMap<String, Object>();
		    map.put("list", list.getRows());
		    map.put("exportDate", DateUtil.format(new Date(),"yyyy-MM-dd"));
		    map.put("exportUserName", UserInfoHolder.getCurrentUserName());
            
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
