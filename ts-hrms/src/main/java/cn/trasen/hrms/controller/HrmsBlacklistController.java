package cn.trasen.hrms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsBlacklist;
import cn.trasen.hrms.service.HrmsBlacklistService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**    
  * <P> @Description: 黑名单管理controller层</p>
  * <P> @Date: 2020年3月11日  上午11:22:20 </p>
  * <P> @Author: panqic </p>
  * <P> @Company: 湖南爱笑恩信息科技有限公司 </p>
  * <P> @version V1.0    </p> 
  */
@Slf4j
@Api(tags = "黑名单管理controller")
@RestController
public class HrmsBlacklistController {

	@Autowired
	private HrmsBlacklistService hrmsBlacklistService;

	/**
	 * 
	 * <p> @Title: getDataList</p>
	 * <p> @Description: 获取列表</p>
	 * <p> @param page
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: DataSet<PersonnelmgrBlacklist></p>
	 * <p> <AUTHOR>
	 */
	@ApiOperation(value = "获取黑名单管理列表", notes = "获取黑名单管理列表")
	@PostMapping("/blacklist/getDataList")
	public DataSet<HrmsBlacklist> getDataList(Page page, HrmsBlacklist hrmsPersonnelmgrBlacklist) {
		List<HrmsBlacklist> list = hrmsBlacklistService.getDataList(page, hrmsPersonnelmgrBlacklist);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}

	/**
	 * 
	 * <p> @Title: insert</p>
	 * <p> @Description: 新增</p>
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: PlatformResult<String></p>
	 * <p> <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/blacklist/save")
	public PlatformResult<String> insert(@RequestBody HrmsBlacklist hrmsPersonnelmgrBlacklist) {
		try {
			if (hrmsBlacklistService.insert(hrmsPersonnelmgrBlacklist) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	/**
	 * 
	 * <p> @Title: update</p>
	 * <p> @Description: 修改</p>
	 * <p> @param personnelmgrBlacklist
	 * <p> @return</p>
	 * <p> @Return: PlatformResult<String></p>
	 * <p> <AUTHOR>
	 */
	@ApiOperation(value = "修改", notes = "修改")
	@PostMapping("/blacklist/update")
	public PlatformResult<String> update(@RequestBody HrmsBlacklist hrmsPersonnelmgrBlacklist) {
		try {
			if (hrmsBlacklistService.update(hrmsPersonnelmgrBlacklist) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	/**
	 * 
	 * <p> @Title: deleteById</p>
	 * <p> @Description: 删除黑名单</p>
	 * <p> @param id
	 * <p> @return</p>
	 * <p> @Return: PlatformResult<String></p>
	 * <p> <AUTHOR>
	 */
	@ApiOperation(value = "删除黑名单", notes = "删除黑名单")
	@PostMapping("/blacklist/deletedById/{blacklistId}")
	public PlatformResult<String> deleteById(@PathVariable String blacklistId) {
		try {
			hrmsBlacklistService.deleted(blacklistId);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

}
