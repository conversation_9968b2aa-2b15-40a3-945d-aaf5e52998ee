package cn.trasen.hrms.attendance.annualLeave.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.attendance.annualLeave.model.HrmsAnnualLeaveWageRules;

/**
 * @ClassName HrmsAnnualLeaveWageRulesService
 * @Description TODO
 * @date 2025��1��22�� ����4:11:42
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsAnnualLeaveWageRulesService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����4:11:42
	 * <AUTHOR>
	 */
	Integer save(HrmsAnnualLeaveWageRules record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����4:11:42
	 * <AUTHOR>
	 */
	Integer update(HrmsAnnualLeaveWageRules record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��22�� ����4:11:42
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsAnnualLeaveWageRules
	 * @date 2025��1��22�� ����4:11:42
	 * <AUTHOR>
	 */
	HrmsAnnualLeaveWageRules selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsAnnualLeaveWageRules>
	 * @date 2025��1��22�� ����4:11:42
	 * <AUTHOR>
	 */
	DataSet<HrmsAnnualLeaveWageRules> getDataSetList(Page page, HrmsAnnualLeaveWageRules record);
}
