package cn.trasen.hrms.attendance.annualLeave.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_annual_leave_operation_record")
@Setter
@Getter
public class HrmsAnnualLeaveOperationRecord {
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 操作类型 1-新增、2-修改、3-删除
     */
    @Column(name = "oper_type")
    @ApiModelProperty(value = "操作类型 1-新增、2-修改、3-删除")
    private String operType;

    /**
     * 操作功能 1-年假设置 2-年假计算规则，3-折算工资规则
     */
    @Column(name = "oper_function")
    @ApiModelProperty(value = "操作功能 1-年假设置 2-年假计算规则，3-折算工资规则")
    private String operFunction;

    /**
     * 操作前内容
     */
    @Column(name = "before_content")
    @ApiModelProperty(value = "操作前内容")
    private String beforeContent;

    /**
     * 操作后内容
     */
    @Column(name = "after_content")
    @ApiModelProperty(value = "操作后内容")
    private String afterContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}