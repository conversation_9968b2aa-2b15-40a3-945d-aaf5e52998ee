package cn.trasen.hrms.attendance.annualLeave.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.attendance.annualLeave.model.HrmsAnnualLeaveOperationRecord;

/**
 * @ClassName HrmsAnnualLeaveOperationRecordService
 * @Description TODO
 * @date 2025��1��22�� ����4:10:27
 * <AUTHOR>
 * @version 1.0
 */
public interface HrmsAnnualLeaveOperationRecordService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����4:10:27
	 * <AUTHOR>
	 */
	Integer save(HrmsAnnualLeaveOperationRecord record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��1��22�� ����4:10:27
	 * <AUTHOR>
	 */
	Integer update(HrmsAnnualLeaveOperationRecord record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��1��22�� ����4:10:27
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return HrmsAnnualLeaveOperationRecord
	 * @date 2025��1��22�� ����4:10:27
	 * <AUTHOR>
	 */
	HrmsAnnualLeaveOperationRecord selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<HrmsAnnualLeaveOperationRecord>
	 * @date 2025��1��22�� ����4:10:27
	 * <AUTHOR>
	 */
	DataSet<HrmsAnnualLeaveOperationRecord> getDataSetList(Page page, HrmsAnnualLeaveOperationRecord record);
}
