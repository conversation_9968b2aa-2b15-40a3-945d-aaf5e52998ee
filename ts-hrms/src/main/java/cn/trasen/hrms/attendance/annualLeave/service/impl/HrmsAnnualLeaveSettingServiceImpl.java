package cn.trasen.hrms.attendance.annualLeave.service.impl;

import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.WebUtils;
import cn.trasen.hrms.attendance.annualLeave.dao.HrmsAnnualLeaveDaysRuleMapper;
import cn.trasen.hrms.attendance.annualLeave.dao.HrmsAnnualLeaveWageRulesMapper;
import cn.trasen.hrms.attendance.annualLeave.model.HrmsAnnualLeaveDaysRule;
import cn.trasen.hrms.attendance.annualLeave.model.HrmsAnnualLeaveWageRules;
import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveEmpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.attendance.annualLeave.dao.HrmsAnnualLeaveSettingMapper;
import cn.trasen.hrms.attendance.annualLeave.model.HrmsAnnualLeaveSetting;
import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveSettingService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsAnnualLeaveSettingServiceImpl
 * @Description TODO
 * @date 2025��1��22�� ����4:10:52
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsAnnualLeaveSettingServiceImpl implements HrmsAnnualLeaveSettingService {

	@Resource
	private HrmsAnnualLeaveSettingMapper mapper;

	@Resource
	private HrmsAnnualLeaveDaysRuleMapper annualLeaveDaysRuleMapper;

	@Resource
	private HrmsAnnualLeaveWageRulesMapper annualLeaveWageRulesMapper;

	@Autowired
	private HrmsAnnualLeaveEmpService hrmsAnnualLeaveEmpService;

	/**
	 * 判断年假配置是否在生成员工年假
	 */
	private static Map<String,Boolean> yearAnnualLeaveOperMap = new HashMap<>();

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsAnnualLeaveSetting record) {
		Assert.notNull(record,"保存数据不能为空");
		Assert.hasText(record.getYear(),"年份不能为空");
		Assert.notNull(record.getAnnualLeaveDaysRuleList(),"年假计算规则不能为空");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//保存年假计算规则
		if(CollUtil.isNotEmpty(record.getAnnualLeaveDaysRuleList())){
			//删除年假计算规则列表
			Example daysExample = new Example(HrmsAnnualLeaveDaysRule.class);
			Example.Criteria daysCriteria = daysExample.createCriteria();
			daysCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			daysCriteria.andEqualTo("year",record.getYear());
			daysCriteria.andEqualTo("ssoOrgCode",UserInfoHolder.getCurrentUserCorpCode());
			annualLeaveDaysRuleMapper.deleteByExample(daysExample);
			record.getAnnualLeaveDaysRuleList().forEach(vo->{
				vo.setYear(record.getYear());
				vo.setId(IdGeneraterUtils.nextId());
				vo.setCreateDate(new Date());
				vo.setUpdateDate(new Date());
				vo.setIsDeleted("N");
				if (user != null) {
					vo.setCreateUser(user.getUsercode());
					vo.setCreateUserName(user.getUsername());
					vo.setUpdateUser(user.getUsercode());
					vo.setUpdateUserName(user.getUsername());
				}
				vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				annualLeaveDaysRuleMapper.insert(vo);
			});
		}
		//保存失效年假折算工资规则
		if(CollUtil.isNotEmpty(record.getAnnualLeaveWageRulesList())){
			//删除失效年假折算工资规则
			Example wageExample = new Example(HrmsAnnualLeaveWageRules.class);
			Example.Criteria wageCriteria = wageExample.createCriteria();
			wageCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			wageCriteria.andEqualTo("year",record.getYear());
			wageCriteria.andEqualTo("ssoOrgCode",UserInfoHolder.getCurrentUserCorpCode());
			annualLeaveWageRulesMapper.deleteByExample(wageExample);
			record.getAnnualLeaveWageRulesList().forEach(vo->{
				vo.setYear(record.getYear());
				vo.setId(IdGeneraterUtils.nextId());
				vo.setCreateDate(new Date());
				vo.setUpdateDate(new Date());
				vo.setIsDeleted("N");
				if (user != null) {
					vo.setCreateUser(user.getUsercode());
					vo.setCreateUserName(user.getUsername());
					vo.setUpdateUser(user.getUsercode());
					vo.setUpdateUserName(user.getUsername());
				}
				vo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				annualLeaveWageRulesMapper.insert(vo);
			});
		}
		int count = 0;
		Example example = new Example(HrmsAnnualLeaveSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("year",record.getYear());
		criteria.andEqualTo("ssoOrgCode",UserInfoHolder.getCurrentUserCorpCode());
		HrmsAnnualLeaveSetting annualLeaveSetting = mapper.selectOneByExample(example);
		if(annualLeaveSetting!=null){
			count = update(record);
		}else {
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			count = mapper.insertSelective(record);
		}
//		if(yearAnnualLeaveOperMap.containsKey(record.getYear()+UserInfoHolder.getCurrentUserCorpCode())){
//			throw new BusinessException("当前年份已在生成员工年假，请勿重复操作");
//		}else{
//			yearAnnualLeaveOperMap.put(record.getYear()+UserInfoHolder.getCurrentUserCorpCode(),true);
//		}
		//异步生成员工年假
//        new Thread(() -> {
            hrmsAnnualLeaveEmpService.createEmployeeAnnualLeaveByYear(record.getYear(),user,null);
//			yearAnnualLeaveOperMap.remove(record.getYear()+UserInfoHolder.getCurrentUserCorpCode());
//        }).start();
		return count;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsAnnualLeaveSetting record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsAnnualLeaveSetting record = new HrmsAnnualLeaveSetting();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsAnnualLeaveSetting selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsAnnualLeaveSetting> getDataSetList(Page page, HrmsAnnualLeaveSetting record) {
		Example example = new Example(HrmsAnnualLeaveSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsAnnualLeaveSetting> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public HrmsAnnualLeaveSetting getDataByYear(String year, ThpsUser user){
		//如果不传用户信息择取当前用户信息
		String ssoOrgCode = "";
		if (user == null){
			ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		}else {
			ssoOrgCode = user.getCorpcode();
		}
		Example example = new Example(HrmsAnnualLeaveSetting.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("year",year);
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode",ssoOrgCode);
		HrmsAnnualLeaveSetting annualLeaveSetting = mapper.selectOneByExample(example);
		if(annualLeaveSetting ==null){
			//查询上一年的年假配置
			String lastYear = (Integer.parseInt(year)-1) + "";
			Example example1 = new Example(HrmsAnnualLeaveSetting.class);
			Example.Criteria criteria1 = example1.createCriteria();
			criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria1.andEqualTo("year",lastYear);
			//根据当前登录账号机构编码过滤查询数据
			criteria1.andEqualTo("ssoOrgCode",ssoOrgCode);
			annualLeaveSetting = mapper.selectOneByExample(example);
			if(annualLeaveSetting==null) {
				annualLeaveSetting = new HrmsAnnualLeaveSetting();
				annualLeaveSetting.setYear(year);
			}else if("1".equals(annualLeaveSetting.getIsPutoffNextYear())){
				annualLeaveSetting.setYear(year);
				year = lastYear;
			}
		}
		//查询年假计算规则列表
		Example daysExample = new Example(HrmsAnnualLeaveDaysRule.class);
		Example.Criteria daysCriteria = daysExample.createCriteria();
		daysCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		daysCriteria.andEqualTo("year",year);
		//根据当前登录账号机构编码过滤查询数据
		daysCriteria.andEqualTo("ssoOrgCode",ssoOrgCode);
		daysExample.setOrderByClause("start_year_limit asc");
		List<HrmsAnnualLeaveDaysRule> daysRules = annualLeaveDaysRuleMapper.selectByExample(daysExample);
		if(CollUtil.isNotEmpty(daysRules)) {
			annualLeaveSetting.setAnnualLeaveDaysRuleList(daysRules);
		}else{
			daysRules = new ArrayList<>();
			daysRules.add(new HrmsAnnualLeaveDaysRule(1,10,5));
			daysRules.add(new HrmsAnnualLeaveDaysRule(10,20,10));
			daysRules.add(new HrmsAnnualLeaveDaysRule(20,null,15));
			annualLeaveSetting.setAnnualLeaveDaysRuleList(daysRules);
		}
		//查询年假折算工资规则
		Example wageExample = new Example(HrmsAnnualLeaveWageRules.class);
		Example.Criteria wageCriteria = wageExample.createCriteria();
		wageCriteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		wageCriteria.andEqualTo("year",year);
		//根据当前登录账号机构编码过滤查询数据
		wageCriteria.andEqualTo("ssoOrgCode",ssoOrgCode);
		wageExample.setOrderByClause("wage_amount desc");
		List<HrmsAnnualLeaveWageRules> wageRulesRules = annualLeaveWageRulesMapper.selectByExample(wageExample);
		if(CollUtil.isNotEmpty(wageRulesRules)) {
			annualLeaveSetting.setAnnualLeaveWageRulesList(wageRulesRules);
		}else{
			//根据当前登录账号机构编码过滤查询数据
			List<Map<String,Object>> result = annualLeaveWageRulesMapper.getJobtitleLevelData(ssoOrgCode);
			if(CollUtil.isNotEmpty(result)) {
				List<HrmsAnnualLeaveWageRules> jobtitleList = new ArrayList<>();
				result.forEach(vo->{
					jobtitleList.add(new HrmsAnnualLeaveWageRules(Convert.toStr(vo.get("jobtitleLevel"))));
				});
				jobtitleList.add(new HrmsAnnualLeaveWageRules("其他职称"));
				jobtitleList.add(new HrmsAnnualLeaveWageRules("无职称"));
				annualLeaveSetting.setAnnualLeaveWageRulesList(jobtitleList);
			}
		}
		return annualLeaveSetting;
	}
}
