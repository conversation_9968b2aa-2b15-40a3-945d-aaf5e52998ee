package cn.trasen.hrms.zp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.zp.bean.RecruitProcessConfigRes;
import cn.trasen.hrms.zp.bean.RecruitProcessConfigSaveReq;
import cn.trasen.hrms.zp.service.RecruitProcessConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021/12/6 15:47
 */
@Api(tags = "招聘流程设置")
@RequestMapping("/recruitProcessConfig")
@RestController
public class RecruitProcessConfigController {
    @Autowired
    RecruitProcessConfigService recruitProcessConfigService;

    @ApiOperation(value = "保存", notes = "保存")
    @PostMapping("/save")
    public PlatformResult save(@RequestBody RecruitProcessConfigSaveReq recruitProcessConfigSaveReq) {
        recruitProcessConfigService.save(recruitProcessConfigSaveReq);
        return PlatformResult.success();
    }

    @ApiOperation(value = "获取设置", notes = "获取设置")
    @RequestMapping(value = "/get", method = {RequestMethod.POST, RequestMethod.GET})
    public PlatformResult<RecruitProcessConfigRes> get() {
        return PlatformResult.success(recruitProcessConfigService.get());
    }

}