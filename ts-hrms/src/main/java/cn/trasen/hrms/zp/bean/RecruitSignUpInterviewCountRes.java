package cn.trasen.hrms.zp.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/12/10 11:28
 */
@Setter
@Getter
public class RecruitSignUpInterviewCountRes {


    @ApiModelProperty(value = "已通过")
    private Integer pass;
    @ApiModelProperty(value = "全部")
    private Integer all;
    @ApiModelProperty(value = "免试")
    private Integer exemption;
   
}
