package cn.trasen.hrms.zp.service;

import java.io.IOException;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpInterviewCountRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpInterviewExcel;
import cn.trasen.hrms.zp.bean.RecruitSignUpInterviewListReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpInterviewListRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpInterviewSaveReq;

/**
 * <AUTHOR>
 * @date 2021/12/13 9:46
 */
public interface RecruitSignUpInterviewService {
    @Transactional(rollbackFor = Exception.class)
    /**
     * 保存
     * @param recruitSignUpInterviewSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/13 15:53
     */
    void save(RecruitSignUpInterviewSaveReq recruitSignUpInterviewSaveReq) throws IOException;

    @Transactional(rollbackFor = Exception.class)
    /**
     *
     * @param recruitSignUpExamSaveReqList
     * @param postId
     * @return void
     * <AUTHOR>
     * @date 2021/12/11 12:28
     */
    void importInterview(List<RecruitSignUpInterviewExcel> recruitSignUpInterviewExcelList, String postId);

    @Transactional(rollbackFor = Exception.class)
    /**
     *
     * @param recruitSignUpExamSaveReqList
     * @param postId
     * @return void
     * <AUTHOR>
     * @date 2021/12/11 12:28
     */
    void importScore(List<RecruitSignUpInterviewExcel> recruitSignUpInterviewExcelList, String postId);

    /**
     * 下载面试模板
     *
     * @return []
     * <AUTHOR>
     * @date 2021/11/21 19:33
     */
    byte[] downloadTemplateImportInterview() throws IOException;

    /**
     * 下载面试分数模板
     *
     * @return []
     * <AUTHOR>
     * @date 2021/11/21 19:33
     */
    byte[] downloadTemplateImportInterviewScore() throws IOException;

    /**
     * 获取分页列表
     *
     * @param page
     * @param recruitSignUpExamListReq
     * @return cn.trasen.BootComm.model.DataSet<cn.trasen.hrms.zp.bean.RecruitSignUpExamListRes>
     * <AUTHOR>
     * @date 2021/12/11 14:05
     */
    DataSet<RecruitSignUpInterviewListRes> getList(Page page, RecruitSignUpInterviewListReq recruitSignUpInterviewListReq);

    /**
     * 导出笔试
     * @param recruitSignUpExamListReq
     * @return byte[]
     * <AUTHOR>
     * @date 2021/12/11 14:32
     */
    byte[] downloadExportInterview(RecruitSignUpInterviewListReq recruitSignUpExamListReq) throws IOException;

    /**
    * 笔试统计
    * @param recruitSignUpCountReq
    * @return cn.trasen.hrms.zp.bean.RecruitSignUpInterviewCountRes
    * <AUTHOR>
    * @date 2021/12/13 15:28
    */
    RecruitSignUpInterviewCountRes signUpInterviewCount(RecruitSignUpCountReq recruitSignUpCountReq);

	void exemption(List<String> signupIdListList);
}
