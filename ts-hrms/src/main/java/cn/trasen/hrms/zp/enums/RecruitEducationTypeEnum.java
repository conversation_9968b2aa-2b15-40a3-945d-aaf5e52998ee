package cn.trasen.hrms.zp.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/21 14:45
 */
@Getter
public enum RecruitEducationTypeEnum {
//0不限、1男、2女
NOLIMIT(0, "不限"),
    N(1, "否"),
    Y(2, "是");


    private final Integer key;
    private final String val;

    private RecruitEducationTypeEnum(Integer key, String val) {
        this.key = key;
        this.val = val;
    }

    /**
     * @Title: getValByKey
     * @Description: 根据key获得val值
     * @Param: key
     * @Return: String
     * <AUTHOR>
     */
    public static String getValByKey(Integer key) {
        for (RecruitEducationTypeEnum item : RecruitEducationTypeEnum.values()) {
            if (item.key.equals(key)) {
                return item.val;
            }
        }
        return RecruitEducationTypeEnum.NOLIMIT.val;
    }

    /**
     * @param val
     * @Title: getKeyByVal
     * @Description: 根据val获得key值
     * @Return String
     * <AUTHOR>
     * @date 2020年6月17日 下午5:30:51
     */
    public static Integer getKeyByVal(String val) {
        for (RecruitEducationTypeEnum item : RecruitEducationTypeEnum.values()) {
            if (item.val.equals(val)) {
                return item.key;
            }
        }
        return RecruitEducationTypeEnum.NOLIMIT.key;
    }
}
