package cn.trasen.hrms.zp.mapper;

import java.util.List;

import cn.trasen.hrms.zp.bean.DictJobtitle;
import cn.trasen.hrms.zp.model.RecruitPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @date 2021/12/6 15:46
 */
public interface RecruitPlanMapper extends Mapper<RecruitPlan> {

	List<DictJobtitle> selectDictJobtitle(@Param("ssoOrgCode") String ssoOrgCode);
}
