package cn.trasen.hrms.zp.model;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import cn.trasen.homs.core.model.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "zp_flow")
@Setter
@Getter
public class ZpFlow extends BaseBean  {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;



    /**
     * 流程名称
     */
    @Column(name = "flow_name")
    @ApiModelProperty(value = "流程名称")
    private String flowName;
    
    @Column(name = "flow_table")
    @ApiModelProperty(value = "流程对应的表")
    private String flowTable;

    
    @Column(name = "flow_enable")
    @ApiModelProperty(value = "复选")
    private String flowEnable;
    
    /**
     * 排序号
     */
    @Column(name = "flow_nymber")
    @ApiModelProperty(value = "排序号")
    private String flowNymber;

    /**
     * 招聘程序id
     */
    @Column(name = "procedure_id")
    @ApiModelProperty(value = "招聘程序id")
    private String procedureId;

    /**
     * 初审
     */
    @Column(name = "is_chushen")
    @ApiModelProperty(value = "招聘程序id")
    private String isChushen;
    
    /**
     * 复审
     */
    @Column(name = "is_fushen")
    @ApiModelProperty(value = "招聘程序id")
    private String isFushen;
    
    
	@Transient
	private List<ZpJurisdiction> zpJurisdictions; //人员权限数据
	
	@Transient
	private List<ZpStep> zpSteps; //环节
}