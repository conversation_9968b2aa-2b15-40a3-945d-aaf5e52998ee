package cn.trasen.hrms.zp.service.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.zp.bean.RecruitPlanPostListAnonymousReq;
import cn.trasen.hrms.zp.bean.RecruitPlanPostListAnonymousRes;
import cn.trasen.hrms.zp.bean.RecruitPlanPostListReq;
import cn.trasen.hrms.zp.bean.RecruitPlanPostListRes;
import cn.trasen.hrms.zp.bean.RecruitPlanPostReq;
import cn.trasen.hrms.zp.bean.RecruitPlanPostSaveReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpListReq;
import cn.trasen.hrms.zp.enums.RecruitEducationTypeEnum;
import cn.trasen.hrms.zp.enums.RecruitGenderEnum;
import cn.trasen.hrms.zp.enums.RecruitGraduateEnum;
import cn.trasen.hrms.zp.enums.RecruitsPostSignUpStatusEnum;
import cn.trasen.hrms.zp.mapper.RecruitPlanPostMapper;
import cn.trasen.hrms.zp.model.RecruitPlanPost;
import cn.trasen.hrms.zp.model.RecruitSignUp;
import cn.trasen.hrms.zp.service.RecruitPlanPostService;
import cn.trasen.hrms.zp.service.RecruitPlanService;
import cn.trasen.hrms.zp.service.RecruitSignUpService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/12/7 14:35
 */

@Service
public class RecruitPlanPostServiceImpl implements RecruitPlanPostService {

    @Autowired
    RecruitPlanPostMapper recruitPlanPostMapper;
@Autowired
RecruitPlanService recruitPlanService;

@Autowired
RecruitSignUpService recruitSignUpService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 保存岗位
     * @param recruitPlanPostSaveReqList
     * @return void
     * <AUTHOR>
     * @date 2021/12/7 14:49
     */
    public void save(List<RecruitPlanPostSaveReq> recruitPlanPostSaveReqList) {
        if (CollectionUtils.isEmpty(recruitPlanPostSaveReqList) == false) {
            Example example = new Example(RecruitPlanPost.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("planId", recruitPlanPostSaveReqList.get(0).getPlanId());
            recruitPlanPostMapper.deleteByExample(example);
        }

        /**
         * 日后优化改为批量
         */
        for (RecruitPlanPostSaveReq recruitPlanPostSaveReq : recruitPlanPostSaveReqList) {
            RecruitPlanPost recruitPlanPost = BeanUtils.addInitBean(RecruitPlanPost.class);
            BeanUtil.copyProperties(recruitPlanPostSaveReq, recruitPlanPost, "id");
            recruitPlanPost.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
            recruitPlanPostMapper.insertSelective(recruitPlanPost);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 修改
     * @param recruitPlanPostSaveReqList
     * @return void
     * <AUTHOR>
     * @date 2021/12/7 14:49
     */
    public void edit(RecruitPlanPostSaveReq recruitPlanPostSaveReq) {
        RecruitPlanPost recruitPlanPost = new RecruitPlanPost();
        BeanUtil.copyProperties(recruitPlanPostSaveReq, recruitPlanPost);
        BeanUtils.updateInitBean(recruitPlanPost);
        recruitPlanPostMapper.updateByPrimaryKeySelective(recruitPlanPost);
    }


    @Override
    /**
    * 获取列表
    * @param recruitPlanPostListAnonymousReq
    * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitPlanPostListAnonymousRes>
    * <AUTHOR>
    * @date 2021/12/13 17:54
    */
    public List<RecruitPlanPostListAnonymousRes> getAnonymousList(RecruitPlanPostListAnonymousReq recruitPlanPostListAnonymousReq) {
        RecruitPlanPostListReq recruitPlanPostListReq = new RecruitPlanPostListReq();
        recruitPlanPostListReq.setPlanId(recruitPlanPostListAnonymousReq.getPlanId());
        List<RecruitPlanPostListRes> recruitPlanPostListResList = getList(recruitPlanPostListReq);
        List<RecruitPlanPostListAnonymousRes> recruitPlanPostListAnonymousResList = BeanUtil.copyToList(recruitPlanPostListResList, RecruitPlanPostListAnonymousRes.class);
        for (RecruitPlanPostListAnonymousRes recruitPlanPostListAnonymousRes : recruitPlanPostListAnonymousResList) {
            RecruitSignUpListReq recruitSignUpReq = new RecruitSignUpListReq();
//            recruitSignUpReq.setName(recruitPlanPostListAnonymousReq.getName());
            recruitSignUpReq.setIdentityCard(recruitPlanPostListAnonymousReq.getIdentityCard());
            recruitSignUpReq.setPostId(recruitPlanPostListAnonymousRes.getId());
            recruitPlanPostListAnonymousRes.setSignUpStatus(1);
            List<RecruitSignUp> recruitSignUpList = recruitSignUpService.getBaseList(recruitSignUpReq);
            if (CollectionUtils.isEmpty(recruitSignUpList) == false) {
                boolean cancel = true;
                for (RecruitSignUp recruitSignUp : recruitSignUpList) {
                    if(recruitSignUp.getName().equals(recruitPlanPostListAnonymousReq.getName())==false)
                    {
                        throw new BusinessException("身份验证不通过，请检查输入是否有误！");
                    }
                    if (recruitSignUp.getCancelStatus().equals(1)==false) {
                        cancel = false;
                        recruitPlanPostListAnonymousRes.setSignupId(recruitSignUp.getId());
                        break;
                    }

                }
                if (cancel) {
                    recruitPlanPostListAnonymousRes.setSignUpStatus(3);

                } else {
                    recruitPlanPostListAnonymousRes.setSignUpStatus(2);

                }
            }

            recruitPlanPostListAnonymousRes.setSignUpStatusLable(RecruitsPostSignUpStatusEnum.getValByKey(recruitPlanPostListAnonymousRes.getSignUpStatus()));

        }
        return recruitPlanPostListAnonymousResList;
    }

    @Override
    /**
     * 获取列表
     * @param recruitPlanPostListReq
     * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitPlanPostListRes>
     * <AUTHOR>
     * @date 2021/12/7 18:00
     */
    public List<RecruitPlanPost> getBaseList(RecruitPlanPostListReq recruitPlanPostListReq) {
        Example example = new Example(RecruitPlanPost.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isBlank(recruitPlanPostListReq.getPlanId()) == false) {
            criteria.andEqualTo("planId", recruitPlanPostListReq.getPlanId());
        }
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        List<RecruitPlanPost> recruitPlanPostList = recruitPlanPostMapper.selectByExample(example);
        return recruitPlanPostList;
    }

    @Override
    /**
     * 获取列表
     * @param recruitPlanPostListReq
     * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitPlanPostListRes>
     * <AUTHOR>
     * @date 2021/12/7 18:00
     */
    public List<RecruitPlanPostListRes> getList(RecruitPlanPostListReq recruitPlanPostListReq) {
        Example example = new Example(RecruitPlanPost.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isBlank(recruitPlanPostListReq.getPlanId())) {
            criteria.andEqualTo("planId", recruitPlanPostListReq.getPlanId());
        }
        //根据当前登录账号机构编码过滤查询数据
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        List<RecruitPlanPost> recruitPlanPostList = recruitPlanPostMapper.selectByExample(example);
        List<RecruitPlanPostListRes> recruitPlanPostListResList = BeanUtil.copyToList(recruitPlanPostList, RecruitPlanPostListRes.class);

        List<DictItemResp> dictItemRespList = recruitPlanService.getEducationList();

        for (RecruitPlanPostListRes recruitPlanPostListRes : recruitPlanPostListResList) {
            recruitPlanPostListRes.setEducationTypeLable(RecruitEducationTypeEnum.getValByKey(recruitPlanPostListRes.getEducationType()));
            recruitPlanPostListRes.setGenderLable(RecruitGenderEnum.getValByKey(recruitPlanPostListRes.getGender()));
            recruitPlanPostListRes.setGraduateLable(RecruitGraduateEnum.getValByKey(recruitPlanPostListRes.getGraduate()));
            for (DictItemResp dictItemResp : dictItemRespList) {
                if (dictItemResp.getItemNameValue().equals(String.valueOf(recruitPlanPostListRes.getMinEducation()))) {
                    recruitPlanPostListRes.setMinEducationLable(dictItemResp.getItemName());
                    break;
                }
            }
        }
        return recruitPlanPostListResList;
    }


    @Override
    /**
     * 获取基础数据
     * @param recruitPlanPostReq
     * @return cn.trasen.hrms.zp.model.RecruitPlanPost
     * <AUTHOR>
     * @date 2021/12/8 14:10
     */
    public RecruitPlanPost getBase(RecruitPlanPostReq recruitPlanPostReq) {
        Example example = new Example(RecruitPlanPost.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if (StringUtils.isBlank(recruitPlanPostReq.getId()) == false) {
            criteria.andEqualTo("id", recruitPlanPostReq.getId());

        }
        example.setOrderByClause(" id desc LIMIT 1");
        return recruitPlanPostMapper.selectOneByExample(example);
    }
}