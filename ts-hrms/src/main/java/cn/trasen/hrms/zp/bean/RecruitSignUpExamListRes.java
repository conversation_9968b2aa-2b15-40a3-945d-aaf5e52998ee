package cn.trasen.hrms.zp.bean;

import cn.trasen.BootComm.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/9 17:25
 */

@Setter
@Getter
public class RecruitSignUpExamListRes {
    @ApiModelProperty(value = "id")
    String id;
    @ApiModelProperty(value = "岗位ID")
    String postId;
    @ApiModelProperty(value = "岗位其他要求")
    String postOther;
    @ApiModelProperty(value = "岗位名称")
    String postName;

    @ApiModelProperty(value = "岗位编码")
    String postCode;

    @ApiModelProperty(value = "手机号码")
    String mobile;

    @ApiModelProperty(value = "备注")
    String remarks;


    @ApiModelProperty(value = "姓名")
    String name;
    @ApiModelProperty(value = "身份证号码")
    String identityCard;

    @ApiModelProperty(value = "报名时间")
    Date signUpTime;
    Integer gender;
    String genderLable;

    Integer educationType;
    String educationTypeLable;
    String major;
    @ApiModelProperty(value = "学历")

    String educationId;
    @ApiModelProperty(value = "学历")

    String educationLable;



    String signupCode;
    Date graduationTime;


    @ApiModelProperty(value = "职称")
    String professionalName;


    @ApiModelProperty(value = "考试地点")
    String examAddr;

    @ApiModelProperty(value = "座位号")
    String seatNo;


    @ApiModelProperty(value = "考试得分")
    Double score;
    
    @ApiModelProperty(value = "考试得分2")
    Double score2;


    @ApiModelProperty(value = "1不通过2通过 是否通过")
    Integer status;

    @ApiModelProperty(value = "1不通过2通过 是否通过")
    String statusLable;

    @ApiModelProperty(value = "面试 1不2是")
    Integer exemptionStatus;


    @ApiModelProperty(value = "面试 1不2是")
    String exemptionStatusLable;

    @ApiModelProperty(value = "年龄")
    Integer age;

    @ApiModelProperty(value = "是否应届毕业")

    String graduateLable;

    Integer graduatesType;
    
    String otherData;
    
    String jobtitleName;
    

}