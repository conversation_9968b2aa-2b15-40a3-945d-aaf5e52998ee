package cn.trasen.hrms.zp.service;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zp.bean.FastSignUpReq;
import cn.trasen.hrms.zp.bean.RclxReq;
import cn.trasen.hrms.zp.bean.RecruitAuditReq;
import cn.trasen.hrms.zp.bean.RecruitCancelSignUpReq;
import cn.trasen.hrms.zp.bean.RecruitPlanCountReq;
import cn.trasen.hrms.zp.bean.RecruitPlanCountRes;
import cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressReq;
import cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressRes;
import cn.trasen.hrms.zp.bean.RecruitSendSMSReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpListReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpListRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpSaveReq;
import cn.trasen.hrms.zp.model.RecruitSignUp;

/**
 * <AUTHOR>
 * @date 2021/12/8 9:34
 */
public interface RecruitSignUpService {

    @Transactional(rollbackFor = Exception.class)
    /**
    * 快速报名
    * @param fastSignUpReq
    * @return void
    * <AUTHOR>
    * @date 2021/12/28 15:31
    */
    void fastSignUp(FastSignUpReq fastSignUpReq) throws Throwable;

    @Transactional(rollbackFor = Exception.class)
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    void signUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable;
    
    
    
	/**
	 * @throws Exception  
	* @Title: newSignUp 
	* @Description: 新报名
	* @param @param recruitSignUpSaveReq    设定文件 
	* @return void    返回类型 
	* @throws 
	*/
	void newSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable;


    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    void batchEditSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable;

    @Transactional(rollbackFor = Exception.class)
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    void editSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable;

    /**
         * 获取基础数据
         *
         * @param recruitSignUpReq
         * @return cn.trasen.hrms.zp.model.RecruitSignUp
         * <AUTHOR>
         * @date 2021/12/8 18:13
         */
    RecruitSignUp getBase(RecruitSignUpReq recruitSignUpReq);

    /**
        * 获取基础列表
        * @param recruitSignUpListReq
        * @return java.util.List<cn.trasen.hrms.zp.model.RecruitSignUp>
        * <AUTHOR>
        * @date 2021/12/13 18:15
        */
    List<RecruitSignUp> getBaseList(RecruitSignUpListReq recruitSignUpListReq);

    /**
        * 获取详情
        * @param recruitSignUpReq
        * @return cn.trasen.hrms.zp.bean.RecruitSignUpRes
        * <AUTHOR>
        * @date 2022/1/7 13:51
        */
    RecruitSignUpRes get(RecruitSignUpReq recruitSignUpReq);

    /**
         * 获取基础数据
         *
         * @param recruitSignUpListReq
         * @return cn.trasen.hrms.zp.model.RecruitSignUp
         * <AUTHOR>
         * @date 2021/12/8 18:13
         */
    DataSet<RecruitSignUpListRes> getList(Page page, RecruitSignUpListReq recruitSignUpListReq);

    /**
     * 报名查询
     *
     * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressRes>
     * <AUTHOR>
     * @date 2021/12/9 17:36
     */
    RecruitReadSignUpProgressRes readSignUpProgress(RecruitReadSignUpProgressReq recruitReadSignUpProgressReq);

    /**
    * 统计报名
    * @return cn.trasen.hrms.zp.bean.RecruitSignUpCountRes
    * <AUTHOR>
    * @date 2021/12/10 11:33
    */
    RecruitSignUpCountRes signUpCount(RecruitSignUpCountReq recruitSignUpCountReq);

    @Transactional(rollbackFor = Exception.class)
    /**
     * 复通过
     * @param recruitAuditReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 14:11
     */
    void  reviewAuditPass(RecruitAuditReq recruitAuditReq);

    @Transactional(rollbackFor = Exception.class)
    /**
    * 出审通过
    * @param recruitAuditReq
    * @return void
    * <AUTHOR>
    * @date 2021/12/10 14:11
    */
    void  firstAuditPass(RecruitAuditReq recruitAuditReq);

    @Transactional(rollbackFor = Exception.class)
    /**
     * 取消
     *
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 13:33
     */
    void cancel(RecruitCancelSignUpReq recruitCancelSignUpReq);

    @Transactional(rollbackFor = Exception.class)
    /**
     * 不通过
     *
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 13:33
     */
    void fail(RecruitAuditReq recruitAuditReq);

    /**
         * 获取统计
         * @param recruitPlanCountReq
         * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitSignUpListRes>
         * <AUTHOR>
         * @date 2021/12/9 17:45
         */
        RecruitPlanCountRes getPlanCount(RecruitPlanCountReq recruitPlanCountReq);

    void sendSMS(RecruitSendSMSReq recruitSendSMSReq);

	void newFastSignUp(FastSignUpReq fastSignUpReq) throws Throwable;

	void newBatchEditSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq)  throws Throwable;

	/** 
	* @Title: getTalentPoolList 
	* @Description: 获取人才库列表
	* @param @param page
	* @param @param recruitSignUpListReq
	* @param @return    设定文件 
	* @return DataSet<RecruitSignUpListRes>    返回类型 
	* @throws 
	*/
	DataSet<RecruitSignUpListRes> getTalentPoolList(Page page, RecruitSignUpListReq recruitSignUpListReq);

	/** 
	* @Title: deleteTalentPool 
	* @Description:删除人才库功能
	* @param @param id
	* @param @return    设定文件 
	* @return int    返回类型 
	* @throws 
	*/
	int deleteTalentPool(String id);

	/** 
	* @Title: updateRck 
	* @Description: 修改人才库
	* @param @param recruitSignUpSaveReq
	* @param @return    设定文件 
	* @return int    返回类型 
	* @throws 
	*/
	int updateRck(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable;

	void updateRclx(List<RclxReq> list);

	/** 
	* @Title: getExporttList 
	* @Description: 人才库导出excel
	* @param @param recruitSignUpListRes
	* @param @return    设定文件 
	* @return List<RecruitSignUpListRes>    返回类型 
	* @throws 
	*/
	List<RecruitSignUpListRes> getExporttList(Page page,RecruitSignUpListReq recruitSignUpListRes);

	/** 
	* @Title: addEmployee 
	* @Description: 添加到人员档案
	* @param @param id    设定文件 
	* @return void    返回类型 
	* @throws 
	*/

	void addEmployee(String id);

}
