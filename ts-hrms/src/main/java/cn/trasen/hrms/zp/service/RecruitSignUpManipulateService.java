package cn.trasen.hrms.zp.service;

import java.io.IOException;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamCountRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamExcel;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamListReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamListRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpExamSaveReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpManipulateExcel;
import cn.trasen.hrms.zp.model.RecruitSignUpManipulate;


/** 
* @ClassName: RecruitSignUpManipulateService 
* @Description: 实操控制器 
* <AUTHOR>  
* @date 2022年10月16日 上午11:53:29 
*  
*/
public interface RecruitSignUpManipulateService {
    
	@Transactional(rollbackFor = Exception.class)
    /**
    * 保存
    * @param recruitSignUpExamSaveReq
    * @return void
    * <AUTHOR>
    * @date 2021/12/13 15:50
    */
    void save(RecruitSignUpExamSaveReq recruitSignUpExamSaveReq) throws IOException;

    @Transactional(rollbackFor = Exception.class)
    /**
     *
     * @param recruitSignUpExamSaveReqList
     * @param postId
     * @return void
     * <AUTHOR>
     * @date 2021/12/11 12:28
     */
    void importExam(List<RecruitSignUpExamExcel> recruitSignUpExamSaveReqList, String postId);

    @Transactional(rollbackFor = Exception.class)
    /**
     *
     * @param recruitSignUpExamSaveReqList
     * @param postId
     * @return void
     * <AUTHOR>
     * @date 2021/12/11 12:28
     */
    void importScore(List<RecruitSignUpManipulateExcel> recruitSignUpManipulateExcel, String postId);

    @Transactional(rollbackFor = Exception.class)
    /**
     *免试
     * @param recruitSignUpExamSaveReqList
     * @param postId
     * @return void
     * <AUTHOR>
     * @date 2021/12/11 12:28
     */
    void exemption(List<String> signupIdListList);

    /**
     * 下载准考证模板
     *
     * @return []
     * <AUTHOR>
     * @date 2021/11/21 19:33
     */
    byte[] downloadTemplateImportExam() throws IOException;

    /**
     * 下载笔试分数模板
     *
     * @return []
     * <AUTHOR>
     * @date 2021/11/21 19:33
     */
    byte[] downloadTemplateImportScore() throws IOException;

    /**
     * 获取分页列表
     *
     * @param page
     * @param recruitSignUpExamListReq
     * @return cn.trasen.BootComm.model.DataSet<cn.trasen.hrms.zp.bean.RecruitSignUpExamListRes>
     * <AUTHOR>
     * @date 2021/12/11 14:05
     */
    DataSet<RecruitSignUpExamListRes> getList(Page page, RecruitSignUpExamListReq recruitSignUpExamListReq);

    /**
     * 导出笔试
     * @param recruitSignUpExamListReq
     * @return byte[]
     * <AUTHOR>
     * @date 2021/12/11 14:32
     */
    byte[] downloadExportExam(RecruitSignUpExamListReq recruitSignUpExamListReq) throws IOException;

    /**
     * 统计笔试
     *
     * @param recruitSignUpCountReq
     * @return cn.trasen.hrms.zp.bean.RecruitSignUpExamCountRes
     * <AUTHOR>
     * @date 2021/12/13 15:22
     */
    RecruitSignUpExamCountRes signUpExamCount(RecruitSignUpCountReq recruitSignUpCountReq);

    /**
    * 获取基础数据
    * @param recruitSignUpExamReq
    * @return cn.trasen.hrms.zp.model.RecruitSignUpExam
    * <AUTHOR>
    * @date 2021/12/22 17:21
    */
    RecruitSignUpManipulate getBase(RecruitSignUpExamReq recruitSignUpExamReq);
}
