package cn.trasen.hrms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**    
  * <P> @Description: 岗位升级记录</p>
  * <P> @Date: 2021年4月30日  上午10:01:41 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 

@Table(name = "hrms_post_information")
@Setter
@Getter
public class PostInformation {

	@Column(name = "sso_org_code")
	private String ssoOrgCode;


	@Id
    @Column(name = "id")
    @ApiModelProperty(value = "id")
	private String id;
	
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
	private String employeeId;
	
    @Column(name = "post_category")
    @ApiModelProperty(value = "岗位类别")
	private String postCategory;
	
    @Column(name = "post_id")
    @ApiModelProperty(value = "岗位等级")
	private String postId;
	
    @Column(name = "employ_duty_equally_date")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "任同职级时间")
	private String employDutyEquallyDate;
	
    @Column(name = "employ_duty_duration")
    @ApiModelProperty(value = "任现职级年限")
	private String employDutyDuration;
	
    /**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 企业ID
	 */
	@Column(name = "enterprise_id")
	@ApiModelProperty(value = "企业ID")
	private String enterpriseId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date")
	@ApiModelProperty(value = "创建时间")
	private Date createDate;

	/**
	 * 创建者ID
	 */
	@Column(name = "create_user")
	@ApiModelProperty(value = "创建者ID")
	private String createUser;

	/**
	 * 创建者姓名
	 */
	@Column(name = "create_user_name")
	@ApiModelProperty(value = "创建者姓名")
	private String createUserName;

	/**
	 * 更新时间
	 */
	@Column(name = "update_date")
	@ApiModelProperty(value = "更新时间")
	private Date updateDate;

	/**
	 * 更新者ID
	 */
	@Column(name = "update_user")
	@ApiModelProperty(value = "更新者ID")
	private String updateUser;

	/**
	 * 更新者姓名
	 */
	@Column(name = "update_user_name")
	@ApiModelProperty(value = "更新者姓名")
	private String updateUserName;

	/**
	 * 删除标识: Y=是; N=否;
	 */
	@Column(name = "is_deleted")
	@ApiModelProperty(value = "删除标识: Y=是; N=否;")
	private String isDeleted;
	
	@Column(name = "org_id")
	@ApiModelProperty(value = "机构id")
	private String orgId;
	
	@Column(name = "org_name")
	@ApiModelProperty(value = "机构名称")
	private String orgName;
	
	@Column(name = "business_id")
	@ApiModelProperty(value = "附件")
	private String businessId;
	
	@Transient
	private String employeeNo;
	@Transient
	private String employeeName;
	@Transient
	private String gender;
	@Transient
	private String postName;
	@Transient
	private String dictName;
	@Transient
	private String upgradeDate;

	//导出序号
	@Transient
	private Integer pm;
}
