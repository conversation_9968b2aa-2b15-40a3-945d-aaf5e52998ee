package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "hrms_scheduling_frequency")
@Setter
@Getter
public class HrmsSchedulingFrequency {

    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    /**
     * 班次id
     */
    @Id
    @Column(name = "frequency_id")
    @ApiModelProperty(value = "班次id")
    private String frequencyId;

    /**
     * 班次名称
     */
    @Column(name = "frequency_name")
    @ApiModelProperty(value = "班次名称")
    private String frequencyName;

    /**
     * 出勤天数
     */
    @Column(name = "day_long")
    @ApiModelProperty(value = "出勤天数")
    private String dayLong;

    /**
     * 颜色
     */
    @Column(name = "frequency_colour")
    @ApiModelProperty(value = "颜色")
    private String frequencyColour;

    /**
     * 考勤时间（多个时间逗号隔开）
     */
    @Column(name = "frequency_time")
    @ApiModelProperty(value = "考勤时间（多个时间逗号隔开）")
    private String frequencyTime;
    
    /**
     * 考勤时间（多个时间逗号隔开）
     */
    @Column(name = "day_hours")
    @ApiModelProperty(value = "考勤小时")
    private String dayHours;
    
    
    /**
     * 班次类型  1：上班 2 修假 3 假勤  4,晚夜班
     */ 
    @Column(name = "frequency_type")
    @ApiModelProperty(value = "班次类型")
    private String frequencyType;
    
    @Column(name = "org_id")
    @ApiModelProperty(value = "班次所属科室id")
    private String orgId;
    
    @Column(name = "org_name")
    @ApiModelProperty(value = "班次所属科室")
    private String orgName;
   
    //门诊班、急诊班、医生班、护士班
    @Column(name = "shift_attribute")
    @ApiModelProperty(value = "班次属性 ")
    private String shiftAttribute;
    
    //全天  上午  下午
    @Column(name = "shift_attribute_time")
    @ApiModelProperty(value = "班次时间 ")
    private String shiftAttributeTime;
    

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    @Column(name = "jjr_type")
    @ApiModelProperty(value = "节假日是否休息")
    private String jjrType;
    
    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    @Column(name = "hlbc")
    @ApiModelProperty(value = "护理班次")
    private String hlbc;
    
    @Column(name = "ywbc")
    @ApiModelProperty(value = "医务班次")
    private String ywbc;
    
    @Transient
    @ApiModelProperty(value = "是否管理员")
    private String isAdmin;
}