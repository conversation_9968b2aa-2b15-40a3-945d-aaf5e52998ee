package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**
 * 同工同酬
 *
 */
@Table(name = "hrms_equal_pay")
@Setter
@Getter
public class HrmsEqualPay {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Id
    private String id;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /**
     * 员工编码
     */
    @Column(name = "employee_code")
    @ApiModelProperty(value = "员工编码")
    private String employeeCode;
    
    @Column(name = "org_name")
    @ApiModelProperty(value = "部门名称")
    private String orgName;
    
    @Column(name = "org_id")
    @ApiModelProperty(value = "部门编码")
    private String orgId;
    
    @Column(name = "business_id")
    @ApiModelProperty(value = "业务id-用于文件上传")
    private String businessId;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birthday;

    /**
     * 岗位类别
     */
    @Column(name = "job_category")
    @ApiModelProperty(value = "岗位类别")
    private String jobCategory;

    /**
     * 来院时间
     */
    @Column(name = "entry_date")
    @ApiModelProperty(value = "来院时间")
    private String entryDate;

    /**
     * 最高职称
     */
    @Column(name = "highest_professional")
    @ApiModelProperty(value = "最高职称")
    private String highestProfessional;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String education;

    /**
     * 最高学历附件
     */
    @Column(name = "education_file")
    @ApiModelProperty(value = "最高学历附件")
    private String educationFile;
    
    
    @Column(name = "id_card_file")
    @ApiModelProperty(value = "身份证附件")
    private String idCardFile;

    /**
     * 最高职称附件
     */
    @Column(name = "professional_file")
    @ApiModelProperty(value = "最高职称附件")
    private String professionalFile;

    /**
     * 执业证书附件
     */
    @Column(name = "zyzs_file")
    @ApiModelProperty(value = "执业证书附件")
    private String zyzsFile;

    /**
     * 规培证书附件
     */
    @Column(name = "gp_file")
    @ApiModelProperty(value = "规培证书附件")
    private String gpFile;

    /**
     * 学信网电子注册备案表
     */
    @Column(name = "xuexin_net_file")
    @ApiModelProperty(value = "学信网电子注册备案表")
    private String xuexinNetFile;

    /**
     * 前第1年年度考核
     */
    @Column(name = "first_year_appraise")
    @ApiModelProperty(value = "前第1年年度考核")
    private String firstYearAppraise;

    /**
     * 前第2年年度考核
     */
    @Column(name = "two_year_appraise")
    @ApiModelProperty(value = "前第2年年度考核")
    private String twoYearAppraise;

    /**
     * 前第3年年度考核
     */
    @Column(name = "three_year_appraise")
    @ApiModelProperty(value = "前第3年年度考核")
    private String threeYearAppraise;

    /**
     * 前第4年年度考核
     */
    @Column(name = "four_year_appraise")
    @ApiModelProperty(value = "前第4年年度考核")
    private String fourYearAppraise;

    /**
     * 前第5年年度考核
     */
    @Column(name = "five_year_appraise")
    @ApiModelProperty(value = "前第5年年度考核")
    private String fiveYearAppraise;

    /**
     * 前一年出勤天数
     */
    @Column(name = "befor_first_year_attendance")
    @ApiModelProperty(value = "前一年出勤天数")
    private String beforFirstYearAttendance;

    @Column(name = "first_year_sick_leave")
    private String firstYearSickLeave;

    /**
     * 前一年事假天数
     */
    @Column(name = "first_year_personal_leave")
    @ApiModelProperty(value = "前一年事假天数")
    private String firstYearPersonalLeave;

    /**
     * 本年出勤天数
     */
    @Column(name = "current_first_year_attendance")
    @ApiModelProperty(value = "本年出勤天数")
    private String currentFirstYearAttendance;

    /**
     * 本年病假天数
     */
    @Column(name = "current_year_sick_leave")
    @ApiModelProperty(value = "本年病假天数")
    private String currentYearSickLeave;

    /**
     * 本年事假天数
     */
    @Column(name = "current_year_personal_leave")
    @ApiModelProperty(value = "本年事假天数")
    private String currentYearPersonalLeave;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 修改人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    /**
     * 修改人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 删除标识
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识")
    private String isDeleted;
    
    /**
     * 同工同酬时间
     */
    @Column(name = "equal_pay_date")
    @ApiModelProperty(value = "同工同酬时间")
    private String equalPayDate;
    
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	@Column(name = "remark")
	private String remark;
	
	
	//同工同筹转流程
	@ApiModelProperty(value = "流程id")
	@Column(name = "l_business_id")
	private String lBusinessId;
	
	@ApiModelProperty(value = "流程节点")
	@Column(name = "workflow_inst_id")
	private String workflowInstId;
	
	@ApiModelProperty(value = "流程编号 ")
	@Column(name = "workflow_no")
	private String workflowNo;

	@Transient
    @ApiModelProperty(value = "身份证号 ")
    private String identityNumber;
}