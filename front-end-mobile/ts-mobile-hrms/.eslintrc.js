module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', '@vue/prettier'],
  parserOptions: {
    parser: 'babel-eslint'
  },
  parser: "vue-eslint-parser",
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-useless-escape': 0,
    // 'vue/no-unused-vars': 0,
    // 'vue/no-use-v-if-with-v-for': 0,
    // 'vue/no-reserved-keys': 0,
    // 'vue/no-unused-components': 0,
    // 'vue/require-v-for-key': 0,
    // 'vue/valid-template-root': 0,
    // 'vue/no-side-effects-in-computed-properties': 0,
    // 'vue/return-in-computed-property': 0,
    // 'vue/valid-v-for': 0,
    'vue/no-parsing-error': 0,
    '__webpack_public_path__': 0,
    'no-unused-components': 0,
    'prettier/prettier': [
      'error',
      {
        singleQuote: true, // 字符串使用单引号
        semi: true, // 每行末尾自动添加分号
        tabWidth: 2, // tab缩进大小,默认为2
        useTabs: false, // 使用tab缩进，默认false
        bracketSpacing: true, //对象中打印空格 默认true
        /** 箭头函数参数括号 默认avoid 可选 avoid| always
         *avoid 能省略括号的时候就省略 例如x => x
         *always 总是有括号
         * **/
        arrowParens: 'avoid',
        printWidth: 80, // 换行长度，默认80
        jsxBracketSameLine: true,
        trailingCommaL: 'all'
      }
    ]
  },
  overrides: [
    {
      files: [
        '**/__tests__/*.{j,t}s?(x)',
        '**/tests/unit/**/*.spec.{j,t}s?(x)'
      ],
      env: {
        jest: true
      }
    }
  ]
};
