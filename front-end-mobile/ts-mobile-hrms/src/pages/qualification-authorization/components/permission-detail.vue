<template>
  <u-popup
    border-radius="16"
    v-model="checkModelShow"
    mode="bottom"
    height="65%"
    :closeable="true"
    @close="handleClose"
  >
    <view class="popup-wrap">
      <view class="popup-title">
        权限明细
      </view>
      <view class="popup-content">
        <mescroll
          ref="mescroll"
          :searchInput="false"
          :down="false"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="permission-list">
            <view
              class="permission-item"
              v-for="item in permissionList"
              :key="item.id"
              >{{ item.itemName }}</view
            >
          </view>
        </mescroll>
      </view>
    </view>
  </u-popup>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      employeeNo: '',
      checkModelShow: false,
      permissionList: []
    };
  },
  methods: {
    show({ employeeNo }) {
      this.employeeNo = employeeNo;
      this.checkModelShow = true;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getMyQuaAuthItemDetlDetails({
          pageSize: page.size,
          pageNo: page.num,
          employeeNo: this.employeeNo
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.permissionList = this.permissionList.concat(rows);
    },
    datasInit() {
      this.permissionList = [];
    },
    handleClose() {
      this.datasInit();
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popup-content {
  flex: 1;
  position: relative;
}
.popup-title {
  padding: 30rpx 30rpx 0;
  font-weight: bold;
}
.permission-item {
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  position: relative;
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 15px;
    right: 0;
  }
}
</style>
