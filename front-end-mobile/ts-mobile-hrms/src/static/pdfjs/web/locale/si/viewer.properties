# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¶à¶½à·à¶±à· à¶´à·à¶§à·à·
previous_label=à¶à¶½à·à¶±à·
next.title=à¶à·à¶ à¶´à·à¶§à·à·
next_label=à¶à·à¶

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=à¶´à·à¶§à·à·
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=à¶à·à¶©à·à¶½à¶±à¶º
zoom_out_label=à¶à·à¶©à·à¶½à¶±à¶º
zoom_in.title=à·à·à·à·à¶½à¶±à¶º
zoom_in_label=à·à·à·à·à¶½à¶±à¶º
zoom.title=à·à·à·à·à¶½ à¶à¶»à¶±à·à¶±
presentation_mode.title=à·à¶¸à¶»à·à¶´à¶« à¶´à·âà¶»à¶à·à¶»à¶º à·à·à¶­ à¶¸à·à¶»à·à·à¶±à·à¶±
presentation_mode_label=à·à¶¸à¶»à·à¶´à¶« à¶´à·âà¶»à¶à·à¶»à¶º
open_file.title=à¶à·à¶±à·à· à¶à¶»à·à¶±à·à¶±
open_file_label=à¶à¶»à·à¶±à·à¶±
print.title=à¶¸à·à¶¯à·âà¶»à¶«à¶º
print_label=à¶¸à·à¶¯à·âà¶»à¶«à¶º
download.title=à¶¶à·à¶à¶±à·à¶±
download_label=à¶¶à·à¶à¶±à·à¶±
bookmark.title=à·à¶­à·à¶¸à¶±à· à¶¯à·à¶à·à¶¸ (à¶´à·à¶§à¶´à¶­à· à¶à¶»à¶±à·à¶± à·à· à¶±à· à¶à·à·à·à·à·à¶ à¶à¶»à·à¶±à·à¶±)
bookmark_label=à·à¶­à·à¶¸à¶±à· à¶¯à·à¶à·à¶¸

# Secondary toolbar and context menu
tools.title=à¶¸à·à·à¶½à¶¸à·
tools_label=à¶¸à·à·à¶½à¶¸à·
first_page.title=à¶¸à·à¶½à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
first_page_label=à¶¸à·à¶½à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
last_page.title=à¶à·à·à¶±à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
last_page_label=à¶à·à·à¶±à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±

cursor_hand_tool_label=à¶à¶­à· à¶¸à·à·à¶½à¶¸



# Document properties dialog box
document_properties.title=à¶½à·à¶à¶±à¶ºà· à¶à·à¶«à·à¶à¶â¦
document_properties_label=à¶½à·à¶à¶±à¶ºà· à¶à·à¶«à·à¶à¶â¦
document_properties_file_name=à¶à·à¶±à·à·à· à¶±à¶¸:
document_properties_file_size=à¶à·à¶±à·à·à· à¶´à·âà¶»à¶¸à·à¶«à¶º:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb=à¶à·.à¶¶. {{size_kb}} (à¶¶à¶ºà·à¶§ {{size_b}})
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb=à¶¸à·.à¶¶. {{size_mb}} (à¶¶à¶ºà·à¶§ {{size_b}})
document_properties_title=à·à·à¶»à·à·à·à¶º:
document_properties_author=à¶à¶­à·:
document_properties_subject=à¶¸à·à¶­à·à¶à·à·:
document_properties_keywords=à¶¸à·à¶½ à¶´à¶¯:
document_properties_creation_date=à·à·à¶¯à· à¶¯à·à¶±à¶º:
document_properties_modification_date=à·à¶à·à·à¶°à·à¶­ à¶¯à·à¶±à¶º:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¶±à·à¶»à·à¶¸à·à¶­à·:
document_properties_producer=à¶´à·à¶©à·à¶à·à· à·à¶¸à·à¶´à·à¶¯à¶:
document_properties_version=à¶´à·à¶©à·à¶à·à· à¶à¶±à·à·à·à¶¯à¶º:
document_properties_page_count=à¶´à·à¶§à· à¶à¶«à¶±:
document_properties_page_size=à¶´à·à¶§à·à·à· à¶­à¶»à¶¸:
document_properties_page_size_unit_inches=à¶à¶à¶½à·
document_properties_page_size_unit_millimeters=à¶¸à·.à¶¸à·.
document_properties_page_size_orientation_portrait=à·à·à¶»à·à·
document_properties_page_size_orientation_landscape=à¶­à·à¶»à·à·
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}}Ã{{height}}{{unit}}{{name}}{{orientation}}
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=à·à·à¶à·à¶­à· à·à·à¶ºà¶¸à¶± à¶¯à·à¶à·à¶¸:
document_properties_linearized_yes=à¶à·à·
document_properties_linearized_no=à¶±à·à·à·
document_properties_close=à·à·à¶±à·à¶±

print_progress_message=à¶¸à·à¶¯à·âà¶»à¶«à¶º à·à¶³à·à· à¶½à·à¶à¶±à¶º à·à·à¶¯à·à¶±à¶¸à· à·à·à¶¸à·à¶±à·â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=à¶à·à¶½à¶à¶à· à¶à¶»à¶±à·à¶±

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
document_outline_label=à¶½à·à¶à¶±à¶ºà· à·à¶§à·à¶±
attachments.title=à¶à¶¸à·à¶«à·à¶¸à· à¶´à·à¶±à·à·à¶±à·à¶±
attachments_label=à¶à¶¸à·à¶«à·à¶¸à·
thumbs.title=à·à·à¶à·à¶­à· à¶»à· à¶´à·à¶±à·à·à¶±à·à¶±
thumbs_label=à·à·à¶à·à¶­à· à¶»à·
findbar.title=à¶½à·à¶à¶±à¶ºà·à·à· à·à·à¶ºà¶±à·à¶±
findbar_label=à·à·à¶ºà¶±à·à¶±

# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¶´à·à¶§à·à· {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¶´à·à¶§à·à·à· à·à·à¶à·à¶­ à¶»à·à· {{page}}

# Find panel button title and messages
find_input.title=à·à·à¶ºà¶±à·à¶±
find_previous.title=à¶¸à·à¶¸ à·à·à¶à·à¶à¶© à¶à¶½à·à¶±à· à¶ºà·à¶¯à·à¶«à· à·à·à¶®à·à¶±à¶º à·à·à¶ºà¶±à·à¶±
find_previous_label=à¶à¶½à·à¶±à·
find_next.title=à¶¸à·à¶¸ à·à·à¶à·à¶à¶© à¶à·à¶à¶§ à¶ºà·à¶¯à·à¶± à·à·à¶®à·à¶±à¶º à·à·à¶ºà¶±à·à¶±
find_next_label=à¶à·à¶
find_highlight=à·à·à¶ºà¶½à·à¶½ à¶à¶¯à·à¶¯à·à¶´à¶±à¶º
find_entire_word_label=à·à¶¸à·à·à¶­ à·à¶ à¶±
find_reached_top=à¶½à·à¶à¶±à¶ºà· à¶¸à·à¶¯à·à¶±à¶§ à·à¶à· à·à·à¶º, à¶´à·à· à·à·à¶§ à¶à·à·à¶§
find_reached_bottom=à¶½à·à¶à¶±à¶ºà· à¶à·à·à·à¶±à¶ºà¶§ à·à¶à· à·à·à¶º, à¶à·à· à·à·à¶§ à¶´à·à·à¶§
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit[zero]=à¶à·à·à¶´à·à¶¸à· {{limit}} à¶à¶§ à·à¶©à·
find_not_found=à·à·à¶à·à¶à¶© à·à¶¸à· à¶±à·à·à·à¶«à·

# Error panel labels
error_more_info=à¶­à· à¶­à·à¶»à¶­à·à¶»à·
error_less_info=à¶à·à¶¸ à¶­à·à¶»à¶­à·à¶»à·
error_close=à·à·à¶±à·à¶±
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=à¶´à·à¶©à·à¶à·à·.js v{{version}} (à¶­à·à¶±à·à¶¸: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=à¶´à¶«à·à·à·à¶©à¶º: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¶à·à¶±à·à·: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¶´à·à·à·à¶º: {{line}}

# Predefined zoom values
page_scale_width=à¶´à·à¶§à·à·à· à¶´à·à¶½
page_scale_auto=à·à·à·à¶ºà¶à¶à·âà¶»à·à¶º à·à·à·à·à¶½à¶±à¶º
page_scale_actual=à·à·à¶¶à· à¶´à·âà¶»à¶¸à·à¶«à¶º
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error=à¶´à·à¶©à·à¶à·à· à¶´à·à¶»à¶«à¶º à¶à·à¶»à·à¶¸à·à¶¯à· à¶¯à·à·à¶ºà¶à· à·à·à¶¯à· à·à·à¶º.
invalid_file_error=à·à¶½à¶à¶à· à¶±à·à·à¶± à·à· à·à·à¶±à·à·à· à¶´à·à¶©à·à¶à·à· à¶à·à¶±à·à·à¶à·.
missing_file_error=à¶¸à¶à·à·à¶»à·à¶«à· à¶´à·à¶©à·à¶à·à· à¶à·à¶±à·à·à¶à·.
unexpected_response_error=à¶à¶±à¶´à·à¶à·âà·à·à¶­ à·à·à·à·à¶¯à·à¶ºà¶ à¶´à·âà¶»à¶­à·à¶ à·à¶»à¶ºà¶à·.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_label=à¶¸à·à¶¸ à¶´à·à¶©à·à¶à·à· à¶à·à¶±à·à· à·à·à·à·à¶­ à¶à·à¶»à·à¶¸à¶§ à¶¸à·à¶»à¶´à¶¯à¶º à¶ºà·à¶¯à¶±à·à¶±.
password_invalid=à·à·à¶»à¶¯à· à¶¸à·à¶»à¶´à¶¯à¶ºà¶à·. à¶±à·à·à¶­ à¶à¶­à·à·à·à· à¶à¶»à¶±à·à¶±.
password_ok=à·à¶»à·
password_cancel=à¶à·à¶½à¶à¶à·

printing_not_supported=à¶à·à·à·à¶¯à¶ºà¶ºà·: à¶¸à·à¶¸ à¶à¶­à·à¶»à·à¶à·à·à·à· à¶¸à·à¶¯à·âà¶»à¶«à¶º à·à¶³à·à· à·à·à¶³à·à¶±à· à·à·à·à¶º à¶±à·à¶¯à¶à·à·à¶ºà·.
printing_not_ready=à¶à·à·à·à¶¯à¶ºà¶ºà·: à¶¸à·à¶¯à·âà¶»à¶«à¶ºà¶§ à¶´à·à¶©à·à¶à·à· à¶à·à¶±à·à· à·à¶¸à·à¶´à·à¶»à·à¶«à¶ºà·à¶±à· à¶´à·à¶»à¶«à¶º à·à· à¶±à·à¶­.
web_fonts_disabled=à·à·à¶ºà¶¸à¶± à¶à¶à·à¶»à· à¶à¶¶à¶½à¶ºà·: à¶´à·à¶©à·à¶à·à· à·à·à¶­ à¶à·à·à·à¶¯à·à¶¯à· à¶à¶à·à¶»à· à¶·à·à·à·à¶­à· à¶à· à¶±à·à·à·à¶à·à¶º.

# Editor


# Editor Parameters

# Editor Parameters

# Editor aria
