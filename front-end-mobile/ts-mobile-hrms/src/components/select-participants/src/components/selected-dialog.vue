<template>
  <u-popup
    mode="right"
    v-model="myDialog"
    :safe-area-inset-bottom="true"
    height="100%"
    width="100%"
    @close="myDialogClose"
  >
    <u-navbar title="已选" title-bold :customBack="myDialogClose">
      <text class="navbar-right" slot="right" @click="addressBookSelectConfirm"
        >确定</text
      >
    </u-navbar>
    <view class="selected-box">
      <view
        class="person-list-item-info"
        v-for="(item, index) in selectData"
        :key="index"
        @click="cancelSelectedHandle(item)"
      >
        <template v-if="item[keyType] === 1">
          <view
            class="checkbox__icon-wrap"
            :class="
              (participantsSelectedIds.indexOf(item[keyId]) !== -1) | iconClass
            "
          >
            <u-icon
              class="checkbox__icon-wrap__icon"
              name="checkbox-mark"
              size="28"
              :color="
                (participantsSelectedIds.indexOf(item[keyId]) !== -1)
                  | iconColor
              "
            />
          </view>
        </template>

        <template v-if="item[keyType] === 2">
          <view
            class="checkbox__icon-wrap"
            :class="
              (organizationSelectedIds.indexOf(item[keyId]) !== -1) | iconClass
            "
          >
            <u-icon
              class="checkbox__icon-wrap__icon"
              name="checkbox-mark"
              size="28"
              :color="
                (organizationSelectedIds.indexOf(item[keyId]) !== -1)
                  | iconColor
              "
            /> </view
        ></template>

        <view class="left">
          <template v-if="item[keyType] === 1">
            <img
              class="person-head-image"
              v-if="item.avatar"
              :src="item.avatar"
            />
            <view
              v-else
              class="person-head-image"
              :class="item.gender | sexClassFilter"
            >
              {{ item.empName | firstNameFilter }}
            </view>
          </template>
          <template v-if="item[keyType] === 2">
            <view class="organization-img-box">
              <img
                class="data-head-image"
                :src="require('@/assets/img/organization-icon.png')"
              />
            </view>
          </template>
        </view>
        <view class="right">
          <view class="person-name">{{ item[keyName] }}</view>
          <view class="person-description" v-if="item[keyType] === 1">
            {{ item.orgName }}
          </view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import componentsData from '../mixins/componentsData';
export default {
  mixins: [componentsData],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    participantsSelectedIds() {
      return this.paddingSelectData
        .filter(item => item[this.keyType] === 1)
        .map(item => item[this.keyId]);
    },
    organizationSelectedIds() {
      return this.paddingSelectData
        .filter(item => item[this.keyType] === 2)
        .map(item => item[this.keyId]);
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.myDialog = val;
        }
      },
      immediate: true
    }
  },
  data: () => ({
    myDialog: false,
    paddingSelectData: []
  }),
  created() {
    this.paddingSelectData = this.$_.cloneDeep(this.selectData);
  },
  methods: {
    cancelSelectedHandle(e) {
      const { id, name, type } = this.$attrs.dataKey;

      let ids = [];
      switch (e[type]) {
        case 1:
          ids = this.participantsSelectedIds;
          break;
        case 2:
          ids = this.organizationSelectedIds;
          break;
      }
      const result = ids.includes(e[id]);
      // 过滤对应type的数组 获取是否存在该id数据 添加 or 删除

      if (!result) {
        this.paddingSelectData.push(e);
      } else {
        let findIndex;
        this.paddingSelectData.forEach((item, index) => {
          if (item[id] === e[id] && item[type] === e[type]) {
            findIndex = index;
          }
        });
        this.paddingSelectData.splice(findIndex, 1);
      }
    },
    addressBookSelectConfirm() {
      const keyType = this.keyType;
      const keyId = this.keyId;

      const participantsIds = this.selectData
        .filter(item => item[keyType] === 1)
        .map(item => item[keyId]);

      const peddingParticipantsIds = this.participantsSelectedIds;

      const organizationIds = this.selectData
        .filter(item => item[keyType] === 2)
        .map(item => item[keyId]);

      const peddingOrganizationIds = this.organizationSelectedIds;

      // 原始选中对象selectData过滤type循环 如果暂存选中对象的type数据里不存在了 则在selectData里移除
      participantsIds.forEach(item => {
        if (!peddingParticipantsIds.includes(item)) {
          let findIndex;
          this.selectData.forEach((select, index) => {
            if (select[keyId] === item && select[keyType] === 1) {
              findIndex = index;
            }
          });
          this.selectData.splice(findIndex, 1);
        }
      });

      organizationIds.forEach(item => {
        if (!peddingOrganizationIds.includes(item)) {
          let findIndex;
          this.selectData.forEach((select, index) => {
            if (select[keyId] === item && select[keyType] === 2) {
              findIndex = index;
            }
          });
          this.selectData.splice(findIndex, 1);
        }
      });

      this.myDialog = false;
      this.$emit('change', false);
    },
    myDialogClose() {
      this.myDialog = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.selected-box {
  padding: 32rpx 0;

  .person-list-item-info {
    display: flex;
    align-items: center;
    padding: 16rpx 32rpx;
    .checkbox__icon-wrap {
      color: $uni-text-content-color;
      @include vue-flex;
      flex: none;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 38rpx;
      height: 38rpx;
      color: transparent;
      text-align: center;
      transition-property: color, border-color, background-color;
      font-size: $uni-icon-size-base;
      border: 1px solid $uni-text-color-disable;
      border-radius: 4px;
      transition-duration: 0.2s;
    }
    .checkbox__icon-wrap--checked {
      border-color: $u-type-primary;
      background-color: $u-type-primary;
    }
    .left {
      margin: 0 24rpx;
      .organization-img-box {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $u-bg-color;
        .data-head-image {
          width: 48rpx;
          height: 48rpx;
          text-align: center;
          line-height: $uni-img-size-lg;
          font-size: $uni-font-size-base;
          color: $uni-text-color-inverse;
        }
      }

      .person-head-image {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: 50%;
        background-color: $u-bg-color;
        text-align: center;
        line-height: $uni-img-size-lg;
        font-size: $uni-font-size-base;
        color: $uni-text-color-inverse;
        &.sex-man {
          background-color: $sexman-color;
        }
        &.sex-woman {
          background-color: $sexwoman-color;
        }
      }
    }
    .right {
      flex: 1;
      .person-name {
        font-size: $uni-font-size-base;
        color: $u-main-color;
      }
      .person-description {
        color: $u-content-color;
        font-size: $uni-font-size-sm;
      }
    }
  }
}
.navbar-right {
  padding: 0 $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
}
</style>
