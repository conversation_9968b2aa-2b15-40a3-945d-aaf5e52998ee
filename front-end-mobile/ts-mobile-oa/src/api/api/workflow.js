import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取流程信息**/
  getTaskHisListById(id) {
    return request.get(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/findTaskHisList/${id}`
    );
  },
  /**@desc 流程催办**/
  pressWorkflow(data) {
    return request.post(`${apiConfig.workflow()}/workflow/wfInst/press`, data);
  },
  /**@desc 流程撤销**/
  doUndoInstance(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/instance/doUndoInstance`,
      data
    );
  },
  /**@desc 审批人流程撤销**/
  workflowRevoke(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/cancelTask`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取发起流程页面默认页签**/
  getDefaultTab(data) {
    return request.post(
      `${apiConfig.workflow()}/preference/set/findByUser`,
      data
    );
  },
  /**@desc 获取全部流程列表**/
  getAllProcessList(data) {
    return request.post(
      `${apiConfig.workflow()}/form/classify/getWfClassifyTree`,
      data
    );
  },
  /**@desc 获取收藏流程列表**/
  getCollectionProcessList(data) {
    return request.post(
      `${apiConfig.workflow()}/form/classify/getCollectTree`,
      data
    );
  },
  /**@desc 获取常用流程列表**/
  getCommonProcessList(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/definition/getCommonlyUsed`,
      {
        params: data
      }
    );
  },
  /**@desc 强制流程**/
  forcedEndWorkflow(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/instance/doTerminateProcessInstance`,
      data
    );
  },
  /**@desc 获取抄送给我的流程**/
  getCopyToMyWorkflowList(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/instance/getCopyToMyWorkflowList`,
      {
        params: data
      }
    );
  },
  //获取内部联系人列表
  getInnerContractList(data) {
    return request.post(
      `${apiConfig.basics()}/employee/linkman/innerLinkManlist`,
      data
    );
  },
  //获取联系人基本信息
  getPersonBasicInfo(id) {
    return request.get(
      `${apiConfig.basics()}/cusotmEmployee/grxxcjb/${id}`,
      {}
    );
  },

  //获取联系人基本信息 人员档案改版
  customEmployeeBaseGrxxcjb(employeeId) {
    return request.get(
      `${apiConfig.basics()}/api/customEmployeeBase/grxxcjb/${employeeId}`
    );
  },
  //获取联系人年度信息
  getPersonYearBasicTable(id) {
    return request.get(`${apiConfig.basics()}/cusotmEmployee/rchx/${id}`, {});
  },
  //获取联系人年度信息 人员档案改版
  customEmployeeBaseRchx(employeeId) {
    return request.get(
      `${apiConfig.basics()}/api/customEmployeeBase/rchx/${employeeId}`
    );
  },
  //根据id获取附件信息
  getFileAttachmentByBusinessIdOrId(id) {
    return request.get(
      `${apiConfig.basics()}/fileAttachment/getFileAttachmentByBusinessIdOrId?businessId=${id}`,
      {}
    );
  },
  //获取机构与机构下的人员
  getOrgEmp(data) {
    return request.post(`${apiConfig.basics()}/organization/getOrgEmp`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 查看抄送流程标记已读 */
  handleMarkWorkflowReaded(copyId) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/wfCopyUserRead`,
      {
        copyId
      }
    );
  },
  /**@desc 获取获取流程审批列表**/
  getMyHandleWorkflowList(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/instance/getMyHandleWorkflowList`,
      {
        params: data
      }
    );
  },
  /**@desc 获取流程查阅统计数据**/
  getMyConsultWfCountByMobile(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/instance/getMyConsultWfCountByMobile`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取流程查阅列表**/
  getMyConsultWfListByMobile(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/instance/getMyConsultWfListByMobile`,
      {
        params: data
      }
    );
  },
  /**@desc 获取流程查阅分类统计数据**/
  getConsultTreeByPermMobile(data) {
    return request.post(
      `${apiConfig.workflow()}/form/classify/getConsultTreeByPermMobile`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 提交发起人选择节点审批人**/
  saveApproveChoice(data) {
    return request.post(
      `${apiConfig.workflow()}/api/approveChoice/save`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true,
          loadingText: '提交中...'
        }
      }
    );
  },
  /**@desc 提交流程表单数据**/
  startProcessInstance(data) {
    return request.post(
      `${apiConfig.form()}/form/api/startProcessInstance`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true,
          loadingText: '提交中...'
        }
      }
    );
  },
  /**@desc 获取当前审批人所在节点**/
  getCurrentStepNoByTaskId(id) {
    return request.get(`${apiConfig.workflow()}/workflow/task/info/${id}`);
  },
  /**@desc 获取流程表单数据**/
  getFormDatas(data) {
    return request.post(`${apiConfig.form()}/form/api/findById`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      },
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 获取子表单数据内容 */
  getChildFormDataById(data) {
    return request.get(`${apiConfig.form()}/dpTable/getChildDataList`, {
      params: data
    });
  },
  /**@desc 获取流程表单字段权限**/
  getFiledPermissions(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/form/filedPermissions`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 通过流程ID获取流程表单模板**/
  getFormTemplate(id) {
    return request.post(
      `${apiConfig.form()}/dpFormTemplate/findByWorkflowId/${id}`,
      {},
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 通过表单ID获取流程表单模板**/
  getFormTemplateById(id) {
    return request.post(`${apiConfig.form()}/dpFormTemplate/findById/${id}`);
  },
  /**@desc 通过id获取子表单数据 */
  getChildFormDetailById(id) {
    return request.post(`${apiConfig.form()}/dpTable/findById/${id}`);
  },
  /**@desc 获取子表单审批权限 */
  getChildFOrmApprovalLimit(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/form/selectChildFormFieldList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取流程表单相关附件**/
  getFiles(data) {
    return request.get(`${apiConfig.document()}/attachment/selectByIds`, {
      params: data
    });
  },
  /**@desc 获取我申请的流程统计数据**/
  getMyLaunchWorkflowCount(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/instance/getMyLaunchWorkflowCount`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取我申请的流程列表**/
  getMyLaunchWorkflowList(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/instance/getMyLaunchWorkflowList`,
      {
        params: data
      }
    );
  },
  /**@desc 获取流程信息数据**/
  getWorkflowData(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/selectWfInstData`,
      data,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取流程审批意见**/
  getApprovalOpinion(id) {
    return request.get(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/findApprovalOpinion/${id}`
    );
  },
  /**@desc 获取抄送人信息**/
  getCopyToUserList(id) {
    return request.get(
      `${apiConfig.workflow()}/workflow/wfInst/getCopyToUserList/${id}`
    );
  },
  /**@desc 获取流程历史操作信息**/
  getTaskHisList(data) {
    return request.get(`${apiConfig.workflow()}/workflow/task/his/list`, {
      params: data,
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 获取流程抄送操作信息**/
  getCopyUserList(data) {
    return request.get(
      `${apiConfig.workflow()}/workflow/taskHis/getCopyUserList`,
      {
        params: data,
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取下个节点信息**/
  getNextWfStepListByWfDefId(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/getNextWfStepListByWfDefId`,
      data
    );
  },
  /**@desc 获取退回节点**/
  getReturnNode(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/taskHis/getHisTaskNodeList`,
      data
    );
  },
  /**@desc 提交申请**/
  saveApply(ajaxPath, data) {
    return request.post(`${ajaxPath}`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      },
      custom: {
        showLoading: true,
        loadingText: '提交中...'
      }
    });
  },
  /**@desc 获取下个节点信息**/
  getNextWfStepListByTaskId(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/getNextWfStepListByTaskId`,
      data
    );
  },
  /**@desc 获取常用语**/
  getMyOfficaldiction() {
    return request.get(
      `${apiConfig.oa()}/employee/officaldiction/getMyOfficaldiction`
    );
  },
  /**@desc 获取当前流程节点提示信息**/
  getCurrentNodeTips(id) {
    return request.get(`${apiConfig.workflow()}/workflow/wfInst/info/${id}`);
  },
  /**@desc 获取流程节点附件**/
  getTaskFileList(data) {
    return request.post(`${apiConfig.workflow()}/task/file/list`, data);
  },
  /**@desc 获取公文按钮权限**/
  getDocumentButtonPermissions(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/step/button/permissionsList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 查询流程定义信息**/
  getWorkflowDefinition(id) {
    return request.get(
      `${apiConfig.workflow()}/workflow/definition/code/${id}`
    );
  },
  /**@desc 查询流程定义信息**/
  getWorkflowDefinitionBywfDefId(id) {
    return request.get(
      `${apiConfig.workflow()}/workflow/definition/info/${id}`
    );
  },
  checkSubmitUrl(url, data) {
    return request.post(url, data);
  },
  /**@desc 查询流程类型(串行、无序并行、有序并行)**/
  getTaskType(data) {
    return request.post(
      `${apiConfig.workflow()}/workflow/retrieve/checkTaskType`,
      data
    );
  },
  /**@desc 获取未处理的流程**/
  getWorkflowUnReadList() {
    return request.post(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/selectToDoTaskList`
    );
  },
  /**@desc 获取流水号**/
  getCalculationSerialNo(datas) {
    return request.post(
      `${apiConfig.form()}/field/serialNumber/calculationSerialNo`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取数据字典**/
  getDictItemByTypeCode(datas) {
    return request.get(`${apiConfig.basics()}/dictItem/getDictItemByTypeCode`, {
      params: datas
    });
  },
  /**@desc 通过ID获取流程数据**/
  getDatasByFieldId(id) {
    return request.post(
      `${apiConfig.form()}/dpFieldRelation/findByFieldId/${id}`
    );
  },
  /**@desc 根据流程id获取自己发起已完结的流程流程数据**/
  getMyselfDataListByWorkflowId(id) {
    return request.post(
      `${apiConfig.form()}/form/api/getMyselfDataListByWorkflowId/${id}`
    );
  },
  /**@desc 获取互通数据（病人信息）**/
  getHisPatientInfo(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/getPatientInfo`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（住院信息）**/
  getEmrPatientInfo(datas) {
    return request.post(
      `${apiConfig.external()}/emrApi/getInpatientInfoById`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（结算信息）**/
  getHisStatementRecord(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/getStatementRecord`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（预交金信息）**/
  getHisInpDepositsParmList(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/getInpDepositsParmList`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（医嘱明细）**/
  getHisQueryNurseInpatientOrder(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/queryNurseInpatientOrder`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（住院费用明细）**/
  getHisQueryInPatientNurseFeeSpeci(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/queryInPatientNurseFeeSpeci`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（医嘱项目）**/
  getHisQueryOrderItemList(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/queryOrderItemList`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 获取互通数据（标本）**/
  getHisQuerySample(datas) {
    return request.post(`${apiConfig.external()}/hisApi/querySample`, datas, {
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 获取手术项目**/
  getOperationItemList(datas) {
    return request.get(`${apiConfig.hrms()}/api/quaAuthCfg/list`, {
      params: datas
    });
  },
  /**@desc 获取工作台流程统计**/
  getWorkBenchMyWfStatistical() {
    return request.post(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/myWfStatistical`
    );
  },
  /**@desc 获取工单相关数据**/
  getWorkOrderDatas(api) {
    return request.get(`${apiConfig.worksheet()}${api}`);
  },
  /**@desc 通过接口服务获取下拉选项**/
  getSelectOptionList(api) {
    return request.get(api);
  },
  //抄送
  handleWorkflowCopy(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/wfCopyUser`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  //加签
  handleAddNodes(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/addSignature`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  //转办
  handleTransfer(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/transferTask`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 经办加签
  addSignatureByStep(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/addSignatureByStep`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 加签退回
  addSignatureReturn(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/addSignatureReturn`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 经办加签获取节点
  getWfStepListByWfDefIdToAddSignature(datas) {
    return request.post(
      `${apiConfig.workflow()}/workflow/task/getWfStepListByWfDefIdToAddSignature`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  //流程审批获取加签节点名称
  getAddSignatureStepName(datas) {
    return request.get(
      `${apiConfig.workflow()}/workflow/task/getAddSignatureStepName`,
      {
        params: datas
      }
    );
  },
  //流程数据存入草稿箱
  saveWorkflowDraft(datas) {
    return request.post(`${apiConfig.form()}/form/api/insert`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  //流程数据更新草稿箱
  updateWorkflowDraft(datas) {
    return request.post(`${apiConfig.form()}/form/api/update`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  //流程删除
  handleDeleteWorkflow(datas) {
    return request.post(`${apiConfig.form()}/form/api/deleteById`, datas);
  },
  /**@desc 获取基本药品字典数据 */
  QueryDrgRoomManageDrug(params) {
    return request.get(
      `${apiConfig.external()}/hisApi/QueryDrgRoomManageDrug`,
      {
        params
      }
    );
  },
  QueryBasicsDrgDictionary(params) {
    return request.get(
      `${apiConfig.external()}/hisApi/QueryBasicsDrgDictionary`,
      {
        params
      }
    );
  },
  getHisInPatientInfo(datas) {
    return request.post(
      `${apiConfig.external()}/hisApi/getQueryInPatient`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  getleaveStatisticsTableHeadCols(datas) {
    return request.post(
      `${apiConfig.hrms()}/api/leaveStatistics/getleaveStatisticsTableHeadCols`,
      datas
    );
  },
  getleaveStatisticsDataList(datas) {
    return request.post(
      `${apiConfig.hrms()}/api/leaveStatistics/getleaveStatisticsDataList`,
      datas
    );
  },

  //收藏附件
  saveCollect(data) {
    return request.post(`${apiConfig.oa()}/attachment/saveCollect`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 获取住院号查询 */
  borrowCheckData(params) {
    return request.get(`${apiConfig.external()}/api/borrow/checkData`, {
      params
    });
  },

  /**@desc 保存流水号 */
  saveSerialNo(datas) {
    return request.post(
      `${apiConfig.form()}/field/serialNumber/saveSerialNo?fieldSerialNumberRoleId=${
        datas.fieldSerialNumberRoleId
      }`
    );
  },

  /**@desc 邀请会诊对象 科室列表 */
  getConsultQuaOrgInfo() {
    return request.get(
      `${apiConfig.hrms()}/api/consultQua/getConsultQuaOrgInfo`
    );
  },

  getmedHighRiskCfgList(params) {
    return request.get(`${apiConfig.hrms()}/api/medHighRiskCfg/list`, {
      params
    });
  }
};
