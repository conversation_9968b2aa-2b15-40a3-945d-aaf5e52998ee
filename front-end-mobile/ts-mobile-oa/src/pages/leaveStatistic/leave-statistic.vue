<template>
  <view class="ts-content leave-statistic-container">
    <page-head title="请假统计" @clickLeft="returnBack"></page-head>
    <view class="search-content">
      <view
        class="search-item"
        :class="{
          'empty-item': !searchYear
        }"
        @click="handleShowPicker('year')"
      >
        {{ searchYear || '请选择年' }}
      </view>
      <view
        class="search-item"
        :class="{
          'empty-item': !searchMonth
        }"
        @click="handleShowPicker('month')"
      >
        {{ searchMonth || '请选择月' }}
      </view>
    </view>

    <view class="mescroll-content">
      <u-table>
        <u-tr class="u-tr">
          <u-th class="u-th">请假类型</u-th>
          <u-th class="u-th">请假天数</u-th>
        </u-tr>
        <u-tr v-for="(data, index) of dataList" :key="index">
          <u-td>{{ data.leaveType }}</u-td>
          <u-td>{{ data.leaveDays }}</u-td>
        </u-tr>
      </u-table>
    </view>

    <date-picker
      ref="datePicker"
      :mode="picker.mode"
      :fields="picker.fields"
      :value="picker.value"
      cancelText="清空"
      @cancel="handleClearTime"
      @confirm="handleDatePickerConfirm"
    >
    </date-picker>
  </view>
</template>

<script>
import DatePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    DatePicker
  },
  data() {
    return {
      searchDate: this.$dayjs().format('YYYY-MM'),
      picker: {},

      dataList: [],
      columns: [
        {
          title: '请假类型',
          name: 'leaveType'
        },
        {
          title: '请假时长',
          name: 'leaveDays'
        }
      ]
    };
  },
  onLoad() {
    this.refresh();
  },
  methods: {
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    refresh() {
      let dateList = this.searchDate
          ? this.searchDate.split('-')
          : this.$dayjs()
              .format('YYYY-MM')
              .split('-'),
        params = {
          year: dateList[0],
          month: dateList[1] || ''
        };
      this.ajax.getLeaveStatisticDataList(params).then(res => {
        this.dataList = res.object;
      });
    },
    handleShowPicker(type) {
      this.picker = {
        year: {
          mode: 'date',
          fields: 'year',
          value: this.searchDate.split('-')[0]
        },
        month: {
          mode: 'date',
          fields: 'month',
          value:
            this.searchDate.length > 4
              ? this.searchDate
              : this.searchDate + '-01'
        }
      }[type];
      this.$refs.datePicker.show();
    },
    handleDatePickerConfirm({ value }) {
      this.searchDate = value;
      this.refresh();
    },
    handleClearTime(type) {
      if (type == 'mask') {
        return;
      }
      if (this.picker.fields == 'month') {
        this.searchDate =
          this.searchDate.split('-')[0] || this.$dayjs().format('YYYY');
      } else {
        this.searchDate = this.$dayjs().format('YYYY-MM');
      }
      this.refresh();
    }
  },
  computed: {
    searchYear() {
      let date = this.$dayjs(this.searchDate)
        .format('YYYY-MM')
        .split('-')[0];
      if (date) {
        return date + '年';
      }
      return '';
    },
    searchMonth() {
      let date = this.searchDate.split('-')[1];
      if (date) {
        return date + '月';
      }
      return '';
    }
  }
};
</script>

<style lang="scss" scoped>
.leave-statistic-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .search-content {
    display: flex;
    align-items: center;
    padding: 10px 24px;
    .search-item {
      line-height: 30px;
      height: 30px;
      flex: 1;
      border-radius: 30px;
      background-color: #fff;
      text-align: center;
      &:not(:first-child) {
        margin-left: 16px;
      }
      &.empty-item {
        color: #999;
      }
    }
  }
}
.mescroll-content {
  flex: 1;
  position: relative;
  background-color: #fff;
}
</style>
