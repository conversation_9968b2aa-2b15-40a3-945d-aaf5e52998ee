<template>
  <view
    class="ts-content"
    v-if="showContent"
    :style="{ backgroundImage: imgSrc }"
    style="background-color: #fff; background-position-x: -60rpx;"
  >
    <view>
      <page-head @clickLeft="returnBack">
        <view
          v-if="informationStatus != 3"
          slot="right"
          style="width: 100%;display: flex; align-items: center;"
        >
          <view class="dataNum" @tap="changeDrawer('read')">
            <text class="oa-icon oa-icon-yuedu dataNumIcon" />
            <text>{{ readNum }}</text>
          </view>
          <view class="dataNum">
            <text class="oa-icon oa-icon-chakan dataNumIcon"></text>
            <text>{{ informationKits }}</text>
          </view>
          <view class="dataNum" @tap="changeDrawer('forward')">
            <text class="oa-icon oa-icon-zhuanfa1 dataNumIcon"></text>
            <text>{{ forwardList.length }}</text>
          </view>
          <view class="dataNum" @tap="handleRemind" v-show="showRemind">
            <text class="oa-icon oa-icon-tixing- dataNumIcon"></text>
            <text>提醒</text>
          </view>
          <view class="dataNum" @click="infoCollect">
            <text
              class="oa-icon dataNumIcon"
              :class="
                isCollect == true
                  ? 'oa-icon-shoucang1 isCollect'
                  : 'oa-icon-shoucang11'
              "
            ></text>
            <text>{{ collectNumber }}</text>
          </view>
        </view>
      </page-head>
      <view class="info_head">
        <view class="info_title" :class="titleColor == 1 ? 'titleRed' : ''">
          <text class="info_title_top" v-if="showSign == 1">[顶]</text>
          <text class="info_title_quintessence" v-if="isMarrow == 1">[精]</text>
          {{ informationTitle }}
        </view>
        <view
          class="info_head_info info_icon oa-icon"
          :class="[
            informationStatus == 2 ? 'oa-icon-yibohui' : '',
            informationStatus == 3 ? ' oa-icon-yiquxiao' : ''
          ]"
        >
          <view class="info_left"
            >{{ createUserName }} {{ createDeptName }} {{ releaseDate }}</view
          >
        </view>
      </view>
      <view class="content" id="infoContent">
        <view class="content-text" :class="{ content_bottom: contentBottom }">
          <!-- <rich-text :nodes="content"></rich-text> -->
          <mp-html
            v-if="informationStatus != 3"
            ref="information"
            :content="content"
            @load="informationOnLoad"
          />
        </view>
      </view>
      <view class="file_content">
        <view v-if="isShowFileBox && informationStatus != 3">
          <view class="mask" @tap="isShowFileList" v-if="isShowFile"></view>
          <view class="attachment">
            <view class="file" @tap="isShowFileList()">
              <text class="oa-icon oa-icon-fujian file_icon"></text>
              {{ fileCount }}个附件
            </view>
            <transition name="slide-fade">
              <view class="attachment_list" v-if="isShowFile">
                <view
                  class="attachment_item"
                  v-for="(item, index) in attachmentList"
                  :key="index"
                  @click="previewFile(item.id, item.accessorySaveName)"
                >
                  <text
                    class="oa-icon"
                    :class="
                      'oa-icon-' + $oaModule.formatFileType(item.accessoryType)
                    "
                  ></text>
                  <view class="attachment_item_info">
                    <text class="original_name">{{ item.accessoryName }}</text>
                    <text class="file_size">{{
                      item.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                  <view
                    class="oa-icon oa-icon-xiazai down_load"
                    @click.stop="downloadFile(item.id, item.accessorySaveName)"
                  >
                  </view>
                </view>
              </view>
            </transition>
          </view>
        </view>
      </view>
    </view>
    <canvas
      v-if="showCanvas"
      style="width:150px;height:100px;z-index: -1;"
      canvas-id="firstCanvas"
      id="firstCanvas"
    ></canvas>
    <uni-drawer :visible="showRight" mode="right" @close="closeDrawer('right')">
      <view class="forward-list" v-if="drawerTap == 'forward'">
        <view class="drawer-title" style="line-height: 80rpx;">
          转发({{ forwardList.length }})
        </view>
        <view class="forward-item" v-for="item in forwardList" :key="item.id">
          <view
            class="iconImg"
            :class="item.createUserSex == 0 ? 'sexMan' : 'sexWoman'"
          >
            {{ item.createUserName.substring(item.createUserName.length - 2) }}
          </view>
          <view class="userInfo">
            <text>{{ item.createUserName }}</text>
            <text class="description">转发给了{{ item.forwardUserName }}</text>
          </view>
        </view>
      </view>
      <view class="read-list" v-else>
        <view class="drawer-title" style="display: flex; align-items: center;">
          阅读详情
          <scroll-view
            class="swiper_head"
            :scroll-x="true"
            :show-scrollbar="false"
          >
            <view
              v-for="(tab, index) in tabBars"
              :key="index"
              class="uni-tab-item"
              :data-current="index"
              @click="ontabtap"
            >
              <view
                :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
              >
                <text class="uni-tab-item-title">{{ tab.name }}</text>
                <text class="uni-tab-item-num" v-if="tab.total != null">{{
                  tab.total
                }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        <swiper
          :current="tabIndex"
          class="swiper_box"
          :duration="300"
          @change="ontabchange"
        >
          <swiper-item
            class="swiper_item"
            v-for="(item, index) in tabBars"
            :key="item.handleStatus"
          >
            <mescroll
              :ref="'mescroll' + index"
              :mescrollIndex="index"
              :down="item.downOption"
              @getDatas="getListData"
              @setDatas="setListData"
              @datasInit="datasInit"
            >
              <view
                class="dept-list"
                v-for="row in item['list']"
                :key="row.deptCode"
              >
                <view class="dept-name">
                  {{ row.deptName }}
                </view>
                <view
                  class="emp-item"
                  :class="one.status == '1' ? 'isRead' : ''"
                  v-for="one in row['emprow']"
                  :key="one.code"
                  >{{ one.name }}
                </view>
              </view>
            </mescroll>
          </swiper-item>
        </swiper>
      </view>
    </uni-drawer>
    <view v-if="pageParam && pageParam.fromPage == 'informationManagement'">
      <uni-fab
        ref="fab"
        :title="title"
        :pattern="pattern"
        :content="btnContent"
        :horizontal="horizontal"
        :vertical="vertical"
        :direction="direction"
        @trigger="trigger"
        @fabClick="fabClick"
      />
    </view>
    <view class="deleteBtn" v-if="informationStatus == 2" @tap="deleteInfo"
      >删除</view
    >
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
import mpHtml from '@/components/mp-html/components/mp-html/mp-html.vue';
import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
import form from '../personalCenter/edit/form';
export default {
  components: {
    mescroll,
    mpHtml,
    uniDrawer
  },
  data() {
    return {
      pageParam: {},
      contentBottom: false,
      showContent: false,
      informationId: '',
      informationTitle: '',
      releaseDate: '',
      createDeptName: '',
      createUserName: '',
      //content: '此信息没有文字内容',
      content: '',
      watermarkText: '',
      isShowFileBox: false,
      isShowFile: false,
      attachmentList: [],
      fileCount: 0,
      isCollect: false,
      informationKits: 0,
      forwardList: [],
      readNum: '0',
      collectNumber: 0,
      showRight: false,
      showManagementbtn: false,
      horizontal: 'right',
      vertical: 'bottom',
      direction: 'vertical',
      title: '管理',
      titleColor: 0,
      showSign: 0,
      isMarrow: 0,
      informationStatus: 1,
      pattern: {
        color: '#666',
        backgroundColor: '#fff',
        selectedColor: '#666'
      },
      btnContent: [
        {
          text: '组件',
          active: false
        },
        {
          text: 'API',
          active: false
        },
        {
          text: '模版',
          active: false
        }
      ],
      drawerTap: '',
      tabIndex: 0,
      tabBars: [
        {
          name: '全部',
          handleStatus: '',
          downOption: true,
          isInit: true,
          total: null,
          list: []
        },
        {
          name: '未读',
          handleStatus: '0',
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '已读',
          handleStatus: '1',
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      imgSrc: '',
      showCanvas: true,
      showRemind: false
    };
  },
  computed: {
    ...mapState(['empcode', 'username'])
  },
  async onLoad(opt) {
    if (opt && opt.showRemind) {
      this.showRemind = true;
    }
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    if (JSON.stringify(opt) != '{}') this.pageParam = opt;
    this.informationId = opt.informationId;
    if (
      this.pageParam &&
      (this.pageParam.fromPage == 'informationManagement' ||
        this.pageParam.fromPage == 'myInformation')
    ) {
      this.showManagementbtn = true;
    } else {
      this.mergeInsertInformationBrowser(opt.informationId);
    }
    this.initInfomationData(opt.informationId);
    this.getReadSituation(opt.informationId);
    this.initInformationFiles(opt.informationId);
    this.selectIsCollect(opt.informationId);
  },
  mounted() {
    let context = uni.createCanvasContext('firstCanvas', this),
      watermarkText = this.username + '-' + this.empcode;
    context.rotate((330 * Math.PI) / 180);
    context.setFontSize(12);
    context.setFillStyle('rgba(169,169,169,.3)');
    context.fillText(watermarkText, -20, 100);
    setTimeout(() => {
      context.draw();
      uni.canvasToTempFilePath({
        x: 0, // 从canvas的x轴的0点开始选中
        y: 0, // 从canvas的y轴的0点开始选中
        width: 150, // 选中canvas多宽
        height: 100, // 选中canvas多宽
        destWidth: 150, // 生成的图片多宽
        destHeight: 100, // 生成的图片多高
        canvasId: 'firstCanvas', // canvas的id
        success: res => {
          this.showCanvas = false;
          // 在H5平台下，tempFilePath 为 base64
          this.imgSrc = `url(${res.tempFilePath})`;
        }
      });
    }, 500);
  },
  methods: {
    ...mapMutations(['changeState']),
    //插入阅读记录
    mergeInsertInformationBrowser(informationId) {
      this.ajax.mergeInsertInformationBrowser({
        informationId: informationId
      });
    },
    //获取信息详情
    initInfomationData(informationId) {
      this.ajax
        .getInformationDatas({
          id: informationId
        })
        .then(res => {
          let data = res.object;
          if (
            this.pageParam.isMobile ||
            this.pageParam.fromPage == 'informationAccess'
          ) {
            if (data.validendTime) {
              const validendTime = new Date(
                data.validendTime.replace(/-/g, '/')
              );
              if (new Date() > validendTime) {
                uni.showToast({
                  icon: 'none',
                  title: '该条信息已过期，不能进行查看!',
                  success: () => {
                    setTimeout(() => {
                      this.returnBack();
                    }, 2000);
                  }
                });
                return;
              }
            }
          }
          this.showContent = true;
          if (data.isDeleted == 'Y') {
            uni.showToast({
              icon: 'none',
              title: '文件已删除'
            });
            setTimeout(() => {
              this.returnBack();
            }, 1700);
            return;
          }
          this.informationTitle = data.informationTitle;
          this.releaseDate = data.releaseDate;
          this.createDeptName = data.createDeptName;
          this.createUserName = data.createUserName;
          this.titleColor = data.titleColor;
          this.showSign = data.showSign;
          this.isMarrow = data.isMarrow;
          this.informationStatus = data.informationStatus;
          this.content = data.informationContent
            ? this.formatRichText(data.informationContent)
            : this.content;
          this.watermarkText =
            data.watermark == '1'
              ? data.watermarkText
                ? data.watermarkText
                : `${data.createUserName}${
                    this.$common.getDate('date', data.releaseDate).timeStr
                  }`
              : '';

          this.informationKits = data.informationKits;
          this.forwardList = data.forwardList;
          this.collectNumber = Number(data.collectNumber);
          this.btnContent = [
            {
              name: 'titleColor',
              text: data.titleColor == 1 ? '取消标红' : '标红',
              active: data.titleColor == 1 ? true : false
            },
            {
              name: 'showSign',
              text: data.showSign == 1 ? '取消置顶' : '置顶',
              active: data.showSign == 1 ? true : false
            },
            {
              name: 'isMarrow',
              text: data.isMarrow == 1 ? '取消加精' : '加精',
              active: data.isMarrow == 1 ? true : false
            },
            // {
            // 	name: 'isBanner',
            // 	text: data.isBanner == 1 ? '取消轮播' : '轮播',
            // 	active: data.isBanner == 1 ? true : false
            // },
            {
              name: 'informationStatus',
              text: data.informationStatus == 1 ? '取消发布' : '恢复发布',
              active: data.informationStatus == 1 ? true : false
            },
            {
              name: 'delete',
              text: '删除',
              active: false
            }
          ];
          this.$nextTick(() => {
            const query = uni.createSelectorQuery().in(this);
            query
              .select('#infoContent')
              .boundingClientRect(data => {
                let watermarkNum = parseInt(data.height / 200) * 3;
                this.watermarkNum = watermarkNum == 0 ? 3 : watermarkNum;
                this.showWatermark = true;
              })
              .exec();
          });
        });
    },
    formatRichText(html) {
      //控制正文中图片大小
      let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
        match = match
          .replace(/style="[^"]+"/gi, '')
          .replace(/style='[^']+'/gi, '');
        match = match
          .replace(/width="[^"]+"/gi, '')
          .replace(/width='[^']+'/gi, '');
        match = match
          .replace(/height="[^"]+"/gi, '')
          .replace(/height='[^']+'/gi, '');
        return match;
      });
      newContent = newContent.replace(/style="[^"]+"/gi, function(
        match,
        capture
      ) {
        match = match
          .replace(/width:[^;]+;/gi, 'max-width:100%;')
          .replace(/width:[^;]+;/gi, 'max-width:100%;');
        return match;
      });
      newContent = newContent.replace(
        /\<img/gi,
        '<img style="width:100%;height:auto;display:inline-block;"'
      );
      newContent = newContent.replace(
        new RegExp('src="/ts-document/attachment/', 'gm'),
        'src="' + this.$config.BASE_HOST + '/ts-document/attachment/'
      );
      newContent = newContent.replace(/<o:p>(.*?)<\/o:p>/g, '$1');
      return newContent;
    },
    informationOnLoad() {
      // 设置内容不超出可视范围
      const elem = this.$refs.information.$el;
      const blockElem = elem.getElementsByClassName('html-block');
      for (let i = 0; i < blockElem.length; i++) {
        blockElem[i].style.marginRight = 0;
        blockElem[i].style.marginLeft = 0;
      }
    },
    //获取阅读量
    getReadSituation(informationId) {
      this.ajax
        .getInformationReaderSituation({
          informationId: informationId
        })
        .then(res => {
          this.readNum = `${res.object.rederNumber}/${res.object.totalNumber}`;
        });
    },
    //获取附件
    initInformationFiles(informationId) {
      this.ajax
        .getInformationFiles({
          informationId: informationId
        })
        .then(res => {
          let data = res.object;
          if (null != data && data.length > 0) {
            this.isShowFileBox = true;
            this.isShowFile = true;
            this.attachmentList = data;
            this.fileCount = data.length;
            this.contentBottom = true;
          }
        });
    },
    isShowFileList() {
      this.isShowFile = !this.isShowFile;
    },
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //获取是否收藏
    selectIsCollect(informationId) {
      this.ajax
        .getInformationIsCollect({
          informationId: informationId
        })
        .then(res => {
          this.isCollect = res.object;
        });
    },
    //收藏功能
    infoCollect() {
      this.isCollect = !this.isCollect;
      if (this.isCollect) {
        this.collectNumber += 1;
        this.sendRequest('save');
      } else {
        this.collectNumber -= 1;
        this.sendRequest('deletedCollect');
      }
    },
    sendRequest(type) {
      this.ajax.confirmInformationCollect(type, {
        informationId: this.informationId
      });
    },
    //删除信息
    deleteInfo() {
      uni.showModal({
        title: '提示',
        content: '您确定删除该信息？',
        confirmText: '取消',
        cancelText: '确定',
        confirmColor: '#005BAC',
        success: function(res) {
          if (res.cancel) this.delete();
        }
      });
    },
    fabClick() {},
    //展开菜单点击事件
    trigger(e) {
      this.btnContent[e.index].active = !e.item.active;
      let title = '';
      if (e.index == 0) {
        title = e.item.active ? '取消标红' : '标红';
      } else if (e.index == 1) {
        title = e.item.active ? '取消置顶' : '置顶';
      } else if (e.index == 2) {
        title = e.item.active ? '取消加精' : '加精';
      }
      // else if(e.index == 3){
      // 	title = e.item.active ? '取消轮播' : '轮播'
      // }
      else if (e.index == 3) {
        title = e.item.active ? '取消发布' : '恢复发布';
      } else if (e.index == 4) {
        title = '删除';
      }
      if (e.index == 4) {
        uni.showModal({
          title: '提示',
          content: '您确定删除该信息？',
          confirmText: '取消',
          cancelText: '确定',
          confirmColor: '#005BAC',
          success: res => {
            if (res.cancel) this.delete();
          }
        });
      } else {
        this.btnContent[e.index].text = title;
        this.fabOperate(e);
      }
    },
    fabOperate(e) {
      let datas = {};
      datas.id = this.informationId;
      if (e.item.name == 'informationStatus') {
        datas[e.item.name] = e.item.active ? 1 : 3;
      } else {
        datas[e.item.name] = e.item.active ? 1 : 0;
      }
      this.ajax.operateInformation(datas).then(res => {
        if (res.success) {
          this[e.item.name] = e.item.active ? 1 : 0;
          uni.showToast({
            title: '操作成功',
            icon: 'none'
          });
        }
      });
    },
    delete() {
      this.ajax
        .deleteInformation({
          id: this.informationId
        })
        .then(res => {
          if (res.success) {
            this.returnBack();
          }
        });
    },
    //分类抽屉切换
    changeDrawer(tap) {
      this.drawerTap = tap;
      this.showRight = !this.showRight;
      this.ajax
        .getInformationDatas({
          id: this.informationId
        })
        .then(res => {
          this.informationKits = res.object.informationKits;
          this.forwardList = res.object.forwardList;
          this.collectNumber = Number(res.object.collectNumber);
        });
      this.getReadSituation(this.informationId);
    },

    handleRemind() {
      uni.showModal({
        title: '提示',
        content: `确定给未阅读的用户进行【消息提醒】吗？`,
        confirmText: '取消',
        cancelText: '确定',
        cancelColor: '#005BAC',
        confirmColor: '#333',
        success: async res => {
          if (res.cancel) {
            let API = this.ajax.sendUnReaderMessage;
            let res = await API(this.informationId);
            if (res.success) {
              uni.showToast({
                title: res.object || '消息提醒成功',
                duration: 2000
              });
            }
          }
        }
      });
    },

    showDrawer() {
      this.showRight = true;
    },
    closeDrawer() {
      this.showRight = false;
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getInformationReaderList({
          informationId: this.informationId,
          readerStatus: this.tabBars[index]['handleStatus'],
          pageSize: page.size,
          pageNo: page.num,
          sidx: 'inst.create_date',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      let list = [];
      rows.forEach(item => {
        let dept = {};
        dept['deptName'] = item.empDeptName;
        dept['deptCode'] = item.empDeptCode;
        dept['emprow'] = [];
        let paersonList = item.empName.split(',');
        paersonList.forEach(one => {
          let emp = one.split('-');
          dept['emprow'].push({
            name: emp[0],
            code: emp[1],
            status: emp[2]
          });
        });
        list.push(dept);
      });
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(list);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    //tab点解切换
    async ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    returnBack() {
      if (this.pageParam.isMobile) {
        uni.redirectTo({
          url: '/pages/information/information-access'
        });
      } else {
        const pages = getCurrentPages(); //获取页面栈
        if (pages.length === 1) {
          //如果只有一个调用原生js
          history.back();
        } else {
          uni.navigateBack();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  overflow: auto;
  .titleRed {
    color: #dd1f36 !important;
  }
  .content {
    font-size: 30rpx;
    color: #333;
    position: relative;
    overflow: hidden;
    zoom: 100%;
    .content_bottom {
      margin-bottom: 100rpx;
    }
    .content-text {
      padding: 20rpx 20rpx;
      box-sizing: border-box;
    }
  }
  .dataNum {
    font-size: 28rpx;
    margin-right: 30rpx;
    color: #666;
    display: flex;
    align-items: center;
    &:first-child {
      margin-left: 24rpx;
    }
    &:last-child {
      margin-right: 0;
    }
    .dataNumIcon {
      font-size: 40rpx;
      margin-right: 6rpx;
    }
    .isCollect {
      color: rgb(249, 170, 93);
    }
  }
  .info_head {
    color: #999999;
    font-size: 24rpx;
    padding: 20rpx 30rpx;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      left: 30rpx;
      right: 30rpx;
      bottom: 0;
      height: 1px;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }

    .info_title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      .info_title_top {
        color: #f59a23;
        font-size: 28rpx;
      }
      .info_title_quintessence {
        color: #3aad73;
        font-size: 28rpx;
      }
    }
    .info_head_info {
      margin: 0;
      font-size: 24rpx;
      display: inline-block;
    }
    .info_left {
      float: left;
      font-size: 28rpx;
    }
    .info_right {
      float: right;
      color: #10b47f;
      font-size: 24rpx;
    }
  }
  .info_icon::before {
    font-size: 140rpx;
    opacity: 0;
    position: absolute;
    bottom: -50rpx;
    right: 20rpx;
    text-transform: uppercase;
    transform-origin: 50% 50%;
    transform: scale(5);
    transition: all 0.3s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    color: #999;
    opacity: 0.75;
    transform: scale(1);
    z-index: 9;
  }
  .file_content {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    z-index: 12;
    .mask {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #333333;
      opacity: 0.2;
      z-index: 11;
    }
    .attachment {
      z-index: 12;
      background-color: #ffffff;
      .file {
        color: #10b47f;
        font-size: 28rpx;
        text-align: center;
        padding: 16rpx 0;
        z-index: 12;
        background-color: #ffffff;
        position: relative;
        &::before {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        .file_icon {
          margin-right: 3px;
          font-size: 36rpx;
        }
      }
      .attachment_list {
        background-color: #ffffff;
        z-index: 12;
        position: relative;
        max-height: 700rpx;
        box-sizing: border-box;
        overflow: auto;
        padding: 10rpx 20rpx 20rpx;
        .attachment_item {
          font-size: 28rpx;
          color: #333333;
          padding: 6rpx 20rpx;
          border: 1px solid #dddddd;
          border-radius: 5px;
          display: flex;
          align-items: center;
          text-decoration: none;
          margin-bottom: 20rpx;
          .oa-icon {
            font-size: 40rpx;
            margin-right: 20rpx;
            color: $theme-color;
          }
          .attachment_item_info {
            flex: 1;
            .original_name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 20rpx;
            }
            .file_size {
              color: #999999;
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }
  .drawer-title {
    height: 80rpx;
    padding: 0 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    border-bottom: 1px solid #eeeeee;
    box-sizing: border-box;
  }
  .forward-item {
    padding: 20rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    .userInfo {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      color: #666;
      font-size: 28rpx;
      .description {
        color: #333;
        font-weight: normal;
      }
    }
    .iconImg {
      width: 80rpx;
      height: 80rpx;
      margin-right: 10rpx;
      border-radius: 100%;
      color: #ffffff;
      text-align: center;
      line-height: 80rpx;
      font-size: 28rpx;
    }
    .sexMan {
      background-color: $sexman-color;
    }
    .sexWoman {
      background-color: $sexwoman-color;
    }
  }
  .read-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    .swiper_box {
      flex: 1;
      .dept-list {
        position: relative;
        padding: 0 0 20rpx 20rpx;
        &::after {
          position: absolute;
          z-index: 10;
          right: 0;
          left: 20rpx;
          height: 1px;
          bottom: 0;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        .dept-name {
          font-weight: bold;
          color: #333;
          padding: 20rpx 0;
          font-weight: 32rpx;
        }
        .emp-item {
          display: inline-block;
          font-size: 28rpx;
          padding: 4rpx 20rpx;
          border-radius: 8rpx;
          color: #999;
          border: 1px solid #ddd;
          margin: 0 20rpx 20rpx 0;
        }
        .isRead {
          color: #005bac;
          border-color: rgba(45, 18, 235, 0.15);
          background-color: #e5e7ff;
        }
      }
    }
    .swiper_head {
      position: relative;
      flex: 1;
      height: 80rpx;
      background-color: #ffffff;
      flex-direction: row;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
      /* flex-wrap: nowrap; */
      /* border-color: #cccccc;
				border-bottom-style: solid;
				border-bottom-width: 1px; */
      &::before,
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &::before {
        top: 0;
      }
      &::after {
        bottom: 0;
      }
      .uni-tab-item {
        /* #ifndef APP-PLUS */
        display: inline-block;
        /* #endif */
        flex-wrap: nowrap;
        width: 33.33%;
        height: 80rpx;
        box-sizing: border-box;
        text-align: center;
        .uni-tab-item-title {
          color: #666;
          font-size: 28rpx;
          font-weight: normal;
          height: 100%;
          line-height: 2.5;
          flex-wrap: nowrap;
          box-sizing: border-box;
          /* #ifndef APP-PLUS */
          white-space: nowrap;
          /* #endif */
        }
        .uni-tab-item-num {
          font-size: 28rpx;
          color: #fff;
          background-color: #f59a23;
          border-radius: 40rpx;
          padding: 0 10rpx;
          margin: 0 10rpx;
        }
        .uni-tab-item-title-active {
          color: $theme-color;
          border-bottom: 2px solid $theme-color;
        }
      }
    }
  }
  .deleteBtn {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 55px;
    height: 55px;
    background-color: rgb(0, 122, 255);
    border-radius: 55px;
    z-index: 11;
    box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
    right: 15px;
    bottom: 60px;
    color: #ffffff;
    cursor: pointer;
    font-weight: bold;
  }
}
</style>
