<template>
  <view class="w-picker-view">
    <picker-view
      class="d-picker-view"
      :indicator-style="itemHeight"
      :value="pickVal"
      @change="handlerChange"
    >
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.fyears"
          :key="index"
          >{{ item }}年</view
        >
      </picker-view-column>
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.fmonths"
          :key="index"
          >{{ item }}月</view
        >
      </picker-view-column>
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.fdays"
          :key="index"
          >{{ item }}日</view
        >
      </picker-view-column>
      <picker-view-column class="w-picker-flex1">
        <view class="w-picker-item">-</view>
      </picker-view-column>
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.tyears"
          :key="index"
          >{{ item }}年</view
        >
      </picker-view-column>
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.tmonths"
          :key="index"
          >{{ item }}月</view
        >
      </picker-view-column>
      <picker-view-column class="w-picker-flex2">
        <view
          class="w-picker-item"
          v-for="(item, index) in range.tdays"
          :key="index"
          >{{ item }}日</view
        >
      </picker-view-column>
    </picker-view>
  </view>
</template>

<script>
/**
 * rangPicker 时间范围选择器
 * @description 时间选择器组件，日期范围选择
 * @property {String} itemHeight  组件高度
 * @property {Array} value  当前选中日期范围
 * @property {Boolean} current = [ture|false] 是否默认选中当前日期
 * @property {String} startDate  默认选择范围开始时间
 * @property {String} endDate  默认选择范围结束时间
 * @event {Function} change 打开关闭弹出选择器，e={show: false}
 */
export default {
  data() {
    return {
      pickVal: [],
      range: {},
      checkObj: {}
    };
  },
  props: {
    itemHeight: {
      type: String,
      default: '44rpx'
    },
    value: {
      type: [String, Array],
      default() {
        return [];
      }
    },
    current: {
      type: Boolean,
      default: false
    },
    startDate: {
      //开始时间
      type: String,
      default: '1920-01-01'
    },
    endDate: {
      //
      type: String,
      default: '2100-12-31'
    }
  },
  watch: {
    value(val) {
      this.initData();
    }
  },
  created() {
    this.initData();
  },
  methods: {
    formatNum(n) {
      return Number(n) < 10 ? '0' + Number(n) : Number(n) + '';
    },
    //检查日期格式
    checkValue(value) {
      let strReg = /^\d{4}-\d{2}-\d{2}$/,
        example = '2020-04-03';
      if (!strReg.test(value[0]) || !strReg.test(value[1])) {
        console.log(
          new Error(
            '请传入与modetype匹配的value值，例[' + example + ',' + example + ']'
          )
        );
      }
      return strReg.test(value[0]) && strReg.test(value[1]);
    },
    //初始化时间范围选择器
    initData() {
      let range = [],
        pickVal = [];
      let result = '',
        full = '',
        obj = {};
      //获取默认日期区间字符串数组
      let dVal = this.getDval();
      //获取
      let dateData = this.getData(dVal);
      let fyears = [],
        fmonths = [],
        fdays = [],
        tyears = [],
        tmonths = [],
        tdays = [];
      let fyear, fmonth, fday, tyear, tmonth, tday;
      pickVal = dateData.pickVal;
      fyears = dateData.fyears;
      fmonths = dateData.fmonths;
      fdays = dateData.fdays;
      tyears = dateData.tyears;
      tmonths = dateData.tmonths;
      tdays = dateData.tdays;
      range = {
        fyears,
        fmonths,
        fdays,
        tyears,
        tmonths,
        tdays
      };
      fyear = range.fyears[pickVal[0]];
      fmonth = range.fmonths[pickVal[1]];
      fday = range.fdays[pickVal[2]];
      tyear = range.tyears[pickVal[4]];
      tmonth = range.tmonths[pickVal[5]];
      tday = range.tdays[pickVal[6]];
      obj = {
        fyear,
        fmonth,
        fday,
        tyear,
        tmonth,
        tday
      };
      result = `${fyear +
        '-' +
        fmonth +
        '-' +
        fday +
        ' 至 ' +
        tyear +
        '-' +
        tmonth +
        '-' +
        tday}`;
      full = `${fyear +
        '年' +
        fmonth +
        '月' +
        fday +
        '日' +
        ' - ' +
        tyear +
        '年' +
        tmonth +
        '月' +
        tday +
        '日'}`;
      this.range = range;
      this.checkObj = obj;
      this.$nextTick(() => {
        this.pickVal = pickVal;
      });
      this.$emit('change', {
        result: result,
        value: full,
        obj: obj
      });
    },
    //获取默认日期区间字符串数组
    getDval() {
      let value = this.value;
      let dVal = null;
      let aDate = new Date();
      let fyear = this.formatNum(aDate.getFullYear());
      let fmonth = this.formatNum(aDate.getMonth() + 1);
      let fday = this.formatNum(aDate.getDate());
      let tyear = this.formatNum(aDate.getFullYear());
      let tmonth = this.formatNum(aDate.getMonth() + 1);
      let tday = this.formatNum(aDate.getDate());
      if (value && value.length > 0) {
        let flag = this.checkValue(value);
        if (flag) {
          dVal = [...value[0].split('-'), '-', ...value[1].split('-')];
        } else {
          dVal = [fyear, fmonth, fday, '-', tyear, tmonth, tday];
        }
      } else {
        dVal = [fyear, fmonth, fday, '-', tyear, tmonth, tday];
      }
      return dVal;
    },
    getData(dVal) {
      let _self = this;
      let rang_dVal = [
        ..._self.startDate.split('-'),
        '-',
        ..._self.endDate.split('-')
      ]; //日期范围字符串数组
      let value_dVal = dVal; //当前选中范围字符串数组
      let initStartDate = new Date(_self.startDate.replace(/-/g, '/')); //开始时间默认初始值（初始时间戳）
      let initEndDate = new Date(_self.endDate.replace(/-/g, '/')); //结束时间默认初始值（初始时间戳）
      let valueStartDate = new Date(
        value_dVal[0] * 1 + '/' + value_dVal[1] * 1 + '/' + value_dVal[2] * 1
      ); //当前所选开始时间值（时间戳）
      let valueEndDate = new Date(
        value_dVal[4] * 1 + '/' + value_dVal[5] * 1 + '/' + value_dVal[6] * 1
      ); //当前所选结束时间值（时间戳）
      let sYear, sMonth, sDay;
      //获取时间范围的第一个选项（开始时间）的各部分开始值
      if (initStartDate >= valueStartDate) {
        sYear = valueStartDate.getFullYear();
        sMonth = valueStartDate.getMonth() + 1;
        sDay = valueStartDate.getDate();
      } else {
        sYear = initStartDate.getFullYear();
        sMonth = initStartDate.getMonth() + 1;
        sDay = initStartDate.getDate();
      }
      let initEndYear = initEndDate.getFullYear(); //默认初始结束年
      let fyears = [],
        fmonths = [],
        fdays = [],
        tyears = [],
        tmonths = [],
        tdays = [];
      //获取开始年可选值数组
      for (let y = sYear; y <= initEndYear; y++) {
        fyears.push(this.formatNum(y));
      }
      //获取开始月可选值数组
      if (sYear >= value_dVal[0] * 1) {
        for (let m = sMonth; m <= 12; m++) {
          fmonths.push(this.formatNum(m));
        }
      } else {
        for (let m = 1; m <= 12; m++) {
          fmonths.push(this.formatNum(m));
        }
      }
      //获取开始日可选值数组
      let sTotalDays = new Date(
        value_dVal[0] * 1,
        value_dVal[1] * 1,
        0
      ).getDate();
      if (sYear >= value_dVal[0] * 1 && sMonth == value_dVal[1] * 1) {
        for (let m = sDay; m <= sTotalDays; m++) {
          fdays.push(this.formatNum(m));
        }
      } else {
        for (let m = 1; m <= sTotalDays; m++) {
          fdays.push(this.formatNum(m));
        }
      }

      //获取结束年可选值数组
      for (let s = value_dVal[0] * 1; s <= initEndYear; s++) {
        tyears.push(this.formatNum(s));
      }
      //获取结束月可选值数组
      if (value_dVal[4] * 1 == value_dVal[0] * 1) {
        for (let m = value_dVal[1] * 1; m <= 12; m++) {
          tmonths.push(this.formatNum(m));
        }
      } else {
        for (let m = 1; m <= 12; m++) {
          tmonths.push(this.formatNum(m));
        }
      }
      //获取结束日可选值数组
      let eTotalDays = new Date(value_dVal[4], value_dVal[5], 0).getDate();
      if (
        value_dVal[4] * 1 == value_dVal[0] * 1 &&
        value_dVal[5] * 1 == value_dVal[1] * 1
      ) {
        for (let d = value_dVal[2] * 1; d <= eTotalDays; d++) {
          tdays.push(this.formatNum(d));
        }
      } else {
        for (let d = 1; d <= eTotalDays; d++) {
          tdays.push(this.formatNum(d));
        }
      }
      let pickVal = [
        fyears.indexOf(value_dVal[0]) == -1 ? 0 : fyears.indexOf(value_dVal[0]),
        fmonths.indexOf(value_dVal[1]) == -1
          ? 0
          : fmonths.indexOf(value_dVal[1]),
        fdays.indexOf(value_dVal[2]) == -1 ? 0 : fdays.indexOf(value_dVal[2]),
        0,
        tyears.indexOf(value_dVal[4]) == -1 ? 0 : tyears.indexOf(value_dVal[4]),
        tmonths.indexOf(value_dVal[5]) == -1
          ? 0
          : tmonths.indexOf(value_dVal[5]),
        tdays.indexOf(value_dVal[6]) == -1 ? 0 : tdays.indexOf(value_dVal[6])
      ];
      return {
        fyears,
        fmonths,
        fdays,
        tyears,
        tmonths,
        tdays,
        pickVal
      };
    },
    handlerChange(e) {
      let arr = [...e.detail.value];
      let result = '',
        full = '',
        obj = {};
      let year = '',
        month = '',
        day = '',
        hour = '',
        minute = '',
        second = '',
        note = [],
        province,
        city,
        area;
      let checkObj = this.checkObj;
      let days = [],
        months = [],
        endYears = [],
        endMonths = [],
        endDays = [],
        startDays = [];
      let modetype = this.modetype;
      let col1, col2, col3, d, a, h, m;
      let xDate = new Date().getTime();
      let range = this.range;
      let fyear = range.fyears[arr[0]] || range.fyears[range.fyears.length - 1];
      let fmonth =
        range.fmonths[arr[1]] || range.fmonths[range.fmonths.length - 1];
      let fday = range.fdays[arr[2]] || range.fdays[range.fdays.length - 1];
      let tyear = range.tyears[arr[4]] || range.tyears[range.tyears.length - 1];
      let tmonth =
        range.tmonths[arr[5]] || range.tmonths[range.tmonths.length - 1];
      let tday = range.tdays[arr[6]] || range.tdays[range.tdays.length - 1];
      let resetData = this.resetData(fyear, fmonth, fday, tyear, tmonth);
      if (fyear != checkObj.fyear) {
        range.fmonths = resetData.fmonths;
        if (range.fmonths.indexOf(fmonth) == -1) {
          arr[1] = 0;
        } else {
          arr[1] = range.fmonths.indexOf(fmonth);
        }
        let newfmonth = range.fmonths[arr[1]];
        if (fmonth !== newfmonth) {
          fmonth = newfmonth;
          let newtotal = new Date(fyear, newfmonth, 0).getDate();
          let p_dVal = [
            ...this.startDate.split('-'),
            '-',
            ...this.endDate.split('-')
          ];
          let fdaysArr = [];
          for (let i = p_dVal[2] * 1; i <= newtotal; i++) {
            fdaysArr.push(this.formatNum(i));
          }
          resetData.fdays = fdaysArr;
        }
      }
      if (
        fyear != checkObj.fyear ||
        fmonth != checkObj.fmonth ||
        fday != checkObj.fday
      ) {
        arr[4] = 0;
        arr[5] = 0;
        arr[6] = 0;
        range.tyears = resetData.tyears;
        range.tmonths = resetData.tmonths;
        range.tdays = resetData.tdays;
        tyear = range.tyears[0];
        checkObj.tyears = range.tyears[0];
        tmonth = range.tmonths[0];
        checkObj.tmonths = range.tmonths[0];
        tday = range.tdays[0];
        checkObj.tdays = range.tdays[0];
      }
      if (fyear != checkObj.fyear || fmonth != checkObj.fmonth) {
        range.fdays = resetData.fdays;
        let fdayNum = fday * 1;
        let lastfdayNum = range.fdays[range.fdays.length - 1];
        let firstfdayNum = range.fdays[0];
        if (range.fdays.indexOf(fday) == -1) {
          if (fdayNum > lastfdayNum) {
            arr[2] = range.fdays.length;
            fday = lastfdayNum;
          } else if (fdayNum < firstfdayNum) {
            arr[2] = 0;
            fday = firstfdayNum;
          }
        } else {
          arr[2] = range.fdays.indexOf(fday);
        }
      }
      if (tyear != checkObj.tyear) {
        arr[5] = 0;
        arr[6] = 0;
        let toData = this.resetToData(fmonth, fday, tyear, tmonth);
        range.tmonths = toData.tmonths;
        range.tdays = toData.tdays;
        tmonth = range.tmonths[0];
        checkObj.tmonths = range.tmonths[0];
        tday = range.tdays[0];
        checkObj.tdays = range.tdays[0];
      }
      if (tmonth != checkObj.tmonth) {
        arr[6] = 0;
        let toData = this.resetToData(fmonth, fday, tyear, tmonth);
        range.tdays = toData.tdays;
        tday = range.tdays[0];
        checkObj.tdays = range.tdays[0];
      }
      result = `${fyear +
        '-' +
        fmonth +
        '-' +
        fday +
        ' 至 ' +
        tyear +
        '-' +
        tmonth +
        '-' +
        tday}`;
      full = `${fyear +
        '年' +
        fmonth +
        '月' +
        fday +
        '日' +
        ' - ' +
        tyear +
        '年' +
        tmonth +
        '月' +
        tday +
        '日'}`;
      obj = {
        fyear,
        fmonth,
        fday,
        tyear,
        tmonth,
        tday
      };
      this.checkObj = obj;
      this.$nextTick(() => {
        this.pickVal = arr;
      });
      this.$emit('change', {
        result: result,
        value: full,
        obj: obj
      });
    },
    resetToData(fmonth, fday, tyear, tmonth) {
      let range = this.range;
      let tmonths = [],
        tdays = [];
      let yearFlag = tyear != range.tyears[0];
      let monthFlag = tyear != range.tyears[0] || tmonth != range.tmonths[0];
      let ttotal = new Date(tyear, tmonth, 0).getDate();
      for (let i = yearFlag ? 1 : fmonth * 1; i <= 12; i++) {
        tmonths.push(this.formatNum(i));
      }
      for (let i = monthFlag ? 1 : fday * 1; i <= ttotal; i++) {
        tdays.push(this.formatNum(i));
      }
      return {
        tmonths,
        tdays
      };
    },
    resetData(fyear, fmonth, fday, tyear, tmonth) {
      let fyears = [],
        fmonths = [],
        fdays = [],
        tyears = [],
        tmonths = [],
        tdays = [];
      let _self = this;
      let p_dVal = [
        ..._self.startDate.split('-'),
        '-',
        ..._self.endDate.split('-')
      ];
      let startYear = p_dVal[0] * 1;
      let endYear = p_dVal[4] * 1;
      let ftotal = new Date(fyear, fmonth, 0).getDate();
      let ttotal = new Date(tyear, tmonth, 0).getDate();
      for (let i = startYear * 1; i <= endYear; i++) {
        fyears.push(this.formatNum(i));
      }
      if (fyear == startYear) {
        for (let i = p_dVal[1] * 1; i <= 12; i++) {
          fmonths.push(this.formatNum(i));
        }
      } else {
        for (let i = 1; i <= 12; i++) {
          fmonths.push(this.formatNum(i));
        }
      }
      if (fyear == startYear && fmonth == p_dVal[1] * 1) {
        for (let i = p_dVal[2] * 1; i <= ftotal; i++) {
          fdays.push(this.formatNum(i));
        }
      } else {
        for (let i = 1; i <= ftotal; i++) {
          fdays.push(this.formatNum(i));
        }
      }
      for (let i = fyear * 1; i <= endYear; i++) {
        tyears.push(this.formatNum(i));
      }
      for (let i = fmonth * 1; i <= 12; i++) {
        tmonths.push(this.formatNum(i));
      }
      for (let i = fday * 1; i <= ttotal; i++) {
        tdays.push(this.formatNum(i));
      }
      return {
        fyears,
        fmonths,
        fdays,
        tyears,
        tmonths,
        tdays
      };
    }
  }
};
</script>

<style lang="scss">
@import './../date-picker.css';
</style>
