<template>
  <view ref="tabHeader" class="base-tabs">
    <view
      v-for="(tab, index) in tabs"
      :key="index"
      class="base-tab-item"
      :data-current="index"
      @click="tabClick(index)"
      :class="value == index ? 'base-tab-item-title-active' : ''"
    >
      <slot :tab="tab">
        <text class="base-tab-item-title">{{ tab.name }}</text>
      </slot>
    </view>
    <view ref="activeLine" class="text-line"></view>
  </view>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: [String, Number],
    tabs: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    tabClick(index) {
      if (!this.tabs.length) {
        return;
      }
      let header = this.$refs.tabHeader?.$el;
      if (!header) {
        this.$nextTick(() => this.tabClick(index));
        return;
      }
      let activeNode = header.querySelector(
          `.base-tab-item:nth-child(${index + 1})`
        ),
        offsetLeft = activeNode.offsetLeft,
        line = this.$refs.activeLine.$el,
        left = activeNode.offsetWidth / 2 + offsetLeft - line.offsetWidth / 2;
      line.style.transform = `translate(${left}px)`;
      this.$emit('input', index);
      this.$emit('change', index);
    }
  },
  watch: {
    value: {
      handler(val = 0) {
        this.tabClick(val);
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.base-tabs {
  display: flex;
  background-color: #ffffff;
  overflow: hidden;
  line-height: 0;
  position: relative;
  .base-tab-item {
    display: inline-block;
    flex-wrap: nowrap;
    flex: 1;
    height: 42px;
    line-height: 42px;
    font-size: 14px;
    box-sizing: border-box;
    text-align: center;
    .base-tab-item-title,
    .base-tab-item-num {
      color: #666;
      height: 100%;
      font-size: 14px;
      flex-wrap: nowrap;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    .base-tab-item-num {
      font-size: 14px;
      color: #fff;
      background-color: #f59a23;
    }
    &.base-tab-item-title-active .base-tab-item-title {
      color: $theme-color;
    }
  }
  .text-line {
    position: absolute;
    bottom: 0;
    border-radius: 100px;
    width: 30px;
    transition-duration: 300ms;
    height: 2px;
    background-color: $theme-color;
  }
}
</style>
