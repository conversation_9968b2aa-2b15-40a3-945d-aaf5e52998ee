import { cssStr } from '@/trasenLayout/components/odlProjectCss';

export default {
  data() {
    return {
      show: true,
      src: '',
      iframe: '',
      js: {}
    };
  },
  props: {
    leftbg: {
      type: Number,
      default: 0
    }
  },
  created() {
    this.isChildApp(this.$route.path);
    this.src = `/#${this.$route.path}`;
  },
  methods: {
    /**@desc 模拟点击老项目一级菜单
     * @param {Object} obj**/
    updateFirstMenu(obj) {
      let domStr = `#top-nav li.nav-item a[menu-id="${obj.id}"]`;
      let dom = this.iframe.document.querySelector(domStr);
      if (dom) {
        dom.click();
      }
    },
    closeContextMenu() {
      this.$root.$emit('closeContextMenu');
    },
    /**@desc 删除以及更新路由页面
     * @param {Object} _obj**/
    curdRouter(_obj) {
      let obj = _obj.detail;
      if (obj.type == 'deleteRouter') {
        this.deleteRouter(obj);
      } else if (obj.type == 'updateRouter') {
        this.updateRouter(obj);
      }
    },
    /**@desc 删除老项目单个页面缓存
     * @param {Object} obj**/
    deleteRouter(obj) {
      let domStr = `div.cuttab-box[menu-id="${obj.id}"]`;
      let dom = this.iframe.document.querySelector(domStr);
      if (dom) {
        let colseDom = dom.querySelector('.cuttab-close');
        colseDom.click();
      }
    },
    /**@desc 刷新老项目单个页面**/
    updateRouter(obj) {
      let domHtml = this.iframe.document.querySelector(
        `.content-hide-box[menu-id="${obj.id}"]`
      );
      if (domHtml) {
        let dom = this.iframe.document.querySelector('#cuttab-contextmenu');
        dom.setAttribute('menu-id', obj.id);
        if (dom) {
          let refreshDom = dom.querySelector('#refreshCut');
          refreshDom.click();
        }
      }
    },
    /**@desc 绑定iframe hsah事件**/
    onhashchange() {
      this.$refs.iframe.contentWindow.addEventListener('hashchange', () => {
        let hash = this.iframe.location.hash.slice(1).replace(/\?.+/, '');
        if (hash != this.$route.path && !this.isChildApp(this.$route.path)) {
          this.$router.push(hash);
        }
      });
      /**@desc 等页面加载完成初始化websocket**/
      this.iframe.onLoad = () => {
        this.webSocketClient();
      };

      /**@desc 模拟老项目点击事件外放出来**/
      this.iframe.document
        .querySelector('body')
        .addEventListener('click', this.closeContextMenu);
    },
    /**@desc 监听元素是否加载完成**/
    getIframeDom() {
      setTimeout(() => {
        this.iframe = this.$refs.iframe.contentWindow;
        window._iframe = this.$refs.iframe.contentWindow;
        if (this.iframe.document.querySelector('#mainContentBox')) {
          let style = this.$refs.iframe.contentWindow.document.createElement(
            'DIV'
          );
          style.id = 'old-oa-css';
          this.iframe.document.querySelector('body').append(style);
          style.innerHTML = cssStr; //注入页面样式
          this.onhashchange(); //注入监听方法
        } else {
          this.getIframeDom();
        }
      });
    },
    /**@desc 判断是否在子应用**/
    isChildApp(path) {
      let index = 0;
      this.$store.state.common.qiankuanapps.forEach(item => {
        if (path.indexOf(item.userData.packageName) != -1) {
          index++;
        }
      });
      this.show = index ? true : false;
      return index;
    },
    /**@desc 更新老项目面包绡**/
    isCollapseFun() {
      let domHtml = this.iframe.document.querySelector(`.toggleLeftMenu`);
      if (domHtml) {
        domHtml.click();
      }
    },
    /**@desc 向老项目发送websokcet消息**/
    webSocketMessage(obj) {
      this.iframe.dispatchEvent(
        new this.iframe.CustomEvent('webSocketMessage', { detail: obj.detail })
      );
    }
  },
  beforeDestroy() {
    /**@desc 接收页签自定义事件**/
    window.removeEventListener('updateDataQianKun', this.curdRouter, false);
    window.removeEventListener(
      'webSocketMessage',
      this.webSocketMessage,
      false
    );
    this.$root.$off('isCollapse', this.isCollapse);
    this.$root.$off('updateFirstMenu', this.updateFirstMenu);
    this.$refs.iframe.contentWindow.removeEventListener(
      'click',
      this.closeContextMenu
    );
  },
  mounted() {
    this.getIframeDom();
    window.addEventListener('updateDataQianKun', this.curdRouter, false);
    window.addEventListener('webSocketMessage', this.webSocketMessage, false);
    this.$root.$on('isCollapse', this.isCollapseFun);
    this.$root.$on('updateFirstMenu', this.updateFirstMenu);
  },
  watch: {
    $route(to) {
      if (!this.isChildApp(to.path)) {
        this.iframe.location.hash = to.path;
      }
    }
  }
};
