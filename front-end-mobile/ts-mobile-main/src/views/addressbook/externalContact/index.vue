<template>
  <div class="ts-container">
    <div class="ts-navbar">
      <div class="ts-navbar-content bottom-line">
        <div class="ts-back-wrap" @click="$router.back()">
          <van-icon name="arrow-left" />
        </div>
        <span class="ts-navbar-content-title">个人信息</span>
      </div>
      <div class="ts-navbar-placeholder"></div>
    </div>
    <div class="page-content" v-if="form">
      <div class="user-info">
        <!-- <div class="user-avatar"> -->
        <van-image
          src="form.avata"
          lazy-load
          class="user-avatar"
          error-icon="user-o"
          loading-icon="user-o"
        >
        </van-image>
        <!-- </div> -->
        <div class="user-details">
          <div style="font-size:16px;margin-top:20px;">
            <span>{{ form.linkmanName }}</span>
            <img
              src="@/assets/images/sex-man.png"
              class="sex-icon"
              v-if="form.linkmanSex == 0"
            />
            <img
              src="@/assets/images/sex-woman.png"
              class="sex-icon"
              v-elseif="form.linkmanSex == 1"
            />
          </div>
        </div>
      </div>
      <div style="height:8px;background:#F4F4F4;"></div>
      <div class="contract-card bottom-line">
        <div class="cell-label">邮箱</div>
        <div class="cell-value">
          <input
            type="text"
            v-model="form.linkmanEmail"
            :readonly="!isEdit"
            ref="linkmanEmail"
          />
        </div>
      </div>
      <div class="contract-card bottom-line">
        <div class="cell-label">手机号码</div>
        <div class="cell-value">
          <input type="text" v-model="form.mobilePhone" :readonly="!isEdit" />
          <div
            v-show="!isEdit"
            class="phone-icon"
            @click="call(form.mobilePhone)"
          ></div>
        </div>
      </div>
      <div class="cell-label bottom-line">
        <span>单位：</span>
        <input type="text" :readonly="!isEdit" v-model="form.linkmanUnit" />
      </div>
      <div class="cell-label bottom-line">
        <span>部门：</span>
        <input type="text" :readonly="!isEdit" v-model="form.linkmanDepart" />
      </div>
      <div class="cell-label bottom-line">
        <span>职务：</span>
        <input type="text" :readonly="!isEdit" v-model="form.linkmanDuty" />
      </div>
      <div class="cell-label bottom-line">
        <span>岗位：</span>
        <input
          type="text"
          :readonly="!isEdit"
          v-model="form.linkmanProfession"
        />
      </div>
      <div class="cell-label bottom-line">
        <span>备注：</span>
        <input type="text" :readonly="!isEdit" v-model="form.linkmanDescribe" />
      </div>
    </div>
    <div class="page-bottom" v-show="!isEdit">
      <div class="options">
        <div class="delete" @click="deleteData">删除</div>
        <div
          class="edit"
          @click="
            isEdit = true;
            $refs.linkmanEmail.focus();
          "
        >
          编辑
        </div>
      </div>
    </div>
    <div class="page-bottom" v-show="isEdit">
      <div class="options">
        <div
          class="cancel"
          @click="
            isEdit = false;
            getData();
          "
        >
          取消
        </div>
        <div class="save" @click="update">保存</div>
      </div>
    </div>
  </div>
</template>

<script>
import index from './index.js';
export default {
  name: 'externalContact',
  mixins: [index]
};
</script>

<style scoped lang="scss">
.ts-container {
  background-color: #fff;
  color: #333;
  .page-content {
    padding-bottom: 170px;
    .user-info {
      padding: 16px 15px;
      display: flex;
      position: relative;
      .sex-icon {
        width: 17px;
        height: 17px;
        vertical-align: text-top;
        margin-left: 16px;
      }
      .user-avatar {
        height: 60px;
        width: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16px;
        img {
          height: 60px;
          width: 60px;
        }
      }
      .user-details {
        flex: 1;
      }
    }
    .contract-card {
      position: relative;
    }
    .cell-label,
    .cell-value {
      font-size: 16px;
      padding: 12px 15px;
      position: relative;
      input {
        border: none;
        padding: 0;
        font-size: 16px;
      }
      img {
        height: 22px;
        width: 22px;
        vertical-align: middle;
        margin-right: 10px;
      }
    }
    .cell-value {
      color: #666;
    }
    .phone-icon {
      background: url('../../../assets/images/phone.png') no-repeat center;
      background-size: 22px 22px;
      position: absolute;
      width: 40px;
      height: 40px;
      right: 15px;
      top: 2px;
      border-radius: 50%;
      &:active {
        background-color: #efefef;
      }
    }
  }
  .page-bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0px -2px 2px 0px #e4e4e4;
    // padding-bottom: 34px;
  }
  .options {
    height: 44px;
    line-height: 44px;
    display: flex;
    .edit,
    .delete,
    .cancel,
    .save {
      flex: 1;
      text-align: center;
      font-size: 18px;
      color: #005bac;
      transition: background-color 0.2s;
      &:active {
        background-color: #efefef;
      }
    }
    .delete {
      color: #e24242;
    }
    .cancel {
      color: #666;
    }
  }
  .switch {
    height: 44px;
    line-height: 44px;
    display: flex;
    .prev,
    .next {
      flex: 1;
      padding: 0 15%;
      text-align: right;
      i {
        font-weight: bold;
        font-size: 18px;
        color: #666;
      }
      &:active {
        background-color: #efefef;
      }
    }
    .next {
      text-align: left;
    }
  }
}
</style>
