import { Dialog } from 'vant';
import { Image as VanImage } from 'vant';
export default {
  components: {
    [Dialog.name]: Dialog,
    [VanImage.name]: VanImage
  },
  data() {
    return {
      isEdit: false,
      form: null,
      pageNo: this.$route.query.index
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      if (this.pageNo == -1) {
        this.$toast('没有更多了!');
        this.pageNo++;
        return false;
      }
      this.ajax
        .getOutterContractList({
          pageNo: Number(this.pageNo) + 1,
          pageSize: 1
        })
        .then(res => {
          if (res) {
            if (res.rows.length == 0) {
              this.$toast('没有更多了!');
              this.pageNo--;
              return false;
            }
            this.form = res.rows[0];
          }
        });
    },
    call(phone) {
      window.location.href = 'tel://' + phone;
    },
    deleteData() {
      Dialog.confirm({
        title: '删除',
        message: '确定删除？'
      })
        .then(() => {
          this.ajax.removeOutterContact(this.form.id).then(res => {
            if (res) {
              if (res.success) {
                this.$toast('删除成功!');
                this.$router.push('/addressbook');
              }
            }
          });
        })
        .catch(err => {});
    },
    update() {
      this.ajax.editOutterContact(this.form).then(res => {
        if (res) {
          if (res.success) {
            this.$toast('编辑成功!');
            this.getData();
            this.isEdit = false;
          }
        }
      });
    }
  }
};
