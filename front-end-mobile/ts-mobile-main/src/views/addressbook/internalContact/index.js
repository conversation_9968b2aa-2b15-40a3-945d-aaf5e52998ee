import { Image as VanImage, ImagePreview } from 'vant';
export default {
  components: {
    [VanImage.name]: VanImage
  },
  data() {
    return {
      pageNo: this.$route.query.index,
      form: null,
      showSwitch: true
    };
  },
  created() {
    let _d = this.$route.query.data;
    if (_d) {
      this.form = _d;
      this.showSwitch = false;
    } else {
      this.getData();
    }
  },
  methods: {
    handleReviewAvatar(row) {
      if (!row.avatar) return;
      ImagePreview({
        images: [row.avatar],
        closeable: true
      });
    },
    call(phone) {
      window.location.href = 'tel:' + phone;
    },
    getData() {
      if (this.pageNo == -1) {
        this.$toast('没有更多了!');
        this.pageNo++;
        return false;
      }
      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '加载中...',
        forbidClick: true,
        duration: 0
      });
      this.ajax
        .getInnerContractList({
          pageNo: Number(this.pageNo) + 1,
          pageSize: 1,
          employeeName: this.$route.query.condition
        })
        .then(res => {
          if (res) {
            this.$toast.clear();
            if (res.rows.length == 0) {
              this.$toast('没有更多了!');
              this.pageNo--;
              return false;
            }
            this.form = res.rows[0];
          }
        });
    }
  }
};
