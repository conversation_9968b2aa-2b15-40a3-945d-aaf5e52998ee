import { ImagePreview } from 'vant';

export default {
  data() {
    return {
      scrollTopArr: [0, 0, 0, 0, 0, 0],
      searchArr: ['', '', '', '', '', ''],

      tabNum: 0,
      searchVal: '',

      shrinkArr2: [], //收起的群组 index
      shrinkArr3: [], //收起的群组 index
      refreshing0: false,
      refreshing1: false,
      refreshing2: false,
      refreshing3: false,
      refreshing4: false,
      refreshing5: false,
      loading0: false,
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      finished0: false,
      finished1: false,
      finished2: false,
      finished3: false,
      finished4: false,
      finished5: false,
      pageNo0: 1,
      pageNo1: 1,
      pageNo2: 1,
      pageNo3: 1,
      pageNo4: 1,
      pageNo5: 1,
      contractArr0: [], //内部联系人
      contractArr1: [], //系统群组
      contractArr2: [], //个人群组
      contractArr3: [], //科室值班通讯录
      contractArr4: [], //科室通讯录
      contractArr5: [] //外部联系人
    };
  },
  methods: {
    // 切换tabs 记录之前位置
    beforeChange() {
      this.scrollTopArr[this.tabNum] = document.scrollingElement.scrollTop;
      this.searchArr[this.tabNum] = this.searchVal;

      return new Promise(resolve => {
        resolve(true);
      });
    },
    // 保留之前位置
    changeTab() {
      setTimeout(() => {
        document.scrollingElement.scrollTop = this.scrollTopArr[this.tabNum];
        this.searchVal = this.searchArr[this.tabNum];
      }, 0);
    },

    search() {
      this['pageNo' + this.tabNum] = 1;
      this['finished' + this.tabNum] = false;
      this['contractArr' + this.tabNum] = [];
      this['loading' + this.tabNum] = true;
      this.getData();
    },

    onLoad() {
      if (this['refreshing' + this.tabNum]) {
        this['loading' + this.tabNum] = false;
        return false;
      }
      this.getData();
    },

    onRefresh() {
      this['pageNo' + this.tabNum] = 1;
      this['finished' + this.tabNum] = false;
      this.searchVal = '';
      this.getData();
      if (this['shrinkArr' + this.tabNum]) {
        this['shrinkArr' + this.tabNum] = [];
      }
    },

    getData() {
      if (this.tabNum == 0) {
        //内部联系人
        this.ajax
          .getInnerContractList({
            pageNo: this.pageNo0,
            pageSize: 100,
            employeeName: this.searchVal
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing0 = false;
              }, 500);
              if (this.pageNo0 == 1) {
                this.contractArr0 = [];
              }
              this.contractArr0.push(...res.rows);
              this.loading0 = false;
              this.pageNo0++;
              if (this.contractArr0.length >= res.totalCount) {
                this.finished0 = true;
              }
            }
          });
      } else if (this.tabNum == 1) {
        //科室通讯录
        this.ajax
          .getAllOrgList({
            pageNo: this.pageNo1,
            pageSize: 100,
            orgName: this.searchVal
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing1 = false;
              }, 500);
              if (this.pageNo1 == 1) {
                this.contractArr1 = [];
              }
              this.contractArr1.push(...res.rows);
              this.loading1 = false;
              this.pageNo1++;
              if (this.contractArr1.length >= res.totalCount) {
                this.finished1 = true;
              }
            }
          });
      } else if (this.tabNum == 2) {
        //系统群组
        this.ajax
          .getOrgGroupList({
            pageNo: this.pageNo2,
            pageSize: 9999,
            groupName: this.searchVal,
            groupType: 0
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing2 = false;
              }, 500);
              if (this.pageNo2 == 1) {
                this.contractArr2 = [];
              }
              this.contractArr2.push(...res.object);
              this.loading2 = false;
              this.pageNo2++;
              if (res.object.length < 5) {
                this.finished2 = true;
              }
            }
          });
      } else if (this.tabNum == 3) {
        //个人群组
        this.ajax
          .getPersonealGroupList({
            pageNo: this.pageNo3,
            pageSize: 9999,
            groupName: this.searchVal,
            groupType: 1
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing3 = false;
              }, 500);
              if (this.pageNo3 == 1) {
                this.contractArr3 = [];
              }
              this.contractArr3.push(...res.object);
              this.loading3 = false;
              this.pageNo3++;
              if (res.object.length < 5) {
                this.finished3 = true;
              }
            }
          });
      } else if (this.tabNum == 4) {
        //外部联系人
        this.ajax
          .getOutterContractList({
            pageNo: this.pageNo4,
            pageSize: 100,
            sidx: 'create_date',
            linkmanName: this.searchVal
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing4 = false;
              }, 500);
              if (this.pageNo4 == 1) {
                this.contractArr4 = [];
              }
              this.contractArr4.push(...res.rows);
              this.loading4 = false;
              this.pageNo4++;
              if (this.contractArr4.length >= res.totalCount) {
                this.finished4 = true;
              }
            }
          });
      } else if (this.tabNum == 5) {
        // 科室值班通讯录
        this.ajax
          .dutyAddressBook({
            pageNo: this.pageNo5,
            pageSize: 100,
            condition: this.searchVal,
            starttime: this.starttime,
            endtime: this.endtime,
            sord: 'desc',
            sidx: 'create_date'
          })
          .then(res => {
            if (res) {
              setTimeout(() => {
                this.refreshing5 = false;
              }, 500);
              if (this.pageNo5 == 1) {
                this.contractArr5 = [];
              }
              this.contractArr5.push(...res.rows);
              this.loading5 = false;
              this.pageNo5++;
              if (this.contractArr5.length >= res.totalCount) {
                this.finished5 = true;
              }
            }
          });
      }
    },

    collapse(item, index) {
      if (this.isExist(index, this.tabNum)) {
        let _index = this['shrinkArr' + this.tabNum].indexOf(index);
        this['shrinkArr' + this.tabNum].splice(_index, 1);
      } else {
        this['shrinkArr' + this.tabNum].push(index);
      }
    },
    isExist(index, sort) {
      return this['shrinkArr' + sort].indexOf(index) > -1;
    },

    //群组设置
    groupManage(item) {
      this.$router.push({
        name: 'groupManage',
        params: {
          data: item
        }
      });
    },

    //查看内部联系人
    viewInternalContact(item, index) {
      this.$router.push({
        path: '/internal-contact',
        query: {
          data: item
        }
        // query: {
        //   index: index,
        //   condition: this.searchVal
        // }
      });
    },

    handleReviewAvatar(row) {
      if (!row.avatar) return;
      ImagePreview({
        images: [row.avatar],
        closeable: true
      });
    },

    //查看外部联系人
    viewExternalContact(item, index) {
      this.$router.push({
        path: '/external-Contact',
        query: {
          index: index
        }
      });
    },

    //查看群组联系人
    viewGroupContact(index1, index2) {
      let _d = this['contractArr' + this.tabNum][index1].employeeList[index2];
      this.$router.push({
        path: '/internal-contact',
        query: {
          data: _d
        }
      });
    }
  }
};
