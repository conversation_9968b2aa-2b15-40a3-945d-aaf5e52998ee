import wxInit from '../../utils/wxInit';
export default {
  name: 'workbench',
  data() {
    return {
      processStatisticalList: [],
      workOrderStatisticalList: [],
      menuList: [],
      deptList: [],
      currentDept: {},
      showOrg: false,
      orgColumns: [],
      institutionList: [],
    };
  },
  filters: {
    iconFilter(val) {
      return val.split(';')[0];
    },
    colorFilter(val) {
      return val.split(';')[1];
    },
  },
  async beforeMount() {
    this.getUserInfo();
    this.$store.state.common.globalSetting.platformLoginType == 2 && this.getMultipleOrgs();
    await this.getMenus();
    this.$store.state.common.globalSetting.mobilePlatform != 2 &&
      (await this.getWxJsapiSignature());
    this.getShowMenus();
    this.getDept();
  },
  computed: {
    usercode() {
      return localStorage.getItem('account') || '';
    },
    password() {
      return localStorage.getItem('password') || '';
    },
    orgCode() {
      return JSON.parse(localStorage.getItem('values')).orgCode || '';
    },
    orgName() {
      return JSON.parse(localStorage.getItem('values')).orgName || '';
    },
  },
  methods: {
    //获取兼职科室
    getDept() {
      this.deptList =
        this.$store.state.common.userInfo.organizationParttimeList || [];
      let list =
        this.$store.state.common.userInfo.organizationParttimeList || [];
      let deptArr = list.filter((item) => item.isDefault == 1);
      this.currentDept = deptArr.length
        ? {
            orgId: deptArr[0].orgId,
            orgName: deptArr[0].orgName,
            id: deptArr[0].orgName,
          }
        : {
            orgId: this.$store.state.common.userInfo.orgId,
            orgName: this.$store.state.common.userInfo.orgName,
          };
    },
    async getUserInfo() {
      await this.ajax.getUserInfo().then(async (userInfoRes) => {
        this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
        this.$store.commit('common/setData', {
          label: 'userInfo',
          value: userInfoRes.object,
        });
      });
    },
    handleDeptChange(e) {
      this.ajax
        .changeUserDept({
          ...e,
          employeeId: this.$store.state.common.userInfo.employeeId,
          employeeNo: this.$store.state.common.userInfo.employeeNo,
        })
        .then((res) => {
          this.handleRefreshUserInfo();
          this.getShowMenus();
        });
    },
    handleRefreshUserInfo() {
      this.ajax.getUserInfo().then((userInfoRes) => {
        this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
        this.$store.commit('common/setData', {
          label: 'userInfo',
          value: userInfoRes.object,
        });
      });
    },
    //获取签名信息并初始化jdk
    async getWxJsapiSignature() {
      await this.ajax
        .getWxJsapiSignature({
          REFERER: this.$store.state.common.token,
        })
        .then((res) => {
          wxInit.initwxJdk(res.object);
        });
    },
    async getMenus() {
      await this.ajax.getMenus().then((res) => {
        this.menuList = res.object.length ? res.object[0].menus : [];
      });
    },
    //获取我的流程/我的工单该展示哪一个
    getShowMenus() {
      this.ajax.getShowMenus().then((res) => {
        const menuList = res.object.menuList || [];
        menuList.findIndex((item) => item.code == 'wdlc') >= 0
          ? this.getMyProcess()
          : null;
        menuList.findIndex((item) => item.code == 'wdg') >= 0
          ? this.getMyWorkOrder()
          : null;
      });
    },
    //获取我的流程数据
    getMyProcess() {
      this.ajax.getMyProcessData().then((res) => {
        const {
          inProcessCount = 0, //在办数量
          copyToMeCount = 0, //抄送数量
          sendBackCount = 0, //退回数量
          toDoCount = 0, // 代办数量
        } = res.object || {};

        this.processStatisticalList = [
          {
            name: '待办',
            count: toDoCount,
            color: '#ff976a',
            index: 0,
            path:
              '/ts-mobile-oa/pages/workflow/workflow-approval-list?fromPage=workBench&index=0',
          },
          {
            name: '在办',
            count: inProcessCount,
            color: '#005BAC',
            index: 0,
            path:
              '/ts-mobile-oa/pages/workflow/my-workflow-list?fromPage=workBench&index=0',
          },
          {
            name: '退回',
            count: sendBackCount,
            color: '#ee0a24',

            index: 1,
            path:
              '/ts-mobile-oa/pages/workflow/my-workflow-list?fromPage=workBench&index=1',
          },
          {
            name: '抄送',
            count: copyToMeCount,
            color: '#3496F6',
            index: null,
            path:
              '/ts-mobile-oa/pages/workflow/workflow-copy-list?fromPage=workBench&index=0',
          },
        ];
      });
    },
    // 获取我的工单数据
    getMyWorkOrder() {
      this.ajax.getMyWorkOrder().then((res) => {
        this.workOrderStatisticalList = [
          {
            name: '待接单',
            count: res.object.snetCount,
            color: '#ff976a',
            index: 0,
          },
          {
            name: '在办',
            count: res.object.processingCount,
            color: '#005BAC',
            index: 0,
          },
          {
            name: '待确认',
            count: res.object.acceptanceCount,
            color: '#ee0a24',
            index: 1,
          },
          {
            name: '待评价',
            count: res.object.evaluateCount,
            color: '#3496F6',
            index: null,
          },
        ];
      });
    },

    async jumpPage(name, packageName, path) {
      if (name === '会议扫码') {
        wxInit.scanQRCode(this.scanQRCodeCallback);
      }
      if (name === '拨号呼叫') {
        this.dialogCall();
        return;
      }
      if (name === '意见反馈') {
        this.dialogFeedBack(1);
        return;
      }
      if (name === '心理关爱') {
        this.dialogFeedBack(2);
        return;
      }
      if (name === '设备扫码') {
        wxInit.scanQRCode(this.scanQRCodeCallbackTo);
        return;
      }
      let connector = '';
      if (path.indexOf('?') === -1) {
        connector = '?';
      } else {
        connector = '&';
      }
      if (path.indexOf('http://') != -1 || path.indexOf('https://') != -1) {
        let empCode = this.$cookies.get('emp_code');
        let token = this.$cookies.get('token');
        window.location.href = `${path}${connector}userCode=${empCode}&token=${token}`;
      } else {
        this.buryingPoint(packageName, path);
        this.$router.push(
          `/${packageName}${path}${connector}fromPage=workBench&index=0`
        );
      }
    },
    buryingPoint(packageName, path) {
      let treeData = {
        packageName: '',
        alink: '',
        menuname: '移动端',
        menus: this.menuList,
      };
      let menu = this.findMenuName(treeData, `/${packageName}${path}`);
      this.ajax
        .modesUsesSave({
          modelName: menu.menuname,
          modelId: menu.id,
          modelUrls: `/${packageName}${path}`,
          teramType: 2,
        })
        .then((res) => {});
    },
    findMenuName(node, value) {
      if (`/${node.packageName}${node.alink}` == value) {
        return { ...node };
      }
      for (let i = 0; i < node.menus.length; i++) {
        const found = this.findMenuName(node.menus[i], value);
        if (found) {
          found.menuname = `${node.menuname}-${found.menuname}`;
          found.menuname = found.menuname.replace('移动端-', '');
          return { ...found };
        }
      }
      return null;
    },
    scanQRCodeCallback(res) {
      if (
        res.resultStr.indexOf('meetingId') === -1 ||
        res.resultStr.indexOf('pkFaultEquipmentId') === -1
      ) {
        this.$toast({
          message: '二维码参数有误',
          forbidClick: true,
        });
        return;
      }
      window.location.href = res.resultStr;
    },
    scanQRCodeCallbackTo(res) {
      let qr_code = res.resultStr.split('?')[1].split('=')[1];
      this.$router.push(
        `/ts-mobile-work-order/pages/work-order-reporting/index?fromPage=workBench&index=0&qrCode=${qr_code}`
      );
    },
    handleChangeRouter(path) {
      this.$router.push(path);
    },
    dialogCall() {
      this.$refs.call.open();
    },
    dialogFeedBack(index) {
      this.$refs.feedBack.open(index);
    },
    changes() {
      if (this.orgColumns.length <= 1) return;
      this.showOrg = true;
    },
    getMultipleOrgs() {
      this.ajax.getMultipleOrg(this.usercode).then((res) => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误');
          return;
        }
        if (res.object && res.object.length) {
          this.institutionList = res.object;
          this.orgColumns = res.object.map((item) => item.orgName);
        }
      });
    },
    onCancelOrg() {
      this.showOrg = false;
    },
    async onConfirmOrg(val, index) {
      let orgCode = this.institutionList[index].orgCode;
      let orgName = this.institutionList[index].orgName;

      this.showOrg = false;
      let requestData = {
        usercode: this.usercode,
        password: this.password,
        orgCode,
        orgName,
      };
      await this.ajax
        .accountLogin(requestData)
        .then(async (data) => {
          localStorage.setItem('account', data.object.usercode);
          localStorage.setItem('password', requestData.password);
          localStorage.setItem('values', JSON.stringify({ orgCode, orgName }));

          this.$cookies.set('trasen_pwd', requestData.password);
          this.$cookies.set('trasen_user', data.object.usercode);
          this.$cookies.set('THPMSCookie', data.object.token);
          this.$cookies.set('token', data.object.token);
          this.$cookies.set('emp_code', data.object.usercode);

          this.$store.commit('common/setData', {
            label: 'token',
            value: data.object.token,
          });
          this.$store.commit('common/setData', {
            label: 'empCode',
            value: data.object.usercode,
          });
          await this.setUserInfo('');
        })
        .catch((error) => {
          this.$toast({
            message: error.message,
            forbidClick: true,
          });
        });
    },
    async setUserInfo(url) {
      await this.ajax
        .getUserInfo()
        .then(async (userInfoRes) => {
          this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
          this.$store.commit('common/setData', {
            label: 'userInfo',
            value: userInfoRes.object,
          });
          this.$toast.clear();
          window.location.reload();
          this.$root.$emit('initQiankun1');
        })
        .catch((error) => {
          this.$toast({
            message: error.message,
            forbidClick: true,
          });
        });
    },
  },
};
