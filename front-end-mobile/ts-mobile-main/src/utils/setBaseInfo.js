import { Decrypt } from '@/utils/encrypt.js';
export default {
  data() {
    return {};
  },
  async created() {
    if (JSON.stringify(this.$store.state.common.systemCustomCode) == '{}') {
      await this.getCustomCodeDictionary();
    }
    localStorage.removeItem('orderReportConfig');
    await this.initBaseInfo();
    this.$root.$on('parentTypeFun', this.handleRefreshUserInfo);
  },
  mounted() {
    window.equipmentStocktaking = (data) => {
      this.$mainMessageStore.setGlobalState({
        event: 'equipmentStocktaking',
        data,
      });
    };
  },
  methods: {
    //获取CUSTOM_CODE字典内容并存到store中
    async getCustomCodeDictionary() {
      let customCode = {};
      await this.ajax.getDictionary('CUSTOM_CODE').then(async (res) => {
        if (res.object) {
          res.object.forEach((item) => {
            customCode[item.itemCode] = item.itemNameValue;
          });
        }
        this.$store.commit('common/setData', {
          label: 'systemCustomCode',
          value: customCode,
        });
        this.$store.commit('common/setData', {
          label: 'personalSortData',
          value: this.getSystemCustomPersonalSort(),
        });
      });
    },
    /**@desc 获取人员排序参数 */
    getSystemCustomPersonalSort() {
      const sortConfig = this.$config?.PERSONAL_SORT ?? {};
      if (sortConfig) {
        let systemCustomCode = this.$store.state.common.systemCustomCode,
          sortCode = systemCustomCode?.HRM_ARCHIVES_MANAGE_SORT ?? '1';
        return sortConfig[Number(sortCode)] || {};
      }
      return {};
    },
    async initBaseInfo() {
      /**@desc 免登录进入 */
      const nologinPath = this.$store.state.common.whiteRouteList.find((path) =>
        location.pathname.includes(path)
      );
      if (nologinPath) {
        return;
      }

      let token = '';
      let empCode = '';
      let account = this.$cookies.get('trasen_user');
      let password =
        (this.$cookies.get('trasen_pwd') &&
          Decrypt(this.$cookies.get('trasen_pwd'))) ||
        '';

      let searchList = location.search.replace('?', '').split('&'),
        tokenString = searchList.find((item) => item.indexOf('token') >= 0),
        usercode = searchList.find((item) => item.indexOf('userCode') >= 0);

      if (tokenString) {
        token = tokenString.split('=')[1];
        this.$cookies.set('THPMSCookie', token);
        this.$cookies.set('token', token);
      }
      if (usercode) {
        empCode = usercode.split('=')[1];
        this.$cookies.set('emp_code', empCode);
      }

      if (!token) {
        token = this.$cookies.get('token');
      }
      if (!usercode) {
        empCode = this.$cookies.get('emp_code');
      }

      if (account || password) {
        this.$store.commit('common/setData', {
          label: 'account',
          value: account,
        });
        this.$store.commit('common/setData', {
          label: 'password',
          value: password,
        });
      }

      // if (account && password) {
      //   this.$router.push('/login' + location.search);
      // }

      if (token && empCode) {
        this.$store.commit('common/setData', {
          label: 'token',
          value: token,
        });
        this.$store.commit('common/setData', {
          label: 'empCode',
          value: empCode,
        });
        await this.ajax.getUserInfo().then((userInfoRes) => {
          this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
          this.$store.commit('common/setData', {
            label: 'userInfo',
            value: userInfoRes.object,
          });
        });
        this.$root.$emit('initQiankun1');
        if (this.$session.get('_oa_user_key') != undefined) {
          this.$store.commit('common/setData', {
            label: 'userInfo',
            value: JSON.parse(this.$session.get('_oa_user_key')),
          });
        }
      } else {
        if (
          location.search.indexOf('returnURL=') === -1 &&
          location.pathname.indexOf('/login') === -1
        ) {
          location.search = location.search + '&returnURL=' + location.pathname;
        }
        this.$router.push('/login' + location.search);
      }
    },
    /**
     * @desc 子应用主动刷新用户信息
     * @param {Object} setting
     * @param {String} setting.event - 子应用触发主应用的事件名称
     * @param {(Function|Object)} setting.data - 子应用触发主应用传递的参数
     */
    handleRefreshUserInfo({ event, data }) {
      if (!event || event !== 'refreshUserInfo') {
        return;
      }
      this.ajax.getUserInfo().then((userInfoRes) => {
        this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
        if (data instanceof Function) {
          data(userInfoRes.object);
        }
        this.$store.commit('common/setData', {
          label: 'userInfo',
          value: userInfoRes.object,
        });
      });
    },
  },
};
