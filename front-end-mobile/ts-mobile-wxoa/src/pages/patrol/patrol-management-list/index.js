export default {
  data() {
    return {
      fromPage: '',
      tabList: [
        {
          name: '待巡查',
          status: 1,
          count: 0,
          path: '/pages/patrol/patrol-reporting/index',
          list: []
        },
        {
          name: '已完结',
          status: 2,
          count: 0,
          path: '/pages/patrol/patrol-detail/index',
          list: []
        },
        {
          name: '草稿',
          status: 0,
          count: 0,
          path: '/pages/patrol/patrol-detail/index',
          list: []
        }
      ],
      currentTab: 0,
      isShowScreen: false,
      screenFormList: [],
      screenForm: {
        patrolName: '',
        startDate: '',
        endDate: ''
      }
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    if (opt && opt.status) {
      if (opt && opt.status) {
        this.currentTab = opt.status == '0' ? 2 : opt.status == '2' ? 1 : 0;
      }
    }
  },
  methods: {
    search(val) {
      this.keywords = val;
      this.$nextTick(() => {
        this.tabList.map((item, index) => {
          this.datasInit(index);
          this.$refs[`mescroll${index}`][0].downCallback();
        });
      });
    },
    clear() {},
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback, index) {
      this.ajax
        .getPatrolManagementList({
          pageNo: page.num,
          pageSize: page.size,
          patrolName: this.screenForm.patrolName,
          startDate: this.screenForm.startDate,
          endDate: this.screenForm.endDate,
          status: this.tabList[index].status,
          sord: 'desc',
          sidx: 'create_date'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      this.tabList[index]['count'] = totalCount;
    },
    datasInit(index) {
      this.tabList[index]['list'] = [];
    },
    jumpToDetail(id, path) {
      uni.navigateTo({
        url: `${path}?fromPage=patrol-management-list&formId=${id}`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    }
  }
};
