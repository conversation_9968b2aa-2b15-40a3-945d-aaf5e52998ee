<template>
  <view class="patrol-item" @click.stop="onClick">
    <view class="item-top">
      <view class="item-top--left">
        {{
          `${item.patrolName} ${
            item.status ? '(第' + item.startNum + '期)' : ''
          }`
        }}
      </view>
      <text class="item-top--right">
        {{ item.checkDate }}
      </text>
    </view>
    <view class="item-bottom">
      <view class="item-bottom--info" v-if="item.status == 0">
        创建时间：{{ item.createDate | formatDate('YYYY-MM-DD HH:mm') }}
      </view>
      <view
        class="item-bottom--info"
        v-if="item.status == 1 && item.executorUserName"
      >
        {{ `责任人：${item.executorUserName}` }}
      </view>
      <view class="item-bottom--info" v-if="item.status == 1">
        截止日期：{{ item.closeDate | formatDate('YYYY-MM-DD') }}
      </view>
      <view class="item-bottom--info" v-if="item.status == 2">
        {{ `巡查人（记录人）：${item.checkUserName}` }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'patrol-item',
  props: {
    item: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/ellipsis.scss';
@import '@/assets/css/flex.scss';
.patrol-item {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
  font-size: $uni-font-size-base;
}
.item-top {
  @include vue-flex;
  align-items: center;
}
.item-top--left {
  flex: 1;
  @include ellipsis;
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  font-weight: bold;
}
.item-top--right {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;
}
.item-bottom--info {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
</style>
