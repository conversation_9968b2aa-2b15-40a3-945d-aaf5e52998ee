export default {
  //格式化时间
  formatTime(e, field = 'yy-MM-dd') {
    if (field == 'YY') {
      return `${e.year}年`;
    } else if (field == 'yy-MM') {
      return `${e.year}-${e.month}`;
    } else if (field == 'yy-MM-dd') {
      return `${e.year}-${e.month}-${e.day}`;
    } else if (field == 'HH:mm') {
      return `${e.hour}:${e.minute}`;
    } else if (field == 'yy-MM-dd HH:mm') {
      return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
    } else if (field == 'yy-MM-dd HH:mm:ss') {
      return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
    }
  },

  /**
   * 格式化日期
   * @param {String} type 返回的格式
   * @param {String} dateVal 日期
   * @return {Object}
   */
  formatDate(type = 'day', dateVal = '') {
    let time, date;
    if (typeof dateVal == 'string') {
      date = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date();
    } else {
      date = new Date(dateVal);
    }
    let YY = date.getFullYear(),
      MM = this.formatDateNum(date.getMonth() + 1),
      DD = this.formatDateNum(date.getDate()),
      HH = date.getHours(),
      mm = this.formatDateNum(date.getMinutes()),
      ss = date.getSeconds();
    switch (type) {
      case 'year':
        time = `${YY}`;
        break;
      case 'yearStr':
        time = `${YY}年`;
        break;
      case 'month':
        time = `${YY}-${MM}`;
        break;
      case 'monthStr':
        time = `${YY}年${MM}月`;
        break;
      case 'day':
        time = `${YY}-${MM}-${DD}`;
        break;
      case 'dayStr':
        time = `${YY}年${MM}月${DD}日`;
        break;
      case 'datetime':
        time = `${YY}-${MM}-${DD} ${HH}:${mm}`;
        break;
      case 'datetimeStr':
        time = `${YY}年${MM}月${DD}日 ${HH}:${mm}`;
        break;
      case 'hour':
        time = `${HH}:${mm}`;
        break;
      case 'monthDay':
        time = `${MM}-${DD}`;
        break;
      case 'monthDayStr':
        time = `${MM}月${DD}日`;
        break;
    }
    return time;
  },

  /**
   * 日期自动补0
   * @param {String | Number} num 日期
   * @return {String}
   */
  formatDateNum(num) {
    return String(num).replace(/(^\d{1}$)/, '0$1');
  },

  /**
   * 获取多少天开始时间和结束时间
   * @param {Object} dateVal 时间
   * @return {Object}	start:开始时间，end:结束时间
   */
  getDate(dateVal, preNum = 0) {
    let now = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date(),
      startDate = new Date(now - Number(preNum) * 24 * 60 * 60 * 1000),
      formatFirstDate = this.formatDate('day', startDate),
      formatEndDate = this.formatDate('day', now),
      preDay = { start: formatFirstDate, end: formatEndDate };
    return preDay;
  },

  /**
   * 获取某周开始时间和结束时间
   * @param {Object} dateVal 时间
   * @return {Object} start:开始时间，end:结束时间
   */
  getWeekDate(dateVal) {
    let now = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date(),
      nowDayOfWeek = now.getDay(),
      startDate = new Date(now - (nowDayOfWeek - 1) * 24 * 60 * 60 * 1000),
      formatFirstDate = this.formatDate('day', startDate),
      endDate = new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        now.getDate() + 7 - nowDayOfWeek
      ),
      formatEndDate = this.formatDate('day', endDate),
      preWeek = { start: formatFirstDate, end: formatEndDate };
    return preWeek;
  },

  /**
   * 获取某月开始时间和结束时间
   * @param {Object} dateVal 时间
   * @param {Object} preNum 前多少月
   * @return {Object} start:开始时间，end:结束时间
   */
  getMonthDate(dateVal, preNum = 0) {
    let now = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date(),
      startDate = new Date(
        now.getFullYear(),
        now.getMonth() - Number(preNum),
        1
      ),
      formatFirstDate = this.formatDate('day', startDate),
      nextMonthFirstDay = new Date(
        startDate.getFullYear(),
        startDate.getMonth() + 1,
        1
      ),
      endDate = new Date(nextMonthFirstDay - 1 * 24 * 60 * 60 * 1000),
      formatEndDate = this.formatDate('day', endDate),
      preMonth = { start: formatFirstDate, end: formatEndDate };
    return preMonth;
  },

  /**
   * 获取某年开始时间和结束时间
   * @param {String} dateVal 时间
   * @param {Number} preNum 前多少年
   * @return {Object} start:开始时间，end:结束时间
   */
  getYearDate(dateVal, preNum = 0) {
    let now = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date(),
      y = now.getFullYear(), //当前年份
      startDate = new Date(y - Number(preNum), 0, 1), //上一年的开始日期
      formatFirstDate = this.formatDate('day', startDate),
      endDate = new Date(y - Number(preNum), 12, 0), //上一年的结束日期
      formatEndDate = this.formatDate('day', endDate),
      preYear = { start: formatFirstDate, end: formatEndDate };
    return preYear;
  }
};
