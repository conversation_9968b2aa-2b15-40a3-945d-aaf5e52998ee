package cn.trasen.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import cn.trasen.api.HisApiController;


@Configuration
@Slf4j
public class ThpsUserDBHelper {

	@Value("${tsSystemDriver}")
	String tsSystemDriver; // 驱动

	@Value("${tsSystemUrl}")
	String tsSystemUrl; // 地址

	@Value("${tsSystemUsername}")
	String tsSystemUsername; // 用户名

	@Value("${tsSystemPwd}")
	String tsSystemPwd; // 密码

	@Value("${ssoDbType}")
	String ssoDbType;

	public Map<String,String> getLeaderUser(String usercode){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		Map<String,String> map = new HashMap<>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询员工信息
			StringBuilder sb = new StringBuilder();
			sb.append("select directleadershipid,directleadershipname from thps_user u");
			sb.append(" left join thps_dept d on u.deptcode = d.deptcode ");
			sb.append(" where usercode = '").append(usercode).append("'");

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					for (int i = 1; i <= userColumnCount; i++) {
						if ("directleadershipid".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("directleadershipid", String.valueOf(rs.getObject(i)));
						}
						if ("directleadershipname".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("directleadershipname", String.valueOf(rs.getObject(i)));
						}
					}
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
				return map;
	}
	
	
	public Map<String,String> getUserInfo(String usercode){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		Map<String,String> map = new HashMap<>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询员工信息
			StringBuilder sb = new StringBuilder();
			sb.append("SELECT U.ID USERID,U.USERNAME,D.ID DID,D.DEPTNAME FROM THPS_USER U");
			sb.append(" LEFT JOIN THPS_DEPT D ON U.DEPTCODE = D.DEPTCODE");
			sb.append(" WHERE USERCODE = '").append(usercode).append("'");
			try {
				ps = conn.prepareStatement(sb.toString());
				
				rs = ps.executeQuery();
				while (rs.next()) {
					map.put("uid", rs.getString("USERID"));
					map.put("username", rs.getString("USERNAME"));
					map.put("did", rs.getString("DID"));
					map.put("deptname", rs.getString("DEPTNAME"));
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				log.info(e.getMessage());
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
				return map;
	}


	/**
	 * @Description: 获得连接对象
	 * @param driver 数据库驱动
	 * @param url 数据库url
	 * @param username 账户名
	 * @param password 密码
	 * @return
	 */
	public Connection getConnetion(String driver, String url, String username, String password) {
		Connection conn = null;
		try {
			Class.forName(driver); // 映射Java驱动
			conn = DriverManager.getConnection(url, username, password); // 获得连接对象
			conn.setAutoCommit(false); // 关闭自动提交事务
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			log.info(e.getMessage());
		} catch (SQLException e) {
			e.printStackTrace();
			log.info(e.getMessage());
		}
		return conn;
	}

	/**
	 * @Description: 关闭所有资源
	 * @param rs
	 * @param ps
	 * @param conn
	 * @return
	 */
	public static void closeAll(ResultSet rs, PreparedStatement ps, Connection conn) {
		try {
			if (rs != null) {
				rs.close();
			}
			if (ps != null) {
				ps.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

}
