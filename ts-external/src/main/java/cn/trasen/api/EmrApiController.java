package cn.trasen.api;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.trasen.critical.model.ExternalLogs;
import cn.trasen.critical.service.EmrApiService;
import cn.trasen.critical.service.ExternalLogsService;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.utils.HttpClientUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "电子病历接口")
@RestController
@Slf4j
public class EmrApiController {

	@Resource
	private ExternalLogsService externalLogsService;
	 
	@Value("${his.request.url}")
	private String hisRequestUrl;
	
	@Value("${his.request.orgCode}")
	private String hisRequestOrgCode;
	
	@Value("${his.request.hospCode}")
	private String hisRequestHospCode;
	
	@Value("${his.request.version}")
	private String hisRequestVersion;
	
	@Autowired
	private EmrApiService emrApiService;
	/**
	 * 
	 * @Title: post   
	 * @Description: 根据住院号查询住院信息的接口  
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年4月25日 下午4:55:11       
	 * @throws
	 */
	 @ApiOperation(value = "根据住院号查询住院信息", notes = "根据住院号查询住院信息")
     @PostMapping("/emrApi/getInpatientInfoById")
	 public PlatformResult<List<Map<String,String>>> getInpatientInfoById(String name) {
		 if("2".equals(hisRequestVersion)){
			 return emrApiService.getInpatientInfoById(name);
		 }else{
			 try {
		        	long startTime = System.currentTimeMillis();    //获取开始时间
		        	
		        	Map<String,Object> params = new HashMap<>();
		        	params.put("IsCompress", "false");
		        	params.put("ServiceName", "Inp-InpatientService");//服务名
		        	params.put("InterfaceName", "QueryInpInpatientInfoList");//接口名
		        	params.put("TimeOut", 15000);//超时时间
		        	
		        	Map<String,Object> paramsMap = new HashMap<>();
		        	paramsMap.put("ORG_CODE", hisRequestOrgCode);//机构编码
		        	paramsMap.put("HOSP_CODE", hisRequestHospCode);//#院区编码
		        	paramsMap.put("INPATIENT_NO", name);//住院号
		        	paramsMap.put("PageIndex", 1);//分页参数
		        	paramsMap.put("PageSize", 1000);//分页参数
		        	
		        	params.put("Parameter",paramsMap);
		        	
		        	SimpleDateFormat sdf = new SimpleDateFormat( "yyyy年 MM月 dd日");
		        	SimpleDateFormat sdf2 = new SimpleDateFormat( "yyyy-MM-dd");
		        	List<Map<String,String>> result = new ArrayList<>();
		        	String requestParams = JSON.toJSONString(params);
		        	
		        	log.info("调用   QueryInpInpatientInfoList请求的参数 :" + requestParams);
		        	
		        	String resultJson = HttpClientUtils.doPostJson(hisRequestUrl, requestParams);
		        	
		        	log.info("HIS返回的数据 :" + resultJson);
		        	
		        	JSONObject resultMap = JSONObject.parseObject(resultJson);
		        	
		        	if((boolean) resultMap.get("Success")) {
		        		JSONObject data = (JSONObject) resultMap.get("data");
		        		JSONObject Value = (JSONObject) data.get("Value");
		        		if(null != Value.get("_InpatientInfo")) {
		        			JSONArray InpatientInfoList = (JSONArray) Value.get("_InpatientInfo");
		            		for (int i = 0; i < InpatientInfoList.size(); i++) {
		            			JSONObject map = InpatientInfoList.getJSONObject(i);
		            			Map<String,String> map2 = new HashMap<>();
		            			StringBuffer sb = new StringBuffer();
		            			sb.append(map.get("PATIENT_NAME")).append(":").append(map.get("PATIENT_SEX_NAM"));
		            			sb.append("（");
		            			if(null != map.get("IN_DATE")) {
		            				sb.append(sdf.format(sdf2.parse((String) map.get("IN_DATE"))));
		            			}
		            			if(null != map.get("OUT_DATE")) {
		            				sb.append("至");
		                			sb.append(sdf.format(sdf2.parse((String) map.get("OUT_DATE"))));
		            			}
		            			sb.append("）第").append(map.get("IN_TIMES")).append("次住院");
		            			String id = (String) map.get("ID");
		            			String ORG_CODE = (String) map.get("ORG_CODE");
		            			String DEPT_ID = (String) map.get("DEPT_ID");
		            			String DEPT_NAME = (String) map.get("DEPT_NAME");
		            			map2.put("id", id + "_" + ORG_CODE + "_" + DEPT_ID + "_" + DEPT_NAME);
		            			map2.put("name", sb.toString());
		            			map2.put("patientName", map.getString("PATIENT_NAME"));//患者姓名
		            			map2.put("patientSexNam", map.getString("PATIENT_SEX_NAM"));//患者性别
		            			map2.put("inDate", map.getString("IN_DATE"));//入院日期
		            			map2.put("outDate", map.getString("OUT_DATE"));//出院日期
		            			map2.put("inDeptName", map.getString("IN_DEPT_NAME"));//入院科室名称
		            			map2.put("deptName", map.getString("DEPT_NAME"));//当前科室名称
		            			map2.put("bedNo", map.getString("BED_NO"));//床位号
		            			map2.put("inTimes", map.getString("IN_TIMES"));//住院次数
		            			map2.put("patientIccard", map.getString("PATIENT_ICCARD"));//身份证号
		            			map2.put("hospName", map.getString("HOSP_NAME"));//医院名称
		            			map2.put("hospCode", map.getString("HOSP_CODE"));//医院编码
		            			map2.put("orgName", map.getString("ORG_NAME"));//机构名称
		            			map2.put("orgCode", map.getString("ORG_CODE"));//机构编码
		            			map2.put("deptId", map.getString("DEPT_ID"));//部门ID
		            			map2.put("deptName", map.getString("DEPT_NAME"));//部门名称
		            			map2.put("patientId", map.getString("PATIENT_ID"));//患者建档ID
		            			map2.put("inpatientNo", map.getString("INPATIENT_NO"));//住院号
		            			map2.put("inpatientId", map.getString("ID"));//患者登记ID
		            			String STATUS = map.getString("STATUS");// 住院状态 1 待入科室 3 在科室 4 出院医嘱 5 出区，6 结算 7欠费结算 10 冻结
		            			if("1".equals(STATUS)) {
		           					map2.put("status","待入科室");
		           				}
		           				if("3".equals(STATUS)) {
		           					map2.put("status","在科室");
		           				}
		           				if("4".equals(STATUS)) {
		           					map2.put("status","出院医嘱");
		           				}
		           				if("5".equals(STATUS)) {
		           					map2.put("status","出区");
		           				}
		           				if("6".equals(STATUS)) {
		           					map2.put("status","结算");
		           				}
		           				if("7".equals(STATUS)) {
		           					map2.put("status","欠费结算 ");
		           				}
		           				if("10".equals(STATUS)) {
		           					map2.put("status","注销");
		           				}
		            			result.add(map2);
		            		}
		        		}
		        	}
		        	
		        	long endTime = System.currentTimeMillis();    //获取结束时间
		        	
		        	ExternalLogs externalLogs = new ExternalLogs();
		        	externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		        	externalLogs.setRequestUrl(hisRequestUrl);
		        	externalLogs.setRequestService("Inp-InpatientService");
		        	externalLogs.setRequestMethos("QueryInpInpatientInfoList");
		        	externalLogs.setRequestParams(requestParams);
		        	externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		        	externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		        	externalLogs.setCreateTime(new Date());
		        	externalLogs.setResponseParams(resultJson);
		        	externalLogs.setTakeTime((endTime - startTime) + "ms");
		        	externalLogsService.insert(externalLogs);
		        	return PlatformResult.success(result);
		        } catch (Exception e) {
		        	e.printStackTrace();
		            log.error(e.getMessage(), e);
		            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
		        }
		 }
       
    }
	 
	/**
	 * 
	 * @Title: getMedicalRecord   
	 * @Description: 查询病历信息 
	 * @param: @param id
	 * @param: @return      
	 * @return: PlatformResult<List<Map<String,String>>>  
	 * @author: Yuec    
	 * @date:   2021年4月26日 上午9:26:20       
	 * @throws
	 */
	@ApiOperation(value = "查询病历信息", notes = "查询病历信息")
    @PostMapping("/emrApi/getMedicalRecord")
	public PlatformResult<List<Map<String,String>>> getMedicalRecord(String id) {
		if("2".equals(hisRequestVersion)){
			 return emrApiService.getMedicalRecord(id);
		 }else{
			 try {
		        	
		        	long startTime = System.currentTimeMillis();    //获取开始时间
		        	
		        	String[] obj = id.split("_");
		        	String id_value = obj[0];
		        	String ORG_CODE = obj[1];
					String DEPT_ID = obj[2];
					String DEPT_NAME = obj[3];
		        	
		        	List<String> ids = new ArrayList<>();
		        	ids.add(id_value);
		        	
		        	Map<String,Object> params = new HashMap<>();
		        	params.put("IsCompress", "false");
		        	params.put("ServiceName", "IOutpatientMedicalRecordService");//服务名
		        	params.put("InterfaceName", "QueryMrFileCheck");//接口名
		        	params.put("TimeOut", 15000);//超时时间
		        	
		        	Map<String,Object> paramsMap = new HashMap<>();
		        	paramsMap.put("INPATIENT_IDS", ids);//住院号
		        	paramsMap.put("ORG_CODE", ORG_CODE);//机构编码
		        	paramsMap.put("DEPT_ID", DEPT_ID);//当前科室
		        	paramsMap.put("DEPT_NAME", DEPT_NAME);//当前科室名称
		        	
		        	params.put("Parameter",paramsMap);
		        	
		        	String requestParams = JSON.toJSONString(params);
		        	
		        	log.info("调用   QueryMrFileCheck请求的参数 :" + requestParams);
		        	
		        	String resultJson = HttpClientUtils.doPostJson(hisRequestUrl,requestParams);
		        	
		        	log.info("HIS返回的数据 :" + resultJson);
		        	
		        	List<Map<String,String>> result = new ArrayList<>();
		        	
		        	JSONObject resultMap = JSONObject.parseObject(resultJson);
		        	if((boolean) resultMap.get("Success")) {
		        		JSONObject data = (JSONObject) resultMap.get("data");
		        		JSONObject Value = (JSONObject) data.get("Value");
		        		if(null != Value.get("Data")) {
		        			JSONArray emr_mr_file = (JSONArray) Value.get("Data");
		            		for (int i = 0; i < emr_mr_file.size(); i++) {
		            			JSONObject map = emr_mr_file.getJSONObject(i);
		            			Map<String,String> map2 = new HashMap<>();
		        				StringBuffer sb = new StringBuffer();
		        				String CREATE_DATE = (String) map.get("CREATE_DATE");
		        				if(StringUtils.isNotBlank(CREATE_DATE)) {
		        					CREATE_DATE = CREATE_DATE.replace("T", " ");
		        					if(CREATE_DATE.contains(".")) {
		        						String[] CREATE_DATE_ARR = CREATE_DATE.split("\\.");
		        						if(CREATE_DATE_ARR.length > 0) {
		        							CREATE_DATE = CREATE_DATE_ARR[0];
		        						}
		        					}
		        				}
		        				sb.append(CREATE_DATE);
		        				sb.append("（").append(map.get("PATIENT_NAME")).append("）");
		        				sb.append("【").append(map.get("MR_TYPE")).append("】");
		        				sb.append(map.get("MR_TITLE"));
		        				if(!"0".equals(map.get("MR_FLAG"))) {
		        					sb.append("（已签）");
		        				}else {
		        					sb.append("（未签）");
		        				}
		        				sb.append(map.get("HOUSEMAN"));
		        				String key = (String) map.get("ID");
		            			
		        				map2.put("id", key + "_" + ORG_CODE + "_" + DEPT_ID + "_" + DEPT_NAME);
		        				map2.put("name", sb.toString());
		        				result.add(map2);
		        			}
		        		}
		        	}
		        	
		        	long endTime = System.currentTimeMillis();    //获取结束时间
		        	
		        	ExternalLogs externalLogs = new ExternalLogs();
		        	externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		        	externalLogs.setRequestUrl(hisRequestUrl);
		        	externalLogs.setRequestService("IOutpatientMedicalRecordService");
		        	externalLogs.setRequestMethos("QueryMrFileCheck");
		        	externalLogs.setRequestParams(requestParams);
		        	externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		        	externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		        	externalLogs.setCreateTime(new Date());
		        	externalLogs.setResponseParams(resultJson);
		        	externalLogs.setTakeTime((endTime - startTime) + "ms");
		        	externalLogsService.insert(externalLogs);
		        	return PlatformResult.success(result);
		        } catch (Exception e) {
		        	e.printStackTrace();
		            log.error(e.getMessage(), e);
		            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
		        }
		 }
       
	}
	
	/**
	 * 
	 * @Title: updateMedicalRecordStatus   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @return      
	 * @return: PlatformResult<List<Map<String,String>>>  
	 * @author: Yuec    
	 * @date:   2021年4月26日 上午9:40:44       
	 * @throws
	 */
	@ApiOperation(value = "修改病历信息状态", notes = "修改病历信息状态")
    @PostMapping("/emrApi/updateMedicalRecordStatus")
	public PlatformResult<String> updateMedicalRecordStatus(HttpServletRequest request) {
		 if("2".equals(hisRequestVersion)){
			 return emrApiService.updateMedicalRecordStatus(request);
		 }else{
			 try {
		        	
		        	long startTime = System.currentTimeMillis();    //获取开始时间
		        	
		        	Map<String, Object> formData = new HashMap<>();
			        Enumeration<String> enu = request.getParameterNames();
			        while (enu.hasMoreElements()) {
			            String key = (String) enu.nextElement();
			            formData.put(key, request.getParameter(key));
			        }
			        String L_name = (String) formData.get("L_name");
					JSONArray jsonArray = JSON.parseArray(L_name);
					
					log.info("L_name的值：" + L_name);
					String ORG_CODE = "";
					String DEPT_ID = "";
					String DEPT_NAME = "";
					List<String> reqIds = new ArrayList<>();
					for (int i = 0; i < jsonArray.size() ; i++){
			            JSONObject jsonObject = jsonArray.getJSONObject(i);
			            String idStr = (String) jsonObject.get("id");
			            String[] idValue = idStr.split("_");
			        	String id_value = idValue[0];
			        	ORG_CODE = idValue[1];
						DEPT_ID = idValue[2];
						DEPT_NAME = idValue[3];
						
						reqIds.add(id_value);
			        }
			        
			        Map<String,Object> params = new HashMap<>();
		        	params.put("IsCompress", "false");
		        	params.put("ServiceName", "IOutpatientMedicalRecordService");//服务名
		        	params.put("InterfaceName", "UpdateMrFileFlag");//接口名
		        	params.put("TimeOut", 15000);//超时时间
		        	
		        	InetAddress ip4 = Inet4Address.getLocalHost();
		        	Map<String,Object> paramsMap = new HashMap<>();
		        	paramsMap.put("EMR_IDS", reqIds);
		        	paramsMap.put("CREATE_USER", "OA");
		        	paramsMap.put("CREATE_NAME", "OA");
		        	paramsMap.put("CREATE_IP", ip4.getHostAddress());
		        	paramsMap.put("ORG_CODE", ORG_CODE);
		        	paramsMap.put("DEPT_ID", DEPT_ID);
		        	paramsMap.put("DEPT_NAME", DEPT_NAME);
		        	
		        	params.put("Parameter",paramsMap);
		        	String requestParams = JSON.toJSONString(params);
		        	
		        	log.info("调用   UpdateMrFileFlag请求的参数 :" + requestParams);
		        	
		        	String resultJson = HttpClientUtils.doPostJson(hisRequestUrl, requestParams);
		        	
		        	log.info("HIS返回的数据 :" + resultJson);
		        	
		        	long endTime = System.currentTimeMillis();    //获取结束时间
		        	
		        	ExternalLogs externalLogs = new ExternalLogs();
		        	externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		        	externalLogs.setRequestUrl(hisRequestUrl);
		        	externalLogs.setRequestService("IOutpatientMedicalRecordService");
		        	externalLogs.setRequestMethos("UpdateMrFileFlag");
		        	externalLogs.setRequestParams(requestParams);
		        	externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		        	externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		        	externalLogs.setCreateTime(new Date());
		        	externalLogs.setResponseParams(resultJson);
		        	externalLogs.setTakeTime((endTime - startTime) + "ms");
		        	externalLogsService.insert(externalLogs);
		        	
		        	return PlatformResult.success(resultJson);
		        } catch (Exception e) {
		        	e.printStackTrace();
		            log.error(e.getMessage(), e);
		            return PlatformResult.failure("服务端异常，修改失败，失败原因：" + e.getMessage());
		        }
		 }
        
	}

}
