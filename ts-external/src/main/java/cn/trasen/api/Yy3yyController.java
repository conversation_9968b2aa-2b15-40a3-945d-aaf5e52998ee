package cn.trasen.api;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.http.HttpUtil;
import cn.trasen.BootComm.utils.RedisUtil;
import cn.trasen.critical.model.ExternalLogs;
import cn.trasen.critical.service.ExternalLogsService;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.po.ReceiveYunCallingInfoDTO;
import cn.trasen.po.ReceiveYunCallingInfoReqDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 益阳三座机呼叫
 */
@RestController
@Slf4j
public class Yy3yyController {
	
	@Resource
	RedisUtil redisUtil;
	
	@Resource
	private ExternalLogsService externalLogsService;
	
    @PostMapping("/yy3yy/callPhone")
	public PlatformResult<String> callPhone(String calltel,String mytel,String mycode) {
        try {
        	
        	if(StringUtils.isEmpty(calltel)){
    			return PlatformResult.failure("呼叫号码不能为空!");
    		}
        	
        	if(StringUtils.isEmpty(mytel) || StringUtils.isEmpty(mycode)){
    			return PlatformResult.failure("参数错误!");
    		}
        	
        	//呼叫登录
        	String cti_login = "http://175.6.34.19:8181/cti/cti_login?tenant_name=yysy&user_name=" 
        	+ mycode + "&password=" + DigestUtils.md5DigestAsHex("Yy3yy7878@".getBytes("utf-8")) + "&tel=" + mytel;
        	
        	log.info("呼叫登录请求url：" + cti_login);
        	
        	String cti_login_result = HttpUtil.get(cti_login);
        	
        	log.info("呼叫登录返回结果：" + cti_login_result);
        	
        	JSONObject jsonObject =	JSONObject.parseObject(cti_login_result);
        	
        	JSONObject result = (JSONObject) jsonObject.get("result");
        	
        	String token = result.getString("token");
        	
        	redisUtil.set("callPhone_" + result.getString("agent_id"), token, 0);
        	
        	//呼叫号码
        	String dial = "http://175.6.34.19:8181/cti/interface/dial?token=" + token + "&target=" + calltel + "&ori_callee=99707374222222";
        	
        	log.info("呼叫号码请求url：" + dial);
        	
        	String dial_result = HttpUtil.get(dial);
        	
        	log.info("呼叫号码返回结果：" + dial_result);
        	
        	ExternalLogs externalLogs = new ExternalLogs();
	    	externalLogs.setId(String.valueOf(IdWork.id.nextId()));
	    	externalLogs.setRequestUrl("cti/interface/dial");
	    	externalLogs.setRequestService("益阳三电话呼叫接口");
	    	externalLogs.setRequestMethos("益阳三电话呼叫接口");
	    	externalLogs.setRequestParams(token);
	    	externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
	    	externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
	    	externalLogs.setCreateTime(new Date());
	    	externalLogs.setResponseParams(token);
	    	externalLogsService.insert(externalLogs);
        	
        	return PlatformResult.success();
	       	
        } catch (Exception e) {
        	e.printStackTrace();
            log.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，失败原因：" + e.getMessage());
        }
	}
    
    @PostMapping("/yy3yy/call/callback")
	public PlatformResult<String> callback(@RequestBody ReceiveYunCallingInfoReqDTO data) {
        try {
        	
        	log.info("呼叫回调获取结果：" + JSON.toJSONString(data));
        	
        	String token = "";
        	
        	if("agent_call_hangup".equals(data.getType())) {
        		
        		ReceiveYunCallingInfoDTO msg = data.getData();
        		
        		String agent_id = msg.getAgentId();
        		
        		token = (String) redisUtil.get("callPhone_" + agent_id);
        		
        		log.info("===获取agent_id: "  + agent_id + "====reids获取token：" + token);
        		
        		String logout = "http://175.6.34.19:8181/cti/interface/logout?token=" + token;
        		
        		String logout_result = HttpUtil.get(logout);
        		
        		log.info("呼叫登出返回结果：" + logout_result);
        		
        		ExternalLogs externalLogs = new ExternalLogs();
    	    	externalLogs.setId(String.valueOf(IdWork.id.nextId()));
    	    	externalLogs.setRequestUrl("cti/interface/logout");
    	    	externalLogs.setRequestService("益阳三电话呼叫回调接口");
    	    	externalLogs.setRequestMethos("益阳三电话呼叫回调接口");
    	    	externalLogs.setRequestParams(token);
    	    	externalLogs.setCreateTime(new Date());
    	    	externalLogs.setResponseParams(JSON.toJSONString(data));
    	    	externalLogsService.insert(externalLogs);
        		
        	}
        	
        	
	    	
        	return PlatformResult.success();
        } catch (Exception e) {
        	e.printStackTrace();
            log.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，失败原因：" + e.getMessage());
        }
	}

}
