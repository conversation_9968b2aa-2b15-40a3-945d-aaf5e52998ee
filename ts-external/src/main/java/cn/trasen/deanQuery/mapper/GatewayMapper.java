package cn.trasen.deanQuery.mapper;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;

public interface GatewayMapper {

	Map<String, String> getTotalIncome(Map<String, Object> mapParam);
	List<Map<String, String>> getSevenIncomeChart(Map<String, Object> mapParam);
	Map<String, String> getIncomeComposition(Map<String, Object> mapParam);
	Map<String, String> getAssessmentIndicators(Map<String, Object> mapParam);
	
	List<Map<String, String>> getSevenIncomeChartLeaderDaily(Map<String, Object> mapParam);
	Map<String, Object> getAllSingleZbData(Map<String, Object> map);//获取单个指标数据
	List<Map<String, Object>> getHospDeptZbData(Map<String, Object> map);//获取日报科室数据
	
	Map<String, Object> getAllSingleZbDataTqdb(Map<String, Object> map);//获取单个指标以及同期对比数据
	List<Map<String, Object>> getDeptZbPageTy(Page page,Map<String, Object> map);//获取指标科室数据，分页
	Map<String, Object> getIncomeType(Map<String, Object> map);//获取收入分类数据及占比
	Map<String, Object> getIncomeTrendByDate(Map<String, Object> map);//获取日期段收入、及同期收入，同于收入趋势
	
}
