package cn.trasen.deanQuery.service.impl;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.collection.CollUtil;
import cn.trasen.deanQuery.mapper.BasOrgDepartmentMapper;
import cn.trasen.deanQuery.mapper.DeptMappingChildMapper;
import cn.trasen.deanQuery.mapper.DeptMappingMapper;
import cn.trasen.deanQuery.model.BasOrgDepartment;
import cn.trasen.deanQuery.model.DeptMapping;
import cn.trasen.deanQuery.model.DeptMappingChild;
import cn.trasen.deanQuery.service.DeptMappingChildService;
import cn.trasen.deanQuery.service.DeptMappingService;
import cn.trasen.homs.bean.hrms.CommInterfaceRegisterResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.hrms.CommInterfaceRegisterFeignService;
import cn.trasen.homs.utils.HttpClient;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName DeptMappingServiceImpl
 * @Description TODO
 * @date 2023��5��12�� ����5:08:11
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DeptMappingServiceImpl implements DeptMappingService {
	
	@Value("${hisRequestVersion:}")
	private String hisRequestVersion;

	@Autowired
	private DeptMappingMapper mapper;
	
	@Autowired
	private DeptMappingChildService deptMappingChildService;
	
	@Autowired
	private DeptMappingChildMapper  deptMappingChildMapper;
	
	@Autowired
	private CommInterfaceRegisterFeignService commInterfaceRegisterFeignService;
	
	@Autowired
	private BasOrgDepartmentMapper basOrgDepartmentMapper;

	@Transactional(readOnly = false)
	@Override
//	@TargetDataSource(name = "deanquery")
	public Integer save(DeptMapping record) {
			record.setId(IdGeneraterUtils.nextId());
			record.setCreateDate(new Date());
			record.setUpdateDate(new Date());
			record.setIsDeleted("N");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			//标记次数是否都是重复数据
			boolean addFlag = true;
			List<DeptMappingChild> deptMappingChildList = deptMappingChildMapper.getListByCon(record.getSyscode(), record.getOaDeptId());
			Map<String, DeptMappingChild> deptMappingChildMap = null;
            if (deptMappingChildList != null && deptMappingChildList.size() > 0) {
            	deptMappingChildMap = deptMappingChildList.stream().collect(Collectors.toMap(DeptMappingChild::getHisDeptId, a -> a, (k1, k2) -> k1));
            }
			if(record.getDeptMappingChildList() !=null && record.getDeptMappingChildList().size()>0){
				String deptMappingId = record.getId();
				//遍历获取已经存在的平台主表ID，并标记该条数据是否新增
				for(DeptMappingChild deptMappingChild : record.getDeptMappingChildList()) {
                    if(deptMappingChildMap != null && deptMappingChildMap.containsKey(deptMappingChild.getHisDeptId())){
                    	addFlag = false;
                    	deptMappingId = deptMappingChildMap.get(deptMappingChild.getHisDeptId()).getDeptMappingId();
                    	break;
                    }
				}
				//去重处理，如果存在则不做处理，否则新增
				//根据系统编码查询关联映射科室列表-返回包含平台科室ID的数据
				for(DeptMappingChild deptMappingChild : record.getDeptMappingChildList()) {
					if(deptMappingChildMap != null && deptMappingChildMap.containsKey(deptMappingChild.getHisDeptId())){
                    	continue;
                    } else {
                    	deptMappingChild.setDeptMappingId(deptMappingId);
                    	deptMappingChild.setSyscode(record.getSyscode());
                    	deptMappingChildService.save(deptMappingChild);
                    }
				}
			}
			if(addFlag){
				return mapper.insertSelective(record);
			}
			return 1;
	}

	@Transactional(readOnly = false)
	@Override
//	@TargetDataSource(name = "deanquery")
	public Integer update(DeptMapping record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		 //先删除子表，再新增子内容
		DeptMappingChild deptMappingChild = new DeptMappingChild();
		deptMappingChild.setIsDeleted(Contants.IS_DELETED_TURE);
		deptMappingChild.setUpdateDate(new Date());
		deptMappingChild.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		deptMappingChild.setUpdateUserName(UserInfoHolder.getCurrentUserName());
	    Example example = new Example(DeptMappingChild.class);
	    example.createCriteria().andEqualTo("deptMappingId",record.getId()).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
	    deptMappingChildMapper.updateByExampleSelective(deptMappingChild, example); 
		if(record.getDeptMappingChildList() !=null && record.getDeptMappingChildList().size()>0){
			for(DeptMappingChild deptMappingChildRecord : record.getDeptMappingChildList()) {
				deptMappingChildRecord.setDeptMappingId(record.getId());
				deptMappingChildRecord.setSyscode(record.getSyscode());
				deptMappingChildService.save(deptMappingChildRecord);
			}
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
//	@TargetDataSource(name = "deanquery")
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		DeptMapping record = new DeptMapping();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		DeptMappingChild deptMappingChild = new DeptMappingChild();
		deptMappingChild.setIsDeleted(Contants.IS_DELETED_TURE);
		deptMappingChild.setUpdateDate(new Date());
		deptMappingChild.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		deptMappingChild.setUpdateUserName(UserInfoHolder.getCurrentUserName());
	    Example example = new Example(DeptMappingChild.class);
	    example.createCriteria().andEqualTo("deptMappingId",id).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
	    deptMappingChildMapper.updateByExampleSelective(deptMappingChild, example); 
	    
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
//	@TargetDataSource(name = "deanquery")
	public DeptMapping selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
//	@TargetDataSource(name = "deanquery")
	public DataSet<DeptMapping> getDataSetList(Page page, DeptMapping record) {
		/*Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotEmpty(record.getCondition())){
            String condition = "%"+record.getCondition()+"%";
            example.and().andLike("oaDeptName", condition).orLike("hisDeptName", condition);
        }
		List<DeptMapping> records = mapper.selectByExampleAndRowBounds(example, page);*/
		List<DeptMapping> records = mapper.getDataSetList(page, record);
		
		//循环查询映射的子科室
		if(records !=null && records.size()>0){
			for(DeptMapping deptMapping : records) {
				Example example = new Example(DeptMappingChild.class);
			    example.createCriteria().andEqualTo("deptMappingId",deptMapping.getId()).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
			    deptMapping.setDeptMappingChildList(deptMappingChildMapper.selectByExample(example)); 
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
//	@TargetDataSource(name = "deanquery")
	public DeptMapping getByOaDeptId(DeptMapping record) {
		// TODO Auto-generated method stub
		return mapper.getByOaDeptId(record);
	}

	@Transactional(readOnly = false)
	@Override
	public void syncHisDept(String syscode) {
		// 从his查询科室列表，同步到  jc_bas_org_department 中 id存在则更新，否则就新增
		DataSet<CommInterfaceRegisterResp> dataSet = commInterfaceRegisterFeignService.selectCommInterfaceRegisterList("2", "集成平台-查询科室", "1");
		List<CommInterfaceRegisterResp> list = dataSet.getRows();
		if(CollUtil.isNotEmpty(list)){
			CommInterfaceRegisterResp commInterfaceRegister = list.get(0);

        	JSONArray jsonArray = queryDepartment(commInterfaceRegister);
        	log.info("获取科室的数据：" + jsonArray.toJSONString());
        	
        	if(null != jsonArray && jsonArray.size() > 0) {
        		for (int i = 0; i < jsonArray.size(); i++) {
        			BasOrgDepartment basOrgDepartment = new BasOrgDepartment();
        			JSONObject obj = jsonArray.getJSONObject(i);
        			String deptId = obj.getString("id");
        			basOrgDepartment.setSyscode(syscode);//所属系统编码
        			basOrgDepartment.setId(deptId);//ID
        			basOrgDepartment.setOrgCode(obj.getString("orgCode"));//医院编码
        			basOrgDepartment.setOrgName(obj.getString("orgName"));//医院名称
        			basOrgDepartment.setHospCode(obj.getString("hospCode"));//院区编码
        			basOrgDepartment.setHospName(obj.getString("hospName"));//院区名称
        			basOrgDepartment.setDeptCode(obj.getString("deptCode"));//科室编码
        			basOrgDepartment.setDeptName(obj.getString("name"));//科室名称
        			basOrgDepartment.setEnabled(obj.getString("enabled"));//状态 Y-有效 N-无效
        			basOrgDepartment.setIsDelete(obj.getString("isDelete"));//删除标志 N-否 Y-是
        			if(basOrgDepartmentMapper.selectByPrimaryKey(deptId) != null){ //更新
        				basOrgDepartmentMapper.updateByPrimaryKeySelective(basOrgDepartment);
        			} else { //新增
        				basOrgDepartmentMapper.insertSelective(basOrgDepartment);
        			}
        		}
        	}
		}
	}
	
	/**
	 * 查询his科室数据
	 * @param commInterfaceRegister
	 * @return
	 */
	private JSONArray queryDepartment(CommInterfaceRegisterResp commInterfaceRegister){
		Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
		log.info("===========开始获取科室数据================");
		Map<String,Object> requestParams = new HashMap<>();
		//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
		if(StringUtils.isNotBlank(hisRequestVersion) && "2".equals(hisRequestVersion)) {
			requestParams.put("IsCompress", "false");
			requestParams.put("ServiceName", "OuterPatPayService");//服务名
			requestParams.put("InterfaceName", "QueryOutPatient");//接口名
			requestParams.put("TimeOut", 15000);//超时时间
			
			Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	       	paramsMap.put("pageIndex", "1");
	       	paramsMap.put("pageSize", "1000000");
	    	paramsMap.put("sortOrder", "0");
	       	
	       	requestParams.put("Parameter",paramsMap);
	       	
		}else {
			requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
			requestParams.put("pageIndex", "1");
			requestParams.put("pageSize", "1000000");
			requestParams.put("sortOrder", "0");
		}
		
		String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
		
		String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
		
		String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
		
		JSONObject reuslt = JSON.parseObject(bodyStr);
		
		JSONArray jsonArray = new JSONArray();
		if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
			JSONObject data = reuslt.getJSONObject("data");
			JSONObject Value = data.getJSONObject("Value");
			jsonArray = Value.getJSONArray("list");
		}else {
			jsonArray = reuslt.getJSONArray("list");
		}
		log.info("===========获取科室数据结束================");
		return jsonArray;
	}
}
