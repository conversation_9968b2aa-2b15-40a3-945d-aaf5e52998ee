package cn.trasen.critical.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.critical.model.CriticalValue;
import cn.trasen.critical.service.CriticalValueService;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "危急值")
@RestController
public class CriticalValueController {

	@Resource
	private CriticalValueService criticalValueService;
	
	/**
	 * 
	 * @Title: getCriticalValue   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param id
	 * @param: @return      
	 * @return: PlatformResult<CriticalValue>  
	 * @author: Yuec    
	 * @date:   2021年5月22日 下午3:41:51       
	 * @throws
	 */
	@ApiOperation(value = "查询危急值详情", notes = "查询危急值详情")
    @PostMapping("/criticalValue/getCriticalValue")
	public PlatformResult<CriticalValue> getCriticalValue(@RequestBody CriticalValue record) {
        try {
        	CriticalValue criticalValue = criticalValueService.selectById(record.getId());
        	return PlatformResult.success(criticalValue);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
	
	/**
	 * 
	 * @Title: getCriticalValueList   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param page
	 * @param: @param record
	 * @param: @return      
	 * @return: DataSet<CriticalValue>  
	 * @author: Yuec    
	 * @date:   2021年5月22日 下午3:43:26       
	 * @throws
	 */
	@ApiOperation(value = "危急值数据表列表", notes = "危急值数据表列表")
    @PostMapping("/criticalValue/getCriticalValueList")
    public DataSet<CriticalValue> getCriticalValueList(Page page, @RequestBody CriticalValue record) {
	    try {
			List<CriticalValue> list = criticalValueService.getDataList(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
		}catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			return null;
		}
	}
	
	/**
	 * 
	 * @Title: updateCriticalValueStstus   
	 * @Description: TODO(描述这个方法的作用)   
	 * @param: @param record
	 * @param: @return      
	 * @return: PlatformResult<String>  
	 * @author: Yuec    
	 * @date:   2021年5月22日 下午5:47:30       
	 * @throws
	 */
	@ApiOperation(value = "更新危急值状态为已读", notes = "更新危急值状态为已读")
    @PostMapping("/criticalValue/updateCriticalValueStstus")
	public PlatformResult<String> updateCriticalValueStstus(@RequestBody CriticalValue record) {
        try {
        	criticalValueService.updateCriticalValueStstus(record.getId());
        	return PlatformResult.success("更新成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
	
	
	@ApiOperation(value = "处理危急值并回传给HIS", notes = "处理危急值并回传给HIS")
    @PostMapping("/criticalValue/handleCriticalValue")
	public PlatformResult<String> handleCriticalValue(@RequestBody CriticalValue record) {
        try {
        	criticalValueService.handleCriticalValue(record);
        	return PlatformResult.success("更新成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
	
}
