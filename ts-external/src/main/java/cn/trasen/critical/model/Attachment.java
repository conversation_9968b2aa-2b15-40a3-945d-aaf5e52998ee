package cn.trasen.critical.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "toa_attachment")
@Setter
@Getter
public class Attachment {
    /**
     * 主键
     */
    @Column(name = "ID")
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 模块名称
     */
    @Column(name = "MODULE_NAME")
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /**
     * 文件名称
     */
    @Column(name = "FILE_NAME")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 是否是图片: 0否; 1是;
     */
    @Column(name = "IS_IMAGE")
    @ApiModelProperty(value = "是否是图片: 0否; 1是;")
    private String isImage;

    /**
     * 是否复制
     */
    @Column(name = "IS_COPY")
    @ApiModelProperty(value = "是否复制")
    private String isCopy;

    /**
     * 复制地点
     */
    @Column(name = "COPY_FROM")
    @ApiModelProperty(value = "复制地点")
    private String copyFrom;

    /**
     * 后缀
     */
    @Column(name = "FILE_EXTENSION")
    @ApiModelProperty(value = "后缀")
    private String fileExtension;

    /**
     * 文件大小
     */
    @Column(name = "FILE_SIZE")
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    /**
     * 状态
     */
    @Column(name = "FILE_STATUS")
    @ApiModelProperty(value = "状态")
    private String fileStatus;

    /**
     * 上传用户编码
     */
    @Column(name = "UPLOAD_USER")
    @ApiModelProperty(value = "上传用户编码")
    private String uploadUser;

    /**
     * 上传时间
     */
    @Column(name = "UPLOAD_TIME")
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    /**
     * 文件夹ID
     */
    @Column(name = "FOLDER_ID")
    @ApiModelProperty(value = "文件夹ID")
    private String folderId;

    /**
     * MIME类型
     */
    @Column(name = "MIME_TYPE")
    @ApiModelProperty(value = "MIME类型")
    private String mimeType;

    /**
     * 内容类型
     */
    @Column(name = "CONTENT_TYPE")
    @ApiModelProperty(value = "内容类型")
    private String contentType;

    /**
     * 上传用户名称
     */
    @Column(name = "UPLOAD_USER_NAME")
    @ApiModelProperty(value = "上传用户名称")
    private String uploadUserName;

    /**
     * 上传用户部门编码
     */
    @Column(name = "UPLOAD_DEPT_NAME")
    @ApiModelProperty(value = "上传用户部门编码")
    private String uploadDeptName;

    /**
     * 上传用户部门名称
     */
    @Column(name = "UPLOAD_DEPT")
    @ApiModelProperty(value = "上传用户部门名称")
    private String uploadDept;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新人ID
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新人ID")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除
     */
    @Column(name = "IS_DELETE")
    @ApiModelProperty(value = "是否删除")
    private String isDelete;

    /**
     * 创建人姓名
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 创建部门编号
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建部门编号")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 上传的文件名称
     */
    @Column(name = "ORIGINAL_NAME")
    @ApiModelProperty(value = "上传的文件名称")
    private String originalName;

    /**
     * 文件路径
     */
    @Column(name = "FILE_PATH")
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 绝对路径
     */
    @Column(name = "REAL_PATH")
    @ApiModelProperty(value = "绝对路径")
    private String realPath;

    /**
     * 分享人ID
     */
    @Column(name = "SHARETO_USER")
    @ApiModelProperty(value = "分享人编码")
    private String sharetoUser;

    /**
     * 分享人部门
     */
    @Column(name = "SHARETO_DEPT")
    @ApiModelProperty(value = "分享人部门")
    private String sharetoDept;

    /**
     * 分享人姓名
     */
    @Column(name = "SHARETO_USER_NAME")
    @ApiModelProperty(value = "分享人姓名")
    private String sharetoUserName;
}