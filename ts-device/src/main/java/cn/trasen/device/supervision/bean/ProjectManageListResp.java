package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.Project;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: DeviceManageListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/16 15:37
 * @version: 1.0
 */

@Data
public class ProjectManageListResp extends Project {


    @ApiModelProperty(value = "流程定义ID")
    private String wfInstanceId;


    @ApiModelProperty(value = "采购结果供应商")
    private String plSupplier;

    @ApiModelProperty(value = "采购结果厂家")
    private String plProducer;

    @ApiModelProperty(value = "采购结果品牌")
    private String plBrand;

    @ApiModelProperty(value = "采购结果型号")
    private String plSpec;

    @ApiModelProperty(value = "采购结果建筑规模")
    private String plBuildScale;

    @ApiModelProperty(value = "采购结果竣工日期")
    private String plAcceptDate;

    @ApiModelProperty(value = "采购结果资金来源")
    private String plFundsSources;

    @ApiModelProperty(value = "采购结果服务开始日期")
    private String plServiceStartAt;

    @ApiModelProperty(value = "采购结果服务结束日期")
    private String plServiceEndAt;

}
