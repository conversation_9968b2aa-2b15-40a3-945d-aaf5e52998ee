package cn.trasen.device.supervision.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.IncomeOrigin;
import cn.trasen.device.supervision.service.IncomeOriginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName IncomeOriginController
 * @Description TODO
 * @date 2024年6月17日 下午5:00:50
 */
@RestController
@Api(tags = "IncomeOriginController")
public class IncomeOriginController {

    private transient static final Logger logger = LoggerFactory.getLogger(IncomeOriginController.class);

    @Autowired
    private IncomeOriginService incomeOriginService;


    /**
     * @return DataSet<IncomeOrigin>
     * @Title selectIncomeOriginList
     * @Description 查询列表
     * @date 2024年6月17日 下午5:00:50
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/incomeOrigin/sync")
    public PlatformResult selectIncomeOriginList() {
        incomeOriginService.consume();
        return PlatformResult.success();
    }
}
