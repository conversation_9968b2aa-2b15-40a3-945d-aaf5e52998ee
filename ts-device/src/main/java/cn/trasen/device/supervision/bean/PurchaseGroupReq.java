package cn.trasen.device.supervision.bean;

import cn.trasen.device.supervision.model.Device;
import cn.trasen.device.supervision.model.PurchaseGroup;
import cn.trasen.device.supervision.model.PurchaseLog;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: PurchaseGroup
 * @author: chenbin
 * @description: TODO
 * @date: 2024/4/9 11:04
 * @version: 1.0
 */

@Getter
@Setter
public class PurchaseGroupReq extends PurchaseGroup {

    List<PurchaseLog> purchaseLogList;

}
