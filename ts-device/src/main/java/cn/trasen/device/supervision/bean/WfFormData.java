package cn.trasen.device.supervision.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: WfFormData
 * @author: chenbin
 * @description: TODO
 * @date: 2024/3/5 15:56
 * @version: 1.0
 */
@Getter
@Setter
public class WfFormData {

    /**
     * 流程定义id
     */
    private String wfDefinitionId;

    private List<String> childTableData;

    private Map<String, String> dataMap;

}
