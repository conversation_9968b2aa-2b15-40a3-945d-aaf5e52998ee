package cn.trasen.device.supervision.controller;

import cn.trasen.device.supervision.bean.ServicesManageDetailResp;
import cn.trasen.device.supervision.bean.ServicesManageListResp;
import cn.trasen.device.supervision.bean.ServicesManageSaveReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.Services;
import cn.trasen.device.supervision.service.ServicesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ServicesController
 * @Description TODO
 * @date 2024年3月21日 下午2:43:02
 */
@RestController
@Api(tags = "ServicesController")
public class ServicesController {

    private transient static final Logger logger = LoggerFactory.getLogger(ServicesController.class);

    @Autowired
    private ServicesService servicesService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveServices
     * @Description 新增
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    @ApiOperation(value = "维护数据", notes = "维护数据")
    @PostMapping("/api/services/save")
    public PlatformResult<String> saveServices(@RequestBody ServicesManageSaveReq record) {
        try {
            servicesService.submit(record);
            return PlatformResult.success("提交成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<Services>
     * @Title selectServicesById
     * @Description 根据ID查询
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @PostMapping("/api/services/{id}")
    public PlatformResult<ServicesManageDetailResp> selectServicesById(@PathVariable String id) {
        try {
            return PlatformResult.success(servicesService.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param page
     * @param record
     * @return DataSet<Services>
     * @Title selectServicesList
     * @Description 查询列表
     * @date 2024年3月21日 下午2:43:02
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/services/list")
    public DataSet<ServicesManageListResp> selectServicesList(Page page, Services record) {
        return servicesService.getDataSetList(page, record);
    }
}
