package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_purchase_group")
@Setter
@Getter
public class PurchaseGroup {
    @Id
    private String id;


    /**
     * 所属机构ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "所属机构ID")
    private String deptId;

    /**
     * 结果ID
     */
    @Column(name = "purchase_result_id")
    @ApiModelProperty(value = "结果ID")
    private String purchaseResultId;

    /**
     * 招标参数
     */
    @Column(name = "tender_parameter")
    @ApiModelProperty(value = "招标参数")
    private String tenderParameter;

    /**
     * 招标文件
     */
    @Column(name = "tender_files")
    @ApiModelProperty(value = "招标文件")
    private String tenderFiles;

    /**
     * 采购备注
     */
    @Column(name = "purchase_remark")
    @ApiModelProperty(value = "采购备注")
    private String purchaseRemark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;


}