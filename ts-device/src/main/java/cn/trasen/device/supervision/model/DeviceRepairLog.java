package cn.trasen.device.supervision.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_device_repair_log")
@Setter
@Getter
public class DeviceRepairLog {
    @Id
    private String id;

    /**
     * 设备ID
     */
    @Column(name = "device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /**
     * 维修日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "repair_at")
    @ApiModelProperty(value = "维修日期")
    private Date repairAt;


    /**
     * 维修内容
     */
    @Column(name = "repair_content")
    @ApiModelProperty(value = "维修内容")
    private String repairContent;


    /**
     * 维修费用
     */
    @ApiModelProperty(value = "维修费用")
    private BigDecimal costs;

    @Column(name = "create_date")
    private Date createDate;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_user_name")
    private String createUserName;

    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_user_name")
    private String updateUserName;

    @Column(name = "is_deleted")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 扩展字段
     */
    @Column(name = "expand_field")
    @ApiModelProperty(value = "扩展字段")
    private String expandField;
}