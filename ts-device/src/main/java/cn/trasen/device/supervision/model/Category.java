package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;

import lombok.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

@Table(name = "toa_device_category")
@Setter
@Getter
public class Category {


    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    private String code;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    /**
     * 父类ID
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value = "父类ID")
    private String parentId;

    /**
     * 父类
     */
    @Column(name = "parent_code")
    @ApiModelProperty(value = "父类")
    private String parentCode;

    @Transient
    @ApiModelProperty(value = "父类名称")
    private String parentName;

    /**
     * 树ID
     */
    @Column(name = "tree_ids")
    @ApiModelProperty(value = "树ID")
    private String treeIds;

    /**
     * 是否启用: 1=是; 2=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 2=否;")
    private String isEnable;

    /**
     * 树结构中级别
     */
    @ApiModelProperty(value = "树结构中级别")
    private Integer level;

    /**
     * 设备数量
     */
    @ApiModelProperty(value = "设备数量")
    private Integer devices;

    /**
     * 排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序")
    private Integer seqNo;


    /**
     * 备注
     */
    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    @Column(name = "is_leaf_node")
    @ApiModelProperty(value = "是否叶节点 0 非叶节点 1 叶节点")
    private String isLeafNode;

    @Transient
    @ApiModelProperty(value = "序号(导出数据用)")
    private String no;
}