package cn.trasen.device.supervision.controller;

import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.device.supervision.bean.BatchExportReq;
import cn.trasen.device.supervision.bean.payslip.DoCheckReq;
import cn.trasen.device.supervision.bean.payslip.PayslipStatusReportResp;
import cn.trasen.device.supervision.bean.payslip.PayslipUploadListResp;
import cn.trasen.device.supervision.exception.DeviceBusinessException;
import cn.trasen.device.supervision.exception.DevicePayslipException;
import cn.trasen.device.supervision.model.PayslipLog;
import cn.trasen.device.supervision.model.organization.Organization;
import cn.trasen.device.supervision.service.DeviceHelperService;
import cn.trasen.device.supervision.service.PayslipLogService;
import cn.trasen.device.supervision.util.Comm;
import cn.trasen.device.supervision.util.PoiHelper;
import cn.trasen.homs.core.exception.BusinessException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PayslipUpload;
import cn.trasen.device.supervision.service.PayslipUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ToaDevicePayslipUploadController
 * @Description TODO
 * @date 2024年6月28日 下午2:21:33
 */
@RestController
@Api(tags = "ToaDevicePayslipUploadController")
@RequestMapping("api/payslipUpload/")

public class PayslipUploadController {

    private transient static final Logger logger = LoggerFactory.getLogger(PayslipUploadController.class);

    @Autowired
    private PayslipUploadService payslipUploadService;

    @Autowired
    private PayslipLogService payslipLogService;

    @Autowired
    private DeviceHelperService deviceHelperService;

    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteToaDevicePayslipUploadById
     * @Description 根据ID删除
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("delete/{id}")
    public PlatformResult<String> deleteToaDevicePayslipUploadById(@PathVariable String id) {
        try {
            payslipUploadService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<ToaDevicePayslipUpload>
     * @Title selectToaDevicePayslipUploadList
     * @Description 查询列表
     * @date 2024年6月28日 下午2:21:33
     * <AUTHOR>
     */
    @ApiOperation(value = "上报列表", notes = "上报列表")
    @GetMapping("upload/list")
    public DataSet<PayslipUploadListResp> uploadList(Page page, PayslipUpload record) {
        return payslipUploadService.uploadList(record, page);
    }

    @ApiOperation(value = "审核列表", notes = "审核列表")
    @GetMapping("check/list")
    public DataSet<PayslipUploadListResp> checkList(Page page, PayslipUpload record) {
        return payslipUploadService.checkList(record, page);
    }

    @ApiOperation(value = "工资条导入", notes = "工资条导入")
    @RequestMapping(value = "import/{type}", method = {RequestMethod.GET, RequestMethod.POST})
    public PlatformResult _import_(@RequestParam("file") MultipartFile file, @PathVariable String type, @RequestParam("month") String month) throws Exception {

        try {
            payslipUploadService._import_(type, month, file);
        } catch (DevicePayslipException e) {
            PlatformResult platformResult = new PlatformResult<>();
            platformResult.setMessage("");
            platformResult.setSuccess(false);
            platformResult.setObject(e.getMessage());
            platformResult.setStatusCode(0);
            return platformResult;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }

        return PlatformResult.success();
    }

    @GetMapping(value = "tpl/download/{type}")
    @ApiOperation(value = "下载工资条导入模板", notes = "下载工资条导入模板")
    public void downloadImportOrganizationTemplate(HttpServletResponse response, @PathVariable("type") String type) {
        try {
            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            String filename = "1".equals(type) ? "工资条导入模板(编外).xlsx" : "工资条导入模板(编内).xlsx";
            String template = String.format("template/payslipImportTpl%s.xlsx", type);
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @RequestMapping(value = "batchExport")
    @ApiOperation(value = "批量导出工资条明细", notes = "批量导出工资条明细")

    public ResponseEntity<byte[]> batchExport(@RequestBody BatchExportReq batchExportReq) throws IOException {

        try {
            List<String> exportIdListFinal = new ArrayList<>();
            String exportId = batchExportReq.getId();
            List<String> exportIdList = batchExportReq.getIdList();

            if (exportId == null && exportIdList == null) {
                throw new DeviceBusinessException("导入参数必传");
            }

            if (exportId != null && !exportId.isEmpty()) {
                exportIdListFinal.addAll(Arrays.asList(exportId.split(",")));
            }

            if (exportIdList != null && !exportIdList.isEmpty()) {
                exportIdListFinal = exportIdList;
            }

            if (exportIdListFinal.isEmpty()) {
                throw new DeviceBusinessException("导入参数必传");
            }


            String tpl = "0".equals(batchExportReq.getStatus()) ? "template/payslipExportTpl0.xlsx" : "template/payslipExportTpl1.xlsx";
            String realPath = Comm.getAbsolutePath(tpl);

            try (FileInputStream fileInputStream = new FileInputStream(realPath); Workbook workbook = new XSSFWorkbook(fileInputStream)) {
                // 获取模板文件的第一页
                Sheet templateSheet = workbook.getSheetAt(0);

                for (String id : exportIdListFinal) {
                    PayslipUpload payslipUpload = payslipUploadService.selectById(id);
                    if (payslipUpload == null) {
                        continue;
                    }

                    List<PayslipLog> payslipLogList = payslipLogService.selectByPayslipUploadId(id);
                    if (payslipLogList == null || payslipLogList.isEmpty()) {
                        continue;
                    }

                    for (int i = 0; i < payslipLogList.size(); i++) {
                        payslipLogList.get(i).setNo(String.valueOf(i + 1));
                    }

                    Organization organization = deviceHelperService.getOrganizationByOrgId(payslipUpload.getDeptId());

                    String title = String.format("%s%s工资条明细(%s)", organization.getName(), payslipUpload.getMonth(), "0".equals(payslipUpload.getType()) ? "在编" : "编外");
                    String sheetName = String.format("%s%s", organization.getName(), payslipUpload.getMonth());

                    // 复制空白的sheet
                    Sheet newSheet = workbook.cloneSheet(workbook.getSheetIndex(templateSheet));
                    workbook.setSheetName(workbook.getSheetIndex(newSheet), sheetName);

                    // 填充数据
                    // 删除第四行
                    newSheet.removeRow(newSheet.getRow(4));

                    // 先填充title
                    Row row = newSheet.getRow(0);
                    Cell cell = row.getCell(0);
                    cell.setCellValue(title);

                    int rowIndex = 4;
                    for (PayslipLog payslipLog : payslipLogList) {
                        Row loopRow = newSheet.getRow(rowIndex);
                        if (loopRow == null) {
                            loopRow = newSheet.createRow(rowIndex);
                        }

                        List<String> cellValueList = payslipLogService.payslipLog2List(payslipLog, payslipUpload.getType());
                        if (cellValueList == null) {
                            continue;
                        }

                        //处理序号
                        Cell zeroCell = loopRow.getCell(0);
                        if (zeroCell == null) {
                            zeroCell = loopRow.createCell(0);
                        }
                        zeroCell.setCellValue(rowIndex - 3);

                        // 处理数据
                        for (int i = 1; i <= cellValueList.size(); i++) {
                            Cell loopCell = loopRow.getCell(i);

                            if (loopCell == null) {
                                loopCell = loopRow.createCell(i);
                            }

                            if (i > 5 && i != cellValueList.size()) {

                                loopCell.setCellType(Cell.CELL_TYPE_NUMERIC);
                                try {
                                    loopCell.setCellValue(Double.parseDouble(cellValueList.get(i - 1)));
                                } catch (Exception e) {
                                    loopCell.setCellValue(0);
                                }

                            } else {
                                loopCell.setCellType(Cell.CELL_TYPE_STRING);
                                loopCell.setCellValue(cellValueList.get(i - 1));
                            }
                        }
                        rowIndex++;
                    }

                }
                // 把第一页空模板删除掉
                PoiHelper.deleteSheetByIndex(workbook, 0);

                String fileName = "工资条明细批量导出.xlsx";
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

                if (workbook.getNumberOfSheets() > 0) {
                    workbook.write(outputStream);
                } else {
                    return null;
                }

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                String encodedFilename = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
                headers.setContentDispositionFormData("attachment", encodedFilename);
                headers.setContentLength(outputStream.size());

                return new ResponseEntity<>(outputStream.toByteArray(), headers, HttpStatus.OK);
            } catch (IOException e) {
                e.printStackTrace();
            }

            System.out.println("Excel file created successfully!");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    @GetMapping(value = "export/{id}")
    @ApiOperation(value = "导出工资条明细", notes = "导出工资条明细")

    public ResponseEntity<byte[]> export(@PathVariable("id") String id) throws IOException {

        try {

            // 获取上传记录
            PayslipUpload payslipUpload = payslipUploadService.selectById(id);
            if (payslipUpload == null) {
                throw new BusinessException("记录不存在");
            }

            List<PayslipLog> payslipLogList = payslipLogService.selectByPayslipUploadId(id);

            // 填充序号
            for (int i = 0; i < payslipLogList.size(); i++) {
                payslipLogList.get(i).setNo(String.valueOf(i + 1));
            }


            if (payslipLogList == null || payslipLogList.size() <= 0) {
                throw new BusinessException("没有需要导出的记录");
            }


            Organization organization = deviceHelperService.getOrganizationByOrgId(payslipUpload.getDeptId());

            String tpl;
            String fileName;
            String title;

            if ("0".equals(payslipUpload.getType())) {
                tpl = "template/payslipExportTpl0.xlsx";
                title = String.format("%s%s工资条明细(在编)", organization.getName(), payslipUpload.getMonth());

            } else {
                tpl = "template/payslipExportTpl1.xlsx";
                title = String.format("%s%s工资条明细(编外)", organization.getName(), payslipUpload.getMonth());
            }

            fileName = title + ".xlsx";


            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath(tpl));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", payslipLogList);
            map.put("title", title);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);


            String encodedFilename = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);
            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "工资条上报", notes = "工资条上报")
    @PostMapping("upload/{id}")
    public PlatformResult upload(@PathVariable String id, @RequestBody DoCheckReq doCheckReq) {

        try {
            payslipUploadService.upload(id, doCheckReq);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }

        return PlatformResult.success();
    }


    @ApiOperation(value = "工资条审核", notes = "工资条审核")
    @PostMapping("check/{taskId}")
    public PlatformResult check(@RequestBody DoCheckReq doCheckReq) {
        try {
            payslipUploadService.doCheck(doCheckReq);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
        return PlatformResult.success();
    }

    @ApiOperation(value = "工资条状态报表", notes = "工资条状态报表")
    @PostMapping("status/report")
    public PlatformResult<PayslipStatusReportResp> payslipStatusReport(PayslipUpload record) {
        try {
            if (record.getType() == null) {
                return PlatformResult.failure("编制类型参数必须传递");
            }

            if (record.getMonth() == null) {
                return PlatformResult.failure("月份参数必须传递");
            }

            return PlatformResult.success(payslipUploadService.payslipStatusReport(record));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

}
