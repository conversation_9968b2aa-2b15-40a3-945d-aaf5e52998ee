package cn.trasen.device.supervision.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.PayslipLog;
import cn.trasen.device.supervision.service.PayslipLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ToaDevicePayslipLogController
 * @Description TODO
 * @date 2024年6月28日 下午2:24:13
 */
@RestController

@Api(tags = "ToaDevicePayslipLogController")
@RequestMapping("api/payslipLog/")
public class PayslipLogController {

    private transient static final Logger logger = LoggerFactory.getLogger(PayslipLogController.class);

    @Autowired
    private PayslipLogService payslipLogService;


    /**
     * @param page
     *
     * @return DataSet<ToaDevicePayslipLog>
     * @Title selectToaDevicePayslipLogList
     * @Description 查询列表
     * @date 2024年6月28日 下午2:24:13
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("list/{payslipUploadId}")
    public DataSet<PayslipLog> selectToaDevicePayslipLogList(Page page, @PathVariable("payslipUploadId") String payslipUploadId) {
        return payslipLogService.getDataSetList(page, payslipUploadId);
    }

}
