package cn.trasen.device.supervision.model;

import cn.trasen.device.supervision.model.WfShInstanceInfo;
import io.swagger.annotations.*;

import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_ps_instance_info")
@Setter
@Getter
public class WfPsInstanceInfo extends WfShInstanceInfo {

    @Column(name = "approval_nums")
    private String approvalNums;

    @Column(name = "approval_unit_price")
    private String approvalUnitPrice;

    @Column(name = "approval_total_price")
    private String approvalTotalPrice;

    @Column(name = "LAUNCH_DEPT_CODE")
    private String launchDeptCode;

    @Column(name = "custom_code")
    private String customCode;


    @Column(name = "ao")
    private String ao;

    @Column(name = "dopn")
    private String dopn;

    @Column(name = "mosp")
    private String mosp;


    @Column(name = "an")
    private String an;

    @Column(name = "aup")
    private String aup;

    @Column(name = "atp")
    private String atp;

}