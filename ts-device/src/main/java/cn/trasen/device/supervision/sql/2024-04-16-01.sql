CREATE TABLE `toa_device_device_repair_log` (
                                                `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
                                                `device_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备ID',
                                                `repair_at` date DEFAULT NULL COMMENT '维修日期',
                                                `costs` decimal(10,2) DEFAULT NULL COMMENT '维修费用',
                                                `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                                `create_date` datetime DEFAULT NULL,
                                                `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `update_date` datetime DEFAULT NULL,
                                                `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `toa_device_device_operate_log` (
                                                 `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
                                                 `device_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备ID',
                                                 `year` smallint DEFAULT NULL COMMENT '年份',
                                                 `month` smallint DEFAULT NULL COMMENT '月份',
                                                 `incomes` decimal(10,2) DEFAULT NULL COMMENT '月收入',
                                                 `checks` smallint DEFAULT NULL COMMENT '检查人数',
                                                 `consumables_costs` decimal(10,2) DEFAULT NULL COMMENT '耗材支出',
                                                 `repair_costs` decimal(10,2) DEFAULT NULL COMMENT '维修成本',
                                                 `maintenance_costs` decimal(10,2) DEFAULT NULL COMMENT '保养成本',
                                                 `utility_costs` decimal(10,2) DEFAULT NULL COMMENT '水电成本',
                                                 `labor_costs` decimal(10,2) DEFAULT NULL COMMENT '人工成本',
                                                 `housing_costs` decimal(10,2) DEFAULT NULL COMMENT '房屋成本',
                                                 `personnel_costs` decimal(10,2) DEFAULT NULL COMMENT '人员成本',
                                                 `other_costs` decimal(10,2) DEFAULT NULL COMMENT '其他成本',
                                                 `depreciations` decimal(10,2) DEFAULT NULL COMMENT '设备折旧',
                                                 `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                                 `create_date` datetime DEFAULT NULL,
                                                 `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `update_date` datetime DEFAULT NULL,
                                                 `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `toa_device_device_maintenance_log` (
                                                     `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
                                                     `device_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备ID',
                                                     `contract_signing_at` date DEFAULT NULL COMMENT '合同签订日期',
                                                     `costs` decimal(20,2) DEFAULT NULL COMMENT '维保费用',
                                                     `expand_field` json DEFAULT NULL COMMENT '扩展字段',
                                                     `create_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `create_date` datetime DEFAULT NULL,
                                                     `create_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `update_date` datetime DEFAULT NULL,
                                                     `update_user` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `update_user_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `is_deleted` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `sso_org_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     `sso_org_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;