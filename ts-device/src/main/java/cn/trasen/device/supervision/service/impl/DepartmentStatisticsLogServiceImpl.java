package cn.trasen.device.supervision.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.device.supervision.bean.report.BenefitReq;
import cn.trasen.device.supervision.bean.report.DepartmentStatisticsListResp;
import cn.trasen.device.supervision.bean.report.StatisticsResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.device.supervision.dao.DepartmentStatisticsLogMapper;
import cn.trasen.device.supervision.model.DepartmentStatisticsLog;
import cn.trasen.device.supervision.service.DepartmentStatisticsLogService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName DepartmentStatisticsLogServiceImpl
 * @Description TODO
 * @date 2024年6月20日 上午11:09:14
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DepartmentStatisticsLogServiceImpl implements DepartmentStatisticsLogService {

    @Autowired
    private DepartmentStatisticsLogMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(DepartmentStatisticsLog record) {
        record.setId(IdGeneraterUtils.nextId());
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(DepartmentStatisticsLog record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        DepartmentStatisticsLog record = new DepartmentStatisticsLog();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public DepartmentStatisticsLog selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<DepartmentStatisticsLog> getDataSetList(Page page, DepartmentStatisticsLog record) {
        Example example = new Example(DepartmentStatisticsLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<DepartmentStatisticsLog> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    /**
     * @param deptId:
     * @param month:
     * @return DepartmentStatisticsLog
     * <AUTHOR>
     * @description 另外有个地方的checkRow 写得不太严谨，
     * 传了一个对象，实际上只是根据其中某个参数去查，这样的话，就不太好了，
     * 应该是传两个参数，然后根据这两个参数去查
     * 另外这里这个month不是正儿八经的月份，是指某个时间年月日都有可能，有一个表示颗粒度的字段
     * @date 2024/6/20 11:20
     */
    @Override
    public DepartmentStatisticsLog checkRow(String deptId, String month) {
        if (deptId == null || month == null) {
            return null;
        }
        Example example = new Example(DepartmentStatisticsLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        criteria.andEqualTo("deptId", deptId);
        criteria.andEqualTo("month", month);

        List<DepartmentStatisticsLog> records = mapper.selectByExample(example);

        if (records != null && records.size() > 0) {
            return records.get(0);
        }

        return null;
    }

    @Override
    public DataSet<DepartmentStatisticsListResp> departmentStatistics(Page page, BenefitReq benefitReq) {
        List<DepartmentStatisticsListResp> records = mapper.departmentStatistics(page, benefitReq);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public StatisticsResp statisticsResp(Page page, BenefitReq benefitReq) {
        return mapper.statisticsResp(page, benefitReq);
    }
}
