package cn.trasen.device.supervision.dao;

import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.IncomeOrigin;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;

public interface IncomeOriginMapper extends Mapper<IncomeOrigin> {
    void insertBatch(@Param("list") List<IncomeOrigin> records);

    List<IndexBlockYYSBNDXYFXResp> yysbndxyfx(IndexBlockYYSBNDXYFXReq req);

    IndexBlockSBYDXYFXResp sbydxyfx(IndexBlockSBYDXYFXReq req);

    BigDecimal sr(IndexBlockYYZBReq indexBlockYYZBReq);
}