package cn.trasen.device.supervision.bean.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean.benefit
 * @className: DepartmentStatisticsListResp
 * @author: chenbin
 * @description: TODO
 * @date: 2024/6/21 10:38
 * @version: 1.0
 */

@Data
public class DepartmentStatisticsListResp {

    @ApiModelProperty(value = "医院名称")
    private String deptName;

    @ApiModelProperty(value = "设备数量")
    private String devices;

    @ApiModelProperty(value = "检查人数")
    private String checks;

    @ApiModelProperty(value = "总收入")
    private String incomes;

    @ApiModelProperty(value = "总支出")
    private String costs;

    @ApiModelProperty(value = "收支结余")
    private String surplus;
}
