package cn.trasen.device.supervision.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.device.supervision.bean
 * @className: BatchExportReq
 * @author: chenbin
 * @description: 通用接收批量导出时候的ID集
 * @date: 2024/7/3 17:33
 * @version: 1.0
 */

@Data
public class BatchExportReq {

    @ApiModelProperty(value = "id 字符串集合，逗号分隔")
    private String id;

    @ApiModelProperty(value = "id 集合")
    private List<String> idList;

    @ApiModelProperty(value = "0 在编  1非编")
    private String status;
}
