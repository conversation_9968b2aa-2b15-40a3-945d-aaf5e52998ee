<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.device.supervision.dao.DeviceHelperMapper">

    <select id="getEmployeeListByOrgIdList" resultType="cn.trasen.device.supervision.model.organization.Employee">
        SELECT *
        FROM hrms_employee
        WHERE org_id IN
        <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </select>

    <select id="getOrganizationList" resultType="cn.trasen.device.supervision.model.organization.Organization">
        SELECT *
        FROM comm_organization t1
        WHERE t1.is_deleted = 'N' and parent_id != ''
        order by custom_code IS NULL, custom_code asc
    </select>

    <select id="getOrganizationListToFrontSelect" resultType="cn.trasen.device.supervision.model.organization.Organization">
        SELECT *
        FROM comm_organization t1
        WHERE t1.is_deleted = 'N'
          and org_level = 2
        order by custom_code IS NULL, custom_code asc
    </select>

    <select id="getOrganizationListByOrgId" resultType="cn.trasen.device.supervision.model.organization.Organization">
        SELECT *
        FROM comm_organization
        WHERE tree_ids LIKE CONCAT('%', #{orgId}, '%')
    </select>

    <select id="getOrganizationByOrgId" resultType="cn.trasen.device.supervision.model.organization.Organization">
        SELECT *
        FROM comm_organization
        WHERE organization_id = #{orgId} limit 1
    </select>


    <sql id="joinClassify">
        LEFT join wf_definition_info wdi on t1.`WF_DEFINITION_ID` = wdi.`WF_DEFINITION_ID`
        LEFT join wf_form_classify_info wfci on wdi.`WORKFLOW_CLASSIFY` = wfci.`wf_form_classify_id`
    </sql>
    <!--    <sql id="joinClassifyWhere">-->
    <!--        and wfci.`wf_form_classify_id`= #{classifyId}-->
    <!--    </sql>-->
    <sql id="joinDefinitionWhere">
        and wdi.`WF_DEFINITION_ID` =
        #{definitionId}
    </sql>
    <sql id="instanceListMainWhere">
        <if test="mainParams != null and mainParams.size() > 0">
            <foreach collection="mainParams.entrySet()" item="value" index="key">
                <if test="value != null and value != ''">
                    <choose>
                        <when test="key == 'start_surplus'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQYY_3' >= ]]> #{value}
                        </when>
                        <when test="key == 'end_surplus'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQYY_3' <= ]]> #{value}
                        </when>
                        <when test="key == 'start_income'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQSR_1' >= ]]> #{value}
                        </when>
                        <when test="key == 'end_income'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQSR_1' <= ]]> #{value}
                        </when>
                        <when test="key == 'start_cost'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQFY_2' >= ]]> #{value}
                        </when>
                        <when test="key == 'end_cost'">
                            AND <![CDATA[ tdwds.extract_detial -> '$.BQFY_2' <= ]]> #{value}
                        </when>
                        <otherwise>
                            AND t1.`${key}` LIKE CONCAT('%', #{value}, '%')
                        </otherwise>
                    </choose>

                </if>
            </foreach>
        </if>
    </sql>

    <sql id="instanceListDynamicWhere">
        <if test="dynamicParams != null and dynamicParams.size() > 0">
            <foreach collection="dynamicParams.entrySet()" item="value" index="key">
                <if test="value != null and value != ''">
                    AND dynamic_table.`${key}` LIKE CONCAT('%',#{value},'%')
                </if>
            </foreach>
        </if>

    </sql>
    <sql id="instanceListViewRightWhere">
        <if test="dataViewRights != null and dataViewRights.size() > 0">
            and t1.CREATE_USER IN
            <foreach collection="dataViewRights" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </sql>
    <sql id="instanceListOrderBy">
        <if test="orderBy != null and orderBy != ''">
            ${orderBy}
        </if>
    </sql>

    <select id="getNumsByStatus" resultType="java.lang.Integer">
        <choose>
            <when test="status == '10000'">
                select count(*) from wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`CREATE_USER` = #{userCode}
                and t1.`status` in ('2','3','4','5','6')
                <include refid="joinDefinitionWhere"/>
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10001'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10002'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t2.`ASSIGNEE_NO` != #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10003'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` = '0'
                <include refid="joinDefinitionWhere"/>
                and t1.`CREATE_USER` = #{userCode}
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10004'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1' and t1.`CURRENT_STEP_NAME` != '重新提交'
                <include refid="joinDefinitionWhere"/>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
            </when>
            <when test="status == '10005'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                left join wf_task_his t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` in ('1','3','5')
                <include refid="joinDefinitionWhere"/>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
            </when>
            <when test="status == '10006'">
                select count(DISTINCT t1.`WF_INSTANCE_ID`) from wf_instance_info t1
                left join wf_copy_user t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t2.`COPYTO_USER_CODE` = #{userCode}
                <include refid="joinDefinitionWhere"/>
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
            </when>
            <when test="status == '10007'">
                select count(*) from wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <choose>
                    <when test="deptCode != null and deptCode != ''">
                        <choose>
                            <when test="deptCode == 'admin'">
                            </when>
                            <otherwise>
                                and t1.`LAUNCH_DEPT_CODE` = #{deptCode}
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        and 1=2
                    </otherwise>
                </choose>
                <include refid="joinDefinitionWhere"/>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                and t1.`is_deleted` = 'N'
            </when>
            <when test="status == '10008'">
                select count(*) from wf_instance_info t1
                <include refid="joinClassify"/>
                where t1.`status` in ('2','3','4','5','6')
                <choose>
                    <when test="deptCode != null and deptCode != ''">
                        <choose>
                            <when test="deptCode == 'admin'">
                            </when>
                            <otherwise>
                                and t1.`LAUNCH_DEPT_CODE` = #{deptCode}
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        and 1=2
                    </otherwise>
                </choose>
                <include refid="joinDefinitionWhere"/>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                and t1.`is_deleted` = 'N'
            </when>
        </choose>
    </select>
    <sql id="instanceListBaseSql">
        select DISTINCT t1.`WF_INSTANCE_ID`,
                        t1.WF_DEFINITION_ID,
                        t1.IS_DELETED                                as WF_IS_DELETED,
                        t1.CREATE_USER                               as WF_CREATE_USER,
                        t1.CREATE_DATE                               as WF_CREATE_DATE,
                        t1.UPDATE_USER                               as WF_UPDATE_USER,
                        t1.UPDATE_DATE                               as WF_UPDATE_DATE,
                        t1.CREATE_USER_NAME                          as WF_CREATE_USER_NAME,
                        t1.UPDATE_USER_NAME                          as WF_UPDATE_USER_NAME,
                        t1.VERSION                                   as WF_VERSION,
                        t1.WORKFLOW_NO                               as WF_WORKFLOW_NO,
                        t1.WORKFLOW_NAME                             as WF_WORKFLOW_NAME,
                        t1.BUSINESS_ID                               as WF_BUSINESS_ID,
                        t1.CURRENT_STEP_NO                           as WF_CURRENT_STEP_NO,
                        t1.CURRENT_STEP_NAME                         as WF_CURRENT_STEP_NAME,
                        t1.CURRENT_ASSIGNEE_NO                       as WF_CURRENT_ASSIGNEE_NO,
                        t1.CURRENT_ASSIGNEE_NAME                     as WF_CURRENT_ASSIGNEE_NAME,
                        t1.WF_FINISHED_DATE,
                        t1.PARENT_ID                                 as WF_PARENT_ID,
                        t1.LAUNCH_DEPT_CODE                          as WF_LAUNCH_DEPT_CODE,
                        t1.LAUNCH_DEPT_NAME                          as WF_LAUNCH_DEPT_NAME,
                        t1.LAUNCH_COMPANY_CODE                       as WF_LAUNCH_COMPANY_CODE,
                        t1.LAUNCH_COMPANY_NAME                       as WF_LAUNCH_COMPANY_NAME,
                        t1.SUMMARY                                   as WF_SUMMARY,
                        t1.STATUS                                    as WF_STATUS,
                        t1.WORKFLOW_NUMBER                           as WF_WORKFLOW_NUMBER,
                        t1.WORKFLOW_TITLE                            as WF_WORKFLOW_TITLE,
                        t1.HANDLE_ALLOTTED_TIME                      as WF_HANDLE_ALLOTTED_TIME,
                        t1.URGENCY_LEVEL                             as WF_URGENCY_LEVEL,
                        t1.HANDLE_MARKED_WORDS                       as WF_HANDLE_MARKED_WORDS,
                        t1.IS_PRESS                                  as WF_IS_PRESS,
                        t1.print                                     as WF_PRINT,
                        t1.choice_id                                 as WF_CHOICE_ID,
                        t1.sso_org_code                              as WF_SSO_ORG_CODE,
                        t1.sso_org_name                              as WF_SSO_ORG_NAME,
                        t1.form_version                              as WF_FORM_VERSION,
                        t1.CHILD_BUSINESS                            as WF_CHILD_BUSINESS,
                        wt.TASK_ID                                   as WF_TASK_ID,
                        tdwds.extract_detial                         as WF_EXTRACT_DETIAL,
                        dynamic_table.*,
                        (select GROUP_CONCAT(ASSIGNEE_NAME SEPARATOR ', ')
                         from wf_task
                         where WF_INSTANCE_ID = t1.`WF_INSTANCE_ID`) as unsure_assignee_name_list
        from wf_instance_info t1
                 inner join ${tableName} dynamic_table
                            on t1.`WF_INSTANCE_ID` = dynamic_table.`WORKFLOW_ID`
                 left join toa_device_wf_data_structs tdwds on tdwds.`WF_INSTANCE_ID` = t1.`WF_INSTANCE_ID`
                 left join wf_task wt on t1.`WF_INSTANCE_ID` = wt.`WF_INSTANCE_ID`
    </sql>
    <select id="getInstanceList" resultType="java.util.Map" parameterType="cn.trasen.device.supervision.bean.GetInstanceListReq">
        <choose>
            <when test="status == '10000'">
                <include refid="instanceListBaseSql"/>
                <include refid="joinClassify"/>
                where t1.`CREATE_USER` = #{userCode}
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t1.`status` in ('2','3','4','5','6')
                and t1.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10001'">
                <include refid="instanceListBaseSql"/>
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t1.`CREATE_USER` = #{userCode}
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10002'">
                <include refid="instanceListBaseSql"/>
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1'
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t1.`CREATE_USER` = #{userCode}
                and t2.`ASSIGNEE_NO` != #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10003'">
                <include refid="instanceListBaseSql"/>
                <include refid="joinClassify"/>
                where t1.`status` = '0'
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t1.`CREATE_USER` = #{userCode}
                and t1.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10004'">
                <include refid="instanceListBaseSql"/>
                left join wf_task t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` = '1' and t1.`CURRENT_STEP_NAME` != '重新提交'
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10005'">
                <include refid="instanceListBaseSql"/>
                left join wf_task_his t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t1.`status` in ('1','3','5')
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t2.`ASSIGNEE_NO` = #{userCode}
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted`= 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10006'">
                <include refid="instanceListBaseSql"/>
                left join wf_copy_user t2 on t1.`WF_INSTANCE_ID` = t2.`WF_INSTANCE_ID`
                <include refid="joinClassify"/>
                where t2.`COPYTO_USER_CODE` = #{userCode}
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                and t1.`is_deleted` = 'N'
                and t2.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10007'">
                <include refid="instanceListBaseSql"/>
                <include refid="joinClassify"/>
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                <include refid="instanceListViewRightWhere"/>
                where t1.`status` = '1'
                <choose>
                    <when test="deptCode != null and deptCode != ''">
                        <choose>
                            <when test="deptCode == 'admin'">
                            </when>
                            <otherwise>
                                and t1.`LAUNCH_DEPT_CODE` = #{deptCode}
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        and 1=2
                    </otherwise>
                </choose>
                and t1.`is_deleted` = 'N'
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '10008'">
                <include refid="instanceListBaseSql"/>
                <include refid="joinClassify"/>
                where t1.`status` in ('2','3','4','5','6')
                and t1.`is_deleted` = 'N'
                <choose>
                    <when test="deptCode != null and deptCode != ''">
                        <choose>
                            <when test="deptCode == 'admin'">
                            </when>
                            <otherwise>
                                and t1.`LAUNCH_DEPT_CODE` = #{deptCode}
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        and 1=2
                    </otherwise>
                </choose>
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                <include refid="instanceListOrderBy"></include>
            </when>
            <when test="status == '99999'">
                <include refid="instanceListBaseSql"/>
                <include refid="joinClassify"/>
                left join `toa_device_wf_data_structs`t3 on t1.`WF_INSTANCE_ID` = t3.`WF_INSTANCE_ID`
                where (
                (t1.`UPDATE_DATE` is null and t3.`ID` is null)
                or (t1.`UPDATE_DATE` is not null and t3.`ID` is null)
                or (t1.`UPDATE_DATE` is not null and t3.`ID` is not null and t3.wf_instance_last_updated_date is null)
                or (t1.`UPDATE_DATE` != t3.`wf_instance_last_updated_date`)
                )
                and t1.`is_deleted` = 'N' and t1.UPDATE_DATE > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                <include refid="joinDefinitionWhere"></include>
                <include refid="instanceListMainWhere"></include>
                <include refid="instanceListDynamicWhere"></include>
                <!--                <include refid="instanceListViewRightWhere"/>-->
                <include refid="instanceListOrderBy"></include>
            </when>
        </choose>
    </select>
    <select id="getUserParttimeOrgId" resultType="java.lang.String">
        SELECT org_id
        FROM comm_organization_parttime
        WHERE employee_id = #{employeeId}
          and is_default = '1'
          and is_deleted = 'N' limit 1
    </select>

    <update id="autoCalcuUnitPrice">
        UPDATE zt_fwxmcgjhsbb
        SET fwnfdj = CASE
                         WHEN fwnfdj IS NULL AND zje IS NOT NULL AND sl IS NOT NULL THEN ROUND(zje / sl, 4)
                         ELSE fwnfdj
                     END,
            shdj   = CASE
                         WHEN shdj IS NULL AND shje IS NOT NULL AND shsl IS NOT NULL THEN ROUND(shje / shsl, 4)
                         ELSE shdj
                    END,
            pfdj   = CASE
                         WHEN pfdj IS NULL AND pfje IS NOT NULL AND pfsl IS NOT NULL THEN ROUND(pfje / pfsl, 4)
                         ELSE pfdj
                    END;
    </update>

    <select id="getWfInstanceJump" resultType="cn.trasen.device.supervision.bean.WfInstanceJump">
        select `t5`.`WF_INSTANCE_ID`    AS `wf_instance_id`,
               `t5`.`WF_DEFINITION_ID`  AS `wf_definition_id`,
               `t5`.`STATUS`            AS `wf_status`,
               `t5`.`WORKFLOW_NO`       AS `wf_workflow_no`,
               `t5`.`BUSINESS_ID`       AS `wf_business_id`,
               `t5`.`TASK_ID`           AS `wf_task_id`,
               `t5`.`WORKFLOW_NUMBER`   AS `wf_workflow_number`,
               `t5`.`CURRENT_STEP_NAME` AS `wf_current_step_name`
        from toa_device_ps_instance_info t5
        where t5.is_deleted = 'N'
          and t5.wf_instance_id = #{wfInstanceId} limit 1
    </select>

    <select id="getStartStepIdByWorkflowNo" resultType="java.lang.String">
        select t2.`WF_STEP_ID` from wf_definition_info t1
            left join wf_step_info t2 on t1.`WF_DEFINITION_ID` = t2.`WF_DEFINITION_ID`
            where `WF_STEP_TYPE` = 0 and t1.`WORKFLOW_NO` = #{workflowNo} limit 1
    </select>

    <select id="getRestartTaskIdByWfInstanceId" resultType="java.lang.String">
        select TASK_ID
        from wf_task
        where wf_instance_id = #{wfInstanceId}
          and is_deleted = 'N'
          and WF_STEP_NAME = '重新提交' limit 1
    </select>

    <select id="getEmployeeByDeptId" parameterType="cn.trasen.device.supervision.bean.payslip.EmployeeReq" resultType="cn.trasen.device.supervision.bean.payslip.EmployeeResp">
        select `employee_name` as name, `identity_number`, `employee_category`
        from hrms_employee
        where org_id = #{deptId}
          and employee_category in (${employeeCategory})
    </select>
</mapper>