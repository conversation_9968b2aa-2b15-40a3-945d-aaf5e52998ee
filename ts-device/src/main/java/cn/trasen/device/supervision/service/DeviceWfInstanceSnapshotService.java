package cn.trasen.device.supervision.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.device.supervision.model.ApproveData;
import cn.trasen.device.supervision.model.WfInstanceSnapshot;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WfInstanceSnapshotService
 * @Description TODO
 * @date 2024年4月24日 下午4:35:53
 */
public interface DeviceWfInstanceSnapshotService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年4月24日 下午4:35:53
     * <AUTHOR>
     */
    Integer save(WfInstanceSnapshot record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年4月24日 下午4:35:53
     * <AUTHOR>
     */
    Integer update(WfInstanceSnapshot record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年4月24日 下午4:35:53
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return WfInstanceSnapshot
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年4月24日 下午4:35:53
     * <AUTHOR>
     */
    WfInstanceSnapshot selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<WfInstanceSnapshot>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年4月24日 下午4:35:53
     * <AUTHOR>
     */
    DataSet<WfInstanceSnapshot> getDataSetList(Page page, WfInstanceSnapshot record);


    void saveSnapShot(String taskId, ApproveData approveData);
}
