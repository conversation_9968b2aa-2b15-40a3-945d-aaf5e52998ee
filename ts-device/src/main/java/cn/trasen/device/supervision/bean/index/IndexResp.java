package cn.trasen.device.supervision.bean.index;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.device.supervision.bean.index.*;
import cn.trasen.device.supervision.model.DepartmentBudget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.bean
 * @className: IndexResp
 * @author: chenbin
 * @description: 首页返回结构体
 * @date: 2024/4/19 14:08
 * @version: 1.0
 */

@Data
public class IndexResp {

    @ApiModelProperty(value = "待办查阅")
    private IndexBlockDBCYResp indexBlockDBCYResp;

    @ApiModelProperty(value = "年度预算采购分析报表块")
    private IndexBlockYSCGResp indexBlockYSCGResp;

    @ApiModelProperty(value = "医院预算使用情况")
    private DataSet<DepartmentBudget> indexBlockYYYSSYQKResp;

    @ApiModelProperty(value = "医院预算使用情况汇总")
    private IndexBlockYYYSSYQKHZResp indexBlockYYYSSYQKHZResp;

    @ApiModelProperty(value = "我的申请")
    private IndexBlockWDSQResp indexBlockWDSQResp;

    @ApiModelProperty(value = "待我审批")
    private IndexBlockDBSXResp indexBlockDBSXResp;

    @ApiModelProperty(value = "运营指标")
    private IndexBlockYYZBResp indexBlockYYZBResp;

    @ApiModelProperty(value = "设备月度效益分析")
    private List<IndexBlockSBYDXYFXResp> indexBlockSBYDXYFXResp;

    @ApiModelProperty(value = "医院设备年度效益分析")
    private List<IndexBlockYYSBNDXYFXResp> indexBlockYYSBNDXYFXResp;
}
