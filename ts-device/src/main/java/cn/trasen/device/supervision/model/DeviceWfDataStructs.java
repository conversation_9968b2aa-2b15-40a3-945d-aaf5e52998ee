package cn.trasen.device.supervision.model;

import cn.trasen.device.supervision.handler.ObjectJsonHandler;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Map;

/**
 * @projectName: xtbg
 * @package: cn.trasen.device.supervision.model
 * @className: DeviceWfDataStructs
 * @author: chenbin
 * @description: TODO
 * @date: 2024/2/15 15:23
 * @version: 1.0
 */

@Table(name = "toa_device_wf_data_structs")
@Setter
@Getter
public class DeviceWfDataStructs {

    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流程定义ID
     */
    @Column(name = "wf_definition_id")
    @ApiModelProperty(value = "流程定义ID")
    private String wfDefinitionId;

    /**
     * 流程ID
     */
    @Column(name = "wf_instance_id")
    @ApiModelProperty(value = "流程ID")
    private String wfInstanceId;

    /**
     * 流程实例的上一次更新时间
     */
    @Column(name = "wf_instance_last_updated_date")
    @ApiModelProperty(value = "流程实例的上一次更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wfInstanceLastUpdatedDate;

    /**
     * 全量结构化json数据
     */
    @Column(name = "detail")
    @ApiModelProperty(value = "全量结构化json数据")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnType(typeHandler = ObjectJsonHandler.class)
    private JSON detial;

    /**
     * 全量结构化json数据
     */

    @Column(name = "extract_detial")
    @ApiModelProperty(value = "特意抽取出来的结构化json数据")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnType(typeHandler = ObjectJsonHandler.class)
    private JSON extractDetial;

    /**
     * 扩展字段
     */
    @Column(name = "ext1")
    @ApiModelProperty(value = "扩展字段1")
    private String ext0;
    /**
     * 扩展字段
     */
    @Column(name = "ext1")
    @ApiModelProperty(value = "扩展字段1")
    private String ext1;
    /**
     * 扩展字段
     */
    @Column(name = "ext2")
    @ApiModelProperty(value = "扩展字段2")
    private String ext2;
    /**
     * 扩展字段
     */
    @Column(name = "ext3")
    @ApiModelProperty(value = "扩展字段3")
    private String ext3;
    /**
     * 扩展字段
     */
    @Column(name = "ext4")
    @ApiModelProperty(value = "扩展字段4")
    private String ext4;
    /**
     * 扩展字段
     */
    @Column(name = "ext5")
    @ApiModelProperty(value = "扩展字段5")
    private String ext5;


    /**
     * 状体字段
     */
    @Column(name = "status")
    @ApiModelProperty(value = "状态字段")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}
