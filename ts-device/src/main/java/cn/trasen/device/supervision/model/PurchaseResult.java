package cn.trasen.device.supervision.model;

import io.swagger.annotations.*;

import java.util.Date;
import java.util.List;
import javax.persistence.*;

import lombok.*;

@Table(name = "toa_device_purchase_result")
@Setter
@Getter
public class PurchaseResult {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    @Transient
    @ApiModelProperty(value = "主键数组")
    private List<String> idList;

    @Column(name = "wf_definition_id")
    @ApiModelProperty(value = "流程定义id")
    private String wfDefinitionId;

    @Column(name = "wf_instance_id")
    @ApiModelProperty(value = "流程实例id")
    private String wfInstanceId;

    /**
     * 所属机构ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "所属机构ID")
    private String deptId;

    /**
     * 资金来源
     */
    @Column(name = "sources_funds")
    @ApiModelProperty(value = "资金来源")
    private String sourcesFunds;

    /**
     * 申报时间
     */
    @Column(name = "apply_date")
    @ApiModelProperty(value = "申报时间")
    private String applyDate;

    /**
     * 规格型号/服务期限
     */
    @ApiModelProperty(value = "规格型号/服务期限")
    private String spec;

    /**
     * 申报单位
     */
    @Column(name = "apply_org")
    @ApiModelProperty(value = "申报单位")
    private String applyOrg;

    /**
     * 设备/项目名称
     */
    @Column(name = "device_name")
    @ApiModelProperty(value = "设备/项目名称")
    private String deviceName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 局务会议时间
     */
    @Column(name = "approval_date")
    @ApiModelProperty(value = "局务会议时间")
    private String approvalDate;


    /**
     * 状态  0待维护  1已维护
     */
    @ApiModelProperty(value = " 状态 -1 取消采购  0待维护  1已维护 待确认 2 已确认通过 3 已确认未通过 ")
    @Column(name = "status")
    private String status;


    @Transient
    @ApiModelProperty(value = "状态翻译")
    private String statusShow;
    /**
     * 检查备注
     */
    @ApiModelProperty(value = "检查备注")
    @Column(name = "check_remark")
    private String checkRemark;


    /**
     * 数量
     */
    @Column(name = "apply_numbers")
    @ApiModelProperty(value = "数量")
    private String applyNumbers;

    /**
     * 单价
     */
    @Column(name = "apply_price")
    @ApiModelProperty(value = "单价")
    private String applyPrice;

    /**
     * 金额
     */
    @Column(name = "apply_total_price")
    @ApiModelProperty(value = "金额")
    private String applyTotalPrice;


    /**
     * 采购（合同）时间
     */
    @Column(name = "purchase_date")
    @ApiModelProperty(value = "采购（合同）时间")
    private String purchaseDate;

    /**
     * 供货单位名称
     */
    @ApiModelProperty(value = "供货单位名称")
    private String supplier;


    @Column(name = "purchase_type")
    @ApiModelProperty(value = "采购方式")
    private String purchaseType;

    @Column(name = "build_scale")
    @ApiModelProperty(value = "建设规模")
    private String buildScale;

    @Column(name = "accept_date")
    @ApiModelProperty(value = "竣工验收备案时间")
    private String acceptDate;


    /**
     * 生成厂家
     */
    @ApiModelProperty(value = "生成厂家")
    private String producer;

    /**
     * 采购品牌
     */
    @Column(name = "purchase_brand")
    @ApiModelProperty(value = "采购品牌")
    private String purchaseBrand;

    /**
     * 采购型号
     */
    @Column(name = "purchase_spec")
    @ApiModelProperty(value = "采购型号")
    private String purchaseSpec;

    /**
     * 采购数量
     */
    @Column(name = "purchase_numbers")
    @ApiModelProperty(value = "采购数量")
    private String purchaseNumbers;

    /**
     * 采购单价
     */
    @Column(name = "purchase_price")
    @ApiModelProperty(value = "采购单价")
    private String purchasePrice;

    /**
     * 采购金额
     */
    @Column(name = "purchase_total_price")
    @ApiModelProperty(value = "采购金额")
    private String purchaseTotalPrice;

    /**
     * 采购附件
     */
    @Column(name = "purchase_files")
    @ApiModelProperty(value = "采购附件")
    private String purchaseFiles;

    @Column(name = "tender_parameter")
    @ApiModelProperty(value = "招标参数")
    private String tenderParameter;

    @Column(name = "tender_files")
    @ApiModelProperty(value = "招标文件")
    private String tenderFiles;

    @Column(name = "purchase_remark")
    @ApiModelProperty(value = "备注")
    private String purchaseRemark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;


    @ApiModelProperty(value = "申报年度")
    @Transient
    private String applyYear;

    @ApiModelProperty(value = "局务会开始时间")
    @Transient
    private String approvalStartDate;

    @ApiModelProperty(value = "局务会结束时间")
    @Transient
    private String approvalEndDate;

    @Transient
    private String deptCode;

    @Transient
    private String keyword;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "采购详情数组")
    private List<PurchaseLog> purchaseLogList;


}